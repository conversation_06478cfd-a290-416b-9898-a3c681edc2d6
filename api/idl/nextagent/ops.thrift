namespace go nextagent

include "types.thrift"

struct OpsListTemplatesRequest {
    1: optional string TemplateID (api.query = "template_id"),
    2: optional string Creator (api.query = "creator"),
    3: optional string Name (api.query = "name"),
    4: optional string ShareID (api.query = "share_id"),
    5: optional string SessionID (api.query = "session_id"),
    6: optional string RunSessionID (api.query = "run_session_id"),

    7: optional i32 Page (api.query = "page"),
    8: optional i64 PageSize (api.query = "page_size"),
}

struct TemplateVersion {
    1: required string TemplateID (go.tag = "json:\"template_id\""),
    2: required string Name (go.tag = "json:\"name\""),
    3: required string Status (go.tag = "json:\"status\""),
    4: required string Creator (go.tag = "json:\"creator\""),
    5: required string SessionID (go.tag = "json:\"session_id\""),
    6: required string PromptContent (go.tag = "json:\"prompt_content\""),
    7: required string PromptVariables (go.tag = "json:\"prompt_variables\""),
    8: required string Plan (go.tag = "json:\"plan\""),
    9: required string PlanSteps (go.tag = "json:\"plan_steps\""),
    10: required string ExpSOP (go.tag = "json:\"exp_sop\""),
    11: required string SupportMCPs (go.tag = "json:\"support_mcps\""),
    12: required string Label (go.tag = "json:\"label\""),
    13: required bool Expired (go.tag = "json:\"expired\""),
    14: required bool Edited (go.tag = "json:\"edited\""),
    15: required string CreatedAt (go.tag = "json:\"created_at\""),
    16: required string UpdatedAt (go.tag = "json:\"updated_at\""),
}

struct OpsListTemplatesResponse {
    1: required list<TemplateVersion> Templates (go.tag = "json:\"templates\""),
}

struct OpsEditTemplateRequest {
	1: required string TemplateID (api.path = "template_id"),
	2: optional string Name (go.tag = "json:\"name\""),
	3: optional string ExpProgressPlan (go.tag = "json:\"progress_plan\""),
	4: optional string ExpSOP (go.tag = "json:\"exp_sop\""),
	5: optional bool Expired (go.tag = "json:\"expired\""),
	6: optional bool Edited (go.tag = "json:\"edited\""),
	7: optional string QueryTemplate (go.tag = "json:\"query_template\""),
    8: optional string QueryTemplatePlaceholders (go.tag = "json:\"query_template_placeholders\""),
	9: optional string Scope (go.tag = "json:\"scope\""),
    10: optional string SupportMCPs (go.tag = "json:\"support_mcps\""),
}

struct OpsEditTemplateResponse {
    1: required TemplateVersion Template (go.tag = "json:\"template\""),
}

struct GetTraceSessionTrajectoryRequest {
	1: required string SessionID (api.path = "session_id"),
}

struct GetTraceSessionTrajectoryResponse {
    1: optional string Message (go.tag = "json:\"message\""),
    2: optional map<string, string> Data (go.tag="json:\"data\""), 
} 

struct DeletePreparedCubesRequest {
    1: optional types.SessionRole Role (api.query = "role"),
}

struct DeletePreparedCubesResponse {
    1: required string Message (go.tag = "json:\"message\""),
    2: required i64 Count (go.tag = "json:\"count\""),
    3: optional list<string> FailedSessions (go.tag = "json:\"failed_sessions\""),
}
