namespace go nextagent

include "../common.thrift"
include "types.thrift"
include "session.thrift"
include "event.thrift"

enum TaskType {
    Unknown = 0
    AimeBot = 1     // 发给个人
    NewGroup = 2    // 新建群组
    BindGroup = 3   // 绑定群组
}

// 生效中、已停止
enum TaskStatus {
    Unknown = 0
    Running = 1
    Stopped = 2
}

// 触发类型：手动、CronJob、DB扫库补偿
enum TriggerType {
    Unknown = 0
    Manual = 1
    CronJob = 2
    DBCompensation = 3
}

// 成功、失败、有异常
enum LastRunStatus {
    Unknown = 0
    Success = 1
    Failed = 2
    Exception = 3 // 有异常：仅模版有变化
}

enum ScheduleType {
    Unknown = 0
    ScheduleTypeByDay = 1   // 每日运行
    ScheduleTypeByWeek = 2  // 每周运行
}

struct GroupInfo {
    1: optional string GroupName (go.tag = "json:\"group_name\""),      // 群名称
    2: optional string GroupAvatar (go.tag = "json:\"group_avatar\""),  // 群的头像链接
    3: optional string GroupID (go.tag = "json:\"group_id\""),          // 群的chat id
}

enum WeekDay {
    Unknown = 0
    Monday = 1
    Tuesday = 2
    Wednesday = 3
    Thursday = 4
    Friday = 5
    Saturday = 6
    Sunday = 7
}

struct CronTask {
    1: optional i64 ID (go.tag = "json:\"id\""),
    2: required string CreatedAt (go.tag = "json:\"created_at\""),      // 创建时间
    3: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    4: optional string Username (go.tag = "json:\"username\""),         // 用户名称
    5: optional string UID (go.tag = "json:\"uid\""),
    6: required string Name (go.tag = "json:\"name\""),
    7: optional string Description (go.tag = "json:\"description\""),
    8: required TaskType TaskType (go.tag = "json:\"task_type\""),
    9: required string TemplateID (go.tag = "json:\"template_id\""),
    10: optional session.TemplateFormValue FormValue (go.tag = "json:\"form_value\""),
    11: optional string Schedule (go.tag = "json:\"schedule\""),          // cronjob转画出来的运行时间，格式：2023-12-25 12:00:00
    12: required string Timezone (go.tag = "json:\"timezone\""),
    13: optional LastRunStatus LastRunStatus (go.tag = "json:\"last_run_status\""),
    14: required TaskStatus TaskStatus (go.tag = "json:\"task_status\""),
    16: required types.SessionRole Role (go.tag = "json:\"role\""),
    17: optional string SpaceID (go.tag = "json:\"space_id\""),
    18: optional GroupInfo GroupInfo (go.tag = "json:\"group_info\""),    // 群组信息
    19: optional WeekDay WeekDay (go.tag = "json:\"week_day\""),          // 每周几执行
    20: required ScheduleType ScheduleType (go.tag = "json:\"schedule_type\""),     // 定时任务类型，每天or每周
    21: optional string LastRunErrMsg (go.tag = "json:\"last_run_err_msg\""),    // 最后一次运行的错误信息
    22: optional bool EnableEdit (go.tag = "json:\"enable_edit\"") // 是否有权限编辑任务
}

struct TaskExecution {
    1: optional i64 ID (go.tag = "json:\"id\""),
    2: required string TaskUID (go.tag = "json:\"task_uid\""),
    3: required string TaskName (go.tag = "json:\"task_name\""),
    4: optional string Username (go.tag = "json:\"username\""),
    6: required string ExecuteTime (go.tag = "json:\"execute_time\""),
    7: optional string ErrorMessage (go.tag = "json:\"error_message\""),
    8: required string SessionID (go.tag = "json:\"session_id\""),
    9: required TriggerType TriggerType (go.tag = "json:\"trigger_type\""),
    10: optional i32 ExecutionDuration (go.tag = "json:\"execution_duration\""),
    11: required string CreatedAt (go.tag = "json:\"created_at\""),
    12: required session.SessionStatus RunStatus (go.tag = "json:\"run_status\""),
}

// 创建任务详情
struct CreateCronTaskRequest {
    1: optional i64 ID (go.tag = "json:\"id\""),
    2: optional string Username (go.tag = "json:\"username\""),
    3: optional string UID (go.tag="json:\"uid\""),
    4: required string Name (go.tag = "json:\"name\""),
    5: optional string Description (go.tag = "json:\"description\""),
    6: required TaskType TaskType (go.tag = "json:\"task_type\""),
    7: required string TemplateID (go.tag = "json:\"template_id\""),
    8: optional session.TemplateFormValue FormValue (go.tag = "json:\"form_value\""),
    9: required ScheduleType ScheduleType (go.tag = "json:\"schedule_type\""),
    10: required string Schedule (go.tag = "json:\"schedule\""),
    11: required string Timezone (go.tag = "json:\"timezone\""),
    12: optional LastRunStatus LastRunStatus (go.tag = "json:\"last_run_status\""),
    13: required TaskStatus TaskStatus (go.tag = "json:\"task_status\""),
    14: optional GroupInfo GroupInfo (go.tag = "json:\"group_info\""),
    15: required types.SessionRole Role (go.tag = "json:\"role\""),
    16: optional string SpaceID (go.tag = "json:\"space_id\""),
    17: optional string Options (go.tag = "json:\"options\""),   // 同CreateMessageWithTemplate模板的options
    18: required string Content (go.tag = "json:\"content\""),   // 同CreateMessageWithTemplate模板的content
}

struct CreateCronTaskResponse {
    1: required bool IsSuccess (go.tag = "json:\"is_success\"", api.body="is_success"),
}

// 更新任务详情
struct UpdateCronTaskRequest {
    1: optional i64 ID (go.tag = "json:\"id\""),
    2: optional string Username (go.tag = "json:\"username\""),
    3: required string UID (go.tag="json:\"uid\"", api.path="uid"),
    4: optional string Name (go.tag = "json:\"name\""),
    5: optional string Description (go.tag = "json:\"description\""),
    6: optional TaskType TaskType (go.tag = "json:\"task_type\""),
    7: required string TemplateID (go.tag = "json:\"template_id\""),
    8: optional session.TemplateFormValue FormValue (go.tag = "json:\"form_value\""),
    9: optional ScheduleType ScheduleType (go.tag = "json:\"schedule_type\""),
    10: optional string Schedule (go.tag = "json:\"schedule\""),
    11: optional string Timezone (go.tag = "json:\"timezone\""),
    12: optional LastRunStatus LastRunStatus (go.tag = "json:\"last_run_status\""),
    13: optional TaskStatus TaskStatus (go.tag = "json:\"task_status\""),
    14: optional GroupInfo GroupInfo (go.tag = "json:\"group_info\""),
    15: optional types.SessionRole Role (go.tag = "json:\"role\""),
    16: optional string SpaceID (go.tag = "json:\"space_id\""),
    17: optional string Options (go.tag = "json:\"options\""),   // 同CreateMessageWithTemplate模板的options
    18: optional string Content (go.tag = "json:\"content\""),   // 同CreateMessageWithTemplate模板的content
}

struct UpdateCronTaskResponse {
    1: required CronTask CronTask (go.tag = "json:\"cron_task\"", api.body="cron_task"),
}

enum TaskManageType {
    TaskManageTypeDelete = 1,
    TaskManageTypePause = 2,
    TaskManageTypeResume = 3,
}

// 任务操作（删除、暂停、开启任务）
struct DeleteCronTaskRequest {
    1: required string UID (go.tag="json:\"uid\"", api.path="uid"),
    2: required TaskManageType TaskManageType (go.tag = "json:\"task_manage_type\""),
    3: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct DeleteCronTaskResponse {
    1: required bool IsSuccess (go.tag = "json:\"is_success\"", api.body="is_success"),
}

// 查看任务详情
struct GetCronTaskRequest {
    1: required string UID (go.tag="json:\"uid\"", api.path="uid"),
    2: optional string SpaceID (go.tag = "json:\"space_id\"", api.query = "space_id"),
}

struct GetCronTaskResponse {
    1: required CronTask CronTask (go.tag = "json:\"cron_task\"", api.body="cron_task"),
}

// 查看任务列表（仅返回列表信息）
struct ListCronTasksRequest {
    1: optional string Query (go.tag = "json:\"query\"", api.query = "query"),
    2: required i64 PageNum (go.tag = "json:\"page_num\"", api.query = "page_num"),
    3: required i64 PageSize (go.tag = "json:\"page_size\"", api.query = "page_size"),
    4: optional string SpaceID (go.tag = "json:\"space_id\"", api.query = "space_id"),
}

struct ListCronTasksResponse {
    1: required list<CronTask> CronTasks (go.tag = "json:\"cron_tasks\"", api.body="cron_tasks"),
    2: required i64 Total (go.tag = "json:\"total\"", api.body="total")

}

// 任务操作（强制发起任务）
struct ReTryCronTaskRequest {
    1: required string UID (go.tag="json:\"uid\"", api.path="uid"),
    2: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct ReTryCronTaskResponse {
    1: required event.Message Message (go.tag = "json:\"message\"", api.body="message"),
    2: required session.Session Session (go.tag = "json:\"session\"", api.body="session"),
    3: required session.Template Template (go.tag = "json:\"template\"", api.body="template"),
}

// 查看任务执行记录
struct ListCronTaskLogsRequest {
    1: required string UID (go.tag="json:\"uid\"", api.path="uid"), // 任务uid
    2: required i64 PageNum (go.tag = "json:\"page_num\"", api.query = "page_num"),
    3: required i64 PageSize (go.tag = "json:\"page_size\"", api.query = "page_size"),
    4: optional string SpaceID (go.tag = "json:\"space_id\"", api.query = "space_id"),
}

struct ListCronTaskLogsResponse {
    1: required list<TaskExecution> TaskExecution (go.tag = "json:\"task_execution\"", api.body="task_execution"),
    2: required i64 Total (go.tag = "json:\"total\"", api.body="total")
    3: required bool EnableView (go.tag = "json:\"enable_view\"", api.body="enable_view") // 是否有权限查看所有任务历史
}