namespace go nextagent

struct FilePatch {
    1: string FilePath (go.tag = "json:\"file_path\""),
    2: string Patch (go.tag = "json:\"patch\""),
}

struct LinkContent {
    1: string URL (go.tag = "json:\"url\""),
    2: string Title (go.tag = "json:\"title\""),
    3: string Description (go.tag = "json:\"description\""),
}

typedef string ArtifactType

const ArtifactType ArtifactTypeFile = "file"
const ArtifactType ArtifactTypeCode = "code"
const ArtifactType ArtifactTypeLink = "link"
const ArtifactType ArtifactTypeImage = "image"
const ArtifactType ArtifactTypeLogs = "logs"
const ArtifactType ArtifactTypeResult = "result"
const ArtifactType ArtifactTypeProject = "project"

struct FileMeta {
    1: required string Name (go.tag = "json:\"name\""),
    2: required i64 Size (go.tag = "json:\"size\""),
    3: required string Content (go.tag = "json:\"content\""),
    4: required string LarkToken (go.tag = "json:\"lark_token\""),
    5: required string ImageXURI (go.tag = "json:\"imagex_uri\""),
    // 目前一个 Artifact 可能会包含不同的业务类型的文件, 以该类型为准
    6: required ArtifactType Type (go.tag = "json:\"type\""),
    // 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
    7: required string SubType (go.tag = "json:\"sub_type\""),
    8: optional LinkContent LinkContent (go.tag = "json:\"link_content,omitempty\""),
    9: optional list<FilePatch> PatchFileResult (go.tag = "json:\"patch_file_result,omitempty\""),
    10: optional string ImageXResizeURL (go.tag = "json:\"imagex_resize_url,omitempty\""), // 默认缩放比例的图片链接
    11: optional string LarkDocVersionToken (go.tag = "json:\"lark_doc_version_token\"")
}