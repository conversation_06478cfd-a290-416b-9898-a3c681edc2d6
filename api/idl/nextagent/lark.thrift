namespace go nextagent

include "../common.thrift"

struct CommentMetadata {
    1: required string CommentID (go.tag = "json:\"comment_id\""),
    // 前端获取的 block id，不一定完全准确
    2: required string BlockID (go.tag = "json:\"block_id\""),
    // 划词的内容
    3: required string Quote (go.tag = "json:\"quote\""),
    4: required string CreatedAt (go.tag = "json:\"created_at\""),
    5: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    6: required string UserID (go.tag = "json:\"user_id\""),
    // 转换后的 Username
    7: required string Username (go.tag = "json:\"uesrname\""),
    8: required list<Reply> Replies (go.tag = "json:\"replies\""),
}

struct Reply { 
    1: required ReplyContent ReplyContent (go.tag = "json:\"reply_content\""),
    2: required Extra Extra (go.tag = "json:\"extra\""),
    3: required string ReplyID (go.tag = "json:\"reply_id\""),
    4: required string CreatedAt (go.tag = "json:\"created_at\""),
    5: required string UpdatedAt (go.tag = "json:\"updated_at\""),
    6: required string UserID (go.tag = "json:\"user_id\""),
    // 转换后的 Username
    7: required string Username (go.tag = "json:\"uesrname\""),
}

struct ReplyContent {
    1: required list<ReplyElement> Elements (go.tag = "json:\"elements\""),
}

typedef string ReplyElementType 
const ReplyElementType ReplyElementTypeUnknown = "unknown"
const ReplyElementType ReplyElementTypeDocsLink = "docs_link"
const ReplyElementType ReplyElementTypeTextRun = "text_run"
const ReplyElementType ReplyElementTypePerson = "person"

struct ReplyElement {
    1: required ReplyElementType Type (go.tag = "json:\"type\""),
    2: optional DocsLink DocsLink (go.tag = "json:\"docs_link\""),
    3: optional TextRun TextRun (go.tag = "json:\"text_run\""),
    4: optional Person Person (go.tag = "json:\"person\""),
}

struct TextRun {
    1: required string Text (go.tag = "json:\"text\""),
    2: required bool IsAimeComment (go.tag = "json:\"is_aime_comment\""),
}

struct DocsLink {
    1: required string Url (go.tag = "json:\"url\""),
}

struct Person {
    1: required string UserID (go.tag = "json:\"user_id\""),
    // 转换后的用户名
    2: required string Username (go.tag = "json:\"username\""),
}

struct Extra {
    1: optional list<string> ImageList (go.tag = "json:\"image_list\""),
}

struct LarkAuthRequest {
    1: required string Code (go.tag="json:\"code\"", api.query="code"), // 授权码
    2: required string State (go.tag="json:\"state\"", api.query="state"), // 包含重定向url和用户名
    3: optional string Error (go.tag="json:\"error\"", api.query="error"), // 错误信息
}

struct LarkAuthResponse {
}

struct CheckLarkAuthRequest {
    1: required string URL (go.tag="json:\"url\"", api.query="url"), // 请求url
}

struct CheckLarkAuthResponse {
    1: required bool Authorization (go.tag="json:\"authorization\"", api.body="authorization"), // 是否授权
    2: optional string RedirectURL (go.tag="json:\"redirect_url\"", api.body="redirect_url"), // 重定向url
    3: optional bool AuthorizationDenied (go.tag="json:\"authorization_denied\"", api.body="authorization_denied"), // 是否拒绝授权
 }

struct SendLarkReplayLinkMessageRequest {
    1: required string Username (go.tag="json:\"username\"", api.body="username"), // 用户名
    2: required string ReplayLink (go.tag="json:\"replay_link\"", api.body="replay_link"), // 链接
    3: required string TaskName (go.tag="json:\"task_name\"", api.body="task_name"), // 任务名称
    4: required string ToType (go.tag="default:\"\"" api.vd="$=='user'||$=='group'", api.body="to_type"), // 发送对象类型
}

struct SendLarkReplayLinkMessageResponse {

}

struct GetLarkTicketRequest{

}

struct JSApiTicket {
    1: required string Ticket (go.tag = "json:\"ticket\""),
    2: required i32 ExpireIn (go.tag = "json:\"expire_in\""),
}

struct GetLarkTicketResponse {
    1: required bool Authorization (go.tag="json:\"authorization\"", api.body="authorization"), // 是否授权
    2: required JSApiTicket JSApiTicket (go.tag="json:\"jsapi_ticket\"", api.body="jsapi_ticket"), // jsapi_ticket
    3: required string AppID (go.tag="json:\"app_id\"", api.body="app_id"), // app_id
    4: required string OpenID (go.tag="json:\"open_id\"", api.body="open_id"), // open_id
    5: optional string Signature (go.tag="json:\"signature\"", api.body="signature"), // signature, 预留
}

struct GetUserLarkURLRequest {
    1: required string LarkURL (go.tag="json:\"lark_url\"", api.query="lark_url")
    2: required string SessionID (go.tag="json:\"session_id\"", api.query="session_id")
}
struct GetUserLarkURLResponse {
    1: required string LarkURL (go.tag="json:\"lark_url\"", api.body="lark_url") // 移动到用户空间的lark_url
}

struct GetLarkDocxBlocksRequest {
    1: required string DocumentID (api.path = "document_id"),
}


// 查看Bot是否在群里
struct BotCheckGroupMembershipRequest {
    1: required string ChatID (go.tag = "json:\"chat_id\"", api.query="chat_id"),
}

struct BotCheckGroupMembershipResponse {
    1: required bool IsInGroup (go.tag = "json:\"is_in_group\"", api.body="is_in_group"),
}

// 获取Bot所在的群信息
struct BotSearchGroupsRequest {
    1: optional string Query (go.tag = "json:\"query\"", api.query="query"),
    2: optional string PageToken (go.tag = "json:\"page_token\"", api.query="page_token"),
    3: optional i32 PageSize (go.tag = "json:\"page_size\"", api.query="page_size"),
}

struct GroupItems {
    1: optional string Avatar (go.tag = "json:\"avatar\""),
    2: optional string ChatId (go.tag = "json:\"chat_id\""),
    3: optional string ChatStatus (go.tag = "json:\"chat_status\""),
    4: optional string Description (go.tag = "json:\"description\""),
    5: optional bool External (go.tag = "json:\"external\""),
    6: optional string Name (go.tag = "json:\"name\""),
    7: optional string TenantKey (go.tag = "json:\"tenant_key\""),
}

struct BotSearchGroupsResponse {
    1: required list<GroupItems> Groups (go.tag = "json:\"groups\"", api.body="groups"),
    2: optional bool HasMore (go.tag = "json:\"has_more\"", api.body="has_more"),
    3: optional string NextPageToken (go.tag = "json:\"next_page_token\"", api.body="next_page_token"),
}

// 获取Bot所在的群信息
struct BotCreateGroupRequest {
    1: required i64 UserID (go.tag = "json:\"user_id\"", api.query="user_id"),
    2: optional string GroupName (go.tag = "json:\"group_name\"", api.query="group_name"),
}

struct BotCreateGroupResponse {

}


struct ListLarkDocCommentsRequest {
    1: required string SessionID (api.query="session_id"),
}

struct LarkDocCommentItem {
    1: required string ArtifactID (go.tag = "json:\"artifact_id\""),
     // 产物名字
    2: required string Name (go.tag = "json:\"name\""),
    // 飞书文档 url
    3: required string LarkDocUrl (go.tag = "json:\"lark_doc_url\""),
    4: required list<CommentMetadata> Comments (go.tag = "json:\"comments\""),
    5: required i32 Version (go.tag = "json:\"version\"")
}

struct ListLarkDocCommentsResponse {
    1: required list<LarkDocCommentItem> LarkDocComments (go.tag = "json:\"lark_doc_comments\""),
}
