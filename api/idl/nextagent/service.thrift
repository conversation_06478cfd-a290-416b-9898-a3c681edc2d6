namespace go nextagent

include "session.thrift"
include "session_collection.thrift"
include "event.thrift"
include "../common.thrift"
include "artifact.thrift"
include "lark.thrift"
include "deployment.thrift"
include "replay.thrift"
include "share.thrift"
include "user.thrift"
include "trace.thrift"
include "agent.thrift"
include "prompt.thrift"
include "activity.thrift"
include "feature.thrift"
include "mcp.thrift"
include "template.thrift"
include "permission.thrift"
include "space.thrift"
include "ops.thrift"
include "knowledgebase.thrift"
include "mention.thrift"
include "debug.thrift"
include "knowledgeset.thrift"
include "deploy.thrift"
include "cron_tasks.thrift"
include "notification_message.thrift"
include "dev_resource.thrift"
include "abtest.thrift"

service NextAgentService {
    replay.ListShowcaseResponse ListShowcase(1: replay.ListShowcaseRequest req) (api.get = "/api/agents/v2/showcases"),
    replay.GetReplayResponse GetReplay(1: replay.GetReplayRequest req) (api.get = "/api/agents/v2/replay/:replay_id"),
    replay.CreateReplayResponse CreateReplay(1: replay.CreateReplayRequest req) (api.post = "/api/agents/v2/replay"),
    session.GetUserRolesResponse GetUserRoles(1: session.GetUserRolesRequest req) (api.get = "/api/agents/v2/roles"),
    // session 会话相关
    session.CreateSessionResponse CreateSession(1: session.CreateSessionRequest req) (api.post = "/api/agents/v2/sessions"),
    session.ListSessionsResponse ListSessions(1: session.ListSessionsRequest req) (api.get = "/api/agents/v2/sessions"),
    session.GetSessionResponse GetSession(1: session.GetSessionRequest req) (api.get = "/api/agents/v2/sessions/:session_id"),
    session.DeleteSessionResponse DeleteSession(1: session.DeleteSessionRequest req) (api.delete = "/api/agents/v2/sessions/:session_id"),
    session.BatchDeleteSessionResponse BatchDeleteSession(1: session.BatchDeleteSessionRequest req) (api.delete = "/api/agents/v2/sessions/batch"),
    session.CreateMessageResponse CreateMessage(1: session.CreateMessageRequest req) (api.post = "/api/agents/v2/sessions/:session_id/message"),
    session.GetOldSessionEventsResponse GetOldSessionEvents(1: session.GetOldSessionEventsRequest req) (api.get = "/api/agents/v2/sessions/:session_id/old_events"),
    session.CheckCreateSessionResponse CheckCreateSession(1: session.CheckCreateSessionRequest req) (api.get = "/api/agents/v2/sessions/check"),
    session.UpdateSessionResponse UpdateSession(1: session.UpdateSessionRequest req) (api.patch = "/api/agents/v2/sessions/:session_id"),
    common.SSEResponse GetSessionStreamEvents(1: session.GetSessionStreamEventsRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/sessions/:session_id/events"),
    session.SubmitToolCallResponse SubmitToolCall(1: session.SubmitToolCallRequest req) (api.post = "/api/agents/v2/sessions/:session_id/tool_call"),
    session.GetSessionMCPDetailsResponse GetSessionMCPDetails(1: session.GetSessionMCPDetailsRequest req) (api.get = "/api/agents/v2/sessions/:session_id/mcp_details")
    // 会话收藏相关
    session.CreateSessionStarResponse CreateSessionStar(1: session.CreateSessionStarRequest req) (api.post = "/api/agents/v2/sessions/:session_id/star"),
    session.DeleteSessionStarResponse DeleteSessionStar(1: session.DeleteSessionStarRequest req) (api.delete = "/api/agents/v2/sessions/:session_id/star"),
    // 获取所有未执行完成的会话
    session.ListSessionPartialResponse ListSessionPartial(1: session.ListSessionPartialRequest req) (api.get = "/api/agents/v2/sessions/partial/list"),
    // mention 相关
    mention.SearchMentionsResponse SearchMentions(1: mention.SearchMentionsRequest req) (api.get = "/api/agents/v2/mentions")
    // 会话任务采集相关
    session_collection.SessionsCollectionResponse SessionsCollection(1: session_collection.SessionsCollectionRequest req) (api.post = "/api/agents/v2/sessions/collection"),
    session_collection.ListSessionCollectionsResponse ListSessionCollections(1: session_collection.ListSessionCollectionsRequest req) (api.get = "/api/agents/v2/sessions/collection/list"),
    session_collection.FilterSessionCollectionsResponse FilterListSessionCollections(1: session_collection.FilterSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/list"),
    session_collection.RunSessionCollectionsResponse RunSessionCollections(1: session_collection.RunSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/run"),
    session_collection.SendSessionCollectionRunNotificationResponse SendSessionCollectionRunNotification (1: session_collection.SendSessionCollectionRunNotificationRequest req) (api.post = "/api/agents/v2/sessions/collection/run/notification"),
    session_collection.DownloadSessionCollectionsResponse DownloadSessionCollections(1: session_collection.DownloadSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/download"),
    session_collection.CloseSessionCollectionsResponse CloseSessionCollections(1: session_collection.CloseSessionCollectionsRequest req) (api.post = "/api/agents/v2/sessions/collection/close"),
    session_collection.ListNotificationTemplatesResponse ListNotificationTemplates(1: session_collection.ListNotificationTemplatesRequest req) (api.get = "/api/agents/v2/sessions/collection/notification_templates"),
    session_collection.SendSessionCollectionNotificationResponse SendSessionCollectionNotification (1: session_collection.SendSessionCollectionNotificationRequest req) (api.post = "/api/agents/v2/sessions/collection/notification"),
    // 快捷任务&模板相关
    session.CreateMessageWithTemplateResponse CreateMessageWithTemplate(1: session.CreateMessageWithTemplateRequest req) (api.post = "/api/agents/v2/message_with_template"),
    session.ListTemplatesResponse ListTemplates(1: session.ListTemplatesRequest req) (api.get = "/api/agents/v2/templates/list"),
    session.GetHistoryTemplateVariablesResponse GetHistoryTemplateVariables(1: session.GetHistoryTemplateVariablesRequest req) (api.get = "/api/agents/v2/templates/:template_id/history_variables"),
    session.GetSessionAgentRunResponse GetSessionAgentRun(1: session.GetSessionAgentRunRequest req) (api.get = "/api/agents/v2/sessions/:session_id/agent_run"),
    template.GetTemplateResponse GetTemplate(1: template.GetTemplateRequest req) (api.get = "/api/agents/v2/templates/:template_id"),
    template.CreateTemplateResponse CreateTemplate(1: template.CreateTemplateRequest req) (api.post = "/api/agents/v2/templates"),
    template.UpdateTemplateResponse UpdateTemplate(1: template.UpdateTemplateRequest req) (api.put = "/api/agents/v2/templates/:template_id"),
    template.DeleteTemplateResponse DeleteTemplate(1: template.DeleteTemplateRequest req) (api.delete = "/api/agents/v2/templates/:template_id"),
    template.CreateTemplateDraftResponse CreateTemplateDraft(1: template.CreateTemplateDraftRequest req) (api.post = "/api/agents/v2/templates/draft"),
    template.GetTemplateDraftResponse GetTemplateDraft(1: template.GetTemplateDraftRequest req) (api.get = "/api/agents/v2/templates/:template_id/draft"),
    template.UpdateTemplateExperienceResponse UpdateTemplateExperience(1: template.UpdateTemplateExperienceRequest req) (api.put = "/api/agents/v2/templates/:template_id/experience"),
    template.UploadTemplateExperienceFileStreamResponse UploadTemplateExperienceFileStream(1: template.UploadTemplateExperienceFileStreamRequest req) (api.post = "/api/agents/v2/templates/:template_id/upload/stream"),
    binary DownloadTemplateExperienceFile(1: template.DownloadTemplateExperienceFileStreamRequest req) (api.get = "/api/agents/v2/templates/file/:file_id/raw"),
    template.CountTemplatesResponse CountTemplates(1: template.CountTemplatesRequest req) (api.get = "/api/agents/v2/templates/count"),
    template.CreateTemplateStarResponse CreateTemplateStar(1: template.CreateTemplateStarRequest req) (api.post = "/api/agents/v2/templates/:template_id/star"),
    template.DeleteTemplateStarResponse DeleteTemplateStar(1: template.DeleteTemplateStarRequest req) (api.delete = "/api/agents/v2/templates/:template_id/star"),
    template.CreateShareTemplateResponse CreateShareTemplate(1: template.CreateShareTemplateRequest req) (api.post = "/api/agents/v2/templates/:template_id/share"),
    template.CreateUserShareTemplateResponse CreateUserShareTemplate(1: template.CreateUserShareTemplateRequest req) (api.post = "/api/agents/v2/templates/shares/:share_id/user"),
    template.DeleteUserShareTemplateResponse DeleteUserShareTemplate(1: template.DeleteUserShareTemplateRequest req) (api.delete = "/api/agents/v2/templates/shares/:share_id/user"),
    template.SaveTemplateShareFormDataResponse SaveTemplateShareFormData(1: template.SaveTemplateShareFormDataRequest req) (api.post = "/api/agents/v2/templates/shares/form_data"),
    template.GetTemplateShareFormDataResponse GetTemplateShareFormData(1: template.GetTemplateShareFormDataRequest req) (api.get = "/api/agents/v2/templates/shares/form_data/:id"),

    // artifact 相关
    artifact.CreateArtifactResponse CreateArtifact(1: artifact.CreateArtifactRequest req) (api.post = "/api/agents/v2/artifacts"),
    artifact.ListArtifactsResponse ListArtifacts(1: artifact.ListArtifactsRequest req) (api.get = "/api/agents/v2/artifacts/list"),
    artifact.GetArtifactResponse GetArtifact(1: artifact.GetArtifactRequest req) (api.get = "/api/agents/v2/artifacts/:artifact_id"),
    artifact.UpdateArtifactResponse UpdateArtifact(1: artifact.UpdateArtifactRequest req) (api.put = "/api/agents/v2/artifacts/:artifact_id"),
    artifact.UploadArtifactResponse UploadArtifact(1: artifact.UploadArtifactRequest req) (api.post = "/api/agents/v2/artifacts/:artifact_id/upload", api.serializer='muti-form'),
    artifact.UploadArtifactResponse UploadArtifactStream(1: artifact.UploadArtifactStreamRequest req) (api.post = "/api/agents/v2/artifacts/:artifact_id/upload/stream"),
    artifact.RetrieveArtifactFilesResponse RetrieveArtifactFiles(1: artifact.RetrieveArtifactFilesRequest req) (api.post = "/api/agents/v2/artifacts/:artifact_id/files"),
    binary DownloadArtifact(1: artifact.DownloadArtifactFileRequest req) (api.get = "/api/agents/v2/artifacts/:artifact_id/raw/:path"),
    binary DownloadArtifactBatch(1: artifact.DownloadArtifactBatchRequest req) (api.post = "/api/agents/v2/artifacts/download/batch"),
    artifact.UpdateArtifactFileResponse UpdateArtifactFile(1: artifact.UpdateArtifactFileRequest req) (api.put = "/api/agents/v2/artifacts/:artifact_id/files/:path"),
    artifact.UploadImageByURLResponse UploadImageByURL(1: artifact.UploadImageByURLRequest req) (api.post = "/api/agents/v2/artifacts/images"),
    artifact.ProjectArtifactDiffResponse GetProjectArtifactDiff(1: artifact.ProjectArtifactDiffRequest req) (api.get = "/api/agents/v2/project_artifacts/:artifact_key/revisions/:revision/diff"),
    artifact.ProjectArtifactFilesResponse  GetProjectArtifactFiles(1: artifact.ProjectArtifactFilesRequest req) (api.get = "/api/agents/v2/project_artifacts/:artifact_key/versions/:version/files"),
    artifact.ProjectArtifactFilesContentResponse  GetProjectArtifactFilesContent(1: artifact.ProjectArtifactFilesContentRequest req) (api.get = "/api/agents/v2/project_artifacts/:artifact_key/versions/:version/files_content"),

    // only for debug.
    event.EventStruct UnUsedStruct(1: event.EventStruct req) (api.get = "/api/agents/v2/debug"),
    event.SaveEventKeyResponse SaveEventKey(1: event.SaveEventKeyRequest req) (api.post = "/api/agents/v2/save_event_key"),
    debug.MockDebugEventData MockDebugEventData(1: debug.MockDebugEventData req) (api.post = "/api/agents/v2/debug/mock_event_data"),

    //lark 相关
    lark.LarkAuthResponse LarkAuth(1: lark.LarkAuthRequest req) (api.get = "/api/agents/v2/lark/auth"),
    lark.CheckLarkAuthResponse CheckLarkAuth(1: lark.CheckLarkAuthRequest req) (api.get = "/api/agents/v2/lark/auth/check"),
    lark.GetLarkTicketResponse GetLarkTicket(1: lark.GetLarkTicketRequest req) (api.get = "/api/agents/v2/lark/auth/ticket"),
    lark.GetUserLarkURLResponse GetUserLarkURL(1: lark.GetUserLarkURLRequest req) (api.get = "/api/agents/v2/lark/get/lark_url"),
    lark.SendLarkReplayLinkMessageResponse SendLarkReplayLinkMessage(1: lark.SendLarkReplayLinkMessageRequest req) (api.post = "/api/agents/v2/lark/send/replay_link"),
    binary GetLarkDocxBlocks (1: lark.GetLarkDocxBlocksRequest req) (api.get = "/api/agents/v2/lark/documents/:document_id/blocks"),
    lark.BotCheckGroupMembershipResponse BotCheckGroupMembership(1: lark.BotCheckGroupMembershipRequest req) (api.get = "/api/agents/v2/lark/task/check/group/membership")
    lark.BotSearchGroupsResponse BotSearchGroups(1: lark.BotSearchGroupsRequest req) (api.get = "/api/agents/v2/lark/task/search/groups")
    lark.BotCreateGroupResponse BotCreateGroup(1: lark.BotCreateGroupRequest req) (api.post = "/api/agents/v2/lark/task/create/group")
    lark.ListLarkDocCommentsResponse ListLarkDocComments(1: lark.ListLarkDocCommentsRequest req) (api.get = "/api/agents/v2/lark_doc/comments"),

    // deployment
    deployment.GetDeploymentArtifactResponse GetMainDeploymentArtifact(1: deployment.GetDeploymentArtifactRequest req) (api.get = "/api/agents/v2/deployments"),
    deployment.GetDeploymentArtifactResponse GetMDeploymentArtifact(1: deployment.GetDeploymentArtifactRequest req) (api.get = "/api/agents/v2/deployments/*path"),
    deployment.CreateDeploymentResponse CreateDeployment(1: deployment.CreateDeploymentRequest req) (api.post = "/api/agents/v2/deployments"),

    // share
    share.SharePreviewCallbackResponse ShareReplayPreviewCallback(1: share.SharePreviewCallbackRequest req) (api.post = "/api/agents/v2/share/replay/call_back"),

    // user
    user.GetUserInfoResponse GetUserInfo(1: user.GetUserInfoRequest req) (api.get = "/api/agents/v2/user/:user_name"),
    user.BindInvitationCodeResponse BindInvitationCode(1: user.BindInvitationCodeRequest req) (api.put = "/api/agents/v2/user/invitation_code/:invitation_code"),
    user.GrantAccessResponse GrantAccess(1: user.GrantAccessRequest req) (api.post = "/api/agents/v2/user/grant_access"),
    user.GetUserSettingsResponse GetUserSettings(1: user.GetUserSettingsRequest req) (api.get = "/api/agents/v2/user/settings"),
    user.UpdateUserSettingsResponse UpdateUserSettings(1: user.UpdateUserSettingsRequest req) (api.put = "/api/agents/v2/user/settings"),


    // trace
    trace.GetTraceSessionResponse GetTraceSession(1: trace.GetTraceSessionRequest req) (api.get = "/api/agents/v2/trace/session")
    common.SSEResponse GetTraceEvents(1: trace.GetTraceEventsRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/trace/events")
    trace.ResumeRuntimeResponse ResumeRuntime(1: trace.ResumeRuntimeRequest req) (api.post = "/api/agents/v2/trace/actions/resume")
    trace.GetTraceSessionChatResponse GetTraceSessionChat(1: trace.GetTraceSessionChatRequest req) (api.get = "/api/agents/v2/trace/session/chat")
    trace.ListSessionAgentStepResponse ListSessionAgentStep(1: trace.ListSessionAgentStepRequest req) (api.get = "/api/agents/v2/trace/session/agent_steps")
    binary DownloadSessionLog(1: trace.DownloadSessionLogRequest req) (api.get = "/api/agents/v2/trace/session/log")
    trace.SuspendRuntimeResponse SuspendRuntime(1: trace.SuspendRuntimeRequest req) (api.post = "/api/agents/v2/trace/actions/suspend")
    trace.DeleteRuntimeResponse DeleteRuntime(1: trace.DeleteRuntimeRequest req) (api.post = "/api/agents/v2/trace/actions/delete")
    trace.ListModelsResponse ListModels(1: trace.ListModelsRequest req) (api.get = "/api/agents/v2/trace/models")
    common.SSEResponse ChatStream(1: trace.ChatStreamRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/trace/:model/chat/completions")
    trace.ListSessionDocumentsResponse ListSessionDocuments(1: trace.ListSessionDocumentsRequest req) (api.get = "/api/agents/v2/trace/session/documents")
    trace.ConvertSessionDocumentToLarkResponse ConvertSessionDocumentToLark(1: trace.ConvertSessionDocumentToLarkRequest req) (api.post = "/api/agents/v2/trace/session/documents/convert_to_lark")
    trace.ConvertMarkdownToLarkResponse ConvertMarkdownToLark(1: trace.ConvertMarkdownToLarkRequest req) (api.post = "/api/agents/v2/trace/markdown/convert_to_lark")
    common.SSEResponse TraceMCPEvents(1: trace.TraceMCPEventsRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/trace/events/mcp")

    // avtivity
    activity.GetUserActivityProgressResponse GetUserActivityProgress(1: activity.GetUserActivityProgressRequest req) (api.get = "/api/agents/v2/activity/progress"),
    activity.VerifyActivityAwardResponse VerifyActivityAward(1: activity.VerifyActivityAwardRequest req) (api.post = "/api/agents/v2/activity/verify"),

    // feature
    feature.GetUserFeaturesResponse GetUserFeatures(1: feature.GetUserFeaturesRequest req) (api.get = "/api/agents/v2/features"),
    feature.GetGlobalFeaturesResponse GetGlobalFeature(1: feature.GetGlobalFeaturesRequest req) (api.get = "/api/agents/v2/features/global"),

    // Agent Manage
    agent.CreateAgentResponse CreateAgent(1: agent.CreateAgentRequest req) (api.post = "/api/agents/v2/agent")
    agent.CreateAgentConfigResponse CreateAgentConfig(1: agent.CreateAgentConfigRequest req) (api.post = "/api/agents/v2/agent/config")
    agent.CreateAgentConfigVersionResponse CreateAgentConfigVersion(1: agent.CreateAgentConfigVersionRequest req) (api.post = "/api/agents/v2/agent/config/version")
    agent.UpdateAgentResponse UpdateAgent(1: agent.UpdateAgentRequest req) (api.put = "/api/agents/v2/agent/:agent_id")
    agent.UpdateAgentConfigResponse UpdateAgentConfig(1: agent.UpdateAgentConfigRequest req) (api.put = "/api/agents/v2/agent/config/:agent_config_id")
    agent.UpdateAgentConfigVersionResponse UpdateAgentConfigVersion(1: agent.UpdateAgentConfigVersionRequest req) (api.put = "/api/agents/v2/agent/config/version/:agent_config_version_id")
    agent.DeployAgentConfigVersionResponse DeployAgentConfigVersion(1: agent.DeployAgentConfigVersionRequest req) (api.post = "/api/agents/v2/agent/config/version/:agent_config_version_id/deploy")
    agent.DeleteAgentResponse DeleteAgent(1: agent.DeleteAgentRequest req) (api.delete = "/api/agents/v2/agent/:agent_id")
    agent.DeleteAgentConfigResponse DeleteAgentConfig(1: agent.DeleteAgentConfigRequest req)  (api.delete = "/api/agents/v2/agent/config/:agent_config_id")
    agent.GetAgentResponse GetAgent(1: agent.GetAgentRequest req) (api.get = "/api/agents/v2/agent/:agent_id")
    agent.ListAgentsResponse ListAgents(1: agent.ListAgentsRequest req) (api.get = "/api/agents/v2/agent")
    agent.GetAgentConfigResponse GetAgentConfig(1: agent.GetAgentConfigRequest req) (api.get = "/api/agents/v2/agent/config/:agent_config_id")
    agent.ListAgentConfigsResponse ListAgentConfigs(1: agent.ListAgentConfigsRequest req) (api.get = "/api/agents/v2/agent/config")
    agent.ListAgentConfigVersionsResponse ListAgentConfigVersions(1: agent.ListAgentConfigVersionsRequest req) (api.get = "/api/agents/v2/agent/config/version")
    agent.GetAgentConfigVersionResponse GetAgentConfigVersion(1: agent.GetAgentConfigVersionRequest req) (api.get = "/api/agents/v2/agent/config/version/:agent_config_version_id")
    agent.CopyAgentConfigVersionResponse CopyAgentConfigVersion(1: agent.CopyAgentConfigVersionRequest req) (api.post = "/api/agents/v2/agent/config/version/copy")
    session.ListUserSessionsResponse ListUserSessions(1: session.ListUserSessionsRequest req) (api.get = "/api/agents/v2/agent/user/sessions"),


    // Prompt Manage
    prompt.CreatePromptResponse CreatePrompt(1: prompt.CreatePromptRequest req) (api.post = "/api/agents/v2/prompt")
    prompt.CreatePromptVersionResponse CreatePromptVersion(1: prompt.CreatePromptVersionRequest req) (api.post = "/api/agents/v2/prompt/version")
    prompt.UpdatePromptResponse UpdatePrompt(1: prompt.UpdatePromptRequest req) (api.put = "/api/agents/v2/prompt/:prompt_id")
    prompt.UpdatePromptVersionResponse UpdatePromptVersion(1: prompt.UpdatePromptVersionRequest req) (api.put = "/api/agents/v2/prompt/version/:prompt_version_id")
    prompt.GetPromptResponse GetPrompt(1: prompt.GetPromptRequest req) (api.get = "/api/agents/v2/prompt/:prompt_id")
    prompt.DownloadPromptVersionResponse DownloadPromptVersion(1: prompt.DownloadPromptVersionRequest req) (api.get = "/api/agents/v2/prompt/version/download")
    prompt.ListPromptVersionResponse ListPromptVersion(1: prompt.ListPromptVersionRequest req) (api.get = "/api/agents/v2/prompt/version")
    prompt.DeletePromptResponse DeletePrompt(1: prompt.DeletePromptRequest req) (api.delete = "/api/agents/v2/prompt/:prompt_id")
    prompt.ListPromptResponse ListPrompt(1: prompt.ListPromptRequest req) (api.get = "/api/agents/v2/prompt")
    prompt.GetPromptVersionResponse GetPromptVersion(1: prompt.GetPromptVersionRequest req) (api.get = "/api/agents/v2/prompt/version/:prompt_version_id")

    // MCP 相关
    mcp.CreateMCPResponse CreateMCP(1: mcp.CreateMCPRequest req) (api.post = "/api/agents/v2/mcp")
    mcp.UpdateMCPResponse UpdateMCP(1: mcp.UpdateMCPRequest req) (api.put = "/api/agents/v2/mcp/update")
    mcp.ListMCPResponse ListMCP(1: mcp.ListMCPRequest req) (api.post = "/api/agents/v2/mcp/list" deprecated = "Use ListSpaceMCP instead") // 因bam array query生成代码问题，改post
    mcp.ModifyMCPActivationResponse ModifyMCPActivation(1: mcp.ModifyMCPActivationRequest req) (api.post = "/api/agents/v2/mcp/activation")
    mcp.ModifyMCPUserConfigResponse ModifyMCPUserConfig(1: mcp.ModifyMCPUserConfigRequest req) (api.post = "/api/agents/v2/mcp/user_config")
    mcp.ValidateMCPResponse ValidateMCP(1: mcp.ValidateMCPRequest req) (api.post = "/api/agents/v2/mcp/validate")
    // 内置MCP管理相关
    mcp.CreateMCPResponse CreateBuildInMCP(1: mcp.CreateMCPRequest req) (api.post = "/api/agents/v2/build_in_mcp/create")
    mcp.UpdateMCPResponse UpdateBuildInMCP(1: mcp.UpdateMCPRequest req) (api.put = "/api/agents/v2/build_in_mcp/update")

    // 权限相关
    permission.GetUserResourcePermissionResponse GetUserResourcePermission(1: permission.GetUserResourcePermissionRequest req) (api.get = "/api/agents/v2/resource/user/permission")
    // space项目空间
    space.CreateSpaceResponse CreateSpace(1: space.CreateSpaceRequest req) (api.post = "/api/agents/v2/space"),
    space.UpdateSpaceResponse UpdateSpace(1: space.UpdateSpaceRequest req) (api.put = "/api/agents/v2/space"),
    space.GetSpaceResponse GetSpace(1: space.GetSpaceRequest req) (api.get = "/api/agents/v2/space"),
    space.DeleteSpaceResponse DeleteSpace(1: space.DeleteSpaceRequest req) (api.delete = "/api/agents/v2/space"),
    space.ListAllSpacesResponse ListAllSpaces(1: space.ListAllSpacesRequest req) (api.get = "/api/agents/v2/space/list"),
    space.AddSpaceMemberResponse AddSpaceMember(1: space.AddSpaceMemberRequest req) (api.post = "/api/agents/v2/space/members"),
    space.RemoveSpaceMemberResponse RemoveSpaceMember(1: space.RemoveSpaceMemberRequest req) (api.delete = "/api/agents/v2/space/members"),
    space.ListSpaceMembersResponse ListSpaceMembers(1: space.ListSpaceMembersRequest req) (api.get = "/api/agents/v2/space/members"),
    space.ListUserSpacesResponse ListUserSpaces(1: space.ListUserSpacesRequest req) (api.get = "/api/agents/v2/user/list/space"),
    space.InitSpaceResponse InitSpace(1: space.InitSpaceRequest req) (api.post = "/api/agents/v2/space/init")
    space.ListSpaceResourcesResponse ListSpaceResources(1: space.ListSpaceResourcesRequest req) (api.post = "/api/agents/v2/space/resources"),

    // v3 相关接口
    session.ListSpaceSessionsResponse ListSpaceSessions(1: session.ListSpaceSessionsRequest req) (api.get = "/api/agents/v3/sessions"),
    template.ListSpaceTemplatesResponse ListSpaceTemplates(1: template.ListSpaceTemplatesRequest req) (api.get = "/api/agents/v3/templates/list"),
    template.CountSpaceTemplatesResponse CountSpaceTemplates(1: template.CountSpaceTemplatesRequest req) (api.get = "/api/agents/v3/templates/count"),
    mcp.ListSpaceMCPResponse ListSpaceMCP(1: mcp.ListSpaceMCPRequest req) (api.post = "/api/agents/v3/mcp/list") // 因bam array query生成代码问题，改post
    mcp.ValidateActiveMCPsResponse ValidateActiveMCPs(1: mcp.ValidateActiveMCPsRequest req) (api.post = "/api/agents/v3/mcp/pre_validate")
    mcp.ValidateTemplateMCPsResponse ValidateTemplateMCPs(1: mcp.ValidateTemplateMCPsRequest req) (api.post = "/api/agents/v3/templates/mcp/validate")
    mcp.CreateCloudSDKAuthTicketResponse CreateCloudSDKAuthTicket(1: mcp.CreateCloudSDKAuthTicketRequest req) (api.post="/api/agents/v3/mcp/cloud_psm_auth")

    // 管理运维接口
    ops.OpsListTemplatesResponse OpsListTemplates(1: ops.OpsListTemplatesRequest req) (api.get = "/api/agents/v2/ops/templates"),
    ops.OpsEditTemplateResponse OpsEditTemplate(1: ops.OpsEditTemplateRequest req) (api.patch = "/api/agents/v2/ops/templates/:template_id"),
    ops.GetTraceSessionTrajectoryResponse GetSessionTrajectory(1: ops.GetTraceSessionTrajectoryRequest req) (api.get = "/api/agents/v2/ops/sessions/:session_id/trajectory"),

    // knowledgebase
    knowledgebase.UploadDocumentsResponse UploadDocuments(1: knowledgebase.UploadDocumentsRequest req) (api.put = "/api/agents/v2/datasets/:dataset_id/documents"),
    knowledgebase.DeleteDocumentResponse DeleteDocument(1: knowledgebase.DeleteDocumentRequest req) (api.delete = "/api/agents/v2/datasets/:dataset_id/documents/:document_id"),
    knowledgebase.ListDocumentsResponse ListDocuments(1: knowledgebase.ListDocumentsRequest req) (api.post = "/api/agents/v2/datasets/:dataset_id/documents"),
    knowledgebase.UpdateDocumentResponse UpdateDocument(1: knowledgebase.UpdateDocumentRequest req) (api.put = "/api/agents/v2/datasets/:dataset_id/documents/:document_id"),
    knowledgebase.GetDocumentResponse GetDocument(1: knowledgebase.GetDocumentRequest req) (api.get = "/api/agents/v2/datasets/:dataset_id/documents/:document_id"),
    knowledgebase.SearchLarkDocumentsResponse SearchLarkDocuments(1: knowledgebase.SearchLarkDocumentsRequest req) (api.get = "/api/agents/v2/datasets/:dataset_id/lark_documents"),
    knowledgebase.RecommendDocumentsResponse RecommendDocuments(1: knowledgebase.RecommendDocumentsRequest req) (api.post = "/api/agents/v2/datasets/:dataset_id/recommend_documents"),
    knowledgebase.CountDocumentsResponse CountDocuments(1: knowledgebase.CountDocumentsRequest req) (api.get = "/api/agents/v2/datasets/:dataset_id/documents/count"),
    knowledgebase.BatchDeleteDocumentResponse BatchDeleteDocument(1: knowledgebase.BatchDeleteDocumentRequest req) (api.delete = "/api/agents/v2/datasets/:dataset_id/documents/batch"),
    knowledgebase.BatchUpdateDocumentResponse BatchUpdateDocument(1: knowledgebase.BatchUpdateDocumentRequest req) (api.put = "/api/agents/v2/datasets/:dataset_id/documents/batch"),
    knowledgebase.GetKnowledgeTaskStatusResponse GetKnowledgeTaskStatus(1: knowledgebase.GetKnowledgeTaskStatusRequest req) (api.get = "/api/agents/v2/datasets/:dataset_id/task_status"),

    // debug
    debug.GetDebugSessionResponse GetDebugSession(1: debug.GetDebugSessionRequest req) (api.get = "/api/agents/v2/devops/session")
    debug.GetDebugSessionEventsResponse GetSessionDebugEvents(1: debug.GetDebugSessionEventsRequest req) (api.get = "/api/agents/v2/devops/session/events")
    common.SSEResponse GetSessionDebugEventsStream(1: debug.GetSessionDebugEventsStreamRequest req) (streaming.mode="server", agw.target_streaming_protocol="sse", api.post = "/api/agents/v2/devops/session/events_stream")
    debug.DebugSessionEventResponse DebugSessionEvent(1: debug.DebugSessionEventRequest req) (api.post = "/api/agents/v2/devops/session/debug")

    // knowledgeset
    knowledgeset.CreateKnowledgesetResponse CreateKnowledgeset(1: knowledgeset.CreateKnowledgesetRequest req) (api.post = "/api/agents/v2/knowledgeset"),
    knowledgeset.UpdateKnowledgesetResponse UpdateKnowledgeset(1: knowledgeset.UpdateKnowledgesetRequest req) (api.put = "/api/agents/v2/knowledgeset/:knowledge_set_id"),
    knowledgeset.DeleteKnowledgesetResponse DeleteKnowledgeset(1: knowledgeset.DeleteKnowledgesetRequest req) (api.delete = "/api/agents/v2/knowledgeset/:knowledge_set_id"),
    knowledgeset.GetKnowledgesetResponse GetKnowledgeset(1: knowledgeset.GetKnowledgesetRequest req) (api.get = "/api/agents/v2/knowledgeset/:knowledge_set_id"),
    knowledgeset.ListKnowledgesetResponse ListKnowledgesets(1: knowledgeset.ListKnowledgesetRequest req) (api.get = "/api/agents/v2/knowledgeset"),
    knowledgeset.ListKnowledgesetResponse GetKnowledgesetByIDs(1: knowledgeset.GetKnowledgesetByIDsRequest req) (api.get = "/api/agents/v2/internal/knowledgeset"),

    // knowledgeset version
    knowledgeset.CreateKnowledgesetVersionResponse CreateKnowledgesetVersion(1: knowledgeset.CreateKnowledgesetVersionRequest req) (api.post = "/api/agents/v2/knowledgeset/version"),
    knowledgeset.UpdateKnowledgesetVersionResponse UpdateKnowledgesetVersion(1: knowledgeset.UpdateKnowledgesetVersionRequest req) (api.put = "/api/agents/v2/knowledgeset/version/:knowledge_set_version_id"),
    knowledgeset.DeleteKnowledgesetVersionResponse DeleteKnowledgesetVersion(1: knowledgeset.DeleteKnowledgesetVersionRequest req) (api.delete = "/api/agents/v2/knowledgeset/version/:knowledge_set_version_id"),
    knowledgeset.GetKnowledgesetVersionResponse GetKnowledgesetVersion(1: knowledgeset.GetKnowledgesetVersionRequest req) (api.get = "/api/agents/v2/knowledgeset/version/:knowledge_set_version_id"),
    knowledgeset.ListKnowledgesetVersionResponse ListKnowledgesetVersions(1: knowledgeset.ListKnowledgesetVersionRequest req) (api.get = "/api/agents/v2/knowledgeset/version"),
    knowledgeset.CopyKnowledgesetVersionResponse CopyKnowledgesetVersion(1: knowledgeset.CopyKnowledgesetVersionRequest req) (api.post = "/api/agents/v2/knowledgeset/version/copy"),

    // knowledge
    knowledgeset.CreateKnowledgeResponse CreateKnowledge(1: knowledgeset.CreateKnowledgeRequest req) (api.post = "/api/agents/v2/knowledge"),
    knowledgeset.UpdateKnowledgeResponse UpdateKnowledge(1: knowledgeset.UpdateKnowledgeRequest req) (api.put = "/api/agents/v2/knowledge/:knowledge_id"),
    knowledgeset.DeleteKnowledgeResponse DeleteKnowledge(1: knowledgeset.DeleteKnowledgeRequest req) (api.delete = "/api/agents/v2/knowledge/:knowledge_id"),
    knowledgeset.GetKnowledgeResponse GetKnowledge(1: knowledgeset.GetKnowledgeRequest req) (api.get = "/api/agents/v2/knowledge/:knowledge_id"),
    knowledgeset.ListKnowledgeResponse ListKnowledges(1: knowledgeset.ListKnowledgeRequest req) (api.get = "/api/agents/v2/internal/knowledge"),
    knowledgeset.CheckEnableIfResponse CheckEnableIf(1: knowledgeset.CheckEnableIfRequest req) (api.get = "/api/agents/v2/knowledge/enableif/check"),

    // knowledge Metadata Conf 
    knowledgeset.GetKnowledgesetMetadataConfResponse GetKnowledgesetMetadataConf(1: knowledgeset.GetKnowledgesetMetadataConfRequest req) (api.get = "/api/agents/v2/knowledgeset/metadata/conf"),

    // deploy review
    deploy.CreateDeployResponse CreateDeploy(1: deploy.CreateDeployRequest req) (api.post = "/api/agents/v2/deploy"),
    deploy.GetDeployResponse GetDeploy(1: deploy.GetDeployRequest req) (api.get = "/api/agents/v2/deploy/:deploy_id"),
    deploy.GetAgentDeployListResponse GetAgentDeployList(1: deploy.GetAgentDeployListRequest req) (api.get = "/api/agents/v2/deploy/list"),
    deploy.GetDeployProcessInfoResponse GetDeployProcessInfo(1: deploy.GetDeployProcessInfoRequest req) (api.get = "/api/agents/v2/deploy/process/info"),
    deploy.BPMAuthCallbackResponse BPMAuthCallback(1: deploy.BPMAuthCallbackRequest req) (api.post = "/api/agents/v2/deploy/bpm/auth"),
    deploy.BPMCloseCallbackResponse BPMCloseCallback(1: deploy.BPMCloseCallbackRequest req) (api.post = "/api/agents/v2/deploy/bpm/close"),
    deploy.BPMCancelCanaryCallbackResponse BPMCancelCanaryCallback(1: deploy.BPMCancelCanaryCallbackRequest req) (api.post = "/api/agents/v2/deploy/bpm/cancel/canary"),
    deploy.BPMCanaryCallbackResponse BPMCanaryCallback(1: deploy.BPMCanaryCallbackRequest req) (api.post = "/api/agents/v2/deploy/bpm/canary"),
    deploy.BPMOnlineCallbackResponse BPMOnlineCallback(1: deploy.BPMOnlineCallbackRequest req) (api.post = "/api/agents/v2/deploy/bpm/online"),
    deploy.GetScmVersionResponse GetScmVersion(1: deploy.GetScmVersionRequest req) (api.get = "/api/agents/v2/scm/version"),
    deploy.GetIcmVersionResponse GetIcmVersion(1: deploy.GetIcmVersionRequest req) (api.get = "/api/agents/v2/icm/version"),
    deploy.GetDeployReviewUserResponse GetDeployReviewUser(1: deploy.GetDeployReviewUserRequest req) (api.get = "/api/agents/v2/deploy/review/user"),
    deploy.BitsUpsertAgentVersionResponse BitsUpsertAgentConfigVersion(1: deploy.BitsUpsertAgentVersionRequest req) (api.post = "/api/agents/v2/deploy/bits/upsert/agent/version"),
    // notification message
    // 创建消息
    notification_message.CreateNotificationMessageResponse CreateNotificationMessage(1: notification_message.CreateNotificationMessageRequest req) (api.post = "/api/agents/v2/notification_message"),
    // 获取特定用户消息列表
    notification_message.ListNotificationMessagesResponse ListNotificationMessage(1: notification_message.ListNotificationMessagesRequest req) (api.get = "/api/agents/v2/notification_messages"),
    // 更新消息状态
    notification_message.UpdateNotificationMessageStatusResponse  UpdateNotificationMessageStatus(1: notification_message.UpdateNotificationMessageStatusRequest req) (api.put = "/api/agents/v2/notification_message_status"),

    // dev resource
    // code repo
    dev_resource.SearchCodeRepoResponse SearchCodeRepo(1: dev_resource.SearchCodeRepoRequest req) (api.get = "/api/agents/v2/code_repo"),
    dev_resource.ListCodeRepoResponse ListCodeRepo(1: dev_resource.ListCodeRepoRequest req) (api.post = "/api/agents/v2/code_repos"),
    dev_resource.DeleteCodeRepoResponse DeleteCodeRepo(1: dev_resource.DeleteCodeRepoRequest req) (api.delete = "/api/agents/v2/code_repos"),
    dev_resource.UploadCodeRepoResponse UploadCodeRepo(1: dev_resource.UploadCodeRepoRequest req) (api.put = "/api/agents/v2/code_repos"),

    // service
    dev_resource.SearchServiceResponse SearchService(1: dev_resource.SearchServiceRequest req) (api.get = "/api/agents/v2/service"),
    dev_resource.ListServiceResponse ListService(1: dev_resource.ListServiceRequest req) (api.post = "/api/agents/v2/services")
    dev_resource.DeleteServiceResponse DeleteServiceRepo(1: dev_resource.DeleteServiceRequest req) (api.delete = "/api/agents/v2/services")
    dev_resource.UploadServiceResponse UploadService(1: dev_resource.UploadServiceRequest req) (api.put = "/api/agents/v2/services")

    // platform config
    dev_resource.SearchMeegoSpaceResponse SearchMeegoSpace(1: dev_resource.SearchMeegoSpaceRequest req) (api.get = "/api/agents/v2/meego_space"),
    dev_resource.GetPlatformConfigResponse GetPlatformConfig(1: dev_resource.GetPlatformConfigRequest req) (api.get = "/api/agents/v2/platform_config")
    dev_resource.UpdatePlatformConfigResponse UpdatePlatformConfig(1: dev_resource.UpdatePlatformConfigRequest req) (api.put = "/api/agents/v2/platform_config")


    // cron tasks
    cron_tasks.CreateCronTaskResponse CreateCronTask(1: cron_tasks.CreateCronTaskRequest req) (api.post = "/api/agents/v2/cron/task")
    cron_tasks.UpdateCronTaskResponse UpdateCronTask(1: cron_tasks.UpdateCronTaskRequest req) (api.put = "/api/agents/v2/cron/task/:uid")
    cron_tasks.DeleteCronTaskResponse DeleteCronTask(1: cron_tasks.DeleteCronTaskRequest req) (api.delete = "/api/agents/v2/cron/task/:uid")
    cron_tasks.GetCronTaskResponse GetCronTask(1: cron_tasks.GetCronTaskRequest req) (api.get = "/api/agents/v2/cron/task/:uid")
    cron_tasks.ListCronTasksResponse ListCronTasks(1: cron_tasks.ListCronTasksRequest req) (api.get = "/api/agents/v2/cron/task/list")
    cron_tasks.ReTryCronTaskResponse ReTryCronTask(1: cron_tasks.ReTryCronTaskRequest req) (api.post = "/api/agents/v2/cron/task/retry/:uid")
    cron_tasks.ListCronTaskLogsResponse ListCronTaskLogs(1: cron_tasks.ListCronTaskLogsRequest req) (api.get = "/api/agents/v2/cron/task/log/:uid")
    // abtest config
    abtest.GetABTestConfigResponse GetABTestConfig(1: abtest.GetABTestConfigRequest req) (api.get = "/api/agents/v2/abtest_config")
}
