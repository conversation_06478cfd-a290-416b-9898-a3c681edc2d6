namespace go nextagent

include "artifact.thrift"
include "task.thrift"

service OpenAPIService {
     task.OpenAPICreateTaskResponse CreateTask(1: task.OpenAPICreateTaskRequet req) (api.post = "/open-apis/v1/tasks"),
     binary DownlaodArtifactFileStream(1: artifact.OpenApiDownloadArtifactFileStreamRequest req) (api.get = "/open-apis/v1/artifacts/:artifact_id/raw/*path"),
     artifact.OpenApiUploadArtifactStreamResponse UploadArtifactStream(1: artifact.OpenApiUploadArtifactStreamRequest req) (api.post = "/open-apis/v1/artifacts/upload/stream")
}