namespace go nextagent

include "../common.thrift"
include "file_meta.thrift"

struct Artifact {
    1: required string ID (go.tag="json:\"id\""),
    2: required file_meta.ArtifactType Type (go.tag="json:\"type\""),
    3: required ArtifactStatus Status (go.tag = "json:\"status\""),
    4: required ArtifactSource Source (go.tag = "json:\"source\""),
    5: required string Key (go.tag = "json:\"key\""),
    6: required i32 Version (go.tag = "json:\"version\""),
    7: required list<file_meta.FileMeta> FileMetas (go.tag = "json:\"file_metas\""),
    8: required string CreatedAt (go.tag = "json:\"created_at\""),
    9: required common.JsonVariables Metadata (go.tag = "json:\"metadata\""),
    10: required list<HistoryVersionArtifact> HistoryVersionArtifacts (go.tag = "json:\"history_version_artifacts\""),
}

struct HistoryVersionArtifact {
    1: required string ID (go.tag="json:\"id\""),
    2: required i32 Version (go.tag = "json:\"version\""),
    3: required list<file_meta.FileMeta> FileMetas (go.tag = "json:\"file_metas\""),
    4: required common.JsonVariables Metadata (go.tag = "json:\"metadata\""),
}



typedef string GitChangeType
const GitChangeType ChangeTypeAdded = "added"
const GitChangeType ChangeTypeModified = "modified"
const GitChangeType ChangeTypeDeleted = "deleted"
const GitChangeType ChangeTypeRenamed = "renamed"

struct DiffFileMeta {
    1: required string Path (go.tag = "json:\"path\""),
    /** Enum values: `added` / `modified` / `deleted` / `renamed`. */
    2: required GitChangeType ChangeType (go.tag = "json:\"change_type\""),
    3: required string FromPath (go.tag = "json:\"from_path\""),
    /** Deprecated. Use FromId instead. */
    4: required string FromCommit (go.tag = "json:\"from_commit\""),
    5: required i32 FromMode (go.tag = "json:\"from_mode\""),
    6: required string ToPath (go.tag = "json:\"to_path\""),
    /** Deprecated. Use ToId instead. */
    7: required string ToCommit (go.tag = "json:\"to_commit\""),
    8: required i32 ToMode (go.tag = "json:\"to_mode\""),
    9: required bool IsBinary (go.tag = "json:\"is_binary\""),
    10: required i64 LinesInserted (go.tag = "json:\"lines_inserted\""),
    11: required i64 LinesDeleted (go.tag = "json:\"lines_deleted\""),
    12: string FromId (go.tag = "json:\"from_id\""),
    13: string ToId (go.tag = "json:\"to_id\""),
}

struct DiffFileContent {
    1: required string Path (go.tag = "json:\"path\""),
    /** Enum values: `added` / `modified` / `deleted` / `renamed`. */
    2: required GitChangeType ChangeType (go.tag = "json:\"change_type\""),
    3: required string FromPath (go.tag = "json:\"from_path\""),
    /** Deprecated. Use FromId instead. */
    4: required string FromCommit (go.tag = "json:\"from_commit\""),
    5: required i32 FromMode (go.tag = "json:\"from_mode\""),
    6: required string ToPath (go.tag = "json:\"to_path\""),
    /** Deprecated. Use ToId instead. */
    7: required string ToCommit (go.tag = "json:\"to_commit\""),
    8: required i32 ToMode (go.tag = "json:\"to_mode\""),
    9: required bool IsBinary (go.tag = "json:\"is_binary\""),
    10: required string RawPatch (go.tag = "json:\"raw_patch\""),
    11: string FromId (go.tag = "json:\"from_id\""),
    12: string ToId (go.tag = "json:\"to_id\""),
    /* Indicates the patch was pruned since it surpassed a hard limit. */
    13: bool TooLarge (go.tag = "json:\"too_large\""),
}

struct ProjectArtifactDiffRequest {
    1: optional string SessionID (go.tag = "json:\"session_id\"" api.query = "session_id"),
    2: optional string ReplayID (go.tag = "json:\"replay_id\"" api.query = "replay_id"),
    3: required string ArtifactKey (go.tag = 'json:"artifact_key"' api.path = "artifact_key"),
    4: required string Revision (go.tag = 'json:"revision"' api.path = "revision"),      // "2" 或 "2..5"
    5: optional bool Detail (go.tag = 'json:"detail"' api.query = "detail"), // true: 返回 DiffFilesContent，false: 返回 DiffFilesMeta
    6: optional string FilePath (go.tag = 'json:"file_path"' api.query = "file_path"),
    // other ...
}

// diff 响应
struct ProjectArtifactDiffResponse {
    1: required list<DiffFileMeta> DiffFilesMeta (go.tag = 'json:"diff_files_meta"'),
    2: required list<DiffFileContent> DiffFilesContent (go.tag = 'json:"diff_files_content"')
}

struct ProjectArtifactFilesRequest {
    1: optional string SessionID (go.tag = "json:\"session_id\"" api.query = "session_id"),
    2: optional string ReplayID (go.tag = "json:\"replay_id\"" api.query = "replay_id"),
    3: required string ArtifactKey (go.tag = "json:\"artifact_key\"" api.path = "artifact_key"),
    4: required string Version (go.tag = "json:\"version\"" api.path = "version"),      // "2" 或 "2..5"
    5: required string FilePath (go.tag = "json:\"file_path\"" api.query = "file_path"),
    6: optional i32 Depth (go.tag = "json:\"depth\"" api.query = "depth"),
}

// 版本下的文件列表结果
struct ProjectArtifactFilesResponse {
    1: required list<FileNode> Files (go.tag = "json:\"files\"")
}

// 文件内容
struct FileContent {
    1: string Path (go.tag = "json:\"path\""),
    2: string Content (go.tag = "json:\"content\""),
    3: i64 Size (go.tag = "json:\"size\""),
    4: bool IsBinary (go.tag = "json:\"is_binary\""),
}

// 版本下的文件树节点
struct FileNode {
    1: string Path (go.tag = "json:\"path\""),
    2: string Name (go.tag = "json:\"name\""),
    3: bool IsDir (go.tag = "json:\"is_dir\""),
    4: bool IsLeaf (go.tag = "json:\"is_leaf\""),
    5: optional i64 Size (go.tag = "json:\"size\""),
    6: optional list<FileNode> Children (go.tag = "json:\"children\""),  // 目录才有
}

struct ProjectArtifactFilesContentRequest {
    1: optional string SessionID (go.tag = "json:\"session_id\"" api.query = "session_id"),
    2: optional string ReplayID (go.tag = "json:\"replay_id\"" api.query = "replay_id"),
    3: required string ArtifactKey (go.tag = "json:\"artifact_key\"" api.path = "artifact_key"),
    4: required string Version (go.tag = "json:\"version\"" api.path = "version"),      // "2" 或 "2..5"
    5: required list<string> FilePathList (go.tag = "json:\"file_path_list\"" api.query = "file_path_list"),
    6: optional bool IsPreviewBinary (go.tag = "json:\"is_preview_binary\"" api.query = "is_preview_binary"),
}

// 版本下的文件内容结果列表
struct ProjectArtifactFilesContentResponse {
    1: required list<FileContent> FilesContent (go.tag = "json:\"files_content\"")
}

typedef string ArtifactStatus

const ArtifactStatus ArtifactStatusCreating = "creating"
const ArtifactStatus ArtifactStatusDraft = "draft"
const ArtifactStatus ArtifactStatusCompleted = "completed"

typedef string ArtifactSource

const ArtifactSource ArtifactSourceUser = "user"
const ArtifactSource ArtifactSourceAgent = "agent"

struct CreateArtifactRequest {
    1: required string SessionID (go.tag = "json:\"session_id\""),
    // 目前对前端支持 file, code, image 类型
    2: required file_meta.ArtifactType Type (go.tag = "json:\"type\""),
    3: required string Key (go.tag = "json:\"key\""),
    4: required common.JsonVariables Metadata (go.tag = "json:\"metadata\""),
    5: optional i32 Version (go.tag = "json:\"version\""),
    6: optional common.JsonVariables FileMetas (go.tag = "json:\"file_metas\""),
}

struct CreateArtifactResponse {
    1: required Artifact Artifact (go.tag = "json:\"artifact\""),
}

struct GetArtifactRequest {
    1: required string ID (api.path = "artifact_id"),
}

struct GetArtifactResponse {
    1: required Artifact Artifact (go.tag = "json:\"artifact\""),
}

struct UpdateArtifactRequest {
    1: required string ID (api.path = "artifact_id"),
    2: optional ArtifactStatus Status (go.tag = "json:\"status\""),
    3: optional common.JsonVariables Metadata (go.tag = "json:\"metadata\""),
    4: optional string SessionID (go.tag = "json:\"session_id\""),
}

struct UpdateArtifactResponse {
    1: required Artifact Artifact (go.tag = "json:\"artifact\""),
}

# multipart/form-data file upload
struct UploadArtifactRequest {
    1: required string ID (api.path = "artifact_id"),
    2: required string Path (api.body = "path"),
    3: required binary Content (api.body = "content"),
}

struct UploadArtifactStreamRequest {
    1: required string ID (api.path = "artifact_id"),
    2: required string Path (api.query = "path"),
    3: required i64 Size (api.query = "size"),
}

struct UploadArtifactResponse {
    1: required Artifact Artifact (go.tag = "json:\"artifact\""),
}

struct UploadImageByURLRequest {
    1: required list<string> URLs (api.body = "urls"),
    2: required string SessionID (go.tag = "json:\"session_id\""),
}

struct UploadImageByURLResponse {
    1: required list<Artifact> Artifacts (go.tag = "json:\"artifacts\""),
}

struct RetrieveArtifactFilesRequest {
    1: required string ID (api.path = "artifact_id"),
    2: required list<string> Files (go.tag = "json:\"files\""),
    3: optional bool preview (go.tag = "json:\"preview\""),
}

struct RetrieveArtifactFilesResponse {
    1: required list<file_meta.FileMeta> Files (go.tag = "json:\"files\""),
}

struct DownloadArtifactFileRequest {
    1: required string ID (api.path = "artifact_id"),
    2: required string Path (api.path = "path"),
    3: optional bool Raw (api.query = "raw"), // 是否返回原始文件内容，默认会替换文件内部分内容，如 ::cite 标签
    4: optional bool Stream (api.query = "stream") // 流式返回
}

struct ArtifactFiles {
    1: required string ID (go.tag = "json:\"artifact_id\""),
    2: required list<string> Paths (go.tag = "json:\"paths\"")
}

struct DownloadArtifactBatchRequest {
    1: required list<ArtifactFiles> Artifacts (go.tag = "json:\"artifacts\""),
    2: optional string SessionID (go.tag = "json:\"session_id\""),
    3: optional string ReplayID (go.tag = "json:\"replay_id\""),
}

struct ListArtifactsRequest {
    1: optional string SessionID (api.query = "session_id"),
    2: optional string ReplayID (api.query = "replay_id"),
    3: optional bool display (api.query="display")
}

struct ListArtifactsResponse {
    1: required list<Artifact> Artifacts (go.tag = "json:\"artifacts\""),
}

struct UpdateArtifactFileRequest {
     1: required string ID (api.path = "artifact_id"),
     2: required string Path (api.path = "path"),
     // UploadLark 上传 Lark 会自动给 Session Owner 授权
     3: optional bool UploadLark (go.tag = "json:\"upload_lark\""),
     // GrantPermission 会给该 Artifact 所属 Session 的 Owner 授权
     4: optional bool GrantPermission (go.tag = "json:\"grant_permission\""),
}

struct UpdateArtifactFileResponse {
     1: required Artifact Artifact (go.tag = "json:\"artifact\""),
}

struct OpenApiUploadArtifactStreamRequest {
    // 36 位 uuid, 绑定版本，如果 key 存在则会生成新版本
    1: required string Key (api.query = "key"),
    // 不填的话后端会算一个
    2: optional ArtifactType Type (api.query = "type"),
    // 文件名
    3: required string Path (api.query = "path"),
    4: required i64 Size (api.query = "size"),
}

struct OpenApiUploadArtifactStreamResponse {
    1: required Artifact Artifact (go.tag = "json:\"artifact\""),
}

struct OpenApiDownloadArtifactFileStreamRequest {
    1: required string ID (api.path = "artifact_id"),
    2: required string Path (api.path = "path"),
}