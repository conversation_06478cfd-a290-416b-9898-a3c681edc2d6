namespace go nextagent

include "types.thrift"
include "session.thrift"
include "mcp.thrift"

struct OpenAPICreateTaskRequet {
    1: required types.SessionRole Role (go.tag = "json:\"role\""),
    2: required string TemplateID (go.tag = "json:\"template_id\""),
    3: optional session.TemplateFormValue FormValue (go.tag = "json:\"form_value\""),
    // 任务配置，比如是否通知到用户
    4: optional TaskConfig Config (go.tag = "json:\"taks_config\""),
    // 指定使用的语言等扩展参数
    5: optional MessageOptional Options (go.tag = "json:\"options\""),
    // 指定空间，不指定用个人空间
    6: optional string SpaceID (go.tag = "json:\"space_id\""),
}

struct MessageOptional {
    1: required LocaleType locale (go.tag = "json:\"locale\""),
}

typedef string LocaleType
const LocaleType LocaleTypeUnknown = "unknown"
const LocaleType LocaleTypeZH = "zh"
const LocaleType LocaleTypeEN = "en"

struct OpenAPICreateTaskResponse {
    1: required session.Session Session (go.tag = "json:\"session\""),
}

struct TaskConfig {
    1: optional string LarkGroupID (go.tag = "json:\"lark_group_id\""), // 发送通知到群组，配置了则需要发送
    2: optional bool SendUserNotifaction (go.tag = "json:\"send_user_notifaction\""), // 是否发送通知给个人

}