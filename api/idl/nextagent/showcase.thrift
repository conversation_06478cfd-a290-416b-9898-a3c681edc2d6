namespace go nextagent

include "file_meta.thrift"

enum ShowcaseCategory {
    Study = 0,
    Code = 1,
    Life = 2,
    Analysis = 3,
}

struct Showcase {
    1: required string ID (go.tag = "json:\"id\""),
    2: required string Title (go.tag = "json:\"title\""),
    3: required string Description (go.tag = "json:\"description\""),
    4: required string ImageURL (go.tag = "json:\"image_url\""),
    5: required i64 TotalSteps (go.tag = "json:\"total_steps\""),
    6: required i64 Duration (go.tag = "json:\"duration\""), // in seconds
    7: required list<ShowcaseArtifact> ArtifactList (go.tag = "json:\"artifacts\""),
    8: required ShowcaseCategory Category (go.tag = "json:\"category\""),
}

struct ShowcaseArtifact {
    1: required file_meta.ArtifactType Type (go.tag = "json:\"type\""),
    2: required string Name (go.tag = "json:\"name\""),
}
