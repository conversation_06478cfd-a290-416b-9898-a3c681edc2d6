namespace go nextagent

include "../common.thrift"
include "types.thrift"
include "dev_resource.thrift"


struct UploadDocumentsRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
    2: required list<string> DocumentURLs (go.tag = "json:\"document_urls\""),
    3: optional list<dev_resource.WikiDocConfig> WikiList  (go.tag = "json:\"wiki_list\"")     // 导入的知识库列表
}

struct UploadDocumentsResponse {
    1: list<KnowledgeBaseDocument> Documents,
}

struct KnowledgeBaseDocument {
    1: string ID (go.tag = "json:\"id\""),
    2: string Title (go.tag = "json:\"title\""),
    3: string Creator (go.tag = "json:\"creator\""),
    4: string URL (go.tag = "json:\"url\""),
    5: string UpdatedAt (go.tag = "json:\"updated_at\""),
    6: string Owner (go.tag = "json:\"owner\""),
    7: string CreatedAt (go.tag = "json:\"created_at\""),
    8: optional string Content (go.tag = "json:\"content\""),
    9: optional i64 Heat (go.tag = "json:\"heat\""),
    10: string DatasetID (go.tag = "json:\"dataset_id\""),
    11: DocumentContentType ContentType (go.tag = "json:\"content_type\""),
    12: ImportType ImportType (go.tag = "json:\"import_type\""),
    13: dev_resource.ProcessStatus ProcessStatus (go.tag = "json:\"process_status\""),
    14: optional string WikiSpaceName (go.tag = "json:\"wiki_space_name\""),
    15: optional string FailedReason (go.tag = "json:\"failed_reason\""),
}

typedef string DocumentContentType
const DocumentContentType DocumentContentTypeDoc = "doc"
const DocumentContentType DocumentContentTypeSheet = "sheet"
const DocumentContentType DocumentContentTypeBitable = "bitable"

struct DeleteDocumentRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
    2: required string DocumentID (go.tag = "json:\"document_id\"", api.path="document_id"),
}

struct DeleteDocumentResponse {

}

struct BatchDeleteDocumentRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
    2: required list<string> DocumentIDs (go.tag = "json:\"document_ids\"", api.body="document_ids"),
}

struct BatchDeleteDocumentResponse {}

struct UpdateDocumentRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
    2: required string DocumentID (go.tag = "json:\"document_id\"", api.path="document_id"),
}

struct UpdateDocumentResponse {

}

struct BatchUpdateDocumentRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
    2: required list<string> DocumentIDs (go.tag = "json:\"document_ids\"", api.body="document_ids"),
}

struct BatchUpdateDocumentResponse {}

struct GetKnowledgeTaskStatusRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
}

typedef string KnowledgeTaskStatus
const KnowledgeTaskStatus KnowledgeTaskStatusFinished = "finished"
const KnowledgeTaskStatus KnowledgeTaskStatusProcessing = "processing"

struct GetKnowledgeTaskStatusResponse {
    1: KnowledgeTaskStatus Status (go.tag = "json:\"status\""),
}

typedef string ListDocumentsDescOrderBy
const ListDocumentsDescOrderBy ListDocumentsDescOrderByLastUpdatedAt = "last_updated_at"
const ListDocumentsDescOrderBy ListDocumentsDescOrderByHeat = "heat"
const ListDocumentsDescOrderBy ListDocumentsDescOrderByUpdatedAt = "updated_at"

struct ListDocumentsRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
    2: optional string Query (go.tag = "json:\"query\""),
    3: optional list<string> Creators (go.tag = "json:\"creators\""),
    4: required i64 PageNum (go.tag = "json:\"page_num\""),
    5: required i64 PageSize (go.tag = "json:\"page_size\""),
    6: optional ListDocumentsDescOrderBy DescOrderBy (go.tag = "json:\"desc_order_by\""),
    7: optional list<dev_resource.ProcessStatus> ProcessStatus (go.tag = "json:\"process_status\""),
    8: optional i64 LteCreatedAt (go.tag = "json:\"lte_created_at\""),
}

struct ListDocumentsResponse {
    1: i64 Total (go.tag = "json:\"total\""),
    2: list<KnowledgeBaseDocument> Documents (go.tag = "json:\"documents\""),
}


struct GetDocumentRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
    2: required string DocumentID (go.tag = "json:\"document_id\"", api.path="document_id"),
}

struct GetDocumentResponse {
    1: KnowledgeBaseDocument Document (go.tag = "json:\"document\""),
}

struct LarkDocument {
    1: string Title (go.tag = "json:\"title\""),
    2: string Content (go.tag = "json:\"content\""),
    3: string URL (go.tag = "json:\"url\""),
    4: bool IsUploaded (go.tag = "json:\"is_uploaded\"") ,
    5: DocumentContentType ContentType (go.tag = "json:\"content_type\""),
    6: string OwnerName (go.tag = "json:\"owner_name\""),
    7: bool HasChild (go.tag = "json:\"has_child\""),
    8: bool IsRoot (go.tag = "json:\"is_root\""),
    9: string DocumentID (go.tag = "json:\"document_id\""),
}

typedef string ImportType
const ImportType ImportTypeSingle = "single"
const ImportType ImportTypeWikiTree = "wiki_tree"  // 会查询所有子目录

struct SearchLarkDocumentsRequest {
   1: required string Query (go.tag = "json:\"query\"", api.query="query")
   2: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
   3: optional ImportType ImportType (go.tag = "json:\"import_type\"", api.query="import_type")
}

struct SearchLarkDocumentsResponse {
    1: list<LarkDocument> Documents (go.tag = "json:\"documents\""),
    // 从知识库导入时返回
    2: dev_resource.WikiSpace WikiSpace (go.tag = "json:\"wiki_space\"")
}

struct RecallDatasetRequest {
   1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
   2: required string Query (go.tag = "json:\"query\"", api.query="query")
   3: required i64 TopK (go.tag = "json:\"top_k\"")
}

struct RecallDatasetResponse {
    1: list<RecallSegment> Segments (go.tag = "json:\"segments\""),
}

struct RecallSegment {
	1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
	2: required string DocumentID (go.tag = "json:\"document_id\"", api.path="document_id"),
	3: required string Content (go.tag = "json:\"content\""),
	4: required string Title (go.tag = "json:\"title\""),
	5: required string URL (go.tag = "json:\"url\""),
	6: required string LastUpdatedAt (go.tag = "json:\"last_updated_at\""),
	7: required string Owner (go.tag = "json:\"Owner\""),
	8: required string DocumentCreatedAt (go.tag = "json:\"document_created_at\""),
}

struct RecommendDocumentsRequest {
    1: list<ReferenceDocument> ReferenceDocuments (go.tag = "json:\"reference_documents\""),
    2: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
}

struct RecommendDocumentsResponse {
    1: list<LarkDocument> Documents (go.tag = "json:\"documents\""),
}

struct ReferenceDocument {
    1: string Title (go.tag = "json:\"title\""),
    3: string URL (go.tag = "json:\"url\""),
}

struct CountDocumentsRequest {
    1: required string DatasetID (go.tag = "json:\"dataset_id\"", api.path="dataset_id"),
}

struct CountDocumentsResponse {
    1: i64 AllTotal (go.tag = "json:\"all_total\""),
    2: i64 MyTotal (go.tag = "json:\"my_total\""),
}