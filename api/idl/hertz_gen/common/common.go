// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package common

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

const (
	EventNameError = "error"
)

type ErrorCode int64

const (
	ErrorCode_ErrUndefined                          ErrorCode = 0
	ErrorCode_ErrNoAuth                             ErrorCode = 1001
	ErrorCode_ErrCheckAuth                          ErrorCode = 1002
	ErrorCode_ErrInternal                           ErrorCode = 2001
	ErrorCode_ErrResourceProcessing                 ErrorCode = 2002
	ErrorCode_ErrModelReqFail                       ErrorCode = 3003
	ErrorCode_ErrEmbeddingModelNotExist             ErrorCode = 3004
	ErrorCode_ErrParamInvalid                       ErrorCode = 4001
	ErrorCode_ErrExist                              ErrorCode = 4002
	ErrorCode_ErrOperating                          ErrorCode = 4003
	ErrorCode_ErrContentTooLong                     ErrorCode = 4006
	ErrorCode_ErrRateLimitExceeded                  ErrorCode = 4007
	ErrorCode_ErrQuotaExceeded                      ErrorCode = 4008
	ErrorCode_ErrLogOut                             ErrorCode = 4010
	ErrorCode_ErrAccessReject                       ErrorCode = 4011
	ErrorCode_ErrDowngrade                          ErrorCode = 4012
	ErrorCode_ErrCountryBlocked                     ErrorCode = 4013
	ErrorCode_ErrDeregister                         ErrorCode = 4014
	ErrorCode_ErrNewRiskControl                     ErrorCode = 4019
	ErrorCode_ErrRecordNotFound                     ErrorCode = 4020
	ErrorCode_ErrUsageReachLimit                    ErrorCode = 4021
	ErrorCode_ErrPromptTokensOverLimit              ErrorCode = 4022
	ErrorCode_ErrUnknownModel                       ErrorCode = 4023
	ErrorCode_ErrAgentRunLoopExceeded               ErrorCode = 5001
	ErrorCode_ErrAgentRunTimeExceeded               ErrorCode = 5002
	ErrorCode_ErrAgentRunQuotaExceeded              ErrorCode = 5003
	ErrorCode_ErrAgentRunBusy                       ErrorCode = 5004
	ErrorCode_ErrContainerStartupTimeout            ErrorCode = 5005
	ErrorCode_ErrContainerIsDeleted                 ErrorCode = 5006
	ErrorCode_ErrInsufficientStock                  ErrorCode = 6001
	ErrorCode_ErrBindInvitation                     ErrorCode = 6002
	ErrorCode_ErrNotEnoughPoints                    ErrorCode = 6003
	ErrorCode_ErrNotInEventTime                     ErrorCode = 6004
	ErrorCode_ErrReachedUpperLimit                  ErrorCode = 6005
	ErrorCode_ErrAccountAbnormality                 ErrorCode = 6006
	ErrorCode_ErrDecryption                         ErrorCode = 6007
	ErrorCode_ErrNotGetPrizeTime                    ErrorCode = 6008
	ErrorCode_ErrReviewResultNotReleased            ErrorCode = 6009
	ErrorCode_ErrNoQualification                    ErrorCode = 6010
	ErrorCode_ErrRedemptionLimitReached             ErrorCode = 6011
	ErrorCode_ErrSecurityCheckFail                  ErrorCode = 7001
	ErrorCode_ErrCompetetionInvalid                 ErrorCode = 7002
	ErrorCode_ErrDuplicateCompetitionRegistration   ErrorCode = 7003
	ErrorCode_ErrTeamFull                           ErrorCode = 7004
	ErrorCode_ErrDuplicateTeamRegistration          ErrorCode = 7005
	ErrorCode_ErrTeamNoAuth                         ErrorCode = 7006
	ErrorCode_ErrInvalidTeam                        ErrorCode = 7007
	ErrorCode_ErrDuplicateTeamName                  ErrorCode = 7008
	ErrorCode_ErrInvalidTeamApplication             ErrorCode = 7009
	ErrorCode_ErrInvalidTeamMember                  ErrorCode = 7010
	ErrorCode_ErrExistedEntitlement                 ErrorCode = 7011
	ErrorCode_ErrCodePreCheckFail                   ErrorCode = 7012
	ErrorCode_ErrSandboxCreateTimeExceeded          ErrorCode = 8001
	ErrorCode_ErrSandboxRunTimeExceeded             ErrorCode = 8002
	ErrorCode_ErrSandboxStillRunning                ErrorCode = 8003
	ErrorCode_ErrImageReviewUnpass                  ErrorCode = 8004
	ErrorCode_ErrImageReviewDownload                ErrorCode = 8005
	ErrorCode_ErrImageReviewImageRead               ErrorCode = 8006
	ErrorCode_ErrImageReviewUpload                  ErrorCode = 8007
	ErrorCode_ErrImageReviewGetUrl                  ErrorCode = 8008
	ErrorCode_ErrImageReviewDecode                  ErrorCode = 8009
	ErrorCode_ErrImageReviewInternal                ErrorCode = 8010
	ErrorCode_ErrTextReviewUnpass                   ErrorCode = 8011
	ErrorCode_ErrSandboxInternalError               ErrorCode = 8012
	ErrorCode_ErrTraePayRisk                        ErrorCode = 9001
	ErrorCode_ErrTraePayUnexpiredOrder              ErrorCode = 9002
	ErrorCode_ErrTraePayRegionNotAllowed            ErrorCode = 9003
	ErrorCode_ErrTraePayParamInvalid                ErrorCode = 9004
	ErrorCode_ErrTraePayEntitlementExists           ErrorCode = 9005
	ErrorCode_ErrTraePayInvalidPI                   ErrorCode = 9006
	ErrorCode_ErrNextAgentReachedUpperLimit         ErrorCode = 10001
	ErrorCode_ErrNextSessionStopped                 ErrorCode = 10002
	ErrorCode_ErrNextAgentNotAllowedUseInternalTool ErrorCode = 10003
	ErrorCode_ErrInternalFatal                      ErrorCode = 10004
	ErrorCode_ErrNextAgentReachedGlobalLimit        ErrorCode = 10005
	ErrorCode_ErrNextAgentGrantAccessEnd            ErrorCode = 10006
	// MCP 激活数量限制
	ErrorCode_ErrMCPActiveLimitExceeded ErrorCode = 10007
	// 文档不存在
	ErrorCode_ErrDocumentNotExist ErrorCode = 10008
	// 文档类型不支持
	ErrorCode_ErrDocumentTypeNotSupport ErrorCode = 10009
	// MCP配置为空
	ErrorCode_ErrCodeMCPConfigEmpty ErrorCode = 10010
	// MCP调用失败
	ErrorCode_ErrCodeMCPConnectFailed ErrorCode = 10011
	// MCP鉴权失败
	ErrorCode_ErrCodeMCPPermissionVerificationFailed ErrorCode = 10012
	// MCP配置的PSM有误
	ErrorCode_ErrCodeMCPPSMNotFound ErrorCode = 10013
	// 机器人不在群组中
	ErrorCode_ErrBotNotInGroup ErrorCode = 10014
	// 用户不在群组中
	ErrorCode_ErrUserNotInGroup ErrorCode = 10015
	// 不能操作外部群组
	ErrorCode_ErrCanNotOperateOuterGroup ErrorCode = 10016
)

func (p ErrorCode) String() string {
	switch p {
	case ErrorCode_ErrUndefined:
		return "ErrUndefined"
	case ErrorCode_ErrNoAuth:
		return "ErrNoAuth"
	case ErrorCode_ErrCheckAuth:
		return "ErrCheckAuth"
	case ErrorCode_ErrInternal:
		return "ErrInternal"
	case ErrorCode_ErrResourceProcessing:
		return "ErrResourceProcessing"
	case ErrorCode_ErrModelReqFail:
		return "ErrModelReqFail"
	case ErrorCode_ErrEmbeddingModelNotExist:
		return "ErrEmbeddingModelNotExist"
	case ErrorCode_ErrParamInvalid:
		return "ErrParamInvalid"
	case ErrorCode_ErrExist:
		return "ErrExist"
	case ErrorCode_ErrOperating:
		return "ErrOperating"
	case ErrorCode_ErrContentTooLong:
		return "ErrContentTooLong"
	case ErrorCode_ErrRateLimitExceeded:
		return "ErrRateLimitExceeded"
	case ErrorCode_ErrQuotaExceeded:
		return "ErrQuotaExceeded"
	case ErrorCode_ErrLogOut:
		return "ErrLogOut"
	case ErrorCode_ErrAccessReject:
		return "ErrAccessReject"
	case ErrorCode_ErrDowngrade:
		return "ErrDowngrade"
	case ErrorCode_ErrCountryBlocked:
		return "ErrCountryBlocked"
	case ErrorCode_ErrDeregister:
		return "ErrDeregister"
	case ErrorCode_ErrNewRiskControl:
		return "ErrNewRiskControl"
	case ErrorCode_ErrRecordNotFound:
		return "ErrRecordNotFound"
	case ErrorCode_ErrUsageReachLimit:
		return "ErrUsageReachLimit"
	case ErrorCode_ErrPromptTokensOverLimit:
		return "ErrPromptTokensOverLimit"
	case ErrorCode_ErrUnknownModel:
		return "ErrUnknownModel"
	case ErrorCode_ErrAgentRunLoopExceeded:
		return "ErrAgentRunLoopExceeded"
	case ErrorCode_ErrAgentRunTimeExceeded:
		return "ErrAgentRunTimeExceeded"
	case ErrorCode_ErrAgentRunQuotaExceeded:
		return "ErrAgentRunQuotaExceeded"
	case ErrorCode_ErrAgentRunBusy:
		return "ErrAgentRunBusy"
	case ErrorCode_ErrContainerStartupTimeout:
		return "ErrContainerStartupTimeout"
	case ErrorCode_ErrContainerIsDeleted:
		return "ErrContainerIsDeleted"
	case ErrorCode_ErrInsufficientStock:
		return "ErrInsufficientStock"
	case ErrorCode_ErrBindInvitation:
		return "ErrBindInvitation"
	case ErrorCode_ErrNotEnoughPoints:
		return "ErrNotEnoughPoints"
	case ErrorCode_ErrNotInEventTime:
		return "ErrNotInEventTime"
	case ErrorCode_ErrReachedUpperLimit:
		return "ErrReachedUpperLimit"
	case ErrorCode_ErrAccountAbnormality:
		return "ErrAccountAbnormality"
	case ErrorCode_ErrDecryption:
		return "ErrDecryption"
	case ErrorCode_ErrNotGetPrizeTime:
		return "ErrNotGetPrizeTime"
	case ErrorCode_ErrReviewResultNotReleased:
		return "ErrReviewResultNotReleased"
	case ErrorCode_ErrNoQualification:
		return "ErrNoQualification"
	case ErrorCode_ErrRedemptionLimitReached:
		return "ErrRedemptionLimitReached"
	case ErrorCode_ErrSecurityCheckFail:
		return "ErrSecurityCheckFail"
	case ErrorCode_ErrCompetetionInvalid:
		return "ErrCompetetionInvalid"
	case ErrorCode_ErrDuplicateCompetitionRegistration:
		return "ErrDuplicateCompetitionRegistration"
	case ErrorCode_ErrTeamFull:
		return "ErrTeamFull"
	case ErrorCode_ErrDuplicateTeamRegistration:
		return "ErrDuplicateTeamRegistration"
	case ErrorCode_ErrTeamNoAuth:
		return "ErrTeamNoAuth"
	case ErrorCode_ErrInvalidTeam:
		return "ErrInvalidTeam"
	case ErrorCode_ErrDuplicateTeamName:
		return "ErrDuplicateTeamName"
	case ErrorCode_ErrInvalidTeamApplication:
		return "ErrInvalidTeamApplication"
	case ErrorCode_ErrInvalidTeamMember:
		return "ErrInvalidTeamMember"
	case ErrorCode_ErrExistedEntitlement:
		return "ErrExistedEntitlement"
	case ErrorCode_ErrCodePreCheckFail:
		return "ErrCodePreCheckFail"
	case ErrorCode_ErrSandboxCreateTimeExceeded:
		return "ErrSandboxCreateTimeExceeded"
	case ErrorCode_ErrSandboxRunTimeExceeded:
		return "ErrSandboxRunTimeExceeded"
	case ErrorCode_ErrSandboxStillRunning:
		return "ErrSandboxStillRunning"
	case ErrorCode_ErrImageReviewUnpass:
		return "ErrImageReviewUnpass"
	case ErrorCode_ErrImageReviewDownload:
		return "ErrImageReviewDownload"
	case ErrorCode_ErrImageReviewImageRead:
		return "ErrImageReviewImageRead"
	case ErrorCode_ErrImageReviewUpload:
		return "ErrImageReviewUpload"
	case ErrorCode_ErrImageReviewGetUrl:
		return "ErrImageReviewGetUrl"
	case ErrorCode_ErrImageReviewDecode:
		return "ErrImageReviewDecode"
	case ErrorCode_ErrImageReviewInternal:
		return "ErrImageReviewInternal"
	case ErrorCode_ErrTextReviewUnpass:
		return "ErrTextReviewUnpass"
	case ErrorCode_ErrSandboxInternalError:
		return "ErrSandboxInternalError"
	case ErrorCode_ErrTraePayRisk:
		return "ErrTraePayRisk"
	case ErrorCode_ErrTraePayUnexpiredOrder:
		return "ErrTraePayUnexpiredOrder"
	case ErrorCode_ErrTraePayRegionNotAllowed:
		return "ErrTraePayRegionNotAllowed"
	case ErrorCode_ErrTraePayParamInvalid:
		return "ErrTraePayParamInvalid"
	case ErrorCode_ErrTraePayEntitlementExists:
		return "ErrTraePayEntitlementExists"
	case ErrorCode_ErrTraePayInvalidPI:
		return "ErrTraePayInvalidPI"
	case ErrorCode_ErrNextAgentReachedUpperLimit:
		return "ErrNextAgentReachedUpperLimit"
	case ErrorCode_ErrNextSessionStopped:
		return "ErrNextSessionStopped"
	case ErrorCode_ErrNextAgentNotAllowedUseInternalTool:
		return "ErrNextAgentNotAllowedUseInternalTool"
	case ErrorCode_ErrInternalFatal:
		return "ErrInternalFatal"
	case ErrorCode_ErrNextAgentReachedGlobalLimit:
		return "ErrNextAgentReachedGlobalLimit"
	case ErrorCode_ErrNextAgentGrantAccessEnd:
		return "ErrNextAgentGrantAccessEnd"
	case ErrorCode_ErrMCPActiveLimitExceeded:
		return "ErrMCPActiveLimitExceeded"
	case ErrorCode_ErrDocumentNotExist:
		return "ErrDocumentNotExist"
	case ErrorCode_ErrDocumentTypeNotSupport:
		return "ErrDocumentTypeNotSupport"
	case ErrorCode_ErrCodeMCPConfigEmpty:
		return "ErrCodeMCPConfigEmpty"
	case ErrorCode_ErrCodeMCPConnectFailed:
		return "ErrCodeMCPConnectFailed"
	case ErrorCode_ErrCodeMCPPermissionVerificationFailed:
		return "ErrCodeMCPPermissionVerificationFailed"
	case ErrorCode_ErrCodeMCPPSMNotFound:
		return "ErrCodeMCPPSMNotFound"
	case ErrorCode_ErrBotNotInGroup:
		return "ErrBotNotInGroup"
	case ErrorCode_ErrUserNotInGroup:
		return "ErrUserNotInGroup"
	case ErrorCode_ErrCanNotOperateOuterGroup:
		return "ErrCanNotOperateOuterGroup"
	}
	return "<UNSET>"
}

func ErrorCodeFromString(s string) (ErrorCode, error) {
	switch s {
	case "ErrUndefined":
		return ErrorCode_ErrUndefined, nil
	case "ErrNoAuth":
		return ErrorCode_ErrNoAuth, nil
	case "ErrCheckAuth":
		return ErrorCode_ErrCheckAuth, nil
	case "ErrInternal":
		return ErrorCode_ErrInternal, nil
	case "ErrResourceProcessing":
		return ErrorCode_ErrResourceProcessing, nil
	case "ErrModelReqFail":
		return ErrorCode_ErrModelReqFail, nil
	case "ErrEmbeddingModelNotExist":
		return ErrorCode_ErrEmbeddingModelNotExist, nil
	case "ErrParamInvalid":
		return ErrorCode_ErrParamInvalid, nil
	case "ErrExist":
		return ErrorCode_ErrExist, nil
	case "ErrOperating":
		return ErrorCode_ErrOperating, nil
	case "ErrContentTooLong":
		return ErrorCode_ErrContentTooLong, nil
	case "ErrRateLimitExceeded":
		return ErrorCode_ErrRateLimitExceeded, nil
	case "ErrQuotaExceeded":
		return ErrorCode_ErrQuotaExceeded, nil
	case "ErrLogOut":
		return ErrorCode_ErrLogOut, nil
	case "ErrAccessReject":
		return ErrorCode_ErrAccessReject, nil
	case "ErrDowngrade":
		return ErrorCode_ErrDowngrade, nil
	case "ErrCountryBlocked":
		return ErrorCode_ErrCountryBlocked, nil
	case "ErrDeregister":
		return ErrorCode_ErrDeregister, nil
	case "ErrNewRiskControl":
		return ErrorCode_ErrNewRiskControl, nil
	case "ErrRecordNotFound":
		return ErrorCode_ErrRecordNotFound, nil
	case "ErrUsageReachLimit":
		return ErrorCode_ErrUsageReachLimit, nil
	case "ErrPromptTokensOverLimit":
		return ErrorCode_ErrPromptTokensOverLimit, nil
	case "ErrUnknownModel":
		return ErrorCode_ErrUnknownModel, nil
	case "ErrAgentRunLoopExceeded":
		return ErrorCode_ErrAgentRunLoopExceeded, nil
	case "ErrAgentRunTimeExceeded":
		return ErrorCode_ErrAgentRunTimeExceeded, nil
	case "ErrAgentRunQuotaExceeded":
		return ErrorCode_ErrAgentRunQuotaExceeded, nil
	case "ErrAgentRunBusy":
		return ErrorCode_ErrAgentRunBusy, nil
	case "ErrContainerStartupTimeout":
		return ErrorCode_ErrContainerStartupTimeout, nil
	case "ErrContainerIsDeleted":
		return ErrorCode_ErrContainerIsDeleted, nil
	case "ErrInsufficientStock":
		return ErrorCode_ErrInsufficientStock, nil
	case "ErrBindInvitation":
		return ErrorCode_ErrBindInvitation, nil
	case "ErrNotEnoughPoints":
		return ErrorCode_ErrNotEnoughPoints, nil
	case "ErrNotInEventTime":
		return ErrorCode_ErrNotInEventTime, nil
	case "ErrReachedUpperLimit":
		return ErrorCode_ErrReachedUpperLimit, nil
	case "ErrAccountAbnormality":
		return ErrorCode_ErrAccountAbnormality, nil
	case "ErrDecryption":
		return ErrorCode_ErrDecryption, nil
	case "ErrNotGetPrizeTime":
		return ErrorCode_ErrNotGetPrizeTime, nil
	case "ErrReviewResultNotReleased":
		return ErrorCode_ErrReviewResultNotReleased, nil
	case "ErrNoQualification":
		return ErrorCode_ErrNoQualification, nil
	case "ErrRedemptionLimitReached":
		return ErrorCode_ErrRedemptionLimitReached, nil
	case "ErrSecurityCheckFail":
		return ErrorCode_ErrSecurityCheckFail, nil
	case "ErrCompetetionInvalid":
		return ErrorCode_ErrCompetetionInvalid, nil
	case "ErrDuplicateCompetitionRegistration":
		return ErrorCode_ErrDuplicateCompetitionRegistration, nil
	case "ErrTeamFull":
		return ErrorCode_ErrTeamFull, nil
	case "ErrDuplicateTeamRegistration":
		return ErrorCode_ErrDuplicateTeamRegistration, nil
	case "ErrTeamNoAuth":
		return ErrorCode_ErrTeamNoAuth, nil
	case "ErrInvalidTeam":
		return ErrorCode_ErrInvalidTeam, nil
	case "ErrDuplicateTeamName":
		return ErrorCode_ErrDuplicateTeamName, nil
	case "ErrInvalidTeamApplication":
		return ErrorCode_ErrInvalidTeamApplication, nil
	case "ErrInvalidTeamMember":
		return ErrorCode_ErrInvalidTeamMember, nil
	case "ErrExistedEntitlement":
		return ErrorCode_ErrExistedEntitlement, nil
	case "ErrCodePreCheckFail":
		return ErrorCode_ErrCodePreCheckFail, nil
	case "ErrSandboxCreateTimeExceeded":
		return ErrorCode_ErrSandboxCreateTimeExceeded, nil
	case "ErrSandboxRunTimeExceeded":
		return ErrorCode_ErrSandboxRunTimeExceeded, nil
	case "ErrSandboxStillRunning":
		return ErrorCode_ErrSandboxStillRunning, nil
	case "ErrImageReviewUnpass":
		return ErrorCode_ErrImageReviewUnpass, nil
	case "ErrImageReviewDownload":
		return ErrorCode_ErrImageReviewDownload, nil
	case "ErrImageReviewImageRead":
		return ErrorCode_ErrImageReviewImageRead, nil
	case "ErrImageReviewUpload":
		return ErrorCode_ErrImageReviewUpload, nil
	case "ErrImageReviewGetUrl":
		return ErrorCode_ErrImageReviewGetUrl, nil
	case "ErrImageReviewDecode":
		return ErrorCode_ErrImageReviewDecode, nil
	case "ErrImageReviewInternal":
		return ErrorCode_ErrImageReviewInternal, nil
	case "ErrTextReviewUnpass":
		return ErrorCode_ErrTextReviewUnpass, nil
	case "ErrSandboxInternalError":
		return ErrorCode_ErrSandboxInternalError, nil
	case "ErrTraePayRisk":
		return ErrorCode_ErrTraePayRisk, nil
	case "ErrTraePayUnexpiredOrder":
		return ErrorCode_ErrTraePayUnexpiredOrder, nil
	case "ErrTraePayRegionNotAllowed":
		return ErrorCode_ErrTraePayRegionNotAllowed, nil
	case "ErrTraePayParamInvalid":
		return ErrorCode_ErrTraePayParamInvalid, nil
	case "ErrTraePayEntitlementExists":
		return ErrorCode_ErrTraePayEntitlementExists, nil
	case "ErrTraePayInvalidPI":
		return ErrorCode_ErrTraePayInvalidPI, nil
	case "ErrNextAgentReachedUpperLimit":
		return ErrorCode_ErrNextAgentReachedUpperLimit, nil
	case "ErrNextSessionStopped":
		return ErrorCode_ErrNextSessionStopped, nil
	case "ErrNextAgentNotAllowedUseInternalTool":
		return ErrorCode_ErrNextAgentNotAllowedUseInternalTool, nil
	case "ErrInternalFatal":
		return ErrorCode_ErrInternalFatal, nil
	case "ErrNextAgentReachedGlobalLimit":
		return ErrorCode_ErrNextAgentReachedGlobalLimit, nil
	case "ErrNextAgentGrantAccessEnd":
		return ErrorCode_ErrNextAgentGrantAccessEnd, nil
	case "ErrMCPActiveLimitExceeded":
		return ErrorCode_ErrMCPActiveLimitExceeded, nil
	case "ErrDocumentNotExist":
		return ErrorCode_ErrDocumentNotExist, nil
	case "ErrDocumentTypeNotSupport":
		return ErrorCode_ErrDocumentTypeNotSupport, nil
	case "ErrCodeMCPConfigEmpty":
		return ErrorCode_ErrCodeMCPConfigEmpty, nil
	case "ErrCodeMCPConnectFailed":
		return ErrorCode_ErrCodeMCPConnectFailed, nil
	case "ErrCodeMCPPermissionVerificationFailed":
		return ErrorCode_ErrCodeMCPPermissionVerificationFailed, nil
	case "ErrCodeMCPPSMNotFound":
		return ErrorCode_ErrCodeMCPPSMNotFound, nil
	case "ErrBotNotInGroup":
		return ErrorCode_ErrBotNotInGroup, nil
	case "ErrUserNotInGroup":
		return ErrorCode_ErrUserNotInGroup, nil
	case "ErrCanNotOperateOuterGroup":
		return ErrorCode_ErrCanNotOperateOuterGroup, nil
	}
	return ErrorCode(0), fmt.Errorf("not a valid ErrorCode string")
}

func ErrorCodePtr(v ErrorCode) *ErrorCode { return &v }
func (p *ErrorCode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ErrorCode(result.Int64)
	return
}

func (p *ErrorCode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// JsonVariables is a JSON encoded object string.
// As thrift does not support map[string]any, we use encoded JSON object string.
// @example: {"locale": "zh-cn"}
type JsonVariables = string

type BaseResp struct {
	Code    int64  `thrift:"code,1" json:"code" `
	Message string `thrift:"message,2" json:"message" `
}

func NewBaseResp() *BaseResp {
	return &BaseResp{

		Code:    0,
		Message: "",
	}
}

func (p *BaseResp) InitDefault() {
	p.Code = 0
	p.Message = ""
}

func (p *BaseResp) GetCode() (v int64) {
	return p.Code
}

func (p *BaseResp) GetMessage() (v string) {
	return p.Message
}

func (p *BaseResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BaseResp(%+v)", *p)
}

type HTTPRequest struct {
	Authorization *string `thrift:"Authorization,1,optional" header:"authorization" json:"authorization,omitempty"`
	XServiceToken *string `thrift:"XServiceToken,2,optional" header:"x-service-token" json:"xservice_token,omitempty"`
	XJwtToken     *string `thrift:"XJwtToken,3,optional" header:"x-jwt-token" json:"xjwt_token,omitempty"`
	XUsername     *string `thrift:"XUsername,4,optional" header:"x-username" json:"xusername,omitempty"`
	XAuthToken    *string `thrift:"XAuthToken,5,optional" header:"x-auth-token" json:"xauth_token,omitempty"`
	XIDEToken     *string `thrift:"XIDEToken,6,optional" header:"x-ide-token" json:"xidetoken,omitempty"`
	XSendTS       *string `thrift:"XSendTS,7,optional" header:"x-send-ts" json:"xsend_ts,omitempty"`
	XAppId        *string `thrift:"XAppId,8,optional" header:"x-app-id" json:"xapp_id,omitempty"`
	// For h2, use :authority
	Host *string `thrift:"Host,9,optional" header:"host" json:"host,omitempty"`
	// Added by AGW.
	ClientIP *string `thrift:"ClientIP,10,optional" header:"Tt-Agw-Client-Ip" json:"client_ip,omitempty"`
	// AGW 开始处理当前请求的时间戳
	AGWRecvTs   *int64              `thrift:"AGWRecvTs,11,optional" header:"X-Tt-Agw-Ts" json:"agwrecv_ts,omitempty"`
	XAppVersion *string             `thrift:"XAppVersion,12,optional" header:"x-app-version" json:"xapp_version,omitempty"`
	Headers     map[string][]string `thrift:"Headers,13,optional" json:"headers,omitempty" `
	Request     []byte              `thrift:"Request,14,optional" json:"request,omitempty" `
	Cookie      *string             `thrift:"Cookie,15,optional" header:"Cookie" json:"cookie,omitempty"`
	UserAgent   *string             `thrift:"UserAgent,16,optional" header:"User-Agent" json:"user_agent,omitempty"`
	Forwarded   *string             `thrift:"Forwarded,17,optional" header:"X-Forwarded-For" json:"forwarded,omitempty"`
	Referer     *string             `thrift:"Referer,18,optional" header:"Referer" json:"referer,omitempty"`
	WebSignRes  *string             `thrift:"WebSignRes,19,optional" header:"web_sign_res" json:"web_sign_res,omitempty"`
	WebMSToken  *string             `thrift:"WebMSToken,20,optional" header:"web_ms_token" json:"web_mstoken,omitempty"`
	TtWid       *string             `thrift:"TtWid,21,optional" cookie:"ttwid" json:"tt_wid,omitempty"`
	FP          *string             `thrift:"FP,22,optional" header:"fp" json:"fp,omitempty"`
}

func NewHTTPRequest() *HTTPRequest {
	return &HTTPRequest{}
}

func (p *HTTPRequest) InitDefault() {
}

var HTTPRequest_Authorization_DEFAULT string

func (p *HTTPRequest) GetAuthorization() (v string) {
	if !p.IsSetAuthorization() {
		return HTTPRequest_Authorization_DEFAULT
	}
	return *p.Authorization
}

var HTTPRequest_XServiceToken_DEFAULT string

func (p *HTTPRequest) GetXServiceToken() (v string) {
	if !p.IsSetXServiceToken() {
		return HTTPRequest_XServiceToken_DEFAULT
	}
	return *p.XServiceToken
}

var HTTPRequest_XJwtToken_DEFAULT string

func (p *HTTPRequest) GetXJwtToken() (v string) {
	if !p.IsSetXJwtToken() {
		return HTTPRequest_XJwtToken_DEFAULT
	}
	return *p.XJwtToken
}

var HTTPRequest_XUsername_DEFAULT string

func (p *HTTPRequest) GetXUsername() (v string) {
	if !p.IsSetXUsername() {
		return HTTPRequest_XUsername_DEFAULT
	}
	return *p.XUsername
}

var HTTPRequest_XAuthToken_DEFAULT string

func (p *HTTPRequest) GetXAuthToken() (v string) {
	if !p.IsSetXAuthToken() {
		return HTTPRequest_XAuthToken_DEFAULT
	}
	return *p.XAuthToken
}

var HTTPRequest_XIDEToken_DEFAULT string

func (p *HTTPRequest) GetXIDEToken() (v string) {
	if !p.IsSetXIDEToken() {
		return HTTPRequest_XIDEToken_DEFAULT
	}
	return *p.XIDEToken
}

var HTTPRequest_XSendTS_DEFAULT string

func (p *HTTPRequest) GetXSendTS() (v string) {
	if !p.IsSetXSendTS() {
		return HTTPRequest_XSendTS_DEFAULT
	}
	return *p.XSendTS
}

var HTTPRequest_XAppId_DEFAULT string

func (p *HTTPRequest) GetXAppId() (v string) {
	if !p.IsSetXAppId() {
		return HTTPRequest_XAppId_DEFAULT
	}
	return *p.XAppId
}

var HTTPRequest_Host_DEFAULT string

func (p *HTTPRequest) GetHost() (v string) {
	if !p.IsSetHost() {
		return HTTPRequest_Host_DEFAULT
	}
	return *p.Host
}

var HTTPRequest_ClientIP_DEFAULT string

func (p *HTTPRequest) GetClientIP() (v string) {
	if !p.IsSetClientIP() {
		return HTTPRequest_ClientIP_DEFAULT
	}
	return *p.ClientIP
}

var HTTPRequest_AGWRecvTs_DEFAULT int64

func (p *HTTPRequest) GetAGWRecvTs() (v int64) {
	if !p.IsSetAGWRecvTs() {
		return HTTPRequest_AGWRecvTs_DEFAULT
	}
	return *p.AGWRecvTs
}

var HTTPRequest_XAppVersion_DEFAULT string

func (p *HTTPRequest) GetXAppVersion() (v string) {
	if !p.IsSetXAppVersion() {
		return HTTPRequest_XAppVersion_DEFAULT
	}
	return *p.XAppVersion
}

var HTTPRequest_Headers_DEFAULT map[string][]string

func (p *HTTPRequest) GetHeaders() (v map[string][]string) {
	if !p.IsSetHeaders() {
		return HTTPRequest_Headers_DEFAULT
	}
	return p.Headers
}

var HTTPRequest_Request_DEFAULT []byte

func (p *HTTPRequest) GetRequest() (v []byte) {
	if !p.IsSetRequest() {
		return HTTPRequest_Request_DEFAULT
	}
	return p.Request
}

var HTTPRequest_Cookie_DEFAULT string

func (p *HTTPRequest) GetCookie() (v string) {
	if !p.IsSetCookie() {
		return HTTPRequest_Cookie_DEFAULT
	}
	return *p.Cookie
}

var HTTPRequest_UserAgent_DEFAULT string

func (p *HTTPRequest) GetUserAgent() (v string) {
	if !p.IsSetUserAgent() {
		return HTTPRequest_UserAgent_DEFAULT
	}
	return *p.UserAgent
}

var HTTPRequest_Forwarded_DEFAULT string

func (p *HTTPRequest) GetForwarded() (v string) {
	if !p.IsSetForwarded() {
		return HTTPRequest_Forwarded_DEFAULT
	}
	return *p.Forwarded
}

var HTTPRequest_Referer_DEFAULT string

func (p *HTTPRequest) GetReferer() (v string) {
	if !p.IsSetReferer() {
		return HTTPRequest_Referer_DEFAULT
	}
	return *p.Referer
}

var HTTPRequest_WebSignRes_DEFAULT string

func (p *HTTPRequest) GetWebSignRes() (v string) {
	if !p.IsSetWebSignRes() {
		return HTTPRequest_WebSignRes_DEFAULT
	}
	return *p.WebSignRes
}

var HTTPRequest_WebMSToken_DEFAULT string

func (p *HTTPRequest) GetWebMSToken() (v string) {
	if !p.IsSetWebMSToken() {
		return HTTPRequest_WebMSToken_DEFAULT
	}
	return *p.WebMSToken
}

var HTTPRequest_TtWid_DEFAULT string

func (p *HTTPRequest) GetTtWid() (v string) {
	if !p.IsSetTtWid() {
		return HTTPRequest_TtWid_DEFAULT
	}
	return *p.TtWid
}

var HTTPRequest_FP_DEFAULT string

func (p *HTTPRequest) GetFP() (v string) {
	if !p.IsSetFP() {
		return HTTPRequest_FP_DEFAULT
	}
	return *p.FP
}

func (p *HTTPRequest) IsSetAuthorization() bool {
	return p.Authorization != nil
}

func (p *HTTPRequest) IsSetXServiceToken() bool {
	return p.XServiceToken != nil
}

func (p *HTTPRequest) IsSetXJwtToken() bool {
	return p.XJwtToken != nil
}

func (p *HTTPRequest) IsSetXUsername() bool {
	return p.XUsername != nil
}

func (p *HTTPRequest) IsSetXAuthToken() bool {
	return p.XAuthToken != nil
}

func (p *HTTPRequest) IsSetXIDEToken() bool {
	return p.XIDEToken != nil
}

func (p *HTTPRequest) IsSetXSendTS() bool {
	return p.XSendTS != nil
}

func (p *HTTPRequest) IsSetXAppId() bool {
	return p.XAppId != nil
}

func (p *HTTPRequest) IsSetHost() bool {
	return p.Host != nil
}

func (p *HTTPRequest) IsSetClientIP() bool {
	return p.ClientIP != nil
}

func (p *HTTPRequest) IsSetAGWRecvTs() bool {
	return p.AGWRecvTs != nil
}

func (p *HTTPRequest) IsSetXAppVersion() bool {
	return p.XAppVersion != nil
}

func (p *HTTPRequest) IsSetHeaders() bool {
	return p.Headers != nil
}

func (p *HTTPRequest) IsSetRequest() bool {
	return p.Request != nil
}

func (p *HTTPRequest) IsSetCookie() bool {
	return p.Cookie != nil
}

func (p *HTTPRequest) IsSetUserAgent() bool {
	return p.UserAgent != nil
}

func (p *HTTPRequest) IsSetForwarded() bool {
	return p.Forwarded != nil
}

func (p *HTTPRequest) IsSetReferer() bool {
	return p.Referer != nil
}

func (p *HTTPRequest) IsSetWebSignRes() bool {
	return p.WebSignRes != nil
}

func (p *HTTPRequest) IsSetWebMSToken() bool {
	return p.WebMSToken != nil
}

func (p *HTTPRequest) IsSetTtWid() bool {
	return p.TtWid != nil
}

func (p *HTTPRequest) IsSetFP() bool {
	return p.FP != nil
}

func (p *HTTPRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HTTPRequest(%+v)", *p)
}

type HTTPResponse struct {
	XSendTS *string `thrift:"XSendTS,1,optional" json:"xsend_ts,omitempty" `
	XRecvTS *string `thrift:"XRecvTS,2,optional" json:"xrecv_ts,omitempty" `
}

func NewHTTPResponse() *HTTPResponse {
	return &HTTPResponse{}
}

func (p *HTTPResponse) InitDefault() {
}

var HTTPResponse_XSendTS_DEFAULT string

func (p *HTTPResponse) GetXSendTS() (v string) {
	if !p.IsSetXSendTS() {
		return HTTPResponse_XSendTS_DEFAULT
	}
	return *p.XSendTS
}

var HTTPResponse_XRecvTS_DEFAULT string

func (p *HTTPResponse) GetXRecvTS() (v string) {
	if !p.IsSetXRecvTS() {
		return HTTPResponse_XRecvTS_DEFAULT
	}
	return *p.XRecvTS
}

func (p *HTTPResponse) IsSetXSendTS() bool {
	return p.XSendTS != nil
}

func (p *HTTPResponse) IsSetXRecvTS() bool {
	return p.XRecvTS != nil
}

func (p *HTTPResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HTTPResponse(%+v)", *p)
}

// Format SSE Response
type SSEResponse struct {
	Event        *string       `thrift:"Event,1,optional" json:"event,omitempty" `
	Data         *string       `thrift:"Data,2,optional" json:"data,omitempty" `
	HTTPResponse *HTTPResponse `thrift:"HTTPResponse,201,optional" json:"httpresponse,omitempty" `
}

func NewSSEResponse() *SSEResponse {
	return &SSEResponse{}
}

func (p *SSEResponse) InitDefault() {
}

var SSEResponse_Event_DEFAULT string

func (p *SSEResponse) GetEvent() (v string) {
	if !p.IsSetEvent() {
		return SSEResponse_Event_DEFAULT
	}
	return *p.Event
}

var SSEResponse_Data_DEFAULT string

func (p *SSEResponse) GetData() (v string) {
	if !p.IsSetData() {
		return SSEResponse_Data_DEFAULT
	}
	return *p.Data
}

var SSEResponse_HTTPResponse_DEFAULT *HTTPResponse

func (p *SSEResponse) GetHTTPResponse() (v *HTTPResponse) {
	if !p.IsSetHTTPResponse() {
		return SSEResponse_HTTPResponse_DEFAULT
	}
	return p.HTTPResponse
}

func (p *SSEResponse) IsSetEvent() bool {
	return p.Event != nil
}

func (p *SSEResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *SSEResponse) IsSetHTTPResponse() bool {
	return p.HTTPResponse != nil
}

func (p *SSEResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SSEResponse(%+v)", *p)
}

// Error detail.
type EventError struct {
	// Error Code.
	// @example: 3001
	Code int64 `thrift:"Code,1,required" json:"code,required"`
	// Error detail.
	// @example: LLM call is failed.
	Error string `thrift:"Error,2,required" json:"error,required"`
	// Message to be presented to user.
	// @example: Sorry, but the request is failed, please retry.
	Message string `thrift:"Message,3,required" json:"message,required"`
}

func NewEventError() *EventError {
	return &EventError{}
}

func (p *EventError) InitDefault() {
}

func (p *EventError) GetCode() (v int64) {
	return p.Code
}

func (p *EventError) GetError() (v string) {
	return p.Error
}

func (p *EventError) GetMessage() (v string) {
	return p.Message
}

func (p *EventError) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventError(%+v)", *p)
}

type Error struct {
	Code    string `thrift:"Code,1"`
	Message string `thrift:"Message,2"`
	Error_  string `thrift:"Error,3"`
}

func NewError() *Error {
	return &Error{}
}

func (p *Error) InitDefault() {
}

func (p *Error) GetCode() (v string) {
	return p.Code
}

func (p *Error) GetMessage() (v string) {
	return p.Message
}

func (p *Error) GetError() (v string) {
	return p.Error_
}

func (p *Error) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Error(%+v)", *p)
}
func (p *Error) Error() string {
	return p.String()
}
