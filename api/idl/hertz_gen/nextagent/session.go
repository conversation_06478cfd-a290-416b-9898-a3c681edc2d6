// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

const (
	UserRoleAimoAdmin = "AimoAdmin"

	UserRoleAimoInternalDev = "AimoInternalDev"

	UserRoleAimoAgentDeveloper = "AimoAgentDeveloper"

	UserRoleAimoMCPDeveloper = "AimoMCPDeveloper"

	UserRoleAimoMCPPlayground = "AimoMCPPlayground"

	UserRoleAimoTraceAll = "AimoTraceAll"

	UserRoleAimoTraceSelf = "AimoTraceSelf"

	UserRoleAimoTraceDiagnose = "AimoTraceDiagnose"

	UserRoleViewOthers = "ViewOthers"

	UserRoleCollectionOperator = "CollectionOperator"

	SessionStatusCreated = "created"

	SessionStatusRunning = "running"

	SessionStatusCanceled = "canceled"

	SessionStatusStopped = "stopped"

	SessionStatusWaiting = "waiting"

	SessionStatusIdle = "idle"

	SessionStatusAbnormal = "abnormal"

	SessionStatusError = "error"

	SessionStatusClosed = "closed"

	CanNotResumeReasonUnknown = "unknown"

	CanNotResumeReasonDeleted = "deleted"

	CanNotResumeReasonNotAllowed = "not_allowed"

	CanNotResumeReasonExpired = "expired"

	SessionSourceUnknown = "unknown"

	SessionSourcePersonal = "personal"

	SessionSourceCronjob = "cronjob"

	SessionSourceCodebase = "codebase"

	SessionSourceSlardar = "slardar"

	TemplateLabelGeneral = "General"

	TemplateLabelProduct = "Product"

	TemplateLabelFrontend = "Frontend"

	TemplateLabelClient = "Client"

	TemplateLabelServer = "Server"

	TemplateLabelAlgorithm = "Algorithm"

	TemplateLabelData = "Data"

	TemplateLabelDesign = "Design"

	TemplateLabelOperations = "Operations"

	TemplateLabelPMO = "PMO"

	TemplateLabelQuality = "Quality"

	TemplateLabelOther = "Other"

	TemplateCategoryAnalysis = "Analysis"

	TemplateCategoryStudy = "Study"

	TemplateCategoryCode = "Code"

	TemplateCategoryProjectAsk = "ProjectAsk"

	TemplateCategoryWrite = "Write"

	TemplateCategoryTroubleshooting = "Troubleshooting"

	TemplateCategoryQuality = "Quality"

	TemplateVariableTypeAutoComplete = "AutoComplete"

	TemplateVariableTypeUpload = "Upload"

	TemplateVariableTypeUploadWithText = "UploadWithText"
)

type SessionScope int64

const (
	SessionScope_Unknown SessionScope = 0
	// 个人
	SessionScope_Private SessionScope = 1
	// 公司内公开
	SessionScope_Public SessionScope = 2
	// 项目内公开
	SessionScope_ProjectPublic SessionScope = 3
)

func (p SessionScope) String() string {
	switch p {
	case SessionScope_Unknown:
		return "Unknown"
	case SessionScope_Private:
		return "Private"
	case SessionScope_Public:
		return "Public"
	case SessionScope_ProjectPublic:
		return "ProjectPublic"
	}
	return "<UNSET>"
}

func SessionScopeFromString(s string) (SessionScope, error) {
	switch s {
	case "Unknown":
		return SessionScope_Unknown, nil
	case "Private":
		return SessionScope_Private, nil
	case "Public":
		return SessionScope_Public, nil
	case "ProjectPublic":
		return SessionScope_ProjectPublic, nil
	}
	return SessionScope(0), fmt.Errorf("not a valid SessionScope string")
}

func SessionScopePtr(v SessionScope) *SessionScope { return &v }
func (p *SessionScope) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SessionScope(result.Int64)
	return
}

func (p *SessionScope) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ListFilterType int64

const (
	// 空间内自己创建的
	ListFilterType_SelfCreated ListFilterType = 0
	// 空间内公开的
	ListFilterType_SpacePublic ListFilterType = 1
)

func (p ListFilterType) String() string {
	switch p {
	case ListFilterType_SelfCreated:
		return "SelfCreated"
	case ListFilterType_SpacePublic:
		return "SpacePublic"
	}
	return "<UNSET>"
}

func ListFilterTypeFromString(s string) (ListFilterType, error) {
	switch s {
	case "SelfCreated":
		return ListFilterType_SelfCreated, nil
	case "SpacePublic":
		return ListFilterType_SpacePublic, nil
	}
	return ListFilterType(0), fmt.Errorf("not a valid ListFilterType string")
}

func ListFilterTypePtr(v ListFilterType) *ListFilterType { return &v }
func (p *ListFilterType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ListFilterType(result.Int64)
	return
}

func (p *ListFilterType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SessionTab int64

const (
	// 全部会话
	SessionTab_All SessionTab = 0
	// 收藏会话
	SessionTab_Starred SessionTab = 1
	// 未收藏会话
	SessionTab_UnStarred SessionTab = 2
)

func (p SessionTab) String() string {
	switch p {
	case SessionTab_All:
		return "All"
	case SessionTab_Starred:
		return "Starred"
	case SessionTab_UnStarred:
		return "UnStarred"
	}
	return "<UNSET>"
}

func SessionTabFromString(s string) (SessionTab, error) {
	switch s {
	case "All":
		return SessionTab_All, nil
	case "Starred":
		return SessionTab_Starred, nil
	case "UnStarred":
		return SessionTab_UnStarred, nil
	}
	return SessionTab(0), fmt.Errorf("not a valid SessionTab string")
}

func SessionTabPtr(v SessionTab) *SessionTab { return &v }
func (p *SessionTab) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SessionTab(result.Int64)
	return
}

func (p *SessionTab) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TemplateSource int64

const (
	// 全部模板
	TemplateSource_All TemplateSource = 0
	// 我的模板
	TemplateSource_My TemplateSource = 1
	// 收藏模板
	TemplateSource_Star TemplateSource = 2
	// 项目内模板
	TemplateSource_Project TemplateSource = 3
)

func (p TemplateSource) String() string {
	switch p {
	case TemplateSource_All:
		return "All"
	case TemplateSource_My:
		return "My"
	case TemplateSource_Star:
		return "Star"
	case TemplateSource_Project:
		return "Project"
	}
	return "<UNSET>"
}

func TemplateSourceFromString(s string) (TemplateSource, error) {
	switch s {
	case "All":
		return TemplateSource_All, nil
	case "My":
		return TemplateSource_My, nil
	case "Star":
		return TemplateSource_Star, nil
	case "Project":
		return TemplateSource_Project, nil
	}
	return TemplateSource(0), fmt.Errorf("not a valid TemplateSource string")
}

func TemplateSourcePtr(v TemplateSource) *TemplateSource { return &v }
func (p *TemplateSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TemplateSource(result.Int64)
	return
}

func (p *TemplateSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TemplateScope int64

const (
	// 私有模板
	TemplateScope_Private TemplateScope = 0
	// 公司内公开模板
	TemplateScope_Public TemplateScope = 1
	// 共享模板
	TemplateScope_Shared TemplateScope = 2
	// 官方模板
	TemplateScope_Official TemplateScope = 3
	// 项目内公开模板
	TemplateScope_ProjectPublic TemplateScope = 4
)

func (p TemplateScope) String() string {
	switch p {
	case TemplateScope_Private:
		return "Private"
	case TemplateScope_Public:
		return "Public"
	case TemplateScope_Shared:
		return "Shared"
	case TemplateScope_Official:
		return "Official"
	case TemplateScope_ProjectPublic:
		return "ProjectPublic"
	}
	return "<UNSET>"
}

func TemplateScopeFromString(s string) (TemplateScope, error) {
	switch s {
	case "Private":
		return TemplateScope_Private, nil
	case "Public":
		return TemplateScope_Public, nil
	case "Shared":
		return TemplateScope_Shared, nil
	case "Official":
		return TemplateScope_Official, nil
	case "ProjectPublic":
		return TemplateScope_ProjectPublic, nil
	}
	return TemplateScope(0), fmt.Errorf("not a valid TemplateScope string")
}

func TemplateScopePtr(v TemplateScope) *TemplateScope { return &v }
func (p *TemplateScope) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TemplateScope(result.Int64)
	return
}

func (p *TemplateScope) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TemplateStatus int64

const (
	// 草稿状态，此时对用户不可见
	TemplateStatus_Draft TemplateStatus = 0
	// 生成经验中，此时用户可见，但是不可执行
	TemplateStatus_Generating TemplateStatus = 1
	// 可用状态，此时用户可见，且可执行
	TemplateStatus_Available TemplateStatus = 2
)

func (p TemplateStatus) String() string {
	switch p {
	case TemplateStatus_Draft:
		return "Draft"
	case TemplateStatus_Generating:
		return "Generating"
	case TemplateStatus_Available:
		return "Available"
	}
	return "<UNSET>"
}

func TemplateStatusFromString(s string) (TemplateStatus, error) {
	switch s {
	case "Draft":
		return TemplateStatus_Draft, nil
	case "Generating":
		return TemplateStatus_Generating, nil
	case "Available":
		return TemplateStatus_Available, nil
	}
	return TemplateStatus(0), fmt.Errorf("not a valid TemplateStatus string")
}

func TemplateStatusPtr(v TemplateStatus) *TemplateStatus { return &v }
func (p *TemplateStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TemplateStatus(result.Int64)
	return
}

func (p *TemplateStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TemplatePlanStepStatus int64

const (
	// 未生成执行规划
	TemplatePlanStepStatus_Default TemplatePlanStepStatus = 0
	// 当前模板无需生成执行规划（比如因为用户编辑过模板）
	TemplatePlanStepStatus_None TemplatePlanStepStatus = 1
	// 当前模板正在生成执行规划
	TemplatePlanStepStatus_Generating TemplatePlanStepStatus = 2
	// 生成结束
	TemplatePlanStepStatus_Generated TemplatePlanStepStatus = 3
)

func (p TemplatePlanStepStatus) String() string {
	switch p {
	case TemplatePlanStepStatus_Default:
		return "Default"
	case TemplatePlanStepStatus_None:
		return "None"
	case TemplatePlanStepStatus_Generating:
		return "Generating"
	case TemplatePlanStepStatus_Generated:
		return "Generated"
	}
	return "<UNSET>"
}

func TemplatePlanStepStatusFromString(s string) (TemplatePlanStepStatus, error) {
	switch s {
	case "Default":
		return TemplatePlanStepStatus_Default, nil
	case "None":
		return TemplatePlanStepStatus_None, nil
	case "Generating":
		return TemplatePlanStepStatus_Generating, nil
	case "Generated":
		return TemplatePlanStepStatus_Generated, nil
	}
	return TemplatePlanStepStatus(0), fmt.Errorf("not a valid TemplatePlanStepStatus string")
}

func TemplatePlanStepStatusPtr(v TemplatePlanStepStatus) *TemplatePlanStepStatus { return &v }
func (p *TemplatePlanStepStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TemplatePlanStepStatus(result.Int64)
	return
}

func (p *TemplatePlanStepStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type UserRole = string

type SessionStatus = string

type CanNotResumeReason = string

type SessionSource = string

type TemplateLabel = string

type TemplateCategory = string

type TemplateVariableType = string

type CheckCreateSessionRequest struct {
	// query 通过逗号切分，不支持多个 roles, example 1,2,3
	Roles string `thrift:"Roles,1" json:"roles" query:"roles"`
}

func NewCheckCreateSessionRequest() *CheckCreateSessionRequest {
	return &CheckCreateSessionRequest{}
}

func (p *CheckCreateSessionRequest) InitDefault() {
}

func (p *CheckCreateSessionRequest) GetRoles() (v string) {
	return p.Roles
}

func (p *CheckCreateSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckCreateSessionRequest(%+v)", *p)
}

type SessionRoleAllowed struct {
	Role           SessionRole `thrift:"Role,1,required" json:"role"`
	Allowed        bool        `thrift:"Allowed,2,required" json:"allowed"`
	RemainingTimes *int32      `thrift:"RemainingTimes,3,optional" json:"remaining_times,omitempty"`
}

func NewSessionRoleAllowed() *SessionRoleAllowed {
	return &SessionRoleAllowed{}
}

func (p *SessionRoleAllowed) InitDefault() {
}

func (p *SessionRoleAllowed) GetRole() (v SessionRole) {
	return p.Role
}

func (p *SessionRoleAllowed) GetAllowed() (v bool) {
	return p.Allowed
}

var SessionRoleAllowed_RemainingTimes_DEFAULT int32

func (p *SessionRoleAllowed) GetRemainingTimes() (v int32) {
	if !p.IsSetRemainingTimes() {
		return SessionRoleAllowed_RemainingTimes_DEFAULT
	}
	return *p.RemainingTimes
}

func (p *SessionRoleAllowed) IsSetRemainingTimes() bool {
	return p.RemainingTimes != nil
}

func (p *SessionRoleAllowed) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SessionRoleAllowed(%+v)", *p)
}

type CheckCreateSessionResponse struct {
	Allowed bool                  `thrift:"Allowed,1,required" json:"allowed"`
	Roles   []*SessionRoleAllowed `thrift:"Roles,2,optional" json:"roles"`
}

func NewCheckCreateSessionResponse() *CheckCreateSessionResponse {
	return &CheckCreateSessionResponse{}
}

func (p *CheckCreateSessionResponse) InitDefault() {
}

func (p *CheckCreateSessionResponse) GetAllowed() (v bool) {
	return p.Allowed
}

var CheckCreateSessionResponse_Roles_DEFAULT []*SessionRoleAllowed

func (p *CheckCreateSessionResponse) GetRoles() (v []*SessionRoleAllowed) {
	if !p.IsSetRoles() {
		return CheckCreateSessionResponse_Roles_DEFAULT
	}
	return p.Roles
}

func (p *CheckCreateSessionResponse) IsSetRoles() bool {
	return p.Roles != nil
}

func (p *CheckCreateSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckCreateSessionResponse(%+v)", *p)
}

type SessionRoleConfig struct {
	Role            SessionRole `thrift:"Role,1,required" json:"role"`
	UseInternalTool bool        `thrift:"UseInternalTool,2,required" json:"use_internal_tool"`
	SessionLimit    *int64      `thrift:"SessionLimit,3,optional" json:"session_limit,omitempty"`
	MessageLimit    *int64      `thrift:"MessageLimit,4,optional" json:"message_limit,omitempty"`
	MessageWarning  *int64      `thrift:"MessageWarning,5,optional" json:"message_warning,omitempty"`
}

func NewSessionRoleConfig() *SessionRoleConfig {
	return &SessionRoleConfig{}
}

func (p *SessionRoleConfig) InitDefault() {
}

func (p *SessionRoleConfig) GetRole() (v SessionRole) {
	return p.Role
}

func (p *SessionRoleConfig) GetUseInternalTool() (v bool) {
	return p.UseInternalTool
}

var SessionRoleConfig_SessionLimit_DEFAULT int64

func (p *SessionRoleConfig) GetSessionLimit() (v int64) {
	if !p.IsSetSessionLimit() {
		return SessionRoleConfig_SessionLimit_DEFAULT
	}
	return *p.SessionLimit
}

var SessionRoleConfig_MessageLimit_DEFAULT int64

func (p *SessionRoleConfig) GetMessageLimit() (v int64) {
	if !p.IsSetMessageLimit() {
		return SessionRoleConfig_MessageLimit_DEFAULT
	}
	return *p.MessageLimit
}

var SessionRoleConfig_MessageWarning_DEFAULT int64

func (p *SessionRoleConfig) GetMessageWarning() (v int64) {
	if !p.IsSetMessageWarning() {
		return SessionRoleConfig_MessageWarning_DEFAULT
	}
	return *p.MessageWarning
}

func (p *SessionRoleConfig) IsSetSessionLimit() bool {
	return p.SessionLimit != nil
}

func (p *SessionRoleConfig) IsSetMessageLimit() bool {
	return p.MessageLimit != nil
}

func (p *SessionRoleConfig) IsSetMessageWarning() bool {
	return p.MessageWarning != nil
}

func (p *SessionRoleConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SessionRoleConfig(%+v)", *p)
}

type GetUserRolesRequest struct {
}

func NewGetUserRolesRequest() *GetUserRolesRequest {
	return &GetUserRolesRequest{}
}

func (p *GetUserRolesRequest) InitDefault() {
}

func (p *GetUserRolesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserRolesRequest(%+v)", *p)
}

type GetUserRolesResponse struct {
	Roles []UserRole `thrift:"Roles,1,required" json:"roles"`
}

func NewGetUserRolesResponse() *GetUserRolesResponse {
	return &GetUserRolesResponse{}
}

func (p *GetUserRolesResponse) InitDefault() {
}

func (p *GetUserRolesResponse) GetRoles() (v []UserRole) {
	return p.Roles
}

func (p *GetUserRolesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserRolesResponse(%+v)", *p)
}

type Session struct {
	ID                 string                  `thrift:"ID,1,required" json:"id"`
	Status             SessionStatus           `thrift:"Status,2,required" json:"status"`
	Title              string                  `thrift:"Title,3,required" json:"title"`
	Context            *SessionContext         `thrift:"Context,4,required" json:"context"`
	CreatedAt          string                  `thrift:"CreatedAt,5,required" json:"created_at"`
	UpdatedAt          string                  `thrift:"UpdatedAt,6,required" json:"updated_at"`
	Role               *SessionRole            `thrift:"Role,7,optional" json:"role"`
	Creator            *string                 `thrift:"Creator,8,optional" json:"creator"`
	Metadata           *SessionRuntimeMetadata `thrift:"Metadata,9,optional" json:"metadata"`
	LastMessageAt      string                  `thrift:"LastMessageAt,10" json:"last_message_at"`
	CanResume          *bool                   `thrift:"CanResume,11,optional" json:"can_resume"`
	CanNotResumeReason *CanNotResumeReason     `thrift:"CanNotResumeReason,12,optional" json:"can_not_resume_reason"`
	TemplateID         *string                 `thrift:"TemplateID,13,optional" json:"template_id"`
	SourceSpaceID      *string                 `thrift:"SourceSpaceID,14,optional" json:"source_space_id"`
	Scope              *SessionScope           `thrift:"Scope,15,optional" json:"scope,omitempty"`
	// 权限列表
	PermissionActions []PermissionAction `thrift:"PermissionActions,16,optional" json:"permission_actions,omitempty"`
	// 会话是否被收藏
	Starred *bool `thrift:"Starred,17,optional" json:"starred,omitempty"`
	// 会话的第一个用户提问
	FirstUserQuery *string       `thrift:"FirstUserQuery,18,optional" json:"first_user_query,omitempty"`
	Source         SessionSource `thrift:"Source,19,required" json:"session"`
}

func NewSession() *Session {
	return &Session{}
}

func (p *Session) InitDefault() {
}

func (p *Session) GetID() (v string) {
	return p.ID
}

func (p *Session) GetStatus() (v SessionStatus) {
	return p.Status
}

func (p *Session) GetTitle() (v string) {
	return p.Title
}

var Session_Context_DEFAULT *SessionContext

func (p *Session) GetContext() (v *SessionContext) {
	if !p.IsSetContext() {
		return Session_Context_DEFAULT
	}
	return p.Context
}

func (p *Session) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Session) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var Session_Role_DEFAULT SessionRole

func (p *Session) GetRole() (v SessionRole) {
	if !p.IsSetRole() {
		return Session_Role_DEFAULT
	}
	return *p.Role
}

var Session_Creator_DEFAULT string

func (p *Session) GetCreator() (v string) {
	if !p.IsSetCreator() {
		return Session_Creator_DEFAULT
	}
	return *p.Creator
}

var Session_Metadata_DEFAULT *SessionRuntimeMetadata

func (p *Session) GetMetadata() (v *SessionRuntimeMetadata) {
	if !p.IsSetMetadata() {
		return Session_Metadata_DEFAULT
	}
	return p.Metadata
}

func (p *Session) GetLastMessageAt() (v string) {
	return p.LastMessageAt
}

var Session_CanResume_DEFAULT bool

func (p *Session) GetCanResume() (v bool) {
	if !p.IsSetCanResume() {
		return Session_CanResume_DEFAULT
	}
	return *p.CanResume
}

var Session_CanNotResumeReason_DEFAULT CanNotResumeReason

func (p *Session) GetCanNotResumeReason() (v CanNotResumeReason) {
	if !p.IsSetCanNotResumeReason() {
		return Session_CanNotResumeReason_DEFAULT
	}
	return *p.CanNotResumeReason
}

var Session_TemplateID_DEFAULT string

func (p *Session) GetTemplateID() (v string) {
	if !p.IsSetTemplateID() {
		return Session_TemplateID_DEFAULT
	}
	return *p.TemplateID
}

var Session_SourceSpaceID_DEFAULT string

func (p *Session) GetSourceSpaceID() (v string) {
	if !p.IsSetSourceSpaceID() {
		return Session_SourceSpaceID_DEFAULT
	}
	return *p.SourceSpaceID
}

var Session_Scope_DEFAULT SessionScope

func (p *Session) GetScope() (v SessionScope) {
	if !p.IsSetScope() {
		return Session_Scope_DEFAULT
	}
	return *p.Scope
}

var Session_PermissionActions_DEFAULT []PermissionAction

func (p *Session) GetPermissionActions() (v []PermissionAction) {
	if !p.IsSetPermissionActions() {
		return Session_PermissionActions_DEFAULT
	}
	return p.PermissionActions
}

var Session_Starred_DEFAULT bool

func (p *Session) GetStarred() (v bool) {
	if !p.IsSetStarred() {
		return Session_Starred_DEFAULT
	}
	return *p.Starred
}

var Session_FirstUserQuery_DEFAULT string

func (p *Session) GetFirstUserQuery() (v string) {
	if !p.IsSetFirstUserQuery() {
		return Session_FirstUserQuery_DEFAULT
	}
	return *p.FirstUserQuery
}

func (p *Session) GetSource() (v SessionSource) {
	return p.Source
}

func (p *Session) IsSetContext() bool {
	return p.Context != nil
}

func (p *Session) IsSetRole() bool {
	return p.Role != nil
}

func (p *Session) IsSetCreator() bool {
	return p.Creator != nil
}

func (p *Session) IsSetMetadata() bool {
	return p.Metadata != nil
}

func (p *Session) IsSetCanResume() bool {
	return p.CanResume != nil
}

func (p *Session) IsSetCanNotResumeReason() bool {
	return p.CanNotResumeReason != nil
}

func (p *Session) IsSetTemplateID() bool {
	return p.TemplateID != nil
}

func (p *Session) IsSetSourceSpaceID() bool {
	return p.SourceSpaceID != nil
}

func (p *Session) IsSetScope() bool {
	return p.Scope != nil
}

func (p *Session) IsSetPermissionActions() bool {
	return p.PermissionActions != nil
}

func (p *Session) IsSetStarred() bool {
	return p.Starred != nil
}

func (p *Session) IsSetFirstUserQuery() bool {
	return p.FirstUserQuery != nil
}

func (p *Session) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Session(%+v)", *p)
}

type RuntimeMeta struct {
	Provider       string `thrift:"Provider,1,required" json:"provider"`
	ContainerID    string `thrift:"ContainerID,2,required" json:"container_id"`
	ContainerHost  string `thrift:"ContainerHost,3,required" json:"container_host"`
	WildcardDomain string `thrift:"WildcardDomain,4,required" json:"wildcard_domain"`
}

func NewRuntimeMeta() *RuntimeMeta {
	return &RuntimeMeta{}
}

func (p *RuntimeMeta) InitDefault() {
}

func (p *RuntimeMeta) GetProvider() (v string) {
	return p.Provider
}

func (p *RuntimeMeta) GetContainerID() (v string) {
	return p.ContainerID
}

func (p *RuntimeMeta) GetContainerHost() (v string) {
	return p.ContainerHost
}

func (p *RuntimeMeta) GetWildcardDomain() (v string) {
	return p.WildcardDomain
}

func (p *RuntimeMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuntimeMeta(%+v)", *p)
}

type SessionContext struct {
	UseInternalTool *bool `thrift:"UseInternalTool,1,optional" json:"use_internal_tool"`
	// MCP集
	MCPs []*MCP `thrift:"MCPs,2,optional" json:"mcps"`
}

func NewSessionContext() *SessionContext {
	return &SessionContext{}
}

func (p *SessionContext) InitDefault() {
}

var SessionContext_UseInternalTool_DEFAULT bool

func (p *SessionContext) GetUseInternalTool() (v bool) {
	if !p.IsSetUseInternalTool() {
		return SessionContext_UseInternalTool_DEFAULT
	}
	return *p.UseInternalTool
}

var SessionContext_MCPs_DEFAULT []*MCP

func (p *SessionContext) GetMCPs() (v []*MCP) {
	if !p.IsSetMCPs() {
		return SessionContext_MCPs_DEFAULT
	}
	return p.MCPs
}

func (p *SessionContext) IsSetUseInternalTool() bool {
	return p.UseInternalTool != nil
}

func (p *SessionContext) IsSetMCPs() bool {
	return p.MCPs != nil
}

func (p *SessionContext) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SessionContext(%+v)", *p)
}

type SessionRuntimeMetadata struct {
	LogID                *string `thrift:"LogID,1,optional" json:"log_id,omitempty"`
	AgentConfigVersionID *string `thrift:"AgentConfigVersionID,2,optional" json:"agent_config_version_id,omitempty"`
	AgentConfigID        *string `thrift:"AgentConfigID,3,optional" json:"agent_config_id,omitempty"`
}

func NewSessionRuntimeMetadata() *SessionRuntimeMetadata {
	return &SessionRuntimeMetadata{}
}

func (p *SessionRuntimeMetadata) InitDefault() {
}

var SessionRuntimeMetadata_LogID_DEFAULT string

func (p *SessionRuntimeMetadata) GetLogID() (v string) {
	if !p.IsSetLogID() {
		return SessionRuntimeMetadata_LogID_DEFAULT
	}
	return *p.LogID
}

var SessionRuntimeMetadata_AgentConfigVersionID_DEFAULT string

func (p *SessionRuntimeMetadata) GetAgentConfigVersionID() (v string) {
	if !p.IsSetAgentConfigVersionID() {
		return SessionRuntimeMetadata_AgentConfigVersionID_DEFAULT
	}
	return *p.AgentConfigVersionID
}

var SessionRuntimeMetadata_AgentConfigID_DEFAULT string

func (p *SessionRuntimeMetadata) GetAgentConfigID() (v string) {
	if !p.IsSetAgentConfigID() {
		return SessionRuntimeMetadata_AgentConfigID_DEFAULT
	}
	return *p.AgentConfigID
}

func (p *SessionRuntimeMetadata) IsSetLogID() bool {
	return p.LogID != nil
}

func (p *SessionRuntimeMetadata) IsSetAgentConfigVersionID() bool {
	return p.AgentConfigVersionID != nil
}

func (p *SessionRuntimeMetadata) IsSetAgentConfigID() bool {
	return p.AgentConfigID != nil
}

func (p *SessionRuntimeMetadata) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SessionRuntimeMetadata(%+v)", *p)
}

type CreateSessionRequest struct {
	Role            *SessionRole `thrift:"Role,1,optional" json:"role"`
	UseInternalTool *bool        `thrift:"UseInternalTool,2,optional" json:"use_internal_tool"`
	SpaceID         *string      `thrift:"SpaceID,3,optional" json:"space_id"`
	ExcludedMCPs    []*MCPKey    `thrift:"ExcludedMCPs,4,optional" json:"excluded_mcps"`
}

func NewCreateSessionRequest() *CreateSessionRequest {
	return &CreateSessionRequest{}
}

func (p *CreateSessionRequest) InitDefault() {
}

var CreateSessionRequest_Role_DEFAULT SessionRole

func (p *CreateSessionRequest) GetRole() (v SessionRole) {
	if !p.IsSetRole() {
		return CreateSessionRequest_Role_DEFAULT
	}
	return *p.Role
}

var CreateSessionRequest_UseInternalTool_DEFAULT bool

func (p *CreateSessionRequest) GetUseInternalTool() (v bool) {
	if !p.IsSetUseInternalTool() {
		return CreateSessionRequest_UseInternalTool_DEFAULT
	}
	return *p.UseInternalTool
}

var CreateSessionRequest_SpaceID_DEFAULT string

func (p *CreateSessionRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateSessionRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var CreateSessionRequest_ExcludedMCPs_DEFAULT []*MCPKey

func (p *CreateSessionRequest) GetExcludedMCPs() (v []*MCPKey) {
	if !p.IsSetExcludedMCPs() {
		return CreateSessionRequest_ExcludedMCPs_DEFAULT
	}
	return p.ExcludedMCPs
}

func (p *CreateSessionRequest) IsSetRole() bool {
	return p.Role != nil
}

func (p *CreateSessionRequest) IsSetUseInternalTool() bool {
	return p.UseInternalTool != nil
}

func (p *CreateSessionRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateSessionRequest) IsSetExcludedMCPs() bool {
	return p.ExcludedMCPs != nil
}

func (p *CreateSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSessionRequest(%+v)", *p)
}

type CreateSessionResponse struct {
	Session *Session `thrift:"session,1,required" json:"session"`
}

func NewCreateSessionResponse() *CreateSessionResponse {
	return &CreateSessionResponse{}
}

func (p *CreateSessionResponse) InitDefault() {
}

var CreateSessionResponse_Session_DEFAULT *Session

func (p *CreateSessionResponse) GetSession() (v *Session) {
	if !p.IsSetSession() {
		return CreateSessionResponse_Session_DEFAULT
	}
	return p.Session
}

func (p *CreateSessionResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *CreateSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSessionResponse(%+v)", *p)
}

type ListSessionsRequest struct {
	PageNum  int64   `thrift:"PageNum,1,required" json:"page_num,required" query:"page_num,required"`
	PageSize int64   `thrift:"PageSize,2,required" json:"page_size,required" query:"page_size,required"`
	SpaceID  *string `thrift:"SpaceID,3,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewListSessionsRequest() *ListSessionsRequest {
	return &ListSessionsRequest{}
}

func (p *ListSessionsRequest) InitDefault() {
}

func (p *ListSessionsRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListSessionsRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListSessionsRequest_SpaceID_DEFAULT string

func (p *ListSessionsRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ListSessionsRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ListSessionsRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ListSessionsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionsRequest(%+v)", *p)
}

type ListSessionsResponse struct {
	Sessions []*Session `thrift:"Sessions,1,required" json:"sessions"`
	Total    int64      `thrift:"Total,2,required" json:"total"`
}

func NewListSessionsResponse() *ListSessionsResponse {
	return &ListSessionsResponse{}
}

func (p *ListSessionsResponse) InitDefault() {
}

func (p *ListSessionsResponse) GetSessions() (v []*Session) {
	return p.Sessions
}

func (p *ListSessionsResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListSessionsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionsResponse(%+v)", *p)
}

type ListUserSessionsRequest struct {
	PageNum              int64          `thrift:"PageNum,1,required" json:"page_num,required" query:"page_num,required"`
	PageSize             int64          `thrift:"PageSize,2,required" json:"page_size,required" query:"page_size,required"`
	SessionID            *string        `thrift:"SessionID,3,optional" json:"session_id,omitempty" query:"session_id"`
	AgentConfigVersionID *string        `thrift:"AgentConfigVersionID,4,optional" json:"agent_config_version_id,omitempty" query:"agent_config_version_id"`
	StartTime            *string        `thrift:"StartTime,5,optional" json:"start_time,omitempty" query:"start_time"`
	Endtime              *string        `thrift:"Endtime,6,optional" json:"endtime,omitempty" query:"end_time"`
	Status               *SessionStatus `thrift:"Status,7,optional" json:"status,omitempty" query:"status"`
}

func NewListUserSessionsRequest() *ListUserSessionsRequest {
	return &ListUserSessionsRequest{}
}

func (p *ListUserSessionsRequest) InitDefault() {
}

func (p *ListUserSessionsRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListUserSessionsRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListUserSessionsRequest_SessionID_DEFAULT string

func (p *ListUserSessionsRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ListUserSessionsRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var ListUserSessionsRequest_AgentConfigVersionID_DEFAULT string

func (p *ListUserSessionsRequest) GetAgentConfigVersionID() (v string) {
	if !p.IsSetAgentConfigVersionID() {
		return ListUserSessionsRequest_AgentConfigVersionID_DEFAULT
	}
	return *p.AgentConfigVersionID
}

var ListUserSessionsRequest_StartTime_DEFAULT string

func (p *ListUserSessionsRequest) GetStartTime() (v string) {
	if !p.IsSetStartTime() {
		return ListUserSessionsRequest_StartTime_DEFAULT
	}
	return *p.StartTime
}

var ListUserSessionsRequest_Endtime_DEFAULT string

func (p *ListUserSessionsRequest) GetEndtime() (v string) {
	if !p.IsSetEndtime() {
		return ListUserSessionsRequest_Endtime_DEFAULT
	}
	return *p.Endtime
}

var ListUserSessionsRequest_Status_DEFAULT SessionStatus

func (p *ListUserSessionsRequest) GetStatus() (v SessionStatus) {
	if !p.IsSetStatus() {
		return ListUserSessionsRequest_Status_DEFAULT
	}
	return *p.Status
}

func (p *ListUserSessionsRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ListUserSessionsRequest) IsSetAgentConfigVersionID() bool {
	return p.AgentConfigVersionID != nil
}

func (p *ListUserSessionsRequest) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *ListUserSessionsRequest) IsSetEndtime() bool {
	return p.Endtime != nil
}

func (p *ListUserSessionsRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *ListUserSessionsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUserSessionsRequest(%+v)", *p)
}

type UserSession struct {
	ID                 string                  `thrift:"ID,1,required" json:"id"`
	Status             SessionStatus           `thrift:"Status,2,required" json:"status"`
	CreatedAt          string                  `thrift:"CreatedAt,3,required" json:"created_at"`
	UpdatedAt          string                  `thrift:"UpdatedAt,4,required" json:"updated_at"`
	Role               *SessionRole            `thrift:"Role,5,optional" json:"role"`
	Metadata           *SessionRuntimeMetadata `thrift:"Metadata,6,optional" json:"metadata"`
	LastMessageAt      string                  `thrift:"LastMessageAt,7" json:"last_message_at"`
	CanResume          *bool                   `thrift:"CanResume,8,optional" json:"can_resume"`
	CanNotResumeReason *CanNotResumeReason     `thrift:"CanNotResumeReason,9,optional" json:"can_not_resume_reason"`
	TemplateID         *string                 `thrift:"TemplateID,10,optional" json:"template_id"`
	SourceSpaceID      *string                 `thrift:"SourceSpaceID,11,optional" json:"source_space_id"`
	Scope              *SessionScope           `thrift:"Scope,12,optional" json:"scope,omitempty"`
	// 权限列表
	PermissionActions []PermissionAction `thrift:"PermissionActions,13,optional" json:"permission_actions,omitempty"`
	// 会话是否被收藏
	Starred *bool `thrift:"Starred,14,optional" json:"starred,omitempty"`
}

func NewUserSession() *UserSession {
	return &UserSession{}
}

func (p *UserSession) InitDefault() {
}

func (p *UserSession) GetID() (v string) {
	return p.ID
}

func (p *UserSession) GetStatus() (v SessionStatus) {
	return p.Status
}

func (p *UserSession) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *UserSession) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var UserSession_Role_DEFAULT SessionRole

func (p *UserSession) GetRole() (v SessionRole) {
	if !p.IsSetRole() {
		return UserSession_Role_DEFAULT
	}
	return *p.Role
}

var UserSession_Metadata_DEFAULT *SessionRuntimeMetadata

func (p *UserSession) GetMetadata() (v *SessionRuntimeMetadata) {
	if !p.IsSetMetadata() {
		return UserSession_Metadata_DEFAULT
	}
	return p.Metadata
}

func (p *UserSession) GetLastMessageAt() (v string) {
	return p.LastMessageAt
}

var UserSession_CanResume_DEFAULT bool

func (p *UserSession) GetCanResume() (v bool) {
	if !p.IsSetCanResume() {
		return UserSession_CanResume_DEFAULT
	}
	return *p.CanResume
}

var UserSession_CanNotResumeReason_DEFAULT CanNotResumeReason

func (p *UserSession) GetCanNotResumeReason() (v CanNotResumeReason) {
	if !p.IsSetCanNotResumeReason() {
		return UserSession_CanNotResumeReason_DEFAULT
	}
	return *p.CanNotResumeReason
}

var UserSession_TemplateID_DEFAULT string

func (p *UserSession) GetTemplateID() (v string) {
	if !p.IsSetTemplateID() {
		return UserSession_TemplateID_DEFAULT
	}
	return *p.TemplateID
}

var UserSession_SourceSpaceID_DEFAULT string

func (p *UserSession) GetSourceSpaceID() (v string) {
	if !p.IsSetSourceSpaceID() {
		return UserSession_SourceSpaceID_DEFAULT
	}
	return *p.SourceSpaceID
}

var UserSession_Scope_DEFAULT SessionScope

func (p *UserSession) GetScope() (v SessionScope) {
	if !p.IsSetScope() {
		return UserSession_Scope_DEFAULT
	}
	return *p.Scope
}

var UserSession_PermissionActions_DEFAULT []PermissionAction

func (p *UserSession) GetPermissionActions() (v []PermissionAction) {
	if !p.IsSetPermissionActions() {
		return UserSession_PermissionActions_DEFAULT
	}
	return p.PermissionActions
}

var UserSession_Starred_DEFAULT bool

func (p *UserSession) GetStarred() (v bool) {
	if !p.IsSetStarred() {
		return UserSession_Starred_DEFAULT
	}
	return *p.Starred
}

func (p *UserSession) IsSetRole() bool {
	return p.Role != nil
}

func (p *UserSession) IsSetMetadata() bool {
	return p.Metadata != nil
}

func (p *UserSession) IsSetCanResume() bool {
	return p.CanResume != nil
}

func (p *UserSession) IsSetCanNotResumeReason() bool {
	return p.CanNotResumeReason != nil
}

func (p *UserSession) IsSetTemplateID() bool {
	return p.TemplateID != nil
}

func (p *UserSession) IsSetSourceSpaceID() bool {
	return p.SourceSpaceID != nil
}

func (p *UserSession) IsSetScope() bool {
	return p.Scope != nil
}

func (p *UserSession) IsSetPermissionActions() bool {
	return p.PermissionActions != nil
}

func (p *UserSession) IsSetStarred() bool {
	return p.Starred != nil
}

func (p *UserSession) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserSession(%+v)", *p)
}

type ListUserSessionsResponse struct {
	Sessions []*UserSession `thrift:"Sessions,1,required" json:"sessions"`
	Total    int64          `thrift:"Total,2,required" json:"total"`
}

func NewListUserSessionsResponse() *ListUserSessionsResponse {
	return &ListUserSessionsResponse{}
}

func (p *ListUserSessionsResponse) InitDefault() {
}

func (p *ListUserSessionsResponse) GetSessions() (v []*UserSession) {
	return p.Sessions
}

func (p *ListUserSessionsResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListUserSessionsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUserSessionsResponse(%+v)", *p)
}

type ListSpaceSessionsRequest struct {
	SpaceID *string `thrift:"SpaceID,1,optional" json:"space_id,omitempty" query:"space_id"`
	Limit   int64   `thrift:"Limit,2,required" json:"limit,required" query:"limit,required"`
	// 时间戳起始位置
	NextID *string         `thrift:"NextID,3,optional" json:"next_id,omitempty" query:"next_id"`
	Type   *ListFilterType `thrift:"Type,4,optional" json:"type,omitempty" query:"type"`
	// 搜索关键词
	Search *string `thrift:"Search,5,optional" json:"search,omitempty" query:"search"`
	// 会话列表页签
	Tab *SessionTab `thrift:"Tab,6,optional" json:"tab,omitempty" query:"tab"`
	// 创建者
	Creators []string `thrift:"Creators,7,optional" json:"creators,omitempty" query:"creators"`
	// 创建时间
	CreatedTime *TimeDuration `thrift:"CreatedTime,8,optional" json:"created_time,omitempty" query:"created_time"`
	// 更新时间
	UpdatedTime *TimeDuration `thrift:"UpdatedTime,9,optional" json:"updated_time,omitempty" query:"updated_time"`
	// 状态
	Statuses []SessionStatus `thrift:"Statuses,10,optional" json:"statuses,omitempty" query:"statuses"`
	// 作用域
	Scopes []SessionScope `thrift:"Scopes,11,optional" json:"scopes,omitempty" query:"scopes"`
}

func NewListSpaceSessionsRequest() *ListSpaceSessionsRequest {
	return &ListSpaceSessionsRequest{}
}

func (p *ListSpaceSessionsRequest) InitDefault() {
}

var ListSpaceSessionsRequest_SpaceID_DEFAULT string

func (p *ListSpaceSessionsRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ListSpaceSessionsRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ListSpaceSessionsRequest) GetLimit() (v int64) {
	return p.Limit
}

var ListSpaceSessionsRequest_NextID_DEFAULT string

func (p *ListSpaceSessionsRequest) GetNextID() (v string) {
	if !p.IsSetNextID() {
		return ListSpaceSessionsRequest_NextID_DEFAULT
	}
	return *p.NextID
}

var ListSpaceSessionsRequest_Type_DEFAULT ListFilterType

func (p *ListSpaceSessionsRequest) GetType() (v ListFilterType) {
	if !p.IsSetType() {
		return ListSpaceSessionsRequest_Type_DEFAULT
	}
	return *p.Type
}

var ListSpaceSessionsRequest_Search_DEFAULT string

func (p *ListSpaceSessionsRequest) GetSearch() (v string) {
	if !p.IsSetSearch() {
		return ListSpaceSessionsRequest_Search_DEFAULT
	}
	return *p.Search
}

var ListSpaceSessionsRequest_Tab_DEFAULT SessionTab

func (p *ListSpaceSessionsRequest) GetTab() (v SessionTab) {
	if !p.IsSetTab() {
		return ListSpaceSessionsRequest_Tab_DEFAULT
	}
	return *p.Tab
}

var ListSpaceSessionsRequest_Creators_DEFAULT []string

func (p *ListSpaceSessionsRequest) GetCreators() (v []string) {
	if !p.IsSetCreators() {
		return ListSpaceSessionsRequest_Creators_DEFAULT
	}
	return p.Creators
}

var ListSpaceSessionsRequest_CreatedTime_DEFAULT *TimeDuration

func (p *ListSpaceSessionsRequest) GetCreatedTime() (v *TimeDuration) {
	if !p.IsSetCreatedTime() {
		return ListSpaceSessionsRequest_CreatedTime_DEFAULT
	}
	return p.CreatedTime
}

var ListSpaceSessionsRequest_UpdatedTime_DEFAULT *TimeDuration

func (p *ListSpaceSessionsRequest) GetUpdatedTime() (v *TimeDuration) {
	if !p.IsSetUpdatedTime() {
		return ListSpaceSessionsRequest_UpdatedTime_DEFAULT
	}
	return p.UpdatedTime
}

var ListSpaceSessionsRequest_Statuses_DEFAULT []SessionStatus

func (p *ListSpaceSessionsRequest) GetStatuses() (v []SessionStatus) {
	if !p.IsSetStatuses() {
		return ListSpaceSessionsRequest_Statuses_DEFAULT
	}
	return p.Statuses
}

var ListSpaceSessionsRequest_Scopes_DEFAULT []SessionScope

func (p *ListSpaceSessionsRequest) GetScopes() (v []SessionScope) {
	if !p.IsSetScopes() {
		return ListSpaceSessionsRequest_Scopes_DEFAULT
	}
	return p.Scopes
}

func (p *ListSpaceSessionsRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ListSpaceSessionsRequest) IsSetNextID() bool {
	return p.NextID != nil
}

func (p *ListSpaceSessionsRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *ListSpaceSessionsRequest) IsSetSearch() bool {
	return p.Search != nil
}

func (p *ListSpaceSessionsRequest) IsSetTab() bool {
	return p.Tab != nil
}

func (p *ListSpaceSessionsRequest) IsSetCreators() bool {
	return p.Creators != nil
}

func (p *ListSpaceSessionsRequest) IsSetCreatedTime() bool {
	return p.CreatedTime != nil
}

func (p *ListSpaceSessionsRequest) IsSetUpdatedTime() bool {
	return p.UpdatedTime != nil
}

func (p *ListSpaceSessionsRequest) IsSetStatuses() bool {
	return p.Statuses != nil
}

func (p *ListSpaceSessionsRequest) IsSetScopes() bool {
	return p.Scopes != nil
}

func (p *ListSpaceSessionsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceSessionsRequest(%+v)", *p)
}

type TimeDuration struct {
	// 开始时间，毫秒级时间戳
	StartTime *int64 `thrift:"StartTime,1,optional" json:"start_time,omitempty" query:"start_time"`
	// 结束时间，毫秒级时间戳
	EndTime *int64 `thrift:"EndTime,2,optional" json:"end_time,omitempty" query:"end_time"`
}

func NewTimeDuration() *TimeDuration {
	return &TimeDuration{}
}

func (p *TimeDuration) InitDefault() {
}

var TimeDuration_StartTime_DEFAULT int64

func (p *TimeDuration) GetStartTime() (v int64) {
	if !p.IsSetStartTime() {
		return TimeDuration_StartTime_DEFAULT
	}
	return *p.StartTime
}

var TimeDuration_EndTime_DEFAULT int64

func (p *TimeDuration) GetEndTime() (v int64) {
	if !p.IsSetEndTime() {
		return TimeDuration_EndTime_DEFAULT
	}
	return *p.EndTime
}

func (p *TimeDuration) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *TimeDuration) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *TimeDuration) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TimeDuration(%+v)", *p)
}

type ListSpaceSessionsCount struct {
	SpacePublic int64 `thrift:"SpacePublic,1,required" json:"space_public"`
	SelfCreated int64 `thrift:"SelfCreated,2,required" json:"self_created"`
}

func NewListSpaceSessionsCount() *ListSpaceSessionsCount {
	return &ListSpaceSessionsCount{}
}

func (p *ListSpaceSessionsCount) InitDefault() {
}

func (p *ListSpaceSessionsCount) GetSpacePublic() (v int64) {
	return p.SpacePublic
}

func (p *ListSpaceSessionsCount) GetSelfCreated() (v int64) {
	return p.SelfCreated
}

func (p *ListSpaceSessionsCount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceSessionsCount(%+v)", *p)
}

type ListSpaceSessionsResponse struct {
	Sessions []*Session `thrift:"Sessions,1,required" json:"sessions"`
	// 是否还有更多
	HasMore bool                    `thrift:"HasMore,2,required" json:"has_more"`
	NextID  *string                 `thrift:"NextID,3,optional" json:"next_id,omitempty"`
	Count   *ListSpaceSessionsCount `thrift:"Count,4,required" json:"count"`
}

func NewListSpaceSessionsResponse() *ListSpaceSessionsResponse {
	return &ListSpaceSessionsResponse{}
}

func (p *ListSpaceSessionsResponse) InitDefault() {
}

func (p *ListSpaceSessionsResponse) GetSessions() (v []*Session) {
	return p.Sessions
}

func (p *ListSpaceSessionsResponse) GetHasMore() (v bool) {
	return p.HasMore
}

var ListSpaceSessionsResponse_NextID_DEFAULT string

func (p *ListSpaceSessionsResponse) GetNextID() (v string) {
	if !p.IsSetNextID() {
		return ListSpaceSessionsResponse_NextID_DEFAULT
	}
	return *p.NextID
}

var ListSpaceSessionsResponse_Count_DEFAULT *ListSpaceSessionsCount

func (p *ListSpaceSessionsResponse) GetCount() (v *ListSpaceSessionsCount) {
	if !p.IsSetCount() {
		return ListSpaceSessionsResponse_Count_DEFAULT
	}
	return p.Count
}

func (p *ListSpaceSessionsResponse) IsSetNextID() bool {
	return p.NextID != nil
}

func (p *ListSpaceSessionsResponse) IsSetCount() bool {
	return p.Count != nil
}

func (p *ListSpaceSessionsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceSessionsResponse(%+v)", *p)
}

type ListSessionPartialRequest struct {
}

func NewListSessionPartialRequest() *ListSessionPartialRequest {
	return &ListSessionPartialRequest{}
}

func (p *ListSessionPartialRequest) InitDefault() {
}

func (p *ListSessionPartialRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionPartialRequest(%+v)", *p)
}

type ListSessionPartialResponse struct {
	Sessions []*SessionPartial `thrift:"Sessions,1,required" json:"sessions"`
}

func NewListSessionPartialResponse() *ListSessionPartialResponse {
	return &ListSessionPartialResponse{}
}

func (p *ListSessionPartialResponse) InitDefault() {
}

func (p *ListSessionPartialResponse) GetSessions() (v []*SessionPartial) {
	return p.Sessions
}

func (p *ListSessionPartialResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSessionPartialResponse(%+v)", *p)
}

type SessionPartial struct {
	ID     string        `thrift:"ID,1,required" json:"id"`
	Status SessionStatus `thrift:"Status,2,required" json:"status"`
	Title  string        `thrift:"Title,3,required" json:"title"`
}

func NewSessionPartial() *SessionPartial {
	return &SessionPartial{}
}

func (p *SessionPartial) InitDefault() {
}

func (p *SessionPartial) GetID() (v string) {
	return p.ID
}

func (p *SessionPartial) GetStatus() (v SessionStatus) {
	return p.Status
}

func (p *SessionPartial) GetTitle() (v string) {
	return p.Title
}

func (p *SessionPartial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SessionPartial(%+v)", *p)
}

type GetSessionRequest struct {
	SessionID string  `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
	SpaceID   *string `thrift:"SpaceID,2,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewGetSessionRequest() *GetSessionRequest {
	return &GetSessionRequest{}
}

func (p *GetSessionRequest) InitDefault() {
}

func (p *GetSessionRequest) GetSessionID() (v string) {
	return p.SessionID
}

var GetSessionRequest_SpaceID_DEFAULT string

func (p *GetSessionRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return GetSessionRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *GetSessionRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *GetSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionRequest(%+v)", *p)
}

type GetSessionResponse struct {
	Session  *Session   `thrift:"Session,1,required" json:"session"`
	Messages []*Message `thrift:"Messages,2,optional" json:"messages"`
}

func NewGetSessionResponse() *GetSessionResponse {
	return &GetSessionResponse{}
}

func (p *GetSessionResponse) InitDefault() {
}

var GetSessionResponse_Session_DEFAULT *Session

func (p *GetSessionResponse) GetSession() (v *Session) {
	if !p.IsSetSession() {
		return GetSessionResponse_Session_DEFAULT
	}
	return p.Session
}

var GetSessionResponse_Messages_DEFAULT []*Message

func (p *GetSessionResponse) GetMessages() (v []*Message) {
	if !p.IsSetMessages() {
		return GetSessionResponse_Messages_DEFAULT
	}
	return p.Messages
}

func (p *GetSessionResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *GetSessionResponse) IsSetMessages() bool {
	return p.Messages != nil
}

func (p *GetSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionResponse(%+v)", *p)
}

type UpdateSessionRequest struct {
	SessionID string         `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
	Title     *string        `thrift:"Title,2,optional" json:"title"`
	Status    *SessionStatus `thrift:"Status,3,optional" json:"status"`
	Scope     *SessionScope  `thrift:"Scope,4,optional" json:"scope"`
	SpaceID   *string        `thrift:"SpaceID,5,optional" json:"space_id"`
}

func NewUpdateSessionRequest() *UpdateSessionRequest {
	return &UpdateSessionRequest{}
}

func (p *UpdateSessionRequest) InitDefault() {
}

func (p *UpdateSessionRequest) GetSessionID() (v string) {
	return p.SessionID
}

var UpdateSessionRequest_Title_DEFAULT string

func (p *UpdateSessionRequest) GetTitle() (v string) {
	if !p.IsSetTitle() {
		return UpdateSessionRequest_Title_DEFAULT
	}
	return *p.Title
}

var UpdateSessionRequest_Status_DEFAULT SessionStatus

func (p *UpdateSessionRequest) GetStatus() (v SessionStatus) {
	if !p.IsSetStatus() {
		return UpdateSessionRequest_Status_DEFAULT
	}
	return *p.Status
}

var UpdateSessionRequest_Scope_DEFAULT SessionScope

func (p *UpdateSessionRequest) GetScope() (v SessionScope) {
	if !p.IsSetScope() {
		return UpdateSessionRequest_Scope_DEFAULT
	}
	return *p.Scope
}

var UpdateSessionRequest_SpaceID_DEFAULT string

func (p *UpdateSessionRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return UpdateSessionRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *UpdateSessionRequest) IsSetTitle() bool {
	return p.Title != nil
}

func (p *UpdateSessionRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *UpdateSessionRequest) IsSetScope() bool {
	return p.Scope != nil
}

func (p *UpdateSessionRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *UpdateSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateSessionRequest(%+v)", *p)
}

type UpdateSessionResponse struct {
	Session *Session `thrift:"Session,1,required" json:"session"`
}

func NewUpdateSessionResponse() *UpdateSessionResponse {
	return &UpdateSessionResponse{}
}

func (p *UpdateSessionResponse) InitDefault() {
}

var UpdateSessionResponse_Session_DEFAULT *Session

func (p *UpdateSessionResponse) GetSession() (v *Session) {
	if !p.IsSetSession() {
		return UpdateSessionResponse_Session_DEFAULT
	}
	return p.Session
}

func (p *UpdateSessionResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *UpdateSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateSessionResponse(%+v)", *p)
}

type DeleteSessionRequest struct {
	SessionID string  `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
	SpaceID   *string `thrift:"SpaceID,2,optional" json:"space_id"`
}

func NewDeleteSessionRequest() *DeleteSessionRequest {
	return &DeleteSessionRequest{}
}

func (p *DeleteSessionRequest) InitDefault() {
}

func (p *DeleteSessionRequest) GetSessionID() (v string) {
	return p.SessionID
}

var DeleteSessionRequest_SpaceID_DEFAULT string

func (p *DeleteSessionRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return DeleteSessionRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *DeleteSessionRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *DeleteSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSessionRequest(%+v)", *p)
}

type DeleteSessionResponse struct {
	Message string `thrift:"Message,1,required" json:"message"`
}

func NewDeleteSessionResponse() *DeleteSessionResponse {
	return &DeleteSessionResponse{}
}

func (p *DeleteSessionResponse) InitDefault() {
}

func (p *DeleteSessionResponse) GetMessage() (v string) {
	return p.Message
}

func (p *DeleteSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSessionResponse(%+v)", *p)
}

type BatchDeleteSessionRequest struct {
	SessionIDs []string `thrift:"SessionIDs,1,required" json:"session_ids" form:"session_ids,required" `
}

func NewBatchDeleteSessionRequest() *BatchDeleteSessionRequest {
	return &BatchDeleteSessionRequest{}
}

func (p *BatchDeleteSessionRequest) InitDefault() {
}

func (p *BatchDeleteSessionRequest) GetSessionIDs() (v []string) {
	return p.SessionIDs
}

func (p *BatchDeleteSessionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchDeleteSessionRequest(%+v)", *p)
}

type BatchDeleteSessionResponse struct {
}

func NewBatchDeleteSessionResponse() *BatchDeleteSessionResponse {
	return &BatchDeleteSessionResponse{}
}

func (p *BatchDeleteSessionResponse) InitDefault() {
}

func (p *BatchDeleteSessionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchDeleteSessionResponse(%+v)", *p)
}

type GetSessionStreamEventsRequest struct {
	SessionID   string  `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
	EventOffset *int64  `thrift:"EventOffset,2,optional" json:"event_offset,omitempty" query:"event_offset"`
	SpaceID     *string `thrift:"SpaceID,3,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewGetSessionStreamEventsRequest() *GetSessionStreamEventsRequest {
	return &GetSessionStreamEventsRequest{}
}

func (p *GetSessionStreamEventsRequest) InitDefault() {
}

func (p *GetSessionStreamEventsRequest) GetSessionID() (v string) {
	return p.SessionID
}

var GetSessionStreamEventsRequest_EventOffset_DEFAULT int64

func (p *GetSessionStreamEventsRequest) GetEventOffset() (v int64) {
	if !p.IsSetEventOffset() {
		return GetSessionStreamEventsRequest_EventOffset_DEFAULT
	}
	return *p.EventOffset
}

var GetSessionStreamEventsRequest_SpaceID_DEFAULT string

func (p *GetSessionStreamEventsRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return GetSessionStreamEventsRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *GetSessionStreamEventsRequest) IsSetEventOffset() bool {
	return p.EventOffset != nil
}

func (p *GetSessionStreamEventsRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *GetSessionStreamEventsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionStreamEventsRequest(%+v)", *p)
}

type GetOldSessionEventsRequest struct {
	SessionID string  `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
	SpaceID   *string `thrift:"SpaceID,2,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewGetOldSessionEventsRequest() *GetOldSessionEventsRequest {
	return &GetOldSessionEventsRequest{}
}

func (p *GetOldSessionEventsRequest) InitDefault() {
}

func (p *GetOldSessionEventsRequest) GetSessionID() (v string) {
	return p.SessionID
}

var GetOldSessionEventsRequest_SpaceID_DEFAULT string

func (p *GetOldSessionEventsRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return GetOldSessionEventsRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *GetOldSessionEventsRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *GetOldSessionEventsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOldSessionEventsRequest(%+v)", *p)
}

type GetOldSessionEventsResponse struct {
	Events  []*Event     `thrift:"Events,1,required" json:"events"`
	Role    *SessionRole `thrift:"Role,2,optional" json:"role"`
	Session *Session     `thrift:"Session,3,optional" json:"session"`
}

func NewGetOldSessionEventsResponse() *GetOldSessionEventsResponse {
	return &GetOldSessionEventsResponse{}
}

func (p *GetOldSessionEventsResponse) InitDefault() {
}

func (p *GetOldSessionEventsResponse) GetEvents() (v []*Event) {
	return p.Events
}

var GetOldSessionEventsResponse_Role_DEFAULT SessionRole

func (p *GetOldSessionEventsResponse) GetRole() (v SessionRole) {
	if !p.IsSetRole() {
		return GetOldSessionEventsResponse_Role_DEFAULT
	}
	return *p.Role
}

var GetOldSessionEventsResponse_Session_DEFAULT *Session

func (p *GetOldSessionEventsResponse) GetSession() (v *Session) {
	if !p.IsSetSession() {
		return GetOldSessionEventsResponse_Session_DEFAULT
	}
	return p.Session
}

func (p *GetOldSessionEventsResponse) IsSetRole() bool {
	return p.Role != nil
}

func (p *GetOldSessionEventsResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *GetOldSessionEventsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOldSessionEventsResponse(%+v)", *p)
}

type CreateMessageRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
	// Content 用户发送的消息内容
	Content     string                `thrift:"Content,2,required" json:"content"`
	Attachments []*AttachmentRequired `thrift:"attachments,3,optional" json:"attachments"`
	// ToolCalls Agent 发送的工具调用，用户需要回复Agent时填写，例如二次确认、授权等
	// Content和ToolCalls互斥，ToolCalls仅在有 tool_call_required event 时使用
	ToolCalls []*ToolCall `thrift:"ToolCalls,4,optional" json:"tool_calls"`
	// 指定创建消息的事件的 offset，通常用于打断或者澄清的场景
	EventOffset int64      `thrift:"EventOffset,5" json:"event_offset"`
	Options     *string    `thrift:"Options,6,optional" json:"options"`
	Mentions    []*Mention `thrift:"Mentions,7,optional" json:"mentions"`
	SpaceID     *string    `thrift:"SpaceID,8,optional" json:"space_id"`
}

func NewCreateMessageRequest() *CreateMessageRequest {
	return &CreateMessageRequest{}
}

func (p *CreateMessageRequest) InitDefault() {
}

func (p *CreateMessageRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *CreateMessageRequest) GetContent() (v string) {
	return p.Content
}

var CreateMessageRequest_Attachments_DEFAULT []*AttachmentRequired

func (p *CreateMessageRequest) GetAttachments() (v []*AttachmentRequired) {
	if !p.IsSetAttachments() {
		return CreateMessageRequest_Attachments_DEFAULT
	}
	return p.Attachments
}

var CreateMessageRequest_ToolCalls_DEFAULT []*ToolCall

func (p *CreateMessageRequest) GetToolCalls() (v []*ToolCall) {
	if !p.IsSetToolCalls() {
		return CreateMessageRequest_ToolCalls_DEFAULT
	}
	return p.ToolCalls
}

func (p *CreateMessageRequest) GetEventOffset() (v int64) {
	return p.EventOffset
}

var CreateMessageRequest_Options_DEFAULT string

func (p *CreateMessageRequest) GetOptions() (v string) {
	if !p.IsSetOptions() {
		return CreateMessageRequest_Options_DEFAULT
	}
	return *p.Options
}

var CreateMessageRequest_Mentions_DEFAULT []*Mention

func (p *CreateMessageRequest) GetMentions() (v []*Mention) {
	if !p.IsSetMentions() {
		return CreateMessageRequest_Mentions_DEFAULT
	}
	return p.Mentions
}

var CreateMessageRequest_SpaceID_DEFAULT string

func (p *CreateMessageRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateMessageRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *CreateMessageRequest) IsSetAttachments() bool {
	return p.Attachments != nil
}

func (p *CreateMessageRequest) IsSetToolCalls() bool {
	return p.ToolCalls != nil
}

func (p *CreateMessageRequest) IsSetOptions() bool {
	return p.Options != nil
}

func (p *CreateMessageRequest) IsSetMentions() bool {
	return p.Mentions != nil
}

func (p *CreateMessageRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateMessageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateMessageRequest(%+v)", *p)
}

type AttachmentRequired struct {
	ID       string `thrift:"ID,1,required" json:"id"`
	FileName string `thrift:"FileName,2,required" json:"file_name"`
}

func NewAttachmentRequired() *AttachmentRequired {
	return &AttachmentRequired{}
}

func (p *AttachmentRequired) InitDefault() {
}

func (p *AttachmentRequired) GetID() (v string) {
	return p.ID
}

func (p *AttachmentRequired) GetFileName() (v string) {
	return p.FileName
}

func (p *AttachmentRequired) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AttachmentRequired(%+v)", *p)
}

// ToolCall 是用户对工具调用的回复，例如二次确认、授权等
type ToolCall struct {
	ToolCallID string `thrift:"ToolCallID,1,required" json:"id"`
	Name       string `thrift:"Name,2,required" json:"name"`
	// 用户回复的内容
	Content string `thrift:"Content,3,required" json:"content"`
	// 用户的操作
	Action ToolCallAction `thrift:"Action,4,required" json:"action"`
	// 是否需要保持登录状态
	NeedKeepLogin *bool `thrift:"NeedKeepLogin,5,optional" json:"need_keep_login"`
	// 填写后的表单数据
	FormData *string `thrift:"FormData,6,optional" json:"form_data"`
}

func NewToolCall() *ToolCall {
	return &ToolCall{}
}

func (p *ToolCall) InitDefault() {
}

func (p *ToolCall) GetToolCallID() (v string) {
	return p.ToolCallID
}

func (p *ToolCall) GetName() (v string) {
	return p.Name
}

func (p *ToolCall) GetContent() (v string) {
	return p.Content
}

func (p *ToolCall) GetAction() (v ToolCallAction) {
	return p.Action
}

var ToolCall_NeedKeepLogin_DEFAULT bool

func (p *ToolCall) GetNeedKeepLogin() (v bool) {
	if !p.IsSetNeedKeepLogin() {
		return ToolCall_NeedKeepLogin_DEFAULT
	}
	return *p.NeedKeepLogin
}

var ToolCall_FormData_DEFAULT string

func (p *ToolCall) GetFormData() (v string) {
	if !p.IsSetFormData() {
		return ToolCall_FormData_DEFAULT
	}
	return *p.FormData
}

func (p *ToolCall) IsSetNeedKeepLogin() bool {
	return p.NeedKeepLogin != nil
}

func (p *ToolCall) IsSetFormData() bool {
	return p.FormData != nil
}

func (p *ToolCall) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ToolCall(%+v)", *p)
}

type CreateMessageResponse struct {
	Message *Message `thrift:"Message,1,required" json:"message"`
}

func NewCreateMessageResponse() *CreateMessageResponse {
	return &CreateMessageResponse{}
}

func (p *CreateMessageResponse) InitDefault() {
}

var CreateMessageResponse_Message_DEFAULT *Message

func (p *CreateMessageResponse) GetMessage() (v *Message) {
	if !p.IsSetMessage() {
		return CreateMessageResponse_Message_DEFAULT
	}
	return p.Message
}

func (p *CreateMessageResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *CreateMessageResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateMessageResponse(%+v)", *p)
}

type CreateMessageWithTemplateRequest struct {
	Role            *SessionRole       `thrift:"Role,1,optional" json:"role"`
	UseInternalTool *bool              `thrift:"UseInternalTool,2,optional" json:"use_internal_tool"`
	TemplateID      string             `thrift:"TemplateID,3,required" json:"template_id"`
	Content         string             `thrift:"Content,4,required" json:"content"`
	FormValue       *TemplateFormValue `thrift:"FormValue,5,optional" json:"form_value"`
	Options         *string            `thrift:"Options,6,optional" json:"options"`
	Mcps            []*MCPKey          `thrift:"mcps,7,optional" json:"mcps"`
	SpaceID         *string            `thrift:"SpaceID,8,optional" json:"space_id"`
	ExcludedMCPs    []*MCPKey          `thrift:"ExcludedMCPs,9,optional" json:"excluded_mcps"`
	FromApp         *string            `thrift:"FromApp,10,optional" json:"from_app"`
}

func NewCreateMessageWithTemplateRequest() *CreateMessageWithTemplateRequest {
	return &CreateMessageWithTemplateRequest{}
}

func (p *CreateMessageWithTemplateRequest) InitDefault() {
}

var CreateMessageWithTemplateRequest_Role_DEFAULT SessionRole

func (p *CreateMessageWithTemplateRequest) GetRole() (v SessionRole) {
	if !p.IsSetRole() {
		return CreateMessageWithTemplateRequest_Role_DEFAULT
	}
	return *p.Role
}

var CreateMessageWithTemplateRequest_UseInternalTool_DEFAULT bool

func (p *CreateMessageWithTemplateRequest) GetUseInternalTool() (v bool) {
	if !p.IsSetUseInternalTool() {
		return CreateMessageWithTemplateRequest_UseInternalTool_DEFAULT
	}
	return *p.UseInternalTool
}

func (p *CreateMessageWithTemplateRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

func (p *CreateMessageWithTemplateRequest) GetContent() (v string) {
	return p.Content
}

var CreateMessageWithTemplateRequest_FormValue_DEFAULT *TemplateFormValue

func (p *CreateMessageWithTemplateRequest) GetFormValue() (v *TemplateFormValue) {
	if !p.IsSetFormValue() {
		return CreateMessageWithTemplateRequest_FormValue_DEFAULT
	}
	return p.FormValue
}

var CreateMessageWithTemplateRequest_Options_DEFAULT string

func (p *CreateMessageWithTemplateRequest) GetOptions() (v string) {
	if !p.IsSetOptions() {
		return CreateMessageWithTemplateRequest_Options_DEFAULT
	}
	return *p.Options
}

var CreateMessageWithTemplateRequest_Mcps_DEFAULT []*MCPKey

func (p *CreateMessageWithTemplateRequest) GetMcps() (v []*MCPKey) {
	if !p.IsSetMcps() {
		return CreateMessageWithTemplateRequest_Mcps_DEFAULT
	}
	return p.Mcps
}

var CreateMessageWithTemplateRequest_SpaceID_DEFAULT string

func (p *CreateMessageWithTemplateRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateMessageWithTemplateRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var CreateMessageWithTemplateRequest_ExcludedMCPs_DEFAULT []*MCPKey

func (p *CreateMessageWithTemplateRequest) GetExcludedMCPs() (v []*MCPKey) {
	if !p.IsSetExcludedMCPs() {
		return CreateMessageWithTemplateRequest_ExcludedMCPs_DEFAULT
	}
	return p.ExcludedMCPs
}

var CreateMessageWithTemplateRequest_FromApp_DEFAULT string

func (p *CreateMessageWithTemplateRequest) GetFromApp() (v string) {
	if !p.IsSetFromApp() {
		return CreateMessageWithTemplateRequest_FromApp_DEFAULT
	}
	return *p.FromApp
}

func (p *CreateMessageWithTemplateRequest) IsSetRole() bool {
	return p.Role != nil
}

func (p *CreateMessageWithTemplateRequest) IsSetUseInternalTool() bool {
	return p.UseInternalTool != nil
}

func (p *CreateMessageWithTemplateRequest) IsSetFormValue() bool {
	return p.FormValue != nil
}

func (p *CreateMessageWithTemplateRequest) IsSetOptions() bool {
	return p.Options != nil
}

func (p *CreateMessageWithTemplateRequest) IsSetMcps() bool {
	return p.Mcps != nil
}

func (p *CreateMessageWithTemplateRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateMessageWithTemplateRequest) IsSetExcludedMCPs() bool {
	return p.ExcludedMCPs != nil
}

func (p *CreateMessageWithTemplateRequest) IsSetFromApp() bool {
	return p.FromApp != nil
}

func (p *CreateMessageWithTemplateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateMessageWithTemplateRequest(%+v)", *p)
}

type CreateMessageWithTemplateResponse struct {
	Message *Message `thrift:"Message,1,required" json:"message"`
	Session *Session `thrift:"Session,2,required" json:"session"`
}

func NewCreateMessageWithTemplateResponse() *CreateMessageWithTemplateResponse {
	return &CreateMessageWithTemplateResponse{}
}

func (p *CreateMessageWithTemplateResponse) InitDefault() {
}

var CreateMessageWithTemplateResponse_Message_DEFAULT *Message

func (p *CreateMessageWithTemplateResponse) GetMessage() (v *Message) {
	if !p.IsSetMessage() {
		return CreateMessageWithTemplateResponse_Message_DEFAULT
	}
	return p.Message
}

var CreateMessageWithTemplateResponse_Session_DEFAULT *Session

func (p *CreateMessageWithTemplateResponse) GetSession() (v *Session) {
	if !p.IsSetSession() {
		return CreateMessageWithTemplateResponse_Session_DEFAULT
	}
	return p.Session
}

func (p *CreateMessageWithTemplateResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *CreateMessageWithTemplateResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *CreateMessageWithTemplateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateMessageWithTemplateResponse(%+v)", *p)
}

type ListTemplatesRequest struct {
	PageNum  *int64            `thrift:"PageNum,1,optional" json:"page_num,omitempty" query:"page_num"`
	PageSize *int64            `thrift:"PageSize,2,optional" json:"page_size,omitempty" query:"page_size"`
	Category *TemplateCategory `thrift:"Category,3,optional" json:"category,omitempty" query:"category"`
	Search   *string           `thrift:"Search,4,optional" json:"search,omitempty" query:"search"`
	Source   *TemplateSource   `thrift:"Source,5,optional" json:"source,omitempty" query:"source"`
	Label    *TemplateLabel    `thrift:"Label,6,optional" json:"label,omitempty" query:"label"`
}

func NewListTemplatesRequest() *ListTemplatesRequest {
	return &ListTemplatesRequest{}
}

func (p *ListTemplatesRequest) InitDefault() {
}

var ListTemplatesRequest_PageNum_DEFAULT int64

func (p *ListTemplatesRequest) GetPageNum() (v int64) {
	if !p.IsSetPageNum() {
		return ListTemplatesRequest_PageNum_DEFAULT
	}
	return *p.PageNum
}

var ListTemplatesRequest_PageSize_DEFAULT int64

func (p *ListTemplatesRequest) GetPageSize() (v int64) {
	if !p.IsSetPageSize() {
		return ListTemplatesRequest_PageSize_DEFAULT
	}
	return *p.PageSize
}

var ListTemplatesRequest_Category_DEFAULT TemplateCategory

func (p *ListTemplatesRequest) GetCategory() (v TemplateCategory) {
	if !p.IsSetCategory() {
		return ListTemplatesRequest_Category_DEFAULT
	}
	return *p.Category
}

var ListTemplatesRequest_Search_DEFAULT string

func (p *ListTemplatesRequest) GetSearch() (v string) {
	if !p.IsSetSearch() {
		return ListTemplatesRequest_Search_DEFAULT
	}
	return *p.Search
}

var ListTemplatesRequest_Source_DEFAULT TemplateSource

func (p *ListTemplatesRequest) GetSource() (v TemplateSource) {
	if !p.IsSetSource() {
		return ListTemplatesRequest_Source_DEFAULT
	}
	return *p.Source
}

var ListTemplatesRequest_Label_DEFAULT TemplateLabel

func (p *ListTemplatesRequest) GetLabel() (v TemplateLabel) {
	if !p.IsSetLabel() {
		return ListTemplatesRequest_Label_DEFAULT
	}
	return *p.Label
}

func (p *ListTemplatesRequest) IsSetPageNum() bool {
	return p.PageNum != nil
}

func (p *ListTemplatesRequest) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *ListTemplatesRequest) IsSetCategory() bool {
	return p.Category != nil
}

func (p *ListTemplatesRequest) IsSetSearch() bool {
	return p.Search != nil
}

func (p *ListTemplatesRequest) IsSetSource() bool {
	return p.Source != nil
}

func (p *ListTemplatesRequest) IsSetLabel() bool {
	return p.Label != nil
}

func (p *ListTemplatesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTemplatesRequest(%+v)", *p)
}

type ListTemplatesResponse struct {
	Templates []*Template `thrift:"Templates,1,required" json:"templates"`
	Total     int64       `thrift:"Total,2,required" json:"total"`
}

func NewListTemplatesResponse() *ListTemplatesResponse {
	return &ListTemplatesResponse{}
}

func (p *ListTemplatesResponse) InitDefault() {
}

func (p *ListTemplatesResponse) GetTemplates() (v []*Template) {
	return p.Templates
}

func (p *ListTemplatesResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListTemplatesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTemplatesResponse(%+v)", *p)
}

type Template struct {
	ID               string              `thrift:"ID,1,required" json:"id"`
	Category         TemplateCategory    `thrift:"Category,2,required" json:"category"`
	Name             string              `thrift:"Name,3,required" json:"name"`
	Prompt           string              `thrift:"Prompt,4,required" json:"prompt"`
	Variables        []*TemplateVariable `thrift:"Variables,5,required" json:"variables"`
	Showcase         *Showcase           `thrift:"Showcase,6,optional" json:"showcase,omitempty"`
	EstimatedMinutes *EstimatedMinutes   `thrift:"EstimatedMinutes,8,optional" json:"estimated_minutes,omitempty"`
	// 快捷模板支持的角色列表
	SupportRoles []SessionRole `thrift:"SupportRoles,9,optional" json:"support_roles,omitempty"`
	// 快捷模板的作用域，public、private、shared
	Scope TemplateScope `thrift:"Scope,10,required" json:"scope"`
	// 快捷模板的创建者
	Creator string `thrift:"Creator,11,required" json:"creator"`
	// 快捷模板的收藏数
	StarCount *int64 `thrift:"StarCount,12,optional" json:"star_count,omitempty"`
	// 快捷模板支持的MCP列表
	SupportMCPs []*MCP `thrift:"SupportMCPs,13,optional" json:"support_mcps,omitempty"`
	// 快捷模板的状态
	Status TemplateStatus `thrift:"Status,14,required" json:"status"`
	// 快捷模板的版本号
	Version string `thrift:"Version,15,required" json:"version"`
	// 快捷模板的步骤
	Steps []string `thrift:"Steps,16,optional" json:"steps,omitempty"`
	// 快捷模板的执行规划状态
	PlanStepStatus TemplatePlanStepStatus `thrift:"PlanStepStatus,17,required" json:"plan_step_status"`
	// 快捷模板的标签
	Label TemplateLabel `thrift:"Label,18,required" json:"label"`
	// 快捷模板是否已收藏
	Starred *bool `thrift:"Starred,19,optional" json:"starred,omitempty"`
	// 快捷模板的分享ID
	ShareID *string `thrift:"ShareID,20,optional" json:"share_id,omitempty"`
}

func NewTemplate() *Template {
	return &Template{}
}

func (p *Template) InitDefault() {
}

func (p *Template) GetID() (v string) {
	return p.ID
}

func (p *Template) GetCategory() (v TemplateCategory) {
	return p.Category
}

func (p *Template) GetName() (v string) {
	return p.Name
}

func (p *Template) GetPrompt() (v string) {
	return p.Prompt
}

func (p *Template) GetVariables() (v []*TemplateVariable) {
	return p.Variables
}

var Template_Showcase_DEFAULT *Showcase

func (p *Template) GetShowcase() (v *Showcase) {
	if !p.IsSetShowcase() {
		return Template_Showcase_DEFAULT
	}
	return p.Showcase
}

var Template_EstimatedMinutes_DEFAULT *EstimatedMinutes

func (p *Template) GetEstimatedMinutes() (v *EstimatedMinutes) {
	if !p.IsSetEstimatedMinutes() {
		return Template_EstimatedMinutes_DEFAULT
	}
	return p.EstimatedMinutes
}

var Template_SupportRoles_DEFAULT []SessionRole

func (p *Template) GetSupportRoles() (v []SessionRole) {
	if !p.IsSetSupportRoles() {
		return Template_SupportRoles_DEFAULT
	}
	return p.SupportRoles
}

func (p *Template) GetScope() (v TemplateScope) {
	return p.Scope
}

func (p *Template) GetCreator() (v string) {
	return p.Creator
}

var Template_StarCount_DEFAULT int64

func (p *Template) GetStarCount() (v int64) {
	if !p.IsSetStarCount() {
		return Template_StarCount_DEFAULT
	}
	return *p.StarCount
}

var Template_SupportMCPs_DEFAULT []*MCP

func (p *Template) GetSupportMCPs() (v []*MCP) {
	if !p.IsSetSupportMCPs() {
		return Template_SupportMCPs_DEFAULT
	}
	return p.SupportMCPs
}

func (p *Template) GetStatus() (v TemplateStatus) {
	return p.Status
}

func (p *Template) GetVersion() (v string) {
	return p.Version
}

var Template_Steps_DEFAULT []string

func (p *Template) GetSteps() (v []string) {
	if !p.IsSetSteps() {
		return Template_Steps_DEFAULT
	}
	return p.Steps
}

func (p *Template) GetPlanStepStatus() (v TemplatePlanStepStatus) {
	return p.PlanStepStatus
}

func (p *Template) GetLabel() (v TemplateLabel) {
	return p.Label
}

var Template_Starred_DEFAULT bool

func (p *Template) GetStarred() (v bool) {
	if !p.IsSetStarred() {
		return Template_Starred_DEFAULT
	}
	return *p.Starred
}

var Template_ShareID_DEFAULT string

func (p *Template) GetShareID() (v string) {
	if !p.IsSetShareID() {
		return Template_ShareID_DEFAULT
	}
	return *p.ShareID
}

func (p *Template) IsSetShowcase() bool {
	return p.Showcase != nil
}

func (p *Template) IsSetEstimatedMinutes() bool {
	return p.EstimatedMinutes != nil
}

func (p *Template) IsSetSupportRoles() bool {
	return p.SupportRoles != nil
}

func (p *Template) IsSetStarCount() bool {
	return p.StarCount != nil
}

func (p *Template) IsSetSupportMCPs() bool {
	return p.SupportMCPs != nil
}

func (p *Template) IsSetSteps() bool {
	return p.Steps != nil
}

func (p *Template) IsSetStarred() bool {
	return p.Starred != nil
}

func (p *Template) IsSetShareID() bool {
	return p.ShareID != nil
}

func (p *Template) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Template(%+v)", *p)
}

// ModifyTemplate 中可选字段根据用户的编辑操作来传，如果用户编辑了则传该字段，否则不传
type ModifyTemplate struct {
	Category  TemplateCategory    `thrift:"Category,1,required" json:"category"`
	Name      *string             `thrift:"Name,2,optional" json:"name"`
	Prompt    *string             `thrift:"Prompt,3,optional" json:"prompt"`
	Variables []*TemplateVariable `thrift:"Variables,4,optional" json:"variables"`
	// 快捷模板的作用域，public、private
	Scope TemplateScope `thrift:"Scope,5,required" json:"scope"`
	// 快捷模板的标签
	Label TemplateLabel `thrift:"Label,6,required" json:"label"`
}

func NewModifyTemplate() *ModifyTemplate {
	return &ModifyTemplate{}
}

func (p *ModifyTemplate) InitDefault() {
}

func (p *ModifyTemplate) GetCategory() (v TemplateCategory) {
	return p.Category
}

var ModifyTemplate_Name_DEFAULT string

func (p *ModifyTemplate) GetName() (v string) {
	if !p.IsSetName() {
		return ModifyTemplate_Name_DEFAULT
	}
	return *p.Name
}

var ModifyTemplate_Prompt_DEFAULT string

func (p *ModifyTemplate) GetPrompt() (v string) {
	if !p.IsSetPrompt() {
		return ModifyTemplate_Prompt_DEFAULT
	}
	return *p.Prompt
}

var ModifyTemplate_Variables_DEFAULT []*TemplateVariable

func (p *ModifyTemplate) GetVariables() (v []*TemplateVariable) {
	if !p.IsSetVariables() {
		return ModifyTemplate_Variables_DEFAULT
	}
	return p.Variables
}

func (p *ModifyTemplate) GetScope() (v TemplateScope) {
	return p.Scope
}

func (p *ModifyTemplate) GetLabel() (v TemplateLabel) {
	return p.Label
}

func (p *ModifyTemplate) IsSetName() bool {
	return p.Name != nil
}

func (p *ModifyTemplate) IsSetPrompt() bool {
	return p.Prompt != nil
}

func (p *ModifyTemplate) IsSetVariables() bool {
	return p.Variables != nil
}

func (p *ModifyTemplate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyTemplate(%+v)", *p)
}

type TemplateVariable struct {
	Name          string               `thrift:"Name,1,required" json:"name"`
	Required      *bool                `thrift:"Required,2,optional" json:"required"`
	Description   *string              `thrift:"Description,3,optional" json:"description"`
	Type          TemplateVariableType `thrift:"Type,4,required" json:"type"`
	Placeholder   *string              `thrift:"Placeholder,5,optional" json:"placeholder"`
	DefaultValue  *string              `thrift:"DefaultValue,6,optional" json:"default_value"`
	SelectContent *string              `thrift:"SelectContent,7,optional" json:"select_content"`
	ID            string               `thrift:"ID,8,required" json:"id"`
}

func NewTemplateVariable() *TemplateVariable {
	return &TemplateVariable{}
}

func (p *TemplateVariable) InitDefault() {
}

func (p *TemplateVariable) GetName() (v string) {
	return p.Name
}

var TemplateVariable_Required_DEFAULT bool

func (p *TemplateVariable) GetRequired() (v bool) {
	if !p.IsSetRequired() {
		return TemplateVariable_Required_DEFAULT
	}
	return *p.Required
}

var TemplateVariable_Description_DEFAULT string

func (p *TemplateVariable) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return TemplateVariable_Description_DEFAULT
	}
	return *p.Description
}

func (p *TemplateVariable) GetType() (v TemplateVariableType) {
	return p.Type
}

var TemplateVariable_Placeholder_DEFAULT string

func (p *TemplateVariable) GetPlaceholder() (v string) {
	if !p.IsSetPlaceholder() {
		return TemplateVariable_Placeholder_DEFAULT
	}
	return *p.Placeholder
}

var TemplateVariable_DefaultValue_DEFAULT string

func (p *TemplateVariable) GetDefaultValue() (v string) {
	if !p.IsSetDefaultValue() {
		return TemplateVariable_DefaultValue_DEFAULT
	}
	return *p.DefaultValue
}

var TemplateVariable_SelectContent_DEFAULT string

func (p *TemplateVariable) GetSelectContent() (v string) {
	if !p.IsSetSelectContent() {
		return TemplateVariable_SelectContent_DEFAULT
	}
	return *p.SelectContent
}

func (p *TemplateVariable) GetID() (v string) {
	return p.ID
}

func (p *TemplateVariable) IsSetRequired() bool {
	return p.Required != nil
}

func (p *TemplateVariable) IsSetDescription() bool {
	return p.Description != nil
}

func (p *TemplateVariable) IsSetPlaceholder() bool {
	return p.Placeholder != nil
}

func (p *TemplateVariable) IsSetDefaultValue() bool {
	return p.DefaultValue != nil
}

func (p *TemplateVariable) IsSetSelectContent() bool {
	return p.SelectContent != nil
}

func (p *TemplateVariable) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateVariable(%+v)", *p)
}

type TemplateFormValue struct {
	Variables map[string]*TemplateVariableValue `thrift:"Variables,1,required" json:"variables"`
}

func NewTemplateFormValue() *TemplateFormValue {
	return &TemplateFormValue{}
}

func (p *TemplateFormValue) InitDefault() {
}

func (p *TemplateFormValue) GetVariables() (v map[string]*TemplateVariableValue) {
	return p.Variables
}

func (p *TemplateFormValue) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateFormValue(%+v)", *p)
}

type TemplateVariableValue struct {
	Content     *string               `thrift:"Content,1,optional" json:"content,omitempty"`
	Attachments []*AttachmentRequired `thrift:"attachments,2,optional" json:"attachments,omitempty"`
}

func NewTemplateVariableValue() *TemplateVariableValue {
	return &TemplateVariableValue{}
}

func (p *TemplateVariableValue) InitDefault() {
}

var TemplateVariableValue_Content_DEFAULT string

func (p *TemplateVariableValue) GetContent() (v string) {
	if !p.IsSetContent() {
		return TemplateVariableValue_Content_DEFAULT
	}
	return *p.Content
}

var TemplateVariableValue_Attachments_DEFAULT []*AttachmentRequired

func (p *TemplateVariableValue) GetAttachments() (v []*AttachmentRequired) {
	if !p.IsSetAttachments() {
		return TemplateVariableValue_Attachments_DEFAULT
	}
	return p.Attachments
}

func (p *TemplateVariableValue) IsSetContent() bool {
	return p.Content != nil
}

func (p *TemplateVariableValue) IsSetAttachments() bool {
	return p.Attachments != nil
}

func (p *TemplateVariableValue) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateVariableValue(%+v)", *p)
}

type EstimatedMinutes struct {
	Minimum int64 `thrift:"Minimum,1,required" json:"min"`
	Maximum int64 `thrift:"Maximum,2,required" json:"max"`
}

func NewEstimatedMinutes() *EstimatedMinutes {
	return &EstimatedMinutes{}
}

func (p *EstimatedMinutes) InitDefault() {
}

func (p *EstimatedMinutes) GetMinimum() (v int64) {
	return p.Minimum
}

func (p *EstimatedMinutes) GetMaximum() (v int64) {
	return p.Maximum
}

func (p *EstimatedMinutes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EstimatedMinutes(%+v)", *p)
}

type GetHistoryTemplateVariablesRequest struct {
	TemplateID string  `thrift:"TemplateID,1,required" json:"template_id,required" path:"template_id,required"`
	SpaceID    *string `thrift:"SpaceID,2,optional" json:"space_id,omitempty" query:"space_id"`
}

func NewGetHistoryTemplateVariablesRequest() *GetHistoryTemplateVariablesRequest {
	return &GetHistoryTemplateVariablesRequest{}
}

func (p *GetHistoryTemplateVariablesRequest) InitDefault() {
}

func (p *GetHistoryTemplateVariablesRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var GetHistoryTemplateVariablesRequest_SpaceID_DEFAULT string

func (p *GetHistoryTemplateVariablesRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return GetHistoryTemplateVariablesRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *GetHistoryTemplateVariablesRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *GetHistoryTemplateVariablesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetHistoryTemplateVariablesRequest(%+v)", *p)
}

type GetHistoryTemplateVariablesResponse struct {
	FormValue []*TemplateFormValueDetail `thrift:"FormValue,1,required" json:"form_value"`
}

func NewGetHistoryTemplateVariablesResponse() *GetHistoryTemplateVariablesResponse {
	return &GetHistoryTemplateVariablesResponse{}
}

func (p *GetHistoryTemplateVariablesResponse) InitDefault() {
}

func (p *GetHistoryTemplateVariablesResponse) GetFormValue() (v []*TemplateFormValueDetail) {
	return p.FormValue
}

func (p *GetHistoryTemplateVariablesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetHistoryTemplateVariablesResponse(%+v)", *p)
}

type TemplateFormValueDetail struct {
	Variables map[string]*TemplateVariableValueDetail `thrift:"Variables,1,required" json:"variables"`
}

func NewTemplateFormValueDetail() *TemplateFormValueDetail {
	return &TemplateFormValueDetail{}
}

func (p *TemplateFormValueDetail) InitDefault() {
}

func (p *TemplateFormValueDetail) GetVariables() (v map[string]*TemplateVariableValueDetail) {
	return p.Variables
}

func (p *TemplateFormValueDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateFormValueDetail(%+v)", *p)
}

type TemplateVariableValueDetail struct {
	Content     *string       `thrift:"Content,1,optional" json:"content,omitempty"`
	Attachments []*Attachment `thrift:"attachments,2,optional" json:"attachments,omitempty"`
}

func NewTemplateVariableValueDetail() *TemplateVariableValueDetail {
	return &TemplateVariableValueDetail{}
}

func (p *TemplateVariableValueDetail) InitDefault() {
}

var TemplateVariableValueDetail_Content_DEFAULT string

func (p *TemplateVariableValueDetail) GetContent() (v string) {
	if !p.IsSetContent() {
		return TemplateVariableValueDetail_Content_DEFAULT
	}
	return *p.Content
}

var TemplateVariableValueDetail_Attachments_DEFAULT []*Attachment

func (p *TemplateVariableValueDetail) GetAttachments() (v []*Attachment) {
	if !p.IsSetAttachments() {
		return TemplateVariableValueDetail_Attachments_DEFAULT
	}
	return p.Attachments
}

func (p *TemplateVariableValueDetail) IsSetContent() bool {
	return p.Content != nil
}

func (p *TemplateVariableValueDetail) IsSetAttachments() bool {
	return p.Attachments != nil
}

func (p *TemplateVariableValueDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateVariableValueDetail(%+v)", *p)
}

type GetSessionAgentRunRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
}

func NewGetSessionAgentRunRequest() *GetSessionAgentRunRequest {
	return &GetSessionAgentRunRequest{}
}

func (p *GetSessionAgentRunRequest) InitDefault() {
}

func (p *GetSessionAgentRunRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *GetSessionAgentRunRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionAgentRunRequest(%+v)", *p)
}

type GetSessionAgentRunResponse struct {
	IsAvailable bool         `thrift:"IsAvailable,1,required" json:"is_available"`
	RuntimeMeta *RuntimeMeta `thrift:"RuntimeMeta,2,required" json:"runtime_meta"`
	CreatedAt   string       `thrift:"CreatedAt,3,required" json:"created_at"`
	UpdatedAt   string       `thrift:"UpdatedAt,4,required" json:"updated_at"`
}

func NewGetSessionAgentRunResponse() *GetSessionAgentRunResponse {
	return &GetSessionAgentRunResponse{}
}

func (p *GetSessionAgentRunResponse) InitDefault() {
}

func (p *GetSessionAgentRunResponse) GetIsAvailable() (v bool) {
	return p.IsAvailable
}

var GetSessionAgentRunResponse_RuntimeMeta_DEFAULT *RuntimeMeta

func (p *GetSessionAgentRunResponse) GetRuntimeMeta() (v *RuntimeMeta) {
	if !p.IsSetRuntimeMeta() {
		return GetSessionAgentRunResponse_RuntimeMeta_DEFAULT
	}
	return p.RuntimeMeta
}

func (p *GetSessionAgentRunResponse) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *GetSessionAgentRunResponse) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *GetSessionAgentRunResponse) IsSetRuntimeMeta() bool {
	return p.RuntimeMeta != nil
}

func (p *GetSessionAgentRunResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionAgentRunResponse(%+v)", *p)
}

type SubmitToolCallRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
	// ToolCalls Agent 发送工具调用，用户需要回复 Agent 时填写，例如二次确认、授权等
	ToolCalls []*ToolCall `thrift:"ToolCalls,2,optional" json:"tool_calls"`
}

func NewSubmitToolCallRequest() *SubmitToolCallRequest {
	return &SubmitToolCallRequest{}
}

func (p *SubmitToolCallRequest) InitDefault() {
}

func (p *SubmitToolCallRequest) GetSessionID() (v string) {
	return p.SessionID
}

var SubmitToolCallRequest_ToolCalls_DEFAULT []*ToolCall

func (p *SubmitToolCallRequest) GetToolCalls() (v []*ToolCall) {
	if !p.IsSetToolCalls() {
		return SubmitToolCallRequest_ToolCalls_DEFAULT
	}
	return p.ToolCalls
}

func (p *SubmitToolCallRequest) IsSetToolCalls() bool {
	return p.ToolCalls != nil
}

func (p *SubmitToolCallRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitToolCallRequest(%+v)", *p)
}

type SubmitToolCallResponse struct {
}

func NewSubmitToolCallResponse() *SubmitToolCallResponse {
	return &SubmitToolCallResponse{}
}

func (p *SubmitToolCallResponse) InitDefault() {
}

func (p *SubmitToolCallResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitToolCallResponse(%+v)", *p)
}

// 会话MCP详细信息
type SessionMCPDetail struct {
	// MCP ID
	ID string `thrift:"ID,1,required" json:"id"`
	// MCP 名称
	Name string `thrift:"Name,2,required" json:"name"`
	// MCP 描述
	Description string `thrift:"Description,3,required" json:"description"`
	// MCP 来源
	Source MCPSource `thrift:"Source,4,required" json:"source"`
	// 工具列表
	Tools []*MCPTool `thrift:"Tools,5,optional" json:"tools"`
	// Agent看到的MCP名称
	NameForAgent string `thrift:"NameForAgent,6,required" "name_for_agent" json:"name_for_agent,required" `
	// 获取不到工具情况下的错误信息
	ErrMessage *string `thrift:"ErrMessage,255,optional" json:"err_message,omitempty"`
}

func NewSessionMCPDetail() *SessionMCPDetail {
	return &SessionMCPDetail{}
}

func (p *SessionMCPDetail) InitDefault() {
}

func (p *SessionMCPDetail) GetID() (v string) {
	return p.ID
}

func (p *SessionMCPDetail) GetName() (v string) {
	return p.Name
}

func (p *SessionMCPDetail) GetDescription() (v string) {
	return p.Description
}

func (p *SessionMCPDetail) GetSource() (v MCPSource) {
	return p.Source
}

var SessionMCPDetail_Tools_DEFAULT []*MCPTool

func (p *SessionMCPDetail) GetTools() (v []*MCPTool) {
	if !p.IsSetTools() {
		return SessionMCPDetail_Tools_DEFAULT
	}
	return p.Tools
}

func (p *SessionMCPDetail) GetNameForAgent() (v string) {
	return p.NameForAgent
}

var SessionMCPDetail_ErrMessage_DEFAULT string

func (p *SessionMCPDetail) GetErrMessage() (v string) {
	if !p.IsSetErrMessage() {
		return SessionMCPDetail_ErrMessage_DEFAULT
	}
	return *p.ErrMessage
}

func (p *SessionMCPDetail) IsSetTools() bool {
	return p.Tools != nil
}

func (p *SessionMCPDetail) IsSetErrMessage() bool {
	return p.ErrMessage != nil
}

func (p *SessionMCPDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SessionMCPDetail(%+v)", *p)
}

// 获取会话MCP详细信息的请求
type GetSessionMCPDetailsRequest struct {
	// 会话ID
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
}

func NewGetSessionMCPDetailsRequest() *GetSessionMCPDetailsRequest {
	return &GetSessionMCPDetailsRequest{}
}

func (p *GetSessionMCPDetailsRequest) InitDefault() {
}

func (p *GetSessionMCPDetailsRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *GetSessionMCPDetailsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionMCPDetailsRequest(%+v)", *p)
}

// 获取会话MCP详细信息的响应
type GetSessionMCPDetailsResponse struct {
	// MCP详细信息列表
	MCPs []*SessionMCPDetail `thrift:"MCPs,1,optional" json:"mcps"`
}

func NewGetSessionMCPDetailsResponse() *GetSessionMCPDetailsResponse {
	return &GetSessionMCPDetailsResponse{}
}

func (p *GetSessionMCPDetailsResponse) InitDefault() {
}

var GetSessionMCPDetailsResponse_MCPs_DEFAULT []*SessionMCPDetail

func (p *GetSessionMCPDetailsResponse) GetMCPs() (v []*SessionMCPDetail) {
	if !p.IsSetMCPs() {
		return GetSessionMCPDetailsResponse_MCPs_DEFAULT
	}
	return p.MCPs
}

func (p *GetSessionMCPDetailsResponse) IsSetMCPs() bool {
	return p.MCPs != nil
}

func (p *GetSessionMCPDetailsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSessionMCPDetailsResponse(%+v)", *p)
}

type CreateSessionStarRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
}

func NewCreateSessionStarRequest() *CreateSessionStarRequest {
	return &CreateSessionStarRequest{}
}

func (p *CreateSessionStarRequest) InitDefault() {
}

func (p *CreateSessionStarRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *CreateSessionStarRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSessionStarRequest(%+v)", *p)
}

type CreateSessionStarResponse struct {
}

func NewCreateSessionStarResponse() *CreateSessionStarResponse {
	return &CreateSessionStarResponse{}
}

func (p *CreateSessionStarResponse) InitDefault() {
}

func (p *CreateSessionStarResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSessionStarResponse(%+v)", *p)
}

type DeleteSessionStarRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" path:"session_id,required"`
}

func NewDeleteSessionStarRequest() *DeleteSessionStarRequest {
	return &DeleteSessionStarRequest{}
}

func (p *DeleteSessionStarRequest) InitDefault() {
}

func (p *DeleteSessionStarRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *DeleteSessionStarRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSessionStarRequest(%+v)", *p)
}

type DeleteSessionStarResponse struct {
}

func NewDeleteSessionStarResponse() *DeleteSessionStarResponse {
	return &DeleteSessionStarResponse{}
}

func (p *DeleteSessionStarResponse) InitDefault() {
}

func (p *DeleteSessionStarResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSessionStarResponse(%+v)", *p)
}
