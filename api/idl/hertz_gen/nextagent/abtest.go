// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
)

type GetABTestConfigRequest struct {
}

func NewGetABTestConfigRequest() *GetABTestConfigRequest {
	return &GetABTestConfigRequest{}
}

func (p *GetABTestConfigRequest) InitDefault() {
}

func (p *GetABTestConfigRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetABTestConfigRequest(%+v)", *p)
}

type GetABTestConfigResponse struct {
	Data map[string]common.JsonVariables `thrift:"Data,1,required" json:"data"`
}

func NewGetABTestConfigResponse() *GetABTestConfigResponse {
	return &GetABTestConfigResponse{}
}

func (p *GetABTestConfigResponse) InitDefault() {
}

func (p *GetABTestConfigResponse) GetData() (v map[string]common.JsonVariables) {
	return p.Data
}

func (p *GetABTestConfigResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetABTestConfigResponse(%+v)", *p)
}
