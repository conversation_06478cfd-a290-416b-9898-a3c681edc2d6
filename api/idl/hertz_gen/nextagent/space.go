// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

const (
	SpaceStatusActive = "active"

	SpaceStatusInactive = "inactive"

	SpaceStatusDeleted = "deleted"

	SpaceStatusUninit = "uninit"

	SpaceTypePersonal = "personal"

	SpaceTypeProject = "project"
)

type SpaceResourceIDType int64

const (
	SpaceResourceIDType_LarkURL SpaceResourceIDType = 1
	SpaceResourceIDType_RepoURL SpaceResourceIDType = 2
)

func (p SpaceResourceIDType) String() string {
	switch p {
	case SpaceResourceIDType_LarkURL:
		return "LarkURL"
	case SpaceResourceIDType_RepoURL:
		return "RepoURL"
	}
	return "<UNSET>"
}

func SpaceResourceIDTypeFromString(s string) (SpaceResourceIDType, error) {
	switch s {
	case "LarkURL":
		return SpaceResourceIDType_LarkURL, nil
	case "RepoURL":
		return SpaceResourceIDType_RepoURL, nil
	}
	return SpaceResourceIDType(0), fmt.Errorf("not a valid SpaceResourceIDType string")
}

func SpaceResourceIDTypePtr(v SpaceResourceIDType) *SpaceResourceIDType { return &v }
func (p *SpaceResourceIDType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SpaceResourceIDType(result.Int64)
	return
}

func (p *SpaceResourceIDType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SpaceStatus = string

type SpaceType = string

// 数据结构定义
type Space struct {
	ID          string  `thrift:"ID,1,required" json:"id"`
	Name        string  `thrift:"Name,2,required" json:"name"`
	NameEN      *string `thrift:"NameEN,3,optional" json:"name_en,omitempty"`
	Description *string `thrift:"Description,4,optional" json:"description,omitempty"`
	Creator     string  `thrift:"Creator,5,required" json:"creator"`
	// 空间类型
	Type      SpaceType   `thrift:"Type,6,required" json:"type"`
	Status    SpaceStatus `thrift:"Status,7,required" json:"status"`
	CreatedAt string      `thrift:"CreatedAt,8,required" json:"created_at"`
	UpdatedAt string      `thrift:"UpdatedAt,9,required" json:"updated_at"`
	DeletedAt *string     `thrift:"DeletedAt,10,optional" json:"deleted_at,omitempty"`
	// 空间用户列表(可选)
	Members     []*SpaceMember `thrift:"Members,11,optional" json:"members,omitempty"`
	DataSetID   string         `thrift:"DataSetID,12,required" json:"dataset_id"`
	SpaceConfig *SpaceConfig   `thrift:"SpaceConfig,13,optional" json:"space_config,omitempty"`
	// 权限列表
	PermissionActions []PermissionAction `thrift:"PermissionActions,14,optional" json:"permission_actions,omitempty"`
}

func NewSpace() *Space {
	return &Space{}
}

func (p *Space) InitDefault() {
}

func (p *Space) GetID() (v string) {
	return p.ID
}

func (p *Space) GetName() (v string) {
	return p.Name
}

var Space_NameEN_DEFAULT string

func (p *Space) GetNameEN() (v string) {
	if !p.IsSetNameEN() {
		return Space_NameEN_DEFAULT
	}
	return *p.NameEN
}

var Space_Description_DEFAULT string

func (p *Space) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return Space_Description_DEFAULT
	}
	return *p.Description
}

func (p *Space) GetCreator() (v string) {
	return p.Creator
}

func (p *Space) GetType() (v SpaceType) {
	return p.Type
}

func (p *Space) GetStatus() (v SpaceStatus) {
	return p.Status
}

func (p *Space) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Space) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var Space_DeletedAt_DEFAULT string

func (p *Space) GetDeletedAt() (v string) {
	if !p.IsSetDeletedAt() {
		return Space_DeletedAt_DEFAULT
	}
	return *p.DeletedAt
}

var Space_Members_DEFAULT []*SpaceMember

func (p *Space) GetMembers() (v []*SpaceMember) {
	if !p.IsSetMembers() {
		return Space_Members_DEFAULT
	}
	return p.Members
}

func (p *Space) GetDataSetID() (v string) {
	return p.DataSetID
}

var Space_SpaceConfig_DEFAULT *SpaceConfig

func (p *Space) GetSpaceConfig() (v *SpaceConfig) {
	if !p.IsSetSpaceConfig() {
		return Space_SpaceConfig_DEFAULT
	}
	return p.SpaceConfig
}

var Space_PermissionActions_DEFAULT []PermissionAction

func (p *Space) GetPermissionActions() (v []PermissionAction) {
	if !p.IsSetPermissionActions() {
		return Space_PermissionActions_DEFAULT
	}
	return p.PermissionActions
}

func (p *Space) IsSetNameEN() bool {
	return p.NameEN != nil
}

func (p *Space) IsSetDescription() bool {
	return p.Description != nil
}

func (p *Space) IsSetDeletedAt() bool {
	return p.DeletedAt != nil
}

func (p *Space) IsSetMembers() bool {
	return p.Members != nil
}

func (p *Space) IsSetSpaceConfig() bool {
	return p.SpaceConfig != nil
}

func (p *Space) IsSetPermissionActions() bool {
	return p.PermissionActions != nil
}

func (p *Space) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Space(%+v)", *p)
}

type SpaceMember struct {
	// member类型
	Type PermissionType `thrift:"Type,1,required" json:"type"`
	Name string         `thrift:"Name,2,required" json:"name"`
	Role PermissionRole `thrift:"Role,3,required" json:"role"`
}

func NewSpaceMember() *SpaceMember {
	return &SpaceMember{}
}

func (p *SpaceMember) InitDefault() {
}

func (p *SpaceMember) GetType() (v PermissionType) {
	return p.Type
}

func (p *SpaceMember) GetName() (v string) {
	return p.Name
}

func (p *SpaceMember) GetRole() (v PermissionRole) {
	return p.Role
}

func (p *SpaceMember) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpaceMember(%+v)", *p)
}

type CreateSpaceRequest struct {
	Name        string       `thrift:"Name,1,required" json:"name"`
	NameEN      *string      `thrift:"NameEN,2,optional" json:"name_en,omitempty"`
	Description *string      `thrift:"Description,3,optional" json:"description,omitempty"`
	SpaceConfig *SpaceConfig `thrift:"SpaceConfig,4,optional" json:"space_config,omitempty"`
}

func NewCreateSpaceRequest() *CreateSpaceRequest {
	return &CreateSpaceRequest{}
}

func (p *CreateSpaceRequest) InitDefault() {
}

func (p *CreateSpaceRequest) GetName() (v string) {
	return p.Name
}

var CreateSpaceRequest_NameEN_DEFAULT string

func (p *CreateSpaceRequest) GetNameEN() (v string) {
	if !p.IsSetNameEN() {
		return CreateSpaceRequest_NameEN_DEFAULT
	}
	return *p.NameEN
}

var CreateSpaceRequest_Description_DEFAULT string

func (p *CreateSpaceRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return CreateSpaceRequest_Description_DEFAULT
	}
	return *p.Description
}

var CreateSpaceRequest_SpaceConfig_DEFAULT *SpaceConfig

func (p *CreateSpaceRequest) GetSpaceConfig() (v *SpaceConfig) {
	if !p.IsSetSpaceConfig() {
		return CreateSpaceRequest_SpaceConfig_DEFAULT
	}
	return p.SpaceConfig
}

func (p *CreateSpaceRequest) IsSetNameEN() bool {
	return p.NameEN != nil
}

func (p *CreateSpaceRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *CreateSpaceRequest) IsSetSpaceConfig() bool {
	return p.SpaceConfig != nil
}

func (p *CreateSpaceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSpaceRequest(%+v)", *p)
}

type CreateSpaceResponse struct {
	Space *Space `thrift:"Space,1,required" json:"space"`
}

func NewCreateSpaceResponse() *CreateSpaceResponse {
	return &CreateSpaceResponse{}
}

func (p *CreateSpaceResponse) InitDefault() {
}

var CreateSpaceResponse_Space_DEFAULT *Space

func (p *CreateSpaceResponse) GetSpace() (v *Space) {
	if !p.IsSetSpace() {
		return CreateSpaceResponse_Space_DEFAULT
	}
	return p.Space
}

func (p *CreateSpaceResponse) IsSetSpace() bool {
	return p.Space != nil
}

func (p *CreateSpaceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSpaceResponse(%+v)", *p)
}

type CreatePersonalSpaceRequest struct {
	UserName string `thrift:"UserName,1,required" json:"user_name"`
}

func NewCreatePersonalSpaceRequest() *CreatePersonalSpaceRequest {
	return &CreatePersonalSpaceRequest{}
}

func (p *CreatePersonalSpaceRequest) InitDefault() {
}

func (p *CreatePersonalSpaceRequest) GetUserName() (v string) {
	return p.UserName
}

func (p *CreatePersonalSpaceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreatePersonalSpaceRequest(%+v)", *p)
}

type CreatePersonalSpaceResponse struct {
	// space
	Space *Space `thrift:"Space,1,required" json:"space"`
	// resource
	Resource *Resource `thrift:"Resource,2,optional" json:"resource,omitempty"`
}

func NewCreatePersonalSpaceResponse() *CreatePersonalSpaceResponse {
	return &CreatePersonalSpaceResponse{}
}

func (p *CreatePersonalSpaceResponse) InitDefault() {
}

var CreatePersonalSpaceResponse_Space_DEFAULT *Space

func (p *CreatePersonalSpaceResponse) GetSpace() (v *Space) {
	if !p.IsSetSpace() {
		return CreatePersonalSpaceResponse_Space_DEFAULT
	}
	return p.Space
}

var CreatePersonalSpaceResponse_Resource_DEFAULT *Resource

func (p *CreatePersonalSpaceResponse) GetResource() (v *Resource) {
	if !p.IsSetResource() {
		return CreatePersonalSpaceResponse_Resource_DEFAULT
	}
	return p.Resource
}

func (p *CreatePersonalSpaceResponse) IsSetSpace() bool {
	return p.Space != nil
}

func (p *CreatePersonalSpaceResponse) IsSetResource() bool {
	return p.Resource != nil
}

func (p *CreatePersonalSpaceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreatePersonalSpaceResponse(%+v)", *p)
}

type SpaceBaseConfig struct {
	// 空间 session 默认可见性，默认 false 不可见，调整后只对后续新创建的 session 生效，之前创建的按照之前的配置决定
	SessionVisibility *bool `thrift:"SessionVisibility,1,optional" json:"session_visibility"`
}

func NewSpaceBaseConfig() *SpaceBaseConfig {
	return &SpaceBaseConfig{}
}

func (p *SpaceBaseConfig) InitDefault() {
}

var SpaceBaseConfig_SessionVisibility_DEFAULT bool

func (p *SpaceBaseConfig) GetSessionVisibility() (v bool) {
	if !p.IsSetSessionVisibility() {
		return SpaceBaseConfig_SessionVisibility_DEFAULT
	}
	return *p.SessionVisibility
}

func (p *SpaceBaseConfig) IsSetSessionVisibility() bool {
	return p.SessionVisibility != nil
}

func (p *SpaceBaseConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpaceBaseConfig(%+v)", *p)
}

type SpaceConfig struct {
	BaseConfig *SpaceBaseConfig `thrift:"BaseConfig,1,optional" json:"base_config"`
}

func NewSpaceConfig() *SpaceConfig {
	return &SpaceConfig{}
}

func (p *SpaceConfig) InitDefault() {
}

var SpaceConfig_BaseConfig_DEFAULT *SpaceBaseConfig

func (p *SpaceConfig) GetBaseConfig() (v *SpaceBaseConfig) {
	if !p.IsSetBaseConfig() {
		return SpaceConfig_BaseConfig_DEFAULT
	}
	return p.BaseConfig
}

func (p *SpaceConfig) IsSetBaseConfig() bool {
	return p.BaseConfig != nil
}

func (p *SpaceConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpaceConfig(%+v)", *p)
}

type UpdateSpaceRequest struct {
	SpaceId     string       `thrift:"SpaceId,1,required" json:"space_id"`
	Name        *string      `thrift:"Name,2,optional" json:"name,omitempty"`
	NameEN      *string      `thrift:"NameEN,3,optional" json:"name_en,omitempty"`
	Description *string      `thrift:"Description,4,optional" json:"description,omitempty"`
	SpaceConfig *SpaceConfig `thrift:"SpaceConfig,5,optional" json:"space_config,omitempty"`
}

func NewUpdateSpaceRequest() *UpdateSpaceRequest {
	return &UpdateSpaceRequest{}
}

func (p *UpdateSpaceRequest) InitDefault() {
}

func (p *UpdateSpaceRequest) GetSpaceId() (v string) {
	return p.SpaceId
}

var UpdateSpaceRequest_Name_DEFAULT string

func (p *UpdateSpaceRequest) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateSpaceRequest_Name_DEFAULT
	}
	return *p.Name
}

var UpdateSpaceRequest_NameEN_DEFAULT string

func (p *UpdateSpaceRequest) GetNameEN() (v string) {
	if !p.IsSetNameEN() {
		return UpdateSpaceRequest_NameEN_DEFAULT
	}
	return *p.NameEN
}

var UpdateSpaceRequest_Description_DEFAULT string

func (p *UpdateSpaceRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UpdateSpaceRequest_Description_DEFAULT
	}
	return *p.Description
}

var UpdateSpaceRequest_SpaceConfig_DEFAULT *SpaceConfig

func (p *UpdateSpaceRequest) GetSpaceConfig() (v *SpaceConfig) {
	if !p.IsSetSpaceConfig() {
		return UpdateSpaceRequest_SpaceConfig_DEFAULT
	}
	return p.SpaceConfig
}

func (p *UpdateSpaceRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateSpaceRequest) IsSetNameEN() bool {
	return p.NameEN != nil
}

func (p *UpdateSpaceRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UpdateSpaceRequest) IsSetSpaceConfig() bool {
	return p.SpaceConfig != nil
}

func (p *UpdateSpaceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateSpaceRequest(%+v)", *p)
}

type UpdateSpaceResponse struct {
	Space *Space `thrift:"Space,1,required" json:"space"`
}

func NewUpdateSpaceResponse() *UpdateSpaceResponse {
	return &UpdateSpaceResponse{}
}

func (p *UpdateSpaceResponse) InitDefault() {
}

var UpdateSpaceResponse_Space_DEFAULT *Space

func (p *UpdateSpaceResponse) GetSpace() (v *Space) {
	if !p.IsSetSpace() {
		return UpdateSpaceResponse_Space_DEFAULT
	}
	return p.Space
}

func (p *UpdateSpaceResponse) IsSetSpace() bool {
	return p.Space != nil
}

func (p *UpdateSpaceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateSpaceResponse(%+v)", *p)
}

type GetSpaceRequest struct {
	SpaceId     *string       `thrift:"SpaceId,1,optional" json:"space_id,omitempty" query:"space_id"`
	Type        *ResourceType `thrift:"Type,2,optional" json:"type,omitempty" query:"type"`
	ExternalID  *string       `thrift:"ExternalID,3,optional" json:"external_id,omitempty" query:"external_id"`
	NeedMembers *bool         `thrift:"NeedMembers,4,optional" json:"need_members,omitempty" query:"need_members"`
}

func NewGetSpaceRequest() *GetSpaceRequest {
	return &GetSpaceRequest{}
}

func (p *GetSpaceRequest) InitDefault() {
}

var GetSpaceRequest_SpaceId_DEFAULT string

func (p *GetSpaceRequest) GetSpaceId() (v string) {
	if !p.IsSetSpaceId() {
		return GetSpaceRequest_SpaceId_DEFAULT
	}
	return *p.SpaceId
}

var GetSpaceRequest_Type_DEFAULT ResourceType

func (p *GetSpaceRequest) GetType() (v ResourceType) {
	if !p.IsSetType() {
		return GetSpaceRequest_Type_DEFAULT
	}
	return *p.Type
}

var GetSpaceRequest_ExternalID_DEFAULT string

func (p *GetSpaceRequest) GetExternalID() (v string) {
	if !p.IsSetExternalID() {
		return GetSpaceRequest_ExternalID_DEFAULT
	}
	return *p.ExternalID
}

var GetSpaceRequest_NeedMembers_DEFAULT bool

func (p *GetSpaceRequest) GetNeedMembers() (v bool) {
	if !p.IsSetNeedMembers() {
		return GetSpaceRequest_NeedMembers_DEFAULT
	}
	return *p.NeedMembers
}

func (p *GetSpaceRequest) IsSetSpaceId() bool {
	return p.SpaceId != nil
}

func (p *GetSpaceRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *GetSpaceRequest) IsSetExternalID() bool {
	return p.ExternalID != nil
}

func (p *GetSpaceRequest) IsSetNeedMembers() bool {
	return p.NeedMembers != nil
}

func (p *GetSpaceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpaceRequest(%+v)", *p)
}

type GetSpaceResponse struct {
	Space          *Space `thrift:"Space,1,required" json:"space"`
	HavePermission *bool  `thrift:"HavePermission,2,optional" json:"have_permission"`
	IsNotFound     *bool  `thrift:"IsNotFound,3,optional" json:"is_not_found"`
}

func NewGetSpaceResponse() *GetSpaceResponse {
	return &GetSpaceResponse{}
}

func (p *GetSpaceResponse) InitDefault() {
}

var GetSpaceResponse_Space_DEFAULT *Space

func (p *GetSpaceResponse) GetSpace() (v *Space) {
	if !p.IsSetSpace() {
		return GetSpaceResponse_Space_DEFAULT
	}
	return p.Space
}

var GetSpaceResponse_HavePermission_DEFAULT bool

func (p *GetSpaceResponse) GetHavePermission() (v bool) {
	if !p.IsSetHavePermission() {
		return GetSpaceResponse_HavePermission_DEFAULT
	}
	return *p.HavePermission
}

var GetSpaceResponse_IsNotFound_DEFAULT bool

func (p *GetSpaceResponse) GetIsNotFound() (v bool) {
	if !p.IsSetIsNotFound() {
		return GetSpaceResponse_IsNotFound_DEFAULT
	}
	return *p.IsNotFound
}

func (p *GetSpaceResponse) IsSetSpace() bool {
	return p.Space != nil
}

func (p *GetSpaceResponse) IsSetHavePermission() bool {
	return p.HavePermission != nil
}

func (p *GetSpaceResponse) IsSetIsNotFound() bool {
	return p.IsNotFound != nil
}

func (p *GetSpaceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpaceResponse(%+v)", *p)
}

type DeleteSpaceRequest struct {
	SpaceId string `thrift:"SpaceId,1,required" json:"space_id" form:"space_id,required" `
}

func NewDeleteSpaceRequest() *DeleteSpaceRequest {
	return &DeleteSpaceRequest{}
}

func (p *DeleteSpaceRequest) InitDefault() {
}

func (p *DeleteSpaceRequest) GetSpaceId() (v string) {
	return p.SpaceId
}

func (p *DeleteSpaceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSpaceRequest(%+v)", *p)
}

type DeleteSpaceResponse struct {
	Success bool `thrift:"Success,1,required" json:"success"`
}

func NewDeleteSpaceResponse() *DeleteSpaceResponse {
	return &DeleteSpaceResponse{}
}

func (p *DeleteSpaceResponse) InitDefault() {
}

func (p *DeleteSpaceResponse) GetSuccess() (v bool) {
	return p.Success
}

func (p *DeleteSpaceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSpaceResponse(%+v)", *p)
}

type ListUserSpacesRequest struct {
	StartID *string    `thrift:"StartID,1,optional" json:"start_id,omitempty" query:"start_id"`
	Limit   *int64     `thrift:"Limit,2,optional" json:"limit,omitempty" query:"limit"`
	Type    *SpaceType `thrift:"Type,3,optional" json:"type,omitempty" query:"type"`
}

func NewListUserSpacesRequest() *ListUserSpacesRequest {
	return &ListUserSpacesRequest{}
}

func (p *ListUserSpacesRequest) InitDefault() {
}

var ListUserSpacesRequest_StartID_DEFAULT string

func (p *ListUserSpacesRequest) GetStartID() (v string) {
	if !p.IsSetStartID() {
		return ListUserSpacesRequest_StartID_DEFAULT
	}
	return *p.StartID
}

var ListUserSpacesRequest_Limit_DEFAULT int64

func (p *ListUserSpacesRequest) GetLimit() (v int64) {
	if !p.IsSetLimit() {
		return ListUserSpacesRequest_Limit_DEFAULT
	}
	return *p.Limit
}

var ListUserSpacesRequest_Type_DEFAULT SpaceType

func (p *ListUserSpacesRequest) GetType() (v SpaceType) {
	if !p.IsSetType() {
		return ListUserSpacesRequest_Type_DEFAULT
	}
	return *p.Type
}

func (p *ListUserSpacesRequest) IsSetStartID() bool {
	return p.StartID != nil
}

func (p *ListUserSpacesRequest) IsSetLimit() bool {
	return p.Limit != nil
}

func (p *ListUserSpacesRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *ListUserSpacesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUserSpacesRequest(%+v)", *p)
}

type ListUserSpacesResponse struct {
	Spaces []*Space `thrift:"Spaces,1,required" json:"spaces"`
	NextID string   `thrift:"NextID,2,required" json:"next_id"`
}

func NewListUserSpacesResponse() *ListUserSpacesResponse {
	return &ListUserSpacesResponse{}
}

func (p *ListUserSpacesResponse) InitDefault() {
}

func (p *ListUserSpacesResponse) GetSpaces() (v []*Space) {
	return p.Spaces
}

func (p *ListUserSpacesResponse) GetNextID() (v string) {
	return p.NextID
}

func (p *ListUserSpacesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUserSpacesResponse(%+v)", *p)
}

type ListAllSpacesRequest struct {
	PageNum  int64 `thrift:"PageNum,1,required" json:"page_num,required" query:"page_num,required"`
	PageSize int64 `thrift:"PageSize,2,required" json:"page_size,required" query:"page_size,required"`
}

func NewListAllSpacesRequest() *ListAllSpacesRequest {
	return &ListAllSpacesRequest{}
}

func (p *ListAllSpacesRequest) InitDefault() {
}

func (p *ListAllSpacesRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListAllSpacesRequest) GetPageSize() (v int64) {
	return p.PageSize
}

func (p *ListAllSpacesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAllSpacesRequest(%+v)", *p)
}

type ListAllSpacesResponse struct {
	Spaces []*Space `thrift:"Spaces,1,required" json:"spaces"`
	Total  int64    `thrift:"Total,2,required" json:"total"`
}

func NewListAllSpacesResponse() *ListAllSpacesResponse {
	return &ListAllSpacesResponse{}
}

func (p *ListAllSpacesResponse) InitDefault() {
}

func (p *ListAllSpacesResponse) GetSpaces() (v []*Space) {
	return p.Spaces
}

func (p *ListAllSpacesResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListAllSpacesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAllSpacesResponse(%+v)", *p)
}

type AddSpaceMemberRequest struct {
	SpaceId string `thrift:"SpaceId,1,required" json:"space_id"`
	// 成员列表
	Members []*SpaceMember `thrift:"Members,2,required" json:"members"`
}

func NewAddSpaceMemberRequest() *AddSpaceMemberRequest {
	return &AddSpaceMemberRequest{}
}

func (p *AddSpaceMemberRequest) InitDefault() {
}

func (p *AddSpaceMemberRequest) GetSpaceId() (v string) {
	return p.SpaceId
}

func (p *AddSpaceMemberRequest) GetMembers() (v []*SpaceMember) {
	return p.Members
}

func (p *AddSpaceMemberRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddSpaceMemberRequest(%+v)", *p)
}

type AddSpaceMemberResponse struct {
	Success bool `thrift:"Success,1,required" json:"success"`
}

func NewAddSpaceMemberResponse() *AddSpaceMemberResponse {
	return &AddSpaceMemberResponse{}
}

func (p *AddSpaceMemberResponse) InitDefault() {
}

func (p *AddSpaceMemberResponse) GetSuccess() (v bool) {
	return p.Success
}

func (p *AddSpaceMemberResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddSpaceMemberResponse(%+v)", *p)
}

type RemoveSpaceMemberRequest struct {
	SpaceId string `thrift:"SpaceId,1,required" json:"space_id" form:"space_id,required" `
	// 成员列表
	Members []*SpaceMember `thrift:"Members,2,required" json:"members" form:"members,required" `
}

func NewRemoveSpaceMemberRequest() *RemoveSpaceMemberRequest {
	return &RemoveSpaceMemberRequest{}
}

func (p *RemoveSpaceMemberRequest) InitDefault() {
}

func (p *RemoveSpaceMemberRequest) GetSpaceId() (v string) {
	return p.SpaceId
}

func (p *RemoveSpaceMemberRequest) GetMembers() (v []*SpaceMember) {
	return p.Members
}

func (p *RemoveSpaceMemberRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveSpaceMemberRequest(%+v)", *p)
}

type RemoveSpaceMemberResponse struct {
	Success bool `thrift:"Success,1,required" json:"success"`
}

func NewRemoveSpaceMemberResponse() *RemoveSpaceMemberResponse {
	return &RemoveSpaceMemberResponse{}
}

func (p *RemoveSpaceMemberResponse) InitDefault() {
}

func (p *RemoveSpaceMemberResponse) GetSuccess() (v bool) {
	return p.Success
}

func (p *RemoveSpaceMemberResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveSpaceMemberResponse(%+v)", *p)
}

type ListSpaceMembersRequest struct {
	SpaceId  string `thrift:"SpaceId,1,required" json:"space_id,required" query:"space_id,required"`
	PageNum  int64  `thrift:"PageNum,2,required" json:"page_num,required" query:"page_num,required"`
	PageSize int64  `thrift:"PageSize,3,required" json:"page_size,required" query:"page_size,required"`
	// 过滤角色
	Role *PermissionRole `thrift:"Role,4,optional" json:"role,omitempty" query:"role"`
	// 过滤类型
	Type *PermissionType `thrift:"Type,5,optional" json:"type,omitempty" query:"type"`
}

func NewListSpaceMembersRequest() *ListSpaceMembersRequest {
	return &ListSpaceMembersRequest{}
}

func (p *ListSpaceMembersRequest) InitDefault() {
}

func (p *ListSpaceMembersRequest) GetSpaceId() (v string) {
	return p.SpaceId
}

func (p *ListSpaceMembersRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListSpaceMembersRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListSpaceMembersRequest_Role_DEFAULT PermissionRole

func (p *ListSpaceMembersRequest) GetRole() (v PermissionRole) {
	if !p.IsSetRole() {
		return ListSpaceMembersRequest_Role_DEFAULT
	}
	return *p.Role
}

var ListSpaceMembersRequest_Type_DEFAULT PermissionType

func (p *ListSpaceMembersRequest) GetType() (v PermissionType) {
	if !p.IsSetType() {
		return ListSpaceMembersRequest_Type_DEFAULT
	}
	return *p.Type
}

func (p *ListSpaceMembersRequest) IsSetRole() bool {
	return p.Role != nil
}

func (p *ListSpaceMembersRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *ListSpaceMembersRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceMembersRequest(%+v)", *p)
}

type ListSpaceMembersResponse struct {
	Members []*SpaceMember `thrift:"Members,1,required" json:"members"`
	Total   int64          `thrift:"Total,2,required" json:"total"`
}

func NewListSpaceMembersResponse() *ListSpaceMembersResponse {
	return &ListSpaceMembersResponse{}
}

func (p *ListSpaceMembersResponse) InitDefault() {
}

func (p *ListSpaceMembersResponse) GetMembers() (v []*SpaceMember) {
	return p.Members
}

func (p *ListSpaceMembersResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListSpaceMembersResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceMembersResponse(%+v)", *p)
}

type InitSpaceRequest struct {
	// 项目空间 ID
	SpaceID string `thrift:"SpaceID,1,required" json:"space_id"`
	// 空间基本信息
	Basic *BasicInfo `thrift:"Basic,2,required" json:"basic"`
	// 空间成员
	Members []*SpaceMember `thrift:"Members,3,optional" json:"members"`
	// 文档信息
	LarkDocConfig *LarkDocConfig `thrift:"LarkDocConfig,4,optional" json:"lark_doc_config"`
	// 仓库信息
	Repos []*CodeRepo `thrift:"Repos,5,optional" json:"repos"`
	// 项目信息
	Services []*Service `thrift:"Services,6,optional" json:"services"`
	// 其余空间配置信息
	PlatformConfig *PlatformConfig `thrift:"PlatformConfig,7,optional" json:"platform_config"`
}

func NewInitSpaceRequest() *InitSpaceRequest {
	return &InitSpaceRequest{}
}

func (p *InitSpaceRequest) InitDefault() {
}

func (p *InitSpaceRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

var InitSpaceRequest_Basic_DEFAULT *BasicInfo

func (p *InitSpaceRequest) GetBasic() (v *BasicInfo) {
	if !p.IsSetBasic() {
		return InitSpaceRequest_Basic_DEFAULT
	}
	return p.Basic
}

var InitSpaceRequest_Members_DEFAULT []*SpaceMember

func (p *InitSpaceRequest) GetMembers() (v []*SpaceMember) {
	if !p.IsSetMembers() {
		return InitSpaceRequest_Members_DEFAULT
	}
	return p.Members
}

var InitSpaceRequest_LarkDocConfig_DEFAULT *LarkDocConfig

func (p *InitSpaceRequest) GetLarkDocConfig() (v *LarkDocConfig) {
	if !p.IsSetLarkDocConfig() {
		return InitSpaceRequest_LarkDocConfig_DEFAULT
	}
	return p.LarkDocConfig
}

var InitSpaceRequest_Repos_DEFAULT []*CodeRepo

func (p *InitSpaceRequest) GetRepos() (v []*CodeRepo) {
	if !p.IsSetRepos() {
		return InitSpaceRequest_Repos_DEFAULT
	}
	return p.Repos
}

var InitSpaceRequest_Services_DEFAULT []*Service

func (p *InitSpaceRequest) GetServices() (v []*Service) {
	if !p.IsSetServices() {
		return InitSpaceRequest_Services_DEFAULT
	}
	return p.Services
}

var InitSpaceRequest_PlatformConfig_DEFAULT *PlatformConfig

func (p *InitSpaceRequest) GetPlatformConfig() (v *PlatformConfig) {
	if !p.IsSetPlatformConfig() {
		return InitSpaceRequest_PlatformConfig_DEFAULT
	}
	return p.PlatformConfig
}

func (p *InitSpaceRequest) IsSetBasic() bool {
	return p.Basic != nil
}

func (p *InitSpaceRequest) IsSetMembers() bool {
	return p.Members != nil
}

func (p *InitSpaceRequest) IsSetLarkDocConfig() bool {
	return p.LarkDocConfig != nil
}

func (p *InitSpaceRequest) IsSetRepos() bool {
	return p.Repos != nil
}

func (p *InitSpaceRequest) IsSetServices() bool {
	return p.Services != nil
}

func (p *InitSpaceRequest) IsSetPlatformConfig() bool {
	return p.PlatformConfig != nil
}

func (p *InitSpaceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InitSpaceRequest(%+v)", *p)
}

type InitSpaceResponse struct {
}

func NewInitSpaceResponse() *InitSpaceResponse {
	return &InitSpaceResponse{}
}

func (p *InitSpaceResponse) InitDefault() {
}

func (p *InitSpaceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InitSpaceResponse(%+v)", *p)
}

type BasicInfo struct {
	Name        *StringInMultiLang `thrift:"Name,1,required" json:"name"`
	Description *StringInMultiLang `thrift:"Description,2,optional" json:"description,omitempty"`
}

func NewBasicInfo() *BasicInfo {
	return &BasicInfo{}
}

func (p *BasicInfo) InitDefault() {
}

var BasicInfo_Name_DEFAULT *StringInMultiLang

func (p *BasicInfo) GetName() (v *StringInMultiLang) {
	if !p.IsSetName() {
		return BasicInfo_Name_DEFAULT
	}
	return p.Name
}

var BasicInfo_Description_DEFAULT *StringInMultiLang

func (p *BasicInfo) GetDescription() (v *StringInMultiLang) {
	if !p.IsSetDescription() {
		return BasicInfo_Description_DEFAULT
	}
	return p.Description
}

func (p *BasicInfo) IsSetName() bool {
	return p.Name != nil
}

func (p *BasicInfo) IsSetDescription() bool {
	return p.Description != nil
}

func (p *BasicInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BasicInfo(%+v)", *p)
}

type StringInMultiLang struct {
	Cn string `thrift:"Cn,1" json:"cn"`
	En string `thrift:"En,2" json:"en"`
}

func NewStringInMultiLang() *StringInMultiLang {
	return &StringInMultiLang{}
}

func (p *StringInMultiLang) InitDefault() {
}

func (p *StringInMultiLang) GetCn() (v string) {
	return p.Cn
}

func (p *StringInMultiLang) GetEn() (v string) {
	return p.En
}

func (p *StringInMultiLang) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StringInMultiLang(%+v)", *p)
}

type SpaceResourceIndex struct {
	ResourceID     string              `thrift:"ResourceID,1,required" json:"resource_id"`
	ResourceIDType SpaceResourceIDType `thrift:"ResourceIDType,2,required" json:"resource_id_type"`
}

func NewSpaceResourceIndex() *SpaceResourceIndex {
	return &SpaceResourceIndex{}
}

func (p *SpaceResourceIndex) InitDefault() {
}

func (p *SpaceResourceIndex) GetResourceID() (v string) {
	return p.ResourceID
}

func (p *SpaceResourceIndex) GetResourceIDType() (v SpaceResourceIDType) {
	return p.ResourceIDType
}

func (p *SpaceResourceIndex) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SpaceResourceIndex(%+v)", *p)
}

type ListSpaceResourcesRequest struct {
	SpaceID         string                `thrift:"SpaceID,1,required" json:"space_id"`
	ResourceIndexes []*SpaceResourceIndex `thrift:"ResourceIndexes,2,optional" json:"resource_indexes"`
}

func NewListSpaceResourcesRequest() *ListSpaceResourcesRequest {
	return &ListSpaceResourcesRequest{}
}

func (p *ListSpaceResourcesRequest) InitDefault() {
}

func (p *ListSpaceResourcesRequest) GetSpaceID() (v string) {
	return p.SpaceID
}

var ListSpaceResourcesRequest_ResourceIndexes_DEFAULT []*SpaceResourceIndex

func (p *ListSpaceResourcesRequest) GetResourceIndexes() (v []*SpaceResourceIndex) {
	if !p.IsSetResourceIndexes() {
		return ListSpaceResourcesRequest_ResourceIndexes_DEFAULT
	}
	return p.ResourceIndexes
}

func (p *ListSpaceResourcesRequest) IsSetResourceIndexes() bool {
	return p.ResourceIndexes != nil
}

func (p *ListSpaceResourcesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceResourcesRequest(%+v)", *p)
}

type ListSpaceResourcesResponse struct {
	Documents []*LarkDocument `thrift:"Documents,1,optional" json:"documents"`
	CodeRepos []*CodeRepo     `thrift:"CodeRepos,2,optional" json:"code_repos"`
}

func NewListSpaceResourcesResponse() *ListSpaceResourcesResponse {
	return &ListSpaceResourcesResponse{}
}

func (p *ListSpaceResourcesResponse) InitDefault() {
}

var ListSpaceResourcesResponse_Documents_DEFAULT []*LarkDocument

func (p *ListSpaceResourcesResponse) GetDocuments() (v []*LarkDocument) {
	if !p.IsSetDocuments() {
		return ListSpaceResourcesResponse_Documents_DEFAULT
	}
	return p.Documents
}

var ListSpaceResourcesResponse_CodeRepos_DEFAULT []*CodeRepo

func (p *ListSpaceResourcesResponse) GetCodeRepos() (v []*CodeRepo) {
	if !p.IsSetCodeRepos() {
		return ListSpaceResourcesResponse_CodeRepos_DEFAULT
	}
	return p.CodeRepos
}

func (p *ListSpaceResourcesResponse) IsSetDocuments() bool {
	return p.Documents != nil
}

func (p *ListSpaceResourcesResponse) IsSetCodeRepos() bool {
	return p.CodeRepos != nil
}

func (p *ListSpaceResourcesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListSpaceResourcesResponse(%+v)", *p)
}
