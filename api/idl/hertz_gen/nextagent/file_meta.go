// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

const (
	ArtifactTypeFile = "file"

	ArtifactTypeCode = "code"

	ArtifactTypeLink = "link"

	ArtifactTypeImage = "image"

	ArtifactTypeLogs = "logs"

	ArtifactTypeResult = "result"

	ArtifactTypeProject = "project"
)

type ArtifactType = string

type FilePatch struct {
	FilePath string `thrift:"FilePath,1" json:"file_path"`
	Patch    string `thrift:"Patch,2" json:"patch"`
}

func NewFilePatch() *FilePatch {
	return &FilePatch{}
}

func (p *FilePatch) InitDefault() {
}

func (p *FilePatch) GetFilePath() (v string) {
	return p.FilePath
}

func (p *FilePatch) GetPatch() (v string) {
	return p.Patch
}

func (p *FilePatch) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FilePatch(%+v)", *p)
}

type LinkContent struct {
	URL         string `thrift:"URL,1" json:"url"`
	Title       string `thrift:"Title,2" json:"title"`
	Description string `thrift:"Description,3" json:"description"`
}

func NewLinkContent() *LinkContent {
	return &LinkContent{}
}

func (p *LinkContent) InitDefault() {
}

func (p *LinkContent) GetURL() (v string) {
	return p.URL
}

func (p *LinkContent) GetTitle() (v string) {
	return p.Title
}

func (p *LinkContent) GetDescription() (v string) {
	return p.Description
}

func (p *LinkContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LinkContent(%+v)", *p)
}

type FileMeta struct {
	Name      string `thrift:"Name,1,required" json:"name"`
	Size      int64  `thrift:"Size,2,required" json:"size"`
	Content   string `thrift:"Content,3,required" json:"content"`
	LarkToken string `thrift:"LarkToken,4,required" json:"lark_token"`
	ImageXURI string `thrift:"ImageXURI,5,required" json:"imagex_uri"`
	// 目前一个 Artifact 可能会包含不同的业务类型的文件, 以该类型为准
	Type ArtifactType `thrift:"Type,6,required" json:"type"`
	// 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是Link，则对应的是 link 类型（参考 ArtifactLinkSubType）
	SubType         string       `thrift:"SubType,7,required" json:"sub_type"`
	LinkContent     *LinkContent `thrift:"LinkContent,8,optional" json:"link_content,omitempty"`
	PatchFileResult []*FilePatch `thrift:"PatchFileResult,9,optional" json:"patch_file_result,omitempty"`
	// 默认缩放比例的图片链接
	ImageXResizeURL     *string `thrift:"ImageXResizeURL,10,optional" json:"imagex_resize_url,omitempty"`
	LarkDocVersionToken *string `thrift:"LarkDocVersionToken,11,optional" json:"lark_doc_version_token"`
}

func NewFileMeta() *FileMeta {
	return &FileMeta{}
}

func (p *FileMeta) InitDefault() {
}

func (p *FileMeta) GetName() (v string) {
	return p.Name
}

func (p *FileMeta) GetSize() (v int64) {
	return p.Size
}

func (p *FileMeta) GetContent() (v string) {
	return p.Content
}

func (p *FileMeta) GetLarkToken() (v string) {
	return p.LarkToken
}

func (p *FileMeta) GetImageXURI() (v string) {
	return p.ImageXURI
}

func (p *FileMeta) GetType() (v ArtifactType) {
	return p.Type
}

func (p *FileMeta) GetSubType() (v string) {
	return p.SubType
}

var FileMeta_LinkContent_DEFAULT *LinkContent

func (p *FileMeta) GetLinkContent() (v *LinkContent) {
	if !p.IsSetLinkContent() {
		return FileMeta_LinkContent_DEFAULT
	}
	return p.LinkContent
}

var FileMeta_PatchFileResult_DEFAULT []*FilePatch

func (p *FileMeta) GetPatchFileResult() (v []*FilePatch) {
	if !p.IsSetPatchFileResult() {
		return FileMeta_PatchFileResult_DEFAULT
	}
	return p.PatchFileResult
}

var FileMeta_ImageXResizeURL_DEFAULT string

func (p *FileMeta) GetImageXResizeURL() (v string) {
	if !p.IsSetImageXResizeURL() {
		return FileMeta_ImageXResizeURL_DEFAULT
	}
	return *p.ImageXResizeURL
}

var FileMeta_LarkDocVersionToken_DEFAULT string

func (p *FileMeta) GetLarkDocVersionToken() (v string) {
	if !p.IsSetLarkDocVersionToken() {
		return FileMeta_LarkDocVersionToken_DEFAULT
	}
	return *p.LarkDocVersionToken
}

func (p *FileMeta) IsSetLinkContent() bool {
	return p.LinkContent != nil
}

func (p *FileMeta) IsSetPatchFileResult() bool {
	return p.PatchFileResult != nil
}

func (p *FileMeta) IsSetImageXResizeURL() bool {
	return p.ImageXResizeURL != nil
}

func (p *FileMeta) IsSetLarkDocVersionToken() bool {
	return p.LarkDocVersionToken != nil
}

func (p *FileMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileMeta(%+v)", *p)
}
