// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"fmt"
)

const (
	AgentConfigTypeUnknown = "unknown"

	AgentConfigTypeBase = "base"

	AgentConfigTypeFeature = "feature"

	AgentConfigTypeUser = "user"

	AgentConfigTypeAbTest = "abtest"

	AgentConfigStatusUnknown = "unknown"

	AgentConfigStatusCreated = "created"

	AgentConfigStatusOnline = "online"

	AgentConfigStatusCanary = "canary"

	RuntimeConfigTypeUnknown = "unknown"

	RuntimeConfigTypeStratoCube = "stratocube"

	RuntimeConfigTypeDocker = "docker"
)

type AgentConfigType = string

type AgentConfigStatus = string

type RuntimeConfigType = string

type Agent struct {
	ID          string `thrift:"ID,1,required" json:"id"`
	Name        string `thrift:"Name,2,required" json:"name"`
	Description string `thrift:"Description,3,required" json:"description"`
	Creator     string `thrift:"Creator,4,required" json:"creator"`
	CreatedAt   string `thrift:"CreatedAt,5,required" json:"created_at"`
	UpdatedAt   string `thrift:"UpdatedAt,6,required" json:"updated_at"`
}

func NewAgent() *Agent {
	return &Agent{}
}

func (p *Agent) InitDefault() {
}

func (p *Agent) GetID() (v string) {
	return p.ID
}

func (p *Agent) GetName() (v string) {
	return p.Name
}

func (p *Agent) GetDescription() (v string) {
	return p.Description
}

func (p *Agent) GetCreator() (v string) {
	return p.Creator
}

func (p *Agent) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Agent) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *Agent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Agent(%+v)", *p)
}

type AgentConfig struct {
	ID          string          `thrift:"ID,1,required" json:"id"`
	AgentID     string          `thrift:"AgentID,2,required" json:"agent_id"`
	Type        AgentConfigType `thrift:"Type,3,required" json:"type"`
	Name        string          `thrift:"Name,4,required" json:"name"`
	Description string          `thrift:"Description,5,required" json:"description"`
	Creator     string          `thrift:"Creator,6,required" json:"creator"`
	CreatedAt   string          `thrift:"CreatedAt,7,required" json:"created_at"`
	UpdatedAt   string          `thrift:"UpdatedAt,8,required" json:"updated_at"`
}

func NewAgentConfig() *AgentConfig {
	return &AgentConfig{}
}

func (p *AgentConfig) InitDefault() {
}

func (p *AgentConfig) GetID() (v string) {
	return p.ID
}

func (p *AgentConfig) GetAgentID() (v string) {
	return p.AgentID
}

func (p *AgentConfig) GetType() (v AgentConfigType) {
	return p.Type
}

func (p *AgentConfig) GetName() (v string) {
	return p.Name
}

func (p *AgentConfig) GetDescription() (v string) {
	return p.Description
}

func (p *AgentConfig) GetCreator() (v string) {
	return p.Creator
}

func (p *AgentConfig) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *AgentConfig) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *AgentConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentConfig(%+v)", *p)
}

type AgentConfigVersion struct {
	ID                   string                 `thrift:"ID,1,required" json:"id"`
	AgentConfigID        string                 `thrift:"AgentConfigID,2,required" json:"agent_config_id"`
	Description          string                 `thrift:"Description,3,required" json:"description"`
	Creator              string                 `thrift:"Creator,4,required" json:"creator"`
	Version              int32                  `thrift:"Version,5,required" json:"version"`
	Enabled              bool                   `thrift:"Enabled,6,required" json:"enabled"`
	Status               AgentConfigStatus      `thrift:"Status,7,required" json:"status"`
	RuntimeConfig        *RuntimeConfig         `thrift:"RuntimeConfig,8,required" json:"runtime_config"`
	CustomConfig         common.JsonVariables   `thrift:"CustomConfig,9,required" json:"custom_config"`
	PromptConfig         *PromptConfig          `thrift:"PromptConfig,10,required" json:"prompt_config"`
	CreatedAt            string                 `thrift:"CreatedAt,11,required" json:"created_at"`
	UpdatedAt            string                 `thrift:"UpdatedAt,12,required" json:"updated_at"`
	KnowledgesetConfig   *KnowledgesetConfig    `thrift:"KnowledgesetConfig,13,optional" json:"knowledgeset_config"`
	Knowledgesets        []*Knowledgeset        `thrift:"Knowledgesets,14,optional" json:"knowledge_set"`
	KnowledgesetVersions []*KnowledgesetVersion `thrift:"KnowledgesetVersions,15,optional" json:"knowledge_set_version"`
	DeployID             *string                `thrift:"DeployID,16,optional" json:"deploy_id"`
}

func NewAgentConfigVersion() *AgentConfigVersion {
	return &AgentConfigVersion{}
}

func (p *AgentConfigVersion) InitDefault() {
}

func (p *AgentConfigVersion) GetID() (v string) {
	return p.ID
}

func (p *AgentConfigVersion) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *AgentConfigVersion) GetDescription() (v string) {
	return p.Description
}

func (p *AgentConfigVersion) GetCreator() (v string) {
	return p.Creator
}

func (p *AgentConfigVersion) GetVersion() (v int32) {
	return p.Version
}

func (p *AgentConfigVersion) GetEnabled() (v bool) {
	return p.Enabled
}

func (p *AgentConfigVersion) GetStatus() (v AgentConfigStatus) {
	return p.Status
}

var AgentConfigVersion_RuntimeConfig_DEFAULT *RuntimeConfig

func (p *AgentConfigVersion) GetRuntimeConfig() (v *RuntimeConfig) {
	if !p.IsSetRuntimeConfig() {
		return AgentConfigVersion_RuntimeConfig_DEFAULT
	}
	return p.RuntimeConfig
}

func (p *AgentConfigVersion) GetCustomConfig() (v common.JsonVariables) {
	return p.CustomConfig
}

var AgentConfigVersion_PromptConfig_DEFAULT *PromptConfig

func (p *AgentConfigVersion) GetPromptConfig() (v *PromptConfig) {
	if !p.IsSetPromptConfig() {
		return AgentConfigVersion_PromptConfig_DEFAULT
	}
	return p.PromptConfig
}

func (p *AgentConfigVersion) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *AgentConfigVersion) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var AgentConfigVersion_KnowledgesetConfig_DEFAULT *KnowledgesetConfig

func (p *AgentConfigVersion) GetKnowledgesetConfig() (v *KnowledgesetConfig) {
	if !p.IsSetKnowledgesetConfig() {
		return AgentConfigVersion_KnowledgesetConfig_DEFAULT
	}
	return p.KnowledgesetConfig
}

var AgentConfigVersion_Knowledgesets_DEFAULT []*Knowledgeset

func (p *AgentConfigVersion) GetKnowledgesets() (v []*Knowledgeset) {
	if !p.IsSetKnowledgesets() {
		return AgentConfigVersion_Knowledgesets_DEFAULT
	}
	return p.Knowledgesets
}

var AgentConfigVersion_KnowledgesetVersions_DEFAULT []*KnowledgesetVersion

func (p *AgentConfigVersion) GetKnowledgesetVersions() (v []*KnowledgesetVersion) {
	if !p.IsSetKnowledgesetVersions() {
		return AgentConfigVersion_KnowledgesetVersions_DEFAULT
	}
	return p.KnowledgesetVersions
}

var AgentConfigVersion_DeployID_DEFAULT string

func (p *AgentConfigVersion) GetDeployID() (v string) {
	if !p.IsSetDeployID() {
		return AgentConfigVersion_DeployID_DEFAULT
	}
	return *p.DeployID
}

func (p *AgentConfigVersion) IsSetRuntimeConfig() bool {
	return p.RuntimeConfig != nil
}

func (p *AgentConfigVersion) IsSetPromptConfig() bool {
	return p.PromptConfig != nil
}

func (p *AgentConfigVersion) IsSetKnowledgesetConfig() bool {
	return p.KnowledgesetConfig != nil
}

func (p *AgentConfigVersion) IsSetKnowledgesets() bool {
	return p.Knowledgesets != nil
}

func (p *AgentConfigVersion) IsSetKnowledgesetVersions() bool {
	return p.KnowledgesetVersions != nil
}

func (p *AgentConfigVersion) IsSetDeployID() bool {
	return p.DeployID != nil
}

func (p *AgentConfigVersion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentConfigVersion(%+v)", *p)
}

type ResourceQuantity struct {
	Requests string  `thrift:"Requests,1,required" json:"requests"`
	Limits   *string `thrift:"Limits,2,optional" json:"limits"`
}

func NewResourceQuantity() *ResourceQuantity {
	return &ResourceQuantity{}
}

func (p *ResourceQuantity) InitDefault() {
}

func (p *ResourceQuantity) GetRequests() (v string) {
	return p.Requests
}

var ResourceQuantity_Limits_DEFAULT string

func (p *ResourceQuantity) GetLimits() (v string) {
	if !p.IsSetLimits() {
		return ResourceQuantity_Limits_DEFAULT
	}
	return *p.Limits
}

func (p *ResourceQuantity) IsSetLimits() bool {
	return p.Limits != nil
}

func (p *ResourceQuantity) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceQuantity(%+v)", *p)
}

type RuntimeResourceQuota struct {
	CPU              *ResourceQuantity `thrift:"CPU,1,optional" json:"cpu"`
	MEM              *ResourceQuantity `thrift:"MEM,2,optional" json:"mem"`
	PersistWorkspace *ResourceQuantity `thrift:"PersistWorkspace,3,optional" json:"persist_workspace"`
	StorageClass     *string           `thrift:"StorageClass,4,optional" json:"storage_class"`
}

func NewRuntimeResourceQuota() *RuntimeResourceQuota {
	return &RuntimeResourceQuota{}
}

func (p *RuntimeResourceQuota) InitDefault() {
}

var RuntimeResourceQuota_CPU_DEFAULT *ResourceQuantity

func (p *RuntimeResourceQuota) GetCPU() (v *ResourceQuantity) {
	if !p.IsSetCPU() {
		return RuntimeResourceQuota_CPU_DEFAULT
	}
	return p.CPU
}

var RuntimeResourceQuota_MEM_DEFAULT *ResourceQuantity

func (p *RuntimeResourceQuota) GetMEM() (v *ResourceQuantity) {
	if !p.IsSetMEM() {
		return RuntimeResourceQuota_MEM_DEFAULT
	}
	return p.MEM
}

var RuntimeResourceQuota_PersistWorkspace_DEFAULT *ResourceQuantity

func (p *RuntimeResourceQuota) GetPersistWorkspace() (v *ResourceQuantity) {
	if !p.IsSetPersistWorkspace() {
		return RuntimeResourceQuota_PersistWorkspace_DEFAULT
	}
	return p.PersistWorkspace
}

var RuntimeResourceQuota_StorageClass_DEFAULT string

func (p *RuntimeResourceQuota) GetStorageClass() (v string) {
	if !p.IsSetStorageClass() {
		return RuntimeResourceQuota_StorageClass_DEFAULT
	}
	return *p.StorageClass
}

func (p *RuntimeResourceQuota) IsSetCPU() bool {
	return p.CPU != nil
}

func (p *RuntimeResourceQuota) IsSetMEM() bool {
	return p.MEM != nil
}

func (p *RuntimeResourceQuota) IsSetPersistWorkspace() bool {
	return p.PersistWorkspace != nil
}

func (p *RuntimeResourceQuota) IsSetStorageClass() bool {
	return p.StorageClass != nil
}

func (p *RuntimeResourceQuota) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuntimeResourceQuota(%+v)", *p)
}

type OrchestrationConfig struct {
	// 0 不限制
	MaxConcurrency       int32  `thrift:"MaxConcurrency,1,required" json:"max_concurrency"`
	PoolSize             int32  `thrift:"PoolSize,2,required" json:"max_poolsize"`
	RuntimeStopTimeout   string `thrift:"RuntimeStopTimeout,3,required" json:"runtime_stop_timeout"`
	RuntimeDeleteTimeout string `thrift:"RuntimeDeleteTimeout,4,required" json:"runtime_delete_timeout"`
}

func NewOrchestrationConfig() *OrchestrationConfig {
	return &OrchestrationConfig{}
}

func (p *OrchestrationConfig) InitDefault() {
}

func (p *OrchestrationConfig) GetMaxConcurrency() (v int32) {
	return p.MaxConcurrency
}

func (p *OrchestrationConfig) GetPoolSize() (v int32) {
	return p.PoolSize
}

func (p *OrchestrationConfig) GetRuntimeStopTimeout() (v string) {
	return p.RuntimeStopTimeout
}

func (p *OrchestrationConfig) GetRuntimeDeleteTimeout() (v string) {
	return p.RuntimeDeleteTimeout
}

func (p *OrchestrationConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrchestrationConfig(%+v)", *p)
}

type RuntimeConfig struct {
	ID                  string                `thrift:"ID,1,required" json:"id"`
	PSM                 string                `thrift:"PSM,2,required" json:"psm"`
	Type                RuntimeConfigType     `thrift:"Type,3,required" json:"type"`
	InjectOpenAIToken   bool                  `thrift:"InjectOpenAIToken,4,required" json:"inject_openai_token"`
	Image               *string               `thrift:"Image,5,optional" json:"image"`
	Envs                map[string]string     `thrift:"Envs,6,optional" json:"envs"`
	Port                *int32                `thrift:"Port,7,optional" json:"port"`
	Quota               *RuntimeResourceQuota `thrift:"Quota,8,optional" json:"runtime_resource_quota"`
	BinarySource        *string               `thrift:"BinarySource,9,optional" json:"binary_source"`
	OrchestrationConfig *OrchestrationConfig  `thrift:"OrchestrationConfig,10,optional" json:"orchestration_config"`
	BashImage           *string               `thrift:"BashImage,11,optional" json:"bash_image"`
}

func NewRuntimeConfig() *RuntimeConfig {
	return &RuntimeConfig{}
}

func (p *RuntimeConfig) InitDefault() {
}

func (p *RuntimeConfig) GetID() (v string) {
	return p.ID
}

func (p *RuntimeConfig) GetPSM() (v string) {
	return p.PSM
}

func (p *RuntimeConfig) GetType() (v RuntimeConfigType) {
	return p.Type
}

func (p *RuntimeConfig) GetInjectOpenAIToken() (v bool) {
	return p.InjectOpenAIToken
}

var RuntimeConfig_Image_DEFAULT string

func (p *RuntimeConfig) GetImage() (v string) {
	if !p.IsSetImage() {
		return RuntimeConfig_Image_DEFAULT
	}
	return *p.Image
}

var RuntimeConfig_Envs_DEFAULT map[string]string

func (p *RuntimeConfig) GetEnvs() (v map[string]string) {
	if !p.IsSetEnvs() {
		return RuntimeConfig_Envs_DEFAULT
	}
	return p.Envs
}

var RuntimeConfig_Port_DEFAULT int32

func (p *RuntimeConfig) GetPort() (v int32) {
	if !p.IsSetPort() {
		return RuntimeConfig_Port_DEFAULT
	}
	return *p.Port
}

var RuntimeConfig_Quota_DEFAULT *RuntimeResourceQuota

func (p *RuntimeConfig) GetQuota() (v *RuntimeResourceQuota) {
	if !p.IsSetQuota() {
		return RuntimeConfig_Quota_DEFAULT
	}
	return p.Quota
}

var RuntimeConfig_BinarySource_DEFAULT string

func (p *RuntimeConfig) GetBinarySource() (v string) {
	if !p.IsSetBinarySource() {
		return RuntimeConfig_BinarySource_DEFAULT
	}
	return *p.BinarySource
}

var RuntimeConfig_OrchestrationConfig_DEFAULT *OrchestrationConfig

func (p *RuntimeConfig) GetOrchestrationConfig() (v *OrchestrationConfig) {
	if !p.IsSetOrchestrationConfig() {
		return RuntimeConfig_OrchestrationConfig_DEFAULT
	}
	return p.OrchestrationConfig
}

var RuntimeConfig_BashImage_DEFAULT string

func (p *RuntimeConfig) GetBashImage() (v string) {
	if !p.IsSetBashImage() {
		return RuntimeConfig_BashImage_DEFAULT
	}
	return *p.BashImage
}

func (p *RuntimeConfig) IsSetImage() bool {
	return p.Image != nil
}

func (p *RuntimeConfig) IsSetEnvs() bool {
	return p.Envs != nil
}

func (p *RuntimeConfig) IsSetPort() bool {
	return p.Port != nil
}

func (p *RuntimeConfig) IsSetQuota() bool {
	return p.Quota != nil
}

func (p *RuntimeConfig) IsSetBinarySource() bool {
	return p.BinarySource != nil
}

func (p *RuntimeConfig) IsSetOrchestrationConfig() bool {
	return p.OrchestrationConfig != nil
}

func (p *RuntimeConfig) IsSetBashImage() bool {
	return p.BashImage != nil
}

func (p *RuntimeConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuntimeConfig(%+v)", *p)
}

type PromptConfig struct {
	Prompts []*PromptConfigMetadata `thrift:"prompts,1,required" json:"prompts"`
}

func NewPromptConfig() *PromptConfig {
	return &PromptConfig{}
}

func (p *PromptConfig) InitDefault() {
}

func (p *PromptConfig) GetPrompts() (v []*PromptConfigMetadata) {
	return p.Prompts
}

func (p *PromptConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromptConfig(%+v)", *p)
}

type PromptConfigMetadata struct {
	Key     string `thrift:"Key,1,required" json:"key"`
	ID      string `thrift:"ID,2,required" json:"id"`
	Version int32  `thrift:"version,3,required" json:"version"`
}

func NewPromptConfigMetadata() *PromptConfigMetadata {
	return &PromptConfigMetadata{}
}

func (p *PromptConfigMetadata) InitDefault() {
}

func (p *PromptConfigMetadata) GetKey() (v string) {
	return p.Key
}

func (p *PromptConfigMetadata) GetID() (v string) {
	return p.ID
}

func (p *PromptConfigMetadata) GetVersion() (v int32) {
	return p.Version
}

func (p *PromptConfigMetadata) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromptConfigMetadata(%+v)", *p)
}

type KnowledgesetConfig struct {
	Knowledgesets []*KnowledgesetMetadata `thrift:"Knowledgesets,1,required" json:"knowledge_sets"`
}

func NewKnowledgesetConfig() *KnowledgesetConfig {
	return &KnowledgesetConfig{}
}

func (p *KnowledgesetConfig) InitDefault() {
}

func (p *KnowledgesetConfig) GetKnowledgesets() (v []*KnowledgesetMetadata) {
	return p.Knowledgesets
}

func (p *KnowledgesetConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KnowledgesetConfig(%+v)", *p)
}

type KnowledgesetMetadata struct {
	KnowledgesetID        string `thrift:"KnowledgesetID,1,required" json:"knowledge_set_id"`
	KnowledgesetVersionID string `thrift:"KnowledgesetVersionID,2,required" json:"knowledge_set_version_id"`
	KnowledgesetType      string `thrift:"KnowledgesetType,3,required" json:"knowledge_set_type"`
}

func NewKnowledgesetMetadata() *KnowledgesetMetadata {
	return &KnowledgesetMetadata{}
}

func (p *KnowledgesetMetadata) InitDefault() {
}

func (p *KnowledgesetMetadata) GetKnowledgesetID() (v string) {
	return p.KnowledgesetID
}

func (p *KnowledgesetMetadata) GetKnowledgesetVersionID() (v string) {
	return p.KnowledgesetVersionID
}

func (p *KnowledgesetMetadata) GetKnowledgesetType() (v string) {
	return p.KnowledgesetType
}

func (p *KnowledgesetMetadata) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KnowledgesetMetadata(%+v)", *p)
}

type CreateAgentRequest struct {
	Name        string `thrift:"Name,1,required" json:"name"`
	Description string `thrift:"Description,2,required" json:"description"`
}

func NewCreateAgentRequest() *CreateAgentRequest {
	return &CreateAgentRequest{}
}

func (p *CreateAgentRequest) InitDefault() {
}

func (p *CreateAgentRequest) GetName() (v string) {
	return p.Name
}

func (p *CreateAgentRequest) GetDescription() (v string) {
	return p.Description
}

func (p *CreateAgentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAgentRequest(%+v)", *p)
}

type CreateAgentResponse struct {
	Agent *Agent `thrift:"Agent,1,required" json:"agent"`
}

func NewCreateAgentResponse() *CreateAgentResponse {
	return &CreateAgentResponse{}
}

func (p *CreateAgentResponse) InitDefault() {
}

var CreateAgentResponse_Agent_DEFAULT *Agent

func (p *CreateAgentResponse) GetAgent() (v *Agent) {
	if !p.IsSetAgent() {
		return CreateAgentResponse_Agent_DEFAULT
	}
	return p.Agent
}

func (p *CreateAgentResponse) IsSetAgent() bool {
	return p.Agent != nil
}

func (p *CreateAgentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAgentResponse(%+v)", *p)
}

// 创建不同类型的配置
type CreateAgentConfigRequest struct {
	AgentID     string          `thrift:"AgentID,1,required" json:"agent_id"`
	Type        AgentConfigType `thrift:"Type,2,required" json:"type"`
	Name        string          `thrift:"Name,3,required" json:"name"`
	Description string          `thrift:"Description,4,required" json:"description"`
}

func NewCreateAgentConfigRequest() *CreateAgentConfigRequest {
	return &CreateAgentConfigRequest{}
}

func (p *CreateAgentConfigRequest) InitDefault() {
}

func (p *CreateAgentConfigRequest) GetAgentID() (v string) {
	return p.AgentID
}

func (p *CreateAgentConfigRequest) GetType() (v AgentConfigType) {
	return p.Type
}

func (p *CreateAgentConfigRequest) GetName() (v string) {
	return p.Name
}

func (p *CreateAgentConfigRequest) GetDescription() (v string) {
	return p.Description
}

func (p *CreateAgentConfigRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAgentConfigRequest(%+v)", *p)
}

type CreateAgentConfigResponse struct {
	AgentConfig *AgentConfig `thrift:"AgentConfig,1,required" "agent_config" json:"agent_config,required" `
}

func NewCreateAgentConfigResponse() *CreateAgentConfigResponse {
	return &CreateAgentConfigResponse{}
}

func (p *CreateAgentConfigResponse) InitDefault() {
}

var CreateAgentConfigResponse_AgentConfig_DEFAULT *AgentConfig

func (p *CreateAgentConfigResponse) GetAgentConfig() (v *AgentConfig) {
	if !p.IsSetAgentConfig() {
		return CreateAgentConfigResponse_AgentConfig_DEFAULT
	}
	return p.AgentConfig
}

func (p *CreateAgentConfigResponse) IsSetAgentConfig() bool {
	return p.AgentConfig != nil
}

func (p *CreateAgentConfigResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAgentConfigResponse(%+v)", *p)
}

// 创建配置版本
type CreateAgentConfigVersionRequest struct {
	AgentConfigID      string               `thrift:"AgentConfigID,1,required" json:"agent_config_id"`
	Description        string               `thrift:"Description,2,required" json:"description"`
	Enabled            bool                 `thrift:"Enabled,3,required" json:"enabled"`
	RuntimeConfig      *RuntimeConfig       `thrift:"RuntimeConfig,4,required" json:"runtime_config"`
	CustomConfig       common.JsonVariables `thrift:"CustomConfig,5,required" json:"custom_config"`
	PromptConfig       *PromptConfig        `thrift:"PromptConfig,6,required" json:"prompt_config"`
	KnowledgesetConfig *KnowledgesetConfig  `thrift:"KnowledgesetConfig,7,optional" json:"knowledge_set_config"`
}

func NewCreateAgentConfigVersionRequest() *CreateAgentConfigVersionRequest {
	return &CreateAgentConfigVersionRequest{}
}

func (p *CreateAgentConfigVersionRequest) InitDefault() {
}

func (p *CreateAgentConfigVersionRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *CreateAgentConfigVersionRequest) GetDescription() (v string) {
	return p.Description
}

func (p *CreateAgentConfigVersionRequest) GetEnabled() (v bool) {
	return p.Enabled
}

var CreateAgentConfigVersionRequest_RuntimeConfig_DEFAULT *RuntimeConfig

func (p *CreateAgentConfigVersionRequest) GetRuntimeConfig() (v *RuntimeConfig) {
	if !p.IsSetRuntimeConfig() {
		return CreateAgentConfigVersionRequest_RuntimeConfig_DEFAULT
	}
	return p.RuntimeConfig
}

func (p *CreateAgentConfigVersionRequest) GetCustomConfig() (v common.JsonVariables) {
	return p.CustomConfig
}

var CreateAgentConfigVersionRequest_PromptConfig_DEFAULT *PromptConfig

func (p *CreateAgentConfigVersionRequest) GetPromptConfig() (v *PromptConfig) {
	if !p.IsSetPromptConfig() {
		return CreateAgentConfigVersionRequest_PromptConfig_DEFAULT
	}
	return p.PromptConfig
}

var CreateAgentConfigVersionRequest_KnowledgesetConfig_DEFAULT *KnowledgesetConfig

func (p *CreateAgentConfigVersionRequest) GetKnowledgesetConfig() (v *KnowledgesetConfig) {
	if !p.IsSetKnowledgesetConfig() {
		return CreateAgentConfigVersionRequest_KnowledgesetConfig_DEFAULT
	}
	return p.KnowledgesetConfig
}

func (p *CreateAgentConfigVersionRequest) IsSetRuntimeConfig() bool {
	return p.RuntimeConfig != nil
}

func (p *CreateAgentConfigVersionRequest) IsSetPromptConfig() bool {
	return p.PromptConfig != nil
}

func (p *CreateAgentConfigVersionRequest) IsSetKnowledgesetConfig() bool {
	return p.KnowledgesetConfig != nil
}

func (p *CreateAgentConfigVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAgentConfigVersionRequest(%+v)", *p)
}

type CreateAgentConfigVersionResponse struct {
	AgentConfigVersion *AgentConfigVersion `thrift:"AgentConfigVersion,1,required" json:"agent_config_version"`
}

func NewCreateAgentConfigVersionResponse() *CreateAgentConfigVersionResponse {
	return &CreateAgentConfigVersionResponse{}
}

func (p *CreateAgentConfigVersionResponse) InitDefault() {
}

var CreateAgentConfigVersionResponse_AgentConfigVersion_DEFAULT *AgentConfigVersion

func (p *CreateAgentConfigVersionResponse) GetAgentConfigVersion() (v *AgentConfigVersion) {
	if !p.IsSetAgentConfigVersion() {
		return CreateAgentConfigVersionResponse_AgentConfigVersion_DEFAULT
	}
	return p.AgentConfigVersion
}

func (p *CreateAgentConfigVersionResponse) IsSetAgentConfigVersion() bool {
	return p.AgentConfigVersion != nil
}

func (p *CreateAgentConfigVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAgentConfigVersionResponse(%+v)", *p)
}

type UpdateAgentRequest struct {
	AgentID     string  `thrift:"AgentID,1,required" json:"agent_id,required" path:"agent_id,required"`
	Name        *string `thrift:"Name,2,optional" json:"name"`
	Description *string `thrift:"Description,3,optional" json:"description"`
}

func NewUpdateAgentRequest() *UpdateAgentRequest {
	return &UpdateAgentRequest{}
}

func (p *UpdateAgentRequest) InitDefault() {
}

func (p *UpdateAgentRequest) GetAgentID() (v string) {
	return p.AgentID
}

var UpdateAgentRequest_Name_DEFAULT string

func (p *UpdateAgentRequest) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateAgentRequest_Name_DEFAULT
	}
	return *p.Name
}

var UpdateAgentRequest_Description_DEFAULT string

func (p *UpdateAgentRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UpdateAgentRequest_Description_DEFAULT
	}
	return *p.Description
}

func (p *UpdateAgentRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateAgentRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UpdateAgentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAgentRequest(%+v)", *p)
}

type UpdateAgentResponse struct {
	Agent *Agent `thrift:"Agent,1,required" json:"agent"`
}

func NewUpdateAgentResponse() *UpdateAgentResponse {
	return &UpdateAgentResponse{}
}

func (p *UpdateAgentResponse) InitDefault() {
}

var UpdateAgentResponse_Agent_DEFAULT *Agent

func (p *UpdateAgentResponse) GetAgent() (v *Agent) {
	if !p.IsSetAgent() {
		return UpdateAgentResponse_Agent_DEFAULT
	}
	return p.Agent
}

func (p *UpdateAgentResponse) IsSetAgent() bool {
	return p.Agent != nil
}

func (p *UpdateAgentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAgentResponse(%+v)", *p)
}

type UpdateAgentConfigRequest struct {
	AgentConfigID string  `thrift:"AgentConfigID,1,required" json:"agent_config_id,required" path:"agent_config_id,required"`
	Name          *string `thrift:"Name,2,optional" json:"name"`
	Description   *string `thrift:"Description,3,optional" json:"description"`
}

func NewUpdateAgentConfigRequest() *UpdateAgentConfigRequest {
	return &UpdateAgentConfigRequest{}
}

func (p *UpdateAgentConfigRequest) InitDefault() {
}

func (p *UpdateAgentConfigRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

var UpdateAgentConfigRequest_Name_DEFAULT string

func (p *UpdateAgentConfigRequest) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateAgentConfigRequest_Name_DEFAULT
	}
	return *p.Name
}

var UpdateAgentConfigRequest_Description_DEFAULT string

func (p *UpdateAgentConfigRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UpdateAgentConfigRequest_Description_DEFAULT
	}
	return *p.Description
}

func (p *UpdateAgentConfigRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateAgentConfigRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UpdateAgentConfigRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAgentConfigRequest(%+v)", *p)
}

type UpdateAgentConfigResponse struct {
	AgentConfig *AgentConfig `thrift:"AgentConfig,1,required" "agent_config" json:"agent_config,required" `
}

func NewUpdateAgentConfigResponse() *UpdateAgentConfigResponse {
	return &UpdateAgentConfigResponse{}
}

func (p *UpdateAgentConfigResponse) InitDefault() {
}

var UpdateAgentConfigResponse_AgentConfig_DEFAULT *AgentConfig

func (p *UpdateAgentConfigResponse) GetAgentConfig() (v *AgentConfig) {
	if !p.IsSetAgentConfig() {
		return UpdateAgentConfigResponse_AgentConfig_DEFAULT
	}
	return p.AgentConfig
}

func (p *UpdateAgentConfigResponse) IsSetAgentConfig() bool {
	return p.AgentConfig != nil
}

func (p *UpdateAgentConfigResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAgentConfigResponse(%+v)", *p)
}

// Base 版本只支持更新 enabled or description or name 其他都是创建新版本，其他类型支持直接更新，不需要创建新版
type UpdateAgentConfigVersionRequest struct {
	AgentConfigVersionID string                `thrift:"AgentConfigVersionID,1,required" json:"agent_config_version_id,required" path:"agent_config_version_id,required"`
	Enabled              *bool                 `thrift:"Enabled,2,optional" json:"enabled"`
	Description          *string               `thrift:"Description,3,optional" json:"description"`
	RuntimeConfig        *RuntimeConfig        `thrift:"RuntimeConfig,4,optional" json:"runtime_config"`
	CustomConfig         *common.JsonVariables `thrift:"CustomConfig,5,optional" json:"custom_config"`
	PromptConfig         *PromptConfig         `thrift:"PromptConfig,6,optional" json:"prompt_config"`
	KnowledgesetConfig   *KnowledgesetConfig   `thrift:"KnowledgesetConfig,7,optional" json:"knowledge_set_config"`
	UpdatedAt            *string               `thrift:"UpdatedAt,8,optional" json:"updated_at"`
}

func NewUpdateAgentConfigVersionRequest() *UpdateAgentConfigVersionRequest {
	return &UpdateAgentConfigVersionRequest{}
}

func (p *UpdateAgentConfigVersionRequest) InitDefault() {
}

func (p *UpdateAgentConfigVersionRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

var UpdateAgentConfigVersionRequest_Enabled_DEFAULT bool

func (p *UpdateAgentConfigVersionRequest) GetEnabled() (v bool) {
	if !p.IsSetEnabled() {
		return UpdateAgentConfigVersionRequest_Enabled_DEFAULT
	}
	return *p.Enabled
}

var UpdateAgentConfigVersionRequest_Description_DEFAULT string

func (p *UpdateAgentConfigVersionRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UpdateAgentConfigVersionRequest_Description_DEFAULT
	}
	return *p.Description
}

var UpdateAgentConfigVersionRequest_RuntimeConfig_DEFAULT *RuntimeConfig

func (p *UpdateAgentConfigVersionRequest) GetRuntimeConfig() (v *RuntimeConfig) {
	if !p.IsSetRuntimeConfig() {
		return UpdateAgentConfigVersionRequest_RuntimeConfig_DEFAULT
	}
	return p.RuntimeConfig
}

var UpdateAgentConfigVersionRequest_CustomConfig_DEFAULT common.JsonVariables

func (p *UpdateAgentConfigVersionRequest) GetCustomConfig() (v common.JsonVariables) {
	if !p.IsSetCustomConfig() {
		return UpdateAgentConfigVersionRequest_CustomConfig_DEFAULT
	}
	return *p.CustomConfig
}

var UpdateAgentConfigVersionRequest_PromptConfig_DEFAULT *PromptConfig

func (p *UpdateAgentConfigVersionRequest) GetPromptConfig() (v *PromptConfig) {
	if !p.IsSetPromptConfig() {
		return UpdateAgentConfigVersionRequest_PromptConfig_DEFAULT
	}
	return p.PromptConfig
}

var UpdateAgentConfigVersionRequest_KnowledgesetConfig_DEFAULT *KnowledgesetConfig

func (p *UpdateAgentConfigVersionRequest) GetKnowledgesetConfig() (v *KnowledgesetConfig) {
	if !p.IsSetKnowledgesetConfig() {
		return UpdateAgentConfigVersionRequest_KnowledgesetConfig_DEFAULT
	}
	return p.KnowledgesetConfig
}

var UpdateAgentConfigVersionRequest_UpdatedAt_DEFAULT string

func (p *UpdateAgentConfigVersionRequest) GetUpdatedAt() (v string) {
	if !p.IsSetUpdatedAt() {
		return UpdateAgentConfigVersionRequest_UpdatedAt_DEFAULT
	}
	return *p.UpdatedAt
}

func (p *UpdateAgentConfigVersionRequest) IsSetEnabled() bool {
	return p.Enabled != nil
}

func (p *UpdateAgentConfigVersionRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UpdateAgentConfigVersionRequest) IsSetRuntimeConfig() bool {
	return p.RuntimeConfig != nil
}

func (p *UpdateAgentConfigVersionRequest) IsSetCustomConfig() bool {
	return p.CustomConfig != nil
}

func (p *UpdateAgentConfigVersionRequest) IsSetPromptConfig() bool {
	return p.PromptConfig != nil
}

func (p *UpdateAgentConfigVersionRequest) IsSetKnowledgesetConfig() bool {
	return p.KnowledgesetConfig != nil
}

func (p *UpdateAgentConfigVersionRequest) IsSetUpdatedAt() bool {
	return p.UpdatedAt != nil
}

func (p *UpdateAgentConfigVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAgentConfigVersionRequest(%+v)", *p)
}

type UpdateAgentConfigVersionResponse struct {
	AgentConfigVersion *AgentConfigVersion `thrift:"AgentConfigVersion,1,required" json:"agent_config_version"`
}

func NewUpdateAgentConfigVersionResponse() *UpdateAgentConfigVersionResponse {
	return &UpdateAgentConfigVersionResponse{}
}

func (p *UpdateAgentConfigVersionResponse) InitDefault() {
}

var UpdateAgentConfigVersionResponse_AgentConfigVersion_DEFAULT *AgentConfigVersion

func (p *UpdateAgentConfigVersionResponse) GetAgentConfigVersion() (v *AgentConfigVersion) {
	if !p.IsSetAgentConfigVersion() {
		return UpdateAgentConfigVersionResponse_AgentConfigVersion_DEFAULT
	}
	return p.AgentConfigVersion
}

func (p *UpdateAgentConfigVersionResponse) IsSetAgentConfigVersion() bool {
	return p.AgentConfigVersion != nil
}

func (p *UpdateAgentConfigVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAgentConfigVersionResponse(%+v)", *p)
}

// TODO: 需要有个百分比的概念, 待设计，可能会复用 TCC
type DeployAgentConfigVersionRequest struct {
	AgentConfigVersionID string            `thrift:"AgentConfigVersionID,1,required" json:"agent_config_version_id,required" path:"agent_config_version_id,required"`
	Status               AgentConfigStatus `thrift:"Status,2,required" json:"status"`
}

func NewDeployAgentConfigVersionRequest() *DeployAgentConfigVersionRequest {
	return &DeployAgentConfigVersionRequest{}
}

func (p *DeployAgentConfigVersionRequest) InitDefault() {
}

func (p *DeployAgentConfigVersionRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *DeployAgentConfigVersionRequest) GetStatus() (v AgentConfigStatus) {
	return p.Status
}

func (p *DeployAgentConfigVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeployAgentConfigVersionRequest(%+v)", *p)
}

type DeployAgentConfigVersionResponse struct {
	AgentConfigVersion *AgentConfigVersion `thrift:"AgentConfigVersion,1,required" json:"agent_config_version"`
}

func NewDeployAgentConfigVersionResponse() *DeployAgentConfigVersionResponse {
	return &DeployAgentConfigVersionResponse{}
}

func (p *DeployAgentConfigVersionResponse) InitDefault() {
}

var DeployAgentConfigVersionResponse_AgentConfigVersion_DEFAULT *AgentConfigVersion

func (p *DeployAgentConfigVersionResponse) GetAgentConfigVersion() (v *AgentConfigVersion) {
	if !p.IsSetAgentConfigVersion() {
		return DeployAgentConfigVersionResponse_AgentConfigVersion_DEFAULT
	}
	return p.AgentConfigVersion
}

func (p *DeployAgentConfigVersionResponse) IsSetAgentConfigVersion() bool {
	return p.AgentConfigVersion != nil
}

func (p *DeployAgentConfigVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeployAgentConfigVersionResponse(%+v)", *p)
}

type DeleteAgentRequest struct {
	AgentID string `thrift:"AgentID,1,required" json:"agent_id,required" path:"agent_id,required"`
}

func NewDeleteAgentRequest() *DeleteAgentRequest {
	return &DeleteAgentRequest{}
}

func (p *DeleteAgentRequest) InitDefault() {
}

func (p *DeleteAgentRequest) GetAgentID() (v string) {
	return p.AgentID
}

func (p *DeleteAgentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAgentRequest(%+v)", *p)
}

type DeleteAgentResponse struct {
}

func NewDeleteAgentResponse() *DeleteAgentResponse {
	return &DeleteAgentResponse{}
}

func (p *DeleteAgentResponse) InitDefault() {
}

func (p *DeleteAgentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAgentResponse(%+v)", *p)
}

type DeleteAgentConfigRequest struct {
	AgentConfigID string `thrift:"AgentConfigID,1,required" json:"agent_config_id,required" path:"agent_config_id,required"`
}

func NewDeleteAgentConfigRequest() *DeleteAgentConfigRequest {
	return &DeleteAgentConfigRequest{}
}

func (p *DeleteAgentConfigRequest) InitDefault() {
}

func (p *DeleteAgentConfigRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *DeleteAgentConfigRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAgentConfigRequest(%+v)", *p)
}

type DeleteAgentConfigResponse struct {
}

func NewDeleteAgentConfigResponse() *DeleteAgentConfigResponse {
	return &DeleteAgentConfigResponse{}
}

func (p *DeleteAgentConfigResponse) InitDefault() {
}

func (p *DeleteAgentConfigResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAgentConfigResponse(%+v)", *p)
}

type GetAgentRequest struct {
	AgentID string `thrift:"AgentID,1,required" json:"agent_id,required" path:"agent_id,required"`
}

func NewGetAgentRequest() *GetAgentRequest {
	return &GetAgentRequest{}
}

func (p *GetAgentRequest) InitDefault() {
}

func (p *GetAgentRequest) GetAgentID() (v string) {
	return p.AgentID
}

func (p *GetAgentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentRequest(%+v)", *p)
}

type GetAgentResponse struct {
	Agent *Agent `thrift:"Agent,1,required" json:"agent"`
}

func NewGetAgentResponse() *GetAgentResponse {
	return &GetAgentResponse{}
}

func (p *GetAgentResponse) InitDefault() {
}

var GetAgentResponse_Agent_DEFAULT *Agent

func (p *GetAgentResponse) GetAgent() (v *Agent) {
	if !p.IsSetAgent() {
		return GetAgentResponse_Agent_DEFAULT
	}
	return p.Agent
}

func (p *GetAgentResponse) IsSetAgent() bool {
	return p.Agent != nil
}

func (p *GetAgentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentResponse(%+v)", *p)
}

type ListAgentsRequest struct {
	Creator *string `thrift:"Creator,1,optional" json:"creator,omitempty" query:"creator"`
	// 根据名字进行匹配
	Name     *string `thrift:"Name,2,optional" json:"name,omitempty" query:"name"`
	PageNum  int64   `thrift:"PageNum,3,required" json:"page_num,required" query:"page_num,required"`
	PageSize int64   `thrift:"PageSize,4,required" json:"page_size,required" query:"page_size,required"`
}

func NewListAgentsRequest() *ListAgentsRequest {
	return &ListAgentsRequest{}
}

func (p *ListAgentsRequest) InitDefault() {
}

var ListAgentsRequest_Creator_DEFAULT string

func (p *ListAgentsRequest) GetCreator() (v string) {
	if !p.IsSetCreator() {
		return ListAgentsRequest_Creator_DEFAULT
	}
	return *p.Creator
}

var ListAgentsRequest_Name_DEFAULT string

func (p *ListAgentsRequest) GetName() (v string) {
	if !p.IsSetName() {
		return ListAgentsRequest_Name_DEFAULT
	}
	return *p.Name
}

func (p *ListAgentsRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListAgentsRequest) GetPageSize() (v int64) {
	return p.PageSize
}

func (p *ListAgentsRequest) IsSetCreator() bool {
	return p.Creator != nil
}

func (p *ListAgentsRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *ListAgentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAgentsRequest(%+v)", *p)
}

type ListAgentsResponse struct {
	Agents []*Agent `thrift:"Agents,1,required" json:"agents"`
	Total  int64    `thrift:"Total,2,required" json:"total"`
}

func NewListAgentsResponse() *ListAgentsResponse {
	return &ListAgentsResponse{}
}

func (p *ListAgentsResponse) InitDefault() {
}

func (p *ListAgentsResponse) GetAgents() (v []*Agent) {
	return p.Agents
}

func (p *ListAgentsResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListAgentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAgentsResponse(%+v)", *p)
}

type GetAgentConfigRequest struct {
	AgentConfigID string `thrift:"AgentConfigID,1,required" json:"agent_config_id,required" path:"agent_config_id,required"`
	// 获取指定版本的具体配置内容
	Version *int32 `thrift:"Version,2,optional" json:"version,omitempty" query:"version"`
	// 获取最新版本，如果 Version 不为空则以 Version 为准, 如果都为空，则不返回具体配置
	Latest *bool `thrift:"Latest,3,optional" json:"latest,omitempty" query:"latest"`
	// 获取 Online 状态的具体配置内容
	Online *bool `thrift:"Online,4,optional" json:"online,omitempty" query:"online"`
}

func NewGetAgentConfigRequest() *GetAgentConfigRequest {
	return &GetAgentConfigRequest{}
}

func (p *GetAgentConfigRequest) InitDefault() {
}

func (p *GetAgentConfigRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

var GetAgentConfigRequest_Version_DEFAULT int32

func (p *GetAgentConfigRequest) GetVersion() (v int32) {
	if !p.IsSetVersion() {
		return GetAgentConfigRequest_Version_DEFAULT
	}
	return *p.Version
}

var GetAgentConfigRequest_Latest_DEFAULT bool

func (p *GetAgentConfigRequest) GetLatest() (v bool) {
	if !p.IsSetLatest() {
		return GetAgentConfigRequest_Latest_DEFAULT
	}
	return *p.Latest
}

var GetAgentConfigRequest_Online_DEFAULT bool

func (p *GetAgentConfigRequest) GetOnline() (v bool) {
	if !p.IsSetOnline() {
		return GetAgentConfigRequest_Online_DEFAULT
	}
	return *p.Online
}

func (p *GetAgentConfigRequest) IsSetVersion() bool {
	return p.Version != nil
}

func (p *GetAgentConfigRequest) IsSetLatest() bool {
	return p.Latest != nil
}

func (p *GetAgentConfigRequest) IsSetOnline() bool {
	return p.Online != nil
}

func (p *GetAgentConfigRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentConfigRequest(%+v)", *p)
}

type GetAgentConfigResponse struct {
	AgentConfig        *AgentConfig        `thrift:"AgentConfig,1,required" json:"agent_config"`
	AgentConfigVersion *AgentConfigVersion `thrift:"AgentConfigVersion,2,optional" json:"agent_config_version"`
}

func NewGetAgentConfigResponse() *GetAgentConfigResponse {
	return &GetAgentConfigResponse{}
}

func (p *GetAgentConfigResponse) InitDefault() {
}

var GetAgentConfigResponse_AgentConfig_DEFAULT *AgentConfig

func (p *GetAgentConfigResponse) GetAgentConfig() (v *AgentConfig) {
	if !p.IsSetAgentConfig() {
		return GetAgentConfigResponse_AgentConfig_DEFAULT
	}
	return p.AgentConfig
}

var GetAgentConfigResponse_AgentConfigVersion_DEFAULT *AgentConfigVersion

func (p *GetAgentConfigResponse) GetAgentConfigVersion() (v *AgentConfigVersion) {
	if !p.IsSetAgentConfigVersion() {
		return GetAgentConfigResponse_AgentConfigVersion_DEFAULT
	}
	return p.AgentConfigVersion
}

func (p *GetAgentConfigResponse) IsSetAgentConfig() bool {
	return p.AgentConfig != nil
}

func (p *GetAgentConfigResponse) IsSetAgentConfigVersion() bool {
	return p.AgentConfigVersion != nil
}

func (p *GetAgentConfigResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentConfigResponse(%+v)", *p)
}

type ListAgentConfigsRequest struct {
	AgentID *string `thrift:"AgentID,1,optional" json:"agent_id,omitempty" query:"agent_id"`
	// 支持一组 Type 用逗号间隔
	Type     *string `thrift:"Type,2,optional" json:"type,omitempty" query:"type"`
	Name     *string `thrift:"Name,3,optional" json:"name,omitempty" query:"name"`
	Creator  *string `thrift:"Creator,4,optional" json:"creator,omitempty" query:"creator"`
	PageNum  int64   `thrift:"PageNum,5,required" json:"page_num,required" query:"page_num,required"`
	PageSize int64   `thrift:"PageSize,6,required" json:"page_size,required" query:"page_size,required"`
}

func NewListAgentConfigsRequest() *ListAgentConfigsRequest {
	return &ListAgentConfigsRequest{}
}

func (p *ListAgentConfigsRequest) InitDefault() {
}

var ListAgentConfigsRequest_AgentID_DEFAULT string

func (p *ListAgentConfigsRequest) GetAgentID() (v string) {
	if !p.IsSetAgentID() {
		return ListAgentConfigsRequest_AgentID_DEFAULT
	}
	return *p.AgentID
}

var ListAgentConfigsRequest_Type_DEFAULT string

func (p *ListAgentConfigsRequest) GetType() (v string) {
	if !p.IsSetType() {
		return ListAgentConfigsRequest_Type_DEFAULT
	}
	return *p.Type
}

var ListAgentConfigsRequest_Name_DEFAULT string

func (p *ListAgentConfigsRequest) GetName() (v string) {
	if !p.IsSetName() {
		return ListAgentConfigsRequest_Name_DEFAULT
	}
	return *p.Name
}

var ListAgentConfigsRequest_Creator_DEFAULT string

func (p *ListAgentConfigsRequest) GetCreator() (v string) {
	if !p.IsSetCreator() {
		return ListAgentConfigsRequest_Creator_DEFAULT
	}
	return *p.Creator
}

func (p *ListAgentConfigsRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListAgentConfigsRequest) GetPageSize() (v int64) {
	return p.PageSize
}

func (p *ListAgentConfigsRequest) IsSetAgentID() bool {
	return p.AgentID != nil
}

func (p *ListAgentConfigsRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *ListAgentConfigsRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *ListAgentConfigsRequest) IsSetCreator() bool {
	return p.Creator != nil
}

func (p *ListAgentConfigsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAgentConfigsRequest(%+v)", *p)
}

type ListAgentConfigsResponse struct {
	Agent        *Agent         `thrift:"Agent,1,required" json:"agent"`
	AgentConfigs []*AgentConfig `thrift:"AgentConfigs,2,required" json:"agent_configs"`
	Total        int64          `thrift:"Total,3,required" json:"total"`
}

func NewListAgentConfigsResponse() *ListAgentConfigsResponse {
	return &ListAgentConfigsResponse{}
}

func (p *ListAgentConfigsResponse) InitDefault() {
}

var ListAgentConfigsResponse_Agent_DEFAULT *Agent

func (p *ListAgentConfigsResponse) GetAgent() (v *Agent) {
	if !p.IsSetAgent() {
		return ListAgentConfigsResponse_Agent_DEFAULT
	}
	return p.Agent
}

func (p *ListAgentConfigsResponse) GetAgentConfigs() (v []*AgentConfig) {
	return p.AgentConfigs
}

func (p *ListAgentConfigsResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListAgentConfigsResponse) IsSetAgent() bool {
	return p.Agent != nil
}

func (p *ListAgentConfigsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAgentConfigsResponse(%+v)", *p)
}

type ListAgentConfigVersionsRequest struct {
	AgentConfigID string  `thrift:"AgentConfigID,1,required" json:"agent_config_id,required" query:"agent_config_id,required"`
	Creator       *string `thrift:"Creator,2,optional" json:"creator,omitempty" query:"creator"`
	Enabled       *bool   `thrift:"Enabled,3,optional" json:"enabled,omitempty" query:"enabled"`
	PageNum       int64   `thrift:"PageNum,4,required" json:"page_num,required" query:"page_num,required"`
	PageSize      int64   `thrift:"PageSize,5,required" json:"page_size,required" query:"page_size,required"`
	// 支持一组 Status 用逗号间隔
	Status *string `thrift:"Status,6,optional" json:"status,omitempty" query:"status"`
}

func NewListAgentConfigVersionsRequest() *ListAgentConfigVersionsRequest {
	return &ListAgentConfigVersionsRequest{}
}

func (p *ListAgentConfigVersionsRequest) InitDefault() {
}

func (p *ListAgentConfigVersionsRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

var ListAgentConfigVersionsRequest_Creator_DEFAULT string

func (p *ListAgentConfigVersionsRequest) GetCreator() (v string) {
	if !p.IsSetCreator() {
		return ListAgentConfigVersionsRequest_Creator_DEFAULT
	}
	return *p.Creator
}

var ListAgentConfigVersionsRequest_Enabled_DEFAULT bool

func (p *ListAgentConfigVersionsRequest) GetEnabled() (v bool) {
	if !p.IsSetEnabled() {
		return ListAgentConfigVersionsRequest_Enabled_DEFAULT
	}
	return *p.Enabled
}

func (p *ListAgentConfigVersionsRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListAgentConfigVersionsRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListAgentConfigVersionsRequest_Status_DEFAULT string

func (p *ListAgentConfigVersionsRequest) GetStatus() (v string) {
	if !p.IsSetStatus() {
		return ListAgentConfigVersionsRequest_Status_DEFAULT
	}
	return *p.Status
}

func (p *ListAgentConfigVersionsRequest) IsSetCreator() bool {
	return p.Creator != nil
}

func (p *ListAgentConfigVersionsRequest) IsSetEnabled() bool {
	return p.Enabled != nil
}

func (p *ListAgentConfigVersionsRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *ListAgentConfigVersionsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAgentConfigVersionsRequest(%+v)", *p)
}

type ListAgentConfigVersionsResponse struct {
	AgentConfig         *AgentConfig          `thrift:"AgentConfig,1,required" json:"agent_config"`
	AgentConfigVersions []*AgentConfigVersion `thrift:"AgentConfigVersions,2,required" json:"agent_config_versions"`
	Total               int64                 `thrift:"Total,3,required" json:"total"`
}

func NewListAgentConfigVersionsResponse() *ListAgentConfigVersionsResponse {
	return &ListAgentConfigVersionsResponse{}
}

func (p *ListAgentConfigVersionsResponse) InitDefault() {
}

var ListAgentConfigVersionsResponse_AgentConfig_DEFAULT *AgentConfig

func (p *ListAgentConfigVersionsResponse) GetAgentConfig() (v *AgentConfig) {
	if !p.IsSetAgentConfig() {
		return ListAgentConfigVersionsResponse_AgentConfig_DEFAULT
	}
	return p.AgentConfig
}

func (p *ListAgentConfigVersionsResponse) GetAgentConfigVersions() (v []*AgentConfigVersion) {
	return p.AgentConfigVersions
}

func (p *ListAgentConfigVersionsResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListAgentConfigVersionsResponse) IsSetAgentConfig() bool {
	return p.AgentConfig != nil
}

func (p *ListAgentConfigVersionsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAgentConfigVersionsResponse(%+v)", *p)
}

type GetAgentConfigVersionRequest struct {
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,1,required" json:"agent_config_version_id,required" path:"agent_config_version_id,required"`
}

func NewGetAgentConfigVersionRequest() *GetAgentConfigVersionRequest {
	return &GetAgentConfigVersionRequest{}
}

func (p *GetAgentConfigVersionRequest) InitDefault() {
}

func (p *GetAgentConfigVersionRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *GetAgentConfigVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentConfigVersionRequest(%+v)", *p)
}

type GetAgentConfigVersionResponse struct {
	AgentConfigVersion *AgentConfigVersion `thrift:"AgentConfigVersion,1,required" json:"agent_config_version"`
}

func NewGetAgentConfigVersionResponse() *GetAgentConfigVersionResponse {
	return &GetAgentConfigVersionResponse{}
}

func (p *GetAgentConfigVersionResponse) InitDefault() {
}

var GetAgentConfigVersionResponse_AgentConfigVersion_DEFAULT *AgentConfigVersion

func (p *GetAgentConfigVersionResponse) GetAgentConfigVersion() (v *AgentConfigVersion) {
	if !p.IsSetAgentConfigVersion() {
		return GetAgentConfigVersionResponse_AgentConfigVersion_DEFAULT
	}
	return p.AgentConfigVersion
}

func (p *GetAgentConfigVersionResponse) IsSetAgentConfigVersion() bool {
	return p.AgentConfigVersion != nil
}

func (p *GetAgentConfigVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentConfigVersionResponse(%+v)", *p)
}

type CopyAgentConfigVersionRequest struct {
	SourceID      string  `thrift:"SourceID,1,required" json:"source_id"`
	AgentConfigID string  `thrift:"AgentConfigID,2,required" json:"agent_config_id"`
	Description   *string `thrift:"Description,3,optional" json:"description"`
}

func NewCopyAgentConfigVersionRequest() *CopyAgentConfigVersionRequest {
	return &CopyAgentConfigVersionRequest{}
}

func (p *CopyAgentConfigVersionRequest) InitDefault() {
}

func (p *CopyAgentConfigVersionRequest) GetSourceID() (v string) {
	return p.SourceID
}

func (p *CopyAgentConfigVersionRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

var CopyAgentConfigVersionRequest_Description_DEFAULT string

func (p *CopyAgentConfigVersionRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return CopyAgentConfigVersionRequest_Description_DEFAULT
	}
	return *p.Description
}

func (p *CopyAgentConfigVersionRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *CopyAgentConfigVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopyAgentConfigVersionRequest(%+v)", *p)
}

type CopyAgentConfigVersionResponse struct {
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,1,required" json:"agent_config_version_id"`
}

func NewCopyAgentConfigVersionResponse() *CopyAgentConfigVersionResponse {
	return &CopyAgentConfigVersionResponse{}
}

func (p *CopyAgentConfigVersionResponse) InitDefault() {
}

func (p *CopyAgentConfigVersionResponse) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *CopyAgentConfigVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CopyAgentConfigVersionResponse(%+v)", *p)
}

type AddAgentPermissionRequest struct {
	AgentID    string         `thrift:"AgentID,1,required" json:"agent_id,required" path:"agent_id,required"`
	Type       PermissionType `thrift:"Type,2,required" json:"type"`
	ExternalID string         `thrift:"ExternalID,3,required" json:"external_id"`
	Role       PermissionRole `thrift:"Role,4,required" json:"role"`
}

func NewAddAgentPermissionRequest() *AddAgentPermissionRequest {
	return &AddAgentPermissionRequest{}
}

func (p *AddAgentPermissionRequest) InitDefault() {
}

func (p *AddAgentPermissionRequest) GetAgentID() (v string) {
	return p.AgentID
}

func (p *AddAgentPermissionRequest) GetType() (v PermissionType) {
	return p.Type
}

func (p *AddAgentPermissionRequest) GetExternalID() (v string) {
	return p.ExternalID
}

func (p *AddAgentPermissionRequest) GetRole() (v PermissionRole) {
	return p.Role
}

func (p *AddAgentPermissionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAgentPermissionRequest(%+v)", *p)
}

type AddAgentPermissionResponse struct {
	Resource *Resource `thrift:"Resource,1,required" json:"resource"`
}

func NewAddAgentPermissionResponse() *AddAgentPermissionResponse {
	return &AddAgentPermissionResponse{}
}

func (p *AddAgentPermissionResponse) InitDefault() {
}

var AddAgentPermissionResponse_Resource_DEFAULT *Resource

func (p *AddAgentPermissionResponse) GetResource() (v *Resource) {
	if !p.IsSetResource() {
		return AddAgentPermissionResponse_Resource_DEFAULT
	}
	return p.Resource
}

func (p *AddAgentPermissionResponse) IsSetResource() bool {
	return p.Resource != nil
}

func (p *AddAgentPermissionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAgentPermissionResponse(%+v)", *p)
}
