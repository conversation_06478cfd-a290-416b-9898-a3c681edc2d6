// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

const (
	LocaleTypeUnknown = "unknown"

	LocaleTypeZH = "zh"

	LocaleTypeEN = "en"
)

type LocaleType = string

type OpenAPICreateTaskRequet struct {
	Role       SessionRole        `thrift:"Role,1,required" json:"role"`
	TemplateID string             `thrift:"TemplateID,2,required" json:"template_id"`
	FormValue  *TemplateFormValue `thrift:"FormValue,3,optional" json:"form_value"`
	// 任务配置，比如是否通知到用户
	Config *TaskConfig `thrift:"Config,4,optional" json:"taks_config"`
	// 指定使用的语言等扩展参数
	Options *MessageOptional `thrift:"Options,5,optional" json:"options"`
	// 指定空间，不指定用个人空间
	SpaceID *string `thrift:"SpaceID,6,optional" json:"space_id"`
}

func NewOpenAPICreateTaskRequet() *OpenAPICreateTaskRequet {
	return &OpenAPICreateTaskRequet{}
}

func (p *OpenAPICreateTaskRequet) InitDefault() {
}

func (p *OpenAPICreateTaskRequet) GetRole() (v SessionRole) {
	return p.Role
}

func (p *OpenAPICreateTaskRequet) GetTemplateID() (v string) {
	return p.TemplateID
}

var OpenAPICreateTaskRequet_FormValue_DEFAULT *TemplateFormValue

func (p *OpenAPICreateTaskRequet) GetFormValue() (v *TemplateFormValue) {
	if !p.IsSetFormValue() {
		return OpenAPICreateTaskRequet_FormValue_DEFAULT
	}
	return p.FormValue
}

var OpenAPICreateTaskRequet_Config_DEFAULT *TaskConfig

func (p *OpenAPICreateTaskRequet) GetConfig() (v *TaskConfig) {
	if !p.IsSetConfig() {
		return OpenAPICreateTaskRequet_Config_DEFAULT
	}
	return p.Config
}

var OpenAPICreateTaskRequet_Options_DEFAULT *MessageOptional

func (p *OpenAPICreateTaskRequet) GetOptions() (v *MessageOptional) {
	if !p.IsSetOptions() {
		return OpenAPICreateTaskRequet_Options_DEFAULT
	}
	return p.Options
}

var OpenAPICreateTaskRequet_SpaceID_DEFAULT string

func (p *OpenAPICreateTaskRequet) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return OpenAPICreateTaskRequet_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *OpenAPICreateTaskRequet) IsSetFormValue() bool {
	return p.FormValue != nil
}

func (p *OpenAPICreateTaskRequet) IsSetConfig() bool {
	return p.Config != nil
}

func (p *OpenAPICreateTaskRequet) IsSetOptions() bool {
	return p.Options != nil
}

func (p *OpenAPICreateTaskRequet) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *OpenAPICreateTaskRequet) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPICreateTaskRequet(%+v)", *p)
}

type MessageOptional struct {
	Locale LocaleType `thrift:"locale,1,required" json:"locale"`
}

func NewMessageOptional() *MessageOptional {
	return &MessageOptional{}
}

func (p *MessageOptional) InitDefault() {
}

func (p *MessageOptional) GetLocale() (v LocaleType) {
	return p.Locale
}

func (p *MessageOptional) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MessageOptional(%+v)", *p)
}

type OpenAPICreateTaskResponse struct {
	Session *Session `thrift:"Session,1,required" json:"session"`
}

func NewOpenAPICreateTaskResponse() *OpenAPICreateTaskResponse {
	return &OpenAPICreateTaskResponse{}
}

func (p *OpenAPICreateTaskResponse) InitDefault() {
}

var OpenAPICreateTaskResponse_Session_DEFAULT *Session

func (p *OpenAPICreateTaskResponse) GetSession() (v *Session) {
	if !p.IsSetSession() {
		return OpenAPICreateTaskResponse_Session_DEFAULT
	}
	return p.Session
}

func (p *OpenAPICreateTaskResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *OpenAPICreateTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPICreateTaskResponse(%+v)", *p)
}

type TaskConfig struct {
	// 发送通知到群组，配置了则需要发送
	LarkGroupID *string `thrift:"LarkGroupID,1,optional" json:"lark_group_id"`
	// 是否发送通知给个人
	SendUserNotifaction *bool `thrift:"SendUserNotifaction,2,optional" json:"bool"`
}

func NewTaskConfig() *TaskConfig {
	return &TaskConfig{}
}

func (p *TaskConfig) InitDefault() {
}

var TaskConfig_LarkGroupID_DEFAULT string

func (p *TaskConfig) GetLarkGroupID() (v string) {
	if !p.IsSetLarkGroupID() {
		return TaskConfig_LarkGroupID_DEFAULT
	}
	return *p.LarkGroupID
}

var TaskConfig_SendUserNotifaction_DEFAULT bool

func (p *TaskConfig) GetSendUserNotifaction() (v bool) {
	if !p.IsSetSendUserNotifaction() {
		return TaskConfig_SendUserNotifaction_DEFAULT
	}
	return *p.SendUserNotifaction
}

func (p *TaskConfig) IsSetLarkGroupID() bool {
	return p.LarkGroupID != nil
}

func (p *TaskConfig) IsSetSendUserNotifaction() bool {
	return p.SendUserNotifaction != nil
}

func (p *TaskConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskConfig(%+v)", *p)
}
