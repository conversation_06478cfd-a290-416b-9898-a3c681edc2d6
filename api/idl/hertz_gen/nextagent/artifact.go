// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"fmt"
)

const (
	ChangeTypeAdded = "added"

	ChangeTypeModified = "modified"

	ChangeTypeDeleted = "deleted"

	ChangeTypeRenamed = "renamed"

	ArtifactStatusCreating = "creating"

	ArtifactStatusDraft = "draft"

	ArtifactStatusCompleted = "completed"

	ArtifactSourceUser = "user"

	ArtifactSourceAgent = "agent"
)

type GitChangeType = string

type ArtifactStatus = string

type ArtifactSource = string

type Artifact struct {
	ID                      string                    `thrift:"ID,1,required" json:"id"`
	Type                    ArtifactType              `thrift:"Type,2,required" json:"type"`
	Status                  ArtifactStatus            `thrift:"Status,3,required" json:"status"`
	Source                  ArtifactSource            `thrift:"Source,4,required" json:"source"`
	Key                     string                    `thrift:"Key,5,required" json:"key"`
	Version                 int32                     `thrift:"Version,6,required" json:"version"`
	FileMetas               []*FileMeta               `thrift:"FileMetas,7,required" json:"file_metas"`
	CreatedAt               string                    `thrift:"CreatedAt,8,required" json:"created_at"`
	Metadata                common.JsonVariables      `thrift:"Metadata,9,required" json:"metadata"`
	HistoryVersionArtifacts []*HistoryVersionArtifact `thrift:"HistoryVersionArtifacts,10,required" json:"history_version_artifacts"`
}

func NewArtifact() *Artifact {
	return &Artifact{}
}

func (p *Artifact) InitDefault() {
}

func (p *Artifact) GetID() (v string) {
	return p.ID
}

func (p *Artifact) GetType() (v ArtifactType) {
	return p.Type
}

func (p *Artifact) GetStatus() (v ArtifactStatus) {
	return p.Status
}

func (p *Artifact) GetSource() (v ArtifactSource) {
	return p.Source
}

func (p *Artifact) GetKey() (v string) {
	return p.Key
}

func (p *Artifact) GetVersion() (v int32) {
	return p.Version
}

func (p *Artifact) GetFileMetas() (v []*FileMeta) {
	return p.FileMetas
}

func (p *Artifact) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Artifact) GetMetadata() (v common.JsonVariables) {
	return p.Metadata
}

func (p *Artifact) GetHistoryVersionArtifacts() (v []*HistoryVersionArtifact) {
	return p.HistoryVersionArtifacts
}

func (p *Artifact) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Artifact(%+v)", *p)
}

type HistoryVersionArtifact struct {
	ID        string               `thrift:"ID,1,required" json:"id"`
	Version   int32                `thrift:"Version,2,required" json:"version"`
	FileMetas []*FileMeta          `thrift:"FileMetas,3,required" json:"file_metas"`
	Metadata  common.JsonVariables `thrift:"Metadata,4,required" json:"metadata"`
}

func NewHistoryVersionArtifact() *HistoryVersionArtifact {
	return &HistoryVersionArtifact{}
}

func (p *HistoryVersionArtifact) InitDefault() {
}

func (p *HistoryVersionArtifact) GetID() (v string) {
	return p.ID
}

func (p *HistoryVersionArtifact) GetVersion() (v int32) {
	return p.Version
}

func (p *HistoryVersionArtifact) GetFileMetas() (v []*FileMeta) {
	return p.FileMetas
}

func (p *HistoryVersionArtifact) GetMetadata() (v common.JsonVariables) {
	return p.Metadata
}

func (p *HistoryVersionArtifact) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HistoryVersionArtifact(%+v)", *p)
}

type DiffFileMeta struct {
	Path string `thrift:"Path,1,required" json:"path"`
	/** Enum values: `added` / `modified` / `deleted` / `renamed`. */
	ChangeType GitChangeType `thrift:"ChangeType,2,required" json:"change_type"`
	FromPath   string        `thrift:"FromPath,3,required" json:"from_path"`
	/** Deprecated. Use FromId instead. */
	FromCommit string `thrift:"FromCommit,4,required" json:"from_commit"`
	FromMode   int32  `thrift:"FromMode,5,required" json:"from_mode"`
	ToPath     string `thrift:"ToPath,6,required" json:"to_path"`
	/** Deprecated. Use ToId instead. */
	ToCommit      string `thrift:"ToCommit,7,required" json:"to_commit"`
	ToMode        int32  `thrift:"ToMode,8,required" json:"to_mode"`
	IsBinary      bool   `thrift:"IsBinary,9,required" json:"is_binary"`
	LinesInserted int64  `thrift:"LinesInserted,10,required" json:"lines_inserted"`
	LinesDeleted  int64  `thrift:"LinesDeleted,11,required" json:"lines_deleted"`
	FromId        string `thrift:"FromId,12" json:"from_id"`
	ToId          string `thrift:"ToId,13" json:"to_id"`
}

func NewDiffFileMeta() *DiffFileMeta {
	return &DiffFileMeta{}
}

func (p *DiffFileMeta) InitDefault() {
}

func (p *DiffFileMeta) GetPath() (v string) {
	return p.Path
}

func (p *DiffFileMeta) GetChangeType() (v GitChangeType) {
	return p.ChangeType
}

func (p *DiffFileMeta) GetFromPath() (v string) {
	return p.FromPath
}

func (p *DiffFileMeta) GetFromCommit() (v string) {
	return p.FromCommit
}

func (p *DiffFileMeta) GetFromMode() (v int32) {
	return p.FromMode
}

func (p *DiffFileMeta) GetToPath() (v string) {
	return p.ToPath
}

func (p *DiffFileMeta) GetToCommit() (v string) {
	return p.ToCommit
}

func (p *DiffFileMeta) GetToMode() (v int32) {
	return p.ToMode
}

func (p *DiffFileMeta) GetIsBinary() (v bool) {
	return p.IsBinary
}

func (p *DiffFileMeta) GetLinesInserted() (v int64) {
	return p.LinesInserted
}

func (p *DiffFileMeta) GetLinesDeleted() (v int64) {
	return p.LinesDeleted
}

func (p *DiffFileMeta) GetFromId() (v string) {
	return p.FromId
}

func (p *DiffFileMeta) GetToId() (v string) {
	return p.ToId
}

func (p *DiffFileMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DiffFileMeta(%+v)", *p)
}

type DiffFileContent struct {
	Path string `thrift:"Path,1,required" json:"path"`
	/** Enum values: `added` / `modified` / `deleted` / `renamed`. */
	ChangeType GitChangeType `thrift:"ChangeType,2,required" json:"change_type"`
	FromPath   string        `thrift:"FromPath,3,required" json:"from_path"`
	/** Deprecated. Use FromId instead. */
	FromCommit string `thrift:"FromCommit,4,required" json:"from_commit"`
	FromMode   int32  `thrift:"FromMode,5,required" json:"from_mode"`
	ToPath     string `thrift:"ToPath,6,required" json:"to_path"`
	/** Deprecated. Use ToId instead. */
	ToCommit string `thrift:"ToCommit,7,required" json:"to_commit"`
	ToMode   int32  `thrift:"ToMode,8,required" json:"to_mode"`
	IsBinary bool   `thrift:"IsBinary,9,required" json:"is_binary"`
	RawPatch string `thrift:"RawPatch,10,required" json:"raw_patch"`
	FromId   string `thrift:"FromId,11" json:"from_id"`
	ToId     string `thrift:"ToId,12" json:"to_id"`
	/* Indicates the patch was pruned since it surpassed a hard limit. */
	TooLarge bool `thrift:"TooLarge,13" json:"too_large"`
}

func NewDiffFileContent() *DiffFileContent {
	return &DiffFileContent{}
}

func (p *DiffFileContent) InitDefault() {
}

func (p *DiffFileContent) GetPath() (v string) {
	return p.Path
}

func (p *DiffFileContent) GetChangeType() (v GitChangeType) {
	return p.ChangeType
}

func (p *DiffFileContent) GetFromPath() (v string) {
	return p.FromPath
}

func (p *DiffFileContent) GetFromCommit() (v string) {
	return p.FromCommit
}

func (p *DiffFileContent) GetFromMode() (v int32) {
	return p.FromMode
}

func (p *DiffFileContent) GetToPath() (v string) {
	return p.ToPath
}

func (p *DiffFileContent) GetToCommit() (v string) {
	return p.ToCommit
}

func (p *DiffFileContent) GetToMode() (v int32) {
	return p.ToMode
}

func (p *DiffFileContent) GetIsBinary() (v bool) {
	return p.IsBinary
}

func (p *DiffFileContent) GetRawPatch() (v string) {
	return p.RawPatch
}

func (p *DiffFileContent) GetFromId() (v string) {
	return p.FromId
}

func (p *DiffFileContent) GetToId() (v string) {
	return p.ToId
}

func (p *DiffFileContent) GetTooLarge() (v bool) {
	return p.TooLarge
}

func (p *DiffFileContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DiffFileContent(%+v)", *p)
}

type ProjectArtifactDiffRequest struct {
	SessionID   *string `thrift:"SessionID,1,optional" json:"session_id" query:"session_id" `
	ReplayID    *string `thrift:"ReplayID,2,optional" json:"replay_id" query:"replay_id" `
	ArtifactKey string  `thrift:"ArtifactKey,3,required" json:"artifact_key" path:"artifact_key,required" `
	// "2" 或 "2..5"
	Revision string `thrift:"Revision,4,required" json:"revision" path:"revision,required" `
	// true: 返回 DiffFilesContent，false: 返回 DiffFilesMeta
	Detail   *bool   `thrift:"Detail,5,optional" json:"detail" query:"detail" `
	FilePath *string `thrift:"FilePath,6,optional" json:"file_path" query:"file_path" `
}

func NewProjectArtifactDiffRequest() *ProjectArtifactDiffRequest {
	return &ProjectArtifactDiffRequest{}
}

func (p *ProjectArtifactDiffRequest) InitDefault() {
}

var ProjectArtifactDiffRequest_SessionID_DEFAULT string

func (p *ProjectArtifactDiffRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ProjectArtifactDiffRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var ProjectArtifactDiffRequest_ReplayID_DEFAULT string

func (p *ProjectArtifactDiffRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return ProjectArtifactDiffRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

func (p *ProjectArtifactDiffRequest) GetArtifactKey() (v string) {
	return p.ArtifactKey
}

func (p *ProjectArtifactDiffRequest) GetRevision() (v string) {
	return p.Revision
}

var ProjectArtifactDiffRequest_Detail_DEFAULT bool

func (p *ProjectArtifactDiffRequest) GetDetail() (v bool) {
	if !p.IsSetDetail() {
		return ProjectArtifactDiffRequest_Detail_DEFAULT
	}
	return *p.Detail
}

var ProjectArtifactDiffRequest_FilePath_DEFAULT string

func (p *ProjectArtifactDiffRequest) GetFilePath() (v string) {
	if !p.IsSetFilePath() {
		return ProjectArtifactDiffRequest_FilePath_DEFAULT
	}
	return *p.FilePath
}

func (p *ProjectArtifactDiffRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ProjectArtifactDiffRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *ProjectArtifactDiffRequest) IsSetDetail() bool {
	return p.Detail != nil
}

func (p *ProjectArtifactDiffRequest) IsSetFilePath() bool {
	return p.FilePath != nil
}

func (p *ProjectArtifactDiffRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectArtifactDiffRequest(%+v)", *p)
}

// diff 响应
type ProjectArtifactDiffResponse struct {
	DiffFilesMeta    []*DiffFileMeta    `thrift:"DiffFilesMeta,1,required" json:"diff_files_meta"`
	DiffFilesContent []*DiffFileContent `thrift:"DiffFilesContent,2,required" json:"diff_files_content"`
}

func NewProjectArtifactDiffResponse() *ProjectArtifactDiffResponse {
	return &ProjectArtifactDiffResponse{}
}

func (p *ProjectArtifactDiffResponse) InitDefault() {
}

func (p *ProjectArtifactDiffResponse) GetDiffFilesMeta() (v []*DiffFileMeta) {
	return p.DiffFilesMeta
}

func (p *ProjectArtifactDiffResponse) GetDiffFilesContent() (v []*DiffFileContent) {
	return p.DiffFilesContent
}

func (p *ProjectArtifactDiffResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectArtifactDiffResponse(%+v)", *p)
}

type ProjectArtifactFilesRequest struct {
	SessionID   *string `thrift:"SessionID,1,optional" json:"session_id" query:"session_id" `
	ReplayID    *string `thrift:"ReplayID,2,optional" json:"replay_id" query:"replay_id" `
	ArtifactKey string  `thrift:"ArtifactKey,3,required" json:"artifact_key" path:"artifact_key,required" `
	// "2" 或 "2..5"
	Version  string `thrift:"Version,4,required" json:"version" path:"version,required" `
	FilePath string `thrift:"FilePath,5,required" json:"file_path" query:"file_path,required" `
	Depth    *int32 `thrift:"Depth,6,optional" json:"depth" query:"depth" `
}

func NewProjectArtifactFilesRequest() *ProjectArtifactFilesRequest {
	return &ProjectArtifactFilesRequest{}
}

func (p *ProjectArtifactFilesRequest) InitDefault() {
}

var ProjectArtifactFilesRequest_SessionID_DEFAULT string

func (p *ProjectArtifactFilesRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ProjectArtifactFilesRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var ProjectArtifactFilesRequest_ReplayID_DEFAULT string

func (p *ProjectArtifactFilesRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return ProjectArtifactFilesRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

func (p *ProjectArtifactFilesRequest) GetArtifactKey() (v string) {
	return p.ArtifactKey
}

func (p *ProjectArtifactFilesRequest) GetVersion() (v string) {
	return p.Version
}

func (p *ProjectArtifactFilesRequest) GetFilePath() (v string) {
	return p.FilePath
}

var ProjectArtifactFilesRequest_Depth_DEFAULT int32

func (p *ProjectArtifactFilesRequest) GetDepth() (v int32) {
	if !p.IsSetDepth() {
		return ProjectArtifactFilesRequest_Depth_DEFAULT
	}
	return *p.Depth
}

func (p *ProjectArtifactFilesRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ProjectArtifactFilesRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *ProjectArtifactFilesRequest) IsSetDepth() bool {
	return p.Depth != nil
}

func (p *ProjectArtifactFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectArtifactFilesRequest(%+v)", *p)
}

// 版本下的文件列表结果
type ProjectArtifactFilesResponse struct {
	Files []*FileNode `thrift:"Files,1,required" json:"files"`
}

func NewProjectArtifactFilesResponse() *ProjectArtifactFilesResponse {
	return &ProjectArtifactFilesResponse{}
}

func (p *ProjectArtifactFilesResponse) InitDefault() {
}

func (p *ProjectArtifactFilesResponse) GetFiles() (v []*FileNode) {
	return p.Files
}

func (p *ProjectArtifactFilesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectArtifactFilesResponse(%+v)", *p)
}

// 文件内容
type FileContent struct {
	Path     string `thrift:"Path,1" json:"path"`
	Content  string `thrift:"Content,2" json:"content"`
	Size     int64  `thrift:"Size,3" json:"size"`
	IsBinary bool   `thrift:"IsBinary,4" json:"is_binary"`
}

func NewFileContent() *FileContent {
	return &FileContent{}
}

func (p *FileContent) InitDefault() {
}

func (p *FileContent) GetPath() (v string) {
	return p.Path
}

func (p *FileContent) GetContent() (v string) {
	return p.Content
}

func (p *FileContent) GetSize() (v int64) {
	return p.Size
}

func (p *FileContent) GetIsBinary() (v bool) {
	return p.IsBinary
}

func (p *FileContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileContent(%+v)", *p)
}

// 版本下的文件树节点
type FileNode struct {
	Path   string `thrift:"Path,1" json:"path"`
	Name   string `thrift:"Name,2" json:"name"`
	IsDir  bool   `thrift:"IsDir,3" json:"is_dir"`
	IsLeaf bool   `thrift:"IsLeaf,4" json:"is_leaf"`
	Size   *int64 `thrift:"Size,5,optional" json:"size"`
	// 目录才有
	Children []*FileNode `thrift:"Children,6,optional" json:"children"`
}

func NewFileNode() *FileNode {
	return &FileNode{}
}

func (p *FileNode) InitDefault() {
}

func (p *FileNode) GetPath() (v string) {
	return p.Path
}

func (p *FileNode) GetName() (v string) {
	return p.Name
}

func (p *FileNode) GetIsDir() (v bool) {
	return p.IsDir
}

func (p *FileNode) GetIsLeaf() (v bool) {
	return p.IsLeaf
}

var FileNode_Size_DEFAULT int64

func (p *FileNode) GetSize() (v int64) {
	if !p.IsSetSize() {
		return FileNode_Size_DEFAULT
	}
	return *p.Size
}

var FileNode_Children_DEFAULT []*FileNode

func (p *FileNode) GetChildren() (v []*FileNode) {
	if !p.IsSetChildren() {
		return FileNode_Children_DEFAULT
	}
	return p.Children
}

func (p *FileNode) IsSetSize() bool {
	return p.Size != nil
}

func (p *FileNode) IsSetChildren() bool {
	return p.Children != nil
}

func (p *FileNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileNode(%+v)", *p)
}

type ProjectArtifactFilesContentRequest struct {
	SessionID   *string `thrift:"SessionID,1,optional" json:"session_id" query:"session_id" `
	ReplayID    *string `thrift:"ReplayID,2,optional" json:"replay_id" query:"replay_id" `
	ArtifactKey string  `thrift:"ArtifactKey,3,required" json:"artifact_key" path:"artifact_key,required" `
	// "2" 或 "2..5"
	Version         string   `thrift:"Version,4,required" json:"version" path:"version,required" `
	FilePathList    []string `thrift:"FilePathList,5,required" json:"file_path_list" query:"file_path_list,required" `
	IsPreviewBinary *bool    `thrift:"IsPreviewBinary,6,optional" json:"is_preview_binary" query:"is_preview_binary" `
}

func NewProjectArtifactFilesContentRequest() *ProjectArtifactFilesContentRequest {
	return &ProjectArtifactFilesContentRequest{}
}

func (p *ProjectArtifactFilesContentRequest) InitDefault() {
}

var ProjectArtifactFilesContentRequest_SessionID_DEFAULT string

func (p *ProjectArtifactFilesContentRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ProjectArtifactFilesContentRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var ProjectArtifactFilesContentRequest_ReplayID_DEFAULT string

func (p *ProjectArtifactFilesContentRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return ProjectArtifactFilesContentRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

func (p *ProjectArtifactFilesContentRequest) GetArtifactKey() (v string) {
	return p.ArtifactKey
}

func (p *ProjectArtifactFilesContentRequest) GetVersion() (v string) {
	return p.Version
}

func (p *ProjectArtifactFilesContentRequest) GetFilePathList() (v []string) {
	return p.FilePathList
}

var ProjectArtifactFilesContentRequest_IsPreviewBinary_DEFAULT bool

func (p *ProjectArtifactFilesContentRequest) GetIsPreviewBinary() (v bool) {
	if !p.IsSetIsPreviewBinary() {
		return ProjectArtifactFilesContentRequest_IsPreviewBinary_DEFAULT
	}
	return *p.IsPreviewBinary
}

func (p *ProjectArtifactFilesContentRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ProjectArtifactFilesContentRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *ProjectArtifactFilesContentRequest) IsSetIsPreviewBinary() bool {
	return p.IsPreviewBinary != nil
}

func (p *ProjectArtifactFilesContentRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectArtifactFilesContentRequest(%+v)", *p)
}

// 版本下的文件内容结果列表
type ProjectArtifactFilesContentResponse struct {
	FilesContent []*FileContent `thrift:"FilesContent,1,required" json:"files_content"`
}

func NewProjectArtifactFilesContentResponse() *ProjectArtifactFilesContentResponse {
	return &ProjectArtifactFilesContentResponse{}
}

func (p *ProjectArtifactFilesContentResponse) InitDefault() {
}

func (p *ProjectArtifactFilesContentResponse) GetFilesContent() (v []*FileContent) {
	return p.FilesContent
}

func (p *ProjectArtifactFilesContentResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectArtifactFilesContentResponse(%+v)", *p)
}

type CreateArtifactRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id"`
	// 目前对前端支持 file, code, image 类型
	Type      ArtifactType          `thrift:"Type,2,required" json:"type"`
	Key       string                `thrift:"Key,3,required" json:"key"`
	Metadata  common.JsonVariables  `thrift:"Metadata,4,required" json:"metadata"`
	Version   *int32                `thrift:"Version,5,optional" json:"version"`
	FileMetas *common.JsonVariables `thrift:"FileMetas,6,optional" json:"file_metas"`
}

func NewCreateArtifactRequest() *CreateArtifactRequest {
	return &CreateArtifactRequest{}
}

func (p *CreateArtifactRequest) InitDefault() {
}

func (p *CreateArtifactRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *CreateArtifactRequest) GetType() (v ArtifactType) {
	return p.Type
}

func (p *CreateArtifactRequest) GetKey() (v string) {
	return p.Key
}

func (p *CreateArtifactRequest) GetMetadata() (v common.JsonVariables) {
	return p.Metadata
}

var CreateArtifactRequest_Version_DEFAULT int32

func (p *CreateArtifactRequest) GetVersion() (v int32) {
	if !p.IsSetVersion() {
		return CreateArtifactRequest_Version_DEFAULT
	}
	return *p.Version
}

var CreateArtifactRequest_FileMetas_DEFAULT common.JsonVariables

func (p *CreateArtifactRequest) GetFileMetas() (v common.JsonVariables) {
	if !p.IsSetFileMetas() {
		return CreateArtifactRequest_FileMetas_DEFAULT
	}
	return *p.FileMetas
}

func (p *CreateArtifactRequest) IsSetVersion() bool {
	return p.Version != nil
}

func (p *CreateArtifactRequest) IsSetFileMetas() bool {
	return p.FileMetas != nil
}

func (p *CreateArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateArtifactRequest(%+v)", *p)
}

type CreateArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewCreateArtifactResponse() *CreateArtifactResponse {
	return &CreateArtifactResponse{}
}

func (p *CreateArtifactResponse) InitDefault() {
}

var CreateArtifactResponse_Artifact_DEFAULT *Artifact

func (p *CreateArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return CreateArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *CreateArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *CreateArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateArtifactResponse(%+v)", *p)
}

type GetArtifactRequest struct {
	ID string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
}

func NewGetArtifactRequest() *GetArtifactRequest {
	return &GetArtifactRequest{}
}

func (p *GetArtifactRequest) InitDefault() {
}

func (p *GetArtifactRequest) GetID() (v string) {
	return p.ID
}

func (p *GetArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactRequest(%+v)", *p)
}

type GetArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewGetArtifactResponse() *GetArtifactResponse {
	return &GetArtifactResponse{}
}

func (p *GetArtifactResponse) InitDefault() {
}

var GetArtifactResponse_Artifact_DEFAULT *Artifact

func (p *GetArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return GetArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *GetArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *GetArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArtifactResponse(%+v)", *p)
}

type UpdateArtifactRequest struct {
	ID        string                `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Status    *ArtifactStatus       `thrift:"Status,2,optional" json:"status"`
	Metadata  *common.JsonVariables `thrift:"Metadata,3,optional" json:"metadata"`
	SessionID *string               `thrift:"SessionID,4,optional" json:"session_id"`
}

func NewUpdateArtifactRequest() *UpdateArtifactRequest {
	return &UpdateArtifactRequest{}
}

func (p *UpdateArtifactRequest) InitDefault() {
}

func (p *UpdateArtifactRequest) GetID() (v string) {
	return p.ID
}

var UpdateArtifactRequest_Status_DEFAULT ArtifactStatus

func (p *UpdateArtifactRequest) GetStatus() (v ArtifactStatus) {
	if !p.IsSetStatus() {
		return UpdateArtifactRequest_Status_DEFAULT
	}
	return *p.Status
}

var UpdateArtifactRequest_Metadata_DEFAULT common.JsonVariables

func (p *UpdateArtifactRequest) GetMetadata() (v common.JsonVariables) {
	if !p.IsSetMetadata() {
		return UpdateArtifactRequest_Metadata_DEFAULT
	}
	return *p.Metadata
}

var UpdateArtifactRequest_SessionID_DEFAULT string

func (p *UpdateArtifactRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return UpdateArtifactRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

func (p *UpdateArtifactRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *UpdateArtifactRequest) IsSetMetadata() bool {
	return p.Metadata != nil
}

func (p *UpdateArtifactRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *UpdateArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactRequest(%+v)", *p)
}

type UpdateArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewUpdateArtifactResponse() *UpdateArtifactResponse {
	return &UpdateArtifactResponse{}
}

func (p *UpdateArtifactResponse) InitDefault() {
}

var UpdateArtifactResponse_Artifact_DEFAULT *Artifact

func (p *UpdateArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return UpdateArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *UpdateArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *UpdateArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactResponse(%+v)", *p)
}

// multipart/form-data file upload
type UploadArtifactRequest struct {
	ID      string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path    string `thrift:"Path,2,required" form:"path,required" json:"path,required"`
	Content []byte `thrift:"Content,3,required" form:"content,required" json:"content,required"`
}

func NewUploadArtifactRequest() *UploadArtifactRequest {
	return &UploadArtifactRequest{}
}

func (p *UploadArtifactRequest) InitDefault() {
}

func (p *UploadArtifactRequest) GetID() (v string) {
	return p.ID
}

func (p *UploadArtifactRequest) GetPath() (v string) {
	return p.Path
}

func (p *UploadArtifactRequest) GetContent() (v []byte) {
	return p.Content
}

func (p *UploadArtifactRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadArtifactRequest(%+v)", *p)
}

type UploadArtifactStreamRequest struct {
	ID   string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path string `thrift:"Path,2,required" json:"path,required" query:"path,required"`
	Size int64  `thrift:"Size,3,required" json:"size,required" query:"size,required"`
}

func NewUploadArtifactStreamRequest() *UploadArtifactStreamRequest {
	return &UploadArtifactStreamRequest{}
}

func (p *UploadArtifactStreamRequest) InitDefault() {
}

func (p *UploadArtifactStreamRequest) GetID() (v string) {
	return p.ID
}

func (p *UploadArtifactStreamRequest) GetPath() (v string) {
	return p.Path
}

func (p *UploadArtifactStreamRequest) GetSize() (v int64) {
	return p.Size
}

func (p *UploadArtifactStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadArtifactStreamRequest(%+v)", *p)
}

type UploadArtifactResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewUploadArtifactResponse() *UploadArtifactResponse {
	return &UploadArtifactResponse{}
}

func (p *UploadArtifactResponse) InitDefault() {
}

var UploadArtifactResponse_Artifact_DEFAULT *Artifact

func (p *UploadArtifactResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return UploadArtifactResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *UploadArtifactResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *UploadArtifactResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadArtifactResponse(%+v)", *p)
}

type UploadImageByURLRequest struct {
	URLs      []string `thrift:"URLs,1,required" form:"urls,required" json:"urls,required"`
	SessionID string   `thrift:"SessionID,2,required" json:"session_id"`
}

func NewUploadImageByURLRequest() *UploadImageByURLRequest {
	return &UploadImageByURLRequest{}
}

func (p *UploadImageByURLRequest) InitDefault() {
}

func (p *UploadImageByURLRequest) GetURLs() (v []string) {
	return p.URLs
}

func (p *UploadImageByURLRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *UploadImageByURLRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadImageByURLRequest(%+v)", *p)
}

type UploadImageByURLResponse struct {
	Artifacts []*Artifact `thrift:"Artifacts,1,required" json:"artifacts"`
}

func NewUploadImageByURLResponse() *UploadImageByURLResponse {
	return &UploadImageByURLResponse{}
}

func (p *UploadImageByURLResponse) InitDefault() {
}

func (p *UploadImageByURLResponse) GetArtifacts() (v []*Artifact) {
	return p.Artifacts
}

func (p *UploadImageByURLResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadImageByURLResponse(%+v)", *p)
}

type RetrieveArtifactFilesRequest struct {
	ID      string   `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Files   []string `thrift:"Files,2,required" json:"files"`
	Preview *bool    `thrift:"preview,3,optional" json:"preview"`
}

func NewRetrieveArtifactFilesRequest() *RetrieveArtifactFilesRequest {
	return &RetrieveArtifactFilesRequest{}
}

func (p *RetrieveArtifactFilesRequest) InitDefault() {
}

func (p *RetrieveArtifactFilesRequest) GetID() (v string) {
	return p.ID
}

func (p *RetrieveArtifactFilesRequest) GetFiles() (v []string) {
	return p.Files
}

var RetrieveArtifactFilesRequest_Preview_DEFAULT bool

func (p *RetrieveArtifactFilesRequest) GetPreview() (v bool) {
	if !p.IsSetPreview() {
		return RetrieveArtifactFilesRequest_Preview_DEFAULT
	}
	return *p.Preview
}

func (p *RetrieveArtifactFilesRequest) IsSetPreview() bool {
	return p.Preview != nil
}

func (p *RetrieveArtifactFilesRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetrieveArtifactFilesRequest(%+v)", *p)
}

type RetrieveArtifactFilesResponse struct {
	Files []*FileMeta `thrift:"Files,1,required" json:"files"`
}

func NewRetrieveArtifactFilesResponse() *RetrieveArtifactFilesResponse {
	return &RetrieveArtifactFilesResponse{}
}

func (p *RetrieveArtifactFilesResponse) InitDefault() {
}

func (p *RetrieveArtifactFilesResponse) GetFiles() (v []*FileMeta) {
	return p.Files
}

func (p *RetrieveArtifactFilesResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetrieveArtifactFilesResponse(%+v)", *p)
}

type DownloadArtifactFileRequest struct {
	ID   string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path string `thrift:"Path,2,required" json:"path,required" path:"path,required"`
	// 是否返回原始文件内容，默认会替换文件内部分内容，如 ::cite 标签
	Raw *bool `thrift:"Raw,3,optional" json:"raw,omitempty" query:"raw"`
	// 流式返回
	Stream *bool `thrift:"Stream,4,optional" json:"stream,omitempty" query:"stream"`
}

func NewDownloadArtifactFileRequest() *DownloadArtifactFileRequest {
	return &DownloadArtifactFileRequest{}
}

func (p *DownloadArtifactFileRequest) InitDefault() {
}

func (p *DownloadArtifactFileRequest) GetID() (v string) {
	return p.ID
}

func (p *DownloadArtifactFileRequest) GetPath() (v string) {
	return p.Path
}

var DownloadArtifactFileRequest_Raw_DEFAULT bool

func (p *DownloadArtifactFileRequest) GetRaw() (v bool) {
	if !p.IsSetRaw() {
		return DownloadArtifactFileRequest_Raw_DEFAULT
	}
	return *p.Raw
}

var DownloadArtifactFileRequest_Stream_DEFAULT bool

func (p *DownloadArtifactFileRequest) GetStream() (v bool) {
	if !p.IsSetStream() {
		return DownloadArtifactFileRequest_Stream_DEFAULT
	}
	return *p.Stream
}

func (p *DownloadArtifactFileRequest) IsSetRaw() bool {
	return p.Raw != nil
}

func (p *DownloadArtifactFileRequest) IsSetStream() bool {
	return p.Stream != nil
}

func (p *DownloadArtifactFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadArtifactFileRequest(%+v)", *p)
}

type ArtifactFiles struct {
	ID    string   `thrift:"ID,1,required" json:"artifact_id"`
	Paths []string `thrift:"Paths,2,required" json:"paths"`
}

func NewArtifactFiles() *ArtifactFiles {
	return &ArtifactFiles{}
}

func (p *ArtifactFiles) InitDefault() {
}

func (p *ArtifactFiles) GetID() (v string) {
	return p.ID
}

func (p *ArtifactFiles) GetPaths() (v []string) {
	return p.Paths
}

func (p *ArtifactFiles) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ArtifactFiles(%+v)", *p)
}

type DownloadArtifactBatchRequest struct {
	Artifacts []*ArtifactFiles `thrift:"Artifacts,1,required" json:"artifacts"`
	SessionID *string          `thrift:"SessionID,2,optional" json:"session_id"`
	ReplayID  *string          `thrift:"ReplayID,3,optional" json:"replay_id"`
}

func NewDownloadArtifactBatchRequest() *DownloadArtifactBatchRequest {
	return &DownloadArtifactBatchRequest{}
}

func (p *DownloadArtifactBatchRequest) InitDefault() {
}

func (p *DownloadArtifactBatchRequest) GetArtifacts() (v []*ArtifactFiles) {
	return p.Artifacts
}

var DownloadArtifactBatchRequest_SessionID_DEFAULT string

func (p *DownloadArtifactBatchRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return DownloadArtifactBatchRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var DownloadArtifactBatchRequest_ReplayID_DEFAULT string

func (p *DownloadArtifactBatchRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return DownloadArtifactBatchRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

func (p *DownloadArtifactBatchRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *DownloadArtifactBatchRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *DownloadArtifactBatchRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DownloadArtifactBatchRequest(%+v)", *p)
}

type ListArtifactsRequest struct {
	SessionID *string `thrift:"SessionID,1,optional" json:"session_id,omitempty" query:"session_id"`
	ReplayID  *string `thrift:"ReplayID,2,optional" json:"replay_id,omitempty" query:"replay_id"`
	Display   *bool   `thrift:"display,3,optional" json:"display,omitempty" query:"display"`
}

func NewListArtifactsRequest() *ListArtifactsRequest {
	return &ListArtifactsRequest{}
}

func (p *ListArtifactsRequest) InitDefault() {
}

var ListArtifactsRequest_SessionID_DEFAULT string

func (p *ListArtifactsRequest) GetSessionID() (v string) {
	if !p.IsSetSessionID() {
		return ListArtifactsRequest_SessionID_DEFAULT
	}
	return *p.SessionID
}

var ListArtifactsRequest_ReplayID_DEFAULT string

func (p *ListArtifactsRequest) GetReplayID() (v string) {
	if !p.IsSetReplayID() {
		return ListArtifactsRequest_ReplayID_DEFAULT
	}
	return *p.ReplayID
}

var ListArtifactsRequest_Display_DEFAULT bool

func (p *ListArtifactsRequest) GetDisplay() (v bool) {
	if !p.IsSetDisplay() {
		return ListArtifactsRequest_Display_DEFAULT
	}
	return *p.Display
}

func (p *ListArtifactsRequest) IsSetSessionID() bool {
	return p.SessionID != nil
}

func (p *ListArtifactsRequest) IsSetReplayID() bool {
	return p.ReplayID != nil
}

func (p *ListArtifactsRequest) IsSetDisplay() bool {
	return p.Display != nil
}

func (p *ListArtifactsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListArtifactsRequest(%+v)", *p)
}

type ListArtifactsResponse struct {
	Artifacts []*Artifact `thrift:"Artifacts,1,required" json:"artifacts"`
}

func NewListArtifactsResponse() *ListArtifactsResponse {
	return &ListArtifactsResponse{}
}

func (p *ListArtifactsResponse) InitDefault() {
}

func (p *ListArtifactsResponse) GetArtifacts() (v []*Artifact) {
	return p.Artifacts
}

func (p *ListArtifactsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListArtifactsResponse(%+v)", *p)
}

type UpdateArtifactFileRequest struct {
	ID   string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path string `thrift:"Path,2,required" json:"path,required" path:"path,required"`
	// UploadLark 上传 Lark 会自动给 Session Owner 授权
	UploadLark *bool `thrift:"UploadLark,3,optional" json:"upload_lark"`
	// GrantPermission 会给该 Artifact 所属 Session 的 Owner 授权
	GrantPermission *bool `thrift:"GrantPermission,4,optional" json:"grant_permission"`
}

func NewUpdateArtifactFileRequest() *UpdateArtifactFileRequest {
	return &UpdateArtifactFileRequest{}
}

func (p *UpdateArtifactFileRequest) InitDefault() {
}

func (p *UpdateArtifactFileRequest) GetID() (v string) {
	return p.ID
}

func (p *UpdateArtifactFileRequest) GetPath() (v string) {
	return p.Path
}

var UpdateArtifactFileRequest_UploadLark_DEFAULT bool

func (p *UpdateArtifactFileRequest) GetUploadLark() (v bool) {
	if !p.IsSetUploadLark() {
		return UpdateArtifactFileRequest_UploadLark_DEFAULT
	}
	return *p.UploadLark
}

var UpdateArtifactFileRequest_GrantPermission_DEFAULT bool

func (p *UpdateArtifactFileRequest) GetGrantPermission() (v bool) {
	if !p.IsSetGrantPermission() {
		return UpdateArtifactFileRequest_GrantPermission_DEFAULT
	}
	return *p.GrantPermission
}

func (p *UpdateArtifactFileRequest) IsSetUploadLark() bool {
	return p.UploadLark != nil
}

func (p *UpdateArtifactFileRequest) IsSetGrantPermission() bool {
	return p.GrantPermission != nil
}

func (p *UpdateArtifactFileRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactFileRequest(%+v)", *p)
}

type UpdateArtifactFileResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewUpdateArtifactFileResponse() *UpdateArtifactFileResponse {
	return &UpdateArtifactFileResponse{}
}

func (p *UpdateArtifactFileResponse) InitDefault() {
}

var UpdateArtifactFileResponse_Artifact_DEFAULT *Artifact

func (p *UpdateArtifactFileResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return UpdateArtifactFileResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *UpdateArtifactFileResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *UpdateArtifactFileResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateArtifactFileResponse(%+v)", *p)
}

type OpenApiUploadArtifactStreamRequest struct {
	// 40 位 uuid, 绑定版本，如果 key 存在则会生成新版本
	Key string `thrift:"Key,1,required" json:"key,required" query:"key,required"`
	// 不填的话后端会算一个
	Type *ArtifactType `thrift:"Type,2,optional" json:"type,omitempty" query:"type"`
	// 文件名
	Path string `thrift:"Path,3,required" json:"path,required" query:"path,required"`
	Size int64  `thrift:"Size,4,required" json:"size,required" query:"size,required"`
}

func NewOpenApiUploadArtifactStreamRequest() *OpenApiUploadArtifactStreamRequest {
	return &OpenApiUploadArtifactStreamRequest{}
}

func (p *OpenApiUploadArtifactStreamRequest) InitDefault() {
}

func (p *OpenApiUploadArtifactStreamRequest) GetKey() (v string) {
	return p.Key
}

var OpenApiUploadArtifactStreamRequest_Type_DEFAULT ArtifactType

func (p *OpenApiUploadArtifactStreamRequest) GetType() (v ArtifactType) {
	if !p.IsSetType() {
		return OpenApiUploadArtifactStreamRequest_Type_DEFAULT
	}
	return *p.Type
}

func (p *OpenApiUploadArtifactStreamRequest) GetPath() (v string) {
	return p.Path
}

func (p *OpenApiUploadArtifactStreamRequest) GetSize() (v int64) {
	return p.Size
}

func (p *OpenApiUploadArtifactStreamRequest) IsSetType() bool {
	return p.Type != nil
}

func (p *OpenApiUploadArtifactStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenApiUploadArtifactStreamRequest(%+v)", *p)
}

type OpenApiUploadArtifactStreamResponse struct {
	Artifact *Artifact `thrift:"Artifact,1,required" json:"artifact"`
}

func NewOpenApiUploadArtifactStreamResponse() *OpenApiUploadArtifactStreamResponse {
	return &OpenApiUploadArtifactStreamResponse{}
}

func (p *OpenApiUploadArtifactStreamResponse) InitDefault() {
}

var OpenApiUploadArtifactStreamResponse_Artifact_DEFAULT *Artifact

func (p *OpenApiUploadArtifactStreamResponse) GetArtifact() (v *Artifact) {
	if !p.IsSetArtifact() {
		return OpenApiUploadArtifactStreamResponse_Artifact_DEFAULT
	}
	return p.Artifact
}

func (p *OpenApiUploadArtifactStreamResponse) IsSetArtifact() bool {
	return p.Artifact != nil
}

func (p *OpenApiUploadArtifactStreamResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenApiUploadArtifactStreamResponse(%+v)", *p)
}

type OpenApiDownloadArtifactFileStreamRequest struct {
	ID   string `thrift:"ID,1,required" json:"id,required" path:"artifact_id,required"`
	Path string `thrift:"Path,2,required" json:"path,required" path:"path,required"`
}

func NewOpenApiDownloadArtifactFileStreamRequest() *OpenApiDownloadArtifactFileStreamRequest {
	return &OpenApiDownloadArtifactFileStreamRequest{}
}

func (p *OpenApiDownloadArtifactFileStreamRequest) InitDefault() {
}

func (p *OpenApiDownloadArtifactFileStreamRequest) GetID() (v string) {
	return p.ID
}

func (p *OpenApiDownloadArtifactFileStreamRequest) GetPath() (v string) {
	return p.Path
}

func (p *OpenApiDownloadArtifactFileStreamRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenApiDownloadArtifactFileStreamRequest(%+v)", *p)
}
