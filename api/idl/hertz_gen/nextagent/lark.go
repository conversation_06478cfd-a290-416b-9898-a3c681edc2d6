// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

const (
	ReplyElementTypeUnknown = "unknown"

	ReplyElementTypeDocsLink = "docs_link"

	ReplyElementTypeTextRun = "text_run"

	ReplyElementTypePerson = "person"
)

type ReplyElementType = string

type CommentMetadata struct {
	CommentID string `thrift:"CommentID,1,required" json:"comment_id"`
	// 前端获取的 block id，不一定完全准确
	BlockID string `thrift:"BlockID,2,required" json:"block_id"`
	// 划词的内容
	Quote     string `thrift:"Quote,3,required" json:"quote"`
	CreatedAt string `thrift:"CreatedAt,4,required" json:"created_at"`
	UpdatedAt string `thrift:"UpdatedAt,5,required" json:"updated_at"`
	UserID    string `thrift:"UserID,6,required" json:"user_id"`
	// 转换后的 Username
	Username string   `thrift:"Username,7,required" json:"uesrname"`
	Replies  []*Reply `thrift:"Replies,8,required" json:"replies"`
}

func NewCommentMetadata() *CommentMetadata {
	return &CommentMetadata{}
}

func (p *CommentMetadata) InitDefault() {
}

func (p *CommentMetadata) GetCommentID() (v string) {
	return p.CommentID
}

func (p *CommentMetadata) GetBlockID() (v string) {
	return p.BlockID
}

func (p *CommentMetadata) GetQuote() (v string) {
	return p.Quote
}

func (p *CommentMetadata) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *CommentMetadata) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *CommentMetadata) GetUserID() (v string) {
	return p.UserID
}

func (p *CommentMetadata) GetUsername() (v string) {
	return p.Username
}

func (p *CommentMetadata) GetReplies() (v []*Reply) {
	return p.Replies
}

func (p *CommentMetadata) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommentMetadata(%+v)", *p)
}

type Reply struct {
	ReplyContent *ReplyContent `thrift:"ReplyContent,1,required" json:"reply_content"`
	Extra        *Extra        `thrift:"Extra,2,required" json:"extra"`
	ReplyID      string        `thrift:"ReplyID,3,required" json:"reply_id"`
	CreatedAt    string        `thrift:"CreatedAt,4,required" json:"created_at"`
	UpdatedAt    string        `thrift:"UpdatedAt,5,required" json:"updated_at"`
	UserID       string        `thrift:"UserID,6,required" json:"user_id"`
	// 转换后的 Username
	Username string `thrift:"Username,7,required" json:"uesrname"`
}

func NewReply() *Reply {
	return &Reply{}
}

func (p *Reply) InitDefault() {
}

var Reply_ReplyContent_DEFAULT *ReplyContent

func (p *Reply) GetReplyContent() (v *ReplyContent) {
	if !p.IsSetReplyContent() {
		return Reply_ReplyContent_DEFAULT
	}
	return p.ReplyContent
}

var Reply_Extra_DEFAULT *Extra

func (p *Reply) GetExtra() (v *Extra) {
	if !p.IsSetExtra() {
		return Reply_Extra_DEFAULT
	}
	return p.Extra
}

func (p *Reply) GetReplyID() (v string) {
	return p.ReplyID
}

func (p *Reply) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *Reply) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

func (p *Reply) GetUserID() (v string) {
	return p.UserID
}

func (p *Reply) GetUsername() (v string) {
	return p.Username
}

func (p *Reply) IsSetReplyContent() bool {
	return p.ReplyContent != nil
}

func (p *Reply) IsSetExtra() bool {
	return p.Extra != nil
}

func (p *Reply) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Reply(%+v)", *p)
}

type ReplyContent struct {
	Elements []*ReplyElement `thrift:"Elements,1,required" json:"elements"`
}

func NewReplyContent() *ReplyContent {
	return &ReplyContent{}
}

func (p *ReplyContent) InitDefault() {
}

func (p *ReplyContent) GetElements() (v []*ReplyElement) {
	return p.Elements
}

func (p *ReplyContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReplyContent(%+v)", *p)
}

type ReplyElement struct {
	Type     ReplyElementType `thrift:"Type,1,required" json:"type"`
	DocsLink *DocsLink        `thrift:"DocsLink,2,optional" json:"docs_link"`
	TextRun  *TextRun         `thrift:"TextRun,3,optional" json:"text_run"`
	Person   *Person          `thrift:"Person,4,optional" json:"person"`
}

func NewReplyElement() *ReplyElement {
	return &ReplyElement{}
}

func (p *ReplyElement) InitDefault() {
}

func (p *ReplyElement) GetType() (v ReplyElementType) {
	return p.Type
}

var ReplyElement_DocsLink_DEFAULT *DocsLink

func (p *ReplyElement) GetDocsLink() (v *DocsLink) {
	if !p.IsSetDocsLink() {
		return ReplyElement_DocsLink_DEFAULT
	}
	return p.DocsLink
}

var ReplyElement_TextRun_DEFAULT *TextRun

func (p *ReplyElement) GetTextRun() (v *TextRun) {
	if !p.IsSetTextRun() {
		return ReplyElement_TextRun_DEFAULT
	}
	return p.TextRun
}

var ReplyElement_Person_DEFAULT *Person

func (p *ReplyElement) GetPerson() (v *Person) {
	if !p.IsSetPerson() {
		return ReplyElement_Person_DEFAULT
	}
	return p.Person
}

func (p *ReplyElement) IsSetDocsLink() bool {
	return p.DocsLink != nil
}

func (p *ReplyElement) IsSetTextRun() bool {
	return p.TextRun != nil
}

func (p *ReplyElement) IsSetPerson() bool {
	return p.Person != nil
}

func (p *ReplyElement) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReplyElement(%+v)", *p)
}

type TextRun struct {
	Text          string `thrift:"Text,1,required" json:"text"`
	IsAimeComment bool   `thrift:"IsAimeComment,2,required" json:"is_aime_comment"`
}

func NewTextRun() *TextRun {
	return &TextRun{}
}

func (p *TextRun) InitDefault() {
}

func (p *TextRun) GetText() (v string) {
	return p.Text
}

func (p *TextRun) GetIsAimeComment() (v bool) {
	return p.IsAimeComment
}

func (p *TextRun) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TextRun(%+v)", *p)
}

type DocsLink struct {
	Url string `thrift:"Url,1,required" json:"url"`
}

func NewDocsLink() *DocsLink {
	return &DocsLink{}
}

func (p *DocsLink) InitDefault() {
}

func (p *DocsLink) GetUrl() (v string) {
	return p.Url
}

func (p *DocsLink) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DocsLink(%+v)", *p)
}

type Person struct {
	UserID string `thrift:"UserID,1,required" json:"user_id"`
	// 转换后的用户名
	Username string `thrift:"Username,2,required" json:"username"`
}

func NewPerson() *Person {
	return &Person{}
}

func (p *Person) InitDefault() {
}

func (p *Person) GetUserID() (v string) {
	return p.UserID
}

func (p *Person) GetUsername() (v string) {
	return p.Username
}

func (p *Person) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Person(%+v)", *p)
}

type Extra struct {
	ImageList []string `thrift:"ImageList,1,optional" json:"image_list"`
}

func NewExtra() *Extra {
	return &Extra{}
}

func (p *Extra) InitDefault() {
}

var Extra_ImageList_DEFAULT []string

func (p *Extra) GetImageList() (v []string) {
	if !p.IsSetImageList() {
		return Extra_ImageList_DEFAULT
	}
	return p.ImageList
}

func (p *Extra) IsSetImageList() bool {
	return p.ImageList != nil
}

func (p *Extra) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Extra(%+v)", *p)
}

type LarkAuthRequest struct {
	// 授权码
	Code string `thrift:"Code,1,required" json:"code" query:"code,required" `
	// 包含重定向url和用户名
	State string `thrift:"State,2,required" json:"state" query:"state,required" `
	// 错误信息
	Error *string `thrift:"Error,3,optional" json:"error" query:"error" `
}

func NewLarkAuthRequest() *LarkAuthRequest {
	return &LarkAuthRequest{}
}

func (p *LarkAuthRequest) InitDefault() {
}

func (p *LarkAuthRequest) GetCode() (v string) {
	return p.Code
}

func (p *LarkAuthRequest) GetState() (v string) {
	return p.State
}

var LarkAuthRequest_Error_DEFAULT string

func (p *LarkAuthRequest) GetError() (v string) {
	if !p.IsSetError() {
		return LarkAuthRequest_Error_DEFAULT
	}
	return *p.Error
}

func (p *LarkAuthRequest) IsSetError() bool {
	return p.Error != nil
}

func (p *LarkAuthRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkAuthRequest(%+v)", *p)
}

type LarkAuthResponse struct {
}

func NewLarkAuthResponse() *LarkAuthResponse {
	return &LarkAuthResponse{}
}

func (p *LarkAuthResponse) InitDefault() {
}

func (p *LarkAuthResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkAuthResponse(%+v)", *p)
}

type CheckLarkAuthRequest struct {
	// 请求url
	URL string `thrift:"URL,1,required" json:"url" query:"url,required" `
}

func NewCheckLarkAuthRequest() *CheckLarkAuthRequest {
	return &CheckLarkAuthRequest{}
}

func (p *CheckLarkAuthRequest) InitDefault() {
}

func (p *CheckLarkAuthRequest) GetURL() (v string) {
	return p.URL
}

func (p *CheckLarkAuthRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckLarkAuthRequest(%+v)", *p)
}

type CheckLarkAuthResponse struct {
	// 是否授权
	Authorization bool `thrift:"Authorization,1,required" json:"authorization" form:"authorization,required" `
	// 重定向url
	RedirectURL *string `thrift:"RedirectURL,2,optional" json:"redirect_url" form:"redirect_url" `
	// 是否拒绝授权
	AuthorizationDenied *bool `thrift:"AuthorizationDenied,3,optional" json:"authorization_denied" form:"authorization_denied" `
}

func NewCheckLarkAuthResponse() *CheckLarkAuthResponse {
	return &CheckLarkAuthResponse{}
}

func (p *CheckLarkAuthResponse) InitDefault() {
}

func (p *CheckLarkAuthResponse) GetAuthorization() (v bool) {
	return p.Authorization
}

var CheckLarkAuthResponse_RedirectURL_DEFAULT string

func (p *CheckLarkAuthResponse) GetRedirectURL() (v string) {
	if !p.IsSetRedirectURL() {
		return CheckLarkAuthResponse_RedirectURL_DEFAULT
	}
	return *p.RedirectURL
}

var CheckLarkAuthResponse_AuthorizationDenied_DEFAULT bool

func (p *CheckLarkAuthResponse) GetAuthorizationDenied() (v bool) {
	if !p.IsSetAuthorizationDenied() {
		return CheckLarkAuthResponse_AuthorizationDenied_DEFAULT
	}
	return *p.AuthorizationDenied
}

func (p *CheckLarkAuthResponse) IsSetRedirectURL() bool {
	return p.RedirectURL != nil
}

func (p *CheckLarkAuthResponse) IsSetAuthorizationDenied() bool {
	return p.AuthorizationDenied != nil
}

func (p *CheckLarkAuthResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckLarkAuthResponse(%+v)", *p)
}

type SendLarkReplayLinkMessageRequest struct {
	// 用户名
	Username string `thrift:"Username,1,required" json:"username" form:"username,required" `
	// 链接
	ReplayLink string `thrift:"ReplayLink,2,required" json:"replay_link" form:"replay_link,required" `
	// 任务名称
	TaskName string `thrift:"TaskName,3,required" json:"task_name" form:"task_name,required" `
	// 发送对象类型
	ToType string `thrift:"ToType,4,required" default:"" form:"to_type,required" json:"to_type,required" vd:"$=='user'||$=='group'"`
}

func NewSendLarkReplayLinkMessageRequest() *SendLarkReplayLinkMessageRequest {
	return &SendLarkReplayLinkMessageRequest{}
}

func (p *SendLarkReplayLinkMessageRequest) InitDefault() {
}

func (p *SendLarkReplayLinkMessageRequest) GetUsername() (v string) {
	return p.Username
}

func (p *SendLarkReplayLinkMessageRequest) GetReplayLink() (v string) {
	return p.ReplayLink
}

func (p *SendLarkReplayLinkMessageRequest) GetTaskName() (v string) {
	return p.TaskName
}

func (p *SendLarkReplayLinkMessageRequest) GetToType() (v string) {
	return p.ToType
}

func (p *SendLarkReplayLinkMessageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendLarkReplayLinkMessageRequest(%+v)", *p)
}

type SendLarkReplayLinkMessageResponse struct {
}

func NewSendLarkReplayLinkMessageResponse() *SendLarkReplayLinkMessageResponse {
	return &SendLarkReplayLinkMessageResponse{}
}

func (p *SendLarkReplayLinkMessageResponse) InitDefault() {
}

func (p *SendLarkReplayLinkMessageResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendLarkReplayLinkMessageResponse(%+v)", *p)
}

type GetLarkTicketRequest struct {
}

func NewGetLarkTicketRequest() *GetLarkTicketRequest {
	return &GetLarkTicketRequest{}
}

func (p *GetLarkTicketRequest) InitDefault() {
}

func (p *GetLarkTicketRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLarkTicketRequest(%+v)", *p)
}

type JSApiTicket struct {
	Ticket   string `thrift:"Ticket,1,required" json:"ticket"`
	ExpireIn int32  `thrift:"ExpireIn,2,required" json:"expire_in"`
}

func NewJSApiTicket() *JSApiTicket {
	return &JSApiTicket{}
}

func (p *JSApiTicket) InitDefault() {
}

func (p *JSApiTicket) GetTicket() (v string) {
	return p.Ticket
}

func (p *JSApiTicket) GetExpireIn() (v int32) {
	return p.ExpireIn
}

func (p *JSApiTicket) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("JSApiTicket(%+v)", *p)
}

type GetLarkTicketResponse struct {
	// 是否授权
	Authorization bool `thrift:"Authorization,1,required" json:"authorization" form:"authorization,required" `
	// jsapi_ticket
	JSApiTicket *JSApiTicket `thrift:"JSApiTicket,2,required" json:"jsapi_ticket" form:"jsapi_ticket,required" `
	// app_id
	AppID string `thrift:"AppID,3,required" json:"app_id" form:"app_id,required" `
	// open_id
	OpenID string `thrift:"OpenID,4,required" json:"open_id" form:"open_id,required" `
	// signature, 预留
	Signature *string `thrift:"Signature,5,optional" json:"signature" form:"signature" `
}

func NewGetLarkTicketResponse() *GetLarkTicketResponse {
	return &GetLarkTicketResponse{}
}

func (p *GetLarkTicketResponse) InitDefault() {
}

func (p *GetLarkTicketResponse) GetAuthorization() (v bool) {
	return p.Authorization
}

var GetLarkTicketResponse_JSApiTicket_DEFAULT *JSApiTicket

func (p *GetLarkTicketResponse) GetJSApiTicket() (v *JSApiTicket) {
	if !p.IsSetJSApiTicket() {
		return GetLarkTicketResponse_JSApiTicket_DEFAULT
	}
	return p.JSApiTicket
}

func (p *GetLarkTicketResponse) GetAppID() (v string) {
	return p.AppID
}

func (p *GetLarkTicketResponse) GetOpenID() (v string) {
	return p.OpenID
}

var GetLarkTicketResponse_Signature_DEFAULT string

func (p *GetLarkTicketResponse) GetSignature() (v string) {
	if !p.IsSetSignature() {
		return GetLarkTicketResponse_Signature_DEFAULT
	}
	return *p.Signature
}

func (p *GetLarkTicketResponse) IsSetJSApiTicket() bool {
	return p.JSApiTicket != nil
}

func (p *GetLarkTicketResponse) IsSetSignature() bool {
	return p.Signature != nil
}

func (p *GetLarkTicketResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLarkTicketResponse(%+v)", *p)
}

type GetUserLarkURLRequest struct {
	LarkURL   string `thrift:"LarkURL,1,required" json:"lark_url" query:"lark_url,required" `
	SessionID string `thrift:"SessionID,2,required" json:"session_id" query:"session_id,required" `
}

func NewGetUserLarkURLRequest() *GetUserLarkURLRequest {
	return &GetUserLarkURLRequest{}
}

func (p *GetUserLarkURLRequest) InitDefault() {
}

func (p *GetUserLarkURLRequest) GetLarkURL() (v string) {
	return p.LarkURL
}

func (p *GetUserLarkURLRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *GetUserLarkURLRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserLarkURLRequest(%+v)", *p)
}

type GetUserLarkURLResponse struct {
	// 移动到用户空间的lark_url
	LarkURL string `thrift:"LarkURL,1,required" json:"lark_url" form:"lark_url,required" `
}

func NewGetUserLarkURLResponse() *GetUserLarkURLResponse {
	return &GetUserLarkURLResponse{}
}

func (p *GetUserLarkURLResponse) InitDefault() {
}

func (p *GetUserLarkURLResponse) GetLarkURL() (v string) {
	return p.LarkURL
}

func (p *GetUserLarkURLResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserLarkURLResponse(%+v)", *p)
}

type GetLarkDocxBlocksRequest struct {
	DocumentID string `thrift:"DocumentID,1,required" json:"document_id,required" path:"document_id,required"`
}

func NewGetLarkDocxBlocksRequest() *GetLarkDocxBlocksRequest {
	return &GetLarkDocxBlocksRequest{}
}

func (p *GetLarkDocxBlocksRequest) InitDefault() {
}

func (p *GetLarkDocxBlocksRequest) GetDocumentID() (v string) {
	return p.DocumentID
}

func (p *GetLarkDocxBlocksRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLarkDocxBlocksRequest(%+v)", *p)
}

// 查看Bot是否在群里
type BotCheckGroupMembershipRequest struct {
	ChatID string `thrift:"ChatID,1,required" json:"chat_id" query:"chat_id,required" `
}

func NewBotCheckGroupMembershipRequest() *BotCheckGroupMembershipRequest {
	return &BotCheckGroupMembershipRequest{}
}

func (p *BotCheckGroupMembershipRequest) InitDefault() {
}

func (p *BotCheckGroupMembershipRequest) GetChatID() (v string) {
	return p.ChatID
}

func (p *BotCheckGroupMembershipRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotCheckGroupMembershipRequest(%+v)", *p)
}

type BotCheckGroupMembershipResponse struct {
	IsInGroup bool `thrift:"IsInGroup,1,required" json:"is_in_group" form:"is_in_group,required" `
}

func NewBotCheckGroupMembershipResponse() *BotCheckGroupMembershipResponse {
	return &BotCheckGroupMembershipResponse{}
}

func (p *BotCheckGroupMembershipResponse) InitDefault() {
}

func (p *BotCheckGroupMembershipResponse) GetIsInGroup() (v bool) {
	return p.IsInGroup
}

func (p *BotCheckGroupMembershipResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotCheckGroupMembershipResponse(%+v)", *p)
}

// 获取Bot所在的群信息
type BotSearchGroupsRequest struct {
	Query     *string `thrift:"Query,1,optional" json:"query" query:"query" `
	PageToken *string `thrift:"PageToken,2,optional" json:"page_token" query:"page_token" `
	PageSize  *int32  `thrift:"PageSize,3,optional" json:"page_size" query:"page_size" `
}

func NewBotSearchGroupsRequest() *BotSearchGroupsRequest {
	return &BotSearchGroupsRequest{}
}

func (p *BotSearchGroupsRequest) InitDefault() {
}

var BotSearchGroupsRequest_Query_DEFAULT string

func (p *BotSearchGroupsRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return BotSearchGroupsRequest_Query_DEFAULT
	}
	return *p.Query
}

var BotSearchGroupsRequest_PageToken_DEFAULT string

func (p *BotSearchGroupsRequest) GetPageToken() (v string) {
	if !p.IsSetPageToken() {
		return BotSearchGroupsRequest_PageToken_DEFAULT
	}
	return *p.PageToken
}

var BotSearchGroupsRequest_PageSize_DEFAULT int32

func (p *BotSearchGroupsRequest) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return BotSearchGroupsRequest_PageSize_DEFAULT
	}
	return *p.PageSize
}

func (p *BotSearchGroupsRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *BotSearchGroupsRequest) IsSetPageToken() bool {
	return p.PageToken != nil
}

func (p *BotSearchGroupsRequest) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *BotSearchGroupsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotSearchGroupsRequest(%+v)", *p)
}

type GroupItems struct {
	Avatar      *string `thrift:"Avatar,1,optional" json:"avatar"`
	ChatId      *string `thrift:"ChatId,2,optional" json:"chat_id"`
	ChatStatus  *string `thrift:"ChatStatus,3,optional" json:"chat_status"`
	Description *string `thrift:"Description,4,optional" json:"description"`
	External    *bool   `thrift:"External,5,optional" json:"external"`
	Name        *string `thrift:"Name,6,optional" json:"name"`
	TenantKey   *string `thrift:"TenantKey,7,optional" json:"tenant_key"`
}

func NewGroupItems() *GroupItems {
	return &GroupItems{}
}

func (p *GroupItems) InitDefault() {
}

var GroupItems_Avatar_DEFAULT string

func (p *GroupItems) GetAvatar() (v string) {
	if !p.IsSetAvatar() {
		return GroupItems_Avatar_DEFAULT
	}
	return *p.Avatar
}

var GroupItems_ChatId_DEFAULT string

func (p *GroupItems) GetChatId() (v string) {
	if !p.IsSetChatId() {
		return GroupItems_ChatId_DEFAULT
	}
	return *p.ChatId
}

var GroupItems_ChatStatus_DEFAULT string

func (p *GroupItems) GetChatStatus() (v string) {
	if !p.IsSetChatStatus() {
		return GroupItems_ChatStatus_DEFAULT
	}
	return *p.ChatStatus
}

var GroupItems_Description_DEFAULT string

func (p *GroupItems) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return GroupItems_Description_DEFAULT
	}
	return *p.Description
}

var GroupItems_External_DEFAULT bool

func (p *GroupItems) GetExternal() (v bool) {
	if !p.IsSetExternal() {
		return GroupItems_External_DEFAULT
	}
	return *p.External
}

var GroupItems_Name_DEFAULT string

func (p *GroupItems) GetName() (v string) {
	if !p.IsSetName() {
		return GroupItems_Name_DEFAULT
	}
	return *p.Name
}

var GroupItems_TenantKey_DEFAULT string

func (p *GroupItems) GetTenantKey() (v string) {
	if !p.IsSetTenantKey() {
		return GroupItems_TenantKey_DEFAULT
	}
	return *p.TenantKey
}

func (p *GroupItems) IsSetAvatar() bool {
	return p.Avatar != nil
}

func (p *GroupItems) IsSetChatId() bool {
	return p.ChatId != nil
}

func (p *GroupItems) IsSetChatStatus() bool {
	return p.ChatStatus != nil
}

func (p *GroupItems) IsSetDescription() bool {
	return p.Description != nil
}

func (p *GroupItems) IsSetExternal() bool {
	return p.External != nil
}

func (p *GroupItems) IsSetName() bool {
	return p.Name != nil
}

func (p *GroupItems) IsSetTenantKey() bool {
	return p.TenantKey != nil
}

func (p *GroupItems) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GroupItems(%+v)", *p)
}

type BotSearchGroupsResponse struct {
	Groups        []*GroupItems `thrift:"Groups,1,required" json:"groups" form:"groups,required" `
	HasMore       *bool         `thrift:"HasMore,2,optional" json:"has_more" form:"has_more" `
	NextPageToken *string       `thrift:"NextPageToken,3,optional" json:"next_page_token" form:"next_page_token" `
}

func NewBotSearchGroupsResponse() *BotSearchGroupsResponse {
	return &BotSearchGroupsResponse{}
}

func (p *BotSearchGroupsResponse) InitDefault() {
}

func (p *BotSearchGroupsResponse) GetGroups() (v []*GroupItems) {
	return p.Groups
}

var BotSearchGroupsResponse_HasMore_DEFAULT bool

func (p *BotSearchGroupsResponse) GetHasMore() (v bool) {
	if !p.IsSetHasMore() {
		return BotSearchGroupsResponse_HasMore_DEFAULT
	}
	return *p.HasMore
}

var BotSearchGroupsResponse_NextPageToken_DEFAULT string

func (p *BotSearchGroupsResponse) GetNextPageToken() (v string) {
	if !p.IsSetNextPageToken() {
		return BotSearchGroupsResponse_NextPageToken_DEFAULT
	}
	return *p.NextPageToken
}

func (p *BotSearchGroupsResponse) IsSetHasMore() bool {
	return p.HasMore != nil
}

func (p *BotSearchGroupsResponse) IsSetNextPageToken() bool {
	return p.NextPageToken != nil
}

func (p *BotSearchGroupsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotSearchGroupsResponse(%+v)", *p)
}

// 获取Bot所在的群信息
type BotCreateGroupRequest struct {
	UserID    int64   `thrift:"UserID,1,required" json:"user_id" query:"user_id,required" `
	GroupName *string `thrift:"GroupName,2,optional" json:"group_name" query:"group_name" `
}

func NewBotCreateGroupRequest() *BotCreateGroupRequest {
	return &BotCreateGroupRequest{}
}

func (p *BotCreateGroupRequest) InitDefault() {
}

func (p *BotCreateGroupRequest) GetUserID() (v int64) {
	return p.UserID
}

var BotCreateGroupRequest_GroupName_DEFAULT string

func (p *BotCreateGroupRequest) GetGroupName() (v string) {
	if !p.IsSetGroupName() {
		return BotCreateGroupRequest_GroupName_DEFAULT
	}
	return *p.GroupName
}

func (p *BotCreateGroupRequest) IsSetGroupName() bool {
	return p.GroupName != nil
}

func (p *BotCreateGroupRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotCreateGroupRequest(%+v)", *p)
}

type BotCreateGroupResponse struct {
}

func NewBotCreateGroupResponse() *BotCreateGroupResponse {
	return &BotCreateGroupResponse{}
}

func (p *BotCreateGroupResponse) InitDefault() {
}

func (p *BotCreateGroupResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BotCreateGroupResponse(%+v)", *p)
}

type ListLarkDocCommentsRequest struct {
	SessionID string `thrift:"SessionID,1,required" json:"session_id,required" query:"session_id,required"`
}

func NewListLarkDocCommentsRequest() *ListLarkDocCommentsRequest {
	return &ListLarkDocCommentsRequest{}
}

func (p *ListLarkDocCommentsRequest) InitDefault() {
}

func (p *ListLarkDocCommentsRequest) GetSessionID() (v string) {
	return p.SessionID
}

func (p *ListLarkDocCommentsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListLarkDocCommentsRequest(%+v)", *p)
}

type LarkDocCommentItem struct {
	ArtifactID string `thrift:"ArtifactID,1,required" json:"artifact_id"`
	// 产物名字
	Name string `thrift:"Name,2,required" json:"name"`
	// 飞书文档 url
	LarkDocUrl string             `thrift:"LarkDocUrl,3,required" json:"lark_doc_url"`
	Comments   []*CommentMetadata `thrift:"Comments,4,required" json:"comments"`
	Version    int32              `thrift:"Version,5,required" json:"version"`
}

func NewLarkDocCommentItem() *LarkDocCommentItem {
	return &LarkDocCommentItem{}
}

func (p *LarkDocCommentItem) InitDefault() {
}

func (p *LarkDocCommentItem) GetArtifactID() (v string) {
	return p.ArtifactID
}

func (p *LarkDocCommentItem) GetName() (v string) {
	return p.Name
}

func (p *LarkDocCommentItem) GetLarkDocUrl() (v string) {
	return p.LarkDocUrl
}

func (p *LarkDocCommentItem) GetComments() (v []*CommentMetadata) {
	return p.Comments
}

func (p *LarkDocCommentItem) GetVersion() (v int32) {
	return p.Version
}

func (p *LarkDocCommentItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LarkDocCommentItem(%+v)", *p)
}

type ListLarkDocCommentsResponse struct {
	LarkDocComments []*LarkDocCommentItem `thrift:"LarkDocComments,1,required" json:"lark_doc_comments"`
}

func NewListLarkDocCommentsResponse() *ListLarkDocCommentsResponse {
	return &ListLarkDocCommentsResponse{}
}

func (p *ListLarkDocCommentsResponse) InitDefault() {
}

func (p *ListLarkDocCommentsResponse) GetLarkDocComments() (v []*LarkDocCommentItem) {
	return p.LarkDocComments
}

func (p *ListLarkDocCommentsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListLarkDocCommentsResponse(%+v)", *p)
}
