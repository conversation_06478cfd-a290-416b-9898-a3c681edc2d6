// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"fmt"
)

type BPMAuthCallbackRequest struct {
	ID                   string `thrift:"ID,1,required" json:"id"`
	WorkflowConfigID     string `thrift:"WorkflowConfigID,2,required" json:"workflow_config_id"`
	AgentConfigID        string `thrift:"AgentConfigID,3,required" json:"agent_config_id"`
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,4,required" json:"agent_config_version_id"`
	Creator              string `thrift:"Creator,5,required" json:"creator"`
}

func NewBPMAuthCallbackRequest() *BPMAuthCallbackRequest {
	return &BPMAuthCallbackRequest{}
}

func (p *BPMAuthCallbackRequest) InitDefault() {
}

func (p *BPMAuthCallbackRequest) GetID() (v string) {
	return p.ID
}

func (p *BPMAuthCallbackRequest) GetWorkflowConfigID() (v string) {
	return p.WorkflowConfigID
}

func (p *BPMAuthCallbackRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *BPMAuthCallbackRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *BPMAuthCallbackRequest) GetCreator() (v string) {
	return p.Creator
}

func (p *BPMAuthCallbackRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMAuthCallbackRequest(%+v)", *p)
}

type BPMAuthCallbackResponseData struct {
	AuthResult bool `thrift:"AuthResult,1,required" json:"auth_result"`
}

func NewBPMAuthCallbackResponseData() *BPMAuthCallbackResponseData {
	return &BPMAuthCallbackResponseData{}
}

func (p *BPMAuthCallbackResponseData) InitDefault() {
}

func (p *BPMAuthCallbackResponseData) GetAuthResult() (v bool) {
	return p.AuthResult
}

func (p *BPMAuthCallbackResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMAuthCallbackResponseData(%+v)", *p)
}

type BPMAuthCallbackResponse struct {
	Code int32                        `thrift:"Code,1,required" json:"id"`
	Data *BPMAuthCallbackResponseData `thrift:"Data,2,required" json:"data"`
}

func NewBPMAuthCallbackResponse() *BPMAuthCallbackResponse {
	return &BPMAuthCallbackResponse{}
}

func (p *BPMAuthCallbackResponse) InitDefault() {
}

func (p *BPMAuthCallbackResponse) GetCode() (v int32) {
	return p.Code
}

var BPMAuthCallbackResponse_Data_DEFAULT *BPMAuthCallbackResponseData

func (p *BPMAuthCallbackResponse) GetData() (v *BPMAuthCallbackResponseData) {
	if !p.IsSetData() {
		return BPMAuthCallbackResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *BPMAuthCallbackResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *BPMAuthCallbackResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMAuthCallbackResponse(%+v)", *p)
}

type BPMCloseCallbackRequest struct {
	ID                   int64   `thrift:"ID,1,required" json:"id"`
	DeployID             string  `thrift:"DeployID,2,required" json:"deploy_id"`
	AgentConfigID        string  `thrift:"AgentConfigID,3,required" json:"agent_config_id"`
	AgentConfigVersionID string  `thrift:"AgentConfigVersionID,4,required" json:"agent_config_version_id"`
	Comment              *string `thrift:"Comment,5,optional" json:"comment"`
	AuditRejectComment   *string `thrift:"AuditRejectComment,6,optional" json:"audit_reject_comment"`
}

func NewBPMCloseCallbackRequest() *BPMCloseCallbackRequest {
	return &BPMCloseCallbackRequest{}
}

func (p *BPMCloseCallbackRequest) InitDefault() {
}

func (p *BPMCloseCallbackRequest) GetID() (v int64) {
	return p.ID
}

func (p *BPMCloseCallbackRequest) GetDeployID() (v string) {
	return p.DeployID
}

func (p *BPMCloseCallbackRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *BPMCloseCallbackRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

var BPMCloseCallbackRequest_Comment_DEFAULT string

func (p *BPMCloseCallbackRequest) GetComment() (v string) {
	if !p.IsSetComment() {
		return BPMCloseCallbackRequest_Comment_DEFAULT
	}
	return *p.Comment
}

var BPMCloseCallbackRequest_AuditRejectComment_DEFAULT string

func (p *BPMCloseCallbackRequest) GetAuditRejectComment() (v string) {
	if !p.IsSetAuditRejectComment() {
		return BPMCloseCallbackRequest_AuditRejectComment_DEFAULT
	}
	return *p.AuditRejectComment
}

func (p *BPMCloseCallbackRequest) IsSetComment() bool {
	return p.Comment != nil
}

func (p *BPMCloseCallbackRequest) IsSetAuditRejectComment() bool {
	return p.AuditRejectComment != nil
}

func (p *BPMCloseCallbackRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMCloseCallbackRequest(%+v)", *p)
}

type BPMCloseCallbackResponse struct {
	Code int32 `thrift:"Code,1,required" json:"code"`
}

func NewBPMCloseCallbackResponse() *BPMCloseCallbackResponse {
	return &BPMCloseCallbackResponse{}
}

func (p *BPMCloseCallbackResponse) InitDefault() {
}

func (p *BPMCloseCallbackResponse) GetCode() (v int32) {
	return p.Code
}

func (p *BPMCloseCallbackResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMCloseCallbackResponse(%+v)", *p)
}

type BPMCancelCanaryCallbackRequest struct {
	ID                   int64  `thrift:"ID,1,required" json:"id"`
	DeployID             string `thrift:"DeployID,2,required" json:"deploy_id"`
	AgentConfigID        string `thrift:"AgentConfigID,3,required" json:"agent_config_id"`
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,4,required" json:"agent_config_version_id"`
}

func NewBPMCancelCanaryCallbackRequest() *BPMCancelCanaryCallbackRequest {
	return &BPMCancelCanaryCallbackRequest{}
}

func (p *BPMCancelCanaryCallbackRequest) InitDefault() {
}

func (p *BPMCancelCanaryCallbackRequest) GetID() (v int64) {
	return p.ID
}

func (p *BPMCancelCanaryCallbackRequest) GetDeployID() (v string) {
	return p.DeployID
}

func (p *BPMCancelCanaryCallbackRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *BPMCancelCanaryCallbackRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *BPMCancelCanaryCallbackRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMCancelCanaryCallbackRequest(%+v)", *p)
}

type BPMCancelCanaryCallbackResponse struct {
	Code int32 `thrift:"Code,1,required" json:"code"`
}

func NewBPMCancelCanaryCallbackResponse() *BPMCancelCanaryCallbackResponse {
	return &BPMCancelCanaryCallbackResponse{}
}

func (p *BPMCancelCanaryCallbackResponse) InitDefault() {
}

func (p *BPMCancelCanaryCallbackResponse) GetCode() (v int32) {
	return p.Code
}

func (p *BPMCancelCanaryCallbackResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMCancelCanaryCallbackResponse(%+v)", *p)
}

type BPMCanaryCallbackRequest struct {
	ID                   int64  `thrift:"ID,1,required" json:"id"`
	DeployID             string `thrift:"DeployID,2,required" json:"deploy_id"`
	AgentConfigID        string `thrift:"AgentConfigID,3,required" json:"agent_config_id"`
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,4,required" json:"agent_config_version_id"`
	Status               string `thrift:"Status,5,required" json:"status"`
}

func NewBPMCanaryCallbackRequest() *BPMCanaryCallbackRequest {
	return &BPMCanaryCallbackRequest{}
}

func (p *BPMCanaryCallbackRequest) InitDefault() {
}

func (p *BPMCanaryCallbackRequest) GetID() (v int64) {
	return p.ID
}

func (p *BPMCanaryCallbackRequest) GetDeployID() (v string) {
	return p.DeployID
}

func (p *BPMCanaryCallbackRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *BPMCanaryCallbackRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *BPMCanaryCallbackRequest) GetStatus() (v string) {
	return p.Status
}

func (p *BPMCanaryCallbackRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMCanaryCallbackRequest(%+v)", *p)
}

type BPMCanaryCallbackResponse struct {
	Code int32 `thrift:"Code,1,required" json:"code"`
}

func NewBPMCanaryCallbackResponse() *BPMCanaryCallbackResponse {
	return &BPMCanaryCallbackResponse{}
}

func (p *BPMCanaryCallbackResponse) InitDefault() {
}

func (p *BPMCanaryCallbackResponse) GetCode() (v int32) {
	return p.Code
}

func (p *BPMCanaryCallbackResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMCanaryCallbackResponse(%+v)", *p)
}

type BPMOnlineCallbackRequest struct {
	ID                   int64   `thrift:"ID,1,required" json:"id"`
	DeployID             string  `thrift:"DeployID,2,required" json:"deploy_id"`
	AgentConfigID        string  `thrift:"AgentConfigID,3,required" json:"agent_config_id"`
	AgentConfigVersionID string  `thrift:"AgentConfigVersionID,4,required" json:"agent_config_version_id"`
	SkipCanaryComment    *string `thrift:"SkipCanaryComment,5,optional" json:"skip_canary_comment"`
}

func NewBPMOnlineCallbackRequest() *BPMOnlineCallbackRequest {
	return &BPMOnlineCallbackRequest{}
}

func (p *BPMOnlineCallbackRequest) InitDefault() {
}

func (p *BPMOnlineCallbackRequest) GetID() (v int64) {
	return p.ID
}

func (p *BPMOnlineCallbackRequest) GetDeployID() (v string) {
	return p.DeployID
}

func (p *BPMOnlineCallbackRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *BPMOnlineCallbackRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

var BPMOnlineCallbackRequest_SkipCanaryComment_DEFAULT string

func (p *BPMOnlineCallbackRequest) GetSkipCanaryComment() (v string) {
	if !p.IsSetSkipCanaryComment() {
		return BPMOnlineCallbackRequest_SkipCanaryComment_DEFAULT
	}
	return *p.SkipCanaryComment
}

func (p *BPMOnlineCallbackRequest) IsSetSkipCanaryComment() bool {
	return p.SkipCanaryComment != nil
}

func (p *BPMOnlineCallbackRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMOnlineCallbackRequest(%+v)", *p)
}

type BPMOnlineCallbackResponseData struct {
	OnlineResult bool `thrift:"OnlineResult,1,required" json:"online_result"`
}

func NewBPMOnlineCallbackResponseData() *BPMOnlineCallbackResponseData {
	return &BPMOnlineCallbackResponseData{}
}

func (p *BPMOnlineCallbackResponseData) InitDefault() {
}

func (p *BPMOnlineCallbackResponseData) GetOnlineResult() (v bool) {
	return p.OnlineResult
}

func (p *BPMOnlineCallbackResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMOnlineCallbackResponseData(%+v)", *p)
}

type BPMOnlineCallbackResponse struct {
	Code int32                          `thrift:"Code,1,required" json:"code"`
	Data *BPMOnlineCallbackResponseData `thrift:"Data,2,required" json:"data"`
}

func NewBPMOnlineCallbackResponse() *BPMOnlineCallbackResponse {
	return &BPMOnlineCallbackResponse{}
}

func (p *BPMOnlineCallbackResponse) InitDefault() {
}

func (p *BPMOnlineCallbackResponse) GetCode() (v int32) {
	return p.Code
}

var BPMOnlineCallbackResponse_Data_DEFAULT *BPMOnlineCallbackResponseData

func (p *BPMOnlineCallbackResponse) GetData() (v *BPMOnlineCallbackResponseData) {
	if !p.IsSetData() {
		return BPMOnlineCallbackResponse_Data_DEFAULT
	}
	return p.Data
}

func (p *BPMOnlineCallbackResponse) IsSetData() bool {
	return p.Data != nil
}

func (p *BPMOnlineCallbackResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BPMOnlineCallbackResponse(%+v)", *p)
}

type CreateDeployRequest struct {
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,1,required" json:"agent_config_version_id"`
	Reviewer             string `thrift:"Reviewer,2,required" json:"reviewer"`
	EnableAB             bool   `thrift:"EnableAB,3,required" json:"is_enable_ab"`
	ABComment            string `thrift:"ABComment,4,required" json:"ab_comment"`
}

func NewCreateDeployRequest() *CreateDeployRequest {
	return &CreateDeployRequest{}
}

func (p *CreateDeployRequest) InitDefault() {
}

func (p *CreateDeployRequest) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *CreateDeployRequest) GetReviewer() (v string) {
	return p.Reviewer
}

func (p *CreateDeployRequest) GetEnableAB() (v bool) {
	return p.EnableAB
}

func (p *CreateDeployRequest) GetABComment() (v string) {
	return p.ABComment
}

func (p *CreateDeployRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDeployRequest(%+v)", *p)
}

type CreateDeployResponse struct {
	DeployID   string `thrift:"DeployID,1,required" json:"deploy_id"`
	WorkflowID int64  `thrift:"WorkflowID,2,required" json:"workflow_id"`
}

func NewCreateDeployResponse() *CreateDeployResponse {
	return &CreateDeployResponse{}
}

func (p *CreateDeployResponse) InitDefault() {
}

func (p *CreateDeployResponse) GetDeployID() (v string) {
	return p.DeployID
}

func (p *CreateDeployResponse) GetWorkflowID() (v int64) {
	return p.WorkflowID
}

func (p *CreateDeployResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateDeployResponse(%+v)", *p)
}

type GetDeployProcessInfoRequest struct {
	DeployID string `thrift:"DeployID,1,required" json:"deploy_id,required" query:"deploy_id,required"`
}

func NewGetDeployProcessInfoRequest() *GetDeployProcessInfoRequest {
	return &GetDeployProcessInfoRequest{}
}

func (p *GetDeployProcessInfoRequest) InitDefault() {
}

func (p *GetDeployProcessInfoRequest) GetDeployID() (v string) {
	return p.DeployID
}

func (p *GetDeployProcessInfoRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeployProcessInfoRequest(%+v)", *p)
}

type GetDeployProcessInfoResponse struct {
	CanaryRatio  float64 `thrift:"CanaryRatio,1,required" json:"canary_ratio"`
	SuccessCount int64   `thrift:"SuccessCount,2,required" json:"success_count"`
	FailCount    int64   `thrift:"FailCount,3,required" json:"fail_count"`
	RunningCount int64   `thrift:"RunningCount,4,required" json:"running_count"`
	DeployStatus string  `thrift:"DeployStatus,5,required" json:"deploy_status"`
}

func NewGetDeployProcessInfoResponse() *GetDeployProcessInfoResponse {
	return &GetDeployProcessInfoResponse{}
}

func (p *GetDeployProcessInfoResponse) InitDefault() {
}

func (p *GetDeployProcessInfoResponse) GetCanaryRatio() (v float64) {
	return p.CanaryRatio
}

func (p *GetDeployProcessInfoResponse) GetSuccessCount() (v int64) {
	return p.SuccessCount
}

func (p *GetDeployProcessInfoResponse) GetFailCount() (v int64) {
	return p.FailCount
}

func (p *GetDeployProcessInfoResponse) GetRunningCount() (v int64) {
	return p.RunningCount
}

func (p *GetDeployProcessInfoResponse) GetDeployStatus() (v string) {
	return p.DeployStatus
}

func (p *GetDeployProcessInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeployProcessInfoResponse(%+v)", *p)
}

type GetDeployRequest struct {
	DeployID string `thrift:"DeployID,1,required" json:"deploy_id,required" path:"deploy_id,required"`
}

func NewGetDeployRequest() *GetDeployRequest {
	return &GetDeployRequest{}
}

func (p *GetDeployRequest) InitDefault() {
}

func (p *GetDeployRequest) GetDeployID() (v string) {
	return p.DeployID
}

func (p *GetDeployRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeployRequest(%+v)", *p)
}

type AgentConfigVersionInfo struct {
	ID          string `thrift:"ID,1,required" json:"id"`
	Version     int32  `thrift:"Version,2,required" json:"version"`
	Description string `thrift:"Description,3,required" json:"description"`
}

func NewAgentConfigVersionInfo() *AgentConfigVersionInfo {
	return &AgentConfigVersionInfo{}
}

func (p *AgentConfigVersionInfo) InitDefault() {
}

func (p *AgentConfigVersionInfo) GetID() (v string) {
	return p.ID
}

func (p *AgentConfigVersionInfo) GetVersion() (v int32) {
	return p.Version
}

func (p *AgentConfigVersionInfo) GetDescription() (v string) {
	return p.Description
}

func (p *AgentConfigVersionInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentConfigVersionInfo(%+v)", *p)
}

type AgentDeploy struct {
	ID                           string                  `thrift:"ID,1,required" json:"id"`
	AgentConfigID                string                  `thrift:"AgentConfigID,2,required" json:"agent_config_id"`
	AgentConfigVersionInfo       *AgentConfigVersionInfo `thrift:"AgentConfigVersionInfo,3,required" json:"agent_config_version_info"`
	WorkflowID                   int64                   `thrift:"WorkflowID,4,required" json:"workflow_id"`
	ExtraInfo                    *DeployExtraInfo        `thrift:"ExtraInfo,5,optional" json:"extra_info"`
	CreatedAt                    string                  `thrift:"CreatedAt,6,required" json:"created_at"`
	Status                       string                  `thrift:"Status,7,required" json:"status"`
	AgentConfigVersionOnlineInfo *AgentConfigVersionInfo `thrift:"AgentConfigVersionOnlineInfo,8,optional" json:"agent_config_version_online_info"`
	Actor                        string                  `thrift:"Actor,9,required" json:"actor"`
}

func NewAgentDeploy() *AgentDeploy {
	return &AgentDeploy{}
}

func (p *AgentDeploy) InitDefault() {
}

func (p *AgentDeploy) GetID() (v string) {
	return p.ID
}

func (p *AgentDeploy) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

var AgentDeploy_AgentConfigVersionInfo_DEFAULT *AgentConfigVersionInfo

func (p *AgentDeploy) GetAgentConfigVersionInfo() (v *AgentConfigVersionInfo) {
	if !p.IsSetAgentConfigVersionInfo() {
		return AgentDeploy_AgentConfigVersionInfo_DEFAULT
	}
	return p.AgentConfigVersionInfo
}

func (p *AgentDeploy) GetWorkflowID() (v int64) {
	return p.WorkflowID
}

var AgentDeploy_ExtraInfo_DEFAULT *DeployExtraInfo

func (p *AgentDeploy) GetExtraInfo() (v *DeployExtraInfo) {
	if !p.IsSetExtraInfo() {
		return AgentDeploy_ExtraInfo_DEFAULT
	}
	return p.ExtraInfo
}

func (p *AgentDeploy) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *AgentDeploy) GetStatus() (v string) {
	return p.Status
}

var AgentDeploy_AgentConfigVersionOnlineInfo_DEFAULT *AgentConfigVersionInfo

func (p *AgentDeploy) GetAgentConfigVersionOnlineInfo() (v *AgentConfigVersionInfo) {
	if !p.IsSetAgentConfigVersionOnlineInfo() {
		return AgentDeploy_AgentConfigVersionOnlineInfo_DEFAULT
	}
	return p.AgentConfigVersionOnlineInfo
}

func (p *AgentDeploy) GetActor() (v string) {
	return p.Actor
}

func (p *AgentDeploy) IsSetAgentConfigVersionInfo() bool {
	return p.AgentConfigVersionInfo != nil
}

func (p *AgentDeploy) IsSetExtraInfo() bool {
	return p.ExtraInfo != nil
}

func (p *AgentDeploy) IsSetAgentConfigVersionOnlineInfo() bool {
	return p.AgentConfigVersionOnlineInfo != nil
}

func (p *AgentDeploy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentDeploy(%+v)", *p)
}

type DeployExtraInfo struct {
	Reviewer           *string `thrift:"Reviewer,1,optional" json:"reviewer"`
	EnableAB           *bool   `thrift:"EnableAB,2,optional" json:"enable_ab"`
	ABComment          *string `thrift:"ABComment,3,optional" json:"ab_comment"`
	SkipCanary         *bool   `thrift:"SkipCanary,4,optional" json:"skip_canary"`
	SkipCanaryComment  *string `thrift:"SkipCanaryComment,5,optional" json:"skip_canary_comment"`
	CloseReason        *string `thrift:"CloseReason,6,optional" json:"close_reason"`
	AuditRejectComment *string `thrift:"AuditRejectComment,7,optional" json:"audit_reject_comment"`
}

func NewDeployExtraInfo() *DeployExtraInfo {
	return &DeployExtraInfo{}
}

func (p *DeployExtraInfo) InitDefault() {
}

var DeployExtraInfo_Reviewer_DEFAULT string

func (p *DeployExtraInfo) GetReviewer() (v string) {
	if !p.IsSetReviewer() {
		return DeployExtraInfo_Reviewer_DEFAULT
	}
	return *p.Reviewer
}

var DeployExtraInfo_EnableAB_DEFAULT bool

func (p *DeployExtraInfo) GetEnableAB() (v bool) {
	if !p.IsSetEnableAB() {
		return DeployExtraInfo_EnableAB_DEFAULT
	}
	return *p.EnableAB
}

var DeployExtraInfo_ABComment_DEFAULT string

func (p *DeployExtraInfo) GetABComment() (v string) {
	if !p.IsSetABComment() {
		return DeployExtraInfo_ABComment_DEFAULT
	}
	return *p.ABComment
}

var DeployExtraInfo_SkipCanary_DEFAULT bool

func (p *DeployExtraInfo) GetSkipCanary() (v bool) {
	if !p.IsSetSkipCanary() {
		return DeployExtraInfo_SkipCanary_DEFAULT
	}
	return *p.SkipCanary
}

var DeployExtraInfo_SkipCanaryComment_DEFAULT string

func (p *DeployExtraInfo) GetSkipCanaryComment() (v string) {
	if !p.IsSetSkipCanaryComment() {
		return DeployExtraInfo_SkipCanaryComment_DEFAULT
	}
	return *p.SkipCanaryComment
}

var DeployExtraInfo_CloseReason_DEFAULT string

func (p *DeployExtraInfo) GetCloseReason() (v string) {
	if !p.IsSetCloseReason() {
		return DeployExtraInfo_CloseReason_DEFAULT
	}
	return *p.CloseReason
}

var DeployExtraInfo_AuditRejectComment_DEFAULT string

func (p *DeployExtraInfo) GetAuditRejectComment() (v string) {
	if !p.IsSetAuditRejectComment() {
		return DeployExtraInfo_AuditRejectComment_DEFAULT
	}
	return *p.AuditRejectComment
}

func (p *DeployExtraInfo) IsSetReviewer() bool {
	return p.Reviewer != nil
}

func (p *DeployExtraInfo) IsSetEnableAB() bool {
	return p.EnableAB != nil
}

func (p *DeployExtraInfo) IsSetABComment() bool {
	return p.ABComment != nil
}

func (p *DeployExtraInfo) IsSetSkipCanary() bool {
	return p.SkipCanary != nil
}

func (p *DeployExtraInfo) IsSetSkipCanaryComment() bool {
	return p.SkipCanaryComment != nil
}

func (p *DeployExtraInfo) IsSetCloseReason() bool {
	return p.CloseReason != nil
}

func (p *DeployExtraInfo) IsSetAuditRejectComment() bool {
	return p.AuditRejectComment != nil
}

func (p *DeployExtraInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeployExtraInfo(%+v)", *p)
}

type GetDeployResponse struct {
	Deploy *AgentDeploy `thrift:"Deploy,1,required" json:"deploy"`
}

func NewGetDeployResponse() *GetDeployResponse {
	return &GetDeployResponse{}
}

func (p *GetDeployResponse) InitDefault() {
}

var GetDeployResponse_Deploy_DEFAULT *AgentDeploy

func (p *GetDeployResponse) GetDeploy() (v *AgentDeploy) {
	if !p.IsSetDeploy() {
		return GetDeployResponse_Deploy_DEFAULT
	}
	return p.Deploy
}

func (p *GetDeployResponse) IsSetDeploy() bool {
	return p.Deploy != nil
}

func (p *GetDeployResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeployResponse(%+v)", *p)
}

type GetScmVersionRequest struct {
	Branch *string `thrift:"Branch,1,optional" json:"branch,omitempty" query:"branch"`
	// 逗号分隔, online/offline/test
	TypeList *string `thrift:"TypeList,2,optional" json:"type_list,omitempty" query:"type_list"`
	Version  *string `thrift:"Version,3,optional" json:"version,omitempty" query:"version"`
	Commit   *string `thrift:"Commit,4,optional" json:"commit,omitempty" query:"commit"`
}

func NewGetScmVersionRequest() *GetScmVersionRequest {
	return &GetScmVersionRequest{}
}

func (p *GetScmVersionRequest) InitDefault() {
}

var GetScmVersionRequest_Branch_DEFAULT string

func (p *GetScmVersionRequest) GetBranch() (v string) {
	if !p.IsSetBranch() {
		return GetScmVersionRequest_Branch_DEFAULT
	}
	return *p.Branch
}

var GetScmVersionRequest_TypeList_DEFAULT string

func (p *GetScmVersionRequest) GetTypeList() (v string) {
	if !p.IsSetTypeList() {
		return GetScmVersionRequest_TypeList_DEFAULT
	}
	return *p.TypeList
}

var GetScmVersionRequest_Version_DEFAULT string

func (p *GetScmVersionRequest) GetVersion() (v string) {
	if !p.IsSetVersion() {
		return GetScmVersionRequest_Version_DEFAULT
	}
	return *p.Version
}

var GetScmVersionRequest_Commit_DEFAULT string

func (p *GetScmVersionRequest) GetCommit() (v string) {
	if !p.IsSetCommit() {
		return GetScmVersionRequest_Commit_DEFAULT
	}
	return *p.Commit
}

func (p *GetScmVersionRequest) IsSetBranch() bool {
	return p.Branch != nil
}

func (p *GetScmVersionRequest) IsSetTypeList() bool {
	return p.TypeList != nil
}

func (p *GetScmVersionRequest) IsSetVersion() bool {
	return p.Version != nil
}

func (p *GetScmVersionRequest) IsSetCommit() bool {
	return p.Commit != nil
}

func (p *GetScmVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetScmVersionRequest(%+v)", *p)
}

type ScmVersion struct {
	Version              string   `thrift:"Version,1,required" json:"version"`
	Arch                 []string `thrift:"Arch,2,required" json:"arch"`
	Type                 string   `thrift:"Type,3,required" json:"type"`
	CreateUser           string   `thrift:"CreateUser,4,required" json:"create_user"`
	GitURL               string   `thrift:"GitURL,5,required" json:"git_url"`
	Desc                 string   `thrift:"Desc,6,required" json:"desc"`
	BranchName           string   `thrift:"BranchName,7,required" json:"branch_name"`
	BaseCommitHash       string   `thrift:"BaseCommitHash,8,required" json:"base_commit_hash"`
	LocalDate            string   `thrift:"LocalDate,9,required" json:"local_date"`
	CommitURL            string   `thrift:"CommitURL,10,required" json:"commit_url"`
	Status               string   `thrift:"Status,11,required" json:"status"`
	StatusAarch64        string   `thrift:"StatusAarch64,12,required" json:"status_aarch64"`
	CreateDate           string   `thrift:"CreateDate,13,required" json:"create_date"`
	StatusDisplay        string   `thrift:"StatusDisplay,14,required" json:"status_display"`
	StatusDisplayAarch64 string   `thrift:"StatusDisplayAarch64,15,required" json:"status_display_aarch64"`
	ID                   int32    `thrift:"ID,16,required" json:"id"`
	GitTag               *string  `thrift:"GitTag,17,optional" json:"git_tag"`
	Tags                 []string `thrift:"Tags,18,required" json:"tags"`
}

func NewScmVersion() *ScmVersion {
	return &ScmVersion{}
}

func (p *ScmVersion) InitDefault() {
}

func (p *ScmVersion) GetVersion() (v string) {
	return p.Version
}

func (p *ScmVersion) GetArch() (v []string) {
	return p.Arch
}

func (p *ScmVersion) GetType() (v string) {
	return p.Type
}

func (p *ScmVersion) GetCreateUser() (v string) {
	return p.CreateUser
}

func (p *ScmVersion) GetGitURL() (v string) {
	return p.GitURL
}

func (p *ScmVersion) GetDesc() (v string) {
	return p.Desc
}

func (p *ScmVersion) GetBranchName() (v string) {
	return p.BranchName
}

func (p *ScmVersion) GetBaseCommitHash() (v string) {
	return p.BaseCommitHash
}

func (p *ScmVersion) GetLocalDate() (v string) {
	return p.LocalDate
}

func (p *ScmVersion) GetCommitURL() (v string) {
	return p.CommitURL
}

func (p *ScmVersion) GetStatus() (v string) {
	return p.Status
}

func (p *ScmVersion) GetStatusAarch64() (v string) {
	return p.StatusAarch64
}

func (p *ScmVersion) GetCreateDate() (v string) {
	return p.CreateDate
}

func (p *ScmVersion) GetStatusDisplay() (v string) {
	return p.StatusDisplay
}

func (p *ScmVersion) GetStatusDisplayAarch64() (v string) {
	return p.StatusDisplayAarch64
}

func (p *ScmVersion) GetID() (v int32) {
	return p.ID
}

var ScmVersion_GitTag_DEFAULT string

func (p *ScmVersion) GetGitTag() (v string) {
	if !p.IsSetGitTag() {
		return ScmVersion_GitTag_DEFAULT
	}
	return *p.GitTag
}

func (p *ScmVersion) GetTags() (v []string) {
	return p.Tags
}

func (p *ScmVersion) IsSetGitTag() bool {
	return p.GitTag != nil
}

func (p *ScmVersion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ScmVersion(%+v)", *p)
}

type GetScmVersionResponse struct {
	ScmVersions []*ScmVersion `thrift:"ScmVersions,1,required" json:"scm_versions"`
}

func NewGetScmVersionResponse() *GetScmVersionResponse {
	return &GetScmVersionResponse{}
}

func (p *GetScmVersionResponse) InitDefault() {
}

func (p *GetScmVersionResponse) GetScmVersions() (v []*ScmVersion) {
	return p.ScmVersions
}

func (p *GetScmVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetScmVersionResponse(%+v)", *p)
}

type GetIcmVersionRequest struct {
	Version     *string `thrift:"Version,1,optional" json:"version,omitempty" query:"version"`
	Region      *string `thrift:"Region,2,optional" json:"region,omitempty" query:"region"`
	SpecificTag *string `thrift:"SpecificTag,3,optional" json:"specific_tag,omitempty" query:"specific_tag"`
	// agent config type, base/abtest/feature
	ConfigType *string `thrift:"ConfigType,4,optional" json:"config_type,omitempty" query:"config_type"`
	ImageType  *string `thrift:"ImageType,5,optional" json:"image_type,omitempty" query:"image_type"`
}

func NewGetIcmVersionRequest() *GetIcmVersionRequest {
	return &GetIcmVersionRequest{}
}

func (p *GetIcmVersionRequest) InitDefault() {
}

var GetIcmVersionRequest_Version_DEFAULT string

func (p *GetIcmVersionRequest) GetVersion() (v string) {
	if !p.IsSetVersion() {
		return GetIcmVersionRequest_Version_DEFAULT
	}
	return *p.Version
}

var GetIcmVersionRequest_Region_DEFAULT string

func (p *GetIcmVersionRequest) GetRegion() (v string) {
	if !p.IsSetRegion() {
		return GetIcmVersionRequest_Region_DEFAULT
	}
	return *p.Region
}

var GetIcmVersionRequest_SpecificTag_DEFAULT string

func (p *GetIcmVersionRequest) GetSpecificTag() (v string) {
	if !p.IsSetSpecificTag() {
		return GetIcmVersionRequest_SpecificTag_DEFAULT
	}
	return *p.SpecificTag
}

var GetIcmVersionRequest_ConfigType_DEFAULT string

func (p *GetIcmVersionRequest) GetConfigType() (v string) {
	if !p.IsSetConfigType() {
		return GetIcmVersionRequest_ConfigType_DEFAULT
	}
	return *p.ConfigType
}

var GetIcmVersionRequest_ImageType_DEFAULT string

func (p *GetIcmVersionRequest) GetImageType() (v string) {
	if !p.IsSetImageType() {
		return GetIcmVersionRequest_ImageType_DEFAULT
	}
	return *p.ImageType
}

func (p *GetIcmVersionRequest) IsSetVersion() bool {
	return p.Version != nil
}

func (p *GetIcmVersionRequest) IsSetRegion() bool {
	return p.Region != nil
}

func (p *GetIcmVersionRequest) IsSetSpecificTag() bool {
	return p.SpecificTag != nil
}

func (p *GetIcmVersionRequest) IsSetConfigType() bool {
	return p.ConfigType != nil
}

func (p *GetIcmVersionRequest) IsSetImageType() bool {
	return p.ImageType != nil
}

func (p *GetIcmVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetIcmVersionRequest(%+v)", *p)
}

type IcmVersion struct {
	Describe          string `thrift:"Describe,1,required" json:"describe"`
	ImageSize         string `thrift:"ImageSize,2,required" json:"image_size"`
	Builder           string `thrift:"Builder,3,required" json:"builder"`
	ImageName         string `thrift:"ImageName,4,required" json:"image_name"`
	ImageVersion      string `thrift:"ImageVersion,5,required" json:"image_version"`
	SpecificTag       string `thrift:"SpecificTag,6,required" json:"specific_tag"`
	SpecificImageName string `thrift:"SpecificImageName,7,required" json:"specific_image_name"`
	CreateTime        string `thrift:"CreateTime,8,required" json:"create_time"`
}

func NewIcmVersion() *IcmVersion {
	return &IcmVersion{}
}

func (p *IcmVersion) InitDefault() {
}

func (p *IcmVersion) GetDescribe() (v string) {
	return p.Describe
}

func (p *IcmVersion) GetImageSize() (v string) {
	return p.ImageSize
}

func (p *IcmVersion) GetBuilder() (v string) {
	return p.Builder
}

func (p *IcmVersion) GetImageName() (v string) {
	return p.ImageName
}

func (p *IcmVersion) GetImageVersion() (v string) {
	return p.ImageVersion
}

func (p *IcmVersion) GetSpecificTag() (v string) {
	return p.SpecificTag
}

func (p *IcmVersion) GetSpecificImageName() (v string) {
	return p.SpecificImageName
}

func (p *IcmVersion) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *IcmVersion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IcmVersion(%+v)", *p)
}

type GetIcmVersionResponse struct {
	IcmVersions []*IcmVersion `thrift:"IcmVersions,1,required" json:"icm_versions"`
}

func NewGetIcmVersionResponse() *GetIcmVersionResponse {
	return &GetIcmVersionResponse{}
}

func (p *GetIcmVersionResponse) InitDefault() {
}

func (p *GetIcmVersionResponse) GetIcmVersions() (v []*IcmVersion) {
	return p.IcmVersions
}

func (p *GetIcmVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetIcmVersionResponse(%+v)", *p)
}

type GetDeployReviewUserRequest struct {
}

func NewGetDeployReviewUserRequest() *GetDeployReviewUserRequest {
	return &GetDeployReviewUserRequest{}
}

func (p *GetDeployReviewUserRequest) InitDefault() {
}

func (p *GetDeployReviewUserRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeployReviewUserRequest(%+v)", *p)
}

type GetDeployReviewUserResponse struct {
	Users []string `thrift:"Users,1,required" json:"users"`
}

func NewGetDeployReviewUserResponse() *GetDeployReviewUserResponse {
	return &GetDeployReviewUserResponse{}
}

func (p *GetDeployReviewUserResponse) InitDefault() {
}

func (p *GetDeployReviewUserResponse) GetUsers() (v []string) {
	return p.Users
}

func (p *GetDeployReviewUserResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDeployReviewUserResponse(%+v)", *p)
}

type GetAgentDeployListRequest struct {
	AgentConfigID string  `thrift:"AgentConfigID,1,required" json:"agent_config_id,required" query:"agent_config_id,required"`
	PageNum       int64   `thrift:"PageNum,2,required" json:"page_num,required" query:"page_num,required"`
	PageSize      int64   `thrift:"PageSize,3,required" json:"page_size,required" query:"page_size,required"`
	Status        *string `thrift:"Status,4,optional" json:"status,omitempty" query:"status"`
}

func NewGetAgentDeployListRequest() *GetAgentDeployListRequest {
	return &GetAgentDeployListRequest{}
}

func (p *GetAgentDeployListRequest) InitDefault() {
}

func (p *GetAgentDeployListRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *GetAgentDeployListRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *GetAgentDeployListRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var GetAgentDeployListRequest_Status_DEFAULT string

func (p *GetAgentDeployListRequest) GetStatus() (v string) {
	if !p.IsSetStatus() {
		return GetAgentDeployListRequest_Status_DEFAULT
	}
	return *p.Status
}

func (p *GetAgentDeployListRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *GetAgentDeployListRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentDeployListRequest(%+v)", *p)
}

type GetAgentDeployListResponse struct {
	AgentDeploys []*AgentDeploy `thrift:"AgentDeploys,1,required" json:"agent_deploys"`
	Total        int64          `thrift:"Total,2,required" json:"total"`
}

func NewGetAgentDeployListResponse() *GetAgentDeployListResponse {
	return &GetAgentDeployListResponse{}
}

func (p *GetAgentDeployListResponse) InitDefault() {
}

func (p *GetAgentDeployListResponse) GetAgentDeploys() (v []*AgentDeploy) {
	return p.AgentDeploys
}

func (p *GetAgentDeployListResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *GetAgentDeployListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentDeployListResponse(%+v)", *p)
}

type BitsUpsertAgentVersionRequest struct {
	AgentConfigID  string  `thrift:"AgentConfigID,1,required" json:"agent_config_id"`
	BitsBuildID    string  `thrift:"BitsBuildID,2,required" json:"bits_build_id"`
	ScmVersion     string  `thrift:"ScmVersion,3,required" json:"scm_version"`
	User           string  `thrift:"User,4,required" json:"user"`
	Description    string  `thrift:"Description,5,required" json:"description"`
	IcmVersion     string  `thrift:"IcmVersion,6,required" json:"icm_version"`
	SourceID       *string `thrift:"SourceID,7,optional" json:"source_id"`
	BashIcmVersion *string `thrift:"BashIcmVersion,8,optional" json:"bash_icm_version"`
}

func NewBitsUpsertAgentVersionRequest() *BitsUpsertAgentVersionRequest {
	return &BitsUpsertAgentVersionRequest{}
}

func (p *BitsUpsertAgentVersionRequest) InitDefault() {
}

func (p *BitsUpsertAgentVersionRequest) GetAgentConfigID() (v string) {
	return p.AgentConfigID
}

func (p *BitsUpsertAgentVersionRequest) GetBitsBuildID() (v string) {
	return p.BitsBuildID
}

func (p *BitsUpsertAgentVersionRequest) GetScmVersion() (v string) {
	return p.ScmVersion
}

func (p *BitsUpsertAgentVersionRequest) GetUser() (v string) {
	return p.User
}

func (p *BitsUpsertAgentVersionRequest) GetDescription() (v string) {
	return p.Description
}

func (p *BitsUpsertAgentVersionRequest) GetIcmVersion() (v string) {
	return p.IcmVersion
}

var BitsUpsertAgentVersionRequest_SourceID_DEFAULT string

func (p *BitsUpsertAgentVersionRequest) GetSourceID() (v string) {
	if !p.IsSetSourceID() {
		return BitsUpsertAgentVersionRequest_SourceID_DEFAULT
	}
	return *p.SourceID
}

var BitsUpsertAgentVersionRequest_BashIcmVersion_DEFAULT string

func (p *BitsUpsertAgentVersionRequest) GetBashIcmVersion() (v string) {
	if !p.IsSetBashIcmVersion() {
		return BitsUpsertAgentVersionRequest_BashIcmVersion_DEFAULT
	}
	return *p.BashIcmVersion
}

func (p *BitsUpsertAgentVersionRequest) IsSetSourceID() bool {
	return p.SourceID != nil
}

func (p *BitsUpsertAgentVersionRequest) IsSetBashIcmVersion() bool {
	return p.BashIcmVersion != nil
}

func (p *BitsUpsertAgentVersionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BitsUpsertAgentVersionRequest(%+v)", *p)
}

type BitsUpsertAgentVersionResponse struct {
	AgentConfigVersionID string `thrift:"AgentConfigVersionID,1,required" json:"agent_config_version_id"`
}

func NewBitsUpsertAgentVersionResponse() *BitsUpsertAgentVersionResponse {
	return &BitsUpsertAgentVersionResponse{}
}

func (p *BitsUpsertAgentVersionResponse) InitDefault() {
}

func (p *BitsUpsertAgentVersionResponse) GetAgentConfigVersionID() (v string) {
	return p.AgentConfigVersionID
}

func (p *BitsUpsertAgentVersionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BitsUpsertAgentVersionResponse(%+v)", *p)
}
