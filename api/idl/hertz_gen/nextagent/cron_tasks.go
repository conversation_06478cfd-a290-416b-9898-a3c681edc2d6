// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package nextagent

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

type TaskType int64

const (
	TaskType_Unknown TaskType = 0
	// 发给个人
	TaskType_AimeBot TaskType = 1
	// 新建群组
	TaskType_NewGroup TaskType = 2
	// 绑定群组
	TaskType_BindGroup TaskType = 3
)

func (p TaskType) String() string {
	switch p {
	case TaskType_Unknown:
		return "Unknown"
	case TaskType_AimeBot:
		return "AimeBot"
	case TaskType_NewGroup:
		return "NewGroup"
	case TaskType_BindGroup:
		return "BindGroup"
	}
	return "<UNSET>"
}

func TaskTypeFromString(s string) (TaskType, error) {
	switch s {
	case "Unknown":
		return TaskType_Unknown, nil
	case "AimeBot":
		return TaskType_AimeBot, nil
	case "NewGroup":
		return TaskType_NewGroup, nil
	case "BindGroup":
		return TaskType_BindGroup, nil
	}
	return TaskType(0), fmt.<PERSON><PERSON>rf("not a valid TaskType string")
}

func TaskTypePtr(v TaskType) *TaskType { return &v }
func (p *TaskType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TaskType(result.Int64)
	return
}

func (p *TaskType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 生效中、已停止
type TaskStatus int64

const (
	TaskStatus_Unknown TaskStatus = 0
	TaskStatus_Running TaskStatus = 1
	TaskStatus_Stopped TaskStatus = 2
)

func (p TaskStatus) String() string {
	switch p {
	case TaskStatus_Unknown:
		return "Unknown"
	case TaskStatus_Running:
		return "Running"
	case TaskStatus_Stopped:
		return "Stopped"
	}
	return "<UNSET>"
}

func TaskStatusFromString(s string) (TaskStatus, error) {
	switch s {
	case "Unknown":
		return TaskStatus_Unknown, nil
	case "Running":
		return TaskStatus_Running, nil
	case "Stopped":
		return TaskStatus_Stopped, nil
	}
	return TaskStatus(0), fmt.Errorf("not a valid TaskStatus string")
}

func TaskStatusPtr(v TaskStatus) *TaskStatus { return &v }
func (p *TaskStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TaskStatus(result.Int64)
	return
}

func (p *TaskStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 触发类型：手动、CronJob、DB扫库补偿
type TriggerType int64

const (
	TriggerType_Unknown        TriggerType = 0
	TriggerType_Manual         TriggerType = 1
	TriggerType_CronJob        TriggerType = 2
	TriggerType_DBCompensation TriggerType = 3
)

func (p TriggerType) String() string {
	switch p {
	case TriggerType_Unknown:
		return "Unknown"
	case TriggerType_Manual:
		return "Manual"
	case TriggerType_CronJob:
		return "CronJob"
	case TriggerType_DBCompensation:
		return "DBCompensation"
	}
	return "<UNSET>"
}

func TriggerTypeFromString(s string) (TriggerType, error) {
	switch s {
	case "Unknown":
		return TriggerType_Unknown, nil
	case "Manual":
		return TriggerType_Manual, nil
	case "CronJob":
		return TriggerType_CronJob, nil
	case "DBCompensation":
		return TriggerType_DBCompensation, nil
	}
	return TriggerType(0), fmt.Errorf("not a valid TriggerType string")
}

func TriggerTypePtr(v TriggerType) *TriggerType { return &v }
func (p *TriggerType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TriggerType(result.Int64)
	return
}

func (p *TriggerType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 成功、失败、有异常
type LastRunStatus int64

const (
	LastRunStatus_Unknown LastRunStatus = 0
	LastRunStatus_Success LastRunStatus = 1
	LastRunStatus_Failed  LastRunStatus = 2
	// 有异常：仅模版有变化
	LastRunStatus_Exception LastRunStatus = 3
)

func (p LastRunStatus) String() string {
	switch p {
	case LastRunStatus_Unknown:
		return "Unknown"
	case LastRunStatus_Success:
		return "Success"
	case LastRunStatus_Failed:
		return "Failed"
	case LastRunStatus_Exception:
		return "Exception"
	}
	return "<UNSET>"
}

func LastRunStatusFromString(s string) (LastRunStatus, error) {
	switch s {
	case "Unknown":
		return LastRunStatus_Unknown, nil
	case "Success":
		return LastRunStatus_Success, nil
	case "Failed":
		return LastRunStatus_Failed, nil
	case "Exception":
		return LastRunStatus_Exception, nil
	}
	return LastRunStatus(0), fmt.Errorf("not a valid LastRunStatus string")
}

func LastRunStatusPtr(v LastRunStatus) *LastRunStatus { return &v }
func (p *LastRunStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = LastRunStatus(result.Int64)
	return
}

func (p *LastRunStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ScheduleType int64

const (
	ScheduleType_Unknown ScheduleType = 0
	// 每日运行
	ScheduleType_ScheduleTypeByDay ScheduleType = 1
	// 每周运行
	ScheduleType_ScheduleTypeByWeek ScheduleType = 2
)

func (p ScheduleType) String() string {
	switch p {
	case ScheduleType_Unknown:
		return "Unknown"
	case ScheduleType_ScheduleTypeByDay:
		return "ScheduleTypeByDay"
	case ScheduleType_ScheduleTypeByWeek:
		return "ScheduleTypeByWeek"
	}
	return "<UNSET>"
}

func ScheduleTypeFromString(s string) (ScheduleType, error) {
	switch s {
	case "Unknown":
		return ScheduleType_Unknown, nil
	case "ScheduleTypeByDay":
		return ScheduleType_ScheduleTypeByDay, nil
	case "ScheduleTypeByWeek":
		return ScheduleType_ScheduleTypeByWeek, nil
	}
	return ScheduleType(0), fmt.Errorf("not a valid ScheduleType string")
}

func ScheduleTypePtr(v ScheduleType) *ScheduleType { return &v }
func (p *ScheduleType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ScheduleType(result.Int64)
	return
}

func (p *ScheduleType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type WeekDay int64

const (
	WeekDay_Unknown   WeekDay = 0
	WeekDay_Monday    WeekDay = 1
	WeekDay_Tuesday   WeekDay = 2
	WeekDay_Wednesday WeekDay = 3
	WeekDay_Thursday  WeekDay = 4
	WeekDay_Friday    WeekDay = 5
	WeekDay_Saturday  WeekDay = 6
	WeekDay_Sunday    WeekDay = 7
)

func (p WeekDay) String() string {
	switch p {
	case WeekDay_Unknown:
		return "Unknown"
	case WeekDay_Monday:
		return "Monday"
	case WeekDay_Tuesday:
		return "Tuesday"
	case WeekDay_Wednesday:
		return "Wednesday"
	case WeekDay_Thursday:
		return "Thursday"
	case WeekDay_Friday:
		return "Friday"
	case WeekDay_Saturday:
		return "Saturday"
	case WeekDay_Sunday:
		return "Sunday"
	}
	return "<UNSET>"
}

func WeekDayFromString(s string) (WeekDay, error) {
	switch s {
	case "Unknown":
		return WeekDay_Unknown, nil
	case "Monday":
		return WeekDay_Monday, nil
	case "Tuesday":
		return WeekDay_Tuesday, nil
	case "Wednesday":
		return WeekDay_Wednesday, nil
	case "Thursday":
		return WeekDay_Thursday, nil
	case "Friday":
		return WeekDay_Friday, nil
	case "Saturday":
		return WeekDay_Saturday, nil
	case "Sunday":
		return WeekDay_Sunday, nil
	}
	return WeekDay(0), fmt.Errorf("not a valid WeekDay string")
}

func WeekDayPtr(v WeekDay) *WeekDay { return &v }
func (p *WeekDay) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = WeekDay(result.Int64)
	return
}

func (p *WeekDay) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TaskManageType int64

const (
	TaskManageType_TaskManageTypeDelete TaskManageType = 1
	TaskManageType_TaskManageTypePause  TaskManageType = 2
	TaskManageType_TaskManageTypeResume TaskManageType = 3
)

func (p TaskManageType) String() string {
	switch p {
	case TaskManageType_TaskManageTypeDelete:
		return "TaskManageTypeDelete"
	case TaskManageType_TaskManageTypePause:
		return "TaskManageTypePause"
	case TaskManageType_TaskManageTypeResume:
		return "TaskManageTypeResume"
	}
	return "<UNSET>"
}

func TaskManageTypeFromString(s string) (TaskManageType, error) {
	switch s {
	case "TaskManageTypeDelete":
		return TaskManageType_TaskManageTypeDelete, nil
	case "TaskManageTypePause":
		return TaskManageType_TaskManageTypePause, nil
	case "TaskManageTypeResume":
		return TaskManageType_TaskManageTypeResume, nil
	}
	return TaskManageType(0), fmt.Errorf("not a valid TaskManageType string")
}

func TaskManageTypePtr(v TaskManageType) *TaskManageType { return &v }
func (p *TaskManageType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TaskManageType(result.Int64)
	return
}

func (p *TaskManageType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type GroupInfo struct {
	// 群名称
	GroupName *string `thrift:"GroupName,1,optional" json:"group_name"`
	// 群的头像链接
	GroupAvatar *string `thrift:"GroupAvatar,2,optional" json:"group_avatar"`
	// 群的chat id
	GroupID *string `thrift:"GroupID,3,optional" json:"group_id"`
}

func NewGroupInfo() *GroupInfo {
	return &GroupInfo{}
}

func (p *GroupInfo) InitDefault() {
}

var GroupInfo_GroupName_DEFAULT string

func (p *GroupInfo) GetGroupName() (v string) {
	if !p.IsSetGroupName() {
		return GroupInfo_GroupName_DEFAULT
	}
	return *p.GroupName
}

var GroupInfo_GroupAvatar_DEFAULT string

func (p *GroupInfo) GetGroupAvatar() (v string) {
	if !p.IsSetGroupAvatar() {
		return GroupInfo_GroupAvatar_DEFAULT
	}
	return *p.GroupAvatar
}

var GroupInfo_GroupID_DEFAULT string

func (p *GroupInfo) GetGroupID() (v string) {
	if !p.IsSetGroupID() {
		return GroupInfo_GroupID_DEFAULT
	}
	return *p.GroupID
}

func (p *GroupInfo) IsSetGroupName() bool {
	return p.GroupName != nil
}

func (p *GroupInfo) IsSetGroupAvatar() bool {
	return p.GroupAvatar != nil
}

func (p *GroupInfo) IsSetGroupID() bool {
	return p.GroupID != nil
}

func (p *GroupInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GroupInfo(%+v)", *p)
}

type CronTask struct {
	ID *int64 `thrift:"ID,1,optional" json:"id"`
	// 创建时间
	CreatedAt string `thrift:"CreatedAt,2,required" json:"created_at"`
	UpdatedAt string `thrift:"UpdatedAt,3,required" json:"updated_at"`
	// 用户名称
	Username    *string            `thrift:"Username,4,optional" json:"username"`
	UID         *string            `thrift:"UID,5,optional" json:"uid"`
	Name        string             `thrift:"Name,6,required" json:"name"`
	Description *string            `thrift:"Description,7,optional" json:"description"`
	TaskType    TaskType           `thrift:"TaskType,8,required" json:"task_type"`
	TemplateID  string             `thrift:"TemplateID,9,required" json:"template_id"`
	FormValue   *TemplateFormValue `thrift:"FormValue,10,optional" json:"form_value"`
	// cronjob转画出来的运行时间，格式：2023-12-25 12:00:00
	Schedule      *string        `thrift:"Schedule,11,optional" json:"schedule"`
	Timezone      string         `thrift:"Timezone,12,required" json:"timezone"`
	LastRunStatus *LastRunStatus `thrift:"LastRunStatus,13,optional" json:"last_run_status"`
	TaskStatus    TaskStatus     `thrift:"TaskStatus,14,required" json:"task_status"`
	Role          SessionRole    `thrift:"Role,16,required" json:"role"`
	SpaceID       *string        `thrift:"SpaceID,17,optional" json:"space_id"`
	// 群组信息
	GroupInfo *GroupInfo `thrift:"GroupInfo,18,optional" json:"group_info"`
	// 每周几执行
	WeekDay *WeekDay `thrift:"WeekDay,19,optional" json:"week_day"`
	// 定时任务类型，每天or每周
	ScheduleType ScheduleType `thrift:"ScheduleType,20,required" json:"schedule_type"`
	// 最后一次运行的错误信息
	LastRunErrMsg *string `thrift:"LastRunErrMsg,21,optional" json:"last_run_err_msg"`
	// 是否有权限编辑任务
	EnableEdit *bool `thrift:"EnableEdit,22,optional" json:"enable_edit"`
}

func NewCronTask() *CronTask {
	return &CronTask{}
}

func (p *CronTask) InitDefault() {
}

var CronTask_ID_DEFAULT int64

func (p *CronTask) GetID() (v int64) {
	if !p.IsSetID() {
		return CronTask_ID_DEFAULT
	}
	return *p.ID
}

func (p *CronTask) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *CronTask) GetUpdatedAt() (v string) {
	return p.UpdatedAt
}

var CronTask_Username_DEFAULT string

func (p *CronTask) GetUsername() (v string) {
	if !p.IsSetUsername() {
		return CronTask_Username_DEFAULT
	}
	return *p.Username
}

var CronTask_UID_DEFAULT string

func (p *CronTask) GetUID() (v string) {
	if !p.IsSetUID() {
		return CronTask_UID_DEFAULT
	}
	return *p.UID
}

func (p *CronTask) GetName() (v string) {
	return p.Name
}

var CronTask_Description_DEFAULT string

func (p *CronTask) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return CronTask_Description_DEFAULT
	}
	return *p.Description
}

func (p *CronTask) GetTaskType() (v TaskType) {
	return p.TaskType
}

func (p *CronTask) GetTemplateID() (v string) {
	return p.TemplateID
}

var CronTask_FormValue_DEFAULT *TemplateFormValue

func (p *CronTask) GetFormValue() (v *TemplateFormValue) {
	if !p.IsSetFormValue() {
		return CronTask_FormValue_DEFAULT
	}
	return p.FormValue
}

var CronTask_Schedule_DEFAULT string

func (p *CronTask) GetSchedule() (v string) {
	if !p.IsSetSchedule() {
		return CronTask_Schedule_DEFAULT
	}
	return *p.Schedule
}

func (p *CronTask) GetTimezone() (v string) {
	return p.Timezone
}

var CronTask_LastRunStatus_DEFAULT LastRunStatus

func (p *CronTask) GetLastRunStatus() (v LastRunStatus) {
	if !p.IsSetLastRunStatus() {
		return CronTask_LastRunStatus_DEFAULT
	}
	return *p.LastRunStatus
}

func (p *CronTask) GetTaskStatus() (v TaskStatus) {
	return p.TaskStatus
}

func (p *CronTask) GetRole() (v SessionRole) {
	return p.Role
}

var CronTask_SpaceID_DEFAULT string

func (p *CronTask) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CronTask_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var CronTask_GroupInfo_DEFAULT *GroupInfo

func (p *CronTask) GetGroupInfo() (v *GroupInfo) {
	if !p.IsSetGroupInfo() {
		return CronTask_GroupInfo_DEFAULT
	}
	return p.GroupInfo
}

var CronTask_WeekDay_DEFAULT WeekDay

func (p *CronTask) GetWeekDay() (v WeekDay) {
	if !p.IsSetWeekDay() {
		return CronTask_WeekDay_DEFAULT
	}
	return *p.WeekDay
}

func (p *CronTask) GetScheduleType() (v ScheduleType) {
	return p.ScheduleType
}

var CronTask_LastRunErrMsg_DEFAULT string

func (p *CronTask) GetLastRunErrMsg() (v string) {
	if !p.IsSetLastRunErrMsg() {
		return CronTask_LastRunErrMsg_DEFAULT
	}
	return *p.LastRunErrMsg
}

var CronTask_EnableEdit_DEFAULT bool

func (p *CronTask) GetEnableEdit() (v bool) {
	if !p.IsSetEnableEdit() {
		return CronTask_EnableEdit_DEFAULT
	}
	return *p.EnableEdit
}

func (p *CronTask) IsSetID() bool {
	return p.ID != nil
}

func (p *CronTask) IsSetUsername() bool {
	return p.Username != nil
}

func (p *CronTask) IsSetUID() bool {
	return p.UID != nil
}

func (p *CronTask) IsSetDescription() bool {
	return p.Description != nil
}

func (p *CronTask) IsSetFormValue() bool {
	return p.FormValue != nil
}

func (p *CronTask) IsSetSchedule() bool {
	return p.Schedule != nil
}

func (p *CronTask) IsSetLastRunStatus() bool {
	return p.LastRunStatus != nil
}

func (p *CronTask) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CronTask) IsSetGroupInfo() bool {
	return p.GroupInfo != nil
}

func (p *CronTask) IsSetWeekDay() bool {
	return p.WeekDay != nil
}

func (p *CronTask) IsSetLastRunErrMsg() bool {
	return p.LastRunErrMsg != nil
}

func (p *CronTask) IsSetEnableEdit() bool {
	return p.EnableEdit != nil
}

func (p *CronTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CronTask(%+v)", *p)
}

type TaskExecution struct {
	ID                *int64        `thrift:"ID,1,optional" json:"id"`
	TaskUID           string        `thrift:"TaskUID,2,required" json:"task_uid"`
	TaskName          string        `thrift:"TaskName,3,required" json:"task_name"`
	Username          *string       `thrift:"Username,4,optional" json:"username"`
	ExecuteTime       string        `thrift:"ExecuteTime,6,required" json:"execute_time"`
	ErrorMessage      *string       `thrift:"ErrorMessage,7,optional" json:"error_message"`
	SessionID         string        `thrift:"SessionID,8,required" json:"session_id"`
	TriggerType       TriggerType   `thrift:"TriggerType,9,required" json:"trigger_type"`
	ExecutionDuration *int32        `thrift:"ExecutionDuration,10,optional" json:"execution_duration"`
	CreatedAt         string        `thrift:"CreatedAt,11,required" json:"created_at"`
	RunStatus         SessionStatus `thrift:"RunStatus,12,required" json:"run_status"`
}

func NewTaskExecution() *TaskExecution {
	return &TaskExecution{}
}

func (p *TaskExecution) InitDefault() {
}

var TaskExecution_ID_DEFAULT int64

func (p *TaskExecution) GetID() (v int64) {
	if !p.IsSetID() {
		return TaskExecution_ID_DEFAULT
	}
	return *p.ID
}

func (p *TaskExecution) GetTaskUID() (v string) {
	return p.TaskUID
}

func (p *TaskExecution) GetTaskName() (v string) {
	return p.TaskName
}

var TaskExecution_Username_DEFAULT string

func (p *TaskExecution) GetUsername() (v string) {
	if !p.IsSetUsername() {
		return TaskExecution_Username_DEFAULT
	}
	return *p.Username
}

func (p *TaskExecution) GetExecuteTime() (v string) {
	return p.ExecuteTime
}

var TaskExecution_ErrorMessage_DEFAULT string

func (p *TaskExecution) GetErrorMessage() (v string) {
	if !p.IsSetErrorMessage() {
		return TaskExecution_ErrorMessage_DEFAULT
	}
	return *p.ErrorMessage
}

func (p *TaskExecution) GetSessionID() (v string) {
	return p.SessionID
}

func (p *TaskExecution) GetTriggerType() (v TriggerType) {
	return p.TriggerType
}

var TaskExecution_ExecutionDuration_DEFAULT int32

func (p *TaskExecution) GetExecutionDuration() (v int32) {
	if !p.IsSetExecutionDuration() {
		return TaskExecution_ExecutionDuration_DEFAULT
	}
	return *p.ExecutionDuration
}

func (p *TaskExecution) GetCreatedAt() (v string) {
	return p.CreatedAt
}

func (p *TaskExecution) GetRunStatus() (v SessionStatus) {
	return p.RunStatus
}

func (p *TaskExecution) IsSetID() bool {
	return p.ID != nil
}

func (p *TaskExecution) IsSetUsername() bool {
	return p.Username != nil
}

func (p *TaskExecution) IsSetErrorMessage() bool {
	return p.ErrorMessage != nil
}

func (p *TaskExecution) IsSetExecutionDuration() bool {
	return p.ExecutionDuration != nil
}

func (p *TaskExecution) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskExecution(%+v)", *p)
}

// 创建任务详情
type CreateCronTaskRequest struct {
	ID            *int64             `thrift:"ID,1,optional" json:"id"`
	Username      *string            `thrift:"Username,2,optional" json:"username"`
	UID           *string            `thrift:"UID,3,optional" json:"uid"`
	Name          string             `thrift:"Name,4,required" json:"name"`
	Description   *string            `thrift:"Description,5,optional" json:"description"`
	TaskType      TaskType           `thrift:"TaskType,6,required" json:"task_type"`
	TemplateID    string             `thrift:"TemplateID,7,required" json:"template_id"`
	FormValue     *TemplateFormValue `thrift:"FormValue,8,optional" json:"form_value"`
	ScheduleType  ScheduleType       `thrift:"ScheduleType,9,required" json:"schedule_type"`
	Schedule      string             `thrift:"Schedule,10,required" json:"schedule"`
	Timezone      string             `thrift:"Timezone,11,required" json:"timezone"`
	LastRunStatus *LastRunStatus     `thrift:"LastRunStatus,12,optional" json:"last_run_status"`
	TaskStatus    TaskStatus         `thrift:"TaskStatus,13,required" json:"task_status"`
	GroupInfo     *GroupInfo         `thrift:"GroupInfo,14,optional" json:"group_info"`
	Role          SessionRole        `thrift:"Role,15,required" json:"role"`
	SpaceID       *string            `thrift:"SpaceID,16,optional" json:"space_id"`
	// 同CreateMessageWithTemplate模板的options
	Options *string `thrift:"Options,17,optional" json:"options"`
	// 同CreateMessageWithTemplate模板的content
	Content string `thrift:"Content,18,required" json:"content"`
}

func NewCreateCronTaskRequest() *CreateCronTaskRequest {
	return &CreateCronTaskRequest{}
}

func (p *CreateCronTaskRequest) InitDefault() {
}

var CreateCronTaskRequest_ID_DEFAULT int64

func (p *CreateCronTaskRequest) GetID() (v int64) {
	if !p.IsSetID() {
		return CreateCronTaskRequest_ID_DEFAULT
	}
	return *p.ID
}

var CreateCronTaskRequest_Username_DEFAULT string

func (p *CreateCronTaskRequest) GetUsername() (v string) {
	if !p.IsSetUsername() {
		return CreateCronTaskRequest_Username_DEFAULT
	}
	return *p.Username
}

var CreateCronTaskRequest_UID_DEFAULT string

func (p *CreateCronTaskRequest) GetUID() (v string) {
	if !p.IsSetUID() {
		return CreateCronTaskRequest_UID_DEFAULT
	}
	return *p.UID
}

func (p *CreateCronTaskRequest) GetName() (v string) {
	return p.Name
}

var CreateCronTaskRequest_Description_DEFAULT string

func (p *CreateCronTaskRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return CreateCronTaskRequest_Description_DEFAULT
	}
	return *p.Description
}

func (p *CreateCronTaskRequest) GetTaskType() (v TaskType) {
	return p.TaskType
}

func (p *CreateCronTaskRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var CreateCronTaskRequest_FormValue_DEFAULT *TemplateFormValue

func (p *CreateCronTaskRequest) GetFormValue() (v *TemplateFormValue) {
	if !p.IsSetFormValue() {
		return CreateCronTaskRequest_FormValue_DEFAULT
	}
	return p.FormValue
}

func (p *CreateCronTaskRequest) GetScheduleType() (v ScheduleType) {
	return p.ScheduleType
}

func (p *CreateCronTaskRequest) GetSchedule() (v string) {
	return p.Schedule
}

func (p *CreateCronTaskRequest) GetTimezone() (v string) {
	return p.Timezone
}

var CreateCronTaskRequest_LastRunStatus_DEFAULT LastRunStatus

func (p *CreateCronTaskRequest) GetLastRunStatus() (v LastRunStatus) {
	if !p.IsSetLastRunStatus() {
		return CreateCronTaskRequest_LastRunStatus_DEFAULT
	}
	return *p.LastRunStatus
}

func (p *CreateCronTaskRequest) GetTaskStatus() (v TaskStatus) {
	return p.TaskStatus
}

var CreateCronTaskRequest_GroupInfo_DEFAULT *GroupInfo

func (p *CreateCronTaskRequest) GetGroupInfo() (v *GroupInfo) {
	if !p.IsSetGroupInfo() {
		return CreateCronTaskRequest_GroupInfo_DEFAULT
	}
	return p.GroupInfo
}

func (p *CreateCronTaskRequest) GetRole() (v SessionRole) {
	return p.Role
}

var CreateCronTaskRequest_SpaceID_DEFAULT string

func (p *CreateCronTaskRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return CreateCronTaskRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var CreateCronTaskRequest_Options_DEFAULT string

func (p *CreateCronTaskRequest) GetOptions() (v string) {
	if !p.IsSetOptions() {
		return CreateCronTaskRequest_Options_DEFAULT
	}
	return *p.Options
}

func (p *CreateCronTaskRequest) GetContent() (v string) {
	return p.Content
}

func (p *CreateCronTaskRequest) IsSetID() bool {
	return p.ID != nil
}

func (p *CreateCronTaskRequest) IsSetUsername() bool {
	return p.Username != nil
}

func (p *CreateCronTaskRequest) IsSetUID() bool {
	return p.UID != nil
}

func (p *CreateCronTaskRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *CreateCronTaskRequest) IsSetFormValue() bool {
	return p.FormValue != nil
}

func (p *CreateCronTaskRequest) IsSetLastRunStatus() bool {
	return p.LastRunStatus != nil
}

func (p *CreateCronTaskRequest) IsSetGroupInfo() bool {
	return p.GroupInfo != nil
}

func (p *CreateCronTaskRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateCronTaskRequest) IsSetOptions() bool {
	return p.Options != nil
}

func (p *CreateCronTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateCronTaskRequest(%+v)", *p)
}

type CreateCronTaskResponse struct {
	IsSuccess bool `thrift:"IsSuccess,1,required" json:"is_success" form:"is_success,required" `
}

func NewCreateCronTaskResponse() *CreateCronTaskResponse {
	return &CreateCronTaskResponse{}
}

func (p *CreateCronTaskResponse) InitDefault() {
}

func (p *CreateCronTaskResponse) GetIsSuccess() (v bool) {
	return p.IsSuccess
}

func (p *CreateCronTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateCronTaskResponse(%+v)", *p)
}

// 更新任务详情
type UpdateCronTaskRequest struct {
	ID            *int64             `thrift:"ID,1,optional" json:"id"`
	Username      *string            `thrift:"Username,2,optional" json:"username"`
	UID           string             `thrift:"UID,3,required" json:"uid" path:"uid,required" `
	Name          *string            `thrift:"Name,4,optional" json:"name"`
	Description   *string            `thrift:"Description,5,optional" json:"description"`
	TaskType      *TaskType          `thrift:"TaskType,6,optional" json:"task_type"`
	TemplateID    string             `thrift:"TemplateID,7,required" json:"template_id"`
	FormValue     *TemplateFormValue `thrift:"FormValue,8,optional" json:"form_value"`
	ScheduleType  *ScheduleType      `thrift:"ScheduleType,9,optional" json:"schedule_type"`
	Schedule      *string            `thrift:"Schedule,10,optional" json:"schedule"`
	Timezone      *string            `thrift:"Timezone,11,optional" json:"timezone"`
	LastRunStatus *LastRunStatus     `thrift:"LastRunStatus,12,optional" json:"last_run_status"`
	TaskStatus    *TaskStatus        `thrift:"TaskStatus,13,optional" json:"task_status"`
	GroupInfo     *GroupInfo         `thrift:"GroupInfo,14,optional" json:"group_info"`
	Role          *SessionRole       `thrift:"Role,15,optional" json:"role"`
	SpaceID       *string            `thrift:"SpaceID,16,optional" json:"space_id"`
	// 同CreateMessageWithTemplate模板的options
	Options *string `thrift:"Options,17,optional" json:"options"`
	// 同CreateMessageWithTemplate模板的content
	Content *string `thrift:"Content,18,optional" json:"content"`
}

func NewUpdateCronTaskRequest() *UpdateCronTaskRequest {
	return &UpdateCronTaskRequest{}
}

func (p *UpdateCronTaskRequest) InitDefault() {
}

var UpdateCronTaskRequest_ID_DEFAULT int64

func (p *UpdateCronTaskRequest) GetID() (v int64) {
	if !p.IsSetID() {
		return UpdateCronTaskRequest_ID_DEFAULT
	}
	return *p.ID
}

var UpdateCronTaskRequest_Username_DEFAULT string

func (p *UpdateCronTaskRequest) GetUsername() (v string) {
	if !p.IsSetUsername() {
		return UpdateCronTaskRequest_Username_DEFAULT
	}
	return *p.Username
}

func (p *UpdateCronTaskRequest) GetUID() (v string) {
	return p.UID
}

var UpdateCronTaskRequest_Name_DEFAULT string

func (p *UpdateCronTaskRequest) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateCronTaskRequest_Name_DEFAULT
	}
	return *p.Name
}

var UpdateCronTaskRequest_Description_DEFAULT string

func (p *UpdateCronTaskRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UpdateCronTaskRequest_Description_DEFAULT
	}
	return *p.Description
}

var UpdateCronTaskRequest_TaskType_DEFAULT TaskType

func (p *UpdateCronTaskRequest) GetTaskType() (v TaskType) {
	if !p.IsSetTaskType() {
		return UpdateCronTaskRequest_TaskType_DEFAULT
	}
	return *p.TaskType
}

func (p *UpdateCronTaskRequest) GetTemplateID() (v string) {
	return p.TemplateID
}

var UpdateCronTaskRequest_FormValue_DEFAULT *TemplateFormValue

func (p *UpdateCronTaskRequest) GetFormValue() (v *TemplateFormValue) {
	if !p.IsSetFormValue() {
		return UpdateCronTaskRequest_FormValue_DEFAULT
	}
	return p.FormValue
}

var UpdateCronTaskRequest_ScheduleType_DEFAULT ScheduleType

func (p *UpdateCronTaskRequest) GetScheduleType() (v ScheduleType) {
	if !p.IsSetScheduleType() {
		return UpdateCronTaskRequest_ScheduleType_DEFAULT
	}
	return *p.ScheduleType
}

var UpdateCronTaskRequest_Schedule_DEFAULT string

func (p *UpdateCronTaskRequest) GetSchedule() (v string) {
	if !p.IsSetSchedule() {
		return UpdateCronTaskRequest_Schedule_DEFAULT
	}
	return *p.Schedule
}

var UpdateCronTaskRequest_Timezone_DEFAULT string

func (p *UpdateCronTaskRequest) GetTimezone() (v string) {
	if !p.IsSetTimezone() {
		return UpdateCronTaskRequest_Timezone_DEFAULT
	}
	return *p.Timezone
}

var UpdateCronTaskRequest_LastRunStatus_DEFAULT LastRunStatus

func (p *UpdateCronTaskRequest) GetLastRunStatus() (v LastRunStatus) {
	if !p.IsSetLastRunStatus() {
		return UpdateCronTaskRequest_LastRunStatus_DEFAULT
	}
	return *p.LastRunStatus
}

var UpdateCronTaskRequest_TaskStatus_DEFAULT TaskStatus

func (p *UpdateCronTaskRequest) GetTaskStatus() (v TaskStatus) {
	if !p.IsSetTaskStatus() {
		return UpdateCronTaskRequest_TaskStatus_DEFAULT
	}
	return *p.TaskStatus
}

var UpdateCronTaskRequest_GroupInfo_DEFAULT *GroupInfo

func (p *UpdateCronTaskRequest) GetGroupInfo() (v *GroupInfo) {
	if !p.IsSetGroupInfo() {
		return UpdateCronTaskRequest_GroupInfo_DEFAULT
	}
	return p.GroupInfo
}

var UpdateCronTaskRequest_Role_DEFAULT SessionRole

func (p *UpdateCronTaskRequest) GetRole() (v SessionRole) {
	if !p.IsSetRole() {
		return UpdateCronTaskRequest_Role_DEFAULT
	}
	return *p.Role
}

var UpdateCronTaskRequest_SpaceID_DEFAULT string

func (p *UpdateCronTaskRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return UpdateCronTaskRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var UpdateCronTaskRequest_Options_DEFAULT string

func (p *UpdateCronTaskRequest) GetOptions() (v string) {
	if !p.IsSetOptions() {
		return UpdateCronTaskRequest_Options_DEFAULT
	}
	return *p.Options
}

var UpdateCronTaskRequest_Content_DEFAULT string

func (p *UpdateCronTaskRequest) GetContent() (v string) {
	if !p.IsSetContent() {
		return UpdateCronTaskRequest_Content_DEFAULT
	}
	return *p.Content
}

func (p *UpdateCronTaskRequest) IsSetID() bool {
	return p.ID != nil
}

func (p *UpdateCronTaskRequest) IsSetUsername() bool {
	return p.Username != nil
}

func (p *UpdateCronTaskRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateCronTaskRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UpdateCronTaskRequest) IsSetTaskType() bool {
	return p.TaskType != nil
}

func (p *UpdateCronTaskRequest) IsSetFormValue() bool {
	return p.FormValue != nil
}

func (p *UpdateCronTaskRequest) IsSetScheduleType() bool {
	return p.ScheduleType != nil
}

func (p *UpdateCronTaskRequest) IsSetSchedule() bool {
	return p.Schedule != nil
}

func (p *UpdateCronTaskRequest) IsSetTimezone() bool {
	return p.Timezone != nil
}

func (p *UpdateCronTaskRequest) IsSetLastRunStatus() bool {
	return p.LastRunStatus != nil
}

func (p *UpdateCronTaskRequest) IsSetTaskStatus() bool {
	return p.TaskStatus != nil
}

func (p *UpdateCronTaskRequest) IsSetGroupInfo() bool {
	return p.GroupInfo != nil
}

func (p *UpdateCronTaskRequest) IsSetRole() bool {
	return p.Role != nil
}

func (p *UpdateCronTaskRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *UpdateCronTaskRequest) IsSetOptions() bool {
	return p.Options != nil
}

func (p *UpdateCronTaskRequest) IsSetContent() bool {
	return p.Content != nil
}

func (p *UpdateCronTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCronTaskRequest(%+v)", *p)
}

type UpdateCronTaskResponse struct {
	CronTask *CronTask `thrift:"CronTask,1,required" json:"cron_task" form:"cron_task,required" `
}

func NewUpdateCronTaskResponse() *UpdateCronTaskResponse {
	return &UpdateCronTaskResponse{}
}

func (p *UpdateCronTaskResponse) InitDefault() {
}

var UpdateCronTaskResponse_CronTask_DEFAULT *CronTask

func (p *UpdateCronTaskResponse) GetCronTask() (v *CronTask) {
	if !p.IsSetCronTask() {
		return UpdateCronTaskResponse_CronTask_DEFAULT
	}
	return p.CronTask
}

func (p *UpdateCronTaskResponse) IsSetCronTask() bool {
	return p.CronTask != nil
}

func (p *UpdateCronTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCronTaskResponse(%+v)", *p)
}

// 任务操作（删除、暂停、开启任务）
type DeleteCronTaskRequest struct {
	UID            string         `thrift:"UID,1,required" json:"uid" path:"uid,required" `
	TaskManageType TaskManageType `thrift:"TaskManageType,2,required" json:"task_manage_type"`
	SpaceID        *string        `thrift:"SpaceID,3,optional" json:"space_id"`
}

func NewDeleteCronTaskRequest() *DeleteCronTaskRequest {
	return &DeleteCronTaskRequest{}
}

func (p *DeleteCronTaskRequest) InitDefault() {
}

func (p *DeleteCronTaskRequest) GetUID() (v string) {
	return p.UID
}

func (p *DeleteCronTaskRequest) GetTaskManageType() (v TaskManageType) {
	return p.TaskManageType
}

var DeleteCronTaskRequest_SpaceID_DEFAULT string

func (p *DeleteCronTaskRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return DeleteCronTaskRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *DeleteCronTaskRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *DeleteCronTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCronTaskRequest(%+v)", *p)
}

type DeleteCronTaskResponse struct {
	IsSuccess bool `thrift:"IsSuccess,1,required" json:"is_success" form:"is_success,required" `
}

func NewDeleteCronTaskResponse() *DeleteCronTaskResponse {
	return &DeleteCronTaskResponse{}
}

func (p *DeleteCronTaskResponse) InitDefault() {
}

func (p *DeleteCronTaskResponse) GetIsSuccess() (v bool) {
	return p.IsSuccess
}

func (p *DeleteCronTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCronTaskResponse(%+v)", *p)
}

// 查看任务详情
type GetCronTaskRequest struct {
	UID     string  `thrift:"UID,1,required" json:"uid" path:"uid,required" `
	SpaceID *string `thrift:"SpaceID,2,optional" json:"space_id" query:"space_id" `
}

func NewGetCronTaskRequest() *GetCronTaskRequest {
	return &GetCronTaskRequest{}
}

func (p *GetCronTaskRequest) InitDefault() {
}

func (p *GetCronTaskRequest) GetUID() (v string) {
	return p.UID
}

var GetCronTaskRequest_SpaceID_DEFAULT string

func (p *GetCronTaskRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return GetCronTaskRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *GetCronTaskRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *GetCronTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCronTaskRequest(%+v)", *p)
}

type GetCronTaskResponse struct {
	CronTask *CronTask `thrift:"CronTask,1,required" json:"cron_task" form:"cron_task,required" `
}

func NewGetCronTaskResponse() *GetCronTaskResponse {
	return &GetCronTaskResponse{}
}

func (p *GetCronTaskResponse) InitDefault() {
}

var GetCronTaskResponse_CronTask_DEFAULT *CronTask

func (p *GetCronTaskResponse) GetCronTask() (v *CronTask) {
	if !p.IsSetCronTask() {
		return GetCronTaskResponse_CronTask_DEFAULT
	}
	return p.CronTask
}

func (p *GetCronTaskResponse) IsSetCronTask() bool {
	return p.CronTask != nil
}

func (p *GetCronTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCronTaskResponse(%+v)", *p)
}

// 查看任务列表（仅返回列表信息）
type ListCronTasksRequest struct {
	Query    *string `thrift:"Query,1,optional" json:"query" query:"query" `
	PageNum  int64   `thrift:"PageNum,2,required" json:"page_num" query:"page_num,required" `
	PageSize int64   `thrift:"PageSize,3,required" json:"page_size" query:"page_size,required" `
	SpaceID  *string `thrift:"SpaceID,4,optional" json:"space_id" query:"space_id" `
}

func NewListCronTasksRequest() *ListCronTasksRequest {
	return &ListCronTasksRequest{}
}

func (p *ListCronTasksRequest) InitDefault() {
}

var ListCronTasksRequest_Query_DEFAULT string

func (p *ListCronTasksRequest) GetQuery() (v string) {
	if !p.IsSetQuery() {
		return ListCronTasksRequest_Query_DEFAULT
	}
	return *p.Query
}

func (p *ListCronTasksRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListCronTasksRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListCronTasksRequest_SpaceID_DEFAULT string

func (p *ListCronTasksRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ListCronTasksRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ListCronTasksRequest) IsSetQuery() bool {
	return p.Query != nil
}

func (p *ListCronTasksRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ListCronTasksRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCronTasksRequest(%+v)", *p)
}

type ListCronTasksResponse struct {
	CronTasks []*CronTask `thrift:"CronTasks,1,required" json:"cron_tasks" form:"cron_tasks,required" `
	Total     int64       `thrift:"Total,2,required" json:"total" form:"total,required" `
}

func NewListCronTasksResponse() *ListCronTasksResponse {
	return &ListCronTasksResponse{}
}

func (p *ListCronTasksResponse) InitDefault() {
}

func (p *ListCronTasksResponse) GetCronTasks() (v []*CronTask) {
	return p.CronTasks
}

func (p *ListCronTasksResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListCronTasksResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCronTasksResponse(%+v)", *p)
}

// 任务操作（强制发起任务）
type ReTryCronTaskRequest struct {
	UID     string  `thrift:"UID,1,required" json:"uid" path:"uid,required" `
	SpaceID *string `thrift:"SpaceID,2,optional" json:"space_id"`
}

func NewReTryCronTaskRequest() *ReTryCronTaskRequest {
	return &ReTryCronTaskRequest{}
}

func (p *ReTryCronTaskRequest) InitDefault() {
}

func (p *ReTryCronTaskRequest) GetUID() (v string) {
	return p.UID
}

var ReTryCronTaskRequest_SpaceID_DEFAULT string

func (p *ReTryCronTaskRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ReTryCronTaskRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ReTryCronTaskRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ReTryCronTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReTryCronTaskRequest(%+v)", *p)
}

type ReTryCronTaskResponse struct {
	Message  *Message  `thrift:"Message,1,required" json:"message" form:"message,required" `
	Session  *Session  `thrift:"Session,2,required" json:"session" form:"session,required" `
	Template *Template `thrift:"Template,3,required" json:"template" form:"template,required" `
}

func NewReTryCronTaskResponse() *ReTryCronTaskResponse {
	return &ReTryCronTaskResponse{}
}

func (p *ReTryCronTaskResponse) InitDefault() {
}

var ReTryCronTaskResponse_Message_DEFAULT *Message

func (p *ReTryCronTaskResponse) GetMessage() (v *Message) {
	if !p.IsSetMessage() {
		return ReTryCronTaskResponse_Message_DEFAULT
	}
	return p.Message
}

var ReTryCronTaskResponse_Session_DEFAULT *Session

func (p *ReTryCronTaskResponse) GetSession() (v *Session) {
	if !p.IsSetSession() {
		return ReTryCronTaskResponse_Session_DEFAULT
	}
	return p.Session
}

var ReTryCronTaskResponse_Template_DEFAULT *Template

func (p *ReTryCronTaskResponse) GetTemplate() (v *Template) {
	if !p.IsSetTemplate() {
		return ReTryCronTaskResponse_Template_DEFAULT
	}
	return p.Template
}

func (p *ReTryCronTaskResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *ReTryCronTaskResponse) IsSetSession() bool {
	return p.Session != nil
}

func (p *ReTryCronTaskResponse) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *ReTryCronTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReTryCronTaskResponse(%+v)", *p)
}

// 查看任务执行记录
type ListCronTaskLogsRequest struct {
	// 任务uid
	UID      string  `thrift:"UID,1,required" json:"uid" path:"uid,required" `
	PageNum  int64   `thrift:"PageNum,2,required" json:"page_num" query:"page_num,required" `
	PageSize int64   `thrift:"PageSize,3,required" json:"page_size" query:"page_size,required" `
	SpaceID  *string `thrift:"SpaceID,4,optional" json:"space_id" query:"space_id" `
}

func NewListCronTaskLogsRequest() *ListCronTaskLogsRequest {
	return &ListCronTaskLogsRequest{}
}

func (p *ListCronTaskLogsRequest) InitDefault() {
}

func (p *ListCronTaskLogsRequest) GetUID() (v string) {
	return p.UID
}

func (p *ListCronTaskLogsRequest) GetPageNum() (v int64) {
	return p.PageNum
}

func (p *ListCronTaskLogsRequest) GetPageSize() (v int64) {
	return p.PageSize
}

var ListCronTaskLogsRequest_SpaceID_DEFAULT string

func (p *ListCronTaskLogsRequest) GetSpaceID() (v string) {
	if !p.IsSetSpaceID() {
		return ListCronTaskLogsRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

func (p *ListCronTaskLogsRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ListCronTaskLogsRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCronTaskLogsRequest(%+v)", *p)
}

type ListCronTaskLogsResponse struct {
	TaskExecution []*TaskExecution `thrift:"TaskExecution,1,required" json:"task_execution" form:"task_execution,required" `
	Total         int64            `thrift:"Total,2,required" json:"total" form:"total,required" `
	// 是否有权限查看所有任务历史
	EnableView bool `thrift:"EnableView,3,required" json:"enable_view" form:"enable_view,required" `
}

func NewListCronTaskLogsResponse() *ListCronTaskLogsResponse {
	return &ListCronTaskLogsResponse{}
}

func (p *ListCronTaskLogsResponse) InitDefault() {
}

func (p *ListCronTaskLogsResponse) GetTaskExecution() (v []*TaskExecution) {
	return p.TaskExecution
}

func (p *ListCronTaskLogsResponse) GetTotal() (v int64) {
	return p.Total
}

func (p *ListCronTaskLogsResponse) GetEnableView() (v bool) {
	return p.EnableView
}

func (p *ListCronTaskLogsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCronTaskLogsResponse(%+v)", *p)
}
