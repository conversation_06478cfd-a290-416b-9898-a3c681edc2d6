namespace go common

exception Error {
    1: string Code,
    2: string Message,
    3: string Error,
}

struct BaseResp {
    1: i64 code = 0,
    2: string message = "",
}

struct HTTPRequest {
	1: optional string Authorization (api.header="authorization"),
	2: optional string XServiceToken (api.header="x-service-token"),
	3: optional string XJwtToken (api.header="x-jwt-token"),
	4: optional string XUsername (api.header="x-username"),
	5: optional string XAuthToken (api.header="x-auth-token"),
	6: optional string XIDEToken (api.header="x-ide-token"),
	7: optional string XSendTS (api.header="x-send-ts"),
	8: optional string XAppId (api.header="x-app-id"),
	9: optional string Host (api.header="host"), // For h2, use :authority
	10: optional string ClientIP (api.header="Tt-Agw-Client-Ip"), // Added by AGW.
	11: optional i64 AGWRecvTs (api.header="X-Tt-Agw-Ts"), // AGW 开始处理当前请求的时间戳
	12: optional string XAppVersion (api.header="x-app-version"),
	13: optional map<string, list<string>> Headers (agw.source="headers"),
    14: optional binary Request (agw.source="raw_body"),
    15: optional string Cookie (api.header="Cookie"),
    16: optional string UserAgent (api.header="User-Agent"),
    17: optional string Forwarded (api.header="X-Forwarded-For"),
    18: optional string Referer (api.header="Referer"),
    19: optional string WebSignRes (api.header="web_sign_res"),
    20: optional string WebMSToken (api.header="web_ms_token"),
    21: optional string TtWid (api.cookie="ttwid"),
    22: optional string FP (api.header="fp"),
}

struct HTTPResponse {
    1: optional string XSendTS (agw.key="x-send-ts"),
    2: optional string XRecvTS (agw.key="x-recv-ts"),
}

enum ErrorCode {
    ErrUndefined = 0
    ErrNoAuth = 1001
    ErrCheckAuth = 1002
    ErrInternal = 2001
    ErrResourceProcessing = 2002
    ErrModelReqFail = 3003
    ErrEmbeddingModelNotExist = 3004
    ErrParamInvalid = 4001
    ErrExist = 4002
    ErrOperating = 4003
    ErrContentTooLong = 4006
    ErrRateLimitExceeded = 4007
    ErrQuotaExceeded = 4008
    ErrLogOut = 4010
    ErrAccessReject = 4011
    ErrDowngrade = 4012
    ErrCountryBlocked = 4013
    ErrDeregister = 4014
    ErrNewRiskControl = 4019
    ErrRecordNotFound = 4020
    ErrUsageReachLimit = 4021
    ErrPromptTokensOverLimit = 4022
    ErrUnknownModel = 4023
    ErrAgentRunLoopExceeded = 5001
    ErrAgentRunTimeExceeded = 5002
    ErrAgentRunQuotaExceeded = 5003
    ErrAgentRunBusy = 5004
    ErrContainerStartupTimeout = 5005
    ErrContainerIsDeleted = 5006
    ErrInsufficientStock = 6001
    ErrBindInvitation = 6002
    ErrNotEnoughPoints = 6003
    ErrNotInEventTime = 6004
    ErrReachedUpperLimit = 6005
    ErrAccountAbnormality = 6006
    ErrDecryption = 6007
    ErrNotGetPrizeTime = 6008
    ErrReviewResultNotReleased = 6009
    ErrNoQualification = 6010
    ErrRedemptionLimitReached =6011
    ErrSecurityCheckFail = 7001
    ErrCompetetionInvalid = 7002
    ErrDuplicateCompetitionRegistration = 7003
    ErrTeamFull = 7004
    ErrDuplicateTeamRegistration = 7005
    ErrTeamNoAuth = 7006
    ErrInvalidTeam = 7007
    ErrDuplicateTeamName = 7008
    ErrInvalidTeamApplication = 7009
    ErrInvalidTeamMember = 7010
    ErrExistedEntitlement = 7011
    ErrCodePreCheckFail = 7012
    ErrSandboxCreateTimeExceeded = 8001
    ErrSandboxRunTimeExceeded = 8002
    ErrSandboxStillRunning = 8003
    ErrImageReviewUnpass = 8004
    ErrImageReviewDownload = 8005
    ErrImageReviewImageRead = 8006
    ErrImageReviewUpload = 8007
    ErrImageReviewGetUrl = 8008
    ErrImageReviewDecode = 8009
    ErrImageReviewInternal = 8010
    ErrTextReviewUnpass = 8011
    ErrSandboxInternalError = 8012
    ErrTraePayRisk = 9001
    ErrTraePayUnexpiredOrder = 9002
    ErrTraePayRegionNotAllowed = 9003
    ErrTraePayParamInvalid = 9004
    ErrTraePayEntitlementExists = 9005
    ErrTraePayInvalidPI = 9006
    ErrNextAgentReachedUpperLimit = 10001
    ErrNextSessionStopped = 10002
    ErrNextAgentNotAllowedUseInternalTool = 10003
    ErrInternalFatal = 10004
    ErrNextAgentReachedGlobalLimit = 10005
    ErrNextAgentGrantAccessEnd = 10006
    ErrMCPActiveLimitExceeded = 10007 // MCP 激活数量限制
    ErrDocumentNotExist = 10008 // 文档不存在
    ErrDocumentTypeNotSupport = 10009 // 文档类型不支持
    ErrCodeMCPConfigEmpty                  = 10010 // MCP配置为空
    ErrCodeMCPConnectFailed                = 10011 // MCP调用失败
    ErrCodeMCPPermissionVerificationFailed = 10012 // MCP鉴权失败
    ErrCodeMCPPSMNotFound                  = 10013 // MCP配置的PSM有误
    ErrBotNotInGroup                       = 10014 // 机器人不在群组中
    ErrUserNotInGroup                      = 10015 // 用户不在群组中
    ErrCanNotOperateOuterGroup             = 10016 // 不能操作外部群组
}

// Format SSE Response
struct SSEResponse {
    1: optional string Event (agw.target="sse", agw.key="event"),
    2: optional string Data (agw.target="sse", agw.key="data"),
    201: optional HTTPResponse HTTPResponse (agw.target="header"),
}

const string EventNameError = "error"

// Error detail.
struct EventError {
    // Error Code.
    // @example: 3001
    1: required i64 Code (go.tag="json:\"code,required\""),
    // Error detail.
    // @example: LLM call is failed.
    2: required string Error (go.tag="json:\"error,required\""),
    // Message to be presented to user.
    // @example: Sorry, but the request is failed, please retry.
    3: required string Message (go.tag="json:\"message,required\""),
}

// JsonVariables is a JSON encoded object string.
// As thrift does not support map[string]any, we use encoded JSON object string.
// @example: {"locale": "zh-cn"}
typedef string JsonVariables
