package framework

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"

	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/stream"
	customopenai "code.byted.org/devgpt/kiwis/port/openai"
)

const (
	RoleUser      = "user"
	RoleSystem    = "system"
	RoleAssistant = "assistant"
	RoleTool      = "tool"
)

// ChatMessage is message in prompt or generated by LLM.
// Prompt:
// [{Role: system, Content: "..."}, {Role: User, ContentParts: [{Image: ""}]}, {Role: assistant, ToolCalls: [{ID: "", Name: "", Arguments: ""}]}, {Role: Tool, content: "{...}"}]
// Result:
// [Role: assistant, Content: "..."] or [Role: assistant, ToolCallResult: {ID: "", Name: "", Arguments: ""}]
type ChatMessage struct {
	Role string `json:"role"` // system(developer in o-series model) | user | assistant | tool.

	Content          string `json:"content"`
	ReasoningContent string `json:"reasoning_content,omitempty"`

	ContentParts []*LLMChatMessageContentPart `json:",omitempty"`

	ToolCalls []LLMToolFunction `json:"tool_calls,omitempty"`

	// role=tool, openai compatible.
	ToolCallID     string             `json:"tool_call_id,omitempty"`
	ToolCallResult *LLMToolCallResult `json:"tool_call_result,omitempty"`

	CacheControl string `json:"cache_control,omitempty"`
}

func (m *ChatMessage) IsSetCachePoint() bool {
	if len(m.CacheControl) > 0 {
		return true
	}
	for _, part := range m.ContentParts {
		if part.CacheControl != "" {
			return true
		}
	}
	return false
}

func (s *ChatMessage) SetCachePoint(cacheControl string) {
	if len(s.Content) > 0 {
		s.ContentParts = append(s.ContentParts, &LLMChatMessageContentPart{
			Text:         lo.ToPtr(s.Content),
			CacheControl: cacheControl,
		})
		s.Content = ""
	} else if len(s.ContentParts) > 0 {
		s.ContentParts[len(s.ContentParts)-1].CacheControl = cacheControl
	}
}

func (s *ChatMessage) UnsetCachePoint() {
	if s.IsSetCachePoint() {
		s.CacheControl = ""
		for idx := range s.ContentParts {
			s.ContentParts[idx].CacheControl = ""
		}
	}
}

// For convenient, tool call schema in the prompt and tool call returned in the results are all using this struct.
// for prompt: Name + Description + ParametersSchema
// for result: ID + Name + Arguments
type LLMToolFunction struct {
	ID               string `json:"id"`
	Name             string `json:"name"`
	Description      string `json:"description"`
	ParametersSchema any    `json:"parameters_schema"`        // Usually json schema.
	Parameters       any    `json:"parameters"`               // json object.
	RawParameters    string `json:"raw_parameters,omitempty"` // original parameters string, if model returns incomplete parameters (possibly due to max tokens), this is still available
	CacheControl     string `json:"cache_control,omitempty"`
}

type LLMToolCallResult struct {
	ID string `json:"id"`
}

type LLMFinishReason string

const (
	LLMFinishReasonStop          LLMFinishReason = "stop"
	LLMFinishReasonLength        LLMFinishReason = "length"
	LLMFinishReasonFunctionCall  LLMFinishReason = "function_call"
	LLMFinishReasonContentFilter LLMFinishReason = "content_filter"
	LLMFinishReasonNull          LLMFinishReason = "null"
)

type TokenUsage = llm.TokenUsage

type LLMResult = LLMChunk

type LLMChunk struct {
	Content          string
	ReasoningContent string
	ToolCalls        []LLMToolFunction
	TokenUsage       *TokenUsage
	FinishReason     LLMFinishReason
}

// Text | ImageURL
type LLMChatMessageContentPart struct {
	Text *string
	// Either a URL of the image or the base64 encoded image data.
	ImageURL     *string
	CacheControl string
}

type LLM interface {
	ChatCompletion(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) (*LLMResult, error)
	ChatCompletionStream(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) *stream.RecvChannel[LLMChunk]
}

var _ LLM = &OpenAILLM{}

type OpenAILLM struct {
	cli *openai.Client
}

func NewAzureOpenAILLM(ak, endpoint string, opts ...func(*openai.ClientConfig)) (*OpenAILLM, error) {
	if len(endpoint) != 0 {
		conf := openai.DefaultAzureConfig(ak, endpoint)
		conf.HTTPClient = &http.Client{
			Transport: http.DefaultTransport,
			Timeout:   time.Minute * 10,
		}
		for _, opt := range opts {
			opt(&conf)
		}
		return &OpenAILLM{cli: openai.NewClientWithConfig(conf)}, nil
	}
	return &OpenAILLM{cli: openai.NewClient(ak)}, nil
}

func NewOpenAILLM(apiKey, endpoint string) (*OpenAILLM, error) {
	conf := openai.DefaultConfig(apiKey)
	conf.HTTPClient = &http.Client{
		Transport: http.DefaultTransport,
		Timeout:   time.Minute * 10,
	}
	if len(endpoint) != 0 {
		conf.BaseURL = endpoint
	}
	return &OpenAILLM{cli: openai.NewClientWithConfig(conf)}, nil
}

type LLMCompletionOption struct {
	Model       string
	Temperature float32
	MaxTokens   int32
	Stop        []string
	Seed        *int
	TopP        *float32
	SessionID   string

	Tools []LLMToolFunction

	// 类型：string 或 `LLMToolFunction`
	// 用途：控制模型是否及如何调用外部工具（函数）
	// 可选值：
	//   1. auto，自动选择调用函数还是直接输出文本
	//   2. none，禁止调用函数，只返回文本
	//   3. required，要求必定调用函数，但不限具体哪个
	//   4. LLMToolFunction，指定调用某个函数
	//
	// 不同模型的具体可选值可能不一样，常见值：
	// OpenAI: auto/none/required/LLMToolFunction
	// Claude: auto/none/any/LLMToolFunction
	// Gemini: AUTO/NONE/ANY
	//
	// 字节 GPT 平台对不同模型做了兼容，因此 auto/none/any/LLMToolFunction 对于 gemini/claude 都是生效的，OpenAI 则是直接透传
	ToolChoice any `json:"tool_choice,omitempty"`
	Thinking   *config.ThinkingConfig

	FallbackModels []string // fallback model list if the previous model is rate limited.

	Tag string // A record of what this LLM call did.

	PromptCacheSessionKey string
	ParallelToolCalls     *bool

	TextOutputVerbosity *string
}

const LLMTagKey = "X-LLM-TAG"
const LLMTraceIDKey = "X-LLM-TRACEID"

func getCtxWithLLMTag(ctx context.Context, tag string) context.Context {
	if len(tag) > 0 {
		return context.WithValue(ctx, LLMTagKey, tag)
	}
	return ctx
}

func toOpenAISDKChatRequest(promptMsgs []*ChatMessage, opt LLMCompletionOption, stream bool) openai.ChatCompletionRequest {
	req := openai.ChatCompletionRequest{
		Model: opt.Model,
		Messages: lo.Map(promptMsgs, func(item *ChatMessage, index int) openai.ChatCompletionMessage {
			msg := openai.ChatCompletionMessage{
				Role:         item.Role,
				Content:      item.Content,
				MultiContent: nil,
				Name:         "",
				FunctionCall: nil, // deprecated
				ToolCalls:    nil,
				ToolCallID:   item.ToolCallID,
			}
			msg.ToolCalls = lo.Map(item.ToolCalls, func(item LLMToolFunction, index int) openai.ToolCall {
				return openai.ToolCall{
					ID:   item.ID,
					Type: "function",
					Function: openai.FunctionCall{
						Name:      item.Name,
						Arguments: conv.JSONString(item.Parameters),
					},
				}
			})
			if len(item.ContentParts) > 0 {
				msg.MultiContent = lo.Map(item.ContentParts, func(item *LLMChatMessageContentPart, index int) openai.ChatMessagePart {
					part := openai.ChatMessagePart{
						Type: "",
					}
					if item.ImageURL != nil {
						part.Type = "image_url"
						part.ImageURL = &openai.ChatMessageImageURL{
							URL: *item.ImageURL,
						}
					}
					if item.Text != nil {
						part.Type = "text"
						part.Text = *item.Text
					}
					return part
				})
			}
			return msg
		}),
		Temperature: opt.Temperature,
		TopP:        lo.FromPtr(opt.TopP),
		MaxTokens:   int(opt.MaxTokens),
		Stream:      stream,
		Stop:        opt.Stop,
		Tools: lo.Map(opt.Tools, func(item LLMToolFunction, index int) openai.Tool {
			return openai.Tool{
				Type: "function",
				Function: &openai.FunctionDefinition{
					Name:        item.Name,
					Description: item.Description,
					Parameters:  item.ParametersSchema,
				},
			}
		}),
		ParallelToolCalls: opt.ParallelToolCalls,
	}
	if len(opt.Tools) > 0 {
		req.ToolChoice = "auto"
	}
	return req
}

func (o *OpenAILLM) ChatCompletion(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) (*LLMResult, error) {
	ctx = getCtxWithLLMTag(ctx, opt.Tag)
	req := toOpenAISDKChatRequest(promptMsgs, opt, false)

	res, err := o.cli.CreateChatCompletion(ctx, req)
	if err != nil {
		return nil, err
	}
	if len(res.Choices) == 0 {
		return nil, errors.New("no choices")
	}
	msg := res.Choices[0].Message
	result := &LLMResult{
		Content:          msg.Content,
		ReasoningContent: "",
		FinishReason:     LLMFinishReason(res.Choices[0].FinishReason),
		TokenUsage: &TokenUsage{
			PromptTokens:     res.Usage.PromptTokens,
			CompletionTokens: res.Usage.CompletionTokens,
			TotalTokens:      res.Usage.TotalTokens,
		},
	}
	if len(msg.ToolCalls) > 0 {
		result.ToolCalls = lo.Map(msg.ToolCalls, func(item openai.ToolCall, index int) LLMToolFunction {
			return LLMToolFunction{
				ID:         item.ID,
				Name:       item.Function.Name,
				Parameters: item.Function.Arguments,
			}
		})
	}
	return result, nil
}

func (o *OpenAILLM) ChatCompletionStream(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) *stream.RecvChannel[LLMChunk] {
	ctx = getCtxWithLLMTag(ctx, opt.Tag)
	const bufSize = 50
	send, recv := stream.NewChannel[LLMChunk](bufSize)

	go func() (err error) {
		defer func() {
			if err != nil {
				send.PublishError(err, false)
			}
			send.Close()
		}()
		req := toOpenAISDKChatRequest(promptMsgs, opt, true)

		res, err := o.cli.CreateChatCompletionStream(ctx, req)
		if err != nil {
			return err
		}

		defer res.Close()

		stream.Generate(ctx, send, false, func(acc *LLMChunk) (*LLMChunk, error) {
			chunk, err := res.Recv()
			if err != nil {
				return nil, err
			}
			// gpt-4o-mini may return empty choices for the first result
			if len(chunk.Choices) == 0 {
				return nil, nil
			}

			chunkResult := &LLMChunk{
				Content:          chunk.Choices[0].Delta.Content,
				ReasoningContent: "",
				ToolCalls: lo.Map(
					chunk.Choices[0].Delta.ToolCalls,
					func(item openai.ToolCall, index int) LLMToolFunction {
						return LLMToolFunction{
							ID:         item.ID,
							Name:       item.Function.Name,
							Parameters: item.Function.Arguments,
						}
					},
				),
				FinishReason: LLMFinishReason(chunk.Choices[0].FinishReason),
			}

			if chunk.Usage != nil {
				chunkResult.TokenUsage = &TokenUsage{
					PromptTokens:     chunk.Usage.PromptTokens,
					CompletionTokens: chunk.Usage.CompletionTokens,
					TotalTokens:      chunk.Usage.TotalTokens,
				}
			}

			return chunkResult, nil
		})

		return nil
	}()

	return recv
}

var _ LLM = &LLMService{}

type LLMService struct {
	Service llm.Service
}

func toLLMServiceChatRequest(promptMsgs []*ChatMessage, opt LLMCompletionOption, stream bool) llm.ChatCompletionRequest {
	req := llm.ChatCompletionRequest{
		Model: opt.Model,
		Messages: lo.Map(promptMsgs, func(item *ChatMessage, index int) llm.ChatCompletionMessage {
			msg := llm.ChatCompletionMessage{
				Role:       item.Role,
				Content:    item.Content,
				ToolCallID: item.ToolCallID,
			}
			msg.ToolCalls = lo.Map(item.ToolCalls, func(item LLMToolFunction, index int) llm.ToolCall {
				return llm.ToolCall{
					ID:   item.ID,
					Type: "function",
					Function: llm.FunctionCall{
						Name:      item.Name,
						Arguments: conv.JSONString(item.Parameters),
					},
				}
			})
			if len(item.ContentParts) > 0 {
				msg.MultiContent = lo.Map(item.ContentParts, func(item *LLMChatMessageContentPart, index int) llm.ChatMessagePart {
					part := llm.ChatMessagePart{
						Type: "",
					}
					if item.ImageURL != nil {
						part.Type = "image_url"
						part.ImageURL = &llm.ChatMessageImageURL{
							URL: *item.ImageURL,
						}
					}
					if item.Text != nil {
						part.Type = "text"
						part.Text = *item.Text
					}
					return part
				})
			}
			return msg
		}),
		MaxTokens:   lo.Ternary(opt.MaxTokens != 0, lo.ToPtr(int(opt.MaxTokens)), nil),
		Temperature: &opt.Temperature,
		Stream:      stream,
		Stop:        opt.Stop,
		SensitiveOpt: llm.SensitiveOpt{
			DisableAntiDirt:       lo.ToPtr(true),
			DisableLLMSecurity:    lo.ToPtr(true),
			EnableAntiDirt4Output: lo.ToPtr(false),
		},
		Account: &entity.Account{
			Username: "",
		},
		AppID:             "",
		Tag:               opt.Tag,
		Record:            true,
		SessionID:         opt.SessionID,
		FallbackModels:    opt.FallbackModels,
		ToolChoice:        nil,
		ParallelToolCalls: opt.ParallelToolCalls,
		Tools: lo.Map(opt.Tools, func(item LLMToolFunction, index int) llm.Tool {
			tool := llm.Tool{
				Type: "function",
				Function: llm.FunctionDefinition{
					Name:        item.Name,
					Description: item.Description,
					Parameters:  item.ParametersSchema,
				},
			}
			if len(item.CacheControl) > 0 {
				tool.CacheControl = &llm.CacheControl{
					Type: item.CacheControl,
				}
			}
			return tool
		}),
	}
	if opt.ToolChoice != nil {
		switch t := opt.ToolChoice.(type) {
		case string:
			req.ToolChoice = t
		case LLMToolFunction:
			req.ToolChoice = llm.Tool{
				Type: "function",
				Function: llm.FunctionDefinition{
					Name:        t.Name,
					Description: t.Description,
					Parameters:  t.ParametersSchema,
				},
			}
		default:
			// Not specified, use auto if there are tools.
			if len(opt.Tools) > 0 {
				req.ToolChoice = "auto"
			}
		}
	}
	return req
}

func (s *LLMService) ChatCompletion(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) (*LLMResult, error) {
	res, err := s.Service.ChatCompletion(ctx, toLLMServiceChatRequest(promptMsgs, opt, false))
	if err != nil {
		return nil, err
	}
	defer res.Close(ctx)

	agg, err := res.Aggregation(ctx)
	if err != nil {
		return nil, err
	}

	stats := res.Statistics(ctx)

	result := &LLMResult{
		Content: agg.GetFirstChoiceContent(),
		TokenUsage: &TokenUsage{
			PromptTokens:     stats.PromptTokens,
			CompletionTokens: stats.CompletionTokens,
			TotalTokens:      stats.TotalTokens,
		},
	}

	if len(agg.Choices) > 0 {
		result.ReasoningContent = agg.Choices[0].Message.ReasoningContent
		result.ToolCalls = lo.Map(agg.Choices[0].Message.ToolCalls, func(item llm.ToolCall, index int) LLMToolFunction {
			return LLMToolFunction{
				ID:         item.ID,
				Name:       item.Function.Name,
				Parameters: item.Function.Arguments,
			}
		})
	}

	return result, nil
}

func (s *LLMService) ChatCompletionStream(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) *stream.RecvChannel[LLMChunk] {
	const bufSize = 50
	send, recv := stream.NewChannel[LLMChunk](bufSize)

	go func() (err error) {
		defer func() {
			if err != nil {
				send.PublishError(err, false)
			}
			send.Close()
		}()

		res, err := s.Service.ChatCompletion(ctx, toLLMServiceChatRequest(promptMsgs, opt, true))
		if err != nil {
			return err
		}

		defer res.Close(ctx)

		stream.Generate(ctx, send, false, func(acc *LLMChunk) (*LLMChunk, error) {
			chunk, err := res.NextChunk(ctx)
			if err != nil {
				return nil, err
			}
			chunkRes := &LLMChunk{
				Content:    chunk.GetFirstChoiceContent(),
				TokenUsage: nil,
			}
			if len(chunk.Choices) > 0 {
				chunkRes.ReasoningContent = chunk.Choices[0].Delta.ReasoningContent
				chunkRes.ToolCalls = lo.Map(chunk.Choices[0].Delta.ToolCalls, func(item llm.ToolCall, index int) LLMToolFunction {
					return LLMToolFunction{
						ID:         item.ID,
						Name:       item.Function.Name,
						Parameters: item.Function.Arguments,
					}
				})
			}
			return chunkRes, nil
		})

		stats := res.Statistics(ctx)
		send.Publish(LLMChunk{
			Content: "",
			TokenUsage: &TokenUsage{
				PromptTokens:     stats.PromptTokens,
				CompletionTokens: stats.CompletionTokens,
				TotalTokens:      stats.TotalTokens,
			},
		})

		return nil
	}()

	return recv
}

var _ LLM = &CustomOpenAILLM{}

type CustomOpenAILLM struct {
	cli *customopenai.Client
}

func NewCustomOpenAILLM(apiKey, endpoint string, hook customopenai.RequestHook) (*CustomOpenAILLM, error) {
	cli, err := customopenai.NewClient(customopenai.ClientOption{
		Endpoint:    endpoint,
		APIKey:      apiKey,
		Timeout:     time.Minute * 10,
		Headers:     map[string]string{},
		RequestHook: hook,
	})
	if err != nil {
		return nil, err
	}
	return &CustomOpenAILLM{cli: cli}, nil
}

func toCustomOpenAISDKChatRequest(promptMsgs []*ChatMessage, opt LLMCompletionOption, stream bool) *customopenai.ChatCompletionsRequest {
	req := &customopenai.ChatCompletionsRequest{
		Model: opt.Model,
		Messages: lo.Map(promptMsgs, func(item *ChatMessage, index int) customopenai.ChatCompletionMessage {
			msg := customopenai.ChatCompletionMessage{
				Role:             item.Role,
				Content:          item.Content,
				MultiContent:     nil,
				ToolCalls:        nil,
				ToolCallID:       item.ToolCallID,
				Refusal:          "",
				CacheControl:     nil,
				ReasoningContent: item.ReasoningContent,
			}

			if len(item.CacheControl) > 0 {
				msg.CacheControl = &customopenai.CacheControl{
					Type: item.CacheControl,
				}
			}
			msg.ToolCalls = lo.Map(item.ToolCalls, func(item LLMToolFunction, index int) customopenai.ToolCall {
				toolcall := customopenai.ToolCall{
					ID:   item.ID,
					Type: "function",
					Function: customopenai.FunctionCall{
						Name:      item.Name,
						Arguments: conv.JSONString(item.Parameters),
					},
				}
				return toolcall
			})
			if len(item.ContentParts) > 0 {
				msg.MultiContent = lo.Map(item.ContentParts, func(item *LLMChatMessageContentPart, index int) customopenai.ChatMessagePart {
					part := customopenai.ChatMessagePart{
						Type: "",
					}
					if item.ImageURL != nil {
						part.Type = "image_url"
						part.ImageURL = &customopenai.ChatMessageImageURL{
							URL: *item.ImageURL,
						}
					}
					if item.Text != nil {
						part.Type = "text"
						part.Text = *item.Text
					}
					if len(item.CacheControl) != 0 {
						part.CacheControl = &customopenai.CacheControl{
							Type: item.CacheControl,
						}
					}
					return part
				})
			}
			return msg
		}),
		Temperature: opt.Temperature,
		TopP:        lo.FromPtr(opt.TopP),
		MaxTokens:   int(opt.MaxTokens),
		Stream:      stream,
		Stop:        opt.Stop,
		Tools: lo.Map(opt.Tools, func(item LLMToolFunction, index int) customopenai.Tool {
			tool := customopenai.Tool{
				Type: "function",
				Function: &customopenai.FunctionDefinition{
					Name:        item.Name,
					Description: item.Description,
					Parameters:  item.ParametersSchema,
				},
			}
			if len(item.CacheControl) > 0 {
				tool.CacheControl = &customopenai.CacheControl{
					Type: item.CacheControl,
				}
			}
			return tool
		}),
		ParallelToolCalls: opt.ParallelToolCalls,
		Seed:              opt.Seed,
		Thinking:          nil,
		ExtraOptions: map[string]any{
			"session_id":      opt.SessionID,
			"fallback_models": opt.FallbackModels,
			"tag":             opt.Tag,
		},
	}
	if len(opt.PromptCacheSessionKey) > 0 {
		req.ExtraOptions["prompt_cache_session_key"] = opt.PromptCacheSessionKey
	}
	if opt.Thinking != nil {
		req.Thinking = &customopenai.ThinkingOption{
			Type:            opt.Thinking.Type,
			BudgetTokens:    opt.Thinking.BudgetTokens,
			IncludeThoughts: opt.Thinking.IncludeThoughts,
		}
	}
	if opt.TextOutputVerbosity != nil {
		req.Text = &customopenai.TextOption{
			Verbosity: opt.TextOutputVerbosity,
		}
	}
	if opt.ToolChoice != nil {
		switch t := opt.ToolChoice.(type) {
		case string:
			req.ToolChoice = t
		case LLMToolFunction:
			req.ToolChoice = customopenai.Tool{
				Type: "function",
				Function: &customopenai.FunctionDefinition{
					Name:        t.Name,
					Description: t.Description,
					Parameters:  t.ParametersSchema,
				},
			}
		default:
			if len(opt.Tools) > 0 {
				// Not specified, use auto if there are tools.
				req.ToolChoice = "auto"
			}
		}
	}
	return req
}

func (o *CustomOpenAILLM) ChatCompletion(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) (*LLMResult, error) {
	ctx = getCtxWithLLMTag(ctx, opt.Tag)
	req := toCustomOpenAISDKChatRequest(promptMsgs, opt, false)

	res, err := o.cli.ChatCompletion(ctx, req)
	if err != nil {
		return nil, err
	}
	if len(res.Choices) == 0 {
		return nil, errors.New("no choices")
	}
	msg := res.Choices[0].Message
	result := &LLMResult{
		Content:          msg.Content,
		ReasoningContent: msg.ReasoningContent,
		FinishReason:     LLMFinishReason(res.Choices[0].FinishReason),
		TokenUsage: &TokenUsage{
			PromptTokens:             res.Usage.PromptTokens,
			CompletionTokens:         res.Usage.CompletionTokens,
			TotalTokens:              res.Usage.TotalTokens,
			ReasoningTokens:          res.Usage.ReasoningTokens,
			CacheCreationInputTokens: res.Usage.CacheCreationInputTokens,
			CacheReadInputTokens:     res.Usage.CacheReadInputTokens,
		},
	}
	if len(msg.ToolCalls) > 0 {
		result.ToolCalls = lo.Map(msg.ToolCalls, func(item customopenai.ToolCall, index int) LLMToolFunction {
			return LLMToolFunction{
				ID:         item.ID,
				Name:       item.Function.Name,
				Parameters: item.Function.Arguments,
			}
		})
	}
	return result, nil
}

func (o *CustomOpenAILLM) ChatCompletionStream(ctx context.Context, promptMsgs []*ChatMessage, opt LLMCompletionOption) *stream.RecvChannel[LLMChunk] {
	ctx = getCtxWithLLMTag(ctx, opt.Tag)
	const bufSize = 50
	send, recv := stream.NewChannel[LLMChunk](bufSize)

	go func() (err error) {
		defer func() {
			if err != nil {
				send.PublishError(err, false)
			}
			send.Close()
		}()
		req := toCustomOpenAISDKChatRequest(promptMsgs, opt, true)

		res, err := o.cli.ChatCompletionStream(ctx, req)
		if err != nil {
			return err
		}

		defer res.Close()

		err = stream.ForwardWithFilterNil(ctx, res, send, false, func(chunk *customopenai.ChatStreamResponse) LLMChunk {
			// gpt series model may return empty choices for the first result and last result.
			if len(chunk.Choices) == 0 {
				result := LLMChunk{}
				if chunk.Usage != nil {
					result.TokenUsage = &TokenUsage{
						PromptTokens:             chunk.Usage.PromptTokens,
						CompletionTokens:         chunk.Usage.CompletionTokens,
						TotalTokens:              chunk.Usage.TotalTokens,
						ReasoningTokens:          chunk.Usage.ReasoningTokens,
						CacheCreationInputTokens: chunk.Usage.CacheCreationInputTokens,
						CacheReadInputTokens:     chunk.Usage.CacheReadInputTokens,
					}
				}
				return result
			}

			chunkResult := LLMChunk{
				Content:          chunk.Choices[0].Delta.Content,
				ReasoningContent: chunk.Choices[0].Delta.ReasoningContent,
				ToolCalls: lo.Map(
					chunk.Choices[0].Delta.ToolCalls,
					func(item customopenai.ToolCall, index int) LLMToolFunction {
						return LLMToolFunction{
							ID:         item.ID,
							Name:       item.Function.Name,
							Parameters: item.Function.Arguments,
						}
					},
				),
				FinishReason: LLMFinishReason(chunk.Choices[0].FinishReason),
			}

			if chunk.Usage != nil {
				chunkResult.TokenUsage = &TokenUsage{
					PromptTokens:             chunk.Usage.PromptTokens,
					CompletionTokens:         chunk.Usage.CompletionTokens,
					TotalTokens:              chunk.Usage.TotalTokens,
					ReasoningTokens:          chunk.Usage.ReasoningTokens,
					CacheCreationInputTokens: chunk.Usage.CacheCreationInputTokens,
					CacheReadInputTokens:     chunk.Usage.CacheReadInputTokens,
				}
			}

			return chunkResult
		})

		return err
	}()

	return recv
}
