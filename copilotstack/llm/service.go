package llm

import (
	"context"
	stderrors "errors"
	"strconv"
	"time"

	"code.byted.org/data/laplace_go_client/laplace_client/kitex_gen/lagrange/laplace/laplaceservingservice"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	lru "github.com/hashicorp/golang-lru/v2"
	openaisdk "github.com/openai/openai-go/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"
	"github.com/volcengine/volc-sdk-golang/service/maas"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/tokenizer"
	"code.byted.org/devgpt/kiwis/port/antidirt"
	"code.byted.org/devgpt/kiwis/port/bytedagi"
	"code.byted.org/devgpt/kiwis/port/llmflow"
	"code.byted.org/devgpt/kiwis/port/llmsecurity"
)

var (
	ErrSensitiveContent     = errors.New("sensitive content")
	ErrLengthyCodeUnderTest = errors.New("length of target code under test is too long")
	// ErrModelServiceNotSupported indicates current service does not support the model service.
	ErrModelServiceNotSupported = errors.New("model service is not supported")
	ErrModelRateLimited         = errors.New("model QPM/TPM rate limited")
	ErrModelNotFound            = errors.New("model is not found, please check model name and model auth configs")
	ErrPromptTokensOverLimit    = errors.New("prompt tokens over limit")
	ErrModelServiceUnavailable  = errors.New("model service is unavailable")
)

var _ Service = &ModelService{}

// Service provides unified LLM interface.
type Service interface {
	ChatCompletion(ctx context.Context, req ChatCompletionRequest) (ChatCompletionStreamResult, error)
	Embedding(ctx context.Context, req EmbeddingRequest) (*EmbeddingResult, error)
}

type ModelService struct {
	azureCliPool          *lru.Cache[string, *openai.Client]
	maasCliPool           *lru.Cache[string, *maas.MaaS]
	maasV3CliPool         *lru.Cache[string, *arkruntime.Client]
	deepseekCliPool       *lru.Cache[string, *hertz.Client]
	openaiHTTPCliPool     *lru.Cache[string, *hertz.Client]
	codekgCliPool         *lru.Cache[string, laplaceservingservice.Client]
	codekgHttpCliPool     *lru.Cache[string, *hertz.Client]
	bytedagiCliPool       *bytedagi.Pool
	fornaxCliPool         *lru.Cache[string, fornaxClient]
	llmflowCliPool        *llmflow.LLMFlowClientPool
	officialOpenaiCliPool *lru.Cache[string, *openaisdk.Client]

	modelAuthConfig       *tcc.GenericConfig[config.ModelDispatchConfig]
	errorCodeConfig       *tcc.GenericConfig[config.ErrorCodeConfig]
	journalService        journal.JournalService
	antidirtCli           *antidirt.AntidirtCli
	llmSecurityCli        *llmsecurity.LLMSecurityCli
	createOpt             CreateModelServiceOption
	resourceManageService ResourceManageService
}

type CreateModelServiceOption struct {
	fx.In
	ModelAuthConfig *tcc.GenericConfig[config.ModelDispatchConfig]
	ErrorCodeConfig *tcc.GenericConfig[config.ErrorCodeConfig] `optional:"true"`

	JournalService        journal.JournalService      `optional:"true"`
	AntiDirtClient        *antidirt.AntidirtCli       `optional:"true"` // Some regions do not have antidirt service.
	LLMSecurityClient     *llmsecurity.LLMSecurityCli `optional:"true"` // Some regions do not have llm_security service.
	ResourceManageService ResourceManageService       `optional:"true"`
}

func NewModelService(c CreateModelServiceOption) (*ModelService, error) {
	var (
		err error
	)

	azurePool, err := lru.New[string, *openai.Client](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create openai client pool")
	}

	maasPool, err := lru.New[string, *maas.MaaS](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create maas client pool")
	}

	maasV3Pool, err := lru.New[string, *arkruntime.Client](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create maas v3 client pool")
	}

	openaiHTTPCliPool, err := lru.New[string, *hertz.Client](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create http client pool")
	}

	deepseekPool, err := lru.New[string, *hertz.Client](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create deepseek client pool")
	}

	codeKGPool, err := lru.New[string, laplaceservingservice.Client](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create codeKG client pool")
	}

	codeKGHttpPool, err := lru.New[string, *hertz.Client](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create codeKG client pool")
	}

	bytedagiPool, err := bytedagi.NewPool(defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create bytedagi client pool")
	}

	fornaxPool, err := lru.New[string, fornaxClient](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create fornax client pool")
	}
	llmflowPool, err := llmflow.NewLLMFlowClientPool()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create llmflow client pool")
	}

	officialOpenaiCliPool, err := lru.New[string, *openaisdk.Client](defaultAzureClientSize)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create official openai client pool")
	}

	s := &ModelService{
		azureCliPool:          azurePool,
		maasCliPool:           maasPool,
		maasV3CliPool:         maasV3Pool,
		deepseekCliPool:       deepseekPool,
		openaiHTTPCliPool:     openaiHTTPCliPool,
		codekgCliPool:         codeKGPool,
		codekgHttpCliPool:     codeKGHttpPool,
		bytedagiCliPool:       bytedagiPool,
		modelAuthConfig:       c.ModelAuthConfig,
		errorCodeConfig:       c.ErrorCodeConfig,
		journalService:        c.JournalService,
		antidirtCli:           c.AntiDirtClient,
		llmSecurityCli:        c.LLMSecurityClient,
		fornaxCliPool:         fornaxPool,
		llmflowCliPool:        llmflowPool,
		officialOpenaiCliPool: officialOpenaiCliPool,
		resourceManageService: c.ResourceManageService,
		createOpt:             c,
	}

	return s, nil
}

// Stats LLM related Stats
type Stats struct {
	ID                 string
	ChunkCount         int
	FirstCountLatency  time.Duration
	TotalTimeCost      time.Duration
	PromptTokens       int
	CompletionTokens   int
	ReasoningTokens    int
	TotalTokens        int
	ContentRaw         string
	ReasoningContent   string
	ToolCalls          []ToolCall
	Status             journal.PromptCompletionStatus
	FinishReason       string
	Error              string
	PromptCompletionID int64

	CacheCreationInputTokens int
	CacheReadInputTokens     int
}

// Metadata LLM related Metadata
type Metadata struct {
	PromptCompletionID int64
	ModelName          string
}

type ChatCompletionStreamResult = stream.Streamer[ChatCompletionStreamResponse, ChatCompletionResponse, Stats, Metadata]

func (s *ModelService) doChatCompletion(ctx context.Context, req ChatCompletionRequest, model string, logResult *journal.PromptCompletion) (r *LLMStreamResult, err error) {
	newReq := req
	newReq.Model = model

	modelTag := &metrics.CopilotModelTraceTag{
		Name:         newReq.Model,
		Type:         newReq.Tag,
		AppID:        newReq.AppID,
		Stream:       lo.Ternary(newReq.Stream, "stream", "non-stream"),
		RequestModel: req.Model,
	}
	_ = metrics.CM.ModelThroughput.WithTags(modelTag).Add(1)
	defer func() {
		if err != nil {
			if !errors.Is(err, ErrSensitiveContent) {
				modelTag.Error = getErrorType(err)
				_ = metrics.CM.ModelErrorThroughput.WithTags(modelTag).Add(1)
			}
		}
	}()
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	if newReq.Record && logResult != nil {
		// If err is returned, update journal log.
		defer func() {
			if err != nil {
				if _, updateErr := s.journalService.UpdateLog(ctx, journal.UpdateLogOption{
					ID:     logResult.ID,
					Status: lo.ToPtr(journal.StatusFail),
					RequestMetadata: &journal.PromptCompletionRequestMetadata{
						Error: lo.ToPtr(err.Error()),
					},
				}); updateErr != nil {
					log.V1.CtxWarn(ctx, "failed to update journal: %v", updateErr)
				}
			}
		}()
	}

	var (
		result  ChatCompletionStreamResult
		chatErr error
	)

	modelConfig := &config.ModelAuthConfig{}
	if req.CustomModelConfig != nil {
		// 用户自定义的模型
		modelConfig = req.CustomModelConfig
	} else {
		modelConfig, err = s.getChatModelAuthConfig(ctx, newReq.AppID, newReq.Model)
		if err != nil {
			return nil, stderrors.Join(err, ErrModelNotFound)
		}

		realModelName, ok := modelConfig.ModelNameAlias[newReq.Model]
		if ok {
			log.V1.CtxInfo(ctx, "mapping alias model name: %s -> %s", newReq.Model, realModelName)
			newReq.Model = realModelName
		}
	}

	log.V1.CtxInfo(ctx, "found model config: %s", modelConfig.Type)

	start := time.Now()

	switch modelConfig.Type {
	case config.ModelTypeAzure, config.ModelTypeOpenAI:
		result, chatErr = s.callAzureOpenAIChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeOpenAICompatible:
		result, chatErr = s.callOpenAICompatibleChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeMaaS:
		result, chatErr = s.callMaaSChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeMaaSV3:
		result, chatErr = s.callMaaSV3ChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeEcho:
		result, chatErr = s.callEchoChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeDeepSeekHTTP:
		result, chatErr = s.callDeepseekChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeCodeKG:
		result, chatErr = s.callCodeKG(ctx, newReq, modelConfig)
	case config.ModelTypeBytedAGI:
		result, chatErr = s.callBytedagiChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeFornax:
		result, chatErr = s.callFornaxChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeLLMFlow:
		result, chatErr = s.callLLMFlowChatCompletion(ctx, newReq, modelConfig)
	case config.ModelTypeOpenAIResponses:
		result, chatErr = s.callOpenAIResponsesCompletion(ctx, newReq, modelConfig)
	default:
		return nil, errors.Errorf("unsupported model: %s", modelConfig.Type)
	}

	if chatErr != nil {
		return nil, chatErr
	}

	return &LLMStreamResult{
		ChatCompletionStreamResult: result,
		promptCompletion:           logResult,
		journalService:             s.journalService,
		logUpdated:                 false,
		req:                        &newReq,
		firstChunk:                 true,
		lastChunkTs:                start,
		startedAt:                  start,
		antiDirtClient:             s.antidirtCli,
		originalModelName:          req.Model,
		// lLMSecurityClient:          s.llmSecurityCli,
	}, nil
}

func (s *ModelService) getChatModelAuthConfig(ctx context.Context, appID, modelName string) (*config.ModelAuthConfig, error) {
	if modelName == "echo" {
		return &config.ModelAuthConfig{
			Type: config.ModelTypeEcho,
		}, nil
	}
	return config.GetModelAuthConfig(appID, modelName, s.modelAuthConfig.GetPointer())
}

func (s *ModelService) ChatCompletion(ctx context.Context, req ChatCompletionRequest) (r ChatCompletionStreamResult, err error) {
	modelTag := &metrics.CopilotModelTag{
		Name:   req.Model,
		Type:   req.Tag,
		AppID:  req.AppID,
		Stream: lo.Ternary(req.Stream, "stream", "non-stream"),
	}
	if ctx.Err() != nil {
		return nil, ctx.Err()
	}
	var (
		logResult *journal.PromptCompletion
	)
	if s.journalService == nil {
		log.V1.CtxInfo(ctx, "no journal service provided, will not record model request")
		req.Record = false
	}
	if req.Record {
		req.RequestMetadata.Stream = lo.ToPtr(req.Stream)
		req.RequestMetadata.MaxTokens = req.MaxTokens
		req.RequestMetadata.Temperature = req.Temperature
		req.RequestMetadata.TopP = req.TopP
		req.RequestMetadata.TopK = req.TopK
		req.RequestMetadata.N = req.N
		req.RequestMetadata.FrequencyPenalty = req.FrequencyPenalty
		req.RequestMetadata.PresencePenalty = req.PresencePenalty
		req.RequestMetadata.MinNewTokens = req.MinNewTokens
		req.RequestMetadata.MaxPromptTokens = req.MaxPromptTokens
		req.RequestMetadata.RepetitionPenalty = req.RepetitionPenalty
		req.RequestMetadata.ParallelToolCalls = req.ParallelToolCalls
		req.RequestMetadata.PromptCacheKey = req.PromptCacheKey
		if req.Thinking != nil {
			req.RequestMetadata.ThinkingConfig = &config.ThinkingConfig{
				Type:            req.Thinking.Type,
				BudgetTokens:    req.Thinking.BudgetTokens,
				IncludeThoughts: req.Thinking.IncludeThoughts,
			}
		}
		if req.TextOutputOption != nil {
			req.RequestMetadata.TextOutputOption = &journal.TextOutputOption{
				Verbosity: req.TextOutputOption.Verbosity,
			}
		}
		if req.ToolChoice != nil {
			req.RequestMetadata.ToolChoice = req.ToolChoice
		}
		req.RequestMetadata.LogID = lo.ToPtr(ctxvalues.LogIDDefault(ctx))
		req.RequestMetadata.FallbackModels = append([]string{req.Model}, req.FallbackModels...)

		if req.RequestMetadata.ContextVariables != nil {
			count, _ := tokenizer.CountModelToken(conv.DefaultAny[string](req.RequestMetadata.ContextVariables["input"]), req.Model)
			req.RequestMetadata.UserInputTokens = lo.ToPtr(count)
		}
		// 记录prompt长度，防止在请求失败后丢失usage信息
		if len(req.Messages) > 0 {
			prompt := GetMessagesPromptString(req.Messages, req.Tools, nil)
			count, _ := tokenizer.CountModelToken(prompt, req.Model)
			req.RequestMetadata.Usage = &journal.Usage{
				PromptTokens: count,
				TotalTokens:  count,
			}
		}

		logResult, err = s.journalService.CreateLog(ctx, journal.CreateLogOption{
			AppId:           &req.AppID,
			Username:        lo.FromPtr(req.Account).Username,
			ModelName:       req.Model,
			Status:          journal.StatusFail,
			SessionId:       req.SessionID,
			Prompt:          lo.ToPtr(GetMessagesPromptString(req.Messages, req.Tools, s.resourceManageService)),
			Type:            req.Tag,
			RequestMetadata: req.RequestMetadata,
			ContextVariable: req.ContextVariable,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to create journal: %s", err.Error())
		} else {
			ctx = logs.CtxAddKVs(ctx, "prompt_completion_id", strconv.Itoa(int(logResult.ID)))
			log.V1.CtxInfo(ctx, "created prompt completion log, id: %d, session_id: %s", logResult.ID, logResult.SessionID)
			// If err is returned, update journal log.
			defer func() {
				if err != nil {
					if _, updateErr := s.journalService.UpdateLog(ctx, journal.UpdateLogOption{
						ID:     logResult.ID,
						Status: lo.ToPtr(journal.StatusFail),
						RequestMetadata: &journal.PromptCompletionRequestMetadata{
							Error: lo.ToPtr(err.Error()),
						},
					}); updateErr != nil {
						log.V1.CtxWarn(ctx, "failed to update journal: %v", updateErr)
					}
				}
			}()
		}
	}

	if !lo.FromPtr(req.SensitiveOpt.DisableAntiDirt) && s.antidirtCli != nil {
		var matched bool
		matched, err = s.checkAntiDirt(ctx, &req)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to do antidirt check: %v", err)
		}
		if matched {
			_ = metrics.CM.ModelAntiDirtHitThroughput.WithTags(modelTag).Add(1)
			return nil, ErrSensitiveContent
		}
	}
	if !lo.FromPtr(req.SensitiveOpt.DisableLLMSecurity) && s.llmSecurityCli != nil {
		var matched bool
		matched, err = s.checkLLMSecurity(ctx, &req)
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to check llm_security: %v", err)
		}
		if matched {
			_ = metrics.CM.ModelLLMSecurityHitThroughput.WithTags(modelTag).Add(1)
			return nil, ErrSensitiveContent
		}
	}

	_ = metrics.CM.ModelOverallThroughput.WithTags(modelTag).Add(1)
	defer func() {
		if err != nil {
			if !errors.Is(err, ErrSensitiveContent) {
				_ = metrics.CM.ModelOverallErrorThroughput.WithTags(modelTag).Add(1)
			}
		}
	}()

	fallbackModels := append([]string{req.Model}, req.FallbackModels...)
	if len(fallbackModels) > 1 {
		log.V1.CtxInfo(ctx, "fallback models list: %v", fallbackModels)
	}

	lastModel := req.Model
	for idx, model := range fallbackModels {
		var res *LLMStreamResult
		res, err = s.doChatCompletion(ctx, req, model, logResult)
		if err != nil {
			log.V1.CtxWarn(ctx, "model %d-%s error: %v", idx, model, err)
			// Currently, only rate limit error will be retried.
			if errors.Is(err, ErrModelRateLimited) {

				// retry current model
				retryTimes := req.RetryTimesBeforeFallback
				retryIdx := 0
				for retryTimes > 0 {
					log.V1.CtxWarn(ctx, "model %d-%s is rate limited: %v, retry: %v", idx, model, err, retryIdx)
					_ = metrics.CM.ModelRetryBeforeFallbackThroughput.WithTags(&metrics.CopilotModelTraceTag{
						Name:         model,
						Type:         req.Tag,
						AppID:        req.AppID,
						Stream:       lo.Ternary(req.Stream, "stream", "non-stream"),
						FromModel:    lastModel,
						RequestModel: req.Model,
					}).Add(1)

					res, err = s.doChatCompletion(ctx, req, model, logResult)
					if err == nil || !errors.Is(err, ErrModelRateLimited) {
						//if success or not rate limit break
						break
					}
					retryTimes--
					retryIdx++
				}
				if errors.Is(err, ErrModelRateLimited) {
					_ = metrics.CM.ModelFallbackThroughput.WithTags(&metrics.CopilotModelTraceTag{
						Name:         model,
						Type:         req.Tag,
						AppID:        req.AppID,
						Stream:       lo.Ternary(req.Stream, "stream", "non-stream"),
						FromModel:    lastModel,
						RequestModel: req.Model,
					}).Add(1)
					// retry still rate limit to fallback
					log.V1.CtxWarn(ctx, "model %d-%s is rate limited: %v, fallback: %v", idx, model, err, len(fallbackModels) > 1)
					lastModel = model
					continue
				}
				if err == nil {
					// retry success
					_ = metrics.CM.ModelRetrySuccessBeforeFallbackThroughput.WithTags(&metrics.CopilotModelTraceTag{
						Name:         model,
						Type:         req.Tag,
						AppID:        req.AppID,
						Stream:       lo.Ternary(req.Stream, "stream", "non-stream"),
						FromModel:    lastModel,
						RequestModel: req.Model,
					}).Add(1)
				}
			} else if errors.Is(err, ErrModelServiceUnavailable) {
				_ = metrics.CM.ModelFallbackThroughput.WithTags(&metrics.CopilotModelTraceTag{
					Name:         model,
					Type:         req.Tag,
					AppID:        req.AppID,
					Stream:       lo.Ternary(req.Stream, "stream", "non-stream"),
					FromModel:    lastModel,
					RequestModel: req.Model,
				}).Add(1)
				log.V1.CtxWarn(ctx, "model %d-%s is service unavailable: %v, fallback: %v", idx, model, err, len(fallbackModels) > 1)
				lastModel = model
				continue
			}
		}
		if res != nil {
			if idx != 0 {
				_ = metrics.CM.ModelFallbackSuccessThroughput.WithTags(&metrics.CopilotModelTraceTag{
					Name:         model,
					Type:         req.Tag,
					AppID:        req.AppID,
					Stream:       lo.Ternary(req.Stream, "stream", "non-stream"),
					FromModel:    lastModel,
					RequestModel: req.Model,
				}).Add(1)
			}
			res.retriedTimes = idx
			r = res
		}
		return
	}

	return
}

func (s *Stats) toUpdateLogOption(promptCompletionId int64) journal.UpdateLogOption {
	contentRaw := s.ContentRaw
	if len(s.ToolCalls) > 0 {
		contentRaw = "<tool_calls>\n" + conv.JSONFormatString(s.ToolCalls) + "\n</tool_calls>\n" + contentRaw
	}
	if len(s.ReasoningContent) > 0 {
		contentRaw = "<reasoning_content>\n" + s.ReasoningContent + "\n</reasoning_content>\n" + contentRaw
	}

	return journal.UpdateLogOption{
		ID:         promptCompletionId,
		ContentRaw: lo.ToPtr(contentRaw),
		Status:     lo.ToPtr(s.Status),
		RequestMetadata: &journal.PromptCompletionRequestMetadata{
			ID:    lo.ToPtr(s.ID),
			Error: lo.ToPtr(s.Error),
			Usage: &journal.Usage{
				PromptTokens:             s.PromptTokens,
				TotalTokens:              s.TotalTokens,
				CompletionTokens:         s.CompletionTokens,
				CacheCreationInputTokens: s.CacheCreationInputTokens,
				CacheReadInputTokens:     s.CacheReadInputTokens,
				ReasoningTokens:          s.ReasoningTokens,
			},
			Latency:           lo.ToPtr(s.TotalTimeCost),
			FirstTokenLatency: lo.ToPtr(s.FirstCountLatency),
			FinishReason:      lo.ToPtr(s.FinishReason),
		},
	}
}

func (s *ModelService) Embedding(ctx context.Context, req EmbeddingRequest) (*EmbeddingResult, error) {
	var (
		result *EmbeddingResult
		embErr error
	)

	modelConfig, err := s.getChatModelAuthConfig(ctx, req.AppID, req.Model)
	if err != nil {
		return nil, stderrors.Join(err, ErrModelNotFound)
	}

	realModelName, ok := modelConfig.ModelNameAlias[req.Model]
	if ok {
		log.V1.CtxInfo(ctx, "mapping alias model name: %s -> %s", req.Model, realModelName)
		req.Model = realModelName
	}

	log.V1.CtxInfo(ctx, "found model config for embedding: %s", modelConfig.Type)

	switch modelConfig.Type {
	case config.ModelTypeAzure, config.ModelTypeOpenAI:
		result, embErr = s.callAzureOpenAIEmbeddings(ctx, req, modelConfig)
	case config.ModelTypeMaaSV3:
		result, embErr = s.callMaaSV3Embeddings(ctx, req, modelConfig)
	default:
		return nil, errors.Errorf("unsupported model for embedding: %s", modelConfig.Type)
	}

	if embErr != nil {
		return nil, embErr
	}

	return result, nil
}
