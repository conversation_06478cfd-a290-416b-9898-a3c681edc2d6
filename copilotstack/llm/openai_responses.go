package llm

import (
	"context"
	"encoding/json"
	"io"
	"strings"
	"time"

	"github.com/openai/openai-go/v2"
	option "github.com/openai/openai-go/v2/option"
	"github.com/openai/openai-go/v2/packages/param"
	"github.com/openai/openai-go/v2/packages/ssestream"
	"github.com/openai/openai-go/v2/responses"
	"github.com/openai/openai-go/v2/shared"
	"github.com/openai/openai-go/v2/shared/constant"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	journalentity "code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
)

// implement chat completion by responses api.
func (s *ModelService) callOpenAIResponsesCompletion(
	ctx context.Context,
	req ChatCompletionRequest,
	modelConfig *config.ModelAuthConfig,
) (ChatCompletionStreamResult, error) {
	cli, err := s.getOpenAIResponsesClient(ctx, modelConfig, req.Model)
	if err != nil {
		return nil, err
	}

	reqOptions := make([]option.RequestOption, 0)

	if extraHeader := conv.DefaultAny[map[string]string](req.Variables["extra_http_header"]); len(extraHeader) > 0 {
		for k, v := range extraHeader {
			reqOptions = append(reqOptions, option.WithHeaderAdd(k, v))
		}
	}
	reqOptions = append(reqOptions, option.WithHeaderAdd("X-TT-LogID", ctxvalues.LogIDDefault(ctx)))

	streamer := &openAIResponsesStreamer{
		client: cli,
		req:    &req,
		stats: Stats{
			Status: journalentity.StatusSuccess,
		},
		start:          time.Now(),
		journalService: s.journalService,
		responseReqOpt: reqOptions,
	}

	responseReq := s.getOpenAIResponsesCreateRequest(ctx, req)
	// os.WriteFile(fmt.Sprintf("dev/local/tempdata/openai_llm_req_%s.json", time.Now().Format("20060102150405")), []byte(conv.JSONFormatString(req)), 0644)
	// os.WriteFile(fmt.Sprintf("dev/local/tempdata/openai_responses_req_%s.json", time.Now().Format("20060102150405")), []byte(conv.JSONFormatString(responseReq)), 0644)
	streamer.responseReq = responseReq

	if req.Stream {
		streamer.streamResponse = streamer.client.Responses.NewStreaming(ctx, responseReq, reqOptions...)
	}

	return streamer, nil
}

func (s *ModelService) getOpenAIResponsesClient(ctx context.Context, modelConfig *config.ModelAuthConfig, model string) (*openai.Client, error) {
	modelURL := modelConfig.Endpoint
	cacheKey := getModelCacheKeyByModelName(modelConfig, model)
	cli, ok := s.officialOpenaiCliPool.Get(cacheKey)
	if ok {
		return cli, nil
	}

	timeout := time.Minute * 5
	if modelConfig.Timeout > 0 {
		timeout = time.Second * time.Duration(modelConfig.Timeout)
	}

	cliOpts := []option.RequestOption{
		option.WithBaseURL(modelURL),
		// option.WithAPIKey(modelConfig.APIKey),
		option.WithHeaderAdd("API-KEY", modelConfig.APIKey),
		option.WithRequestTimeout(timeout),
		// option.WithDebugLog(),
	}

	newCli := openai.NewClient(cliOpts...)
	s.officialOpenaiCliPool.Add(cacheKey, &newCli)

	return &newCli, nil
}

var _ ChatCompletionStreamResult = &openAIResponsesStreamer{}

type openAIResponsesStreamer struct {
	client         *openai.Client
	req            *ChatCompletionRequest
	responseReq    responses.ResponseNewParams
	responseReqOpt []option.RequestOption
	streamResponse *ssestream.Stream[responses.ResponseStreamEventUnion]
	stats          Stats
	metadata       Metadata
	start          time.Time
	journalService journalentity.JournalService
	httpReqOption  hertz.ReqOption
}

// Aggregation implements stream.Streamer.
func (o *openAIResponsesStreamer) Aggregation(ctx context.Context) (*ChatCompletionResponse, error) {
	o.streamResponse = o.client.Responses.NewStreaming(ctx, o.responseReq, o.responseReqOpt...)

	if o.streamResponse != nil {
		return aggregateStream(ctx, o, nil)
	}

	return nil, errors.New("stream response is nil")

	// TODO(cyx): non-stream responses API response seems to miss many informations we need, such as encrypted reasoning content, so we implement non-stream by stream method temporarily.

	// resp, err := o.client.Responses.New(ctx, o.responseReq, o.responseReqOpt...)
	// if err != nil {
	// 	return nil, err
	// }

	// res := getOpenAPIResponsesResult(resp)

	// return res, nil
}

func (o *openAIResponsesStreamer) getResponseNextEvent() (*responses.ResponseStreamEventUnion, error) {
	if err := o.streamResponse.Err(); err != nil {
		o.stats.Error = err.Error()
		o.stats.Status = journalentity.StatusFail
		return nil, err
	}
	next := o.streamResponse.Next()
	o.stats.ChunkCount++
	if !next {
		o.stats.TotalTimeCost = time.Since(o.start)
		if err := o.streamResponse.Err(); err != nil {
			o.stats.Error = err.Error()
			o.stats.Status = journalentity.StatusFail
			return nil, err
		}
		return nil, io.EOF
	}

	if err := o.streamResponse.Err(); err != nil {
		// TODO(cyx): parse the error.
		o.stats.Error = err.Error()
		o.stats.Status = journalentity.StatusFail
		o.stats.TotalTimeCost = time.Since(o.start)
		return nil, err
	}

	cur := o.streamResponse.Current()
	if o.stats.ID == "" && cur.Response.ID != "" {
		o.stats.ID = cur.Response.ID
	}
	if o.stats.FirstCountLatency == 0 && lo.Contains([]string{"response.output_text.delta", "response.function_call_arguments.delta"}, cur.Type) {
		o.stats.FirstCountLatency = time.Since(o.start)
	}
	return &cur, nil
}

// getNextResponseEvent gets the next important event from the stream response.
// The events in following will be returned, others will be ignored.
func (o *openAIResponsesStreamer) getResponseNextImportantEvent() (*responses.ResponseStreamEventUnion, error) {
	for {
		next, err := o.getResponseNextEvent()
		if err != nil {
			return nil, err
		}

		// Ignore the events that we do not use.
		if !lo.Contains([]string{
			"response.output_text.delta",             // output text delta.
			"response.function_call_arguments.delta", // function call arguments delta.
			"response.output_item.added",             // function call name.
			"response.completed",                     // token usage.
			"response.output_item.done",              // reasoning content.
			"response.incomplete",                    // finish reason
		}, next.Type) {
			continue
		}

		if next.Type == "response.output_item.added" && !lo.Contains([]string{"reasoning", "function_call"}, next.Item.Type) {
			continue
		}

		return next, nil
	}
}

// NextChunk implements stream.Streamer.
func (o *openAIResponsesStreamer) NextChunk(ctx context.Context) (*ChatCompletionStreamResponse, error) {
	next, err := o.getResponseNextImportantEvent()
	if err != nil {
		return nil, err
	}

	result := getOpenAIResponsesStreamResult(o.req.Model, next)

	if len(result.Choices) > 0 {
		item := result.Choices[0]
		o.stats.ContentRaw += item.Delta.Content
		if len(item.Delta.ReasoningContent) > 0 {
			o.stats.ReasoningContent += item.Delta.ReasoningContent
		}
		if len(item.FinishReason) > 0 {
			o.stats.FinishReason = string(item.FinishReason)
		}
		if len(item.Delta.ToolCalls) > 0 {
			for _, toolCall := range item.Delta.ToolCalls {
				_, idx, ok := lo.FindIndexOf(o.stats.ToolCalls, func(item ToolCall) bool {
					if toolCall.Index != nil && item.Index != nil {
						return *item.Index == *toolCall.Index
					}
					return item.ID == toolCall.ID
				})
				if ok {
					o.stats.ToolCalls[idx].Function.Arguments += toolCall.Function.Arguments
				} else {
					o.stats.ToolCalls = append(o.stats.ToolCalls, toolCall)
				}
			}
		}
	}

	if result.Usage != nil {
		o.stats.PromptTokens = result.Usage.PromptTokens
		o.stats.CompletionTokens = result.Usage.CompletionTokens
		o.stats.TotalTokens = result.Usage.TotalTokens
		o.stats.CacheCreationInputTokens = result.Usage.CacheCreationInputTokens
		o.stats.CacheReadInputTokens = result.Usage.CacheReadInputTokens
		o.stats.ReasoningTokens = result.Usage.ReasoningTokens
	}

	return result, nil
}

// Close implements stream.Streamer.
func (o *openAIResponsesStreamer) Close(ctx context.Context) error {
	if o.streamResponse != nil {
		return o.streamResponse.Close()
	}
	return nil
}

// Metadata implements stream.Streamer.
func (o *openAIResponsesStreamer) Metadata(ctx context.Context) Metadata {
	return o.metadata
}

// Statistics implements stream.Streamer.
func (o *openAIResponsesStreamer) Statistics(ctx context.Context) Stats {
	return o.stats
}

func getOpenAIResponseTool(tool Tool, _ int) responses.ToolUnionParam {
	return responses.ToolUnionParam{
		OfFunction: &responses.FunctionToolParam{
			Strict:      param.NewOpt(false),
			Parameters:  conv.DefaultAny[map[string]any](tool.Function.Parameters),
			Name:        tool.Function.Name,
			Description: param.NewOpt(tool.Function.Description),
			Type:        constant.Function("function"),
		},
	}
}

func (s *ModelService) getOpenAIResponsesCreateRequest(ctx context.Context, req ChatCompletionRequest) responses.ResponseNewParams {
	params := responses.ResponseNewParams{
		Background:         param.NewOpt(false),
		Instructions:       param.Opt[string]{},
		MaxOutputTokens:    param.NewOpt(int64(lo.FromPtr(req.MaxTokens))),
		MaxToolCalls:       param.NewOpt(int64(0)),
		ParallelToolCalls:  param.NewOpt(lo.FromPtr(req.ParallelToolCalls)),
		PreviousResponseID: param.Opt[string]{},
		Store:              param.NewOpt(false),
		Temperature:        param.NewOpt(float64(lo.FromPtr(req.Temperature))),
		// TopLogprobs:        param.NewOpt(int64(0)),
		// TopP:               param.NewOpt(float64(lo.FromPtr(req.TopP))),
		PromptCacheKey: param.Opt[string]{},
		// SafetyIdentifier:   param.NewOpt(""),
		// User:               param.NewOpt(""),
		Conversation: responses.ResponseNewParamsConversationUnion{},
		Include: []responses.ResponseIncludable{
			responses.ResponseIncludableReasoningEncryptedContent,
		},
		Metadata: shared.Metadata{},
		// Prompt:      responses.ResponsePromptParam{},
		// ServiceTier: "",
		StreamOptions: responses.ResponseNewParamsStreamOptions{
			IncludeObfuscation: param.NewOpt(false),
		},
		// Truncation: "",
		Input: responses.ResponseNewParamsInputUnion{
			OfInputItemList: responses.ResponseInputParam{},
		},
		Model:      req.Model,
		Reasoning:  shared.ReasoningParam{},
		Text:       responses.ResponseTextConfigParam{},
		ToolChoice: responses.ResponseNewParamsToolChoiceUnion{},
		Tools:      lo.Ternary(req.Tools != nil, lo.Map(req.Tools, getOpenAIResponseTool), nil),
	}

	for idx, msg := range req.Messages {
		switch req.Messages[idx].Role {
		case "system", "developer":
			if len(msg.Content) > 0 {
				params.Instructions = param.NewOpt(req.Messages[idx].Content)
			} else if len(msg.MultiContent) > 0 {
				// 理论上不会出现 system 里面有 multi content 的情况
				var instructions []string
				for _, part := range msg.MultiContent {
					if part.Type == "text" {
						instructions = append(instructions, part.Text)
					}
				}
				params.Instructions = param.NewOpt(strings.Join(instructions, "\n"))
			}
		case "assistant":
			if len(msg.ReasoningContent) > 0 {
				structrualReasoning := openAIResponsesReasoningRecord{}
				if err := json.Unmarshal([]byte(msg.ReasoningContent), &structrualReasoning); err != nil {
					log.V1.CtxWarn(ctx, "failed to unmarshal reasoning content", "error", err)
				} else {
					params.Input.OfInputItemList = append(params.Input.OfInputItemList, responses.ResponseInputItemUnionParam{
						OfReasoning: &responses.ResponseReasoningItemParam{
							ID: structrualReasoning.ID,
							Summary: lo.Map(structrualReasoning.Summary, func(item openAIResponsesReasoningSummaryRecord, _ int) responses.ResponseReasoningItemSummaryParam {
								return responses.ResponseReasoningItemSummaryParam{
									Text: item.Text,
									Type: constant.SummaryText(item.Type),
								}
							}),
							EncryptedContent: param.NewOpt(structrualReasoning.EncryptedContent),
							Content:          []responses.ResponseReasoningItemContentParam{},
							Status:           "",
							Type:             "reasoning",
						},
					})
				}
			}
			if len(msg.Content) > 0 {
				params.Input.OfInputItemList = append(params.Input.OfInputItemList, responses.ResponseInputItemUnionParam{
					OfInputMessage: &responses.ResponseInputItemMessageParam{
						Content: responses.ResponseInputMessageContentListParam{
							{
								OfInputText: &responses.ResponseInputTextParam{
									Text: msg.Content,
									Type: "output_text",
								},
							},
						},
						Role:   "assistant",
						Status: "",
						Type:   "message",
					},
				})
			}
			if len(msg.MultiContent) > 0 {
				for _, part := range msg.MultiContent {
					switch part.Type {
					case "text":
						params.Input.OfInputItemList = append(params.Input.OfInputItemList, responses.ResponseInputItemUnionParam{
							OfInputMessage: &responses.ResponseInputItemMessageParam{
								Content: responses.ResponseInputMessageContentListParam{
									{
										OfInputText: &responses.ResponseInputTextParam{
											Text: part.Text,
											Type: "output_text",
										},
									},
								},
								Role:   "assistant",
								Status: "",
								Type:   "message",
							},
						})
					}
				}
			}
			if len(msg.ToolCalls) > 0 {
				for _, toolCall := range msg.ToolCalls {
					params.Input.OfInputItemList = append(params.Input.OfInputItemList, responses.ResponseInputItemUnionParam{
						OfFunctionCall: &responses.ResponseFunctionToolCallParam{
							Arguments: toolCall.Function.Arguments,
							CallID:    toolCall.ID,
							Name:      toolCall.Function.Name,
							ID:        param.Opt[string]{},
							Status:    "",
							Type:      "function_call",
						},
					})
				}
			}
		case "tool":
			if len(msg.Content) > 0 {
				params.Input.OfInputItemList = append(params.Input.OfInputItemList, responses.ResponseInputItemUnionParam{
					OfFunctionCallOutput: &responses.ResponseInputItemFunctionCallOutputParam{
						CallID: req.Messages[idx].ToolCallID,
						Output: req.Messages[idx].Content,
						ID:     param.Opt[string]{},
						Status: "",
						Type:   "function_call_output",
					},
				})
			} else if len(msg.MultiContent) > 0 {
				var contents []string
				for _, part := range msg.MultiContent {
					switch part.Type {
					case "text":
						contents = append(contents, part.Text)
					}
				}
				params.Input.OfInputItemList = append(params.Input.OfInputItemList, responses.ResponseInputItemUnionParam{
					OfFunctionCallOutput: &responses.ResponseInputItemFunctionCallOutputParam{
						CallID: req.Messages[idx].ToolCallID,
						Output: strings.Join(contents, "\n"),
						ID:     param.Opt[string]{},
						Status: "",
						Type:   "function_call_output",
					},
				})
			}

		case "user":
			contentParts := responses.ResponseInputMessageContentListParam{}
			if len(msg.MultiContent) > 0 {
				for _, part := range msg.MultiContent {
					switch part.Type {
					case "text":
						contentParts = append(contentParts, responses.ResponseInputContentUnionParam{
							OfInputText: &responses.ResponseInputTextParam{
								Text: part.Text,
								Type: "input_text",
							},
						})
					case "image":
						contentParts = append(contentParts, responses.ResponseInputContentUnionParam{
							OfInputImage: &responses.ResponseInputImageParam{
								Detail:   "auto",
								FileID:   param.NewOpt(""),
								ImageURL: param.NewOpt(part.ImageURL.URL),
								Type:     "input_image",
							},
						})
					}
				}
			} else {
				contentParts = append(contentParts, responses.ResponseInputContentUnionParam{
					OfInputText: &responses.ResponseInputTextParam{
						Text: msg.Content,
						Type: "input_text",
					},
				})
			}
			params.Input.OfInputItemList = append(params.Input.OfInputItemList, responses.ResponseInputItemUnionParam{
				OfInputMessage: &responses.ResponseInputItemMessageParam{
					Content: contentParts,
					Role:    "user",
					Status:  "",
					Type:    "message",
				},
			})
		default:
			log.V1.CtxWarn(ctx, "unknown message role", "role", req.Messages[idx].Role)
		}
	}

	if req.Thinking != nil {
		if req.Thinking.Type != "" {
			params.Reasoning.Effort = responses.ReasoningEffort(responses.ReasoningEffort(req.Thinking.Type))
		}
		if req.Thinking.IncludeThoughts {
			params.Reasoning.Summary = responses.ReasoningSummaryAuto
		}
	}

	if req.PromptCacheKey != nil && len(*req.PromptCacheKey) > 0 {
		params.PromptCacheKey = param.NewOpt(*req.PromptCacheKey)
	}

	if req.TextOutputOption != nil {
		if req.TextOutputOption.Verbosity != nil {
			params.Text.Verbosity = responses.ResponseTextConfigVerbosity(*req.TextOutputOption.Verbosity)
		}
	}

	if req.ToolChoice != nil {
		switch t := req.ToolChoice.(type) {
		case string:
			params.ToolChoice = responses.ResponseNewParamsToolChoiceUnion{
				OfToolChoiceMode: param.NewOpt(responses.ToolChoiceOptions(t)),
			}
		case Tool:
			params.ToolChoice = responses.ResponseNewParamsToolChoiceUnion{
				OfFunctionTool: &responses.ToolChoiceFunctionParam{
					Name: t.Function.Name,
					Type: constant.Function(t.Type),
				},
			}
		default:
			log.V1.CtxWarn(ctx, "unknown tool choice type", "tool_choice", req.ToolChoice)
		}
	}

	return params
}

func getOpenAPIResponsesResult(res *responses.Response) *ChatCompletionResponse {
	return nil
}

type openAIResponsesReasoningRecord struct {
	ID               string                                  `json:"id"`
	EncryptedContent string                                  `json:"encrypted_content"`
	Summary          []openAIResponsesReasoningSummaryRecord `json:"summary"`
}

type openAIResponsesReasoningSummaryRecord struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

func getOpenAIResponsesStreamResult(model string, res *responses.ResponseStreamEventUnion) *ChatCompletionStreamResponse {
	result := &ChatCompletionStreamResponse{
		ID:      "",
		Object:  res.Type,
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []ChatCompletionStreamChoice{},
		Usage:   &TokenUsage{},
	}
	switch res.Type {
	case "response.output_item.added":
		switch res.Item.Type {
		case "function_call":
			result.ID = res.Item.ID
			result.Choices = append(result.Choices, ChatCompletionStreamChoice{
				Index: 0,
				Delta: ChatCompletionStreamChoiceDelta{
					ToolCalls: []ToolCall{
						{
							Index: lo.ToPtr(int(res.OutputIndex)),
							ID:    res.Item.CallID,
							Type:  res.Item.Type,
							Function: FunctionCall{
								Name:      res.Item.Name,
								Arguments: res.Item.Arguments,
							},
						},
					},
				},
			})
		}
	case "response.output_item.done":
		switch res.Item.Type {
		case "reasoning":
			result.ID = res.Item.ID
			result.Choices = append(result.Choices, ChatCompletionStreamChoice{
				Index: 0,
				Delta: ChatCompletionStreamChoiceDelta{
					ReasoningContent: conv.JSONString(map[string]any{
						"id":                res.Item.ID,
						"encrypted_content": res.Item.EncryptedContent,
						"summary":           res.Item.Summary,
					}),
				},
			})
		}
	case "response.output_text.delta":
		result.ID = res.ItemID
		result.Choices = append(result.Choices, ChatCompletionStreamChoice{
			Index: int(res.ContentIndex),
			Delta: ChatCompletionStreamChoiceDelta{
				Content:          res.Delta,
				ReasoningContent: "",
				Role:             "assistant",
				FunctionCall:     nil,
				ToolCalls:        nil,
			},
		})
	case "response.function_call_arguments.delta":
		result.ID = res.ItemID
		result.Choices = append(result.Choices, ChatCompletionStreamChoice{
			Index: 0,
			Delta: ChatCompletionStreamChoiceDelta{
				ToolCalls: []ToolCall{
					{
						Index: lo.ToPtr(int(res.OutputIndex)),
						// ID:   res.ItemID,
						Type: "",
						Function: FunctionCall{
							Arguments: res.Delta,
						},
					},
				},
			},
		})
	case "response.incomplete":
		result.ID = res.Response.ID
		result.Choices = []ChatCompletionStreamChoice{
			{
				Index:        0,
				FinishReason: res.Response.IncompleteDetails.Reason,
				Delta: ChatCompletionStreamChoiceDelta{
					Content:          "",
					ReasoningContent: "",
					Role:             "",
					FunctionCall:     nil,
					ToolCalls:        nil,
				},
			},
		}
		result.Usage = &TokenUsage{
			PromptTokens:             int(res.Response.Usage.InputTokens),
			CompletionTokens:         int(res.Response.Usage.OutputTokens),
			ReasoningTokens:          int(res.Response.Usage.OutputTokensDetails.ReasoningTokens),
			TotalTokens:              int(res.Response.Usage.TotalTokens),
			CacheCreationInputTokens: 0,
			CacheReadInputTokens:     int(res.Response.Usage.InputTokensDetails.CachedTokens),
		}
	case "response.completed":
		result.ID = res.Response.ID
		result.Choices = []ChatCompletionStreamChoice{
			{
				Index:        0,
				FinishReason: FinishReasonStop,
				Delta: ChatCompletionStreamChoiceDelta{
					Content:          "",
					ReasoningContent: "",
					Role:             "",
					FunctionCall:     nil,
					ToolCalls:        nil,
				},
			},
		}
		result.Usage = &TokenUsage{
			PromptTokens:             int(res.Response.Usage.InputTokens),
			CompletionTokens:         int(res.Response.Usage.OutputTokens),
			ReasoningTokens:          int(res.Response.Usage.OutputTokensDetails.ReasoningTokens),
			TotalTokens:              int(res.Response.Usage.TotalTokens),
			CacheCreationInputTokens: 0,
			CacheReadInputTokens:     int(res.Response.Usage.InputTokensDetails.CachedTokens),
		}
	}
	return result
}
