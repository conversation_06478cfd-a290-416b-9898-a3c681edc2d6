package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/codebase/sdk/v2/types/bytedance"
	"code.byted.org/devgpt/kiwis/port/nextcode"
	"code.byted.org/gopkg/logs/v2/log"
	bcjwt "code.byted.org/paas/cloud-sdk-go/jwt"
	"code.byted.org/security/zti-jwt-golang/token"
	"github.com/golang-jwt/jwt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/ideaccount"
)

var _ Service = &AuthService{}

type AuthOrigin string

const (
	AuthOriginByteCloud  AuthOrigin = "byte_cloud"
	AuthOriginCodebase   AuthOrigin = "codebase"
	AuthOriginBizIDE     AuthOrigin = "biz_ide"
	AuthOriginGDPRToken  AuthOrigin = "gdpr"
	AuthOriginCloudIDE   AuthOrigin = "cloud_ide"
	BitsAuthOriginBitsAI AuthOrigin = "bits_ai"
	AuthOriginNextCode   AuthOrigin = "next_code"
)

const (
	AuthOriginTokenNameByteCloud = "Byte-Cloud-JWT"
	AuthOriginTokenNameCodebase  = "Codebase-User-JWT"
	AuthOriginTokenNameBizIDE    = "Biz-IDE-Service-JWT"
	AuthOriginTokenNameGDPRToken = "GDPR-JWT"
	AuthOriginTokenNameCloudIDE  = "Cloud-IDE-JWT"
	AuthOriginTokenNameBitsAI    = "Bits-AI-JWT"
)

const (
	// 单位为毫秒
	// TODO: 目前最长仅支持1天，看后续是否能支持更长Token过期时间
	defaultNextCodeTokenExpiration = int64(14 * 24 * time.Hour / time.Millisecond)
)

type Service interface {
	// AuthHeader authenticates user identity from http header.
	AuthHeader(ctx context.Context, headers HeaderGetter, allowedOrigins []AuthOrigin) (*entity.Account, error)
	AuthServiceAccountJWT(ctx context.Context, jwtStr string) (*entity.ServiceAccount, error)
	RefreshToken(ctx context.Context, origin AuthOrigin, token string) (string, error)
	AuthByteCloudJWT(ctx context.Context, tokenValue string) (account *entity.Account, err error)
}

type HeaderGetter interface {
	Get(key string) string
}

type CloudIDEPubConfig struct {
	PubKeys map[string]string
}

type AuthService struct {
	authTCCConfig               *libtcc.GenericConfig[config.AuthTCCConfig]
	authConfig                  config.AuthConfig
	byteCloudValidator          bcjwt.Validator
	codebaseCli                 codebase.Client
	nextcodeCli                 nextcode.Client
	cloudIDEPubs                *CloudIDEPubConfig
	ideAccountClient            *ideaccount.Client
	usernameOriginConfig        *libtcc.GenericConfig[config.UsernameOriginConfig]
	codebaseBitsAIAccountSecret *libtcc.GenericConfig[config.CodebaseBitsAIJWTConfig]
}

type CreateAuthServiceOption struct {
	fx.In
	AuthTCCConfig     *libtcc.GenericConfig[config.AuthTCCConfig] `optional:"true"`
	CodebaseCli       codebase.Client                             `optional:"true"`
	NextCodeCli       nextcode.Client                             `optional:"true"`
	CloudIDEPubConfig *CloudIDEPubConfig                          `optional:"true"`
	IDEAccountClient  *ideaccount.Client                          `optional:"true"`

	// UsernameOriginConfig retrieves pre-defined username groups to help decide user's locale
	UsernameOriginConfig        *libtcc.GenericConfig[config.UsernameOriginConfig]    `optional:"true"`
	CodebaseBitsAIAccountSecret *libtcc.GenericConfig[config.CodebaseBitsAIJWTConfig] `optional:"true"`

	AuthConfig config.AuthConfig
}

const (
	cloudIDEBOEPubKey    = "-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAodcWgf3A/vVlapRruF0N\ngq0pX5Oo+OUK6IVNvooP7p9D8n3WwFVrCl/ho09jtxNMTI7YniRK2m865C0+xW92\nNoYBtX4qsF+vd0LFMEaYgyhFCx4dCzqc6SD7YVbtwuvUzcJwRVvCw3KzRvJN/BBo\naB2XCTaGhIgBtMKskkd8nZFdbz2+1Rg2t0QXexbGZOpFqRJYXBY7scTK30UsB5WO\nhtXTr36dl9ssE8ZedoDTfPIZYVeejFvEFiDcbc26UNLVkIPOyryJQhw/mhrliZDX\n6n5rK6goFsAs5YlOuWmoRClKeC7l4BacsvKYLx5vW4qymlWDgVNlEr+wy3hntQLN\n38hGnITQBA4ORNBQo9PuDxtbJjk9cfPk8noPCoRtlQ1Bp9HbKqmiibYDr9CzCsDi\n+BDJEHFVnkMhoD/+O80hAxd1oHb0jSKN2wWovXvQZq/Q5gSjYuuq+CUKaGt27Nc6\nEwTE3wEl/w8Sh0cwXbK0iUCOJUh9kT9ph0RVmIG3vD7k3ORmlRY0oRMsnShEAQOo\npAb2HVBzeI9JrMmiqVHsEDzYpSF0HALA/AT925addSpRZMhOoUj++5dc9tFaX4lJ\nN4T7ktUdj9+au7j8nDoE07M8+ykPAUhVlodq6f9bzRT0XKvKODtmaIJk9gODvQHY\nzcz4UnGplVvnz5R4/VciBQ8CAwEAAQ==\n-----END PUBLIC KEY-----\n"
	cloudIDEOnlinePubKey = "-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAu1MXxam1y7Wc+PN542lr\nFWanzhPMVQ5N0jzhLcBZnLf/APXFgRQDthSEkzj36UaHll4Z7cg+rceA77isSNhh\nI/AiqEXRoK/DH5fSY+TNPIydxIUU6VFkGBkKIJNE+lToFS5GLendCQNucNjSsvIx\nuKJbNhhekBoM2ehNSaqzhgOsYWLhx/hRN0exW1S8z9OyW5moaGCcseSzzjYQJ8p1\nC1HbClOvz72/qIz3As04TzCdzIMaCisoId4W+y78E2nd1EF4vhD88CtQRkmLam14\nR/HQFiklYJ+zShAfBtbiMfpPqoglfXBfWflF1Ukp1BxosD31M1pK4XsZuDas2M8v\n/Uk5Z29EJEg+TDyAb2uqElYla0Nwss8v6WtPjb4tPlsPuXN+EcDV7CDz94NFaxmP\nJ1OYMK4U3ChRB5QK962mMDSBv7nPPjE6p08LO1wqPN0nJ7REIo+ysxn1VSJjEyvq\nQb1LZcr7HPSDixNaUgMXbdsuDE96Mmgc9xUgXvfPkNj4tIK9ddEauGIKg0NMpzcc\nyrO/LbxAb/foMUOyNyQ3s3reMUeLXZPoLvpf5EpWntKe9KtgMP3ohp66W/4ADLFH\nz2Xb58HmLCOUEfICs4P3+QD+X3e7ub4zgQwD6iwtRahdm2NMBk3aWbCU6eXWZ3Fx\nMtXLG+5TE2IQIIrHL1RfhM0CAwEAAQ==\n-----END PUBLIC KEY-----\n"

	cozeIDEBOEPubKey = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApAutrrID+sNkO/td9G+r\nq4YXFb0eBoXBU3xrhorHuRBgFkZ11/v6btNU08tnmniSAkejnt9LXzKsp3VTRNNt\nY566Y2d37F16XNATzfWd1ibRevF8U4z4fRpdIz4l/1oSMwXNpFW2fV6WMmouuRW5\nJ6KzgNVtUxT2xh6VnexaCEyeibKUfUT2C5asTTFogDM6IpGpn2ynb9xteQ9+1R9Z\n5eW+m8fCZ0QI/347V30jYZ/AcQ8B+iGGy2J44ItXdsdTsjw8AYd6Xzxc8nNFWCIX\nO/2uRCvhblFRye+kRkIYj1hU6xzrMcVDdQ7R52B/Fvp2JDjhTGNXbZ/su0H7j+7I\nuwIDAQAB\n-----END PUBLIC KEY-----\n"
	cozeIDECNPubKey  = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnRg+IR7SEq7XAdtdHcKr\nTY8/M5+TS/eLj2F5GgERnHE1p1EJ9iPi38e29s+BL9nbsHF397azAc5hNup5zoTY\npEB0dFw+UwN4AOq3OAbWYchqfiFp6feLm2EcoxA0KSX8Qvx9kdgwLupby0Kg6drP\n2pDT9nfw9SRj/2vyEbgUQ5rysGn5GkwgfPizabrFX2BedZiu7pvgxDqoecmg8aKE\nS01DKAktb2TPqeGRFsjG+dmn4406YBTWgxMEIbw1mMjNafZr3rJ/A3OwobqapqnZ\n7ZOplWbezaYY1QoLnPTQZFv+u9Zt7kvy24NvowBCvatko5Gw+9nbsSmyV7JVnRj7\ntwIDAQAB\n-----END PUBLIC KEY-----"
	cozeIDESGPubKey  = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt60mZ0Gwg1pTuiQPEAXk\nlH58g0HHANhw794bjUkISCiPaBmqKAIZYHRVkNXCdlOeePUKHOEXcgBnNB3/Lkka\n5N7w+4JCdmfKCKpUxCgeYXYD3ZDaCK404zpMoNKcdAVmFyVRqkQXHTz3YKneA5rh\nbsO3r8WY2vk4I/d3s2V4xudrvrRagF2wGCBdtzk4clqYL+6OBNQ+Ofj0bcFola6B\nntbplxLzv/SZqzxcQT1mzrfYgAC1h3w4jjlW05CfY46fwRmIwZzxlXVt302xm1m8\nq3lUGwjl0jyuXqvXsyM7xJvXlNNzw3GjvttZHB7BoE7J4/HoAQQ73kRRAxVOu+qL\ntQIDAQAB\n-----END PUBLIC KEY-----"
	cozeIDEUSPubKey  = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA28HYQ4M36/apB51YsM8t\n/UpuXqTCzNY/leHrVyW9ZLh8bQnlYtEof6uNX/Y42+uOUHPcD3ido5x0UHCgqhE+\ndb711ueLsGyaZkXhpRsfDZNjGVLshpVJRAF8+c0Fbo4k9YzqOmk2NI9aPpzUAaY1\nXvvuArs7sRzNOJmNSLWrIu4B47D2Nqsa+MAjB3+X5fsK3DI6KPNK6i9lbKgRmSiy\ndy1Dpqp4RybifWgumOXJhE1VZ26v1Xvco+SK7jKF836ncuKsmsf1yUb7MOXRNen/\ndFytO2ujQzvkqGx8tRpqEtEyytznHcYWTofQny5yjkXV2agzMqAElSMc4Yav8EiF\nCQIDAQAB\n-----END PUBLIC KEY-----"
)

var defaultCloudIDEPubKey = map[string]string{
	// AI IDE:
	// Product:
	//  Bytedance:
	"n61w51v7680l16": cloudIDEOnlinePubKey,
	//  ToC:
	"1dknw951n5p5vn": cloudIDEOnlinePubKey,
	"7o2d894p7dr0o4": cloudIDEOnlinePubKey,
	// BOE.
	//  Bytedance:
	"5p5lw4yp38q8n4": cloudIDEBOEPubKey,
	//  ToC:
	"82g9meypogge2g": cloudIDEBOEPubKey,

	// Coze IDE:
	// BOE:
	"e4a28a0e7bdef77d3598f85201c270f1": cozeIDEBOEPubKey,
	// CN:
	"8dc18c67f26e94714cfc2034063aac46": cozeIDECNPubKey,
	// SG:
	"c17cc4720a8095be4589b02b351cdae9": cozeIDESGPubKey,
	// US:
	"bd182043fbe6dee12b24773e7e785516": cozeIDEUSPubKey,
}

func NewAuthService(opt CreateAuthServiceOption) *AuthService {
	// For testing.
	if opt.CloudIDEPubConfig == nil {
		opt.CloudIDEPubConfig = &CloudIDEPubConfig{PubKeys: defaultCloudIDEPubKey}
	}
	return &AuthService{
		authTCCConfig:               opt.AuthTCCConfig,
		authConfig:                  opt.AuthConfig,
		byteCloudValidator:          bcjwt.NewValidator([]string{bcjwt.RegionCN, bcjwt.RegionI18N, bcjwt.RegionTX}),
		codebaseCli:                 opt.CodebaseCli,
		nextcodeCli:                 opt.NextCodeCli,
		cloudIDEPubs:                opt.CloudIDEPubConfig,
		ideAccountClient:            opt.IDEAccountClient,
		usernameOriginConfig:        opt.UsernameOriginConfig,
		codebaseBitsAIAccountSecret: opt.CodebaseBitsAIAccountSecret,
	}
}

func (s *AuthService) getPublicKey(token *jwt.Token) (any, error) {
	privateKey, err := jwt.ParseRSAPrivateKeyFromPEM([]byte(s.authTCCConfig.GetValue().JWTPrivateKey))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse private key")
	}
	return privateKey.Public(), nil
}

type copilotClaims struct {
	jwt.StandardClaims
	entity.ServiceAccount
}

func (s *AuthService) AuthServiceAccountJWT(ctx context.Context, jwtStr string) (*entity.ServiceAccount, error) {
	if s.authTCCConfig == nil {
		return nil, errors.New("service account jwt is not provided")
	}
	token, err := jwt.ParseWithClaims(jwtStr, new(copilotClaims), s.getPublicKey)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse jwt")
	}

	if claims, ok := token.Claims.(*copilotClaims); ok && token.Valid && len(claims.ServiceAccount.Name) > 0 {
		return &claims.ServiceAccount, nil
	}
	return nil, errors.New("expired jwt or invalid identity")
}

func (s *AuthService) RefreshToken(ctx context.Context, origin AuthOrigin, token string) (string, error) {
	switch origin {
	case AuthOriginCodebase:
		codebaseJWT, err := s.codebaseCli.SignCodebaseJWTByCodebaseJWT(ctx, token, s.authConfig.SignCodebaseJWTScopes)
		if err != nil {
			return "", errors.Wrap(err, "failed to sign codebase jwt from codebase")
		} else {
			return codebaseJWT, nil
		}
	case AuthOriginNextCode:
		nextCodeJWT, err := s.nextcodeCli.SignUserJWT(ctx, bytedance.SignUserJWTRequest{
			Scopes:       s.authConfig.SignNextCodeJWTScopes,
			CloudUserJWT: &token,
			Expiration:   lo.ToPtr(defaultNextCodeTokenExpiration),
		})
		if err != nil {
			// 没有登陆过 codebase，无法签发相关 token
			if strings.Contains(err.Error(), "user not found") {
				return "", nil
			}
			return "", errors.Wrap(err, "failed to sign nextcode jwt from nextcode")
		} else {
			return nextCodeJWT.GetJWT(), nil
		}
	default:
		return "", errors.New(fmt.Sprintf("unsupport this origin: %s", origin))
	}
}

func (s *AuthService) predictUserLocale(ctx context.Context, username, workCountry string) string {
	var (
		zhLang              = "zh"
		zhWorkCountrySimply = "CHN"
		zhWorkCountry       = "Chinese"
	)
	if (workCountry == zhWorkCountrySimply || strings.Contains(workCountry, zhWorkCountry)) && s.usernameOriginConfig != nil {
		zhNames := s.usernameOriginConfig.GetValue().ZhSurnames
		if isZhSurname := lo.ContainsBy(zhNames, func(item string) bool {
			return strings.Contains(username, item)
		}); isZhSurname {
			log.V1.CtxInfo(ctx, fmt.Sprintf("predict username %s contains zh surname", username))
			return zhLang
		}
	}
	// log.V1.CtxInfo(ctx, fmt.Sprintf("stop predict username %s locale", username))
	return ""
}

func (s *AuthService) authByteCloudJWT(ctx context.Context, tokenValue string) (account *entity.Account, err error) {
	payload, err := s.byteCloudValidator.Validate(ctx, tokenValue)
	if err != nil {
		return nil, errors.WithMessage(err, "invalid byte cloud jwt")
	}
	locale := s.predictUserLocale(ctx, payload.Username, payload.WorkCountry)
	account = &entity.Account{
		ID:              payload.EmployeeID,
		Username:        payload.Username,
		Name:            payload.Username,
		Type:            string(payload.Type),
		Department:      payload.Organization,
		Locale:          locale,
		Email:           payload.Email,
		Sequence:        payload.Sequence,
		Description:     "",
		CreatedAt:       "",
		AuthOrigin:      string(AuthOriginByteCloud),
		CodebaseUserJWT: "",
		WorkCountry:     payload.WorkCountry,
		CloudUserJWT:    tokenValue,
	}
	// Only signs codebase jwt if it is person account.
	if s.authConfig.EnableSignCodebaseJWT && payload.Type == "person_account" {
		codebaseJWT, err := s.codebaseCli.SignCodebaseJWTByByteCloudJWT(ctx, tokenValue, s.authConfig.SignCodebaseJWTScopes)
		if err != nil {
			log.V1.CtxInfo(ctx, "failed to sign codebase jwt from byte cloud jwt: %v", err)
		} else {
			account.CodebaseUserJWT = codebaseJWT
		}
		if s.nextcodeCli != nil {
			// for next code
			nextCodeJWTResp, err := s.nextcodeCli.SignUserJWT(ctx, bytedance.SignUserJWTRequest{
				Scopes:       s.authConfig.SignNextCodeJWTScopes,
				CloudUserJWT: &tokenValue,
				Expiration:   lo.ToPtr(defaultNextCodeTokenExpiration),
			})
			if err != nil {
				log.V1.CtxInfo(ctx, "failed to sign next code jwt from byte cloud jwt: %v", err)
			} else {
				account.NextCodeUserJWT = nextCodeJWTResp.GetJWT()
			}
		}
	}
	return account, nil
}

func (s *AuthService) AuthByteCloudJWT(ctx context.Context, tokenValue string) (account *entity.Account, err error) {
	payload, err := s.byteCloudValidator.Validate(ctx, tokenValue)
	if err != nil {
		return nil, errors.WithMessage(err, "invalid byte cloud jwt")
	}
	locale := s.predictUserLocale(ctx, payload.Username, payload.WorkCountry)
	account = &entity.Account{
		ID:              payload.EmployeeID,
		Username:        payload.Username,
		Name:            payload.Username,
		Type:            string(payload.Type),
		Department:      payload.Organization,
		Locale:          locale,
		Email:           payload.Email,
		Sequence:        payload.Sequence,
		Description:     "",
		CreatedAt:       "",
		AuthOrigin:      string(AuthOriginByteCloud),
		CodebaseUserJWT: "",
		WorkCountry:     payload.WorkCountry,
		CloudUserJWT:    tokenValue,
	}
	// Only signs codebase jwt if it is person account.
	if s.authConfig.EnableSignCodebaseJWT && payload.Type == "person_account" {
		codebaseJWT, err := s.codebaseCli.SignCodebaseJWTByByteCloudJWT(ctx, tokenValue, s.authConfig.SignCodebaseJWTScopes)
		if err != nil {
			log.V1.CtxInfo(ctx, "failed to sign codebase jwt from byte cloud jwt: %v", err)
		} else {
			account.CodebaseUserJWT = codebaseJWT
		}
		if s.nextcodeCli != nil {
			// for next code
			nextCodeJWTResp, err := s.nextcodeCli.SignUserJWT(ctx, bytedance.SignUserJWTRequest{
				Scopes:       s.authConfig.SignNextCodeJWTScopes,
				CloudUserJWT: &tokenValue,
				Expiration:   lo.ToPtr(defaultNextCodeTokenExpiration),
			})
			if err != nil {
				log.V1.CtxInfo(ctx, "failed to sign next code jwt from byte cloud jwt: %v", err)
			} else {
				account.NextCodeUserJWT = nextCodeJWTResp.GetJWT()
			}
		}
	}
	return account, nil
}

func (s *AuthService) authCodebaseJWT(ctx context.Context, tokenValue string) (account *entity.Account, err error) {
	if s.codebaseCli == nil {
		return nil, errors.New("codebase authentication is not available")
	}

	payload, err := s.codebaseCli.GetUserInfo(ctx, s.authConfig.IncludeCodebaseUserDetails, codebase.WithUserJWT(tokenValue))
	if err != nil {
		return nil, errors.WithMessage(err, "invalid codebase jwt")
	}
	account = &entity.Account{
		ID:              lo.FromPtr(payload.ID),
		Username:        lo.FromPtr(payload.Username),
		Name:            lo.FromPtr(payload.Name),
		Type:            "person_account",
		Department:      lo.TernaryF(payload.Department != nil, func() string { return payload.Department.Name }, func() string { return "" }),
		Email:           lo.FromPtr(payload.Email),
		Description:     "",
		CreatedAt:       "",
		AuthOrigin:      string(AuthOriginCodebase),
		CodebaseUserJWT: tokenValue,
	}

	// sign codebase user jwt with provided scopes
	if s.authConfig.EnableSignCodebaseJWT && len(s.authConfig.SignCodebaseJWTScopes) > 0 {
		codebaseJWT, err := s.codebaseCli.SignCodebaseJWTByCodebaseJWT(ctx, tokenValue, s.authConfig.SignCodebaseJWTScopes)
		if err != nil {
			log.V1.CtxInfo(ctx, "failed to sign codebase jwt from codebase jwt: %v", err)
		} else {
			account.CodebaseUserJWT = codebaseJWT
		}
		if s.nextcodeCli != nil {
			// for next code
			nextCodeJWTResp, err := s.nextcodeCli.SignUserJWT(ctx, bytedance.SignUserJWTRequest{
				Scopes:       s.authConfig.SignNextCodeJWTScopes,
				CloudUserJWT: &tokenValue,
				Expiration:   lo.ToPtr(defaultNextCodeTokenExpiration),
			})
			if err != nil {
				log.V1.CtxInfo(ctx, "failed to sign next code jwt from codebase jwt: %v", err)
			} else {
				account.NextCodeUserJWT = nextCodeJWTResp.GetJWT()
			}
		}
	}

	if s.codebaseBitsAIAccountSecret == nil {
		return account, nil
	}

	// get extra location info; don't break the pipeline if error occurs.
	userInfo, err := s.codebaseCli.BatchGetEmployees(ctx, []string{lo.FromPtr(payload.Username)},
		codebase.WithBitsAIServiceJWT(string(s.codebaseBitsAIAccountSecret.GetValue())))
	if err == nil && userInfo != nil && len(userInfo.Employees) > 0 {
		locale := s.predictUserLocale(ctx, lo.FromPtr(payload.Username), userInfo.Employees[0].WorkCountryEnName)
		account.Locale = locale
	}

	return account, nil
}

func (s *AuthService) authBizIDEJWT(ctx context.Context, tokenValue, userID string) (account *entity.Account, err error) {
	serviceAccount, err := s.AuthServiceAccountJWT(ctx, tokenValue)
	if err != nil {
		return nil, errors.WithMessage(err, "invalid service jwt")
	}
	// TODO(caoyunxiang): validate app id.
	account = &entity.Account{
		Username:    userID,
		Name:        userID,
		Type:        "person_account",
		Department:  "",
		Email:       userID + "@" + serviceAccount.Name,
		Description: "user identity provided by " + serviceAccount.Name,
		CreatedAt:   time.Now().Format(time.RFC3339),
		AuthOrigin:  string(AuthOriginBizIDE),
	}
	return account, nil
}

func (s *AuthService) authBitsAIJWT(ctx context.Context, tokenValue string, username string) (account *entity.Account, err error) {
	if s.authTCCConfig == nil {
		return nil, errors.New("service account jwt is not provided")
	}
	token, err := jwt.ParseWithClaims(tokenValue, new(copilotClaims), s.getPublicKey)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse jwt")
	}

	if claims, ok := token.Claims.(*copilotClaims); ok && token.Valid {
		return &entity.Account{
			Username:          "BitsAI",
			Name:              username,
			Type:              "person_account",
			Department:        "",
			Email:             "",
			Description:       "user identity provided by " + claims.Name,
			CreatedAt:         time.Now().Format(time.RFC3339),
			AuthOrigin:        string(BitsAuthOriginBitsAI),
			CodebaseBitsAIJWT: string(lo.FromPtr(s.codebaseBitsAIAccountSecret.GetPointer())),
		}, nil
	}
	return nil, errors.New("expired jwt or invalid identity")
}

func (s *AuthService) authGDPRToken(ctx context.Context, tokenValue string) (account *entity.Account, err error) {
	verifyRes, err := token.VerifyToken(tokenValue, "", "")
	if err != nil {
		return nil, errors.WithMessage(err, "invalid gdpr token")
	}
	if len(verifyRes.User) == 0 {
		return nil, errors.New("no user identity provided for gdpr token")
	}
	account = &entity.Account{
		ID:              0,
		Username:        verifyRes.User,
		Name:            verifyRes.User,
		Type:            "person_account",
		Department:      "",
		Email:           "",
		Description:     "user identity provided by " + verifyRes.PSM + "/" + verifyRes.Authority,
		CreatedAt:       "",
		AuthOrigin:      string(AuthOriginGDPRToken),
		CodebaseUserJWT: "",
		CloudUserJWT:    "",
	}
	return account, nil
}

func (s *AuthService) authCloudIDEJWT(ctx context.Context, tokenValue, clientIP string) (account *entity.Account, err error) {
	verifyRes, err := s.validateCloudIDEJWT(ctx, tokenValue)
	if err != nil {
		return nil, errors.WithMessage(err, "invalid cloud ide jwt")
	}
	account = &entity.Account{
		ID:               0,
		Username:         verifyRes.ID,
		Name:             verifyRes.ID,
		Type:             "person_account",
		Department:       "",
		Email:            "",
		Description:      "user identity provided by cloud ide",
		CreatedAt:        "",
		AuthOrigin:       string(AuthOriginCloudIDE),
		CodebaseUserJWT:  "",
		CloudUserJWT:     "",
		CloudIDEJWT:      tokenValue,
		CloudIDETenantID: verifyRes.TenantID,
		ClientIP:         clientIP,
	}
	return account, nil
}

// Authenticate from header `Authorization: <origin> <token>`.
func (s *AuthService) authAuthorizationHeader(ctx context.Context, headers HeaderGetter, allowedOrigins []AuthOrigin) (account *entity.Account, err error) {
	header := headers.Get("Authorization")
	if len(header) == 0 {
		return nil, errors.New("no authorization header found")
	}

	parts := strings.SplitN(header, " ", 2)
	if len(parts) != 2 {
		return nil, errors.New("invalid authorization header")
	}

	clientIP := headers.Get("Client-IP")
	log.V1.CtxInfo(ctx, "get user ip: %s", clientIP)

	tokenType := AuthOrigin(parts[0])
	tokenValue := parts[1]
	if len(tokenValue) == 0 {
		return nil, errors.New("no token found")
	}
	switch tokenType {
	case AuthOriginTokenNameByteCloud:
		account, err = s.authByteCloudJWT(ctx, tokenValue)
	case AuthOriginTokenNameCodebase:
		account, err = s.authCodebaseJWT(ctx, tokenValue)
	case AuthOriginTokenNameBizIDE:
		account, err = s.authBizIDEJWT(ctx, tokenValue, headers.Get("X-Username"))
	case AuthOriginTokenNameGDPRToken:
		account, err = s.authGDPRToken(ctx, tokenValue)
	case AuthOriginTokenNameCloudIDE:
		account, err = s.authCloudIDEJWT(ctx, tokenValue, clientIP)
	case AuthOriginTokenNameBitsAI:
		account, err = s.authBitsAIJWT(ctx, tokenValue, headers.Get("X-Username"))
	default:
		return nil, errors.Errorf("unsupported token type: %s", tokenType)
	}
	if account != nil && !lo.Contains(allowedOrigins, AuthOrigin(account.AuthOrigin)) {
		return nil, errors.Errorf("unsupported auth origin: %s", account.AuthOrigin)
	}
	return account, err
}

func (s *AuthService) AuthHeader(ctx context.Context, headers HeaderGetter, allowedOrigins []AuthOrigin) (account *entity.Account, err error) {
	if len(s.authConfig.EnabledAuthOrigins) != 0 {
		log.V1.CtxInfo(ctx, "use allowed auth origins from config file: %s", s.authConfig.EnabledAuthOrigins)
		allowedOrigins = lo.Map(s.authConfig.EnabledAuthOrigins, func(item string, index int) AuthOrigin {
			return AuthOrigin(item)
		})
	}
	account, err = s.authAuthorizationHeader(ctx, headers, allowedOrigins)
	if err == nil {
		log.V1.CtxDebug(ctx, "auth from authorization header: %s", account.AuthOrigin)
		return account, nil
	}

	msg := strings.Builder{}

	// Standard auth header `Authorization: <token-type> <token>` is recommended,
	// but we still check the old-style non-standard token header key, too.
	msg.WriteString("failed to auth standard header: ")
	msg.WriteString(err.Error())

	clientIP := headers.Get("Client-IP")

	msg.WriteString("client ip: " + clientIP)

	for _, origin := range allowedOrigins {
		switch origin {
		case AuthOriginByteCloud:
			token := headers.Get("x-jwt-token")
			if len(token) == 0 {
				msg.WriteString("\nno byte cloud token found.")
				continue
			}
			payload, err := s.byteCloudValidator.Validate(ctx, token)
			if err != nil {
				msg.WriteString("\ninvalid byte cloud token: " + err.Error())
				continue
			}
			locale := s.predictUserLocale(ctx, payload.Username, payload.WorkCountry)
			account = &entity.Account{
				ID:              payload.EmployeeID,
				Username:        payload.Username,
				Name:            payload.Username,
				Type:            string(payload.Type),
				Department:      payload.Organization,
				Locale:          locale,
				Email:           payload.Email,
				Description:     "",
				CreatedAt:       "",
				AuthOrigin:      string(AuthOriginByteCloud),
				CodebaseUserJWT: "",
				CloudUserJWT:    token,
				ClientIP:        clientIP,
			}
			// Only signs codebase jwt if it is person account.
			if s.authConfig.EnableSignCodebaseJWT && payload.Type == "person_account" {
				codebaseJWT, err := s.codebaseCli.SignCodebaseJWTByByteCloudJWT(ctx, token, s.authConfig.SignCodebaseJWTScopes)
				if err != nil {
					log.V1.CtxInfo(ctx, "failed to sign codebase jwt from byte cloud jwt: %v", err)
				} else {
					account.CodebaseUserJWT = codebaseJWT
				}
				if s.nextcodeCli != nil {
					// for next code
					nextCodeJWTResp, err := s.nextcodeCli.SignUserJWT(ctx, bytedance.SignUserJWTRequest{
						Scopes:       s.authConfig.SignNextCodeJWTScopes,
						CloudUserJWT: &token,
						Expiration:   lo.ToPtr(defaultNextCodeTokenExpiration),
					})
					if err != nil {
						log.V1.CtxInfo(ctx, "failed to sign next code jwt from byte cloud jwt: %v", err)
					} else {
						account.NextCodeUserJWT = nextCodeJWTResp.GetJWT()
					}
				}
			}
			return account, nil
		case AuthOriginCodebase:
			token := ""
			// Check cookie first. If not found, check header.
			cookieParts := strings.Split(headers.Get("Cookie"), ";")
			for _, part := range cookieParts {
				part = strings.TrimSpace(part)
				if strings.HasPrefix(part, "Codebase-User-JWT=") {
					token = strings.TrimPrefix(part, "Codebase-User-JWT=")
				}
			}

			if len(token) == 0 {
				codebaseToken := headers.Get("Authorization")
				if strings.Contains(strings.ToLower(codebaseToken), "codebase-user-jwt") {
					token = strings.TrimPrefix(codebaseToken, "Codebase-User-JWT ")
				}
			}

			if len(token) == 0 {
				msg.WriteString("\nno codebase token found.")
				continue
			}

			account, err = s.authCodebaseJWT(ctx, token)

			if err != nil {
				msg.WriteString("\ninvalid codebase token: " + err.Error())
				continue
			}

			return account, nil
		case AuthOriginBizIDE:
			serviceToken := headers.Get("X-Service-Token")
			if len(serviceToken) == 0 {
				msg.WriteString("\nno service token found")
				continue
			}
			serviceAccount, err := s.AuthServiceAccountJWT(ctx, serviceToken)
			if err != nil {
				msg.WriteString("\ninvalid service token: " + err.Error())
				continue
			}
			// TODO(caoyunxiang): validate app id.
			username := headers.Get("X-Username")
			if len(username) == 0 {
				msg.WriteString("\nno valid service provided username found")
				continue
			}
			account = &entity.Account{
				Username:    username,
				Name:        username,
				Type:        "person_account",
				Department:  "",
				Email:       username + "@" + serviceAccount.Name,
				Description: "user identity provided by " + serviceAccount.Name,
				CreatedAt:   time.Now().Format(time.RFC3339),
				AuthOrigin:  string(AuthOriginBizIDE),
			}
			return account, nil
		case AuthOriginGDPRToken:
			ztiToken := headers.Get("X-Auth-Token")
			if len(ztiToken) == 0 {
				msg.WriteString("\nno gdpr token found")
				continue
			}
			verifyRes, err := token.VerifyToken(ztiToken, "", "")
			if err != nil {
				msg.WriteString("\ninvalid gdpr token: " + err.Error())
				continue
			}
			if len(verifyRes.User) == 0 {
				msg.WriteString("\nno user identity provided for gdpr token")
				continue
			}
			account = &entity.Account{
				ID:              0,
				Username:        verifyRes.User,
				Name:            verifyRes.User,
				Type:            "person_account",
				Department:      "",
				Email:           "",
				Description:     "user identity provided by " + verifyRes.PSM + "/" + verifyRes.Authority,
				CreatedAt:       "",
				AuthOrigin:      string(AuthOriginGDPRToken),
				CodebaseUserJWT: "",
				CloudUserJWT:    "",
			}
			return account, nil
		case AuthOriginCloudIDE:
			ideJWT := headers.Get("X-IDE-Token")
			if len(ideJWT) == 0 {
				msg.WriteString("\nno ide token found")
				continue
			}

			verifyRes, err := s.validateCloudIDEJWT(ctx, ideJWT)
			if err != nil {
				msg.WriteString("\ninvalid ide token: " + err.Error())
				continue
			}
			account = &entity.Account{
				ID:               0,
				Username:         verifyRes.ID,
				Name:             verifyRes.ID,
				Type:             "person_account",
				Department:       "",
				Email:            "",
				Description:      "user identity provided by " + verifyRes.TenantID,
				CreatedAt:        "",
				AuthOrigin:       string(AuthOriginCloudIDE),
				CodebaseUserJWT:  "",
				CloudUserJWT:     "",
				CloudIDEJWT:      ideJWT,
				CloudIDETenantID: verifyRes.TenantID,
				ClientIP:         clientIP,
			}
			return account, nil
		default:
			msg.WriteString("\nunsupported auth origin")
		}
	}
	return nil, errors.New("no valid credentials found: " + msg.String())
}

type CloudIDEAccount struct {
	ID       string `json:"id"`
	TenantID string `json:"tenant_id"`
	Type     string `json:"type"`
}

type CloudIDEJWTClaims struct {
	jwt.StandardClaims
	Data CloudIDEAccount `json:"data"`
}

func (s *AuthService) validateCloudIDEJWT(ctx context.Context, token string) (*CloudIDEAccount, error) {
	c := new(CloudIDEJWTClaims)
	// the user id is not validated, just for debug.
	pendingUserID := ""
	_, err := jwt.ParseWithClaims(token, c, func(t *jwt.Token) (interface{}, error) {
		ideClaims, _ := t.Claims.(*CloudIDEJWTClaims)
		pendingUserID = ideClaims.Data.ID
		pubKey, ok := s.cloudIDEPubs.PubKeys[ideClaims.Data.TenantID]
		if !ok {
			return nil, errors.Errorf("unsupported tenant: %s", ideClaims.Data.TenantID)
		}
		return jwt.ParseRSAPublicKeyFromPEM([]byte(pubKey))
	})
	if err != nil {
		return nil, errors.WithMessagef(err, "invalid or expired cloud ide jwt(unauthenticated user id: %s)", pendingUserID)
	}
	if c.Data.Type != "user" {
		return nil, errors.Errorf("unsupported ide account type: %s@%s", c.Data.Type, c.Data.TenantID)
	}

	if s.authConfig.EnableValidateCloudIDEJWT {
		if s.ideAccountClient == nil {
			log.V1.CtxWarn(ctx, "cloud IDE account JWT validation is enabled but no client is provided")
			return &c.Data, nil
		}
		if !lo.Contains(s.authConfig.EnableValidateCloudIDEJWTTenantIDs, c.Data.TenantID) {
			log.V1.CtxInfo(ctx, "CloudIDE tenant %s is not enabled to validate JWT via API", c.Data.TenantID)
			return &c.Data, nil
		}
		if !s.ideAccountClient.MethodEnabled("CheckLogin") {
			log.V1.CtxInfo(ctx, "cloud ide CheckLogin is not enabled")
			return &c.Data, nil
		}

		// FIXME(cyx): not ignore error?
		login, err := s.ideAccountClient.CheckLogin(ctx, token, c.Data.TenantID)
		if err != nil {
			if errors.Is(err, ideaccount.ErrDeregister) {
				//注销的错误不忽略
				return nil, err
			}
			log.V1.CtxWarn(ctx, "failed to check cloud ide is login: %v", err)
			return &c.Data, nil
		}
		if !login {
			log.V1.CtxInfo(ctx, "ide account %s@%s is not login, authentication denied", c.Data.ID, c.Data.TenantID)
			return nil, errors.Errorf("ide account %s@%s is not login, authentication denied", c.Data.ID, c.Data.TenantID)
		}
		log.V1.CtxInfo(ctx, "check login passed for ide account %s@%s", c.Data.ID, c.Data.TenantID)
	}

	return &c.Data, nil
}
