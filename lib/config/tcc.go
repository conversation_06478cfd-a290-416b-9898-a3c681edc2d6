package config

import (
	"code.byted.org/devgpt/kiwis/lib/tcc"
)

type DevGPTTCCConfig struct {
	CodebaseServiceJWT       *tcc.GenericConfig[CodebaseServiceJWTConfig]    `tcc:"key:codebase_service_jwt;format:string;space:default"`
	CORSConfig               *tcc.GenericConfig[CORSConfig]                  `tcc:"key:cors_config;format:yaml;space:default"`
	ApplicationModelConfig   *tcc.GenericConfig[ApplicationModelConfig]      `tcc:"key:application_model_config;format:json;space:default"`
	CodeCopilotFeatureConfig *tcc.GenericConfig[CodeCopilotFeatureConfig]    `tcc:"key:code_copilot_feature_config;format:yaml;space:default"`
	MergeRequestConfig       *tcc.GenericConfig[MergeRequestConfig]          `tcc:"key:prompt_config;format:json;space:default"`
	MaasAKConfig             *tcc.GenericConfig[MaasAKConfigs]               `tcc:"key:maas_ak_sk;format:json;space:default"`
	MaasApiKeyConfig         *tcc.GenericConfig[MaasApiKeyConfigs]           `tcc:"key:maas_api_key;format:json;space:default"`
	AzureAKConfig            *tcc.GenericConfig[AzureAKConfig]               `tcc:"key:azure_ak;format:json;space:default"`
	AntidirtConfig           *tcc.GenericConfig[AntidirtConfig]              `tcc:"key:antidirt_config;format:json;space:default"`
	RateLimitConfig          *tcc.GenericConfig[RateLimitConfig]             `tcc:"key:rate_limit_config;format:json;space:default"`
	DatasetConfig            *tcc.GenericConfig[DatasetTCCConfig]            `tcc:"key:dataset_config;format:json;space:default"`
	EmbeddingDatasetConfig   *tcc.GenericConfig[EmbeddingModelDatasetConfig] `tcc:"key:embedding-model_dataset_id;format:json;space:default"`
	KBDevGPTToken            *tcc.GenericConfig[KBDevGPTTCCTokenConfig]      `tcc:"key:kb_devgpt_token;format:json;space:default"`
	ModelDispatchConfig      *tcc.GenericConfig[ModelDispatchConfig]         `tcc:"key:model_auth_config;format:yaml;space:default"`
}

type PromptTemplateConfig map[string]string

type CodebaseServiceJWTConfig string
type AzureAKConfig map[string]string // key：账号名(默认default)，value：密钥

type ApplicationModelConfig map[string]ApplicationModel

type ApplicationModel struct {
	MainModel        string   `json:"main_model"`
	FallbackModels   []string `json:"fallback_models"`
	PromptTemplate   string   `json:"prompt_template"`
	PromptTemplateZh string   `json:"prompt_template_zh"`
	MaxTokenLimit    int      `json:"max_token_limit"`
	Temperature      float32  `json:"temperature"`
}

type FeatureConfig struct {
	Usernames      []string `yaml:"usernames"`
	Departments    []string `yaml:"departments"`
	Reponames      []string `yaml:"reponames"`
	RepoGroupnames []string `yaml:"repo_groupnames"`
	// required indicate the matching for these repose are restricted, same as "and" for the whole query
	RequiredReponames      []string `yaml:"required_reponames"`
	RequiredRepoGroupnames []string `yaml:"required_repo_groupnames"`
}

type CodeCopilotFeatureConfig map[string]FeatureConfig

type MaasAKConfigs []MaasAKConfig

type MaasAKConfig struct {
	AccessKey string `json:"access_key"`
	SecretKey string `json:"secret_key"`
	// Models is the available models for this ak.
	Models []string `json:"models"`
}

type MaasApiKeyConfigs []MaasApiKeyConfig

type MaasApiKeyConfig struct {
	ApiKey string `json:"api_key"`
	// Models is the available models for this ak.
	Models []string `json:"models"`
}

type AntidirtInnerStructure struct {
	Scene         string  `json:"scene" yaml:"scene"`
	TableIDs      []int64 `json:"table_ids" yaml:"table_ids"`
	WhiteTableIDs []int64 `json:"white_table_ids" yaml:"white_table_ids"`
}

type AntidirtConfig struct {
	PSM           string                    `json:"psm" yaml:"psm"`         // antidirt psm
	Cluster       string                    `json:"cluster" yaml:"cluster"` // antidirt 下游集群，在申请对应工单时候会填写，默认default
	Caller        string                    `json:"caller" yaml:"caller"`
	TableIDs      []int64                   `json:"table_ids" yaml:"table_ids"`
	WhiteTableIDs []int64                   `json:"white_table_ids" yaml:"white_table_ids"`
	Scenes        []*AntidirtInnerStructure `json:"scenes" yaml:"scenes"` // 分场景的产品词表
	Timeout       int                       `json:"timeout" yaml:"timeout"`
}

type MergeRequestConfig struct {
	TotalLengthThreshold      int             `json:"mr_summary_length_threshold"`
	SingleFileLengthThreshold int             `json:"mr_summary_file_length_threshold"`
	ChangeTypes               map[string]bool `json:"mr_summary_change_types"`
}

type RateLimitConfig struct {
	// allow `UserQuota` events per `Duration` seconds
	UserQuota int `json:"user_quota"`
	Duration  int `json:"window"`
}

type DatasetTCCConfig struct {
	// AutoUpdateDatasetIDs is a whitelist of dataset IDs that will be updated automatically.
	AutoUpdateDatasetIDs []int64 `json:"auto_update_dataset_ids"`
}

type KBDevGPTTCCTokenConfig struct {
	// DevGPT 管理员 token
	Token string `json:"token"`
}

type EmbeddingModelDatasetConfig []EmbeddingDataset
type EmbeddingDataset struct {
	// 某个模型创建索引并存储的 datasets.
	IndexedModel string `json:"indexed_model"`
	DatasetID    int    `json:"dataset_id"`
}
