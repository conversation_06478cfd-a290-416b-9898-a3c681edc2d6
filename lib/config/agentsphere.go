package config

type AgentSphereConfig struct {
	DBConfig            RDSConfig                      `yaml:"DBConfig"`
	TCCConfig           TCCConfig                      `yaml:"TCCConfig"`
	AuthConfig          AuthConfig                     `yaml:"AuthConfig"`
	CodebaseConfig      CodebaseConfig                 `yaml:"CodebaseConfig"`
	RedisConfig         RedisConfig                    `yaml:"RedisConfig"`
	MQConfig            AgentSphereMQConfig            `yaml:"MQConfig"`
	RuntimeAPIConfig    AgentSphereRuntimeAPIConfig    `yaml:"RuntimeAPIConfig"`
	KnowledgebaseConfig AgentSphereKnowledgebaseConfig `yaml:"KnowledgebaseConfig"`
	ByteDocConfig       ByteDocConfig                  `yaml:"ByteDocConfig"`
	// psm: flow.agentsphere.runtime, 和Runtime同镜像同psm的HTTP服务
	RuntimeServerConfig   AgentSphereRuntimeServerConfig `yaml:"RuntimeServerConfig"`
	CloudMCPAPIConfig     CloudMCPAPIConfig              `yaml:"CloudMCPAPIConfig"`
	BaseConfig            BaseConfig                     `yaml:"BaseConfig"`
	BPMConfig             AgentSphereBpmConfig           `yaml:"BPMConfig"`
	SCMConfig             AgentSphereScmConfig           `yaml:"SCMConfig"`
	ICMConfig             AgentSphereIcmConfig           `yaml:"ICMConfig"`
	AgentDeploymentConfig AgentDeploymentConfig          `yaml:"AgentDeploymentConfig"`
}

type BaseConfig struct {
	Domain string `yaml:"Domain"`
}

type AgentSphereMQConfig struct {
	RuntimeOrchestrator     RocketMQConfig `yaml:"RuntimeOrchestrator" name:"runtime_orchestrator"`
	AssignmentMonitor       RocketMQConfig `yaml:"AssignmentMonitor" name:"assignment_monitor"`
	NextRuntimeOrchestrator RocketMQConfig `yaml:"NextRuntimeOrchestrator" name:"next_runtime_orchestrator"`
	NextSessionMonitor      RocketMQConfig `yaml:"NextSessionMonitor" name:"next_session_monitor"`
	Knowledgebase           RocketMQConfig `yaml:"Knowledgebase" name:"knowledgebase"`
	KnowledgebaseOffline    RocketMQConfig `yaml:"KnowledgebaseOffline" name:"knowledgebase_offline"`
	NextTrace               RocketMQConfig `yaml:"NextTrace" name:"next_trace"`
	DeployReview            RocketMQConfig `yaml:"DeployReview" name:"deploy_review"`
	TestingMonitor          RocketMQConfig `yaml:"TestingMonitor" name:"testing_monitor"`
	TaskCronJob             RocketMQConfig `yaml:"TaskCronJob" name:"task_cronjob"`
}

// For Runtime to connect to
type AgentSphereRuntimeAPIConfig struct {
	// APIBaseURL is the base URL of the API server.
	// E.g. https://agentsphere.bytedance.net
	APIBaseURL string `yaml:"APIBaseURL"`
	// APIPrefix is the prefix of the API server.
	// E.g. /api/agents/v2
	APIPrefix string `yaml:"APIPrefix"`
}
type AgentSphereKnowledgebaseConfig struct {
	ElasticSearchPSM         string `yaml:"ElasticSearchPSM"`
	ElasticSearchCluster     string `yaml:"ElasticSearchCluster"`
	ElasticSearchRecallIndex string `yaml:"ElasticSearchRecallIndex"`
	VikingName               string `yaml:"VikingName"`
	PruneSegmentTable        string `yaml:"PruneSegmentTable"`
	PatrolSegmentTable       string `yaml:"PatrolSegmentTable"`
}

// To Connect "Mock" Runtime (tce psm: flow.agentsphere.runtime)
type AgentSphereRuntimeServerConfig struct {
	// APIBaseURL is the base URL of the API server.
	APIBaseURL string `yaml:"APIBaseURL"`
}

type CloudMCPAPIConfig struct {
	// APIBaseURL is the base URL of the API server.
	APIBaseURL string `yaml:"APIBaseURL"`
}

type AgentSphereBpmConfig struct {
	Host             string `yaml:"Host"`
	Timeout          int    `yaml:"Timeout"`
	WorkflowConfigID string `yaml:"WorkflowConfigID"`
}

type AgentSphereScmConfig struct {
	Host     string `yaml:"Host"`
	Timeout  int    `yaml:"Timeout"`
	RepoName string `yaml:"RepoName"`
	RepoID   string `yaml:"RepoID"`
}

type AgentSphereIcmConfig struct {
	Host                string `yaml:"Host"`
	Timeout             int    `yaml:"Timeout"`
	Namespace           string `yaml:"Namespace"`
	OnlineImageName     string `yaml:"OnlineImageName"`
	OnlineBashImageName string `yaml:"OnlineBashImageName"`
	Region              string `yaml:"Region"`
	Registry            string `yaml:"Registry"`
}

type AgentDeploymentConfig struct {
	FrontendURL string `yaml:"FrontendURL"`
	BackendURL  string `yaml:"BackendURL"`
}
