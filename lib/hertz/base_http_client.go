package hertz

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"runtime/debug"
	"strings"
	"time"

	"github.com/cloudwego/hertz/pkg/protocol/consts"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/byted/discovery"
	"code.byted.org/middleware/hertz/pkg/app/client"
	"code.byted.org/middleware/hertz/pkg/common/config"
	"code.byted.org/middleware/hertz/pkg/network/standard"
	"code.byted.org/middleware/hertz/pkg/protocol"
	ztihelper "code.byted.org/security/zti-jwt-helper-golang/helper"

	"code.byted.org/devgpt/kiwis/lib/stream"
)

// Client is a wrapped Hertz HTTP client
// which provides more friendly interfaces for HTTP requests.
type Client struct {
	baseURL string
	cli     *byted.Client
	// TODO: Remove it, get log level to decide whether to print debug info.
	debug      bool
	disableLog bool
	timeout    time.Duration

	injectZtiTokenHeader string

	headers map[string]string

	useSD     bool
	sdCluster string
}

type NewHTTPClientOption struct {
	Timeout    time.Duration
	Debug      bool
	DisableLog bool

	InjectZtiTokenHeader string

	Headers map[string]string

	EnableStream bool

	UseSD               bool
	SDCluster           string // Target cluster name, only used when UseSD is true.
	MaxConnsPerHost     int
	MaxIdleConnDuration time.Duration
	MaxConnDuration     time.Duration
}

func NewClient(baseURL string, opt NewHTTPClientOption) (*Client, error) {
	options := []config.ClientOption{
		client.WithDialer(standard.NewDialer()),
		client.WithResponseBodyStream(opt.EnableStream),
	}
	if opt.MaxConnsPerHost > 0 {
		options = append(options, client.WithMaxConnsPerHost(opt.MaxConnsPerHost))
	}
	if opt.MaxIdleConnDuration > 0 {
		options = append(options, client.WithMaxIdleConnDuration(opt.MaxIdleConnDuration))
	}
	if opt.MaxConnDuration > 0 {
		options = append(options, client.WithMaxConnDuration(opt.MaxConnDuration))
	}
	httpCli, err := byted.NewClient(
		byted.WithAppClientOptions(
			options...,
		),
	)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create HTTP client")
	}
	cli := &Client{
		baseURL:    baseURL,
		cli:        httpCli,
		debug:      opt.Debug,
		disableLog: opt.DisableLog,
		timeout:    opt.Timeout,

		injectZtiTokenHeader: opt.InjectZtiTokenHeader,

		headers: opt.Headers,

		useSD:     opt.UseSD,
		sdCluster: opt.SDCluster,
	}
	return cli, nil
}

// HTTPError is the error occurred when requesting server.
type HTTPError struct {
	Code    int64  `json:"code"`    // Common field to contain error code.
	Message string `json:"message"` // Common field to contain error message.
	// Extra is the error message when parsing response or requesting.
	Extra string `json:"-"`
	// StatusCode is the server returned HTTP status code.
	StatusCode int    `json:"-"`
	LogID      string `json:"-"`
}

func (e *HTTPError) Error() string {
	var str []string
	if e.StatusCode > 0 {
		str = append(str, " got code: "+http.StatusText(e.StatusCode))
	}
	if len(e.Message) != 0 {
		str = append(str, " err msg: "+e.Message)
	}
	if len(e.Extra) != 0 {
		str = append(str, " extra msg: "+e.Extra)
	}
	if len(e.LogID) != 0 {
		str = append(str, " log id: "+e.LogID)
	}

	return strings.TrimSpace(strings.Join(str, ","))
}

func getHTTPErrorReason(err error) string {
	if err == nil {
		return "none"
	}

	var uErr *url.Error
	if errors.As(err, &uErr) && uErr.Timeout() {
		return "timeout"
	}

	if errors.Is(err, context.DeadlineExceeded) || os.IsTimeout(err) {
		return "timeout"
	}

	if errors.Is(err, context.Canceled) {
		return "cancelled"
	}

	var kErr *HTTPError
	if !errors.As(err, &kErr) {
		return "unknown"
	}

	return fmt.Sprintf("status_%d", kErr.StatusCode)
}

type ReqOption struct {
	ExpectedCode int
	// Body is the request body.
	// If nil, the request body would be empty.
	// If []byte, the request body would be the raw bytes.
	// If other type, the request body would be the json marshal of it.
	Body any
	// Result is the pointer of the result struct.
	// The response body would unmarshal into it if not nil.
	Result  any
	Notes   string
	Headers map[string]string
	Cookies map[string]string
	// If close the connection after request done,
	// this is useful for big file downloading.
	SetConnectionClose bool
	Timeout            time.Duration

	// Fill response headers if required.
	ResponseHeaders map[string]string
	// Env headers such as `x-tt-env` and `x-use-ppe` will be filled if SetTTEnvHeaders is true.
	SetTTEnvHeaders bool
}

type Event struct {
	ID    string
	Event string
	Data  []byte
	// Retry int // Add if needed.
}

var (
	sseDataPrefix  = []byte("data:")
	sseIDPrefix    = []byte("id:")
	sseEventPrefix = []byte("event:")
)

// according to the spec, the prefix and first space should be trim.
func trimPrefixAndSpace(data []byte, prefix []byte) []byte {
	data = bytes.TrimPrefix(data, prefix)
	if len(data) > 0 && data[0] == ' ' {
		data = data[1:]
	}
	return data
}

func (c *Client) handleSSE(ctx context.Context, ch *stream.SendChannel[*Event], resp *protocol.Response) {
	defer func(resp *protocol.Response) {
		if e := recover(); e != nil {
			log.V1.CtxError(ctx, "recovering panic during handle SSE: %+v, %s", e, string(debug.Stack()))
		}
		ch.Close()
		err := resp.CloseBodyStream()
		if err != nil {
			log.V1.CtxWarn(ctx, "failed to close body stream: %v", err)
		}
	}(resp)

	scanner := bufio.NewScanner(resp.BodyStream())

	// TODO(cyx): use sync.Pool for event if necessary.
	event := new(Event)

	for scanner.Scan() {
		// Do deep copy, as the scanner.Bytes() is cut from the scanner's buf,
		// and may be overwritten.
		tmpData := scanner.Bytes()
		if len(tmpData) == 0 {
			newEvent := &Event{
				ID:    event.ID,
				Event: "",
				Data:  nil,
			}
			ch.DataChannel <- event
			event = newEvent
			continue
		}
		newData := make([]byte, len(tmpData))
		copy(newData, tmpData)

		switch {
		case bytes.HasPrefix(newData, sseIDPrefix):
			event.ID = string(trimPrefixAndSpace(newData, sseIDPrefix))
			continue
		case bytes.HasPrefix(newData, sseEventPrefix):
			event.Event = string(trimPrefixAndSpace(newData, sseEventPrefix))
			continue
		case bytes.HasPrefix(newData, sseDataPrefix):
			newData = trimPrefixAndSpace(newData, sseDataPrefix)
			event.Data = append(event.Data, newData...)
			event.Data = append(event.Data, '\n')
			continue
		default:
			// retry or other things are ignored.
		}
	}

	if scannerErr := scanner.Err(); scannerErr != nil {
		log.V1.CtxError(ctx, "failed to read SSE stream: %v", scannerErr)
		ch.ErrorChannel <- scannerErr
	}
}

func (c *Client) DoJSONReq(ctx context.Context, method, paths string, opt ReqOption) (resp *protocol.Response, fErr error) {
	var (
		req               = protocol.AcquireRequest()
		logger            = log.V1
		shouldReleaseResp bool
	)
	resp = protocol.AcquireResponse()
	ctx = logs.CtxAddKVs(ctx, "method", method, "paths", paths)
	defer func(start time.Time) {
		// Add metrics and logs for request results.
		var (
			reqName   = opt.Notes
			costSec   = time.Since(start).Seconds()
			errReason = getHTTPErrorReason(fErr)
		)
		if len(reqName) == 0 {
			reqName = fmt.Sprintf("%s_%s", method, paths)
		}
		if !c.disableLog {
			logger.CtxInfo(ctx, "completed http request(note: %s), cost %.2f s, got status: %d, err: %v, reason: %s",
				reqName, costSec, resp.StatusCode(), fErr, errReason)
		}

		// Release req and resp.
		protocol.ReleaseRequest(req)
		if shouldReleaseResp {
			protocol.ReleaseResponse(resp)
		}
	}(time.Now())
	if c.useSD {
		req.SetRequestURI("http://" + c.baseURL + paths)
		req.SetOptions(discovery.WithSD(true))
		if len(c.sdCluster) > 0 {
			req.SetOptions(discovery.WithDestinationCluster(c.sdCluster))
		}
	} else {
		req.SetRequestURI(c.baseURL + paths)
	}
	req.URI().DisablePathNormalizing = true
	req.SetMethod(method)
	if opt.SetConnectionClose {
		req.SetConnectionClose()
	}

	if len(c.injectZtiTokenHeader) != 0 {
		ztiToken, err := ztihelper.GetJwtSVID()
		if err != nil { // Or just return error?
			log.V1.CtxWarn(ctx, "failed to get zti token: %v", err)
		} else {
			req.Header.Set(c.injectZtiTokenHeader, ztiToken)
		}
	}

	req.Header.Set("Content-Type", consts.MIMEApplicationJSON)
	if opt.SetTTEnvHeaders {
		for k, v := range getTTEnvHeaders(ctx) {
			req.Header.Set(k, v)
		}
	}

	for k, v := range c.headers {
		req.Header.Set(k, v)
	}
	for k, v := range opt.Headers {
		req.Header.Set(k, v)
	}
	if len(opt.Cookies) > 0 {
		req.SetCookies(opt.Cookies)
	}

	if c.debug {
		fmt.Printf("%s %s\n", string(req.Method()), req.URI())
		req.Header.VisitAll(func(key, value []byte) {
			fmt.Printf("%s: %s\n", string(key), string(value))
		})
	}

	if opt.Body != nil {
		switch v := opt.Body.(type) {
		case []byte:
			req.SetBodyRaw(v)
		case io.Reader:
			req.SetBodyStream(v, -1)
		default:
			data, err := json.Marshal(opt.Body)
			if err != nil {
				return resp, nil
			}
			if c.debug {
				jsonStr, _ := json.MarshalIndent(opt.Body, "", "  ")
				fmt.Println(string(jsonStr))
			}
			req.SetBodyRaw(data)
		}
	}

	timeout := lo.Ternary(opt.Timeout > 0, opt.Timeout, c.timeout)
	err := c.cli.DoTimeout(ctx, req, resp, timeout)
	if err != nil {
		return resp, errors.WithMessage(err, "failed to do request")
	}

	pErr := &HTTPError{
		Extra:      "",
		StatusCode: resp.StatusCode(),
		LogID:      resp.Header.Get("X-TT-LOGID"),
	}
	if c.debug {
		fmt.Printf("X-TT-LOGID: %s\n", resp.Header.Get("X-TT-LOGID"))
		buf := bytes.NewBuffer(nil)
		_ = json.Indent(buf, resp.Body(), "", "  ")
		fmt.Println(buf.String())
		// FIXME(cyx): when stream body is enabled, this will consume and close body stream.
	}

	if opt.ExpectedCode > 0 && resp.StatusCode() != opt.ExpectedCode {
		err = json.Unmarshal(resp.Body(), pErr)
		if err == nil {
			pErr.Extra = "original response: " + string(resp.Body())
			return resp, pErr
		}
		return resp, errors.Errorf("got unexpected status code: %d, response: %s", resp.StatusCode(), string(resp.Body()))
	}

	if opt.ResponseHeaders != nil {
		for k := range opt.ResponseHeaders {
			opt.ResponseHeaders[k] = resp.Header.Get(k)
		}
	}

	if opt.Result != nil {
		switch r := opt.Result.(type) {
		case *io.ReadCloser:
			// Let the caller close and release response.
			shouldReleaseResp = false
			*r = io.ReadCloser(&readCloser{
				Reader: resp.BodyStream(),
				close: func() error {
					return resp.CloseBodyStream()
				},
			})
			return resp, nil
		case *[]byte:
			*r = resp.Body()
		case *stream.SendChannel[*Event]:
			shouldReleaseResp = false
			go c.handleSSE(ctx, r, resp)
			return resp, nil
		default:
			if err := json.Unmarshal(resp.Body(), opt.Result); err != nil {
				pErr.Extra = fmt.Sprintf("failed to parse response(%v)", err)
				return resp, pErr
			}
		}
	}

	return resp, nil
}

func (c *Client) DoRawReq(ctx context.Context, method, paths string, opt ReqOption) (resp *protocol.Response, fErr error) {
	var (
		req = protocol.AcquireRequest()
	)
	if c.useSD {
		req.SetRequestURI("http://" + c.baseURL + paths)
		req.SetOptions(discovery.WithSD(true))
		if len(c.sdCluster) > 0 {
			req.SetOptions(discovery.WithDestinationCluster(c.sdCluster))
		}
	} else {
		req.SetRequestURI(c.baseURL + paths)
	}
	resp = protocol.AcquireResponse()
	req.SetMethod(method)
	if opt.SetConnectionClose {
		req.SetConnectionClose()
	}
	if len(c.injectZtiTokenHeader) != 0 {
		ztiToken, err := ztihelper.GetJwtSVID()
		if err != nil { // Or just return error?
			log.V1.CtxWarn(ctx, "failed to get zti token: %v", err)
		} else {
			req.Header.Set(c.injectZtiTokenHeader, ztiToken)
		}
	}

	for k, v := range c.headers {
		req.Header.Set(k, v)
	}
	for k, v := range opt.Headers {
		req.Header.Set(k, v)
	}
	if len(opt.Cookies) > 0 {
		req.SetCookies(opt.Cookies)
	}

	if opt.Body != nil {
		switch v := opt.Body.(type) {
		case []byte:
			req.SetBodyRaw(v)
		case io.Reader:
			req.SetBodyStream(v, -1)
		default:
			data, err := json.Marshal(opt.Body)
			if err != nil {
				return resp, nil
			}
			if c.debug {
				jsonStr, _ := json.MarshalIndent(opt.Body, "", "  ")
				fmt.Println(string(jsonStr))
			}
			req.SetBodyRaw(data)
			req.Header.SetContentTypeBytes([]byte(consts.MIMEApplicationJSON))
		}
	}

	timeout := lo.Ternary(opt.Timeout > 0, opt.Timeout, c.timeout)
	err := c.cli.DoTimeout(ctx, req, resp, timeout)
	if err != nil {
		return resp, errors.WithMessage(err, "failed to do request")
	}

	pErr := &HTTPError{
		Extra:      "",
		StatusCode: resp.StatusCode(),
		LogID:      resp.Header.Get("X-TT-LOGID"),
	}
	if opt.ExpectedCode > 0 && resp.StatusCode() != opt.ExpectedCode {
		err = json.Unmarshal(resp.Body(), pErr)
		if err == nil {
			pErr.Extra = "original response: " + string(resp.Body())
			return resp, pErr
		}
		return resp, errors.Errorf("got unexpected status code: %d, response: %s", resp.StatusCode(), string(resp.Body()))
	}

	if opt.ResponseHeaders != nil {
		for k := range opt.ResponseHeaders {
			opt.ResponseHeaders[k] = resp.Header.Get(k)
		}
	}
	return resp, nil
}

type readCloser struct {
	io.Reader
	close  func() error
	closed bool
}

func (rc *readCloser) Close() error {
	if rc.closed {
		return nil
	}
	rc.closed = true
	return rc.close()
}

func getTTEnvHeaders(ctx context.Context) map[string]string {
	headers := make(map[string]string)
	if env := ctxvalues.EnvDefault(ctx); env != "" {
		headers["x-tt-env"] = env
		if strings.HasPrefix(env, "ppe_") {
			headers["x-use-ppe"] = "1"
		}
		if strings.HasPrefix(env, "boe_") {
			headers["x-use-boe"] = "1"
		}
	}
	return headers
}
