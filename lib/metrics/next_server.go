package metrics

import (
	"time"

	gmetrics "code.byted.org/gopkg/metrics/generic"
)

var NSM NextServerMetricSet

func InitNextServerMetric() error {
	return gmetrics.RegisterMetrics(&NSM)
}

type NextServerMetricSet struct {
	AgentRate  gmetrics.DeltaCounterVec[NextServerAgentTag] `name:"agent_rate"`
	AgentError gmetrics.DeltaCounterVec[NextServerAgentTag] `name:"agent_error"`
	AgentTimer gmetrics.TimerVec[NextServerAgentTag]        `name:"agent_timer"`

	SessionRate             gmetrics.DeltaCounterVec[NextServerSessionTag]       `name:"session_rate"`
	SessionError            gmetrics.DeltaCounterVec[NextServerSessionTag]       `name:"session_error"`
	SessionTimer            gmetrics.TimerVec[NextServerSessionTag]              `name:"session_timer"`
	SessionStatusRate       gmetrics.DeltaCounterVec[NextServerSessionStatusTag] `name:"session_status_rate"`
	SessionStayTimer        gmetrics.TimerVec[NextServerSessionStayTag]          `name:"session_stay_timer"`
	SessionStay             gmetrics.DeltaCounterVec[NextServerSessionStayTag]   `name:"session_stay"`
	SessionWithTemplateRate gmetrics.DeltaCounterVec[NextServerSessionTag]       `name:"session_with_template_rate"`
	SessionWithOpenAPITask  gmetrics.DeltaCounterVec[NextServerSessionTag]       `name:"session_with_open_api_task_rate"`

	ArtifactRate  gmetrics.DeltaCounterVec[NextServerArtifactTag] `name:"artifact_rate"`
	ArtifactError gmetrics.DeltaCounterVec[NextServerArtifactTag] `name:"artifact_error"`
	ArtifactTimer gmetrics.TimerVec[NextServerArtifactTag]        `name:"artifact_timer"`

	ModuleRate  gmetrics.DeltaCounterVec[NextServerModuleTag] `name:"module_rate"`
	ModuleError gmetrics.DeltaCounterVec[NextServerModuleTag] `name:"module_error"`
	ModuleTimer gmetrics.TimerVec[NextServerModuleTag]        `name:"module_timer"`

	EventRate      gmetrics.DeltaCounterVec[NextServerEventProcessTag]   `name:"event_rate"`
	EventError     gmetrics.DeltaCounterVec[NextServerEventProcessTag]   `name:"event_error"`
	EventTimer     gmetrics.TimerVec[NextServerEventProcessTag]          `name:"event_timer"`
	EventDoneRate  gmetrics.DeltaCounterVec[NextServerDoneEventTag]      `name:"event_done_rate"`
	EventDoneError gmetrics.DeltaCounterVec[NextServerDoneEventErrorTag] `name:"event_done_error"`

	AgentFirstEventLatency   gmetrics.TimerVec[NextServerAgentEventTag]                      `name:"agent_fist_event_latency"` // session开启到接收到第一个有效agent event耗时
	AgentEventRate           gmetrics.DeltaCounterVec[NextServerAgentEventTag]               `name:"agent_event_rate"`
	AgentEventOffsetAbnormal gmetrics.DeltaCounterVec[NextServerAgentEventOffsetAbnormalTag] `name:"agent_event_offset_abnormal"`

	ConnectAgentRate  gmetrics.DeltaCounterVec[NextServerConnectAgentTag] `name:"connect_agent_rate"`
	ConnectAgentError gmetrics.DeltaCounterVec[NextServerConnectAgentTag] `name:"connect_agent_error"`
	ConnectAgentTimer gmetrics.TimerVec[NextServerConnectAgentTag]        `name:"connect_agent_timer"`

	PrepareCubeNotExist gmetrics.StoreVec[NextServerPrepareCubeTag] `name:"prepare_cube_not_exist"`
	PrepareCubeSize     gmetrics.StoreVec[NextServerPrepareCubeTag] `name:"prepare_cube_size"`
	PrepareCubeTimer    gmetrics.TimerVec[NextServerPrepareCubeTag] `name:"prepare_cube_timer"`

	PermissionError   gmetrics.DeltaCounterVec[NextServerPermissionEventTag] `name:"permission_error"`
	KnowledgeBaseRate gmetrics.DeltaCounterVec[NextServerKnowledgeBaseTag]   `name:"knowledgebase_rate"`

	ScenarioDetectionRate  gmetrics.DeltaCounterVec[NextServerScenarioDetectionTag] `name:"scenario_detection_rate"`
	ScenarioDetectionError gmetrics.DeltaCounterVec[NextServerScenarioDetectionTag] `name:"scenario_detection_error"`
	ScenarioDetectionTimer gmetrics.TimerVec[NextServerScenarioDetectionTag]        `name:"scenario_detection_timer"`
}

func (d *NextServerMetricSet) ReportAgentMethodMetrics() func(method string, isError bool) {
	start := time.Now()
	return func(method string, isError bool) {
		tags := &NextServerAgentTag{
			Method: method,
		}
		// rate
		_ = d.AgentRate.WithTags(tags).Add(1)
		// latency
		_ = d.AgentTimer.WithTags(tags).Observe(float64(time.Since(start).Milliseconds()))
		// err
		if isError {
			_ = d.AgentError.WithTags(tags).Add(1)
		}
	}
}

func (d *NextServerMetricSet) ReportSessionMetrics() func(method string, isError bool, errorReason, username string) {
	start := time.Now()
	return func(method string, isError bool, errorReason, username string) {
		tags := &NextServerSessionTag{
			Method:   method,
			Username: username,
		}
		// rate
		_ = d.SessionRate.WithTags(tags).Add(1)
		// latency
		_ = d.SessionTimer.WithTags(tags).Observe(float64(time.Since(start).Milliseconds()))
		// err
		if isError {
			tags.ErrorReason = errorReason
			_ = d.SessionError.WithTags(tags).Add(1)
		}
	}
}

func (d *NextServerMetricSet) ReportArtifactMetrics() func(method string, source string, isError bool, errorReason string) {
	start := time.Now()

	return func(method string, source string, isError bool, errorReason string) {
		tags := &NextServerArtifactTag{
			Method: method,
			Source: source,
		}
		// rate
		_ = d.ArtifactRate.WithTags(tags).Add(1)
		// latency
		_ = d.ArtifactTimer.WithTags(tags).Observe(float64(time.Since(start).Milliseconds()))
		// err
		if isError {
			tags.ErrorReason = errorReason
			_ = d.ArtifactError.WithTags(tags).Add(1)
		}
	}
}

func (d *NextServerMetricSet) ReportModuleMetrics(module NextServerModule, method string) func(isError bool, errorReason string) {
	start := time.Now()

	return func(isError bool, errorReason string) {
		tags := &NextServerModuleTag{
			Module: module,
			Method: method,
		}
		// rate
		_ = d.ModuleRate.WithTags(tags).Add(1)
		// latency
		_ = d.ModuleTimer.WithTags(tags).Observe(float64(time.Since(start).Milliseconds()))
		// err
		if isError {
			tags.ErrorReason = errorReason
			_ = d.ModuleError.WithTags(tags).Add(1)
		}
	}
}

func (d *NextServerMetricSet) ReportEventMetrics() func(method string, isError bool) {
	start := time.Now()

	return func(method string, isError bool) {
		tags := &NextServerEventProcessTag{
			Method: method,
		}
		// rate
		_ = d.EventRate.WithTags(tags).Add(1)
		// latency
		_ = d.EventTimer.WithTags(tags).Observe(float64(time.Since(start).Milliseconds()))
		// err
		if isError {
			_ = d.EventError.WithTags(tags).Add(1)
		}
	}
}

func (d *NextServerMetricSet) ReportScenarioDetectionMetrics() func(operation string, isError bool, errorReason string) {
	start := time.Now()
	return func(operation string, isError bool, errorReason string) {
		tags := &NextServerScenarioDetectionTag{
			Operation: operation,
		}
		// rate
		_ = d.ScenarioDetectionRate.WithTags(tags).Add(1)
		// latency
		_ = d.ScenarioDetectionTimer.WithTags(tags).Observe(float64(time.Since(start).Milliseconds()))
		// err
		if isError {
			tags.ErrorReason = errorReason
			_ = d.ScenarioDetectionError.WithTags(tags).Add(1)
		}
	}
}
