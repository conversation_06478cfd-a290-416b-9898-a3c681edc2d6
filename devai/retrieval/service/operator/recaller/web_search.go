package recaller

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	asynciter "github.com/sourcegraph/conc/iter"

	bytedtrace "code.byted.org/bytedtrace/interface-go"
	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/devai/common/constant"
	"code.byted.org/devgpt/kiwis/devai/retrieval/entity"
	"code.byted.org/devgpt/kiwis/port/antidirt"
	"code.byted.org/devgpt/kiwis/search/dal/crawler"
	"code.byted.org/devgpt/kiwis/search/dal/searcher/engine"
	"code.byted.org/devgpt/kiwis/search/tools"
)

type WebSearchOperator struct {
	MetaData WebSearchMetaData
	crawler  crawler.Crawler
	antidirt antidirt.Antidirt
	engines  []engine.Engine
}

type WebSearchMetaData struct {
	Query   string
	TopK    int32
	IsQuick bool
}

func NewWebSearchMetaData(query string, topK int32, isQuick bool) WebSearchMetaData {
	return WebSearchMetaData{
		Query:   query,
		TopK:    topK,
		IsQuick: isQuick,
	}
}

func NewWebSearchOperator(webSearchMetaData WebSearchMetaData, crawler crawler.Crawler, antidirt antidirt.Antidirt, engines []engine.Engine) *WebSearchOperator {
	return &WebSearchOperator{
		MetaData: webSearchMetaData,
		crawler:  crawler,
		antidirt: antidirt,
		engines:  engines,
	}
}

func (o *WebSearchOperator) Run(ctx context.Context) (*entity.OperatorData, error) {
	references := o.GetReferences(ctx, o.MetaData.Query, int(o.MetaData.TopK), o.MetaData.IsQuick)
	if len(references) == 0 {
		logs.V1.CtxWarn(ctx, "references is empty")
	}
	segments := make([]*entity.Segment, 0, len(references))
	for _, reference := range references {
		link := reference.URI
		icon := reference.Icon
		segments = append(segments, &entity.Segment{
			SegmentID:  strconv.FormatInt(time.Now().UnixNano(), 10),
			Type:       constant.SegmentTypeSearch,
			Content:    reference.CombineContent,
			SourceType: constant.SourceTypeWebSearch,
			SearchSegment: &entity.SearchSegment{
				Title:      reference.Title,
				Content:    reference.Content,
				Snippet:    reference.Snippet,
				Link:       &link,
				SourceType: reference.Source,
				Icon:       icon,
			},
		})
	}
	return &entity.OperatorData{
		Type: entity.OperatorRecaller,
		RecallData: &entity.RecallData{
			Provider: entity.OperatorProviderWebSearch,
			Segments: segments,
		},
	}, nil
}

type Reference struct {
	Source         string
	Title          string
	URI            string
	Icon           *string
	CombineContent string // For compatibility, it combines snippet and content.
	Snippet        string
	Content        string
}

func (o *WebSearchOperator) GetReferences(ctx context.Context, input string, topK int, isQuick bool) (refs []*Reference) {
	type searchPair struct {
		Query  string
		Engine engine.Engine
		Limit  int
		Offset int
	}

	var pairs []searchPair

	logs.CtxInfo(ctx, "[GetReferences] engines len is %v", len(o.engines))

	for _, e := range o.engines {
		pairs = append(pairs, searchPair{
			Query:  input,
			Engine: e,
			Limit:  topK,
			Offset: 0,
		})
	}

	mapperSearch := asynciter.Mapper[searchPair, []engine.Result]{MaxGoroutines: len(pairs)}

	collections := mapperSearch.Map(pairs, func(p *searchPair) (items []engine.Result) {
		_engine := p.Engine
		span, ct := bytedtrace.StartCustomSpan(
			ctx, "search",
			fmt.Sprintf("%s-%s", string(_engine.Source()), lo.Substring(p.Query, 0, 15)),
		)

		items, err := _engine.Search(ct, p.Query)
		if err != nil {
			logs.CtxError(ct, "search %s query=[%s] failed: %v", _engine.Source(), p.Query, err)
			return
		}

		logs.CtxInfo(ctx, "[GetReferences] search %s query=[%s] results=%v", _engine.Source(), p.Query, items)

		// 按照指定的 offset 调整 result 的顺序，确保 deduplicate 后的顺序符合预期
		items = lo.Map(items, func(item engine.Result, _ int) engine.Result {
			item.Index += p.Offset
			return item
		})

		span.Finish()
		return
	})

	collections = append(
		[][]engine.Result{lo.Map([]string{}, func(link string, _ int) engine.Result { return engine.NewResult(link) })},
		collections...,
	)
	items := lo.Flatten(collections)

	items = lo.Slice(
		lo.Filter(items, func(item engine.Result, _ int) bool {
			return strings.TrimSpace(item.URL) != ""
		}),
		0, topK,
	)

	logs.CtxInfo(ctx, "[GetReferences] items length is %v", len(items))

	logs.CtxInfo(ctx, "[GetReferences] items are %v", items)

	engineResultToRef := func(item *engine.Result, _ int) *Reference {
		return &Reference{
			Source:         string(item.Source),
			Title:          item.Title,
			Icon:           lo.ToPtr(tools.GetFaviconProxyURL(tools.GoogleFaviconProxyURL, item.URL)),
			URI:            item.URL,
			CombineContent: fmt.Sprintf("%s\n%s", item.Snippet, item.Content),
			Snippet:        item.Snippet,
			Content:        item.Content,
		}
	}

	if isQuick {
		return lo.Map(items, func(item engine.Result, _ int) *Reference {
			return engineResultToRef(&item, 0)
		})
	}
	mapper := asynciter.Mapper[engine.Result, *engine.Result]{MaxGoroutines: len(items)}
	results := mapper.Map(items, func(item *engine.Result) *engine.Result {
		if strings.TrimSpace(item.CrawlURL) != "" {
			chunks, _ := o.crawler.CrawlAndSplit(ctx, item.CrawlURL)
			if len(chunks) > 0 {
				item.Content = strings.Join(chunks, "\n")
			} else {
				item.Content = item.Snippet
			}
			if strings.Replace(item.Content, "\n", "", -1) == "" {
				item.Content = item.Snippet
			}
		}
		if item.Content == "" {
			item.Content = item.Snippet
		}
		return item
	})
	results = o.filterSensitive(ctx, results)
	logs.CtxInfo(ctx, "[GetReferences] results length is %v", len(results))
	return lo.Map(results, engineResultToRef)
}

func (o *WebSearchOperator) filterSensitive(ctx context.Context, items []*engine.Result) (filteredItems []*engine.Result) {
	mapper := asynciter.Mapper[*engine.Result, bool]{MaxGoroutines: lo.Min([]int{len(items), 10})}

	isFilters := mapper.Map(items, func(item **engine.Result) bool {
		content := (*item).String()
		return !o.IsSensitive(ctx, content)
	})

	for i, ok := range isFilters {
		if ok {
			filteredItems = append(filteredItems, items[i])
		}
	}
	return
}

func (o *WebSearchOperator) IsSensitive(ctx context.Context, content string) bool {
	// TODO: content might be very large, split into several pieces.
	isMatch, err := o.antidirt.MatchRules(ctx, content, "")
	if err != nil {
		logs.CtxWarn(ctx, "antidirt string=[%s] error, reason=%s", content, err)
		return true
	}
	if isMatch {
		logs.CtxInfo(ctx, "antidirt match and filter string=[%s]", content)
		return true
	}
	return isMatch
}
