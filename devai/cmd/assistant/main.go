package main

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/copilotstack/common/auth"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/devai/account"
	"code.byted.org/devgpt/kiwis/devai/app"
	"code.byted.org/devgpt/kiwis/devai/chat"
	chatdal "code.byted.org/devgpt/kiwis/devai/chat/dal"
	"code.byted.org/devgpt/kiwis/devai/chat/dal/po"
	cmdcommon "code.byted.org/devgpt/kiwis/devai/cmd/common"
	"code.byted.org/devgpt/kiwis/devai/journal"
	journaldalimpl "code.byted.org/devgpt/kiwis/devai/journal/dal/impl"
	po2 "code.byted.org/devgpt/kiwis/devai/journal/dal/po"
	"code.byted.org/devgpt/kiwis/devai/share"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/antidirt"
	"code.byted.org/devgpt/kiwis/port/kani"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/larkrank"
	"code.byted.org/devgpt/kiwis/port/llmops"
	"code.byted.org/devgpt/kiwis/port/nextcode"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/textembedding"
)

func main() {
	opts := fx.Options(
		fx.Invoke(metrics.InitCopilotMetric),
		fx.Invoke(metrics.InitDevAIAssistantMetric),
		tccModule,
		dbModule,
		configModule,
		redis.Module,
		cmdcommon.IDGenModule,
		kitexModule,
		auth.Module,
		chat.Module,
		larkrank.Module,
		antidirt.Module,
		llm.Module,
		journal.Module,
		share.Module,
		account.Module,
		app.Module,
		textembedding.Module,
		llmops.Module,
		nextcode.Module2,
		fx.Invoke(func(dal *chatdal.DAO, journaldal *journaldalimpl.PromptCompletionDAOImpl) error {

			var (
				message []*po.MessagePO
				cursor  = int64(0)
				err     error
			)
			for {
				message, cursor, err = dal.ScanMessage(context.Background(), cursor, 100)
				if err != nil {
					return err
				}
				if len(message) == 0 {
					break
				}
				fmt.Printf("message: %v\n", message[0].ID)

				for _, messagePO := range message {
					prompts, err := journaldal.GetPromptCompletionLogBySessionID(context.Background(), messagePO.SessionID)
					if err != nil {
						return err
					}
					if len(prompts) > 1 {

						prompts = lo.Filter(prompts, func(item *po2.PromptCompletionPO, index int) bool {
							if item.ModelName == "deepseek-7b" {
								return false
							}
							return item.ContentRaw != nil && !strings.Contains(*item.ContentRaw, "is_summary_or_translate")
						})
					}
					if len(prompts) == 1 {
						err := dal.UpdateMessageModel(context.Background(), messagePO.ID, prompts[0].ModelName)
						if err != nil {
							return err
						}
					}
					if len(prompts) > 1 {
						continue
						//return errors.New("more than one prompts")
					}

				}
			}
			return nil

		}),
		fx.Provide(func(tccConfig *config.DevAIAssistantTCCConfig) (lark.Client, error) {
			if tccConfig == nil || tccConfig.LarkAppConfig == nil {
				return nil, errors.New("lark account config is nil")
			}
			return lark.NewClient(tccConfig.LarkAppConfig.GetPointer().AppID, tccConfig.LarkAppConfig.GetPointer().AppSecret, "", tccConfig.LarkAppConfig.GetPointer().RedirectURI), nil
		}),

		fx.Provide(func() *tcc.GenericConfig[config.AILimitQueueTCCConfig] {
			// FIXME: remove it.
			return nil
		}),
		fx.Provide(func(conf config.KaniConfig) kani.Client {
			return kani.NewHTTPClient(conf.BaseURL)
		}),

		fx.Provide(func(conf config.KaniConfig) kani.ProtegoClient {
			return kani.NewProtegoClient(conf.ProtegoBaseURL, conf.Namespace, conf.WorkflowBaseURL)
		}),
	)
	fx.New(opts).Run()
}
