// Code generated by MockGen. DO NOT EDIT.
// Source: client.go
//
// Generated by this command:
//
//	mockgen -destination=mock/rocketmq_gen.go -package rocketmqmock -source client.go
//

// Package rocketmqmock is a generated GoMock package.
package rocketmqmock

import (
	context "context"
	reflect "reflect"
	time "time"

	deduplicatorlib "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/deduplicator"
	proto "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/proto"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockClient) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockClient)(nil).Close))
}

// RegisterDeduplicator mocks base method.
func (m *MockClient) RegisterDeduplicator(deduplicator deduplicatorlib.IDeduplicator) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterDeduplicator", deduplicator)
}

// RegisterDeduplicator indicates an expected call of RegisterDeduplicator.
func (mr *MockClientMockRecorder) RegisterDeduplicator(deduplicator any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterDeduplicator", reflect.TypeOf((*MockClient)(nil).RegisterDeduplicator), deduplicator)
}

// RegisterHandler mocks base method.
func (m *MockClient) RegisterHandler(handler func(context.Context, []byte) error) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterHandler", handler)
}

// RegisterHandler indicates an expected call of RegisterHandler.
func (mr *MockClientMockRecorder) RegisterHandler(handler any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterHandler", reflect.TypeOf((*MockClient)(nil).RegisterHandler), handler)
}

// RegisterHandlerV2 mocks base method.
func (m *MockClient) RegisterHandlerV2(handler func(context.Context, []byte, *proto.MessageExt) error) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterHandlerV2", handler)
}

// RegisterHandlerV2 indicates an expected call of RegisterHandlerV2.
func (mr *MockClientMockRecorder) RegisterHandlerV2(handler any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterHandlerV2", reflect.TypeOf((*MockClient)(nil).RegisterHandlerV2), handler)
}

// SendBatchDelayedMessage mocks base method.
func (m *MockClient) SendBatchDelayedMessage(ctx context.Context, messages [][]byte, delay time.Duration, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendBatchDelayedMessage", ctx, messages, delay, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendBatchDelayedMessage indicates an expected call of SendBatchDelayedMessage.
func (mr *MockClientMockRecorder) SendBatchDelayedMessage(ctx, messages, delay, tag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBatchDelayedMessage", reflect.TypeOf((*MockClient)(nil).SendBatchDelayedMessage), ctx, messages, delay, tag)
}

// SendBatchMessage mocks base method.
func (m *MockClient) SendBatchMessage(ctx context.Context, messages [][]byte, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendBatchMessage", ctx, messages, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendBatchMessage indicates an expected call of SendBatchMessage.
func (mr *MockClientMockRecorder) SendBatchMessage(ctx, messages, tag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBatchMessage", reflect.TypeOf((*MockClient)(nil).SendBatchMessage), ctx, messages, tag)
}

// SendBatchOrderlyMessage mocks base method.
func (m *MockClient) SendBatchOrderlyMessage(ctx context.Context, messages [][]byte, partitionKey, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendBatchOrderlyMessage", ctx, messages, partitionKey, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendBatchOrderlyMessage indicates an expected call of SendBatchOrderlyMessage.
func (mr *MockClientMockRecorder) SendBatchOrderlyMessage(ctx, messages, partitionKey, tag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBatchOrderlyMessage", reflect.TypeOf((*MockClient)(nil).SendBatchOrderlyMessage), ctx, messages, partitionKey, tag)
}

// SendDelayMessageAndGetID mocks base method.
func (m *MockClient) SendDelayMessageAndGetID(ctx context.Context, message []byte, delay time.Duration, tag string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendDelayMessageAndGetID", ctx, message, delay, tag)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendDelayMessageAndGetID indicates an expected call of SendDelayMessageAndGetID.
func (mr *MockClientMockRecorder) SendDelayMessageAndGetID(ctx, message, delay, tag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendDelayMessageAndGetID", reflect.TypeOf((*MockClient)(nil).SendDelayMessageAndGetID), ctx, message, delay, tag)
}

// SendDelayedMessage mocks base method.
func (m *MockClient) SendDelayedMessage(ctx context.Context, message []byte, delay time.Duration, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendDelayedMessage", ctx, message, delay, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendDelayedMessage indicates an expected call of SendDelayedMessage.
func (mr *MockClientMockRecorder) SendDelayedMessage(ctx, message, delay, tag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendDelayedMessage", reflect.TypeOf((*MockClient)(nil).SendDelayedMessage), ctx, message, delay, tag)
}

// SendMessage mocks base method.
func (m *MockClient) SendMessage(ctx context.Context, message []byte, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", ctx, message, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockClientMockRecorder) SendMessage(ctx, message, tag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockClient)(nil).SendMessage), ctx, message, tag)
}

// SendOrderlyMessage mocks base method.
func (m *MockClient) SendOrderlyMessage(ctx context.Context, message []byte, partitionKey, tag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendOrderlyMessage", ctx, message, partitionKey, tag)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendOrderlyMessage indicates an expected call of SendOrderlyMessage.
func (mr *MockClientMockRecorder) SendOrderlyMessage(ctx, message, partitionKey, tag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOrderlyMessage", reflect.TypeOf((*MockClient)(nil).SendOrderlyMessage), ctx, message, partitionKey, tag)
}

// StartConsumer mocks base method.
func (m *MockClient) StartConsumer() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartConsumer")
	ret0, _ := ret[0].(error)
	return ret0
}

// StartConsumer indicates an expected call of StartConsumer.
func (mr *MockClientMockRecorder) StartConsumer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartConsumer", reflect.TypeOf((*MockClient)(nil).StartConsumer))
}
