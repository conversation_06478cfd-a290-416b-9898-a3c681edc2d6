// Package rocketmq wraps consumer and producer api for rocketmq.
package rocketmq

import (
	"context"
	"time"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logid"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	rmqconfig "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/config"
	rmqconsumer "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/consumer"
	deduplicatorlib "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/deduplicator"
	rmqlog "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/log"
	rmqproducer "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/producer"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/proto"
	rmqtypes "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/types"

	"code.byted.org/devgpt/kiwis/lib/config"
)

// NoTag is a constant for no tag.
const NoTag = ""

type Client interface {
	SendMessage(ctx context.Context, message []byte, tag string) error
	SendBatchMessage(ctx context.Context, messages [][]byte, tag string) error
	SendBatchDelayedMessage(ctx context.Context, messages [][]byte, delay time.Duration, tag string) error
	SendDelayMessageAndGetID(ctx context.Context, message []byte, delay time.Duration, tag string) (string, error)
	SendDelayedMessage(ctx context.Context, message []byte, delay time.Duration, tag string) error
	SendOrderlyMessage(ctx context.Context, message []byte, partitionKey string, tag string) error
	SendBatchOrderlyMessage(ctx context.Context, messages [][]byte, partitionKey string, tag string) error
	RegisterHandler(handler func(ctx context.Context, message []byte) error)
	RegisterHandlerV2(handler func(ctx context.Context, message []byte, ext *proto.MessageExt) error)
	RegisterDeduplicator(deduplicator deduplicatorlib.IDeduplicator)
	StartConsumer() error
	Close() error
}

// client is a RocketMQ client.
type client struct {
	Options
	producer     rmqproducer.Producer
	consumer     rmqconsumer.Consumer
	handler      func(ctx context.Context, message []byte) error
	handlerV2    func(ctx context.Context, message []byte, ext *proto.MessageExt) error
	topic        string
	env          string // add env tag to all producer messages, since not all ctx are created by kitex/hertz and has "K_ENV"; consumer automatically read from environ
	deduplicator deduplicatorlib.IDeduplicator
}

// NewClient returns a rocketmq client which provides a more friendly interfaces.
func NewClient(conf *config.RocketMQConfig, opts ...Option) (Client, error) {
	rmqlog.InitRocketMQLog(rmqlog.NewDefaultRocketMQLogConfig())
	consumerCfg := rmqconfig.NewDefaultConsumerConfig(conf.ConsumerGroup, conf.Topic, conf.Cluster)
	consumerCfg.LogOutputs = nil
	if len(conf.ConsumeTags) != 0 {
		consumerCfg.SubExpr = conf.ConsumeTags
	}
	if conf.WorkerCount > 0 {
		consumerCfg.WorkerNum = conf.WorkerCount
	}
	if conf.SwimlaneV2 != nil {
		consumerCfg.SwimlaneV2 = *conf.SwimlaneV2
	}
	consumerCfg.CustomizedEnv = conf.FeatureEnv
	if conf.ConsumeFromWhere != nil {
		consumerCfg.ConsumeFromWhere = rmqconfig.ConsumeFromWhere(*conf.ConsumeFromWhere)
	}
	if conf.ConsumeTimeoutMillis != nil {
		consumerCfg.ConsumeTimeoutMillis = *conf.ConsumeTimeoutMillis
	}
	if conf.DisablePPE != nil {
		if *conf.DisablePPE {
			consumerCfg.EnablePPE = false
		}
	}
	if conf.Broadcast != nil {
		if *conf.Broadcast {
			consumerCfg.FanoutMode = rmqconfig.FanoutMode_BROADCAST
		}
	}
	if conf.Orderly != nil {
		consumerCfg.Orderly = *conf.Orderly
	}
	if conf.EnableRateLimit != nil {
		consumerCfg.EnableRateLimit = *conf.EnableRateLimit
	}
	if conf.RateLimitQPS != nil {
		consumerCfg.RateLimitQPS = *conf.RateLimitQPS
	}
	if conf.RetryTimes != nil {
		consumerCfg.RetryTimes = *conf.RetryTimes
	}
	consumer, err := rmqconsumer.NewConsumer(consumerCfg)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to init rocketmq consumer")
	}
	producerCfg := rmqconfig.NewProducerConfig(env.PSM(), conf.Cluster)
	producerCfg.LogOutputs = nil
	producer, err := rmqproducer.NewProducer(producerCfg)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to init rocketmq producer")
	}
	options := Options{}
	for _, opt := range opts {
		opt(&options)
	}
	return &client{
		Options:  options,
		producer: producer,
		consumer: consumer,
		topic:    conf.Topic,
		env:      conf.FeatureEnv,
	}, nil
}

func (c *client) SendMessage(ctx context.Context, message []byte, tag string) error {
	rmqMsg := rmqtypes.NewMessage(c.topic, message)
	if len(tag) != 0 {
		rmqMsg = rmqMsg.WithTag(tag)
	}
	if len(c.env) != 0 {
		ctx = context.WithValue(ctx, "K_ENV", c.env)
	}
	result, err := c.producer.Send(ctx, rmqMsg, rmqproducer.WithContext(true))
	if err != nil {
		return err
	}
	log.V1.CtxInfo(ctx, "send message with msg id: %s", result.MsgId)
	return err
}

func (c *client) SendBatchMessage(ctx context.Context, messages [][]byte, tag string) error {
	rmqMsgs := make([]*rmqtypes.Message, 0, len(messages))
	for _, message := range messages {
		rmqMsg := rmqtypes.NewMessage(c.topic, message)
		if len(tag) != 0 {
			rmqMsg = rmqMsg.WithTag(tag)
		}
		rmqMsgs = append(rmqMsgs, rmqMsg)
	}
	if len(c.env) != 0 {
		ctx = context.WithValue(ctx, "K_ENV", c.env)
	}
	result, err := c.producer.SendBatch(ctx, rmqMsgs, rmqproducer.WithContext(true))
	if err != nil {
		return err
	}
	log.V1.CtxInfo(ctx, "send batch message with msg id: %s", result.MsgId)
	return err
}

// SendBatchDelayedMessage sends batch of delay messages with a time duration from 1s to 7 days(required by RMQ service).
func (c *client) SendBatchDelayedMessage(ctx context.Context, messages [][]byte, delay time.Duration, tag string) error {
	rmqMsgs := make([]*rmqtypes.Message, 0, len(messages))
	for _, message := range messages {
		rmqMsg := rmqtypes.NewDeferMessage(c.topic, delay, message)
		if len(tag) != 0 {
			rmqMsg = rmqMsg.WithTag(tag)
		}
		rmqMsgs = append(rmqMsgs, rmqMsg)
	}
	if len(c.env) != 0 {
		ctx = context.WithValue(ctx, "K_ENV", c.env)
	}
	result, err := c.producer.SendBatch(ctx, rmqMsgs, rmqproducer.WithContext(true))
	if err != nil {
		return err
	}
	log.V1.CtxInfo(ctx, "send batch delayed message with msg id: %s", result.MsgId)
	return err
}

// SendDelayedMessage sends a delay message with a time duration from 1s to 7 days(required by RMQ service).
func (c *client) SendDelayedMessage(ctx context.Context, message []byte, delay time.Duration, tag string) error {
	rmqMsg := rmqtypes.NewDeferMessage(c.topic, delay, message)
	if len(tag) != 0 {
		rmqMsg = rmqMsg.WithTag(tag)
	}
	if len(c.env) != 0 {
		ctx = context.WithValue(ctx, "K_ENV", c.env)
	}
	result, err := c.producer.Send(ctx, rmqMsg, rmqproducer.WithContext(true))
	if err != nil {
		return err
	}
	log.V1.CtxInfo(ctx, "send message with msg id: %s", result.MsgId)
	return nil
}

func (c *client) SendDelayMessageAndGetID(ctx context.Context, message []byte, delay time.Duration, tag string) (string, error) {
	rmqMsg := rmqtypes.NewDeferMessage(c.topic, delay, message)
	if len(tag) != 0 {
		rmqMsg = rmqMsg.WithTag(tag)
	}
	if len(c.env) != 0 {
		ctx = context.WithValue(ctx, "K_ENV", c.env)
	}
	result, err := c.producer.Send(ctx, rmqMsg, rmqproducer.WithContext(true))
	if err != nil {
		return "", err
	}
	log.V1.CtxInfo(ctx, "send message with msg id: %s", result.MsgId)
	log.V1.CtxInfo(ctx, "get result: %v", result)
	return result.MsgId, nil
}

func (c *client) SendOrderlyMessage(ctx context.Context, message []byte, partitionKey string, tag string) error {
	rmqMsg := rmqtypes.NewOrderlyMessage(c.topic, partitionKey, message)
	if len(tag) != 0 {
		rmqMsg = rmqMsg.WithTag(tag)
	}
	if len(c.env) != 0 {
		ctx = context.WithValue(ctx, "K_ENV", c.env)
	}
	result, err := c.producer.Send(ctx, rmqMsg, rmqproducer.WithContext(true))
	if err != nil {
		return err
	}
	log.V1.CtxInfo(ctx, "send orderly message with msg id: %s", result.MsgId)
	return nil
}

func (c *client) SendBatchOrderlyMessage(ctx context.Context, messages [][]byte, partitionKey string, tag string) error {
	rmqMsgs := make([]*rmqtypes.Message, 0, len(messages))
	for _, message := range messages {
		rmqMsg := rmqtypes.NewOrderlyMessage(c.topic, partitionKey, message)
		if len(tag) != 0 {
			rmqMsg = rmqMsg.WithTag(tag)
		}
		rmqMsgs = append(rmqMsgs, rmqMsg)
	}
	if len(c.env) != 0 {
		ctx = context.WithValue(ctx, "K_ENV", c.env)
	}
	result, err := c.producer.SendBatch(ctx, rmqMsgs, rmqproducer.WithContext(true))
	if err != nil {
		return err
	}
	log.V1.CtxInfo(ctx, "send batch orderly message with msg id: %s", result.MsgId)
	return nil
}

// RegisterHandler registers mq handler for consumer, it must be called before StartConsumerHandler.
func (c *client) RegisterHandler(handler func(ctx context.Context, message []byte) error) {
	c.handler = handler
}

// RegisterHandlerV2 registers mq handler for consumer, it must be called before StartConsumerHandler.
func (c *client) RegisterHandlerV2(handler func(ctx context.Context, message []byte, ext *proto.MessageExt) error) {
	for i := len(c.Options.MWs) - 1; i >= 0; i-- {
		mw := c.Options.MWs[i]
		handler = mw(handler)
	}
	c.handlerV2 = handler
}

func (c *client) RegisterDeduplicator(deduplicator deduplicatorlib.IDeduplicator) {
	c.deduplicator = deduplicator
}

// StartConsumer starts asynchronous message consuming.
func (c *client) StartConsumer() error {
	if c.handler == nil && c.handlerV2 == nil {
		return errors.New("no handler is registered")
	}
	c.consumer.RegisterHandler(c)
	if c.deduplicator != nil {
		c.consumer.RegisterDeduplicator(c.deduplicator)
	}
	if err := c.consumer.Start(); err != nil {
		log.V1.Error("failed to start consumer, topic: %s, error: %v", c.topic, err)
	}
	return nil
}

func (c *client) HandleMessage(ctx context.Context, rmqMsg *rmqtypes.MessageExt) error {
	ctx = logs.CtxAddKVs(ctx, "msg_id", rmqMsg.MsgId)
	logID, exists := ctxvalues.LogID(ctx)
	if !exists || len(logID) == 0 {
		// No log id in context, generate a new one.
		// It might be a bug or something wrong when configurate consumer.
		ctx = ctxvalues.SetLogID(ctx, logid.GenLogID())
		log.V1.CtxInfo(ctx, "generate and set log id")
	}
	log.V1.CtxInfo(ctx, "start to handle rocketmq event: %s", rmqMsg.MsgId)
	if c.handler == nil && c.handlerV2 == nil {
		return errors.New("failed to handle message, no handler registered")
	}
	var err error
	if c.handler != nil {
		err = c.handler(ctx, rmqMsg.GetMsg().GetMsgBody())
	} else if c.handlerV2 != nil {
		err = c.handlerV2(ctx, rmqMsg.GetMsg().GetMsgBody(), &rmqMsg.MessageExt)
	}
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to handle rmq event: %+v", err)
	}
	return err
}

// Close stops mq consumer and producer.
func (c *client) Close() error {
	multiError := &multierror.Error{}
	if c.consumer != nil {
		if err := c.consumer.Close(); err != nil {
			multiError = multierror.Append(multiError, err)
		}
	}
	if c.producer != nil {
		if err := c.producer.Close(); err != nil {
			multiError = multierror.Append(multiError, err)
		}
	}
	return multiError.ErrorOrNil()
}
