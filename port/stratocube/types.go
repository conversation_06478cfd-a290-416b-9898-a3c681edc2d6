package stratocube

// Cube is a Kubernetes `Pod` equivalent in Stratocube
type CubeSpec struct {
	OS             CubeOS            `json:"os"`
	WildcardDomain WildcardDomain    `json:"wildcard_domain"`
	Containers     []ContainerSpec   `json:"containers"`
	Volumes        Volumes           `json:"volumes"`
	NodeSelector   map[string]string `json:"node_selector,omitempty"`
	Annotations    map[string]string `json:"annotations"`
	Labels         map[string]string `json:"labels"`
	CtrlInfo       CtrlInfo          `json:"ctrl_info,omitempty"`
}

type WildcardDomain string
type CubeOS string

const (
	CubeOSLinux          CubeOS         = "linux"
	WildcardDomainPPE    WildcardDomain = "strato-cloudbuild-ppe.byted.org"
	WildcardDomainOnline WildcardDomain = "cube-kubestrato-online.byted.org"
)

type ContainerSpec struct {
	Name            string             `json:"name"`
	Image           string             `json:"image"`
	Command         []string           `json:"command"`
	Ports           []PortItem         `json:"ports,omitempty"`
	Environ         []EnvironItem      `json:"env,omitempty"`
	CPURequirements ResourceQuantity   `json:"cpu_requirements"`
	MemRequirements ResourceQuantity   `json:"mem_requirements"`
	VolumeMounts    []VolumeMountsItem `json:"volume_mounts,omitempty"`
}

type ACLEntry struct {
	Path string `json:"path"`
}

type CtrlInfo struct {
	EnableBytedInfra    bool       `json:"enable_byted_infra"`
	StreamLogPSM        string     `json:"stream_log_psm"`
	EnableFileServer    bool       `json:"enable_file_server"`
	EnableBrowserServer bool       `json:"enable_browser_server"`
	EnableCodeServer    bool       `json:"enable_code_server"`
	CodeServerImage     string     `json:"code_server_image"`
	ACLEntries          []ACLEntry `json:"acl_entries,omitempty"`
	ACLIsolationMode    string     `json:"acl_isolation_mode,omitempty"`
}

type ResourceQuantity struct {
	Limits   string `json:"limits"`
	Requests string `json:"requests"`
}

type EnvironItem struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type HostPathListItem struct {
	Name string `json:"name"`
	Path string `json:"path"`
}

type PortItem struct {
	Name        string       `json:"name"` // unique name for port
	Var         int          `json:"var"`  // port number
	Protocol    PortProtocol `json:"protocol"`
	NeedIngress bool         `json:"need_ingress"`
	ServicePort int          `json:"service_port"` // port exposed in ingress, all cubes must have the same ingress port
}

type PortProtocol string

const (
	PortProtocolTCP  PortProtocol = "tcp"
	PortProtocolHTTP PortProtocol = "http"
	PortProtocolGRPC PortProtocol = "grpc"
)

type PVCListItem struct {
	Name             string            `json:"name"`
	ExistedPVCName   string            `json:"existed_pvc_name,omitempty"`
	StorageRequests  string            `json:"storage_requests"`
	StorageClassName string            `json:"storage_class_name"`
	Annotations      map[string]string `json:"annotations,omitempty"`
	Labels           map[string]string `json:"labels,omitempty"`
}

type VolumeMountsItem struct {
	Name      string `json:"name"`
	MountPath string `json:"mount_path"`
	SubPath   string `json:"sub_path"`
	ReadOnly  bool   `json:"read_only,omitempty"`
}

type Volumes struct {
	HostPathList []HostPathListItem `json:"host_path_list"`
	PVCList      []PVCListItem      `json:"pvc_list"`
}

type CubeState struct {
	CubeID    string      `json:"cube_id"`
	Status    CubeStatus  `json:"status"`
	Endpoints []Endpoints `json:"endpoints"`
	NodeName  string      `json:"node_name"`
	UserName  string      `json:"user_name"`
}

type CubeWildcardDomain struct {
	CubeID         string `json:"cube_id"`
	WildcardDomain string `json:"wildcard_domain"`
}

type CubeStatus string

const (
	CubeStatusCreating   CubeStatus = "Creating"
	CubeStatusRunning    CubeStatus = "Running"
	CubeStatusSuspending CubeStatus = "Suspending"
	CubeStatusSuspend    CubeStatus = "Suspend"
	CubeStatusResuming   CubeStatus = "Resuming"
	CubeStatusExit       CubeStatus = "Exit"
	// CubeStatusDeleted 并非是Cube 原生的状态（原生会报错10020，映射为Deleted），是用于标记 Cube 已经被删除
	CubeStatusDeleted CubeStatus = "Deleted"
)

type Endpoints struct {
	ContainerName string `json:"container_name"`
	TCPProxy      string `json:"tcp_proxy"`
	URL           string `json:"url"`
}

type CubeExecStatus struct {
	Exited   bool `json:"exited"`
	ExitCode int  `json:"exit_code"`
}
