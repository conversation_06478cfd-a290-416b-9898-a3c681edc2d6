package openai

import (
	"encoding/json"
	"fmt"

	"github.com/samber/lo"
)

// ChatCompletionsRequest is the request model that uses OpenAI's protocol but adds other fields.
// 尽可能兼容 OpenAI 协议的同时，支持 prompt cache、reasoning 过程等非 OpenAI 官方协议的字段。
type ChatCompletionsRequest struct {
	// openai.ChatCompletionRequest
	Model string `json:"model"`
	// MaxTokens The maximum number of tokens that can be generated in the chat completion.
	// This value can be used to control costs for text generated via API.
	// This value is now deprecated in favor of max_completion_tokens, and is not compatible with o1 series models.
	// refs: https://platform.openai.com/docs/api-reference/chat/create#chat-create-max_tokens
	MaxTokens int `json:"max_tokens,omitempty"`
	// MaxCompletionsTokens An upper bound for the number of tokens that can be generated for a completion,
	// including visible output tokens and reasoning tokens https://platform.openai.com/docs/guides/reasoning
	MaxCompletionTokens int                           `json:"max_completion_tokens,omitempty"`
	Temperature         float32                       `json:"temperature,omitempty"`
	TopP                float32                       `json:"top_p,omitempty"`
	N                   int                           `json:"n,omitempty"`
	Stream              bool                          `json:"stream,omitempty"`
	Stop                []string                      `json:"stop,omitempty"`
	PresencePenalty     float32                       `json:"presence_penalty,omitempty"`
	ResponseFormat      *ChatCompletionResponseFormat `json:"response_format,omitempty"`
	Seed                *int                          `json:"seed,omitempty"`
	FrequencyPenalty    float32                       `json:"frequency_penalty,omitempty"`
	// LogitBias is must be a token id string (specified by their token ID in the tokenizer), not a word string.
	// incorrect: `"logit_bias":{"You": 6}`, correct: `"logit_bias":{"1639": 6}`
	// refs: https://platform.openai.com/docs/api-reference/chat/create#chat/create-logit_bias
	LogitBias map[string]int `json:"logit_bias,omitempty"`
	// LogProbs indicates whether to return log probabilities of the output tokens or not.
	// If true, returns the log probabilities of each output token returned in the content of message.
	// This option is currently not available on the gpt-4-vision-preview model.
	LogProbs bool `json:"logprobs,omitempty"`
	// TopLogProbs is an integer between 0 and 5 specifying the number of most likely tokens to return at each
	// token position, each with an associated log probability.
	// logprobs must be set to true if this parameter is used.
	TopLogProbs int    `json:"top_logprobs,omitempty"`
	User        string `json:"user,omitempty"`
	Tools       []Tool `json:"tools,omitempty"`
	// This can be either a string or an ToolChoice object.
	ToolChoice any `json:"tool_choice,omitempty"`
	// Options for streaming response. Only set this when you set stream: true.
	StreamOptions *StreamOptions `json:"stream_options,omitempty"`
	// Disable the default behavior of parallel tool calls by setting it: false.
	ParallelToolCalls *bool `json:"parallel_tool_calls,omitempty"`

	Messages []ChatCompletionMessage `json:"messages"`
	// Thinking options.
	Thinking *ThinkingOption `json:"thinking,omitempty"`

	// Text output options, currently only GPT 5 Responses API supports this option.
	Text *TextOption `json:"text,omitempty"`

	// ExtraOptions is the extra options passed to server or llm proxy.
	ExtraOptions map[string]any `json:"extra_options,omitempty"`
}

type TextOption struct {
	// Add it if needed.
	// Format any `json:"format,omitempty"`

	// Constrains the verbosity of the model's response. Lower values will result in more concise responses, while higher values will result in more verbose responses.
	// Currently supported values are low, medium, and high.
	Verbosity *string `json:"verbosity,omitempty"`
}

type StreamOptions struct {
	// If set, an additional chunk will be streamed before the data: [DONE] message.
	// The usage field on this chunk shows the token usage statistics for the entire request,
	// and the choices field will always be an empty array.
	// All other chunks will also include a usage field, but with a null value.
	IncludeUsage bool `json:"include_usage,omitempty"`
}

type ThinkingOption struct {
	// Claude | Gemini.
	Type         string `json:"type"`
	BudgetTokens int    `json:"budget_tokens"`

	// Gemini.
	IncludeThoughts bool `json:"include_thoughts"`
}

type ChatCompletionResponseFormat struct {
	Type       string                                  `json:"type,omitempty"`
	JSONSchema *ChatCompletionResponseFormatJSONSchema `json:"json_schema,omitempty"`
}

type ChatCompletionResponseFormatJSONSchema struct {
	Name        string         `json:"name"`
	Description string         `json:"description,omitempty"`
	Schema      json.Marshaler `json:"schema"`
	Strict      bool           `json:"strict"`
}

type Tool struct {
	// function
	Type         string              `json:"type"`
	Function     *FunctionDefinition `json:"function,omitempty"`
	CacheControl *CacheControl       `json:"cache_control,omitempty"`
}

type FunctionDefinition struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	Strict      bool   `json:"strict,omitempty"`
	// Parameters is an object describing the function.
	// You can pass json.RawMessage to describe the schema,
	// or you can pass in a struct which serializes to the proper JSON schema.
	// The jsonschema package is provided for convenience, but you should
	// consider another specialized library if you require more complex schemas.
	Parameters any `json:"parameters"`
}

type CacheControl struct {
	Type string `json:"type"`
}

type ChatMessagePart struct {
	Type         string               `json:"type,omitempty"`
	Text         string               `json:"text,omitempty"`
	ImageURL     *ChatMessageImageURL `json:"image_url,omitempty"`
	CacheControl *CacheControl        `json:"cache_control,omitempty"`
}

type ChatMessageImageURL struct {
	URL    string `json:"url,omitempty"`
	Detail string `json:"detail,omitempty"`
}

type ToolCall struct {
	// Index is not nil only in chat completion chunk object
	Index    *int         `json:"index,omitempty"`
	ID       string       `json:"id,omitempty"`
	Type     string       `json:"type"`
	Function FunctionCall `json:"function"`
}

type FunctionCall struct {
	Name string `json:"name,omitempty"`
	// call function with arguments in JSON format
	Arguments string `json:"arguments,omitempty"`
}

type ChatCompletionMessage struct {
	Role         string `json:"role"`
	Content      string `json:"content"`
	Refusal      string `json:"refusal,omitempty"`
	MultiContent []ChatMessagePart

	// For Role=assistant prompts this may be set to the tool calls generated by the model, such as function calls.
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`

	// For Role=tool prompts this should be set to the ID given in the assistant's prior request to call a tool.
	ToolCallID string `json:"tool_call_id,omitempty"`

	CacheControl *CacheControl `json:"cache_control,omitempty"`

	ReasoningContent string `json:"reasoning_content,omitempty"`
}

func (m ChatCompletionMessage) MarshalJSON() ([]byte, error) {
	if len(m.MultiContent) > 0 {
		msg := struct {
			Role             string            `json:"role"`
			Content          string            `json:"-"`
			Refusal          string            `json:"refusal,omitempty"`
			MultiContent     []ChatMessagePart `json:"content,omitempty"`
			ToolCalls        []ToolCall        `json:"tool_calls,omitempty"`
			ToolCallID       string            `json:"tool_call_id,omitempty"`
			CacheControl     *CacheControl     `json:"cache_control,omitempty"`
			ReasoningContent string            `json:"reasoning_content,omitempty"`
		}(m)
		return json.Marshal(msg)
	}

	msg := struct {
		Role             string            `json:"role"`
		Content          string            `json:"content,omitempty"`
		Refusal          string            `json:"refusal,omitempty"`
		MultiContent     []ChatMessagePart `json:"-"`
		ToolCalls        []ToolCall        `json:"tool_calls,omitempty"`
		ToolCallID       string            `json:"tool_call_id,omitempty"`
		CacheControl     *CacheControl     `json:"cache_control,omitempty"`
		ReasoningContent string            `json:"reasoning_content,omitempty"`
	}(m)
	return json.Marshal(msg)
}

func (m *ChatCompletionMessage) UnmarshalJSON(bs []byte) error {
	msg := struct {
		Role             string `json:"role"`
		Content          string `json:"content,omitempty"`
		Refusal          string `json:"refusal,omitempty"`
		MultiContent     []ChatMessagePart
		ToolCalls        []ToolCall    `json:"tool_calls,omitempty"`
		ToolCallID       string        `json:"tool_call_id,omitempty"`
		CacheControl     *CacheControl `json:"cache_control,omitempty"`
		ReasoningContent string        `json:"reasoning_content,omitempty"`
	}{}

	if err := json.Unmarshal(bs, &msg); err == nil {
		*m = ChatCompletionMessage(msg)
		return nil
	}
	multiMsg := struct {
		Role             string `json:"role"`
		Content          string
		Refusal          string            `json:"refusal,omitempty"`
		MultiContent     []ChatMessagePart `json:"content"`
		ToolCalls        []ToolCall        `json:"tool_calls,omitempty"`
		ToolCallID       string            `json:"tool_call_id,omitempty"`
		CacheControl     *CacheControl     `json:"cache_control,omitempty"`
		ReasoningContent string            `json:"reasoning_content,omitempty"`
	}{}
	if err := json.Unmarshal(bs, &multiMsg); err != nil {
		return err
	}
	*m = ChatCompletionMessage(multiMsg)
	return nil
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`

	// Claude.
	CacheCreationInputTokens int `json:"cache_creation_input_tokens,omitempty"`
	CacheReadInputTokens     int `json:"cache_read_input_tokens,omitempty"`

	// Gemini.
	ReasoningTokens int `json:"reasoning_tokens,omitempty"`
}

// ChatStreamResponse is the response model that uses OpenAI's protocol but adds other fields.
type ChatStreamResponse struct {
	ID                string             `json:"id"`
	Object            string             `json:"object,omitempty"`
	Created           int64              `json:"created"`
	Model             string             `json:"model"`
	Choices           []ChatStreamChoice `json:"choices"`
	SystemFingerprint string             `json:"system_fingerprint,omitempty"`
	Usage             *Usage             `json:"usage,omitempty"`
}

type ChatStreamChoice struct {
	Index        int                   `json:"index"`
	Delta        ChatStreamChoiceDelta `json:"delta"`
	FinishReason string                `json:"finish_reason"`
}

type ChatStreamChoiceDelta struct {
	Content          string     `json:"content,omitempty"`
	ReasoningContent string     `json:"reasoning_content,omitempty"`
	Role             string     `json:"role,omitempty"`
	ToolCalls        []ToolCall `json:"tool_calls,omitempty"`
}

type ChatResponse struct {
	ID                string                 `json:"id"`
	Object            string                 `json:"object"`
	Created           int64                  `json:"created"`
	Model             string                 `json:"model"`
	Choices           []ChatCompletionChoice `json:"choices"`
	Usage             Usage                  `json:"usage"`
	SystemFingerprint string                 `json:"system_fingerprint"`
}

type ChatCompletionChoice struct {
	Index   int                   `json:"index"`
	Message ChatCompletionMessage `json:"message"`
	// FinishReason
	// stop: API returned complete message,
	// or a message terminated by one of the stop sequences provided via the stop parameter
	// length: Incomplete model output due to max_tokens parameter or token limit
	// function_call: The model decided to call a function
	// content_filter: Omitted content due to a flag from our content filters
	// null: API response still in progress or incomplete
	FinishReason string `json:"finish_reason"`
}

// ErrorResponse is returned if errors occurs requesting OpenAI API.
// Ref: https://platform.openai.com/docs/guides/error-codes/api-errors
type OpenAIErrorResponseDetail struct {
	Code    any     `json:"code"`
	Message string  `json:"message"`
	Type    string  `json:"type"`
	Param   *string `json:"param"`
}

func (e *OpenAIErrorResponseDetail) Error() string {
	return fmt.Sprintf("API Error: %s, Type: %s, Code: %v, Param: %s", e.Message, e.Type, e.Code, lo.FromPtr(e.Param))
}
