package openai

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/stream"

	"code.byted.org/middleware/hertz/pkg/protocol"
	"github.com/samber/lo"
)

type Client struct {
	cli     *hertz.Client
	reqHook RequestHook
}

type RequestHook func(ctx context.Context, req *ChatCompletionsRequest, opt *hertz.ReqOption) context.Context

type ClientOption struct {
	Timeout time.Duration
	APIKey  string

	Endpoint string

	Headers     map[string]string
	RequestHook RequestHook
}

func NewClient(opt ClientOption) (*Client, error) {
	httpCli, err := hertz.NewClient(opt.Endpoint, hertz.NewHTTPClientOption{
		Timeout:              opt.Timeout,
		Debug:                false,
		DisableLog:           false,
		InjectZtiTokenHeader: "",
		Headers: lo.Assign(map[string]string{
			"Authorization": "Bearer " + opt.<PERSON>Key,
		}, opt.Headers),
		EnableStream:        true,
		UseSD:               false,
		MaxConnsPerHost:     0,
		MaxIdleConnDuration: 0,
	})
	if err != nil {
		return nil, err
	}
	cli := &Client{
		cli:     httpCli,
		reqHook: opt.RequestHook,
	}
	return cli, nil
}

func parseError(rawResp *protocol.Response, err error) error {
	if rawResp == nil {
		return err
	}
	var openaiErr struct {
		Error *OpenAIErrorResponseDetail `json:"error,omitempty"`
	}
	if parseErr := json.Unmarshal(rawResp.Body(), &openaiErr); parseErr != nil || openaiErr.Error == nil {
		return err
	}
	return errors.Join(openaiErr.Error, err)
}

func (c *Client) ChatCompletion(ctx context.Context, req *ChatCompletionsRequest) (*ChatResponse, error) {
	var resp ChatResponse
	reqOpt := hertz.ReqOption{
		ExpectedCode:       http.StatusOK,
		Body:               req,
		Result:             &resp,
		Notes:              "chat_completions",
		Headers:            map[string]string{},
		Cookies:            map[string]string{},
		SetConnectionClose: false,
		Timeout:            0,
		ResponseHeaders:    map[string]string{},
		SetTTEnvHeaders:    true,
	}
	if c.reqHook != nil {
		ctx = c.reqHook(ctx, req, &reqOpt)
	}
	rawResp, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/chat/completions", reqOpt)
	if err != nil {
		return nil, parseError(rawResp, err)
	}
	return &resp, nil
}

// ChatCompletionStream returns a stream of ChatOpenAIHTTPStreamResponse.
func (c *Client) ChatCompletionStream(ctx context.Context, req *ChatCompletionsRequest) (*stream.RecvChannel[*ChatStreamResponse], error) {
	sseSend, sseRecv := stream.NewChannel[*hertz.Event](10)
	reqOpt := hertz.ReqOption{
		ExpectedCode:       http.StatusOK,
		Body:               req,
		Result:             sseSend,
		Notes:              "chat_completions",
		Headers:            map[string]string{},
		Cookies:            map[string]string{},
		SetConnectionClose: false,
		Timeout:            0,
		ResponseHeaders:    map[string]string{},
		SetTTEnvHeaders:    true,
	}
	if c.reqHook != nil {
		ctx = c.reqHook(ctx, req, &reqOpt)
	}

	rawResp, err := c.cli.DoJSONReq(ctx, http.MethodPost, "/chat/completions", reqOpt)
	if err != nil {
		return nil, parseError(rawResp, err)
	}

	send, recv := stream.NewChannel[*ChatStreamResponse](10)
	go stream.ForwardWithFilterNil(ctx, sseRecv, send, true, func(e *hertz.Event) *ChatStreamResponse {
		if len(e.Data) <= 64 && bytes.Equal(bytes.TrimSpace(e.Data), []byte("[DONE]")) {
			return nil
		}

		// fmt.Println(string(e.Data))

		var chunk struct {
			*ChatStreamResponse
			Error *OpenAIErrorResponseDetail `json:"error,omitempty"`
		}
		err := json.Unmarshal(e.Data, &chunk)
		if err != nil {
			send.PublishError(err, false)
			return nil
		}
		if chunk.Error != nil {
			send.PublishError(chunk.Error, false)
			return nil
		}
		return chunk.ChatStreamResponse
	})

	return recv, nil
}

func NewEmptyChatStreamResponse() *ChatStreamResponse {
	return &ChatStreamResponse{
		ID:      "",
		Object:  "",
		Created: 0,
		Model:   "",
		Choices: []ChatStreamChoice{
			{
				Index: 0,
				Delta: ChatStreamChoiceDelta{
					Content:          "",
					ReasoningContent: "",
					Role:             "",
					ToolCalls:        nil,
				},
				FinishReason: "",
			},
		},
		SystemFingerprint: "",
		Usage: &Usage{
			CacheCreationInputTokens: 0,
			CacheReadInputTokens:     0,
			PromptTokens:             0,
			CompletionTokens:         0,
			TotalTokens:              0,
			ReasoningTokens:          0,
		},
	}
}
