package lark

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/jsonx"

	"github.com/google/uuid"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkbaike "github.com/larksuite/oapi-sdk-go/v3/service/baike/v1"
	larkbitable "github.com/larksuite/oapi-sdk-go/v3/service/bitable/v1"
	larkboard "github.com/larksuite/oapi-sdk-go/v3/service/board/v1"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larklingo "github.com/larksuite/oapi-sdk-go/v3/service/lingo/v1"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	larkwiki "github.com/larksuite/oapi-sdk-go/v3/service/wiki/v2"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"
	"golang.org/x/oauth2"

	"code.byted.org/devgpt/kiwis/lib/metrics"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/paas/cloud-sdk-go/jwt"
)

type client struct {
	larkCli               *lark.Client
	jwtGen                jwt.Generator // TODO(dingbo.2020): 用于签发字节云 Service JWT，然后访问 DevGPT 平台的接口获取用户的飞书 token，这个逻辑正式上线后需要去掉
	larkAPIBaseURL        string
	byteCloudServiceToken string
	larkID                string
	larkSecret            string
	oauthConfig           *oauth2.Config
}

const (
	rpcTimeout       = time.Second * 3
	larkAPIBaseURL   = "https://fsopen.bytedance.net"
	ttLogIDHeaderKey = "X-Tt-Logid"
)

// 飞书接口错误码：https://open.feishu.cn/document/server-docs/api-call-guide/generic-error-code
const (
	APIFileDeletedCode        = 95007
	APIPermissionDeniedCode   = 95009
	NodeNotFoundCode          = 131005
	SpacePermissionDeniedCode = 131006
	ResourceNotFoundCode      = 1770002
	ResourceDeletedCode       = 1770003
	APIForbiddenCode          = 1770032
	APIFrequencyLimitCode     = 99991400
	AuthTokenExpiredCode      = 99991677
	DocNoPermission           = 1069902
	FolderLockedCode          = 1770036
	NotExist                  = 91402
	// InternalErrorCode 飞书服务内部错误错误码 https://open.larkoffice.com/search?from=header&page=1&pageSize=10&q=1066001&topicFilter=
	InternalErrorCode                = 1066001
	ConcurrencyErrorCode             = 1066002
	DocxFieldValidationErrorCode     = ********
	DocUnauthorizedErrorCode         = ********
	CanNotOperateOuterGroupErrorCode = 232033
)
const (
	GetDocMetaCodeUnsupportedDocType = 970002
	GetDocMetaCodeNoPermission       = 970003
	GetDocMetaCodeResourceNotExist   = 970005
)

func NewClient(larkAppID, larkSecret, byteCloudServiceToken, redirectURI string, options ...lark.ClientOptionFunc) Client {
	var oauthEndpoint = oauth2.Endpoint{
		AuthURL:  "https://accounts.feishu.cn/open-apis/authen/v1/authorize",
		TokenURL: "https://open.larkoffice.com/open-apis/authen/v2/oauth/token",
	}
	var oauthConfig = &oauth2.Config{
		ClientID:     larkAppID,
		ClientSecret: larkSecret,
		RedirectURL:  redirectURI,
		Endpoint:     oauthEndpoint,
		Scopes:       []string{"offline_access"},
	}

	// Default options
	defaultOptions := []lark.ClientOptionFunc{
		lark.WithOpenBaseUrl(larkAPIBaseURL),
		lark.WithLogLevel(larkcore.LogLevelDebug),
		lark.WithReqTimeout(rpcTimeout),
		lark.WithEnableTokenCache(true),
		lark.WithHttpClient(http.DefaultClient),
	}

	// Combine default options with provided options
	// Custom options will override default options when there are conflicts
	allOptions := append(defaultOptions, options...)

	larkCli := lark.NewClient(larkAppID, larkSecret, allOptions...)

	return &client{
		larkCli:               larkCli,
		larkAPIBaseURL:        larkAPIBaseURL,
		jwtGen:                jwt.NewGenerator(jwt.WithRegion(jwt.RegionCN)),
		byteCloudServiceToken: byteCloudServiceToken,
		larkID:                larkAppID,
		larkSecret:            larkSecret,
		oauthConfig:           oauthConfig,
	}
}

// GetLarkDocBlock returns the markdown content of a lark document with doc type.
// https://open.larkoffice.com/document/server-docs/docs/docs/docs/content/get-document
func (c *client) GetLarkDocBlock(ctx context.Context, documentID, operator string, option ...Option) (resp *DocContent, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkDocBlock"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	larkAccessToken, err := c.getLarkAccessTokenWithOption(ctx, operator, option...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}
	result, err := RequestAndCheckError(ctx, func() (*getDocContentResp, any) {
		resp, err := c.larkCli.Do(ctx,
			&larkcore.ApiReq{
				HttpMethod:                http.MethodGet,
				ApiPath:                   c.larkAPIBaseURL + "/open-apis/doc/v2/:docToken/content",
				Body:                      nil,
				QueryParams:               larkcore.QueryParams{"user_id_type": []string{"open_id"}},
				PathParams:                larkcore.PathParams{"docToken": documentID},
				SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeUser},
			},
			larkcore.WithUserAccessToken(larkAccessToken),
		)

		if err != nil {
			logs.CtxError(ctx, "GetMarkdownDataFromLarkDocument Doc Error = %v", err)
			return nil, err
		}

		response := &getDocContentResp{}
		err = json.Unmarshal(resp.RawBody, response)
		if err != nil {
			return nil, err
		}
		return response, &response.CodeError
	})

	if err != nil {
		return nil, err
	}

	content := &DocContent{}
	err = json.Unmarshal([]byte(result.Data.Content), content)
	return content, err
}

type getDocContentResp struct {
	larkcore.CodeError
	Data struct {
		Content string `json:"content"`
	} `json:"data"`
}

// GetLarkDocxRawContent returns the content of a lark document with docx type.
func (c *client) GetLarkDocxRawContent(ctx context.Context, documentID, larkAccessToken string) (resp *DocxContent, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkDocxRawContent"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	return RequestAndCheckError(ctx, func() (*DocxContent, any) {
		metaReq := larkdocx.NewGetDocumentReqBuilder().
			DocumentId(documentID).
			Build()
		metaRes, err := c.larkCli.Docx.V1.Document.Get(context.Background(), metaReq, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		// 服务端错误处理
		if !metaRes.Success() {
			return nil, metaRes.CodeError
		}
		docxContent := &DocxContent{}
		if metaRes.Data != nil && metaRes.Data.Document != nil && metaRes.Data.Document.Title != nil {
			docxContent.Title = *metaRes.Data.Document.Title
		}

		// 创建请求对象
		contentReq := larkdocx.NewRawContentDocumentReqBuilder().
			DocumentId(documentID).
			Lang(0).
			Build()

		// 发起请求
		contentRes, err := c.larkCli.Docx.V1.Document.RawContent(context.Background(), contentReq, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		if !contentRes.Success() {
			return nil, contentRes.CodeError
		}
		if contentRes.Data != nil && contentRes.Data.Content != nil {
			docxContent.Body = *contentRes.Data.Content
		}
		return docxContent, nil
	})
}

// GetLarkDocxBlock returns the markdown content of a lark document with docx type.
func (c *client) GetLarkDocxBlock(ctx context.Context, documentID, operator string, option ...Option) (resp []*larkdocx.Block, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkDocxBlock"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	larkAccessToken, err := c.getLarkAccessTokenWithOption(ctx, operator, option...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}
	hasMore := true
	maxPageSize := 500
	pageToken := ""
	docxBlocks := make([]*larkdocx.Block, 0)
	for hasMore {
		resp, err := RequestAndCheckError(ctx, func() (*larkdocx.ListDocumentBlockResp, any) {
			req := larkdocx.NewListDocumentBlockReqBuilder().
				DocumentId(documentID).
				PageSize(maxPageSize).
				PageToken(pageToken).
				DocumentRevisionId(-1).
				Build()
			resp, err := c.larkCli.Docx.DocumentBlock.List(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))

			if err != nil {
				return nil, err
			}

			return resp, &resp.CodeError
		})

		if err != nil {
			return nil, err
		}

		docxBlocks = append(docxBlocks, resp.Data.Items...)
		if resp.Data.PageToken != nil {
			pageToken = *resp.Data.PageToken
		}

		if resp.Data.HasMore != nil {
			hasMore = *resp.Data.HasMore
		} else {
			hasMore = false
		}
	}

	return docxBlocks, nil
}

func (c *client) getLarkAccessTokenWithOption(ctx context.Context, operator string, option ...Option) (string, error) {
	if len(option) > 0 && len(option[0].UserAccessToken) > 0 {
		return option[0].UserAccessToken, nil
	} else {
		return c.getUserAccessToken(ctx, operator)
	}
}

// ListWikiNodes returns all wiki nodes under the given parent node.
func (c *client) ListWikiNodes(ctx context.Context, spaceID, parentNodeToken, operator string) (resp []*larkwiki.Node, err error) {
	tags := &metrics.LarkTag{Method: "ListWikiNodes"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	larkAccessToken, err := c.getUserAccessToken(ctx, operator)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}
	nodes := make([]*larkwiki.Node, 0)
	pageToken := ""

	for {
		// 获取子节点列表
		resp, err := RequestAndCheckError(ctx, func() (*larkwiki.ListSpaceNodeResp, any) {
			spaceResp, err := c.larkCli.Wiki.SpaceNode.List(ctx,
				larkwiki.
					NewListSpaceNodeReqBuilder().
					SpaceId(spaceID).
					PageSize(50). // max 50
					ParentNodeToken(parentNodeToken).
					PageToken(pageToken).
					Build(),
				larkcore.WithUserAccessToken(larkAccessToken),
			)

			if err != nil {
				return nil, err
			}
			return spaceResp, &spaceResp.CodeError
		})

		if err != nil {
			return nil, errors.WithMessage(err, "failed to list wiki nodes")
		}

		nodes = append(nodes, resp.Data.Items...)

		if *resp.Data.HasMore {
			pageToken = *resp.Data.PageToken
		} else {
			break
		}
	}
	return nodes, nil
}

// ListWikiNodesV2 returns all wiki nodes under the given parent node.
func (c *client) ListWikiNodesV2(ctx context.Context, spaceID, parentNodeToken, larkAccessToken string) (resp []*larkwiki.Node, err error) {
	tags := &metrics.LarkTag{Method: "ListWikiNodesV2"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	nodes := make([]*larkwiki.Node, 0)
	pageToken := ""

	for {
		// 获取子节点列表
		resp, err := RequestAndCheckError(ctx, func() (*larkwiki.ListSpaceNodeResp, any) {
			spaceResp, err := c.larkCli.Wiki.V2.SpaceNode.List(ctx,
				larkwiki.
					NewListSpaceNodeReqBuilder().
					SpaceId(spaceID).
					PageSize(50). // max 50
					ParentNodeToken(parentNodeToken).
					PageToken(pageToken).
					Build(),
				larkcore.WithUserAccessToken(larkAccessToken),
			)

			if err != nil {
				return nil, err
			}
			return spaceResp, &spaceResp.CodeError
		})

		if err != nil {
			return nil, errors.WithMessage(err, "failed to list wiki nodes")
		}

		nodes = append(nodes, resp.Data.Items...)

		if *resp.Data.HasMore {
			pageToken = *resp.Data.PageToken
		} else {
			break
		}
	}
	return nodes, nil
}

func (c *client) GetWikiNode(ctx context.Context, nodeToken, operator string, option ...Option) (response *larkwiki.Node, err error) {
	tags := &metrics.LarkTag{Method: "GetWikiNode"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	larkAccessToken, err := c.getLarkAccessTokenWithOption(ctx, operator, option...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkwiki.GetNodeSpaceResp, any) {
		resp, err := c.larkCli.Wiki.Space.GetNode(ctx,
			larkwiki.NewGetNodeSpaceReqBuilder().
				Token(nodeToken).
				Build(),
			larkcore.WithUserAccessToken(larkAccessToken))

		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}

	node := resp.Data.Node
	if *node.NodeType == "shortcut" && *node.OriginSpaceId != "0" && *node.OriginNodeToken != "" {
		resp, err = RequestAndCheckError(ctx, func() (*larkwiki.GetNodeSpaceResp, any) {
			resp, err = c.larkCli.Wiki.Space.GetNode(ctx,
				larkwiki.NewGetNodeSpaceReqBuilder().
					Token(*node.OriginNodeToken).Build(),
				larkcore.WithUserAccessToken(larkAccessToken))

			if err != nil {
				return nil, err
			}
			return resp, &resp.CodeError
		})
		if err != nil {
			return nil, err
		}
		return resp.Data.Node, nil
	}

	return resp.Data.Node, nil
}

func (c *client) GetLarkDocx(ctx context.Context, documentID, operator string, option ...Option) (response *larkdocx.GetDocumentRespData, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkDocx"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	larkAccessToken, err := c.getLarkAccessTokenWithOption(ctx, operator, option...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkdocx.GetDocumentResp, any) {
		req := larkdocx.NewGetDocumentReqBuilder().
			DocumentId(documentID).
			Build()
		resp, err := c.larkCli.Docx.Document.
			Get(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})

	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}

type RequestDoc struct {
	DocToken string `json:"doc_token"` // 文件的 token
	DocType  string `json:"doc_type"`  // 文件类型
}

// GetDocMeta 获取文档元数据，一次请求中不可超过 200 个文档，频控为 1000 次/分钟、50 次/秒.
func (c *client) GetDocMeta(ctx context.Context, docs []*RequestDoc, operator string, option ...Option) (response *larkdrive.BatchQueryMetaRespData, err error) {
	tags := &metrics.LarkTag{Method: "GetDocMeta"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	larkAccessToken, err := c.getLarkAccessTokenWithOption(ctx, operator, option...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.BatchQueryMetaResp, any) {
		req := larkdrive.NewBatchQueryMetaReqBuilder().
			MetaRequest(&larkdrive.MetaRequest{
				RequestDocs: lo.Map(docs, func(d *RequestDoc, _ int) *larkdrive.RequestDoc {
					return &larkdrive.RequestDoc{
						DocToken: &d.DocToken,
						DocType:  &d.DocType,
					}
				}),
				WithUrl: nil,
			}).
			Build()
		resp, err := c.larkCli.Drive.Meta.BatchQuery(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})

	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}

type DocMeta struct {
	larkcore.CodeError
	Data struct {
		CreateTime    int64  `json:"create_time"`
		EditTime      int64  `json:"edit_time"`
		Title         string `json:"title"`
		URL           string `json:"url"`
		ObjType       string `json:"obj_type"`
		IsUpgraded    bool   `json:"is_upgraded"`
		UpgradedToken string `json:"upgraded_token"`
	} `json:"data"`
}

type SearchPassage struct {
	Content string  `json:"content"`
	Title   string  `json:"title"`
	URL     string  `json:"url"`
	ID      string  `json:"passage_id"`
	Score   float64 `json:"score"`
	Extra   string  `json:"extra"`
}

type SearchPassageExtra struct {
	ObjType   string `json:"obj_type"`
	OwnerName string `json:"owner_name"`
}

func (s *SearchPassageExtra) GetContentType() string {
	switch s.ObjType {
	case "2":
		return "doc"
	case "22", "25": //25为wiki的快捷方式
		return "docx"
	case "3":
		return "sheet"
	case "8":
		return "bitable"
	case "0":
		return "folder"
	default:
		return ""
	}
}

type SearchMeta struct {
	larkcore.CodeError
	Data struct {
		Passages []SearchPassage `json:"passages"`
	} `json:"data"`
}

type EntityInfo struct {
	EntityID        string
	EntityTitle     string
	EntityContent   string
	EntityLinkInfo  string
	EntityUserInfo  string
	EntityDocInfo   string
	Classifications []*larkbaike.Classification
}

type SearchRequest struct {
	Query        string `json:"query"`
	Count        int    `json:"count"`
	PassageParam struct {
		DocParam struct {
			Searchable bool     `json:"searchable"`
			DocTokens  []string `json:"doc_tokens"`
		} `json:"doc_param"`
		WikiParam struct {
			Searchable bool     `json:"searchable"`
			NodeTokens []string `json:"node_tokens"`
		} `json:"wiki_param"`
		HelpdeskParam struct {
			Searchable bool `json:"searchable"`
		} `json:"helpdesk_param"`
	} `json:"passage_param"`
}

type PassageDisableSearch struct {
	DocDisable      bool
	DocTokens       []string
	WikiDisable     bool
	HelpdeskDisable bool
	WikiNodeTokens  []string
}

// SearchLarkData search lark data by vector search. It differs from keyword search in that it uses vector search to find the most relevant documents.
func (c *client) SearchLarkData(ctx context.Context, topK int, query, userToken string, disableSearch *PassageDisableSearch) (resp *SearchMeta, err error) {
	tags := &metrics.LarkTag{Method: "SearchLarkData"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	logs.CtxInfo(ctx, "[SearchLarkData] lark client info is %v, %v", c.larkID, c.larkSecret)
	request := &SearchRequest{
		Query: query,
		Count: topK,
		PassageParam: struct {
			DocParam struct {
				Searchable bool     `json:"searchable"`
				DocTokens  []string `json:"doc_tokens"`
			} `json:"doc_param"`
			WikiParam struct {
				Searchable bool     `json:"searchable"`
				NodeTokens []string `json:"node_tokens"`
			} `json:"wiki_param"`
			HelpdeskParam struct {
				Searchable bool `json:"searchable"`
			} `json:"helpdesk_param"`
		}{
			DocParam: struct {
				Searchable bool     `json:"searchable"`
				DocTokens  []string `json:"doc_tokens"`
			}{
				Searchable: lo.Ternary(disableSearch != nil, !lo.FromPtr(disableSearch).DocDisable, true),
				DocTokens:  lo.Ternary(disableSearch.DocTokens != nil, lo.FromPtr(disableSearch).DocTokens, nil),
			},
			WikiParam: struct {
				Searchable bool     `json:"searchable"`
				NodeTokens []string `json:"node_tokens"`
			}{
				Searchable: lo.Ternary(disableSearch != nil, !lo.FromPtr(disableSearch).WikiDisable, true),
				NodeTokens: lo.Ternary(disableSearch.WikiNodeTokens != nil, lo.FromPtr(disableSearch).WikiNodeTokens, nil),
			},
			HelpdeskParam: struct {
				Searchable bool `json:"searchable"`
			}{
				Searchable: lo.Ternary(disableSearch != nil, !lo.FromPtr(disableSearch).HelpdeskDisable, true),
			},
		},
	}
	result, err := RequestAndCheckError(ctx, func() (*SearchMeta, any) {
		resp, err := c.larkCli.Post(context.Background(),
			"/open-apis/search/v2/suite_dataset/search",
			request,
			larkcore.AccessTokenTypeUser,
			larkcore.WithUserAccessToken(userToken))
		logs.CtxInfo(ctx, "[SearchLarkData] Post resp: %v, lark_id %v, err: %v", resp, c.larkID, err)
		if err != nil {
			return nil, err
		}

		response := &SearchMeta{}
		err = json.Unmarshal(resp.RawBody, &response)
		if err != nil {
			return nil, err
		}
		return response, &response.CodeError
	})
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[SearchLarkData] result: %v", result)

	return result, nil
}

type SearchLarkObjectRequest struct {
	SearchKey string   `json:"search_key"`
	Count     int      `json:"count"`
	Offset    int      `json:"offset"`
	OwnerIDs  []string `json:"owner_ids"`
	ChatIDs   []string `json:"chat_ids"`
	DocsTypes []string `json:"docs_types"`
}

type SearchLarkObjectResp struct {
	larkcore.CodeError
	Data *SearchLarkObjectRespData `json:"data"`
}

type SearchLarkObjectRespData struct {
	DocsEntities []struct {
		DocsToken string `json:"docs_token"`
		DocsType  string `json:"docs_type"`
		OwnerID   string `json:"owner_id"`
		Title     string `json:"title"`
	} `json:"docs_entities"`
	HasMore bool `json:"has_more"`
	Total   int  `json:"total"`
}

func (c *client) SearchLarkObject(ctx context.Context, req *SearchLarkObjectRequest, userToken string) (resp *SearchLarkObjectRespData, err error) {
	tags := &metrics.LarkTag{Method: "SearchLarkObject"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	logs.CtxInfo(ctx, "[SearchLarkObject] lark client info is %v, %v", c.larkID, c.larkSecret)
	result, err := RequestAndCheckError(ctx, func() (*SearchLarkObjectRespData, any) {
		resp, err := c.larkCli.Post(context.Background(),
			"/open-apis/suite/docs-api/search/object",
			req,
			larkcore.AccessTokenTypeUser,
			larkcore.WithUserAccessToken(userToken))

		logs.CtxInfo(ctx, "[SearchLarkObject] Post resp: %v, lark_id %v, err: %v, logId:%v", resp, c.larkID, err, resp.LogId())
		if err != nil {
			return nil, err
		}

		response := &SearchLarkObjectResp{}
		err = json.Unmarshal(resp.RawBody, &response)
		if err != nil {
			return nil, err
		}
		return response.Data, &response.CodeError
	})
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[SearchLarkData] result: %v", result)
	return result, nil
}

type SearchWikiNodeRequest struct {
	Query    string  `json:"query"`
	SpaceID  *string `json:"space_id,omitempty"`
	NodeID   *string `json:"node_id,omitempty"`
	PageSize *int    `json:"page_size,omitempty"`
}

type SearchWikiNodeResponse struct {
	larkcore.CodeError
	Data *SearchWikiNodeData `json:"data"`
}

type SearchWikiNodeData struct {
	Items []struct {
		NodeID   string `json:"node_id"`
		ObjToken string `json:"obj_token"`
		ObjType  int    `json:"obj_type"`
		ParentID string `json:"parent_id"`
		SortID   int    `json:"sort_id"`
		SpaceID  string `json:"space_id"`
		Title    string `json:"title"`
		URL      string `json:"url"`
	} `json:"items"`
	HasMore bool `json:"has_more"`
	Total   int  `json:"total"`
}

func (c *client) SearchWikiNode(ctx context.Context, req *SearchWikiNodeRequest, userToken string) (resp *SearchWikiNodeData, err error) {
	tags := &metrics.LarkTag{Method: "SearchWikiNode"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	logs.CtxInfo(ctx, "[SearchWikiNode] lark client info is %v, %v", c.larkID, c.larkSecret)
	result, err := RequestAndCheckError(ctx, func() (*SearchWikiNodeData, any) {
		resp, err := c.larkCli.Post(context.Background(),
			lo.Ternary(req.PageSize == nil, "/open-apis/wiki/v1/nodes/search", fmt.Sprintf("/open-apis/wiki/v1/nodes/search?page_size=%d", lo.FromPtr(req.PageSize))),
			req,
			larkcore.AccessTokenTypeUser,
			larkcore.WithUserAccessToken(userToken))
		logs.CtxInfo(ctx, "[SearchWikiNode] Post resp: %v, lark_id %v, err: %v", resp, c.larkID, err)
		if err != nil {
			return nil, err
		}

		response := &SearchWikiNodeResponse{}
		err = json.Unmarshal(resp.RawBody, &response)
		if err != nil {
			return nil, err
		}
		return response.Data, &response.CodeError
	})
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[SearchWikiNode] result: %v", result)
	return result, nil
}

func (c *client) GetLarkDoc(ctx context.Context, documentID, operator string, option ...Option) (resp *DocMeta, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkDoc"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	larkAccessToken, err := c.getLarkAccessTokenWithOption(ctx, operator, option...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}
	result, err := RequestAndCheckError(ctx, func() (*DocMeta, any) {
		resp, err := c.larkCli.Do(ctx,
			&larkcore.ApiReq{
				HttpMethod:                http.MethodGet,
				ApiPath:                   c.larkAPIBaseURL + "/open-apis/doc/v2/meta/:docToken",
				QueryParams:               larkcore.QueryParams{"user_id_type": []string{"open_id"}},
				PathParams:                larkcore.PathParams{"docToken": documentID},
				Body:                      nil,
				SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeUser},
			},
			larkcore.WithUserAccessToken(larkAccessToken),
		)

		if err != nil {
			return nil, err
		}

		response := &DocMeta{}
		err = json.Unmarshal(resp.RawBody, &response)
		if err != nil {
			return nil, err
		}
		return response, &response.CodeError
	})
	if err != nil {
		return nil, err
	}
	return result, nil
}

type FileStatistics struct {
	UV             int `json:"uv"`
	PV             int `json:"pv"`
	LikeCount      int `json:"like_count"`
	Timestamp      int `json:"timestamp"`
	UVToday        int `json:"uv_today"`
	PVToday        int `json:"pv_today"`
	LikeCountToday int `json:"like_count_today"`
}

// GetLarkDocumentsStatisticsInfo https://open.larkoffice.com/document/server-docs/docs/drive-v1/file/get?appId=cli_a75a41b019abd01c
func (c *client) GetLarkFileStatisticsInfo(ctx context.Context, fileToken, fileType, token string, option ...Option) (FileStatistics, error) {
	var result FileStatistics
	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.GetFileStatisticsResp, any) {
		req := larkdrive.NewGetFileStatisticsReqBuilder().
			FileToken(fileToken).
			FileType(fileType).
			Build()
		resp, err := c.larkCli.Drive.V1.FileStatistics.Get(context.Background(), req, larkcore.WithUserAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, nil
	})
	if err != nil {
		return result, err
	}
	if !resp.Success() {
		return result, errors.New(resp.Msg)
	}
	if resp.Msg != "Success" {
		return result, errors.New(resp.Msg)
	}
	if resp.Data == nil {
		return result, errors.New("[GetLarkFileStatisticsInfo] Data is nil")
	}
	if resp.Data.Statistics == nil {
		return result, errors.New("[GetLarkFileStatisticsInfo] Statistics is nil")
	}

	result.UV = lo.FromPtr(resp.Data.Statistics.Uv)

	result.PV = lo.FromPtr(resp.Data.Statistics.Pv)

	result.LikeCount = lo.FromPtr(resp.Data.Statistics.LikeCount)

	result.Timestamp = lo.FromPtr(resp.Data.Statistics.Timestamp)

	result.UVToday = lo.FromPtr(resp.Data.Statistics.UvToday)

	result.PVToday = lo.FromPtr(resp.Data.Statistics.PvToday)

	result.LikeCountToday = lo.FromPtr(resp.Data.Statistics.LikeCountToday)

	return result, nil
}

// GetFilesMeta 获取文档元数据，一次请求中不可超过 200 个文档，频控为 1000 次/分钟、50 次/秒.
func (c *client) GetFilesMeta(ctx context.Context, docs []*RequestDoc, token string, option ...Option) (response *larkdrive.BatchQueryMetaRespData, err error) {
	tags := &metrics.LarkTag{Method: "GetFilesMeta"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	var userIDType string
	if len(option) > 0 && len(option[0].UserIDType) > 0 {
		userIDType = string(option[0].UserIDType)
	}

	var accessToken = token
	if token == "" {
		tenantToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
		accessToken = tenantToken
	}

	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.BatchQueryMetaResp, any) {
		builder := larkdrive.NewBatchQueryMetaReqBuilder()
		if len(userIDType) > 0 {
			builder = builder.UserIdType(userIDType)
		}
		req := builder.
			MetaRequest(&larkdrive.MetaRequest{
				RequestDocs: lo.Map(docs, func(d *RequestDoc, _ int) *larkdrive.RequestDoc {
					return &larkdrive.RequestDoc{
						DocToken: &d.DocToken,
						DocType:  &d.DocType,
					}
				}),
				WithUrl: lo.ToPtr(true),
			}).
			Build()
		resp, err := c.larkCli.Drive.Meta.BatchQuery(ctx, req, lo.Ternary(token != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})

	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}

func (c *client) getLarkEntityDesc(ctx context.Context, token, entityID string) (*EntityInfo, error) {
	resp, err := RequestAndCheckError(ctx, func() (*larkbaike.GetEntityResp, any) {
		req := larkbaike.NewGetEntityReqBuilder().EntityId(entityID).Build()
		resp, err := c.larkCli.Baike.Entity.Get(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, errors.New(resp.Msg)
	}
	if resp.Data != nil && resp.Data.Entity != nil && resp.Data.Entity.Description != nil && resp.Data.Entity.Id != nil && resp.Data.Entity.RelatedMeta != nil {
		// get all aliases
		aliases := make([]string, 0)
		for _, alias := range resp.Data.Entity.Aliases {
			if alias.Key != nil {
				aliases = append(aliases, *alias.Key)
			}
		}
		for _, mainKey := range resp.Data.Entity.MainKeys {
			if mainKey.Key != nil {
				aliases = append(aliases, *mainKey.Key)
			}
		}
		aliases = gslice.Uniq(aliases)
		if len(aliases) == 0 {
			return nil, errors.New("main key is empty, entity id is " + entityID)
		}
		title := strings.Join(aliases, "/")

		// get all related users
		userOpenIDs := make([]string, 0)
		for _, userMeta := range resp.Data.Entity.RelatedMeta.Users {
			if userMeta != nil && userMeta.Title != nil && userMeta.Id != nil {
				userOpenIDs = append(userOpenIDs, *userMeta.Id)
			}
		}
		userOpenIDs = gslice.Uniq(userOpenIDs)
		var userContent string
		if len(userOpenIDs) != 0 {
			larkUsers, err := c.ListLarkUsers(ctx, userOpenIDs, "")
			if err != nil {
				logs.CtxError(ctx, "[GetLarkEntityDesc] ListLarkUsers err %v", err)
				userContent = ""
			} else {
				users := make([]string, 0)
				for _, larkUser := range larkUsers {
					if larkUser != nil && larkUser.Email != nil {
						userName := strings.Split(*larkUser.Email, "@")[0]
						logs.CtxInfo(ctx, "*larkUser.Email is %v", *larkUser.Email)
						users = append(users, fmt.Sprintf("@%v", userName))
					}
				}
				userContent = strings.Join(users, ", ")
			}
		}

		// get all related docs
		docs := make([]string, 0)
		for _, docMeta := range resp.Data.Entity.RelatedMeta.Docs {
			if docMeta != nil && docMeta.Title != nil && docMeta.Url != nil {
				docInfo := fmt.Sprintf("名称：%v，链接：%v", *docMeta.Title, *docMeta.Url)
				docs = append(docs, docInfo)
			}
		}
		docs = gslice.Uniq(docs)
		docContent := strings.Join(docs, "\n")

		// get all related link
		links := make([]string, 0)
		for _, linkMeta := range resp.Data.Entity.RelatedMeta.Links {
			if linkMeta != nil && linkMeta.Title != nil && linkMeta.Url != nil {
				linkInfo := fmt.Sprintf("名称：%v，链接：%v", *linkMeta.Title, *linkMeta.Url)
				links = append(docs, linkInfo)
			}
		}
		links = gslice.Uniq(links)
		linkContent := strings.Join(links, "\n")
		logs.CtxInfo(ctx, "[GetLarkEntityDesc] EntityDocInfo is %v, EntityLinkInfo is %v, EntityUserInfo is %v", docContent, linkContent, userContent)

		entityInfo := &EntityInfo{
			EntityID:       *resp.Data.Entity.Id,
			EntityTitle:    title,
			EntityDocInfo:  docContent,
			EntityLinkInfo: linkContent,
			EntityUserInfo: userContent,
			EntityContent:  fmt.Sprintf("%s: %s", title, *resp.Data.Entity.Description),
		}
		if resp.Data.Entity.RelatedMeta != nil {
			entityInfo.Classifications = resp.Data.Entity.RelatedMeta.Classifications
		}
		return entityInfo, nil
	}
	return nil, errors.New("entity info is empty, entity id is " + entityID)
}

func (c *client) GetLarkEntityDesc(ctx context.Context, entityIDs []string) (resp []*EntityInfo, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkEntityDesc"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	if len(entityIDs) == 0 {
		return nil, nil
	}
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	entityInfos := make([]*EntityInfo, 0, len(entityIDs))
	lock := sync.Mutex{}
	maxPoolSize := 5

	group := poolsdk.New().WithMaxGoroutines(maxPoolSize)
	for _, entityID := range entityIDs {
		id := entityID
		group.Go(func() {
			entityInfo, err := c.getLarkEntityDesc(ctx, token, id)
			if err != nil {
				logs.CtxError(ctx, "[GetLarkEntityDesc] getLarkEntityDesc err %v", err)
				return
			}
			lock.Lock()
			entityInfos = append(entityInfos, entityInfo)
			lock.Unlock()
		})
	}
	group.Wait()
	return entityInfos, nil
}

func (c *client) GetLarkBaikePhrases(ctx context.Context, text string) (response []*larkbaike.Phrase, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkBaikePhrases"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkbaike.HighlightEntityResp, any) {
		req := larkbaike.NewHighlightEntityReqBuilder().Body(larkbaike.NewHighlightEntityReqBodyBuilder().Text(text).Build()).Build()
		resp, err := c.larkCli.Baike.Entity.Highlight(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[getLarkWikiEntityIDs] failed, err=%s", resp.Msg)
	}
	return resp.Data.Phrases, nil
}

func (c *client) GetLarkEntity(ctx context.Context, entityID string) (response *larklingo.Entity, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkEntity"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	resp, err := RequestAndCheckError(ctx, func() (*larklingo.GetEntityResp, any) {
		req := larklingo.NewGetEntityReqBuilder().EntityId(entityID).Build()
		resp, err := c.larkCli.Lingo.Entity.Get(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[GetLarkEntity] failed, err=%s", resp.Msg)
	}
	if resp.Data == nil {
		return nil, nil
	}
	return resp.Data.Entity, nil
}

func (c *client) ListLarkUsers(ctx context.Context, userIDs []string, userIDType string, option ...Option) (response []*larkcontact.User, err error) {
	tags := &metrics.LarkTag{Method: "ListLarkUsers"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	// 默认open_id获取
	if userIDType == "" {
		userIDType = larkcontact.UserIdTypeOpenId
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkcontact.BatchUserResp, any) {
		req := larkcontact.NewBatchUserReqBuilder().UserIdType(userIDType).UserIds(userIDs).Build()
		resp, err := c.larkCli.Contact.User.Batch(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[ListLarkUsers] failed, err=%s", resp.Msg)
	}
	return resp.Data.Items, nil
}

// SendLarkApplicationMessage 发送飞书应用消息：https://open.larkoffice.com/document/server-docs/im-v1/message/create?appId=cli_a75a41b019abd01c
func (c *client) SendLarkApplicationMessage(ctx context.Context, receiveIDType ReceiveIDType, receiverEmail, msgType, content string) (data *larkim.CreateMessageRespData, err error) {
	tags := &metrics.LarkTag{Method: "SendLarkApplicationMessage"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[SendLarkApplicationMessage] receiverEmail is %v, msgType is %v, receiveIDType is %v, content is %v", receiverEmail, msgType, receiveIDType, content)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.CreateMessageResp, any) {
		req := larkim.NewCreateMessageReqBuilder().
			ReceiveIdType(string(receiveIDType)).
			Body(larkim.NewCreateMessageReqBodyBuilder().
				ReceiveId(receiverEmail).
				MsgType(msgType).
				Content(content).
				Uuid(``).
				Build()).
			Build()
		resp, err := c.larkCli.Im.V1.Message.Create(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[SendLarkApplicationMessage] failed, err=%s", resp.Msg)
	}
	return resp.Data, nil
}

// UpdateLarkApplicationMessage 更新已发送的飞书应用消息：https://open.larkoffice.com/document/server-docs/im-v1/message-card/patch
func (c *client) UpdateLarkApplicationMessage(ctx context.Context, messageID, content string) (err error) {
	tags := &metrics.LarkTag{Method: "UpdateLarkApplicationMessage"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return err
	}
	logs.CtxInfo(ctx, "[UpdateLarkApplicationMessage] messageID is %v, content is %v", messageID, content)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.PatchMessageResp, any) {
		req := larkim.NewPatchMessageReqBuilder().
			MessageId(messageID).
			Body(larkim.NewPatchMessageReqBodyBuilder().
				Content(content).
				Build()).
			Build()

		resp, err := c.larkCli.Im.V1.Message.Patch(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return err
	}
	if !resp.Success() {
		return fmt.Errorf("[UpdateLarkApplicationMessage] failed, err=%s", resp.Msg)
	}
	return nil
}

// SendLarkApplicationReplyMessage 发送飞书应用Reply消息：https://open.larkoffice.com/document/server-docs/im-v1/message/create?appId=cli_a75a41b019abd01c
func (c *client) SendLarkApplicationReplyMessage(ctx context.Context, replyID string, msgType, content string) (data *larkim.ReplyMessageRespData, err error) {
	tags := &metrics.LarkTag{Method: "SendLarkApplicationReplyMessage"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[SendLarkApplicationReplyMessage] replyID is %v, msgType is %v, content is %v", replyID, msgType, content)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.ReplyMessageResp, any) {
		req := larkim.NewReplyMessageReqBuilder().
			MessageId(replyID).
			Body(larkim.NewReplyMessageReqBodyBuilder().
				Content(content).
				MsgType(msgType).
				ReplyInThread(true).
				Build()).
			Build()
		resp, err := c.larkCli.Im.Message.Reply(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[SendLarkApplicationMessage] failed, err=%s", resp.Msg)
	}
	return resp.Data, nil
}

// GetMessageResource 获取消息中的资源文件：https://open.larkoffice.com/document/server-docs/im-v1/message/get-2?appId=cli_a88d3b19f958d01c
func (c *client) GetMessageResource(ctx context.Context, messageID string, fileKey string, fileType string) (data *larkim.GetMessageResourceResp, err error) {
	tags := &metrics.LarkTag{Method: "GetMessageResource"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[GetMessageResource] messageID is %v, fileKey is %v, fileType is %v", messageID, fileKey, fileType)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.GetMessageResourceResp, any) {
		req := larkim.NewGetMessageResourceReqBuilder().
			MessageId(messageID).
			FileKey(fileKey).
			Type(fileType).
			Build()
		resp, err := c.larkCli.Im.V1.MessageResource.Get(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[SendLarkApplicationMessage] failed, err=%s", resp.Msg)
	}
	return resp, nil
}

// AddLarkFilePermission 给云文档添加协作者权限：https://open.larkoffice.com/document/server-docs/docs/permission/permission-member/create?appId=cli_a75a41b019abd01c
func (c *client) AddLarkFilePermission(ctx context.Context, fileToken, email, perm, fileType string) (err error) {
	tags := &metrics.LarkTag{Method: "AddLarkFilePermission"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return err
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.CreatePermissionMemberResp, any) {
		req := larkdrive.NewCreatePermissionMemberReqBuilder().
			NeedNotification(true).
			Token(fileToken).
			Type(fileType).
			BaseMember(larkdrive.NewBaseMemberBuilder().
				// 固定用公司邮箱授权 eg：<EMAIL>
				MemberType(`email`).
				MemberId(email).
				Perm(perm).
				Build()).
			Build()
		resp, err := c.larkCli.Drive.V1.PermissionMember.Create(context.Background(), req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return err
	}
	if !resp.Success() {
		return fmt.Errorf("[AddLarkFilePermission] failed, err=%s", resp.Msg)
	}
	return nil
}

// UploadLarkFile 上传文件到飞书,api文档：https://open.feishu.cn/document/server-docs/docs/drive-v1/upload/upload_all?appId=cli_a75a41b019abd01c
func (c *client) UploadLarkFile(ctx context.Context, fileName, parentNode string, size int, file io.Reader) (response *string, err error) {
	tags := &metrics.LarkTag{Method: "UploadLarkFile"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.UploadAllFileResp, any) {
		req := larkdrive.NewUploadAllFileReqBuilder().
			Body(larkdrive.NewUploadAllFileReqBodyBuilder().
				FileName(fileName).
				// explorer固定值代表云空间
				ParentType("explorer").
				ParentNode(parentNode).
				Size(size).
				Checksum(``).
				File(file).
				Build()).Build()
		resp, err := c.larkCli.Drive.V1.File.UploadAll(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[UploadLarkFile] failed, err=%s", resp.Msg)
	}
	return resp.Data.FileToken, nil
}

// UploadLarkMedia 上传媒体文件到飞书,api文档：https://open.feishu.cn/document/server-docs/docs/drive-v1/media/upload-all
func (c *client) UploadLarkMedia(ctx context.Context, body *larkdrive.UploadAllMediaReqBody, larkAccessToken string) (response *string, err error) {
	tags := &metrics.LarkTag{Method: "UploadLarkMedia"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, err
		}
	}

	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.UploadAllMediaResp, any) {
		req := larkdrive.NewUploadAllMediaReqBuilder().
			Body(body).Build()
		resp, err := c.larkCli.Drive.V1.Media.UploadAll(ctx, req, lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(larkAccessToken), larkcore.WithTenantAccessToken(accessToken)))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[UploadLarkMedia] failed, err=%s", resp.Msg)
	}
	return resp.Data.FileToken, nil
}

// CreateExportTasks 创建导出任务，支持导出多种类型文件，返回任务ID：https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/create
func (c *client) CreateExportTasks(ctx context.Context, fileExtension, fileToken, fileType, subID, larkAccessToken string) (response *string, err error) {
	tags := &metrics.LarkTag{Method: "CreateExportTasks"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	if fileExtension == "" || fileToken == "" || fileType == "" {
		return nil, errors.New("fileExtension, fileToken, fileType can not be empty")
	}
	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.CreateExportTaskResp, any) {
		exportTask := larkdrive.NewExportTaskBuilder().
			FileExtension(fileExtension).
			Token(fileToken).
			Type(fileType)
		if subID != "" {
			exportTask = exportTask.SubId(subID)
		}
		req := larkdrive.NewCreateExportTaskReqBuilder().ExportTask(exportTask.
			Build()).Build()
		resp, err := c.larkCli.Drive.V1.ExportTask.Create(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[CreateExportTasks] failed, err=%s", resp.Msg)
	}
	return resp.Data.Ticket, nil
}

// GetExportTask 获取导出任务状态,返回任务是否完成以及导出文件token：https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/get
func (c *client) GetExportTask(ctx context.Context, fileToken, taskID, larkAccessToken string) (isSucceed bool, token string, filename string, err error) {
	tags := &metrics.LarkTag{Method: "GetExportTask"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	if taskID == "" {
		return false, "", "", errors.New("taskID can not be empty")
	}
	req := larkdrive.NewGetExportTaskReqBuilder().Ticket(taskID).Token(fileToken).Build()
	resp, err := c.larkCli.Drive.ExportTask.Get(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
	if err != nil {
		return false, "", "", err
	}
	if !resp.Success() {
		return false, "", "", fmt.Errorf("[GetExportTask] failed, err=%s", resp.Msg)
	}
	switch *resp.Data.Result.JobStatus {
	case int(ExportTaskJobStatusInit), int(ExportTaskJobStatusRunning):
		return false, "", "", nil
	case int(ExportTaskJobStatusSucceed):
		return true, *resp.Data.Result.FileToken, *resp.Data.Result.FileName, nil
	default:
		return false, "", "", errors.New(*resp.Data.Result.JobErrorMsg)
	}
}

// AddRowForLarkSheet 插入数据：https://open.larkoffice.com/document/server-docs/docs/sheets-v3/data-operation/prepend-data
func (c *client) AddRowForLarkSheet(ctx context.Context, spreadsheetToken string, valueRange string, values [][]interface{}) (resp *AddRowForLarkSheetResp, err error) {
	tags := &metrics.LarkTag{Method: "AddRowForLarkSheet"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	header := make(http.Header)
	header.Set("Content-Type", "application/json; charset=utf-8")
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	queryData := map[string]interface{}{
		"valueRange": map[string]interface{}{
			"range":  valueRange,
			"values": values,
		},
	}
	queryDataRaw, err := json.Marshal(queryData)
	if err != nil {
		return nil, err
	}
	apiUrl := fmt.Sprintf("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%v/values_append", spreadsheetToken)
	respBytes, err := c.PostWithHeader(ctx, apiUrl, header, queryDataRaw)
	if err != nil {
		logs.CtxError(ctx, "[AddRowForLarkSheetWithQuery] PostWithHeader err %v", err)
		return nil, err
	}
	var response AddRowForLarkSheetResp
	err = json.Unmarshal(respBytes, &response)
	if err != nil {
		return nil, err
	}
	if response.Code != 0 {
		return nil, fmt.Errorf("[AddRowForLarkSheet] failed, code=%d, err=%s", response.Code, response.Msg)
	}
	// 可以根据需要判断 response.Code 是否为 0
	return &response, nil
}

// AddRowForLarkSheet 插入数据：https://open.larkoffice.com/document/server-docs/docs/sheets-v3/data-operation/prepend-data
func (c *client) AddImageCellForLarkSheet(ctx context.Context, spreadsheetToken string, valueRange string, image []byte, name string) (resp *AddImageCellForLarkSheetResp, err error) {
	tags := &metrics.LarkTag{Method: "AddImageCellForLarkSheet"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	header := make(http.Header)
	header.Set("Content-Type", "application/json; charset=utf-8")
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	queryData := map[string]interface{}{
		"range": valueRange,
		"image": image,
		"name":  name,
	}
	queryDataRaw, err := json.Marshal(queryData)
	if err != nil {
		return nil, err
	}
	apiUrl := fmt.Sprintf("https://open.larkoffice.com/open-apis/sheets/v2/spreadsheets/%v/values_image", spreadsheetToken)
	respBytes, err := c.PostWithHeader(ctx, apiUrl, header, queryDataRaw)
	if err != nil {
		logs.CtxError(ctx, "[AddImageCellForLarkSheetWithQuery] PostWithHeader err %v", err)
		return nil, err
	}
	var response AddImageCellForLarkSheetResp
	err = json.Unmarshal(respBytes, &response)
	if err != nil {
		return nil, err
	}
	// 可以根据需要判断 response.Code 是否为 0
	if response.Code != 0 {
		return nil, fmt.Errorf("[AddImageCellForLarkSheet] failed, code=%d, err=%s", response.Code, response.Msg)
	}
	return &response, nil
}

// DownLoadExportFile 下载导出的文件：https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/download?appId=cli_a75a41b019abd01c
func (c *client) DownLoadExportFile(ctx context.Context, fileToken, larkAccessToken string) (response *larkdrive.DownloadExportTaskResp, err error) {
	tags := &metrics.LarkTag{Method: "DownLoadExportFile"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	resp, err := RequestAndCheckError(ctx, func() (*larkdrive.DownloadExportTaskResp, any) {
		req := larkdrive.NewDownloadExportTaskReqBuilder().FileToken(fileToken).Build()
		resp, err := c.larkCli.Drive.V1.ExportTask.Download(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[DownLoadExportFile] failed, err=%s", resp.Msg)
	}

	// 调用方resp.WriteFile()获取文件内容
	return resp, nil
}

// GetWikiNodeInfo 获取 wiki 的文件信息：https://open.larkoffice.com/document/server-docs/docs/wiki-v2/space-node/get_node
func (c *client) GetSheetsSubIDs(ctx context.Context, fileToken, larkAccessToken string) (response *larksheets.QuerySpreadsheetSheetResp, err error) {
	tags := &metrics.LarkTag{Method: "GetSheetsSubIDs"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	req := larksheets.NewQuerySpreadsheetSheetReqBuilder().
		SpreadsheetToken(fileToken).
		Build()

	const maxRetries = 3
	for i := 0; i < maxRetries; i++ {
		resp, err := c.larkCli.Sheets.V3.SpreadsheetSheet.Query(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		if resp.Success() {
			return resp, nil
		}
		// 如果是错误码 1315201 且还有重试次数，则继续重试
		if resp.Code == 1315201 && i < maxRetries-1 {
			continue
		}
		// 其他错误或重试次数用完，返回错误
		return nil, fmt.Errorf("[GetSheetsSubID] failed, err code=%d, err msg=%s", resp.Code, resp.Msg)
	}
	return nil, fmt.Errorf("[GetSheetsSubID] failed after %d retries", maxRetries)
}

// GetWikiNodeInfo 获取 wiki 的文件信息：https://open.larkoffice.com/document/server-docs/docs/wiki-v2/space-node/get_node
func (c *client) GetWikiNodeInfo(ctx context.Context, fileToken, larkAccessToken string) (response *larkwiki.GetNodeSpaceResp, err error) {
	tags := &metrics.LarkTag{Method: "GetWikiNodeInfo"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	req := larkwiki.NewGetNodeSpaceReqBuilder().Token(fileToken).Build()
	resp, err := c.larkCli.Wiki.V2.Space.GetNode(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[CreateExportTasks] failed, err=%s", resp.Msg)
	}
	return resp, nil
}

func (c *client) GetWikiSpaceInfo(ctx context.Context, spaceID, larkAccessToken string) (response *larkwiki.GetSpaceResp, err error) {
	tags := &metrics.LarkTag{Method: "GetWikiSpaceInfo"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	req := larkwiki.NewGetSpaceReqBuilder().SpaceId(spaceID).Build()
	resp, err := c.larkCli.Wiki.V2.Space.Get(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[CreateExportTasks] failed, err=%s", resp.Msg)
	}
	return resp, nil
}

// ReadBatchLarkSheet 读取电子表格中多个指定范围的数据：https://open.larkoffice.com/document/server-docs/docs/sheets-v3/data-operation/reading-multiple-ranges
func (c *client) ReadBatchLarkSheet(ctx context.Context, fileToken, valueRanges string, larkAccessToken string) (*SheetData, error) {
	apiURL := fmt.Sprintf("https://open.larkoffice.com/open-apis/sheets/v2/spreadsheets/%s/values_batch_get?ranges=%s&valueRenderOption=ToString", fileToken, valueRanges)
	req, err := http.NewRequest(http.MethodGet, apiURL, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("content-type", "application/json; charset=utf-8")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", larkAccessToken))
	httpCli := http.Client{}
	resp, err := httpCli.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var response struct {
		Code int       `json:"code"`
		Data SheetData `json:"data"`
	}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to unmarshal response")
	}
	return &response.Data, nil
}

func (c *client) WriteBatchLarkSheet(ctx context.Context, fileToken string, valueRanges []*SheetValueRange) error {
	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return err
	}
	header := make(http.Header)
	header.Set("Content-Type", "application/json; charset=utf-8")
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	queryData := map[string]interface{}{
		"valueRanges": valueRanges,
	}
	queryDataRaw, err := json.Marshal(queryData)
	if err != nil {
		return err
	}
	log.V1.CtxInfo(ctx, "[WriteBatchLarkSheet] queryDataRaw %v", string(queryDataRaw))
	apiUrl := fmt.Sprintf("https://open.larkoffice.com/open-apis/sheets/v2/spreadsheets/%s/values_batch_update", fileToken)
	respBytes, err := c.PostWithHeader(ctx, apiUrl, header, queryDataRaw)
	if err != nil {
		logs.CtxError(ctx, "[WriteBatchLarkSheet] PostWithHeader err %v", err)
		return err
	}
	var response struct {
		Msg  string `json:"msg"`
		Code int    `json:"code"`
	}
	err = json.Unmarshal(respBytes, &response)
	if err != nil {
		return err
	}
	return nil
}

func (c *client) MGetUserIDByEmail(ctx context.Context, emails []string, idType UserIDType, includeResigned bool) (map[string]string, error) {
	req := larkcontact.NewBatchGetIdUserReqBuilder().
		UserIdType(string(idType)).
		Body(larkcontact.NewBatchGetIdUserReqBodyBuilder().
			Emails(emails).
			IncludeResigned(includeResigned).
			Build()).
		Build()
	// 发起请求
	resp, err := c.larkCli.Contact.V3.User.BatchGetId(context.Background(), req)
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[MGetUserIDByEmail] failed, logId: %s, error response: %s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
	}
	ret := make(map[string]string)
	for _, item := range resp.Data.UserList {
		ret[lo.FromPtr(item.Email)] = lo.FromPtr(item.UserId)
	}
	return ret, nil
}

const (
	maxRetryTimes           = 3
	rateLimitResetHeaderKey = "x-ogw-ratelimit-reset" // 限流时建议等待时间，单位：秒
)

func RequestAndCheckError[T any](ctx context.Context, doRequest func() (T, any)) (result T, err error) {
	var (
		respErr    any
		retryTimes int
	)
	for retryTimes < maxRetryTimes {
		retryTimes++
		result, respErr = doRequest()
		if respErr == nil { // 请求成功
			return result, nil
		}
		codeErr, ok := respErr.(*larkcore.CodeError)
		if !ok {
			if larkErr, ok := respErr.(*larkcore.ServerTimeoutError); ok {
				// 服务器超时错误，使用指数退避策略
				backoffTime := calculateExponentialBackoff(retryTimes)
				log.V1.CtxInfo(ctx, "ServerTimeoutError: try count: %d, waiting %v before retry", retryTimes, backoffTime)
				time.Sleep(backoffTime)
				err = larkErr
				continue
			}
			if err, ok := respErr.(error); ok {
				return result, err
			}
			return result, nil
		}
		if codeErr == nil || codeErr.Code == 0 { // 请求成功
			return result, nil
		}

		switch resp := any(result).(type) { // 对失败请求打印 log id
		case *larkcontact.BatchUserResp:
			if resp == nil || resp.ApiResp == nil {
				break
			}
			logID := resp.Header.Get(ttLogIDHeaderKey)
			log.V1.CtxError(ctx, "RequestAndCheckError: try count: %d, logID: %s, err: %v", retryTimes, logID, respErr)
		}

		// 处理各种错误码
		switch codeErr.Code {
		case APIFrequencyLimitCode:
			// 飞书频控策略：https://open.larkoffice.com/document/server-docs/api-call-guide/frequency-control
			var resetSeconds int64
			if resp, ok := any(result).(*larkwiki.ListSpaceNodeResp); ok {
				if resp.Header.Get(rateLimitResetHeaderKey) != "" {
					resetSeconds, _ = strconv.ParseInt(resp.Header.Get(rateLimitResetHeaderKey), 10, 64) // ignore the parse error
				}
			}
			err = ErrFrequencyLimit
			log.V1.CtxError(ctx, "RequestFrequencyLimitError: try count: %d", retryTimes)
			SleepSeveralSeconds(int(resetSeconds))
			continue
		case FolderLockedCode, InternalErrorCode, ConcurrencyErrorCode:
			// 此处错误统一走退避重试
			log.V1.CtxError(ctx, "Error[%d]: %s, try count: %d", codeErr.Code, codeErr.Msg, retryTimes)
			SleepSeveralSeconds(0)
			continue
		case APIPermissionDeniedCode, APIForbiddenCode, SpacePermissionDeniedCode, DocNoPermission:
			err = ErrPermissionDenied
		case AuthTokenExpiredCode:
			err = ErrLarkAuthFailed
		case NodeNotFoundCode, ResourceNotFoundCode, ResourceDeletedCode, APIFileDeletedCode, NotExist:
			err = ErrResourceNotFound
		case DocxFieldValidationErrorCode:
			err = fmt.Errorf("docx field validation error: %s", jsonx.ToString(codeErr.Err.FieldViolations))
		case DocUnauthorizedErrorCode:
			err = ErrLarkAuthUnauthorized
		case CanNotOperateOuterGroupErrorCode:
			err = ErrCanNotOperateOuterGroup
		default:
			if codeErr.Code != 0 {
				err = fmt.Errorf("[%v] %v", codeErr.Code, codeErr.Error())
			} else {
				err = nil
			}
		}
		return
	}
	return
}

func SleepSeveralSeconds(seconds int) {
	if seconds <= 0 { //  未指定休眠时间，则随机休眠 1-5s
		seconds = rand.Intn(5)
	}
	log.V1.CtxInfo(context.Background(), "sleep %d seconds", seconds)
	slp := time.Duration(seconds) * time.Second
	time.Sleep(slp)
}

// calculateExponentialBackoff calculates wait time using exponential backoff with jitter
// Formula: min(cap, base * 2^retryCount) + random jitter
func calculateExponentialBackoff(retryCount int) time.Duration {
	// 基础等待时间（毫秒）
	const baseDelayMs = 300
	// 最大等待时间（毫秒）
	const maxDelayMs = 10000

	// 计算指数退避时间
	delayMs := float64(baseDelayMs) * math.Pow(2, float64(retryCount-1))
	// 限制在最大等待时间内
	if delayMs > maxDelayMs {
		delayMs = maxDelayMs
	}

	// 添加随机抖动（±30%）以避免请求同步
	jitter := rand.Float64()*0.6 - 0.3 // -0.3 到 0.3 之间的随机值
	delayMs = delayMs * (1 + jitter)

	// 转换为时间并返回
	return time.Duration(delayMs) * time.Millisecond
}

// CreateLarkDocx creates a new Docx document in Lark (Feishu)
// API documentation: https://open.feishu.cn/document/server-docs/docs-docx-v1/document/create
func (c *client) CreateLarkDocx(ctx context.Context, folderToken, title, larkAccessToken string) (response *larkdocx.CreateDocumentRespData, err error) {
	tags := &metrics.LarkTag{Method: "CreateLarkDocx"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if larkAccessToken == "" {
		tenantToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
		accessToken = tenantToken
	}

	resp, err := RequestAndCheckError(ctx, func() (*larkdocx.CreateDocumentResp, any) {
		resp, err := c.larkCli.Docx.Document.Create(ctx,
			larkdocx.NewCreateDocumentReqBuilder().Body(
				larkdocx.NewCreateDocumentReqBodyBuilder().
					FolderToken(folderToken).
					Title(title).
					Build()).
				Build(),
			lo.Ternary(larkAccessToken == "", larkcore.WithTenantAccessToken(accessToken), larkcore.WithUserAccessToken(accessToken)),
		)

		if err != nil {
			logs.CtxError(ctx, "CreateLarkDocx Error = %v", err)
			return nil, err
		}
		return resp, &resp.CodeError
	})

	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[CreateLarkDocx] failed, err=%s", resp.Msg)
	}

	return resp.Data, nil
}

// DocxBlockDescendantsResponse represents the response from inserting block descendants
type DocxBlockDescendantsResponse struct {
	BlockIDRelations []struct {
		BlockID          string `json:"block_id"`
		TemporaryBlockID string `json:"temporary_block_id"`
	} `json:"block_id_relations"`
	Children           []*larkdocx.Block `json:"children"`
	ClientToken        string            `json:"client_token"`
	DocumentRevisionID int               `json:"document_revision_id"`
}

// InsertDocxBlockDescendants inserts descendant blocks into a document block
// API documentation: https://open.feishu.cn/document/server-docs/docs-docx-v1/document-block/insert-descendant
func (c *client) InsertDocxBlockDescendants(ctx context.Context, documentID, blockID string, index int, childrenID []string, descendants []*larkdocx.Block, larkAccessToken string) (*DocxBlockDescendantsResponse, error) {
	tags := &metrics.LarkTag{Method: "InsertDocxBlockDescendants"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	var err error
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get lark access token")
		}
	}

	type insertBlockDescendantsRequest struct {
		Index       int               `json:"index"`
		ChildrenID  []string          `json:"children_id"`
		Descendants []*larkdocx.Block `json:"descendants"`
	}

	type insertBlockDescendantsResponse struct {
		larkcore.CodeError
		Data *DocxBlockDescendantsResponse `json:"data"`
	}

	reqBody := &insertBlockDescendantsRequest{
		Index:       index,
		ChildrenID:  childrenID,
		Descendants: descendants,
	}

	// Construct query parameters for document_revision_id
	queryParams := larkcore.QueryParams{
		"document_revision_id": []string{"-1"},
		"client_token":         []string{uuid.New().String()},
	}

	// 重试逻辑：如果遇到 "unexpected end of JSON input" 错误，进行重试
	// 这个错误实际上是429错误，在飞书SDK修复之前先hardcode处理
	const maxRetries = 3
	var larkCodeErr error
	var result *insertBlockDescendantsResponse
	for i := 0; i < maxRetries; i++ {
		result, err = RequestAndCheckError(ctx, func() (*insertBlockDescendantsResponse, any) {
			resp, err := c.larkCli.Do(ctx,
				&larkcore.ApiReq{
					HttpMethod:                http.MethodPost,
					ApiPath:                   c.larkAPIBaseURL + "/open-apis/docx/v1/documents/" + documentID + "/blocks/" + blockID + "/descendant",
					Body:                      reqBody,
					QueryParams:               queryParams,
					PathParams:                nil,
					SupportedAccessTokenTypes: []larkcore.AccessTokenType{lo.Ternary(larkAccessToken != "", larkcore.AccessTokenTypeUser, larkcore.AccessTokenTypeTenant)},
				},
				lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)),
			)

			if err != nil {
				return nil, err
			}

			response := &insertBlockDescendantsResponse{}
			err = json.Unmarshal(resp.RawBody, response)
			if err != nil {
				return nil, err
			}
			larkCodeErr = &response.CodeError
			return response, &response.CodeError
		})

		// 如果没有错误，直接返回成功结果
		if err == nil {
			break
		}

		// 如果错误信息包含 "unexpected end of JSON input" 且还有重试次数，则重试
		if strings.Contains(err.Error(), "unexpected end of JSON input") && i < maxRetries-1 {
			time.Sleep(500 * time.Millisecond) // sleep 0.5秒
			continue
		}

		// 其他错误或重试次数用完，直接返回错误
		break
	}
	if larkCodeErr != nil && err != nil {
		return nil, larkCodeErr
	}

	if err != nil {
		return nil, err
	}

	return result.Data, nil
}

// API documentation: https://open.feishu.cn/document/server-docs/docs-docx-v1/document-block/batch_update
func (c *client) BatchUpdateDocxBlocks(ctx context.Context, documentID string, requests []*larkdocx.UpdateBlockRequest, larkAccessToken string) (resp *larkdocx.BatchUpdateDocumentBlockRespData, err error) {
	tags := &metrics.LarkTag{Method: "BatchUpdateDocxBlocks"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get lark access token")
		}
	}

	result, err := RequestAndCheckError(ctx, func() (*larkdocx.BatchUpdateDocumentBlockResp, any) {
		resp, err := c.larkCli.Docx.DocumentBlock.BatchUpdate(ctx,
			larkdocx.NewBatchUpdateDocumentBlockReqBuilder().
				DocumentId(documentID).
				DocumentRevisionId(-1).
				Body(larkdocx.NewBatchUpdateDocumentBlockReqBodyBuilder().
					Requests(requests).
					Build()).
				Build(),
			lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)),
		)
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})

	if err != nil {
		return nil, err
	}

	return result.Data, nil
}

// CreateImportTask creates an import task for files in Lark Drive: https://fsopen.bytedance.net/open-apis/drive/v1/import_tasks
func (c *client) CreateImportTask(ctx context.Context, req *larkdrive.CreateImportTaskReq, larkAccessToken string) (ticket *string, err error) {
	tags := &metrics.LarkTag{Method: "CreateImportTask"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	var accessToken = larkAccessToken
	if larkAccessToken == "" {
		tenantToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
		accessToken = tenantToken
	}
	resp, err := c.larkCli.Drive.ImportTask.Create(ctx, req, lo.Ternary(larkAccessToken == "", larkcore.WithTenantAccessToken(accessToken), larkcore.WithUserAccessToken(accessToken)))
	if err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return nil, fmt.Errorf("[CreateImportTask] failed, err=%s", resp.Msg)
	}

	return resp.Data.Ticket, nil
}

// GetImportTask retrieves the status of an import task: https://fsopen.bytedance.net/open-apis/drive/v1/import_tasks/:ticket
func (c *client) GetImportTask(ctx context.Context, taskID, larkAccessToken string) (response *larkdrive.GetImportTaskRespData, err error) {
	tags := &metrics.LarkTag{Method: "GetImportTask"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	if taskID == "" {
		return nil, errors.New("taskID can not be empty")
	}

	var accessToken = larkAccessToken
	if larkAccessToken == "" {
		tenantToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
		accessToken = tenantToken
	}

	result, err := RequestAndCheckError(ctx, func() (*larkdrive.GetImportTaskResp, any) {
		resp, err := c.larkCli.Drive.ImportTask.Get(ctx,
			larkdrive.NewGetImportTaskReqBuilder().
				Ticket(taskID).
				Build(),
			lo.Ternary(larkAccessToken == "", larkcore.WithTenantAccessToken(accessToken), larkcore.WithUserAccessToken(accessToken)))

		if err != nil {
			logs.CtxError(ctx, "GetImportTask Error = %v", err)
			return nil, err
		}
		return resp, &resp.CodeError
	})

	if err != nil {
		return nil, err
	}
	if !result.Success() {
		return nil, fmt.Errorf("[GetImportTask] failed, err=%s", result.Msg)
	}

	return result.Data, nil
}

// GetRootFolderMeta returns the metadata of the root folder.
// https://open.feishu.cn/open-apis/drive/explorer/v2/root_folder/meta
func (c *client) GetRootFolderMeta(ctx context.Context, operator string) (response *RootFolderMeta, err error) {
	tags := &metrics.LarkTag{Method: "GetRootFolderMeta"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	larkAccessToken, err := c.getUserAccessToken(ctx, operator)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark access token")
	}

	type rootFolderMetaResp struct {
		larkcore.CodeError
		Data *RootFolderMeta `json:"data"`
	}

	return RequestAndCheckError(ctx, func() (*RootFolderMeta, any) {
		resp, err := c.larkCli.Do(ctx,
			&larkcore.ApiReq{
				HttpMethod:                http.MethodGet,
				ApiPath:                   c.larkAPIBaseURL + "/open-apis/drive/explorer/v2/root_folder/meta",
				Body:                      nil,
				QueryParams:               nil,
				PathParams:                nil,
				SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeUser},
			},
			larkcore.WithUserAccessToken(larkAccessToken),
		)

		if err != nil {
			logs.CtxError(ctx, "GetRootFolderMeta Error = %v", err)
			return nil, err
		}

		response := &rootFolderMetaResp{}
		err = json.Unmarshal(resp.RawBody, response)
		if err != nil {
			return nil, err
		}
		return response.Data, &response.CodeError
	})
}

// TransferOwnerPermissionMember transfers ownership of a file or document.
// API documentation: https://open.feishu.cn/document/server-docs/docs/drive-v1/permission/permission-member/transfer-owner
func (c *client) TransferOwnerPermissionMember(ctx context.Context, req *larkdrive.TransferOwnerPermissionMemberReq, larkAccessToken string) (response *larkdrive.TransferOwnerPermissionMemberResp, err error) {
	tags := &metrics.LarkTag{Method: "TransferOwnerPermissionMember"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if larkAccessToken == "" {
		tenantToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
		accessToken = tenantToken
	}

	// Send the request with appropriate access token
	resp, err := c.larkCli.Drive.V1.PermissionMember.TransferOwner(
		ctx,
		req,
		lo.Ternary(larkAccessToken == "", larkcore.WithTenantAccessToken(accessToken), larkcore.WithUserAccessToken(accessToken)),
	)
	if err != nil {
		return nil, err
	}

	if !resp.Success() {
		return nil, fmt.Errorf("[TransferOwnerPermissionMember] failed, err=%s", resp.Msg)
	}

	return resp, nil
}

// ListPermissionMember transfers ownership of a file or document.
// API documentation: https://open.larkoffice.com/document/server-docs/docs/permission/permission-member/list
func (c *client) ListPermissionMember(ctx context.Context, req *larkdrive.ListPermissionMemberReq, larkAccessToken string) (response *larkdrive.ListPermissionMemberRespData, err error) {
	tags := &metrics.LarkTag{Method: "ListPermissionMember"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if larkAccessToken == "" {
		tenantToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
		accessToken = tenantToken
	}

	// Send the request with appropriate access token
	resp, err := c.larkCli.Drive.V1.PermissionMember.List(
		ctx,
		req,
		lo.Ternary(larkAccessToken == "", larkcore.WithTenantAccessToken(accessToken), larkcore.WithUserAccessToken(accessToken)),
	)
	if err != nil {
		return nil, err
	}

	if !resp.Success() {
		return nil, errors.Errorf("[ListPermissionMember] failed, err=%s", resp.Msg)
	}

	return resp.Data, nil
}

// BatchDeleteDocxBlockChildren deletes a range of blocks from a document
// API documentation: https://open.feishu.cn/document/server-docs/docs-docx-v1/document-block/batch_delete
func (c *client) BatchDeleteDocxBlockChildren(ctx context.Context, documentID, blockID string, startIndex, endIndex int, larkAccessToken string) (data *larkdocx.BatchDeleteDocumentBlockChildrenRespData, err error) {
	tags := &metrics.LarkTag{Method: "BatchDeleteDocxBlockChildren"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get lark access token")
		}
	}

	return RequestAndCheckError(ctx, func() (*larkdocx.BatchDeleteDocumentBlockChildrenRespData, any) {
		resp, err := c.larkCli.Docx.DocumentBlockChildren.BatchDelete(ctx, larkdocx.NewBatchDeleteDocumentBlockChildrenReqBuilder().
			DocumentId(documentID).
			BlockId(blockID).
			Body(larkdocx.NewBatchDeleteDocumentBlockChildrenReqBodyBuilder().
				StartIndex(startIndex).
				EndIndex(endIndex).
				Build(),
			).
			DocumentRevisionId(-1).
			Build(),
			lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)),
		)

		if err != nil {
			return nil, err
		}

		return resp.Data, &resp.CodeError
	})
}

// DownloadWhiteboardAsImage downloads a whiteboard as an image.
// https://open.larkoffice.com/open-apis/board/v1/whiteboards/:whiteboard_id/download_as_image
func (c *client) DownloadWhiteboardAsImage(ctx context.Context, whiteboardID string, larkAccessToken string) (*larkboard.DownloadAsImageWhiteboardResp, error) {
	tags := &metrics.LarkTag{Method: "DownloadWhiteboardAsImage"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err := recover(); err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
			logs.CtxError(ctx, "panic in DownloadWhiteboardAsImage: %v", err)
		}
	}()

	resp, err := c.larkCli.Board.V1.Whiteboard.DownloadAsImage(ctx, larkboard.NewDownloadAsImageWhiteboardReqBuilder().
		WhiteboardId(whiteboardID).
		Build(),
		larkcore.WithUserAccessToken(larkAccessToken),
	)

	if err != nil {
		logs.CtxError(ctx, "failed to download whiteboard as image, err=%v", err)
		return nil, err
	}

	if !resp.Success() {
		return nil, fmt.Errorf("[DownloadWhiteboardAsImage] failed, err=%s", resp.Msg)
	}

	return resp, nil
}

// DownloadLarkMedia downloads a file from Lark by its file token
// https://open.feishu.cn/document/server-docs/docs/drive-v1/media/download
func (c *client) DownloadLarkMedia(ctx context.Context, fileToken, larkAccessToken string) (*larkdrive.DownloadMediaResp, error) {
	tags := &metrics.LarkTag{Method: "DownloadLarkMedia"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err := recover(); err != nil {
			logs.CtxError(ctx, "panic in DownloadLarkMedia, err=%v", err)
		}
	}()

	req := larkdrive.NewDownloadMediaReqBuilder().
		FileToken(fileToken).
		Build()

	resp, err := c.larkCli.Drive.Media.Download(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
	if err != nil {
		return nil, err
	}

	if !resp.Success() {
		return nil, fmt.Errorf("[DownloadLarkMedia] failed, err=%s", resp.Msg)
	}

	return resp, nil
}

// CreateFolder creates a folder in Lark Drive
// https://open.larkoffice.com/document/server-docs/drive-v1/folder/create-folder
func (c *client) CreateFolder(ctx context.Context, name, folderToken string, option ...Option) (response *larkdrive.CreateFolderFileRespData, err error) {
	tags := &metrics.LarkTag{Method: "CreateFolder"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	accessToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tenant access token")
	}

	// Create request object
	req := larkdrive.NewCreateFolderFileReqBuilder().
		Body(larkdrive.NewCreateFolderFileReqBodyBuilder().
			Name(name).
			FolderToken(folderToken).
			Build()).
		Build()

	// Send request
	resp, err := c.larkCli.Drive.V1.File.CreateFolder(ctx, req, larkcore.WithTenantAccessToken(accessToken))
	if err != nil {
		return nil, err
	}

	// Handle server error
	if !resp.Success() {
		return nil, resp.CodeError
	}

	return resp.Data, nil
}

// ListFiles lists the files in the specified folder.
func (c *client) ListFiles(ctx context.Context, folderToken, pageToken string, pageSize int, orderBy, direction string, option ...Option) (response *larkdrive.ListFileRespData, err error) {
	tags := &metrics.LarkTag{Method: "ListFiles"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	accessToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tenant access token")
	}

	// Determine UserIDType from options or use default
	userIDType := "open_id" // Default
	for _, opt := range option {
		if opt.UserIDType != "" {
			userIDType = string(opt.UserIDType)
			break
		}
	}

	return RequestAndCheckError(ctx, func() (*larkdrive.ListFileRespData, any) {
		// Create the request builder
		reqBuilder := larkdrive.NewListFileReqBuilder().
			FolderToken(folderToken)

		// Add optional parameters if provided
		if pageSize > 0 {
			reqBuilder = reqBuilder.PageSize(pageSize)
		}
		if pageToken != "" {
			reqBuilder = reqBuilder.PageToken(pageToken)
		}
		if orderBy != "" {
			reqBuilder = reqBuilder.OrderBy(orderBy)
		}
		if direction != "" {
			reqBuilder = reqBuilder.Direction(direction)
		}

		reqBuilder = reqBuilder.UserIdType(userIDType)

		req := reqBuilder.Build()

		// Send the request
		resp, err := c.larkCli.Drive.V1.File.List(ctx, req, larkcore.WithTenantAccessToken(accessToken))
		if err != nil {
			return nil, err
		}

		return resp.Data, &resp.CodeError
	})
}

// CreateSheet creates a new sheet in Lark Sheets
// https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet/create?appId=cli_a75a41b019abd01c
func (c *client) CreateSheet(ctx context.Context, title string, folder string) (response *larksheets.CreateSpreadsheetRespData, err error) {
	tags := &metrics.LarkTag{Method: "CreateSheet"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	accessToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tenant access token")
	}
	return RequestAndCheckError(ctx, func() (*larksheets.CreateSpreadsheetRespData, any) {
		req := larksheets.NewCreateSpreadsheetReqBuilder().
			Spreadsheet(larksheets.NewSpreadsheetBuilder().
				Title(title).
				FolderToken(folder).
				Build()).
			Build()
		resp, err := c.larkCli.Sheets.V3.Spreadsheet.Create(context.Background(), req, larkcore.WithTenantAccessToken(accessToken))
		if err != nil {
			return nil, err
		}
		return resp.Data, &resp.CodeError
	})
}

func (c *client) QuerySheet(ctx context.Context, token string, accessToken string) (response *larksheets.QuerySpreadsheetSheetRespData, err error) {
	tags := &metrics.LarkTag{Method: "QuerySheet"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var tenantAccessToken string
	if accessToken == "" {
		tenantAccessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get lark access token")
		}
	}

	a, err := RequestAndCheckError(ctx, func() (*larksheets.QuerySpreadsheetSheetRespData, any) {
		req := larksheets.NewQuerySpreadsheetSheetReqBuilder().SpreadsheetToken(token).
			Build()
		resp, err := c.larkCli.Sheets.V3.SpreadsheetSheet.Query(context.Background(),
			req,
			lo.Ternary(accessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(tenantAccessToken)))
		if err != nil {
			return nil, err
		}
		return resp.Data, &resp.CodeError
	})
	return a, err
}

// GetLarkDocxBlocks get the lark document blocks produced by aime
func (c *client) GetLarkDocxBlocks(ctx context.Context, documentID string, larkAccessToken string) (resp []*larkdocx.Block, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkDocxBlocks"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get lark access token")
		}
	}
	hasMore := true
	maxPageSize := 500
	pageToken := ""
	docxBlocks := make([]*larkdocx.Block, 0)
	for hasMore {
		resp, err := RequestAndCheckError(ctx, func() (*larkdocx.ListDocumentBlockResp, any) {
			req := larkdocx.NewListDocumentBlockReqBuilder().
				DocumentId(documentID).
				PageSize(maxPageSize).
				PageToken(pageToken).
				DocumentRevisionId(-1).
				Build()
			resp, err := c.larkCli.Docx.DocumentBlock.List(ctx, req, lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)))

			if err != nil {
				return nil, err
			}

			return resp, &resp.CodeError
		})

		if err != nil {
			return nil, err
		}

		docxBlocks = append(docxBlocks, resp.Data.Items...)
		if resp.Data.PageToken != nil {
			pageToken = *resp.Data.PageToken
		}

		if resp.Data.HasMore != nil {
			hasMore = *resp.Data.HasMore
		} else {
			hasMore = false
		}
	}

	return docxBlocks, nil
}

func (c *client) GetLarkDocComments(ctx context.Context, solved bool, fileToken, fileType string, larkAccessToken string) (resp []*larkdrive.FileComment, err error) {
	tags := &metrics.LarkTag{Method: "GetLarkDocComments"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	accessToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tenant access token")
	}

	hasMore := true
	maxPageSize := 100
	pageToken := ""
	comments := make([]*larkdrive.FileComment, 0)

	for hasMore {
		resp, err := RequestAndCheckError(ctx, func() (*larkdrive.ListFileCommentResp, any) {
			req := larkdrive.NewListFileCommentReqBuilder().
				FileToken(fileToken).
				FileType(fileType).
				PageSize(maxPageSize).
				PageToken(pageToken).
				IsSolved(solved).
				Build()
			resp, err := c.larkCli.Drive.FileComment.List(ctx, req, lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(larkAccessToken), larkcore.WithTenantAccessToken(accessToken)))
			if err != nil {
				return nil, err
			}
			return resp, &resp.CodeError
		})
		if err != nil {
			return nil, err
		}
		comments = append(comments, resp.Data.Items...)
		if resp.Data.PageToken != nil {
			pageToken = *resp.Data.PageToken
		}
		if resp.Data.HasMore != nil {
			hasMore = *resp.Data.HasMore
		} else {
			hasMore = false
		}
	}

	return comments, nil
}

// ResolveLarkDocComment resolves or restores a comment in a Lark document
// API documentation: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/drive-v1/file-comment/patch
func (c *client) ResolveLarkDocComment(ctx context.Context, fileToken, fileType, commentID string, isSolved bool, larkAccessToken string) (*larkdrive.PatchFileCommentResp, error) {
	tags := &metrics.LarkTag{Method: "ResolveLarkDocComment"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()

	var accessToken = larkAccessToken
	if accessToken == "" {
		var err error
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
			return nil, errors.WithMessage(err, "failed to get lark access token")
		}
	}

	return RequestAndCheckError(ctx, func() (*larkdrive.PatchFileCommentResp, any) {
		req := larkdrive.NewPatchFileCommentReqBuilder().
			FileToken(fileToken).
			CommentId(commentID).
			FileType(fileType).
			Body(larkdrive.NewPatchFileCommentReqBodyBuilder().
				IsSolved(isSolved).
				Build()).
			Build()

		resp, err := c.larkCli.Drive.FileComment.Patch(ctx, req, lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)))
		if err != nil {
			return nil, err
		}
		return resp, resp.CodeError.Err
	})
}

func (c *client) ListBitableAppTable(ctx context.Context, appToken string, pageToken string, pageSize int, accessToken string) (response *larkbitable.ListAppTableRespData, err error) {
	// About the API: https://open.larkoffice.com/document/server-docs/docs/bitable-v1/app-table/list
	tags := &metrics.LarkTag{Method: "ListBitableAppTable"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	a, err := RequestAndCheckError(ctx, func() (*larkbitable.ListAppTableRespData, any) {
		req :=
			larkbitable.NewListAppTableReqBuilder().AppToken(appToken).PageToken(pageToken).PageSize(pageSize).Build()
		resp, err := c.larkCli.Bitable.AppTable.List(context.Background(), req, larkcore.WithUserAccessToken(accessToken))
		if err != nil {
			return nil, err
		}
		return resp.Data, &resp.CodeError
	})
	return a, err
}

func (c *client) SearchBitableRecord(ctx context.Context, appToken string, tableID string, userIDType string, pageToken string, pageSize int, accessToken string) (response *larkbitable.SearchAppTableRecordRespData, err error) {
	// About the API: https://open.larkoffice.com/document/docs/bitable-v1/app-table-record/search
	tags := &metrics.LarkTag{Method: "SearchBitableRecord"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	if len(userIDType) == 0 {
		userIDType = larkcontact.UserIdTypeOpenId
	}
	a, err := RequestAndCheckError(ctx, func() (*larkbitable.SearchAppTableRecordRespData, any) {
		req :=
			larkbitable.NewSearchAppTableRecordReqBuilder().AppToken(appToken).TableId(tableID).Body(larkbitable.NewSearchAppTableRecordReqBodyBuilder().Build()).PageToken(pageToken).PageSize(pageSize).Build()
		resp, err := c.larkCli.Bitable.V1.AppTableRecord.Search(context.Background(), req, larkcore.WithUserAccessToken(accessToken))
		if err != nil {
			return nil, err
		}
		return resp.Data, &resp.CodeError
	})
	return a, err
}

func (c *client) ListAppTableField(ctx context.Context, appToken string, tableID string, pageToken string, pageSize int, accessToken string) (response *larkbitable.ListAppTableFieldRespData, err error) {
	// About the API: https://open.larkoffice.com/document/server-docs/docs/bitable-v1/app-table-field/list?appId=cli_a752fda7a2f9d01c
	tags := &metrics.LarkTag{Method: "ListAppTableField"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	a, err := RequestAndCheckError(ctx, func() (*larkbitable.ListAppTableFieldRespData, any) {
		req := larkbitable.NewListAppTableFieldReqBuilder().
			AppToken(appToken).
			TableId(tableID).
			TextFieldAsArray(true).
			PageToken(pageToken).
			PageSize(pageSize).
			Build()
		resp, err := c.larkCli.Bitable.V1.AppTableField.List(context.Background(), req, larkcore.WithUserAccessToken(accessToken))
		if err != nil {
			return nil, err
		}
		return resp.Data, &resp.CodeError
	})
	return a, err
}

func (c *client) GetSheetMarkdownContent(ctx context.Context, spreadsheetToken string, sheetID string, ranges []string, larkAccessToken string) (response *SheetMarkdownData, err error) {
	// About the API:https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet-sheet-value/batch_get
	tags := &metrics.LarkTag{Method: "GetSheetMarkdownContent"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	reqBody := map[string][]string{
		"ranges": ranges,
	}
	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
	}
	result, err := RequestAndCheckError(ctx, func() (*SheetMarkdownData, any) {
		resp, err := c.larkCli.Post(ctx,
			fmt.Sprintf("/open-apis/sheets/v3/spreadsheets/%s/sheets/%s/values/batch_get?user_id_type=open_id&value_render_option=unformatted_value", spreadsheetToken, sheetID),
			reqBody,
			lo.Ternary(larkAccessToken != "", larkcore.AccessTokenTypeUser, larkcore.AccessTokenTypeTenant),
			lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)))
		if err != nil {
			return nil, err
		}

		var sheetResponse struct {
			Code int               `json:"code"`
			Data SheetMarkdownData `json:"data"`
			Msg  string            `json:"msg"`
		}
		err = json.Unmarshal(resp.RawBody, &sheetResponse)
		if err != nil {
			return nil, err
		}
		if sheetResponse.Code != 0 {
			return nil, errors.New(sheetResponse.Msg)
		}
		return &sheetResponse.Data, nil
	})
	return result, err
}

// BatchUpdateSheetValues 批量更新电子表格中的值 (v3 API)
// API documentation: https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet-sheet-value/batch_update
func (c *client) BatchUpdateSheetValues(ctx context.Context, spreadsheetToken, sheetID string, valueRanges []*ValueRange, userIDType string, larkAccessToken string) (response *BatchUpdateSheetValuesRespData, err error) {
	tags := &metrics.LarkTag{Method: "BatchUpdateSheetValues"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
	}

	if userIDType == "" {
		userIDType = "open_id"
	}

	reqBody := &BatchUpdateSheetValuesRequest{
		ValueRanges: valueRanges,
	}

	result, err := RequestAndCheckError(ctx, func() (*BatchUpdateSheetValuesRespData, any) {
		apiPath := fmt.Sprintf("/open-apis/sheets/v3/spreadsheets/%s/sheets/%s/values/batch_update?user_id_type=%s", spreadsheetToken, sheetID, userIDType)

		resp, err := c.larkCli.Post(ctx,
			apiPath,
			reqBody,
			lo.Ternary(larkAccessToken != "", larkcore.AccessTokenTypeUser, larkcore.AccessTokenTypeTenant),
			lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)))

		if err != nil {
			return nil, err
		}

		var sheetResponse struct {
			larkcore.CodeError
			Data *BatchUpdateSheetValuesRespData `json:"data"`
		}
		err = json.Unmarshal(resp.RawBody, &sheetResponse)
		if err != nil {
			return nil, err
		}
		return sheetResponse.Data, &sheetResponse.CodeError
	})

	return result, err
}

// MergeLarkSheetCells 合并 Lark Sheet 单元格
func (c *client) MergeLarkSheetCells(ctx context.Context, spreadsheetToken, sheetID string, mergeCells []*larksheets.MergeCell, larkAccessToken string) (response *MergeCellsRespData, err error) {
	tags := &metrics.LarkTag{Method: "MergeLarkSheetCells"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	var accessToken = larkAccessToken
	if accessToken == "" {
		accessToken, err = c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get tenant access token")
		}
	}

	reqBody := &MergeCellsRequest{
		MergeCells: mergeCells,
	}

	result, err := RequestAndCheckError(ctx, func() (*MergeCellsRespData, any) {
		apiPath := fmt.Sprintf("/open-apis/sheets/v3/spreadsheets/%s/sheets/%s/merge_cells", spreadsheetToken, sheetID)

		resp, err := c.larkCli.Post(ctx,
			apiPath,
			reqBody,
			lo.Ternary(larkAccessToken != "", larkcore.AccessTokenTypeUser, larkcore.AccessTokenTypeTenant),
			lo.Ternary(larkAccessToken != "", larkcore.WithUserAccessToken(accessToken), larkcore.WithTenantAccessToken(accessToken)))

		if err != nil {
			return nil, err
		}

		var sheetResponse struct {
			larkcore.CodeError
			Data *MergeCellsRespData `json:"data"`
		}
		err = json.Unmarshal(resp.RawBody, &sheetResponse)
		if err != nil {
			return nil, err
		}
		return sheetResponse.Data, &sheetResponse.CodeError
	})

	return result, err
}

// GetAuthenUserInfo 获取用户认证信息(只能使用user_access_token)
func (c *client) GetAuthenUserInfo(ctx context.Context, userAccessToken string) (response *UserInfo, err error) {
	tags := &metrics.LarkTag{Method: "GetAuthenUserInfo"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	if userAccessToken == "" {
		return nil, errors.New("userAccessToken cannot be empty")
	}

	return RequestAndCheckError(ctx, func() (*UserInfo, any) {
		resp, err := c.larkCli.Authen.V1.UserInfo.Get(ctx, larkcore.WithUserAccessToken(userAccessToken))
		if err != nil {
			return nil, err
		}

		if !resp.Success() {
			return nil, resp.CodeError
		}

		userInfo := &UserInfo{}
		if resp.Data != nil {
			userInfo.UserID = lo.FromPtr(resp.Data.UserId)
			userInfo.UnionID = lo.FromPtr(resp.Data.UnionId)
			userInfo.OpenID = lo.FromPtr(resp.Data.OpenId)
			userInfo.Name = lo.FromPtr(resp.Data.Name)
			userInfo.EnName = lo.FromPtr(resp.Data.EnName)
			userInfo.Email = lo.FromPtr(resp.Data.Email)
			userInfo.Mobile = lo.FromPtr(resp.Data.Mobile)
			userInfo.TenantKey = lo.FromPtr(resp.Data.TenantKey)
			userInfo.AvatarURL = lo.FromPtr(resp.Data.AvatarUrl)
			userInfo.AvatarThumb = lo.FromPtr(resp.Data.AvatarThumb)
			userInfo.AvatarMiddle = lo.FromPtr(resp.Data.AvatarMiddle)
			userInfo.AvatarBig = lo.FromPtr(resp.Data.AvatarBig)
		}

		return userInfo, nil
	})
}

// CreateWhiteboardPlantUMLNodeRequest represents the request body for creating a PlantUML node
type CreateWhiteboardPlantUMLNodeRequest struct {
	PlantUMLCode string `json:"plant_uml_code"`
	StyleType    int    `json:"style_type"`
	SyntaxType   int    `json:"syntax_type"`
	DiagramType  int    `json:"diagram_type"`
}

// CreateWhiteboardPlantUMLNodeResponse represents the response from creating a PlantUML node
type CreateWhiteboardPlantUMLNodeResponse struct {
	larkcore.CodeError
	Data interface{} `json:"data"`
}

// CreateWhiteboardPlantUMLNode creates a PlantUML node in a whiteboard
func (c *client) CreateWhiteboardPlantUMLNode(ctx context.Context, whiteboardID string, req *CreateWhiteboardPlantUMLNodeRequest) (response interface{}, err error) {
	tags := &metrics.LarkTag{Method: "CreateWhiteboardPlantUMLNode"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	if req == nil {
		return nil, errors.New("request cannot be nil")
	}

	tenantToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tenant access token")
	}

	result, err := RequestAndCheckError(ctx, func() (*CreateWhiteboardPlantUMLNodeResponse, any) {
		resp, err := c.larkCli.Do(ctx,
			&larkcore.ApiReq{
				HttpMethod:                http.MethodPost,
				ApiPath:                   c.larkAPIBaseURL + "/open-apis/board/v1/whiteboards/" + whiteboardID + "/nodes/plantuml",
				Body:                      req,
				QueryParams:               nil,
				PathParams:                nil,
				SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant},
			},
			larkcore.WithTenantAccessToken(tenantToken),
		)

		if err != nil {
			logs.CtxError(ctx, "CreateWhiteboardPlantUMLNode Error = %v", err)
			return nil, err
		}

		response := &CreateWhiteboardPlantUMLNodeResponse{}
		err = json.Unmarshal(resp.RawBody, response)
		if err != nil {
			return nil, err
		}
		return response, &response.CodeError
	})
	if err != nil {
		return nil, err
	}
	return result.Data, nil
}

func (c *client) JudgeBotInGroup(ctx context.Context, chatID string) (inGroup bool, err error) {
	tags := &metrics.LarkTag{Method: "JudgeBotInGroup"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return inGroup, err
	}
	logs.CtxInfo(ctx, "[JudgeBotInGroup] chatID is %v", chatID)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.IsInChatChatMembersResp, any) {
		req := larkim.NewIsInChatChatMembersReqBuilder().ChatId(chatID).Build()

		resp, err := c.larkCli.Im.V1.ChatMembers.IsInChat(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return inGroup, err
	}
	if !resp.Success() {
		return inGroup, fmt.Errorf("[JudgeBotInGroup] failed, err=%s", resp.Msg)
	}

	if resp.Data != nil {
		inGroup = lo.FromPtr(resp.Data.IsInChat)
	}

	return inGroup, nil
}

func (c *client) JudgeUserInGroup(ctx context.Context, userAccessToken, chatID string) (inGroup bool, err error) {
	tags := &metrics.LarkTag{Method: "JudgeUserInGroup"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	logs.CtxInfo(ctx, "[JudgeUserInGroup] chatID is %v", chatID)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.IsInChatChatMembersResp, any) {
		req := larkim.NewIsInChatChatMembersReqBuilder().ChatId(chatID).Build()

		resp, err := c.larkCli.Im.V1.ChatMembers.IsInChat(ctx, req, larkcore.WithUserAccessToken(userAccessToken))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return inGroup, err
	}
	if !resp.Success() {
		return inGroup, fmt.Errorf("[JudgeUserInGroup] failed, err=%s", resp.Msg)
	}

	if resp.Data != nil {
		inGroup = lo.FromPtr(resp.Data.IsInChat)
	}

	return inGroup, nil
}

// SearchGroups 搜索对用户或机器人可见的群列表
// API documentation: https://open.larkoffice.com/document/server-docs/group/chat/search
func (c *client) SearchGroups(ctx context.Context, userIDType UserIDType, larkAccessToken, query string, pageToken string, pageSize int) (response *SearchGroupsRespData, err error) {
	tags := &metrics.LarkTag{Method: "SearchGroups"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	// 默认获取50个群的信息
	if pageSize == 0 {
		pageSize = 50
	}

	logs.CtxInfo(ctx, "[SearchGroups] query is %v, userIDType is %v", query, userIDType)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.SearchChatResp, any) {
		req := larkim.NewSearchChatReqBuilder().
			UserIdType(string(userIDType)).
			Query(query).
			PageToken(pageToken).
			PageSize(pageSize).
			Build()
		resp, err := c.larkCli.Im.V1.Chat.Search(ctx, req, larkcore.WithUserAccessToken(larkAccessToken))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[SearchGroups] failed, err=%s", resp.Msg)
	}

	groupInfo := &SearchGroupsRespData{}
	if resp.Data != nil {
		groupInfo.PageToken = lo.FromPtr(resp.Data.PageToken)
		groupInfo.HasMore = lo.FromPtr(resp.Data.HasMore)
		groupInfo.Items = make([]GroupItems, 0, len(resp.Data.Items))
		groupInfo.Items = lo.Map(resp.Data.Items, func(item *larkim.ListChat, _ int) GroupItems {
			if item == nil {
				return GroupItems{}
			}
			return GroupItems{
				Avatar:      lo.FromPtr(item.Avatar),
				ChatId:      lo.FromPtr(item.ChatId),
				ChatStatus:  lo.FromPtr(item.ChatStatus),
				Description: lo.FromPtr(item.Description),
				External:    lo.FromPtr(item.External),
				Name:        lo.FromPtr(item.Name),
				TenantKey:   lo.FromPtr(item.TenantKey),
			}
		})
		// 过滤掉空的 GroupItems（ChatId 为空的视为无效）
		groupInfo.Items = lo.Filter(groupInfo.Items, func(item GroupItems, _ int) bool {
			return item.ChatId != ""
		})
	}

	return groupInfo, nil
}

// CreateGroup 创建群
// API documentation: https://open.larkoffice.com/document/server-docs/group/chat/create
func (c *client) CreateGroup(ctx context.Context, userIDType UserIDType, groupName string, ownerID string,
	userIDList []string) (response *larkim.CreateChatRespData, err error) {
	tags := &metrics.LarkTag{Method: "CreateGroup"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()

	token, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[CreateGroup] groupName is %v, userIDList is %v", groupName, userIDList)
	resp, err := RequestAndCheckError(ctx, func() (*larkim.CreateChatResp, any) {
		req := larkim.NewCreateChatReqBuilder().
			UserIdType(string(userIDType)).
			Body(larkim.NewCreateChatReqBodyBuilder().
				Name(groupName).
				OwnerId(ownerID).
				UserIdList(userIDList).Build()).
			Build()

		resp, err := c.larkCli.Im.V1.Chat.Create(ctx, req, larkcore.WithTenantAccessToken(token))
		if err != nil {
			return nil, err
		}
		return resp, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		return nil, fmt.Errorf("[CreateGroup] failed, err=%s", resp.Msg)
	}

	return resp.Data, nil
}

type CreateFileVersionParams struct {
	FileToken       string
	UserIDType      string
	VersionName     string
	ObjType         string
	LarkAccessToken string
}

type CreateFileVersionRespData struct {
	Name *string `json:"name,omitempty"` // 版本文档标题，最大长度 1024 个Unicode 码点。通常情况下，一个英文或中文字符对应一个码点，但是某些特殊符号可能会对应多个码点。例如，家庭组合「👨‍👩‍👧」这个表情符号对应5个码点。

	Version *string `json:"version,omitempty"` // 版本文档版本号

	ParentToken *string `json:"parent_token,omitempty"` // 源文档token

	OwnerId *string `json:"owner_id,omitempty"` // 版本文档所有者id

	CreatorId *string `json:"creator_id,omitempty"` // 版本文档创建者id

	CreateTime *string `json:"create_time,omitempty"` // 版本文档创建时间

	UpdateTime *string `json:"update_time,omitempty"` // 版本文档更新时间

	Status *int `json:"status,omitempty"` // 版本文档状态

	ObjType *string `json:"obj_type,omitempty"` // 版本文档类型

	ParentType *string `json:"parent_type,omitempty"` // 源文档类型
}

type CreateFileVersionResp struct {
	Code int `json:"code"` // 错误码

	Msg string `json:"msg"` // 错误描述

	Data *CreateFileVersionRespData `json:"data"` // 业务数据
}

func (c *client) CreateFileVersion(ctx context.Context, params *CreateFileVersionParams) (fileVersion *CreateFileVersionRespData, err error) {
	tags := &metrics.LarkTag{Method: "CreateFileVersion"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	accessToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tenant access token")
	}

	resp, err := c.larkCli.Do(ctx,
		&larkcore.ApiReq{
			HttpMethod: http.MethodPost,
			ApiPath:    fmt.Sprintf("%s/open-apis/drive/v1/files/%s/versions", larkAPIBaseURL, params.FileToken),
			Body: map[string]any{
				"name":     params.VersionName,
				"obj_type": params.ObjType,
			},
			QueryParams:               larkcore.QueryParams{"user_id_type": []string{lo.Ternary(params.UserIDType != "", params.UserIDType, "open_id")}},
			SupportedAccessTokenTypes: []larkcore.AccessTokenType{larkcore.AccessTokenTypeTenant, larkcore.AccessTokenTypeUser},
		},
		lo.Ternary(params.LarkAccessToken != "", larkcore.WithUserAccessToken(params.LarkAccessToken), larkcore.WithTenantAccessToken(accessToken)),
	)

	if err != nil {
		return nil, errors.WithMessage(err, "request lark create file version")
	}

	result := &CreateFileVersionResp{}
	err = json.Unmarshal(resp.RawBody, &result)
	if err != nil {
		return nil, errors.Wrap(err, "create file version unmarshal resp body failed")
	}
	if result.Code != 0 {
		return nil, errors.New(result.Msg)
	}
	fileVersion = result.Data
	return
}

func (c *client) GetUserInfoById(ctx context.Context, userID, userIDType string) (resp *larkcontact.GetUserRespData, err error) {
	tags := &metrics.LarkTag{Method: "GetUserInfoById"}
	_ = metrics.M.LarkRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.M.LarkTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.M.LarkError.WithTags(tags).Add(1)
		}
	}()
	accessToken, err := c.getTenantAccessToken(ctx, c.larkID, c.larkSecret)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tenant access token")
	}
	resp, err = RequestAndCheckError(ctx, func() (*larkcontact.GetUserRespData, any) {
		req := larkcontact.NewGetUserReqBuilder().
			UserIdType(lo.Ternary(userIDType != "", userIDType, "open_id")).
			UserId(userID).
			Build()
		resp, err := c.larkCli.Contact.V3.User.Get(ctx, req, larkcore.WithTenantAccessToken(accessToken))
		if err != nil {
			return nil, err
		}
		return resp.Data, &resp.CodeError
	})
	if err != nil {
		return nil, err
	}
	return
}
