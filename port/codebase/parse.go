package codebase

import (
	"regexp"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

// RefsHeadPrefix is the prefix of git branch name.
const RefsHeadPrefix = "refs/heads/"

type ParsedGitURL struct {
	RepoName string `json:"repo_name"`

	Env string `json:"env"`
}

var (
	// gitlab ssh url: ******************:devgpt/kiwis.git
	gitlabSSHURLReg = regexp.MustCompile(`^git@code(?P<boe>-boe)?.byted.org:(?P<repo>.*).git$`)
	// gitlab ssh url(from gitlab mq event): ssh://******************:devgpt/kiwis.git
	gitlabSSHHeadURLReg = regexp.MustCompile(`^ssh://git@code(?P<boe>-boe)?.byted.org:(?P<repo>.*).git$`)
	// gitlab http url: https://code.byted.org/devgpt/kiwis.git
	gitlabHTTPURLReg = regexp.MustCompile(`^https://code(?P<boe>-boe)?.byted.org/(?P<repo>.*).git$`)
	// gerrit ssh url: ssh://*****************:29418/ee/lark/rpc-interface.git
	gerritSSHURL = regexp.MustCompile(`^ssh://git@git(?P<boe>-boe)?.byted.org:29418/(?P<repo>.*).git$`)
	// gerrit http url: https://review.byted.org/ee/lark/rpc-interface.git
	gerritHTTPURL = regexp.MustCompile(`^https://review(?P<boe>-boe)?.byted.org/(?P<repo>.*).git$`)
	// gitlab repo url: https://code.byted.org/devgpt/kiwis
	// gerrit repo url: https://code.byted.org/gerrit/ee/lark/rpc-interface
	gitlabRepoURL = regexp.MustCompile(`^https://code(?P<boe>-boe)?.byted.org/(?P<gerrit>gerrit/)?(?P<repo>.*)$`)
	// gitlab url
	gitlabOpenSourceURL = regexp.MustCompile(`^<EMAIL>:(?P<repo>.*)$`)
	// bits code http url: https://bits.bytedance.net/code/devinfra/devapis
	bitsCodeHTTPURLReg = regexp.MustCompile(`^https://bits.bytedance.net/code/(?P<repo>.*)$`)
)

func matchGitURL(u string, reg *regexp.Regexp, v map[string]string) (matched bool) {
	matched = reg.MatchString(u)
	if !matched {
		return
	}
	matches := reg.FindStringSubmatch(u)
	matchNames := reg.SubexpNames()
	for i, name := range matchNames {
		if i > 0 && i < len(matches) && len(name) > 0 {
			v[name] = matches[i]
		}
	}

	return matched
}

func ParseGitURL(gitURL string) (*ParsedGitURL, error) {
	gitURL = strings.Trim(gitURL, " \n\r\t")

	v := map[string]string{}
	switch {
	case matchGitURL(gitURL, gitlabSSHURLReg, v):
	case matchGitURL(gitURL, gitlabSSHHeadURLReg, v):
	case matchGitURL(gitURL, gitlabHTTPURLReg, v):
	case matchGitURL(gitURL, gerritSSHURL, v):
	case matchGitURL(gitURL, gerritHTTPURL, v):
	case matchGitURL(gitURL, gitlabRepoURL, v):
	case matchGitURL(gitURL, gitlabOpenSourceURL, v):
	default:
		return nil, errors.New("not recognized Git URL")
	}

	pu := &ParsedGitURL{
		RepoName: v["repo"],
		Env:      lo.Ternary(len(v["boe"]) > 0, "boe", "online"),
	}

	return pu, nil
}

var (
	gitlabMRURLReg       = regexp.MustCompile(`^https://code(?P<boe>-boe)?\.byted\.org/(?P<repo>.*)/merge_requests/\d+.*$`)
	gitlabRepoFileURLReg = regexp.MustCompile(`^https://code(?P<boe>-boe)?\.byted\.org/(?P<repo>.*)/blob/.*$`)
	gitlabRepoURLReg     = regexp.MustCompile(`^https://code(?P<boe>-boe)?\.byted\.org/[\w_\-.]+/[\w_\-.]+/?$`)
)

const (
	SubPageMergeRequest = "merge_request" // MR page.
	SubPageRepoFile     = "repo_file"     // Repo file page.
	SubPageRepo         = "repo"          // Repo main page.
)

func GetCodebaseSubPageFromURL(u string) (string, error) {
	u = strings.Trim(u, " \n\r\t")

	switch {
	case gitlabMRURLReg.MatchString(u):
		return SubPageMergeRequest, nil
	case gitlabRepoFileURLReg.MatchString(u):
		return SubPageRepoFile, nil
	case gitlabRepoURLReg.MatchString(u):
		return SubPageRepo, nil
	default:
		return "", errors.New("undefined page")
	}
}

func ParseRepoNameFromURL(url string) (string, error) {
	url = strings.Trim(url, " \n\r\t")

	v := map[string]string{}
	switch {
	case matchGitURL(url, gitlabSSHURLReg, v):
	case matchGitURL(url, gitlabSSHHeadURLReg, v):
	case matchGitURL(url, gitlabHTTPURLReg, v):
	case matchGitURL(url, gerritSSHURL, v):
	case matchGitURL(url, gerritHTTPURL, v):
	case matchGitURL(url, gitlabRepoURL, v):
	case matchGitURL(url, gitlabOpenSourceURL, v):
	case matchGitURL(url, bitsCodeHTTPURLReg, v):
	default:
		return "", errors.New("not recognized URL")
	}

	repoName, ok := v["repo"]
	if !ok {
		return "", errors.New("repo not found")
	}
	return repoName, nil
}
