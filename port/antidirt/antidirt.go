package antidirt

import (
	"context"
	"os"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	kclient "code.byted.org/kite/kitex/client"
	"code.byted.org/overpass/data_nlp_antidirt/kitex_gen/base"
	"code.byted.org/overpass/data_nlp_antidirt/kitex_gen/content/arch/antidirt"
	"code.byted.org/overpass/data_nlp_antidirt/kitex_gen/content/arch/antidirt/antidirtservice"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
)

const (
	SceneDefault = "default"

	SceneCodeverseChat    = "codeverse_chat"
	SceneGenerateUnittest = "generate_unittest"

	SceneCodeverseCompletion  = "codeverse_completion"
	SceneCodeCompletionStream = "code_completion_stream"
)

type Antidirt interface {
	MatchRulesPart(ctx context.Context, content string, scene string) (bool, error)
	MatchRules(ctx context.Context, content string, scene string) (bool, error)
	GetSeparator() string
}

type AntidirtCli struct {
	Client         antidirtservice.Client
	antidirtConfig *libtcc.GenericConfig[config.AntidirtConfig]
}

func NewAntidirtCli(antidirtConfig *libtcc.GenericConfig[config.AntidirtConfig]) (*AntidirtCli, error) {
	antidirtConf := antidirtConfig.GetPointer()
	client, err := antidirtservice.NewClient(antidirtConf.PSM)
	if err != nil {
		return nil, err
	}
	log.V1.Info("Antidirt loaded: %+v", antidirtConf)

	return &AntidirtCli{
		Client:         client,
		antidirtConfig: antidirtConfig,
	}, nil
}

func NewAntidirt(antidirtConfig *libtcc.GenericConfig[config.AntidirtConfig]) (Antidirt, error) {
	antidirtConf := antidirtConfig.GetPointer()
	opts := []kclient.Option{kclient.WithCluster(antidirtConf.Cluster)}
	if antidirtConfig.GetValue().Timeout > 0 {
		opts = append(opts, kclient.WithRPCTimeout(time.Millisecond*time.Duration(antidirtConfig.GetValue().Timeout)))
	}
	client, err := antidirtservice.NewClient(
		antidirtConf.PSM,
		opts...,
	)
	if err != nil {
		return nil, err
	}
	log.V1.Info("Antidirt loaded: %+v", antidirtConf)

	return &AntidirtCli{
		Client:         client,
		antidirtConfig: antidirtConfig,
	}, nil
}

// 用于分析 AntiDirt 误伤情况
type matchedWordDetail struct {
	HitDetail  *antidirt.WordMatch `json:"hit_detail"`
	TextAround string              `json:"text_around"`
	Table      *antidirt.Table     `json:"table"`
	Rule       *antidirt.Rule      `json:"rule"`
	Scene      string              `json:"scene"`
	Extra      map[string]string   `json:"extra"`
}

func (c *AntidirtCli) MatchRulesPart(ctx context.Context, content string, scene string) (bool, error) {
	antidirtConf := c.antidirtConfig.GetPointer()

	tableIds := antidirtConf.TableIDs
	whiteTableIds := antidirtConf.WhiteTableIDs
	sceneConf, found := lo.Find(antidirtConf.Scenes, func(item *config.AntidirtInnerStructure) bool {
		return item.Scene == scene
	})
	if found {
		tableIds = sceneConf.TableIDs
		whiteTableIds = sceneConf.WhiteTableIDs
	}

	req := antidirt.MatchRulesReq{
		Text:             content,
		TableIds:         tableIds,
		ReturnAllMatches: true,
		Base:             &base.Base{Caller: antidirtConf.Caller}, // antidirt base必填
		WhiteTableIds:    whiteTableIds,
	}

	resp, err := c.Client.MatchRules(ctx, &req)
	if err != nil {
		return false, err
	}
	if len(resp.WordMatches) > 0 && resp.Rules != nil {
		details := getHitDetail(ctx, content, scene, resp)
		if os.Getenv("ENABLE_ANTIDIRT_DETAIL_LOG") == "1" {
			// TODO(cyx): Remove this after IDE antidirt hit analysis is done, as this brings many extra logs.
			log.V1.CtxInfo(ctx, "[Antidirt] antidirt hit detail: %s", conv.JSONString(details))
		} else {
			log.V1.CtxInfo(ctx, "[Antidirt] matched sensitive rules, matched content is %+v, matched rule is %+v", resp.WordMatches, resp.Rules)
		}
	}
	return len(resp.WordMatches) > 0, nil
}

const (
	maxPartSize = 20000
	maxParallel = 5
)

func splitContent(content string, maxSize int) []string {
	var parts []string
	for idx := 0; idx < len(content); idx += maxSize {
		var (
			start = idx
			end   = idx + maxSize
		)
		if end > len(content) {
			end = len(content)
		}
		parts = append(parts, content[start:end])
	}
	return parts
}

func (c *AntidirtCli) MatchRules(ctx context.Context, content string, scene string) (bool, error) {
	parts := splitContent(content, maxPartSize)
	// defer func(start time.Time) {
	//	log.V1.CtxInfo(ctx, "antidirt costs %dms", time.Since(start).Milliseconds())
	// }(time.Now())
	g := errgroup.Group{}
	g.SetLimit(maxParallel)
	results := make([]bool, len(parts))
	for idx := range parts {
		g.Go(func() error {
			matched, err := c.MatchRulesPart(ctx, parts[idx], scene)
			if err != nil {
				return errors.WithMessagef(err, "failed to check part %d", idx)
			}
			results[idx] = matched
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return false, err
	}

	result := false
	for idx := range results {
		result = result || results[idx]
	}

	return result, nil
}

func (c *AntidirtCli) GetSeparator() string {
	return "&"
}

const aroundSize = 100

func getHitDetail(ctx context.Context, content, scene string, res *antidirt.MatchRulesRsp) []matchedWordDetail {
	if res == nil {
		return nil
	}

	details := make([]matchedWordDetail, len(res.WordMatches))
	for idx, match := range res.WordMatches {
		detail := matchedWordDetail{
			HitDetail:  match,
			TextAround: "",
			Table:      nil,
			Rule:       nil,
			Scene:      scene,
			Extra:      map[string]string{},
		}
		if table, ok := res.Tables[match.TableId]; ok {
			detail.Table = table
		}
		if rule, ok := res.Rules[match.RuleId]; ok {
			detail.Rule = rule
		}
		hitIdx := strings.Index(content, match.GetWord())
		detail.TextAround = content[max(hitIdx-aroundSize, 0):min(hitIdx+aroundSize, len(content))]
		details[idx] = detail
	}

	return details
}
