package toutiaosearch

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logid"
	"github.com/pkg/errors"

	"code.byted.org/gopkg/env"
	"code.byted.org/overpass/seed_plugin_proxy/kitex_gen/seed/plugin/proxy"
	"code.byted.org/overpass/seed_plugin_proxy/rpc/seed_plugin_proxy"

	c "code.byted.org/kite/kitex/client"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/tcc"
)

var (
	pluginNameSearch     = "SearchPlugin"
	pluginID             = "47"
	toolNameSearchPlugin = "SearchPlugin"
	PictureTypeSearch    = "image_search"
)

type ruyiControl struct {
	UseRuyi bool `json:"use_ruyi"`
}

type textControl struct {
	GetFullText         bool `json:"get_full_text"`
	HasIntent           bool `json:"has_intent"`
	GetAbaseCoreContent bool `json:"get_abase_core_content"`
}

type ImageCtrl struct {
	Intent string `json:"intent"`
}

type Thought struct {
	InputQuery   string       `json:"input_query"`
	PluginPrompt bool         `json:"plugin_prompt"`
	Cursor       int          `json:"cursor"`
	Count        int          `json:"count"`
	RuyiCtrl     *ruyiControl `json:"ruyi_ctrl,omitempty"`
	TextCtrl     *textControl `json:"text_ctrl,omitempty"`
	ImageCtrl    ImageCtrl    `json:"image_ctrl"`
}

type Document struct {
	Title       string `json:"title"`
	URL         string `json:"url"`
	LogoURL     string `json:"logo_url"`
	CoreContent string `json:"core_content"`
	Summary     string `json:"summary"`
}

type SearchObservation struct {
	DocResults []*Document `json:"doc_results"`
}

type observation struct {
	SingleSearchResult struct {
		HasMore     bool `json:"has_more"`
		QueryParams struct {
			ImageCard []struct {
				SearchID string `json:"search_id"`
			} `json:"image_card"`
		} `json:"query_params"`
		SearchResult struct {
			ImageCard struct {
				CardList []struct {
					ImageInfo struct {
						URLList      []string `json:"url_list"`
						Height       int      `json:"height"`
						Width        int      `json:"width"`
						ThumbnailURL string   `json:"thumbnail_url"`
						Format       string   `json:"format"`
					} `json:"image_info"`
					Author struct {
					} `json:"author"`
					AbstractInfo struct {
						TitleOriginal string `json:"TitleOriginal"`
					} `json:"abstract_info"`
				} `json:"card_list"`
			} `json:"image_card"`
		} `json:"search_result"`
	} `json:"single_search_result"`
}

type ImageSearchObservation struct {
	ImageResults []*observation `json:"image_results"`
}

type HTTPClient struct {
	host   string
	apiKey string
	client *hertz.Client
}

type errorHTTPResp struct {
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message"`
}

type pluginObservation struct {
	Observation string `json:"observation"`
}
type searchHTTPResp struct {
	PluginObservation pluginObservation `json:"plugin_observation"`
	Error             *errorHTTPResp    `json:"error"`
}

// pluginThought 由于头条搜索 idl 生成的 client sdk 代码里面 json 序列化使用的是首字母大写，和 http 请求的不一致
type pluginThought struct {
	PluginName   string `json:"plugin_name"`
	Thought      string `json:"thought"`
	BizID        string `json:"biz_id"`
	PluginID     string `json:"plugin_id"`
	ToolName     string `json:"tool_name"`
	TrafficGroup string `json:"traffic_group"`
	TrafficID    string `json:"traffic_id"`
}

type SearchOption func(thought2 *Thought) *Thought

func WithPageSize(pageSize int) SearchOption {
	return func(thought *Thought) *Thought {
		thought.Count = pageSize
		return thought
	}
}

func (t *HTTPClient) Search(ctx context.Context, query string, opts ...SearchOption) (*SearchObservation, error) {
	searchObs := &SearchObservation{}
	thought := &Thought{
		InputQuery:   query,
		Cursor:       0,
		Count:        10,
		PluginPrompt: false,
		// 使用ruyi需要单独法务评估，存在风险：https://gpt.bytedance.net/gpt_openapi/model?scene_keyword=CloudIDE_Search
		RuyiCtrl: &ruyiControl{
			UseRuyi: false,
		},
		TextCtrl: &textControl{
			HasIntent:           true,
			GetFullText:         true,
			GetAbaseCoreContent: true,
		},
	}
	for _, opt := range opts {
		thought = opt(thought)
	}

	thoughtBytes, err := json.Marshal(&thought)
	if err != nil {
		return nil, err
	}
	searchResp := &searchHTTPResp{}

	// 接入文档：https://bytedance.larkoffice.com/wiki/Ni16wTRyCipaKHkLiU9cjQrFnYf
	_, err = t.client.DoJSONReq(ctx, http.MethodPost, "/plugin/openapi/online/single", hertz.ReqOption{
		Headers: map[string]string{
			"api-key":               t.apiKey,
			"plugin-source-service": env.PSM(),
		},
		Body: pluginThought{
			PluginName:   pluginNameSearch,
			Thought:      string(thoughtBytes),
			BizID:        "Flow_CloudIDE",
			PluginID:     "47",
			ToolName:     toolNameSearchPlugin,
			TrafficGroup: "Flow_CloudIDE",
			TrafficID:    "CloudIDE",
		},
		Result: &searchResp,
	})
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal([]byte(searchResp.PluginObservation.Observation), searchObs)
	if err != nil {
		return nil, err
	}
	return searchObs, nil
}

func NewToutiaoSearchHTTPClient(config config.ToutiaoSearchConfig, apiKey *tcc.GenericConfig[config.ToutiaoSearchAPIKey]) (*HTTPClient, error) {
	key := apiKey.GetValue()
	client, err := hertz.NewClient(config.APIBaseURL, hertz.NewHTTPClientOption{
		Timeout: 3 * time.Second,
	})
	if err != nil {
		return nil, err
	}
	return &HTTPClient{host: config.APIBaseURL, apiKey: string(key), client: client}, nil
}

type RPCClient struct {
	client       seed_plugin_proxy.OverpassClient
	bizId        string
	trafficGroup string
	trafficId    string
	ak           string
}

func NewToutiaoSearchRpcClient(client seed_plugin_proxy.OverpassClient, tccConfig *config.ToutiaoSearchPluginConfig) *RPCClient {
	if client == nil {
		client = seed_plugin_proxy.DefaultClient()
	}
	return &RPCClient{
		client:       client,
		bizId:        tccConfig.BizId,
		trafficGroup: tccConfig.TrafficGroup,
		trafficId:    tccConfig.TrafficId,
		ak:           tccConfig.AK,
	}
}

type ToutiaoSearchConfig struct {
	TrafficGroup string
	TrafficId    string
}

func NewToutiaoSearchRpcClientByLocalConfig(client seed_plugin_proxy.OverpassClient, config ToutiaoSearchConfig) *RPCClient {
	if client == nil {
		rawCli, err := seed_plugin_proxy.NewClient(seed_plugin_proxy.BASIC_PSM, c.WithIDC("lf"), c.WithRPCTimeout(3*time.Minute))
		if err != nil {
			panic(err)
		}
		client = rawCli
	}
	return &RPCClient{
		client:       client,
		trafficGroup: config.TrafficGroup,
		trafficId:    config.TrafficId,
	}
}

func (t *RPCClient) Search(ctx context.Context, query string, opts ...SearchOption) (*SearchObservation, error) {
	thought := &Thought{
		InputQuery: query,
		Cursor:     0,
		Count:      10,
		RuyiCtrl: &ruyiControl{
			UseRuyi: false,
		},
		TextCtrl: &textControl{
			GetFullText:         false,
			GetAbaseCoreContent: true,
		},
	}
	for _, opt := range opts {
		thought = opt(thought)
	}

	thoughtBytes, err := json.Marshal(&thought)
	if err != nil {
		return nil, err
	}
	pluginList := []*proxy.PluginThought{
		{
			PluginName: pluginNameSearch,
			Thought:    string(thoughtBytes),
			PluginId:   &pluginID,
			ToolName:   &toolNameSearchPlugin,
		},
	}
	req := &proxy.PluginReq{
		PluginThoughtList: pluginList,
		BizId:             &t.bizId,
		TrafficGroup:      &t.trafficGroup,
		TrafficId:         &t.trafficId,
		AK:                &t.ak,
	}
	resp, err := t.client.RawCall().GetPluginResp(ctx, req)
	if err != nil {
		return nil, err
	}

	respSearchObs := &SearchObservation{}
	for _, item := range resp.PluginObservationList {
		searchObs := &SearchObservation{}
		err = json.Unmarshal([]byte(item.Observation), searchObs)
		if err != nil {
			return nil, err
		}
		respSearchObs.DocResults = append(respSearchObs.DocResults, searchObs.DocResults...)
	}
	return respSearchObs, nil
}

func (t *RPCClient) ImageSearch(run *iris.AgentRunContext, query string, opts ...SearchOption) (r *ImageSearchObservation, err error) {
	lid := logid.NewLogID().GenLogID()
	ctx := ctxvalues.SetLogID(run, lid)
	thought := &Thought{
		InputQuery: query,
		TextCtrl:   &textControl{HasIntent: false},
		ImageCtrl:  ImageCtrl{Intent: "only"},
	}
	for _, opt := range opts {
		thought = opt(thought)
	}
	thoughtBytes, err := json.Marshal(&thought)
	if err != nil {
		return nil, err
	}
	pluginList := []*proxy.PluginThought{
		{
			PluginName: pluginNameSearch,
			Thought:    string(thoughtBytes),
			PluginId:   &pluginID,
			ToolName:   &toolNameSearchPlugin,
		},
	}
	req := &proxy.PluginReq{
		PluginThoughtList: pluginList,
		BizId:             &t.bizId,
		TrafficGroup:      &t.trafficGroup,
		TrafficId:         &t.trafficId,
		AK:                &t.ak,
	}
	run.GetLogger().Infof("Logid: %v, Query: %s", lid, query)
	resp, err := t.client.RawCall().GetPluginResp(ctx, req)
	if err != nil {
		return nil, err
	}
	if resp == nil {
		return nil, errors.New("empty response")
	}
	respSearchObs := &ImageSearchObservation{}
	for _, item := range resp.PluginObservationList {
		searchObs := &observation{}
		if item != nil {
			err = json.Unmarshal([]byte(item.Observation), searchObs)
			if err != nil {
				return nil, err
			}
			respSearchObs.ImageResults = append(respSearchObs.ImageResults, searchObs)
		}
	}
	return respSearchObs, nil
}

func emitSearchMetrics(pictureType string, errPtr *error) func() {
	tags := &metrics.CodeAssistSearchGenerationImageTag{
		Type: pictureType,
	}
	start := time.Now()
	return func() {
		_ = metrics.CodeAssistMetric.SearchGenerationImageThroughput.WithTags(tags).Add(1)
		if *errPtr != nil {
			_ = metrics.CodeAssistMetric.SearchGenerationImageError.WithTags(tags).Add(1)
		}
		_ = metrics.CodeAssistMetric.SearchGenerationImageLatency.WithTags(tags).Observe(float64(time.Since(start).Milliseconds()))
	}
}
