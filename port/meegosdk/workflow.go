package meegosdk

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/lib/hertz"
)

type GetWorkflowRequest struct {
	FlowType int64    `json:"flow_type,omitempty"` // 选填 工作流类型： 0：节点流，节点流工作项举例：需求等 1 ：状态流，状态流工作项举例：缺陷、版本等 非必填，默认为0（节点流）
	Fields   []string `json:"fields,omitempty"`    // 选填 定义需要返回的字段，非必填，默认返回全部 如果fields以-开头，表示返回除-号后字段中的所有字段 否则只返回fields中的字段（两种方式不能混用）
}

func (client *client) GetWorkflow(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, request *GetWorkflowRequest) (*NodesConnections, error) {
	u := fmt.Sprintf("/open_api/%s/work_item/%s/%d/workflow/query", projectKey, workItemTypeKey, workItemID)

	response := new(RawResponse[*NodesConnections])
	resp, err := client.doReq(ctx, http.MethodPost, u, &hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Headers: map[string]string{
			"X-USER-KEY": userKey,
		},
		Body:   request,
		Result: response,
		Notes:  "GetWorkflow",
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to send request to meego")
	}

	value, err := response.Unwrap(resp.StatusCode())
	if err != nil {
		return nil, err
	}
	return value, nil
}

type ChangeWorkflowNodeStateRequest struct {
	TransitionID int64             `json:"transition_id"` // 必填 流转到下一状态的id，从获取工作流详情接口查询状态流获取
	RoleOwners   []*RoleOwner      `json:"role_owners"`   // 选填 角色及负责人
	Fields       []*FieldValuePair `json:"fields"`        // 选填 要更新的字段数组(只能更新状态表单)
}

func (client *client) ChangeWorkflowNodeState(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, request *ChangeWorkflowNodeStateRequest) error {
	u := fmt.Sprintf("/open_api/%s/work_item/%s/%d/workflow/transition", projectKey, workItemTypeKey, workItemID)

	response := new(RawResponse[any])
	resp, err := client.doReq(ctx, http.MethodPost, u, &hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Headers: map[string]string{
			"X-USER-KEY": userKey,
		},
		Body:   request,
		Result: response,
		Notes:  "ChangeWorkflowNodeState",
	})
	if err != nil {
		return errors.WithMessage(err, "failed to send request to meego")
	}

	_, err = response.Unwrap(resp.StatusCode())
	return err
}

func (client *client) DownloadAsset(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, uuid string) ([]byte, error) {
	u := fmt.Sprintf("/open_api/%s/work_item/%s/%d/file/download", projectKey, workItemTypeKey, workItemID)
	opt := hertz.ReqOption{
		ExpectedCode: http.StatusOK,
		Notes:        "DownloadAsset",
		Headers: map[string]string{
			"X-USER-KEY": userKey,
		},
		Body: map[string]string{
			"uuid": uuid,
		},
	}

	err := client.WithPluginToken(ctx, &opt)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get plugin token")
	}
	resp, err := client.httpclient.DoRawReq(ctx, http.MethodPost, u, opt)
	if err != nil {
		return nil, err
	}
	return resp.Body(), nil
}
