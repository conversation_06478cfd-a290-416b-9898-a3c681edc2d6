package meegosdk

import (
	"time"

	"github.com/samber/lo"
)

type RoleOwner struct {
	Role   string   `json:"role,omitempty"`   // 角色id
	Name   string   `json:"name,omitempty"`   // 名称，作为请求参数非必填
	Owners []string `json:"owners,omitempty"` // 负责人user_key数组
}

type Schedule struct {
	Owners            []string `json:"owners,omitempty"`              // 负责人user_key数组，作为请求参数且更新差异化排期时必填，其余接口填了不识别
	EstimateStartDate int64    `json:"estimate_start_date,omitempty"` // 估计开始时间戳，精度毫秒（不填默认排期起始时间为空）
	EstimateEndDate   int64    `json:"estimate_end_date,omitempty"`   // 估计结束时间戳，精度毫秒（不填默认排期结束时间为空）
	Points            float64  `json:"points,omitempty"`              // 排期估分，选填
}

type Business struct {
	ID           string                `json:"id,omitempty"`            // 业务线id
	Name         string                `json:"name,omitempty"`          // 业务线名称
	Project      string                `json:"project,omitempty"`       // 所属空间id
	Labels       []string              `json:"labels,omitempty"`        // 标签数组
	RoleOwners   map[string]*RoleOwner `json:"role_owners,omitempty"`   // 默认角色及负责人，Map的key为角色标识（RoleOwner.role）
	Watchers     []string              `json:"watchers,omitempty"`      // 默认的关注者数组
	Order        float64               `json:"order,omitempty"`         // 排序字段
	SuperMasters []string              `json:"super_masters,omitempty"` // 管理员数组
	Parent       string                `json:"parent,omitempty"`        // 父级业务线id
	Disabled     bool                  `json:"disabled,omitempty"`      // 是否弃用
	LevelID      int                   `json:"level_id,omitempty"`      // 层级id，最顶层为1，子节点层级id等于父节点层级id+1
	Children     []*Business           `json:"children,omitempty"`      // 子业务线数组
}

type WBSWorkItem struct {
	NodeUuid     string            `json:"node_uuid,omitempty"`      // 子节点ID
	WorkItemID   int64             `json:"work_item_id,omitempty"`   // 子工作项ID, 不是子工作项 - 该字段不返回
	Type         string            `json:"type,omitempty"`           // 子工作项类型 node/sub_workitem/sub_task
	WbsStatus    string            `json:"wbs_status,omitempty"`     // 子工作项所属状态, 就是泳道图中的状态
	SubWorkItem  []*WBSWorkItem    `json:"sub_work_item,omitempty"`  // 子工作项数组 当且仅当type="node","sub_workitem"该字段有值
	Name         string            `json:"name,omitempty"`           // 子工作项名称
	Deliverable  []*FieldValuePair `json:"deliverable,omitempty"`    // 交付物
	WbsStatusMap map[string]string `json:"wbs_status_map,omitempty"` // wbs状态映射<status_key,status_name>
	Schedule     *Schedule         `json:"schedule,omitempty"`       // 非差异化排期
	Schedules    []*Schedule       `json:"schedules,omitempty"`      // 差异化排期
	Points       float64           `json:"points,omitempty"`         // 估分
	RoleOwners   []*RoleOwner      `json:"role_owners,omitempty"`    // 角色负责人
}

type FieldValuePair struct {
	FieldKey     string      `json:"field_key,omitempty"`      // 字段key，作为请求参数和field_alias二选一必填
	FieldAlias   string      `json:"field_alias,omitempty"`    // 字段对接标识，作为请求参数和field_key二选一必填
	FieldValue   interface{} `json:"field_value,omitempty"`    // 字段值，作为请求参数必填
	FieldTypeKey string      `json:"field_type_key,omitempty"` // 字段类型，作为请求参数非必填
	TargetState  TargetState `json:"target_state,omitempty"`   // 指代流转至该目标状态需要填写对应字段，状态流使用，作为请求参数非必填
}

type TargetState struct {
	StateKey     string `json:"state_key,omitempty"`     // 目标状态key
	TransitionID int64  `json:"transition_id,omitempty"` // 流转到目标状态的id
}

type WorkItemKeyType struct {
	TypeKey   string `json:"type_key,omitempty"`   // 工作项类型key
	Name      string `json:"name,omitempty"`       // 工作项类型的名称
	APIName   string `json:"api_name,omitempty"`   // 工作项系统标识
	IsDisable int    `json:"is_disable,omitempty"` // 是否禁用，1为禁用，2为启用
}

type WorkItemInfo struct {
	ID                   int64                       `json:"id,omitempty"`                 // 工作项的id
	Name                 string                      `json:"name,omitempty"`               // 工作项的名称
	WorkItemTypeKey      string                      `json:"work_item_type_key,omitempty"` // 工作项类型key
	WorkItemType         string                      `json:"work_item_type,omitempty"`     // 工作项类型 WorkItemTypeKey的DisplayName
	ProjectKey           string                      `json:"project_key,omitempty"`        // 所属空间id
	TemplateID           int64                       `json:"template_id,omitempty"`        // 模板id
	TemplateType         string                      `json:"template_type,omitempty"`      // 模板类型
	SimpleName           string                      `json:"simple_name,omitempty"`        // 工作项所属空间的简称
	Pattern              string                      `json:"pattern,omitempty"`            // 工作项模式,分为节点模式(Node)/状态模式(State)
	SubStage             string                      `json:"sub_stage,omitempty"`          // 当前阶段状态(仅需求)
	CurrentNodes         []*NodeBasicInfo            `json:"current_nodes,omitempty"`      // 当前节点信息（状态模式节点为空）
	CreatedBy            string                      `json:"created_by,omitempty"`         // 创建人user_key
	UpdatedBy            string                      `json:"updated_by,omitempty"`         // 更新人user_key
	DeletedBy            string                      `json:"deleted_by,omitempty"`         // 删除人user_key
	CreatedAt            int64                       `json:"created_at,omitempty"`         // 创建时间戳，毫秒精度
	UpdatedAt            int64                       `json:"updated_at,omitempty"`         // 更新时间戳，毫秒精度
	DeletedAt            int64                       `json:"deleted_at,omitempty"`         // 删除时间戳，毫秒精度
	Fields               []*FieldValuePair           `json:"fields,omitempty"`             // 工作项的其他字段
	MultiTexts           []*FieldValuePair           `json:"multi_texts,omitempty"`
	WorkItemStatus       *WorkItemStatus             `json:"work_item_status,omitempty"`       // 工作项状态(除需求外)
	WorkflowInfos        *NodesConnections           `json:"workflow_infos,omitempty"`         // 工作项节点流信息(仅在获取工作项详情接口指定了额外参数时返回)
	StateTimes           []*StateTime                `json:"state_times,omitempty"`            // 节点时间
	RelationFieldsDetail []*RelationFieldsDetailItem `json:"relation_fields_detail,omitempty"` // 关联字段详细信息
	// below are not official field, but it is used in the frontend
	RefURL        string         `json:"ref_url,omitempty"`           // 工作项链接
	ProjectName   string         `json:"project_name,omitempty"`      // 工作项所属空间的名称
	Status        string         `json:"status,omitempty"`            // 工作项状态
	FieldsMap     map[string]any `json:"fields_map,omitempty"`        // Fields的用户友好形式
	DevMindFields map[string]any `json:"extra_time_fields,omitempty"` // DevMind的计算结果
	UserDetails   []*UserDetail  `json:"user_details,omitempty"`      // 用户详细信息
}

type WorkItemStatus struct {
	StateKey        string `json:"state_key,omitempty"`         // 状态key（作为搜索参数时只需传入该字段）
	IsArchivedState bool   `json:"is_archived_state,omitempty"` // 状态是否到达
	IsInitState     bool   `json:"is_init_state,omitempty"`     // 是否初始状态
	UpdatedAt       int64  `json:"updated_at,omitempty"`        // 状态更新时间，毫秒精度
	UpdatedBy       string `json:"updated_by,omitempty"`        // 状态更新人user_key
}

type NodeBasicInfo struct {
	ID     string   `json:"id,omitempty"`     // 节点id
	Name   string   `json:"name,omitempty"`   // 节点名称
	Owners []string `json:"owners,omitempty"` // 负责人user_key 数组
}

type PagedWorkItemIDs struct {
	WorkItemIDs []int64 `json:"work_item_ids,omitempty"` // 工作项id列表
	PageNum     int64   `json:"page_num,omitempty"`      // 当前页码
	PageSize    int64   `json:"page_size,omitempty"`     // 当前页大小
	Total       int64   `json:"total,omitempty"`         // 匹配的工作项总数
}

type NodesConnections struct {
	WorkflowNodes  []*WorkflowNode  `json:"workflow_nodes,omitempty"`  // 工作项下的工作流节点数组
	Connections    []*Connection    `json:"connections,omitempty"`     // 工作项下的连接数组
	StateflowNodes []*StateFlowNode `json:"stateflow_nodes,omitempty"` // 工作项下的状态流节点数组
}

func (n *NodesConnections) NormalizeFields() {
	if n == nil {
		return
	}
	now := time.Now()
	// 获取今天0点的时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).UnixMilli()
	tomorrowStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, 1).UnixMilli()
	for _, node := range n.WorkflowNodes {
		node.StatusStr = node.Status.String()
		if node.Status == WorkflowNodeStatusReached {
			if node.NodeSchedule.EstimateEndDate > 0 && node.NodeSchedule.EstimateEndDate < todayStart {
				node.NodeTag = NodeTagDelay
			} else if node.NodeSchedule.EstimateEndDate < tomorrowStart && node.NodeSchedule.EstimateEndDate > todayStart {
				node.NodeTag = NodeTagDueToday
			} else if lo.ContainsBy(node.Schedules, func(item *Schedule) bool {
				return item.Points == 0
			}) {
				node.NodeTag = NodeTagScheduleUnComplete
			} else {
				node.NodeTag = NodeTagNormal
			}
		} else {
			node.NodeTag = NodeTagNormal
		}
	}
	n.Connections = nil
}

type WorkflowNodeStatus int

const (
	WorkflowNodeStatusUnReach WorkflowNodeStatus = 1 // UNREACHE (未开始)
	WorkflowNodeStatusReached WorkflowNodeStatus = 2 // REACHED (进行中)
	WorkflowNodeStatusPassed  WorkflowNodeStatus = 3 // PASSED (已完成)
)

func (s WorkflowNodeStatus) String() string {
	switch s {
	case WorkflowNodeStatusUnReach:
		return "UnReach"
	case WorkflowNodeStatusReached:
		return "Reached"
	case WorkflowNodeStatusPassed:
		return "Passed"
	}
	return ""
}

type WorkflowNode struct {
	ID               string             `json:"id,omitempty"`                 // 节点id
	StateKey         string             `json:"state_key,omitempty"`          // 节点key
	Name             string             `json:"name,omitempty"`               // 节点名称
	Status           WorkflowNodeStatus `json:"status,omitempty"`             // 节点状态,见"WFState.status对应表"
	StatusStr        string             `json:"status_str,omitempty"`         // 用户友好状态
	NodeTag          NodeTag            `json:"node_tag,omitempty"`           // 节点标签
	Fields           []*FieldValuePair  `json:"fields,omitempty"`             // 工作项的其他字段, 太细了，暂时不用
	Owners           []string           `json:"owners,omitempty"`             // 负责人user_key数组
	NodeSchedule     *Schedule          `json:"node_schedule,omitempty"`      // 节点总排期
	Schedules        []*Schedule        `json:"schedules,omitempty"`          // 不同用户的排期数组（仅在差异化排期时有值）
	SubTasks         []*SubTask         `json:"sub_tasks,omitempty"`          // 子任务数组
	ActualBeginTime  string             `json:"actual_begin_time,omitempty"`  // 实际开始时间
	ActualFinishTime string             `json:"actual_finish_time,omitempty"` // 实际结束时间
	RoleAssignee     []*RoleOwner       `json:"role_assignee,omitempty"`      // 节点绑定的角色负责人(未绑定角色时不返回)
}

type NodeTag string

const (
	NodeTagDelay              NodeTag = "已延期"  // 延期
	NodeTagScheduleUnComplete NodeTag = "排期不全" // 排期不全
	NodeTagDueToday           NodeTag = "今日到期" // 今日到期
	NodeTagNormal             NodeTag = "正常"   // 正常
)

type Connection struct {
	SourceStateKey string `json:"source_state_key,omitempty"` // 开始节点的key
	TargetStateKey string `json:"target_state_key,omitempty"` // 结束节点的key
	TransitionID   int64  `json:"transition_id,omitempty"`    // 状态流转id，仅状态流返回
}

type SubTask struct {
	ID               string            `json:"id,omitempty"`                 // 任务id
	Name             string            `json:"name,omitempty"`               // 任务名称
	Schedules        []*Schedule       `json:"schedules,omitempty"`          // 排期数组
	Order            float64           `json:"order,omitempty"`              // 排序字段
	Passed           bool              `json:"passed,omitempty"`             // 是否通过
	Owners           []string          `json:"owners,omitempty"`             // 负责人user_key数组
	Note             string            `json:"note,omitempty"`               // 备注
	ActualBeginTime  string            `json:"actual_begin_time,omitempty"`  // 创建时间
	ActualFinishTime string            `json:"actual_finish_time,omitempty"` // 完成时间
	Assignee         []string          `json:"assignee,omitempty"`           // 子任务负责人userKey列表，节点负责人类型为非角色负责人时可用
	RoleAssignee     []*RoleOwner      `json:"role_assignee,omitempty"`      // 子任务角色负责人列表，节点负责人类型为角色负责人时可用
	Deliverable      []*FieldValuePair `json:"deliverable,omitempty"`        // 交付物
}

type StateFlowNode struct {
	ID               string            `json:"id,omitempty"`                 // 状态id
	Name             string            `json:"name,omitempty"`               // 状态名称
	Status           string            `json:"status,omitempty"`             // 状态节点的状态,见"WFState.status对应表"
	Owners           string            `json:"owners,omitempty"`             // 负责人
	Fields           []*FieldValuePair `json:"fields,omitempty"`             // 字段
	ActualBeginTime  string            `json:"actual_begin_time,omitempty"`  // 状态开始时间
	ActualFinishTime string            `json:"actual_finish_time,omitempty"` // 状态结束时间
}

type SimpleField struct {
	FieldKey       string         `json:"field_key"`        // 字段key
	FieldAlias     string         `json:"field_alias"`      // 字段对接标识
	FieldTypeKey   string         `json:"field_type_key"`   // 字段类型
	FieldName      string         `json:"field_name"`       // 字段名称
	IsCustomField  bool           `json:"is_custom_field"`  // 是否自定义字段
	IsObsoleted    bool           `json:"is_obsoleted"`     // 字段是否废弃
	Options        []*Option      `json:"options"`          // 选项
	CompoundFields []*SimpleField `json:"compound_fields"`  // 复合字段
	WorkItemScopes []string       `json:"work_item_scopes"` // 工作项类型范围
}

type Option struct {
	Label    string `json:"label,omitempty"`    // 选项标签
	Value    string `json:"value,omitempty"`    // 选项值
	Children any    `json:"children,omitempty"` // 子选项
}

type Comment struct {
	ID              int    `json:"id,omitempty"`                 // 评论id
	WorkItemID      int    `json:"work_item_id,omitempty"`       // 评论所属工作项id
	WorkItemTypeKey string `json:"work_item_type_key,omitempty"` // 评论所属工作项类型
	CreatedAt       int    `json:"created_at,omitempty"`         // 评论创建时间，毫秒精度
	Operator        string `json:"operator,omitempty"`           // 评论人
	Content         string `json:"content,omitempty"`            // 评论内容
}

type Project struct {
	ProjectKey string `json:"project_key,omitempty"` // 空间id
	Name       string `json:"name,omitempty"`        // 空间名
	SimpleName string `json:"simple_name,omitempty"` // 空间路由名
	//Administrators []string `json:"administrators,omitempty"` // 空间管理员 userkey 列表（仅管理员可见）
}

type FixView struct {
	ViewID         string  `json:"view_id,omitempty"`           // 视图ID
	Name           string  `json:"name,omitempty"`              // 视图名称
	CreatedBy      string  `json:"created_by,omitempty"`        // 创建人user_key
	CreatedAt      int     `json:"created_at,omitempty"`        // 创建时间，毫秒精度
	ModifiedBy     string  `json:"modified_by,omitempty"`       // 最后一次修改人user_key
	WorkItemIDList []int64 `json:"work_item_id_list,omitempty"` // 固定视图包含的工作项ID列表
	Editable       bool    `json:"editable,omitempty"`          // 当前视图是否可编辑
}

type TimeInterval struct {
	Start int64 `json:"start,omitempty"` // 开始时间，毫秒精度
	End   int64 `json:"end,omitempty"`   // 结束时间，毫秒精度
}

type NodeTask struct {
	ID       string     `json:"id,omitempty"`        // 节点id
	StateKey string     `json:"state_key,omitempty"` // 节点key
	SubTasks []*SubTask `json:"sub_tasks,omitempty"` // 节点下子任务列表
}

type ViewConf struct {
	ViewID        string   `json:"view_id,omitempty"`       // 视图id
	ViewUrl       string   `json:"view_url,omitempty"`      // 视图url
	Name          string   `json:"name,omitempty"`          // 视图名称
	ViewType      int64    `json:"view_type,omitempty"`     // 视图类型(条件、固定) Unknow = 0 Condition = 1 //条件视图 Fixed     = 2 //固定视图
	Auth          int64    `json:"auth,omitempty"`          // 是否开放权限 Open   = 1 //开放权限开关 开 Close  = 2 //开放权限开关 关
	SystemView    int64    `json:"system_view,omitempty"`   // 是否系统视图 True   = 1 //系统视图 False  = 2 //不是系统视图
	Collaborators []string `json:"collaborators,omitempty"` // 协作者
	CreatedAt     int64    `json:"created_at,omitempty"`    // 创建时间
	CreatedBy     string   `json:"created_by,omitempty"`    // 创建者
}

type TemplateConf struct {
	TemplateID   string `json:"template_id,omitempty"`   // 流程模板id
	TemplateName string `json:"template_name,omitempty"` // 流程模板名称
	IsDisabled   int64  `json:"is_disabled,omitempty"`   // 是否禁用 True = 1 //禁用 False = 2 //启用
	Version      int64  `json:"version,omitempty"`       // 当前版本号
	UniqueKey    string `json:"unique_key,omitempty"`    // 唯一键，仅查询需求时有值
}

type FieldConf struct {
	IsRequired     int           `json:"is_required,omitempty"`     // 展现为必填、非必填、条件必填，若为条件必填，则展示为条件必填，具体条件用户自行到空间查看 Required = 1 //必填 NoRequired = 2 //非必填 CondRequired=3 //条件必填
	IsVisibility   int           `json:"is_visibility,omitempty"`   // 是否可见，只有默认可见和某条件下可见，条件可见需用户到空间查看 Visibility = 1 //可见 CondVisibility =2 //条件可见
	IsValidity     int           `json:"is_validity,omitempty"`     // 有效性 True = 1 //有效 False = 2 //无效
	RoleAssign     []*RoleAssign `json:"role_assign,omitempty"`     // 字段是role_owners，展现角色出现方式
	FieldName      string        `json:"field_name,omitempty"`      // 字段名称
	FieldKey       string        `json:"field_key,omitempty"`       // 字段key
	FieldAlias     string        `json:"field_alias,omitempty"`     // 字段对接标识
	FieldTypeKey   string        `json:"field_type_key,omitempty"`  // 字段类型 开放能力-字段&属性解析格式 文档里没有的和field_key一致
	DefaultValue   *DefaultValue `json:"default_value,omitempty"`   // 默认值
	Label          string        `json:"label,omitempty"`           // 表单项名称
	Options        []*OptionConf `json:"options,omitempty"`         // 选项
	CompoundFields []*FieldConf  `json:"compound_fields,omitempty"` // 复合字段
}

type RoleAssign struct {
	Role          string   `json:"role,omitempty"`           // 角色ID
	Name          string   `json:"name,omitempty"`           // 角色名称
	DefaultAppear int64    `json:"default_appear,omitempty"` // 展现"默认出现""默认不出现""条件出现"，则展示为条件出现，具体条件用户自行到空间查看 Appear = 1 //默认出现 NoAppear = 2 //默认不出现 CondAppear = 3 //条件出现
	Deletable     int64    `json:"deletable,omitempty"`      // 是否可删除 CanDeleted = 1 //可删除 NoDeleted  = 2 //不可删除
	MemberAssign  int64    `json:"member_assign,omitempty"`  // 成员分配方式，"自行添加""默认分配为指定人员""默认分配为创建者""条件分配"，具体条件用户自行到空间查看 CanAdded = 1 //自行添加 DefaultUser = 2 //指定人员 DefaultCreator = 3 //指定为创建者 CondAssign = 4 //条件分配
	Members       []string `json:"members,omitempty"`        // 默认出现的成员列表
}

type DefaultValue struct {
	DefaultAppear int64       `json:"default_appear,omitempty"` // 展现"默认出现""默认不出现""条件出现"，则展示为条件出现，具体条件用户自行到空间查看 Appear = 1 //默认出现 NoAppear = 2 //默认不出现 CondAppear = 3 //条件出现
	Value         interface{} `json:"value,omitempty"`          // 默认值  开放能力-字段&属性解析格式
}

type OptionConf struct {
	Label        string        `json:"label,omitempty"`         // 选项标签
	Value        string        `json:"value,omitempty"`         // 选项值
	IsVisibility int64         `json:"is_visibility,omitempty"` // 是否可见，只有默认可见和某条件下可见，条件可见需用户到空间查看 Visibility = 1 //可见 CondVisibility = 2 //条件可见
	IsDisabled   int64         `json:"is_disabled,omitempty"`   // 是否禁用 True = 1 //禁用 False = 2 //启用
	Children     []*OptionConf `json:"children,omitempty"`      // 子选项
}

type ExPand struct {
	NeedWorkflow         bool `json:"need_workflow,omitempty"`          // 是否需要返回工作流信息(仅工作流模式可以使用)
	NeedMultiText        bool `json:"need_multi_text,omitempty"`        // 是否需要返回富文本详细信息(若有则会在multi_texts结构中返回)
	RelationFieldsDetail bool `json:"relation_fields_detail,omitempty"` // 是否需要关联字段详细信息
	NeedUserDetail       bool `json:"need_user_detail,omitempty"`       // 是否需要用户详细信息
	NeedSubTaskParent    bool `json:"need_sub_task_parent,omitempty"`   // 是否需要子任务父级
}

type StateTime struct {
	StateKey  string `json:"state_key,omitempty"`  // 节点key
	StartTime int64  `json:"start_time,omitempty"` // 节点开始时间
	EndTime   int64  `json:"end_time,omitempty"`   // 节点结束时间
	Name      string `json:"name,omitempty"`       // 节点名称
}

type SearchUser struct {
	UserKeys []string `json:"user_keys,omitempty"` // 需要查询的用户user_key列表 (field_key和role都不传的时候，仅支持查询角色中包含指定人员的需求-无需传参角色，可查询所有角色下包含传参人员的工作项）
	FieldKey string   `json:"field_key,omitempty"` // 指定的时候，查询该人员字段key包含指定人员的工作项， (只支持查创建人、关注人、经办人、报告人)
	Role     string   `json:"role,omitempty"`      // 指定的时候，查询指定角色id包含指定人员的工作项
}

type Operator string

const (
	OperatorReg       Operator = "~"           //匹配
	OperatorNReg      Operator = "!~"          //不匹配
	OperatorEq        Operator = "="           //等于
	OperatorNe        Operator = "!="          //不等于
	OperatorLt        Operator = "<"           //小于
	OperatorGt        Operator = ">"           //大于
	OperatorLte       Operator = "<="          //小于等于
	OperatorGte       Operator = ">="          //大于等于
	OperatorHasAnyOf  Operator = "HAS ANY OF"  //存在选项属于
	OperatorHasNoneOf Operator = "HAS NONE OF" //不存在选项属于
	OperatorIsNull    Operator = "IS NULL"     //为空
	OperatorNotNull   Operator = "IS NOT NULL" //不为空
)

type SearchParam struct {
	ParamKey string      `json:"param_key,omitempty"` // 字段key
	Value    interface{} `json:"value,omitempty"`     // 搜索字段值结构
	Operator Operator    `json:"operator,omitempty"`  // 操作符类型，枚举
}

type SearchGroup struct {
	SearchParams []*SearchParam `json:"search_params,omitempty"` // 固定参数
	Conjunction  string         `json:"conjunction,omitempty"`   // 枚举 And，Or
	SearchGroups []*SearchGroup `json:"search_groups,omitempty"` // 筛选组
}

type Template struct {
	IsDisabled   int    `json:"is_disabled,omitempty"`   //是否禁用 True = 1 //禁用 False = 2 //启用
	TemplateID   string `json:"template_id,omitempty"`   //模板id
	TemplateName string `json:"template_name,omitempty"` //模板名称
	Version      int    `json:"version,omitempty"`       //模板版本
	UniqueKey    string `json:"unique_key,omitempty"`
}

type MultiSignalDetailStatus string

const (
	MultiSignalDetailStatusPassed     MultiSignalDetailStatus = "passed"
	MultiSignalDetailStatusProcessing MultiSignalDetailStatus = "processing"
	MultiSignalDetailStatusRejected   MultiSignalDetailStatus = "rejected"
)

type MultiSignalDetail struct {
	ID        string                  `json:"id,omitempty"`         // 一条多值外信号的唯一id,在这个字段内的id是唯一的
	Title     string                  `json:"title,omitempty"`      // 标题
	Status    MultiSignalDetailStatus `json:"status,omitempty"`     // 状态（"passed"、"processing"、"rejected"）
	ViewLink  string                  `json:"view_link,omitempty"`  // 可跳转链接
	QueryLink *QueryLink              `json:"query_link,omitempty"` // 查询链接
}

type QueryLink struct {
	Url     string      `json:"url,omitempty"`     // 链接
	Method  string      `json:"method,omitempty"`  // 请求方法
	Headers interface{} `json:"headers,omitempty"` // 请求头
	Body    interface{} `json:"body,omitempty"`    // 请求体
	Params  interface{} `json:"params,omitempty"`  // 请求参数
}

type MultiSignal struct {
	Status string               `json:"status,omitempty"` // 多值外信号状态
	Detail []*MultiSignalDetail `json:"detail,omitempty"` // 多值外信号详细信息
}

type RelationFieldsDetailItem struct {
	FieldKey         string                 `json:"field_key,omitempty"`           // 字段key
	FieldName        string                 `json:"field_name,omitempty"`          // 字段名称
	WorkItemTypeKeys []string               `json:"work_item_key_types,omitempty"` // 关联的工作项类型key
	Detail           []*RelationFieldDetail `json:"detail,omitempty"`              // 详细信息
}

type RelationFieldDetail struct {
	StoryID         int64  `json:"story_id,omitempty"`           // 需求ID
	WorkItemTypeKey string `json:"work_item_type_key,omitempty"` // 工作项类型key
	ProjectKey      string `json:"project_key,omitempty"`        // 项目key
}

type UserDetail struct {
	UserKey  string `json:"user_key,omitempty"` // 用户唯一标识
	Username string `json:"username,omitempty"` // 用户名
	Email    string `json:"email,omitempty"`    // 邮箱
	NameCN   string `json:"name_cn,omitempty"`  // 中文名
	NameEN   string `json:"name_en,omitempty"`  // 英文名
}

// FlowRole represents a flow role configuration
type FlowRole struct {
	ID                    string   `json:"id,omitempty"`                      // 角色ID
	Name                  string   `json:"name,omitempty"`                    // 角色名称
	Deletable             bool     `json:"deletable,omitempty"`               // 是否可删除
	AuthorizationRoleKeys []string `json:"authorization_role_keys,omitempty"` // 授权角色键列表
	IsMemberMulti         bool     `json:"is_member_multi,omitempty"`         // 是否支持多成员
	RoleAppearMode        int      `json:"role_appear_mode,omitempty"`        // 角色出现模式
	AutoEnterGroup        bool     `json:"auto_enter_group,omitempty"`        // 是否自动进入组
	MemberAssignMode      int      `json:"member_assign_mode,omitempty"`      // 成员分配模式
	AllowDelete           bool     `json:"allow_delete,omitempty"`            // 是否允许删除
	IsOwner               bool     `json:"is_owner,omitempty"`                // 是否为负责人
}
