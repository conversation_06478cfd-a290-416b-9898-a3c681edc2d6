package meegosdk

import (
	"context"
)

// SearchWorkItemsByRelationRequest represents the request parameters for searching work items by relation
type SearchWorkItemsByRelationRequest struct {
	RelationWorkItemTypeKey string                 `json:"relation_work_item_type_key"`
	Relation<PERSON>ey             string                 `json:"relation_key"`
	PageNum                 int                    `json:"page_num"`
	PageSize                int                    `json:"page_size"`
	RelationType            int                    `json:"relation_type"`
	Expand                  *SearchWorkItemsExpand `json:"expand"`
}

// SearchWorkItemsExpand represents the expand options for searching work items
type SearchWorkItemsExpand struct {
	NeedWorkflow         bool `json:"need_workflow"`
	RelationFieldsDetail bool `json:"relation_fields_detail"`
	NeedMultiText        bool `json:"need_multi_text"`
	NeedUserDetail       bool `json:"need_user_detail"`
	NeedSubTaskParent    bool `json:"need_sub_task_parent"`
}

type Api interface {
	// 业务线相关

	// ListBusiness 获取空间下业务线详情
	ListBusiness(ctx context.Context, userKey string, projectKey string) ([]*Business, error)

	// ------------------------------------------------

	// 空间字段相关

	// ListSimpleFields 获取空间字段
	ListSimpleFields(ctx context.Context, projectKey string, userKey string, request *ListSimpleFieldsRequest) ([]*SimpleField, error)

	// ------------------------------------------------

	// 空间相关

	// ListProjectKeys 获取空间列表
	ListProjectKeys(ctx context.Context, userKey string, request *ListProjectKeysRequest) ([]string, error) // 查询某个用户的空间列表,返回project_id(也叫project_key)数组

	// ListProjects 获取空间详情
	ListProjects(ctx context.Context, request *ListProjectsRequest) (map[string]*Project, error) // 查询空间详情

	// ------------------------------------------------

	// 工作项模板列表

	// ListTemplates 获取工作项模板列表
	ListTemplates(ctx context.Context, userKey, projectKey, workItemTypeKey string) ([]*Template, error)

	// ------------------------------------------------

	// 用户相关

	// ListUsers 获取用户详情
	ListUsers(ctx context.Context, request *GetUsersRequest) ([]*User, error) // 批量获取用户详情
	GetUserByEmail(ctx context.Context, email string) (*User, error)          // 通过邮箱获取用户详情

	// ------------------------------------------------

	// 视图相关

	// ListViewConfs 获取视图列表
	ListViewConfs(ctx context.Context, userKey string, projectKey string, request *ListViewConfsRequest) ([]*ViewConf, error)
	// GetFixView 获取固定视图详情
	GetFixView(ctx context.Context, userKey string, projectKey string, viewID string, req *Page, quickFilterID string) (*FixView, *Pagination, error)
	// GetMultiProjectView 获取全景视图工作项
	GetMultiProjectView(ctx context.Context, userKey string, projectKey string, viewID string, request *GetViewRequest) ([]*WorkItemInfo, *Pagination, error)

	// ------------------------------------------------

	// 工作项相关

	// ListWorkItems 获取指定的工作项列表
	ListWorkItems(ctx context.Context, userKey string, projectKey string, request *ListWorkItemsRequest) ([]*WorkItemInfo, *Pagination, error) //非跨空间
	// ListAcrossProjectWorkItems 获取指定的工作项列表（跨空间）
	ListAcrossProjectWorkItems(ctx context.Context, userKey string, request *ListAcrossProjectWorkItemsRequest) ([]*WorkItemInfo, *Pagination, error)
	// ListWorkItemAllTypes 获取空间下工作项类型
	ListWorkItemAllTypes(ctx context.Context, userKey string, projectKey string) ([]*WorkItemKeyType, error)
	// ListWorkItemFieldConfs 获取创建工作项元数据
	ListWorkItemFieldConfs(ctx context.Context, userKey string, projectKey string, workItemTypeKey string) ([]*FieldConf, error)
	// SearchWorkItems 获取指定的工作项列表（单空间 | 复杂传参）
	SearchWorkItems(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, request *SearchWorkItemsRequest) ([]*WorkItemInfo, *Pagination, error)
	// CreateWorkItem 创建工作项
	CreateWorkItem(ctx context.Context, userKey, projectKey string, request *CreateWorkItemRequest) (int64, error)
	// DeleteWorkItem 删除工作项
	DeleteWorkItem(ctx context.Context, userKey, projectKey, workItemTypeKey string, workItemID int64) error
	// UpdateWorkItem 更新工作项
	UpdateWorkItem(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, request *UpdateWorkItemRequest) error
	// BatchUpdateWorkItemField 批量更新工作项字段
	BatchUpdateWorkItemField(ctx context.Context, userKey string, request *BatchUpdateWorkItemFieldRequest) (string, error)
	GetBatchUpdateWorkItemFieldResult(ctx context.Context, userKey string, taskID string) (*BatchUpdateWorkItemFieldTaskDetail, error)
	// GetWorkItemDetail 获取工作项详情
	GetWorkItemDetail(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, request *GetWorkItemDetailRequest) ([]*WorkItemInfo, error)
	// ------------------------------------------------

	// GetWorkflow 获取工作流详情
	GetWorkflow(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, request *GetWorkflowRequest) (*NodesConnections, error)

	// ChangeWorkflowNodeState 状态流转
	ChangeWorkflowNodeState(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, request *ChangeWorkflowNodeStateRequest) error

	// SearchWorkItemsByRelation 根据关联关系搜索工作项
	SearchWorkItemsByRelation(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, request *SearchWorkItemsByRelationRequest) ([]*WorkItemInfo, *Pagination, error)

	// ListFlowRoles 获取工作项类型的流程角色列表
	ListFlowRoles(ctx context.Context, userKey string, projectKey string, workItemTypeKey string) ([]*FlowRole, error)

	DownloadAsset(ctx context.Context, userKey string, projectKey string, workItemTypeKey string, workItemID int64, uuid string) ([]byte, error)
}
