package bits

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/env"
	"github.com/pkg/errors"
)

const (
	BitsBaseURL            = "https://bits.bytedance.net"
	AppCenterFaasCNUrl     = "https://ix2reyff.fn.bytedance.net"
	AppCenterFaasI18NTTUrl = "https://i9ladx5x.sg-fn.bytedance.net"
)

type Client interface {
	SearchTCEComponent(ctx context.Context, req *SearchComponentRequest, jwt string) (*SearchComponentResponse, error)
	SearchRecentDeployment(ctx context.Context, req *SearchRecentDeploymentRequest) (*SearchRecentDeploymentResponse, error)
}

type client struct {
	bitsURL             string
	appcenterFaasUrl    string
	bitsClient          *hertz.Client
	appcenterFaasClient *hertz.Client
}

func New() (Client, error) {
	appcenterFaasUrl := AppCenterFaasCNUrl
	if env.GetCurrentVRegion() == env.VREGION_SINGAPORECENTRAL {
		appcenterFaasUrl = AppCenterFaasI18NTTUrl
	}
	c := &client{
		bitsURL:          BitsBaseURL,
		appcenterFaasUrl: appcenterFaasUrl,
	}

	bitsClient, err := hertz.NewClient(c.bitsURL, hertz.NewHTTPClientOption{
		Timeout: 10 * time.Second,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create HTTP client")
	}

	appcenterFaasClient, err := hertz.NewClient(c.appcenterFaasUrl, hertz.NewHTTPClientOption{
		Timeout: 10 * time.Second,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create HTTP client")
	}

	c.bitsClient = bitsClient
	c.appcenterFaasClient = appcenterFaasClient
	return c, nil
}

type SearchComponentRequest struct {
	Keyword       string `json:"keyword"`
	Limit         int    `json:"limit"`
	ProjectType   int    `json:"projectType"`
	ControlPlanes []int  `json:"controlPlanes"`
}

type SearchComponentResponse struct {
	Projects []*Project `json:"projects"`
}

const (
	ProjectTypeTCE int = 1
	ControlPlaneCN int = 1
	OnlyBOE            = 1
)

type Project struct {
	ProjectUniqueId            string                `json:"projectUniqueId"`
	ProjectName                string                `json:"projectName"`
	ProjectType                int                   `json:"projectType"`
	ControlPlane               int                   `json:"controlPlane"`
	NoControlPlaneDetailReason int                   `json:"noControlPlaneDetailReason"`
	ControlPlaneDetail         []*ControlPlaneDetail `json:"controlPlaneDetail"`
}

type ControlPlaneDetail struct {
	ControlPlane int `json:"controlPlane"`
}

func (c *client) SearchTCEComponent(ctx context.Context, req *SearchComponentRequest, jwt string) (*SearchComponentResponse, error) {
	resp := &SearchComponentResponse{}
	_, err := c.bitsClient.DoJSONReq(ctx, http.MethodPost, "/api/v2/appcenter/fuzzy_search_component", hertz.ReqOption{
		Headers: map[string]string{
			"x-jwt-token": jwt,
		},
		Body:    req,
		Result:  &resp,
		Timeout: 10 * time.Second,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to search component")
	}

	return resp, nil
}

type SearchRecentDeploymentRequest struct {
	Username string `json:"username"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type SearchRecentDeploymentResponse struct {
	Code string `json:"code"`
	Data *Data  `json:"data"`
}

type Data struct {
	Deployments []*Deployment `json:"deployments"`
	Total       int           `json:"total"`
}

type Deployment struct {
	Psm string `json:"psm"`
}

func (c *client) SearchRecentDeployment(ctx context.Context, req *SearchRecentDeploymentRequest) (*SearchRecentDeploymentResponse, error) {
	resp := &SearchRecentDeploymentResponse{}
	_, err := c.appcenterFaasClient.DoJSONReq(ctx, http.MethodPost, "/tce/get_deployments_by_user", hertz.ReqOption{
		Body:    req,
		Result:  &resp,
		Timeout: 15 * time.Second,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to search recent deployment")
	}
	if resp.Code != "0" {
		return nil, errors.Errorf("failed to search recent deployment: %s", resp.Code)
	}

	return resp, nil
}
