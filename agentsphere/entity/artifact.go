package entity

import (
	"encoding/json"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing/filemode"
	"github.com/samber/lo"
)

type ArtifactStatus string
type ArtifactType string
type ArtifactMetadata any

const (
	ArtifactTypeFile      ArtifactType = "file"
	ArtifactTypeCode      ArtifactType = "code"
	ArtifactTypeLink      ArtifactType = "link"
	ArtifactTypeImage     ArtifactType = "image"
	ArtifactTypePatch     ArtifactType = "patch"
	ArtifactTypeZip       ArtifactType = "zip"
	ArtifactTypeLogs      ArtifactType = "logs"   // an agent run should have only one logs artifact with key `logs`
	ArtifactTypeResult    ArtifactType = "result" // an agent run should have only one result artifact with key `result`
	ArtifactTypeEval      ArtifactType = "eval"
	ArtifactTypeDynamicUI ArtifactType = "dynamic_ui" // an agent run should have only one dynamic UI artifact with key `dynamic_ui`
	ArtifactTypeProject   ArtifactType = "project"

	ArtifactStatusCreating  ArtifactStatus = "creating"
	ArtifactStatusDraft     ArtifactStatus = "draft"
	ArtifactStatusCompleted ArtifactStatus = "completed"
)

type Artifact struct {
	SessionID    string         `json:"session_id"`
	AssignmentID string         `json:"assignment_id"`
	ID           string         `json:"id"`
	Type         ArtifactType   `json:"type"`
	Status       ArtifactStatus `json:"status"`
	Key          string         `json:"key"`
	Version      int32          `json:"version"`
	Files        []string       `json:"files"`
	CreatedAt    time.Time      `json:"created_at"`
	// need to be casted to the following types, type MUST match ArtifactType:
	// *PatchArtifactMetadata, *ZipArtifactMetadata, *LogsArtifactMetadata
	Metadata ArtifactMetadata `json:"metadata"`
}

type PatchArtifactMetadata struct {
	Platform   GitPlatform `json:"platform,omitempty"`
	Repository string      `json:"repository,omitempty"`
	GitURL     string      `json:"git_url,omitempty"`

	BranchName         string `json:"branch_name,omitempty"` // patch branch name
	BaseCommitSHA      string `json:"base_commit_sha,omitempty"`
	PatchCommitSHA     string `json:"patch_commit_sha,omitempty"`
	PatchCommitMessage string `json:"patch_commit_message,omitempty"`

	// platform specific merge request info
	MergeRequestID  string `json:"merge_request_id,omitempty"`
	MergeRequestURL string `json:"merge_request_url,omitempty"`

	GitRoot        string    `json:"git_root,omitempty"`
	GitFilesStatus GitStatus `json:"git_files_status,omitempty"`
}

type GitPlatform string

const (
	GitPlatformGithub   GitPlatform = "github"
	GitPlatformCodebase GitPlatform = "codebase"
	GitPlatformUnknown  GitPlatform = "unknown"
)

type GitStatus map[string]GitFileStatus

type GitFileStatus struct {
	Status  git.StatusCode     `json:"status"`
	Path    string             `json:"path"`
	OldPath string             `json:"old_path"` // if status is rename, this field is not empty
	OldMode *filemode.FileMode `json:"old_mode"`
	NewMode *filemode.FileMode `json:"new_mode"`
}

func (s GitFileStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Status  string `json:"status"`
		Path    string `json:"path"`
		OldPath string `json:"old_path,omitempty"`
		OldMode string `json:"old_mode,omitempty"`
		NewMode string `json:"new_mode,omitempty"`
	}{
		Status:  string(s.Status),
		Path:    s.Path,
		OldPath: s.OldPath,
		OldMode: lo.TernaryF(s.OldMode != nil, func() string { return s.OldMode.String() }, func() string { return "" }),
		NewMode: lo.TernaryF(s.NewMode != nil, func() string { return s.NewMode.String() }, func() string { return "" }),
	})
}

func (s *GitFileStatus) UnmarshalJSON(data []byte) error {
	var res struct {
		Status  string `json:"status"`
		Path    string `json:"path"`
		OldPath string `json:"old_path"`
		OldMode string `json:"old_mode"`
		NewMode string `json:"new_mode"`
	}
	if err := json.Unmarshal(data, &res); err != nil {
		return err
	}
	s.Status = lo.Ternary(res.Status == "", git.Unmodified, git.StatusCode(byte(res.Status[0])))
	s.Path = res.Path
	s.OldPath = res.OldPath
	if len(res.OldMode) != 0 {
		mode, err := filemode.New(res.OldMode)
		if err != nil {
			return err
		}
		s.OldMode = lo.ToPtr(mode)
	}
	if len(res.NewMode) != 0 {
		mode, err := filemode.New(res.NewMode)
		if err != nil {
			return err
		}
		s.NewMode = lo.ToPtr(mode)
	}
	return nil
}

type ZipArtifactMetadata struct {
	Packing     string   `json:"packing,omitempty"`
	Compression string   `json:"compression,omitempty"`
	Size        int64    `json:"size,omitempty"`
	Files       []string `json:"files,omitempty"`
}

// ResultArtifactMetadata stores arbitrary data created by the agent
// it should keep stable for specific agent and should not be available to the user
type ResultArtifactMetadata map[string]any

type StaticSiteDeployment struct {
	DeploymentID string
	URL          string
}

type BackendDeployment struct {
	DeploymentID string
	URL          string
	ServiceID    string
}

type LogsArtifactMetadata struct{}

// DynamicUIArtifactMetadata stores UI related data created by the agent
type DynamicUIArtifactMetadata map[string]any

func UnmarshalMetadata(typ ArtifactType, data []byte) (metadata ArtifactMetadata, err error) {
	if len(data) == 0 {
		return nil, nil
	}

	switch typ {
	case ArtifactTypePatch:
		var res PatchArtifactMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeZip:
		var res ZipArtifactMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeLogs:
		var res LogsArtifactMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeResult:
		var res ResultArtifactMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	case ArtifactTypeDynamicUI:
		var res DynamicUIArtifactMetadata
		if err = json.Unmarshal(data, &res); err == nil {
			metadata = &res
		}
	}

	if err != nil {
		return nil, err
	}
	return metadata, nil
}
