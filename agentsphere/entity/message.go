package entity

import (
	"fmt"
	"time"
)

type Conversation struct {
	ID        string
	Title     string
	CreatedAt time.Time
	UpdatedAt time.Time
}

type MentionType string

const (
	MentionTypeCodebase   MentionType = "codebase"
	MentionTypeLarkDoc    MentionType = "lark_doc"
	MentionTypeAttachment MentionType = "attachment"
	MentionTypeAeolus     MentionType = "aeolus"
	MentionTypeSnippet    MentionType = "snippet"
)

type CodebaseMention struct {
	RepoName string `json:"repo_name" mapstructure:"repo_name"`
	Branch   string `json:"branch" mapstructure:"branch"`
	Tag      string `json:"tag" mapstructure:"tag"`
	Path     string `json:"path" mapstructure:"path"` // 文件路径/目录路径
}

func (m *CodebaseMention) PromptString() string {
	if m == nil {
		return ""
	}
	s := fmt.Sprintf("%s", m.RepoName)
	if m.Branch != "" {
		s += fmt.Sprintf(":%s", m.Branch)
	}
	if m.Tag != "" {
		s += fmt.Sprintf(":%s", m.Tag)
	}
	if m.Path != "" {
		s += fmt.Sprintf(":%s", m.Path)
	}
	return s
}

type LarkDocMention struct {
	ID              string `json:"id" mapstructure:"id"`         // 数据库主键 ID
	DocID           string `json:"doc_id" mapstructure:"doc_id"` // 飞书文档 token
	Title           string `json:"title" mapstructure:"title"`
	URL             string `json:"url" mapstructure:"url"`
	KnowledgeBaseID string `json:"knowledge_base_id" mapstructure:"knowledge_base_id"`
}

func (m *LarkDocMention) PromptString() string {
	if m == nil {
		return ""
	}
	return m.Title
}

type AttachmentMentionType string

const (
	AttachmentMentionFileType           AttachmentMentionType = "file"
	AttachmentMentionLarkDocCommentType AttachmentMentionType = "lark_doc_comment"
)

type AttachmentMention struct {
	ArtifactID  string `json:"artifact_id" mapstructure:"artifact_id"`
	Path        string `json:"path" mapstructure:"path"`
	IsFromQuery bool   `json:"is_from_query" mapstructure:"is_from_query"` // 附件是否从用户 query 转换而来，如果是可能需要拼接默认 query
	Summary     string `json:"summary" mapstructure:"summary"`             // 附件内容摘要，主要是超长 query 转附件时使用，一般是从前端传过来的被裁减后的原始 query 内容

	Version             int32                 `json:"version" mapstructure:"version"`
	Type                AttachmentMentionType `json:"type" mapstructure:"type"`
	Comments            []CommentMetadata     `json:"comments" mapstructure:"comments"`
	URL                 string                `json:"url" mapstructure:"url"`
	LarkDocVersionToken string                `json:"lark_doc_version_token" mapstructure:"lark_doc_version_token"`
	Content             string                `json:"content" mapstructure:"content"`
	SubType             string                `json:"sub_type" mapstructure:"sub_type"`
	ArtifactType        ArtifactType          `json:"artifact_type" mapstructure:"artifact_type"`
}

func (m *AttachmentMention) PromptString() string {
	if m == nil {
		return ""
	}
	return m.Path
}

type CommentMetadata struct {
	CommentID string    `json:"comment_id"`
	BlockID   string    `json:"block_id"`
	Quote     string    `json:"quote"`
	CreateAt  time.Time `json:"create_at"`
	UpdateAt  time.Time `json:"update_at"`
	UserID    string    `json:"user_id"`
	Username  string    `json:"username"`
	Replies   []*Reply  `json:"replies"`
}
type Reply struct {
	ReplyContent *ReplyContent `json:"reply_content,omitempty"` // 回复内容

	ReplyID string `json:"reply_id,omitempty"` // 回复ID

	UserID string `json:"user_id,omitempty"` // 用户ID

	Username string `json:"username,omitempty"` // 用户名

	CreateAt time.Time `json:"create_time,omitempty"` // 创建时间

	UpdateAt time.Time `json:"update_time,omitempty"` // 更新时间

	Extra *ReplyExtra `json:"extra,omitempty"` // 回复的其他内容，图片token等
}

type ReplyContent struct {
	Elements []*ReplyElement `json:"elements,omitempty"` // 回复的内容
}

type ReplyElement struct {
	Type string `json:"type,omitempty"` // 回复的内容元素

	TextRun *TextRun `json:"text_run,omitempty"` // 文本内容

	DocsLink *DocsLink `json:"docs_link,omitempty"` // 文本内容

	Person *Person `json:"person,omitempty"` // 文本内容
}

type TextRun struct {
	Text          string `json:"text,omitempty"` // 回复 普通文本
	IsAimeComment bool   `json:"is_aime_comment,omitempty"`
}

type DocsLink struct {
	Link string `json:"link,omitempty"` // 文档链接
}

type Person struct {
	UserID   string `json:"person_id,omitempty"` // 人员ID
	Username string `json:"username,omitempty"`
}

type ReplyExtra struct {
	ImageList []string `json:"image_list,omitempty"` // 评论中的图片token list
}

type AeolusMention struct {
	URL string `json:"url" mapstructure:"url"`
}

type SnippetMention struct {
	Path              string             `json:"path" mapstructure:"path"`
	Content           string             `json:"content" mapstructure:"content"`
	SnippetRange      *SnippetRange      `json:"snippet_range" mapstructure:"snippet_range"`
	BelongsToArtifact *BelongsToArtifact `json:"belongs_to_artifact" mapstructure:"belongs_to_artifact"`
}

type SnippetRange struct {
	StartLine   int32  `json:"start_line" mapstructure:"start_line"`
	EndLine     int32  `json:"end_line" mapstructure:"end_line"`
	StartColumn *int32 `json:"start_column" mapstructure:"start_column"`
	EndColumn   *int32 `json:"end_column" mapstructure:"end_column"`
}

type BelongsToArtifact struct {
	ID      string `thrift:"ID,1,required" json:"id"`
	Type    string `thrift:"Type,2,required" json:"type"`
	Key     string `thrift:"Key,3,required" json:"key"`
	Version int32  `thrift:"Version,4,required" json:"version"`
}

type Mention struct {
	ID   string      `json:"id" mapstructure:"id"`
	Type MentionType `json:"type" mapstructure:"type"`

	CodebaseMention   *CodebaseMention   `json:"codebase_mention" mapstructure:"codebase_mention"`
	LarkDocMention    *LarkDocMention    `json:"lark_doc_mention" mapstructure:"lark_doc_mention"`
	AttachmentMention *AttachmentMention `json:"attachment_mention" mapstructure:"attachment_mention"`
	AeolusMention     *AeolusMention     `json:"aeolus_mention" mapstructure:"aeolus_mention"`
	SnippetMention    *SnippetMention    `json:"snippet_mention" mapstructure:"snippet_mention"`
}

func (m *Mention) PromptString() string {
	if m == nil {
		return ""
	}
	var s string
	switch m.Type {
	case MentionTypeCodebase:
		s = m.CodebaseMention.PromptString()
	case MentionTypeLarkDoc:
		s = m.LarkDocMention.PromptString()
	case MentionTypeAttachment:
		s = m.AttachmentMention.PromptString()
	}
	return fmt.Sprintf("@%s ", s)
}

type Message struct {
	ID             string
	ConversationID string
	Number         int
	Type           MessageType
	Creator        User
	ParentID       string
	Content        MessageContent
	Attachments    []AttachmentMeta
	Mentions       []*Mention
	Options        MessageOptions
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

type MessageType string

const (
	MessageTypeNormal MessageType = "normal"
	MessageTypeNotice MessageType = "notice"
)

type MessageContent struct {
	Content string `json:"content"`
}

type MessageOptions struct {
	Locale string `json:"locale" mapstructure:"locale"`
}

type AttachmentMeta struct {
	ArtifactID string `json:"artifact_id"`
	Filename   string `json:"filename"`
}
