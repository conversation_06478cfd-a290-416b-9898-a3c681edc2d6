package entity

import (
	"errors"
	"fmt"
	"strings"
)

// 基于模版经验运行 agent 任务的相关参数
type ExpRunParameters struct {
	UserQueryPlaceholders map[string]string `json:"user_query_placeholders"`

	UserQueryTemplate *ExpUserQueryTemplate `json:"user_query_template,omitempty"`
	ProgressPlan      *string               `json:"progress_plan,omitempty"`
	SOP               *ExpSOP               `json:"sop,omitempty"`
}

type ExpUserQueryTemplate struct {
	Name                  string        `json:"name"`
	UserQuery             string        `json:"user_query"`
	UserQueryPlaceholders []Placeholder `json:"user_query_placeholders"`
}

type ExpSOP struct {
	Name string `json:"name"`
	// 清晰简洁的用户任务模版，使用变量
	UserQuery             string        `json:"user_query"`
	UserQueryPlaceholders []Placeholder `json:"user_query_placeholders"`
	// 什么时候应该使用这个经验模版
	UsedWhen string `json:"used_when"`
	// 使用 TODO list 来指导任务的执行
	ProgressPlan string `json:"progress_plan"`
	// 具体的执行 workflow/SOP
	PlanSteps []*ExpSOPPlanStep `json:"plan_steps"`

	EvaluateResult *ExpEvaluateResult `json:"evaluate_result,omitempty"`

	// 模版经验临时测试，线上不会用到
	Knowledges []*ExpSOPKnowledge `json:"knowledges,omitempty"`
}

type ExpSOPKnowledge struct {
	// Short title of the knowledge.
	Title string `json:"title"`
	// when to use this knowledge.
	UsedWhen string `json:"used_when"`
	// knowledge content.
	Content string `json:"content"`
}

type ExpEvaluateAspect struct {
	Aspect        string `json:"aspect"`
	Score         int    `json:"score"`
	Justification string `json:"justification"`
}

type ExpEvaluateResult struct {
	Score   int                 `json:"score"`
	Aspects []ExpEvaluateAspect `json:"aspects"`
}

func (sop *ExpSOP) ToXMLString() string {
	var xmlContent strings.Builder

	xmlContent.WriteString(fmt.Sprintf("<sop name=\"%s\">\n", sop.Name))

	xmlContent.WriteString("<user_query_template>\n")
	xmlContent.WriteString(fmt.Sprintf("<template>\n%s\n</template>\n", sop.UserQuery))
	for _, ph := range sop.UserQueryPlaceholders {
		xmlContent.WriteString(fmt.Sprintf("<placeholder name=\"%s\" default=\"%s\" description=\"%s\"></placeholder>\n", ph.Name, ph.Default, ph.Description))
	}
	xmlContent.WriteString("</user_query_template>\n")

	xmlContent.WriteString(fmt.Sprintf("<used_when>\n%s\n</used_when>\n", sop.UsedWhen))

	xmlContent.WriteString(fmt.Sprintf("<progress_plan>\n%s\n</progress_plan>\n", sop.ProgressPlan))

	xmlContent.WriteString("<plan_steps>\n")
	for _, step := range sop.PlanSteps {
		xmlContent.WriteString(fmt.Sprintf("<step name=\"%s\">\n", step.Name))
		xmlContent.WriteString(fmt.Sprintf("<objective>\n%s\n</objective>\n", step.Objective))
		xmlContent.WriteString(fmt.Sprintf("<assigned_to>\n%s\n</assigned_to>\n", step.AssignedTo))
		if len(step.Toolsets) > 0 {
			xmlContent.WriteString("<toolsets>\n")
			xmlContent.WriteString(strings.Join(step.Toolsets, ",") + "\n")
			xmlContent.WriteString("</toolsets>\n")
		}
		if len(step.Persona) > 0 {
			xmlContent.WriteString(fmt.Sprintf("<persona>\n%s\n</persona>\n", step.Persona))
		}
		if len(step.Parameters) > 0 {
			xmlContent.WriteString("<parameters>\n")
			for k, v := range step.Parameters {
				xmlContent.WriteString(fmt.Sprintf("<%s>\n%s\n</%s>\n", k, v, k))
			}
			xmlContent.WriteString("</parameters>\n")
		}
		if len(step.Phases) > 0 {
			xmlContent.WriteString("<phases>\n")
			for _, ph := range step.Phases {
				xmlContent.WriteString(fmt.Sprintf("<phase name=\"%s\">\n", ph.Name))
				xmlContent.WriteString(fmt.Sprintf("<objective>\n%s\n</objective>\n", ph.Objective))
				xmlContent.WriteString("<reusable_materials>\n")
				for _, r := range ph.References {
					if len(r.Error) > 0 {
						xmlContent.WriteString(fmt.Sprintf("- %s (error: %s)\n", r.Filepath, r.Error))
					} else {
						xmlContent.WriteString(fmt.Sprintf("- %s\n", r.Filepath))
					}
				}
				xmlContent.WriteString("</reusable_materials>\n")
				xmlContent.WriteString(fmt.Sprintf("<actions>\n%s\n</actions>\n", ph.Actions))
				xmlContent.WriteString("<deliverables>\n")
				for _, d := range ph.Deliverable {
					xmlContent.WriteString(fmt.Sprintf("- [%s] (%s)\n", d, d))
				}
				xmlContent.WriteString("</deliverables>\n")
				xmlContent.WriteString("</phase>\n")
			}
			xmlContent.WriteString("</phases>\n")
		}
		xmlContent.WriteString("</step>\n")
	}
	xmlContent.WriteString("</plan_steps>\n")

	xmlContent.WriteString("</sop>")

	return xmlContent.String()
}

func (sop *ExpSOP) Render(values map[string]string, needValidate bool) error {
	render := func(content string) string {
		for _, ph := range sop.UserQueryPlaceholders {
			value := ph.Default
			if v, ok := values[ph.Name]; ok {
				value = v
			}
			content = strings.ReplaceAll(content, "{"+ph.Name+"}", value)
		}
		return content
	}

	if needValidate {
		for _, ph := range sop.UserQueryPlaceholders {
			if _, ok := values[ph.Name]; !ok {
				return errors.New("placeholder not found: " + ph.Name)
			}
		}
		return nil
	}

	sop.UserQuery = render(sop.UserQuery)
	for _, step := range sop.PlanSteps {
		step.Objective = render(step.Objective)
		for k := range step.Parameters {
			step.Parameters[k] = render(step.Parameters[k])
		}
		for _, ph := range step.Phases {
			ph.Actions = render(ph.Actions)
			ph.Objective = render(ph.Objective)
			for idx := range ph.Deliverable {
				ph.Deliverable[idx] = render(ph.Deliverable[idx])
			}
		}
	}

	return nil
}

type Placeholder struct {
	Name            string `json:"name"`
	Default         string `json:"default"`
	Description     string `json:"description"`
	RefAattachments bool   `json:"ref_attachments"`
	Required        bool   `json:"required"`
}

type ExpSOPPlanStep struct {
	Name       string `json:"name"`
	AssignedTo string `json:"assigned_to"`

	// Mewtwo parameters.
	Objective string                 `json:"objective"`
	Persona   string                 `json:"persona"`
	Toolsets  []string               `json:"toolsets"`
	Phases    []*ExpSOPPlanStepPhase `json:"phases"`
	// Other parameters for agents such as `ask_user`, `task_concluder` and a2a third-party agents.
	Parameters map[string]string `json:"parameters"`
}

type ReferenceFile struct {
	ID       string `json:"id"`
	Filepath string `json:"filepath"`
	Content  string `json:"content"`
	Error    string `json:"error"`
	Binary   bool   `json:"binary"`
}

type ExpSOPPlanStepPhase struct {
	Name string `json:"name"`
	// 这个步骤应该做什么
	Objective string `json:"objective"`
	// 这个步骤需要做什么，详细的执行流程
	Actions string `json:"actions"`
	// 在执行这一步的时候可以用于参考的之前任务的脚本、文档、代码等
	References []ReferenceFile `json:"references"`
	// 这个步骤应该产出什么，例如文件、目录、数据库表等
	Deliverable []string `json:"deliverable"`
}

type ProgressPlan struct {
	Plan  string             `json:"plan" xml:"plan"`
	Steps []ProgressPlanStep `json:"steps" xml:"steps>step"`
}

type ProgressPlanStep struct {
	Agent string `json:"agent" xml:"agent"`
	Task  string `json:"task" xml:"task"`
}

type ExpReusableWorkflow struct {
	// interger id, easy for LLM to select.
	Number      string                    `json:"number"`
	ID          string                    `json:"id"`
	Title       string                    `json:"title"`
	UsedWhen    string                    `json:"used_when"`
	Description string                    `json:"description"`
	Steps       []ExpReusableWorkflowStep `json:"steps"`
}

type ExpReusableWorkflowStep struct {
	Tool        string `json:"tool"`
	Description string `json:"description"`
}
