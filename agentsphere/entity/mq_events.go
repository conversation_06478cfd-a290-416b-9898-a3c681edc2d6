package entity

import "time"

const (
	RuntimeOrchestrationTag     = "runtime_orchestrator"
	AssignmentMonitoringTag     = "assignment_monitor"
	NextRuntimeOrchestrationTag = "next_runtime_orchestrator"
	NextSessionMonitorTag       = "next_session_monitor"
	KnowledgebaseTag            = "knowledgebase"
	NextTraceTag                = "next_trace"
	DeployReviewTag             = "deploy_review"
	TestingMonitorTag           = "next_testing_monitor"
	TaskCronJobTag              = "task_cronjob"
	KnowledgebaseOfflineTag     = "knowledgebase_offline"
)

type RuntimeOrchestrationEvent struct {
	RuntimeAllocationEvent *RuntimeAllocationEvent `json:"runtime_allocation,omitempty"`
	AgentRunTimeoutEvent   *AgentRunTimeoutEvent   `json:"agent_run_timeout,omitempty"`
	RecycleContainerEvent  *RecycleContainerEvent  `json:"recycle_container,omitempty"`
	PrepareContainerEvent  *PrepareContainerEvent  `json:"prepare_container,omitempty"`
	RuntimeScheduleEvent   *RuntimeScheduleEvent   `json:"runtime_schedule,omitempty"`
	CreateWorkspaceEvent   *CreateWorkspaceEvent   `json:"create_workspace,omitempty"`
	ReclaimWorkspaceEvent  *ReclaimWorkspaceEvent  `json:"reclaim_workspace,omitempty"`

	// for next agent runtime
	NextRuntimeScheduleEvent                   *NextRunTimeScheduleEvent                   `json:"next_runtime_schedule,omitempty"`
	NextRuntimeCreateWorkspaceEvent            *NextRuntimeCreateWorkspaceEvent            `json:"next_runtime_create_workspace,omitempty"`
	NextRuntimeStopWorkspaceEvent              *NextRuntimeStopWorkspaceEvent              `json:"next_runtime_stop_workspace,omitempty"`
	NextRuntimeDeleteWorkspaceEvent            *NextRuntimeDeleteWorkspaceEvent            `json:"next_runtime_delete_workspace,omitempty"`
	NextRuntimeImmediatelyDeleteWorkspaceEvent *NextRuntimeImmediatelyDeleteWorkspaceEvent `json:"next_runtime_immediately_delete_workspace,omitempty"`
	// TODO(add prepare workspace event)
	NextRuntimeSuspendDebugEvent *NextRuntimeSuspendDebugEvent `json:"next_runtime_suspend_debug,omitempty"`
}

type NextRuntimeSuspendDebugEvent struct {
	RunID string `json:"run_id"`
}

type NextRunTimeScheduleEvent struct {
	SessionID string `json:"session_id"`
	Agent     string `json:"agent"`
	Version   string `json:"version"`
}

type NextRuntimeCreateWorkspaceEvent struct {
	SessionID string `json:"session_id"`
	Agent     string `json:"agent"`
	Version   string `json:"version"`
}

type NextRuntimeStopWorkspaceEvent struct {
	SessionID   string     `json:"session_id"`
	WorkspaceID string     `json:"workspace_id"`
	Agent       string     `json:"agent"`
	Version     string     `json:"version"`
	Provider    string     `json:"provider"`
	Immediately bool       `json:"immediately"`
	Restart     bool       `json:"restart"`
	EventTime   *time.Time `json:"event_time,omitempty"`
}

type NextRuntimeDeleteWorkspaceEvent struct {
	SessionID   string `json:"session_id"`
	WorkspaceID string `json:"workspace_id"`
	Agent       string `json:"agent"`
	Version     string `json:"version"`
	Provider    string `json:"provider"`
	Immediately bool   `json:"immediately"`
}

type NextRuntimeImmediatelyDeleteWorkspaceEvent struct {
	SessionID   string `json:"session_id"`
	WorkspaceID string `json:"workspace_id"`
	Agent       string `json:"agent"`
	Version     string `json:"version"`
	Provider    string `json:"provider"`
}

type ReclaimWorkspaceEvent struct {
	SessionID   string `json:"session_id"`
	WorkspaceID string `json:"workspace_id"`
	Agent       string `json:"agent"`
	Version     string `json:"version"`
	Provider    string `json:"provider"`
}

type RuntimeScheduleEvent struct {
	SessionID string `json:"session_id"`
	Agent     string `json:"agent"`
	Version   string `json:"version"`
}

type CreateWorkspaceEvent struct {
	SessionID string `json:"session_id"`
	Agent     string `json:"agent"`
	Version   string `json:"version"`
}

type RuntimeAllocationEvent struct {
	BatchID      string `json:"batch_id,omitempty"` // used to limit max concurrency for a batch
	AssignmentID string `json:"assignment_id"`
	SessionID    string `json:"session_id"`
}

type AgentRunTimeoutEvent struct {
	AssignmentID string `json:"assignment_id"`
	Timeout      string `json:"timeout"`
}

type RecycleContainerEvent struct {
	AssignmentID string `json:"assignment_id"`
	SessionID    string `json:"session_id"`
}

type PrepareContainerEvent struct {
	Agent   string `json:"agent"`
	Version string `json:"version"`
}

type AssignmentMonitoringEvent struct {
	AssignmentStatusUpdate *AssignmentStatusUpdate `json:"assignment_status_update,omitempty"`
	AssignmentTimeoutEvent *AssignmentTimeoutEvent `json:"assignment_timeout,omitempty"`
}

type AssignmentStatusUpdate struct {
	AssignmentID string           `json:"assignment_id"`
	Status       AssignmentStatus `json:"status"`
}

type AssignmentTimeoutEvent struct {
	AssignmentID string `json:"assignment_id"`
	Timeout      string `json:"timeout"`
}
