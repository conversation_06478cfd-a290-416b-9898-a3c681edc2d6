package agents

import (
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"github.com/samber/lo"
)

type PromptCache struct {
	PromptCacheKey       string // Used to route the same series agent llm request to the same cluster or machine to improve cache hit ratio.
	MaxCachePoints       int
	CacheType            string
	NoCacheLastNMessages int          // Not to add cache point for the last N messages.
	CachePoints          map[int]bool // Last request's cache points.
	NewCachePointChars   int          // Chars threshold for adding new cache point. Every N chars to add a new cache point.
}

func CountPromptCachePoints(msgs []*framework.ChatMessage) int {
	return lo.CountBy(msgs, func(msg *framework.ChatMessage) bool {
		for idx := range msg.ContentParts {
			if msg.ContentParts[idx].CacheControl != "" {
				return true
			}
		}
		return msg.CacheControl != ""
	})
}

func (c *PromptCache) UpdateCachePoints(msgs []*framework.ChatMessage) {
	// 非 claude 模型的 prompt cache 不用设置 cache point
	if c.<PERSON>achePoints <= 0 {
		return
	}
	msgCount := len(msgs)
	for idx, cached := range c.CachePoints {
		if !cached {
			continue
		}
		if idx >= msgCount {
			c.CachePoints[idx] = false
			continue
		}
		msgs[idx].SetCachePoint(c.CacheType)
	}

	// Calculate the new cache point position.
	_, lastCachePointIdx, ok := lo.FindLastIndexOf(msgs, func(msg *framework.ChatMessage) bool {
		return msg.IsSetCachePoint()
	})
	if !ok {
		lastCachePointIdx = 0
	}
	newCachePoint := msgCount - c.NoCacheLastNMessages - 1
	if newCachePoint < lastCachePointIdx {
		newCachePoint = lastCachePointIdx
	}

	newChars := 0
	for idx := lastCachePointIdx + 1; idx <= newCachePoint; idx++ {
		newChars += len(msgs[idx].Content)
		newChars += len(msgs[idx].ReasoningContent)
		for _, part := range msgs[idx].ContentParts {
			if part.Text != nil {
				newChars += len(*part.Text)
			}
			// FIXME(cyx): how to calculate the image tokens?
			if part.ImageURL != nil {
				newChars += 1500
			}
		}
		for _, tool := range msgs[idx].ToolCalls {
			newChars += len(conv.JSONFormatString(tool))
		}
	}

	if newCachePoint > lastCachePointIdx && newChars >= c.NewCachePointChars {
		msgs[newCachePoint].SetCachePoint(c.CacheType)
		c.CachePoints[newCachePoint] = true
	}

	count := CountPromptCachePoints(msgs)
	if count <= c.MaxCachePoints {
		return
	}

	for idx := range msgs {
		if count > c.MaxCachePoints && msgs[idx].IsSetCachePoint() {
			msgs[idx].UnsetCachePoint()
			c.CachePoints[idx] = false
			count--
		}
	}
}
