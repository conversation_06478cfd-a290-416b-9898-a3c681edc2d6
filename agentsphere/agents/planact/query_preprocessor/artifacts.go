package queryrecognizer

import (
	_ "embed"
	"fmt"
	"strings"

	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"github.com/samber/lo"
)

type ArtifactsPreprocessor struct{}

type ArtifactsAnnotationStore struct {
	ExpectedArtifacts string `json:"expected_artifacts"`
}

const ArtifactsAnnotationStoreKey = "artifacts_annotation_store"

var _ MentionPreprocessor = &ArtifactsPreprocessor{}

var (
	//go:embed prompt/artifact_annotation_system.go.tmpl
	artifactsSystemPrompt         string
	ArtifactsSystemPromptTemplate = prompt.MustGetTemplate("ArtifactsSystemPrompt", artifactsSystemPrompt)
	//go:embed prompt/artifact_annotation_user.go.tmpl
	artifactsUserPrompt         string
	ArtifactsUserPromptTemplate = prompt.MustGetTemplate("ArtifactsUserPrompt", artifactsUserPrompt)
)

func (p *ArtifactsPreprocessor) Visible() bool {
	return false
}

func (p *ArtifactsPreprocessor) Preprocess(run *iris.AgentRunContext, mentions []iris.Mention) error {
	logger := run.GetLogger()
	logger.Infof("start artifacts annotate")
	store := iris.RetrieveStoreByKey[ArtifactsAnnotationStore](run, ArtifactsAnnotationStoreKey)
	defer func() {
		logger.Infof("end artifacts annotate, expected artifact: %s", store.ExpectedArtifacts)
		iris.UpdateStoreByKey(run, ArtifactsAnnotationStoreKey, store)
	}()

	messages := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: prompt.TemplateSet{
			SystemTmpl: ArtifactsSystemPromptTemplate,
			UserTmpl:   ArtifactsUserPromptTemplate,
		},
		UserPromptVariables: map[string]any{"Conversations": lo.Reduce(run.State.Conversation.GetMessages(), func(agg string, msg *iris.Message, index int) string {
			currentMsg := lo.Ternary(msg.From == iris.MessageFromUser || msg.From == iris.MessageResult, fmt.Sprintf("%s:\n%s\n\n", msg.From, msg.Content), "")
			if msg.From == iris.MessageFromUser && len(msg.Attachments) > 0 {
				currentMsg += fmt.Sprintf("Attachments:\n%s", lo.Map(msg.Attachments, func(attachment iris.Attachment, _ int) string {
					return attachment.Path + "\n"
				}))
			}
			return agg + currentMsg
		}, "")},
	})

	result, err := agents.Think(
		run,
		"artifacts_annotation",
		messages,
		agents.ThinkOption{DisableThinkDelta: true},
	)
	if err != nil {
		logger.Errorf("failed to think artifacts annotation: %v", err)
		return err
	}
	tags, err := prompt.ParseTopTagsV2(result.Content)
	if err != nil {
		logger.Errorf("failed to parse top tags: %v", err)
		return err
	}
	for _, tag := range tags {
		if tag.XMLName.Local == "artifacts" {
			store.ExpectedArtifacts = "\n\n[Expected Artifacts]\n" + strings.TrimSpace(tag.Content)
		}
	}

	return nil
}

func (p *ArtifactsPreprocessor) GetPreprocessedItems(run *iris.AgentRunContext) PreprocessedItem {
	return PreprocessedItem{}
}

func (p *ArtifactsPreprocessor) GetPreprocessedItemsWithMsgs(run *iris.AgentRunContext, messages []*iris.Message) PreprocessedItem {
	// Artifacts preprocessing is message-independent, so we just return the same result
	return p.GetPreprocessedItems(run)
}
