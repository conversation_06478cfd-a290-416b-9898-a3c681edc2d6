package queryrecognizer

import (
	"fmt"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"github.com/samber/lo"
)

// RewriteMentions rewrites the mentions in the messages `[resource](aime://resource_type/resource_id)` to the prompt string `[resource_name](resource_uri)`.
func RewriteMentions(messages []*iris.Message) error {
	lo.ForEach(messages, func(msg *iris.Message, _ int) {
		lo.ForEach(msg.Mentions, func(mention iris.Mention, _ int) {
			msg.Content = rewriteMention(msg.Content, []iris.Mention{mention})
		})
	})
	return nil
}

func rewriteMention(content string, mentions []iris.Mention) string {
	lo.ForEach(mentions, func(mention iris.Mention, _ int) {
		mentionLink := fmt.Sprintf("[resource](aime://mention/%s)", mention.GetID())
		// add extra spaces to avoid the mention link being merged with the previous word and affect url parsing
		content = strings.ReplaceAll(content, mentionLink, fmt.Sprintf(" %s ", mention.PromptString()))
		copilotStr := mention.CopilotString()
		if len(content) != 0 && len(copilotStr) != 0 {
			content = content + "\n" + copilotStr
		}
	})
	// if the content is empty and there is an attachment mention from query, add a default content
	if len(content) == 0 && lo.SomeBy(mentions, func(mention iris.Mention) bool {
		if mention.GetType() == iris.MentionTypeAttachment {
			m, ok := mention.(*iris.AttachmentMention)
			if ok && m.IsFromQuery {
				return true
			}
		}
		return false
	}) {
		return "Execute instructions in the attachments"
	}
	return content
}
