package planact_agent

import (
	"bytes"
	"context"
	"fmt"
	"strings"

	"github.com/go-enry/go-enry/v2"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	reporteractor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/reporter"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/prompts"
	agentutil "code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"

	"code.byted.org/gopkg/pkg/errors"

	"code.byted.org/devgpt/kiwis/lib/conv"
)

// already used experience template id
const (
	ExperienceAlreadyUsed = "experience_already_used"
)

func (a *PlanActAgent) getExperience(run *iris.AgentRunContext) (exp *agententity.ExpRunParameters, err error) {
	runWithExp, ok := run.Parameters[agententity.RuntimeParametersRunWithExperience]
	if !ok || conv.DefaultAny[string](runWithExp) == ExperienceAlreadyUsed {
		return nil, nil
	}

	run.GetLogger().Infof("found experience parameters:\n%s", conv.JSONFormatString(runWithExp))

	expParams, err := conv.MapToStructByJSONTag[agententity.ExpRunParameters](conv.DefaultAny[map[string]any](runWithExp))
	if err != nil {
		run.GetLogger().Errorf("failed to parse experience parameters: %v", err)
		return nil, errors.WithMessage(err, "failed to parse experience parameters")
	}

	return lo.ToPtr(expParams), nil
}

func (a *PlanActAgent) generateExperienceByLarkTemplate(run *iris.AgentRunContext, exp *agententity.ExpRunParameters) (step *iris.AgentRunStep, err error) {
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	if store.ExperienceProgressPlan != "" {
		// already used experience
		return step, nil
	}
	//sop := exp.SOP
	placeholders := exp.UserQueryPlaceholders
	larkTemplate := placeholders["lark_template"]
	if len(larkTemplate) == 0 {
		run.GetLogger().Errorf("lark_template is empty")
		return nil, errors.New("lark_template is empty")
	}
	tasks, err := lark.DownloadLarkComments(run, lark.LarkToolArgs{DocumentURL: larkTemplate})
	if err != nil {
		run.GetLogger().Errorf("failed to download lark comments: %v", err)
		return nil, errors.WithMessage(err, "failed to download lark comments")
	}

	appendix := ""

	if val, ok := tasks["aime_tasks"]; ok {
		task := val.([]lark.AimeTask)
		for _, subTask := range task {
			appendix += fmt.Sprintf("%s\n%s\n", subTask.Quote, subTask.Content)
		}

	}

	genSop, err := genexp.GenerateLarkTemplateSOP(context.Background(), genexp.GenerateLarkTemplateSOPOption{
		Tasks: tasks,
		LLM:   run.GetLLM(),
		Model: run.GetConfig().GetModelByScene("gen_exp_sop"),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to generate SOP: %v", err)
	} else {
		run.GetLogger().Infof("generated SOP: %v", genSop)
		sb := strings.Builder{}
		sb.WriteString("## Progress Plan\n")
		sb.WriteString(genSop.ProgressPlan)
		sb.WriteString("\n\n")
		sb.WriteString("## SOP\n")
		for _, planStep := range genSop.PlanSteps {
			sb.WriteString("### ")
			sb.WriteString(planStep.Name)
			sb.WriteString("\n")
			sb.WriteString(planStep.Objective)
			sb.WriteString("\n")
			for _, phase := range planStep.Phases {
				sb.WriteString("#### ")
				sb.WriteString(phase.Name)
				sb.WriteString("\nObjective: ")
				sb.WriteString(phase.Objective)
				sb.WriteString("\nActions: ")
				sb.WriteString(phase.Actions)
			}
		}
		appendix = sb.String()
	}

	store.ExperienceProgressPlan = appendix
	run.GetLogger().Infof("append requirements: %s", appendix)
	iris.UpdateStoreByKey(run, entity.PlannerStoreKey, store)

	return nil, nil
}

func (a *PlanActAgent) prepareExperience(run *iris.AgentRunContext) (step *iris.AgentRunStep, err error) {
	exp, err := a.getExperience(run)
	if err != nil {
		return nil, err
	}

	// Currently, only SOP experience is supported.
	if exp == nil || exp.SOP == nil || (len(exp.SOP.PlanSteps) == 0 && exp.SOP.UsedWhen != "lark_template") {
		run.GetLogger().Errorf("no sop experience is found")
		return nil, nil
	}

	if exp.SOP.UsedWhen == "lark_template" {
		return a.generateExperienceByLarkTemplate(run, exp)
	}

	// Download reference files.
	for _, step := range exp.SOP.PlanSteps {
		for _, phase := range step.Phases {
			for idx, ref := range phase.References {
				run.GetLogger().Infof("downloading template exp ref file: %s, id: %s, size: %d, error: %s", ref.Filepath, ref.ID, len(ref.Content), ref.Error)
				if len(ref.Filepath) == 0 || len(ref.Error) > 0 {
					continue
				}
				if len(ref.ID) != 0 {
					phase.References[idx].Content, err = run.GetArtifactService().DownloadTemplateExperienceFile(run, ref.ID)
					if err != nil {
						run.GetLogger().Errorf("failed to download file %s: %v", ref.ID, err)
						continue
					}
					if enry.IsBinary([]byte(phase.References[idx].Content)) {
						phase.References[idx].Binary = true
						run.GetLogger().Infof("file %s is binary, size: %d", ref.ID, len(phase.References[idx].Content))
					}
				}
			}
		}
	}

	run.Parameters[agententity.RuntimeParametersRunWithExperience] = ExperienceAlreadyUsed
	step = run.CreateStep(&iris.CreateStepOption{ExecutorAgent: a.planner.Name()})
	step.Thought = &iris.Thought{
		Tool:       string(agententity.RuntimeParametersRunWithExperience),
		Content:    "use the predefined experience",
		Parameters: map[string]any{},
	}

	sop := exp.SOP

	logger := run.GetLogger()
	err = sop.Render(exp.UserQueryPlaceholders, false)
	if err != nil {
		logger.Errorf("failed to render trace: %v", err)
	}
	var plan entity.Plan
	inputTemplate := prompts.GetPhaseInputTemplate(run.GetConfig().GetVariantByScene("planner"))
	for _, step := range sop.PlanSteps {
		// SOP Step for non-mewtwo agents such as `ask_user`, `task_concluder` and a2a third-party agents.
		if step.AssignedTo != "mewtwo" {
			input := &bytes.Buffer{}
			inputRenderErr := inputTemplate.Execute(input, map[string]any{
				"Actor":      step.AssignedTo,
				"Parameters": step.Parameters,
			})
			if inputRenderErr != nil {
				logger.Errorf("failed to render non-mewtwo step input: %v", inputRenderErr)
			}
			curStep := entity.PlanStep{
				ReuseTrace:  false,
				ActorName:   step.AssignedTo,
				Input:       input.String(),
				Description: step.Name,
				Reference:   iris.Reference{},
			}
			plan.Steps = append(plan.Steps, curStep)
			continue
		}
		// Prepare step for mewtwo.
		for idx, phase := range step.Phases {
			input := &bytes.Buffer{}
			refs := make([]agententity.ReferenceFile, 0, len(phase.References))
			stepRefs := iris.Reference{}
			for _, ref := range phase.References {
				if len(ref.Content) == 0 || len(ref.Filepath) == 0 {
					logger.Infof("skip empty reference file %s: %s", ref.ID, ref.Filepath)
					continue
				}
				// TODO(cyx): 更好的检查产物是否可以复用
				if !genexp.IsTemplateReferenceFileReusable(ref.Filepath) {
					logger.Infof("skip non-reusable reference file %s: %s", ref.ID, ref.Filepath)
					continue
				}
				refs = append(refs, ref)
				stepRefs = append(stepRefs, iris.ReferenceItem{
					URI: ref.Filepath,
					MetaData: map[string]any{
						"content": ref.Content,
					},
				})
			}
			inputRenderErr := inputTemplate.Execute(input, map[string]any{
				"Phase":     phase,
				"PhaseRefs": refs,
				"Persona":   step.Persona,
				"Tools":     step.Toolsets,
				"Actor":     step.AssignedTo,
			})
			if inputRenderErr != nil {
				logger.Errorf("failed to render phase input: %v", inputRenderErr)
			}
			curStep := entity.PlanStep{
				ReuseTrace:  idx != 0,
				ActorName:   step.AssignedTo,
				Input:       input.String(),
				Description: phase.Name,
				Reference:   stepRefs,
			}
			plan.Steps = append(plan.Steps, curStep)
		}
	}
	logger.Infof("load sop experience with %d phases", len(plan.Steps))
	if len(plan.Steps) == 0 || plan.Steps[len(plan.Steps)-1].ActorName != reporteractor.Identifier {
		plan.Steps = append(plan.Steps, entity.PlanStep{
			ActorName:   reporteractor.Identifier,
			Description: "汇报最终结果",
			Input:       "summarize and report the final result",
		})
	}

	step.Conclusion = map[string]any{
		"plan": agentutil.ValueToMap(plan),
	}

	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	if run.Err() == nil {
		store.CurRound++
		iris.UpdateStoreByKey(run, entity.PlannerStoreKey, store)
		a.planner.IncreaseCurrentRound(run)
	}

	return step, nil
}
