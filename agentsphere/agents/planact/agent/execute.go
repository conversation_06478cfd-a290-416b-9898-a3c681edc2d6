package planact_agent

import (
	"bytes"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/browseract"
	dynamicactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/dynamic"
	reporteractor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/reporter"
	responseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/response"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/tracing"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/streamparser"

	"github.com/google/uuid"
	"github.com/samber/lo"
)

const (
	MaxExecutionSteps = 30
)

func (a *PlanActAgent) getPlan(step *iris.AgentRunStep) entity.Plan {
	var plan entity.Plan
	mapstructure.Decode(step.Conclusion["plan"], &plan)
	if len(plan.Steps) == 0 {
		return entity.Plan{}
	}
	if plan.Steps[len(plan.Steps)-1].ActorName != reporteractor.IdentifierV2 && plan.Steps[len(plan.Steps)-1].ActorName != ReplanActorName {
		plan.Steps = append(plan.Steps, entity.PlanStep{
			ActorName:   reporteractor.Identifier,
			Description: "汇报最终结果",
			Input:       "summarize and report the final result",
		})
	}
	return plan
}

type CreateActorOption struct {
	actorInput    string
	currentStep   entity.PlanStep
	trace         []entity.PlanStep
	previousActor actors.WrappedActor
	planVersion   int
	idx           int
	step          *iris.AgentRunStep
}

// createActor creates an actor for the current step
func (a *PlanActAgent) createActor(run *iris.AgentRunContext, opt CreateActorOption) (actors.WrappedActor, *PlanStep, error) {
	var (
		actor            actors.WrappedActor
		planStepInstance *PlanStep
		ok               bool
		err              error
	)

	if opt.currentStep.ReuseTrace {
		actor = opt.previousActor
		planStepInstance, err = a.createPlanStep(run, opt.planVersion, opt.idx, opt.step, actor.Name(), opt.actorInput)
		if err != nil {
			return nil, nil, err
		}
		actor.SetExecutionStep(planStepInstance.actorStep)
	} else {
		if !a.actorFactory.HasActor(run, opt.currentStep.ActorName) {
			return nil, nil, fmt.Errorf("actor %s is not found", opt.currentStep.ActorName)
		}
		planStepInstance, err = a.createPlanStep(run, opt.planVersion, opt.idx, opt.step, opt.currentStep.ActorName, opt.actorInput)
		if err != nil {
			return nil, nil, err
		}
		actor, ok = a.actorFactory.Create(run, opt.currentStep.ActorName, ActorDefOption{ActorInput: opt.actorInput, ActorStep: planStepInstance.actorStep})
		if !ok {
			return nil, nil, fmt.Errorf("actor %s is not found", opt.currentStep.ActorName)
		}
	}

	return actor, planStepInstance, nil
}

func (a *PlanActAgent) prepareActorInput(run *iris.AgentRunContext, currentStep entity.PlanStep, trace []entity.PlanStep) string {
	// 如果是预定义的 workflow，那么 execution trace 里只渲染每个 planstep 的 output
	_, ok := run.Parameters[agententity.RuntimeParametersRunWithExperience]
	executionTrace, _ := executionPrompt(trace, !ok, MaxExecutionSteps)

	actorInput, _ := prompts.Set.ExecutePrompt(prompts.ActorInputPrompt, "", map[string]any{
		"Input":          currentStep.Input,
		"ExecutionTrace": lo.Ternary(currentStep.ReuseTrace, "", executionTrace),
	})

	return actorInput
}

// executeActor executes an actor and returns its result
func (a *PlanActAgent) executeActor(run *iris.AgentRunContext, actor actors.Actor, planStepInstance *PlanStep, actorInput string, reuseTrace bool) (controltool.ConclusionOutput, time.Duration, error) {
	publisher := run.GetPublisher()

	// TODO(zsc): remove actorInput here
	planStepInstance.actorStep.Inputs["actor_input"] = actorInput
	planStepInstance.actorStep.Status = iris.AgentRunStepStatusRunning
	// 上报 running 状态
	publisher.ReportStep(planStepInstance.actorStep)

	actorExecStartAt := time.Now()
	result := actor.Run(run, actorInput, actors.RunOptions{ReuseTrace: reuseTrace})
	execTime := time.Since(actorExecStartAt)

	// 添加对结果的验证
	var err error
	if result.Evaluation == controltool.ConclusionEvaluationFailed {
		err = fmt.Errorf("actor %s execution failed", actor.Name())
	}
	if result.Evaluation == controltool.ConclusionEvaluationFatal {
		err = fmt.Errorf("%s", result.Content) // this will be displayed to user
	}

	return result, execTime, err
}

// handleActorResult processes the actor's result
func (a *PlanActAgent) handleActorResult(run *iris.AgentRunContext, currentStep *entity.PlanStep, planStepInstance *PlanStep, result controltool.ConclusionOutput, execTime time.Duration) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	defer func() {
		if currentStep.ActorName == websearch.Identifier || currentStep.ActorName == browseract.Identifier {
			currentStep.Reference = iris.Reference{}
			currentStep.ProjectReference = iris.ProjectReference{}
		}
	}()

	// 更新引用
	switch currentStep.ActorName {
	case websearch.Identifier, browseract.Identifier:
		a.UpdateReference(run, &result, entity.ReferenceTypeSearched)
	default:
		a.UpdateReference(run, &result, entity.ReferenceTypeCreated)
	}

	currentStep.Output, currentStep.Evaluation, currentStep.Reference, currentStep.ProjectReference = result.Content, result.Evaluation, result.Reference, result.ProjectReference

	// 记录指标
	_ = metrics.AR.ActorFinishedThroughput.WithTags(&metrics.ActorFinishedTag{
		ActorName: currentStep.ActorName,
		Result:    string(currentStep.Evaluation),
	}).Add(1)
	_ = metrics.AR.ActorFinishedCost.WithTags(&metrics.ActorFinishedTag{
		ActorName: currentStep.ActorName,
		Result:    string(currentStep.Evaluation),
	}).Observe(float64(execTime.Milliseconds()))

	logger.Infof("actor %s run finished, evaluation=%s, cost=%dms", currentStep.ActorName, currentStep.Evaluation, execTime.Milliseconds())

	// 更新步骤输出和状态
	planStepInstance.actorStep.Outputs["actor_evaluation"] = currentStep.Evaluation
	planStepInstance.actorStep.Outputs["actor_output"] = currentStep.Output
	planStepInstance.actorStep.Status = lo.Ternary(
		currentStep.Evaluation == controltool.ConclusionEvaluationSuccess,
		iris.AgentRunStepStatusSuccess,
		iris.AgentRunStepStatusFailed,
	)

	// 特殊处理网络搜索结果
	if (currentStep.ActorName == websearch.Identifier || currentStep.ActorName == browseract.Identifier) &&
		(result.Evaluation == controltool.ConclusionEvaluationSuccess ||
			result.Evaluation == controltool.ConclusionEvaluationPartialCompleted) {
		a.reportWebsearchResult(run, planStepInstance.actorStep, result.Content, iris.ReferenceSimpleString(result.Reference))
	}

	// 上报结束状态，但是注意 actor 内部（比如 react agent）可能会重复上报结束状态
	publisher.ReportStep(planStepInstance.actorStep)
}

// handleEvaluationResult handles different evaluation results
func (a *PlanActAgent) handleEvaluationResult(currentStep entity.PlanStep) error {
	if currentStep.Evaluation != controltool.ConclusionEvaluationSuccess {
		var err error
		switch currentStep.Evaluation {
		case controltool.ConclusionEvaluationFailed:
			err = fmt.Errorf("actor %s failed to execute:\n", currentStep.ActorName)
		case controltool.ConclusionEvaluationPartialCompleted:
			if currentStep.ActorName != reporteractor.IdentifierV2 {
				err = fmt.Errorf("actor %s reported interim progress, please review the result and continue:\n", currentStep.ActorName)
			}
		default:
			// Actor forgot to report evaluation result, maybe success or failed
			err = fmt.Errorf("actor %s finished, please review the result:\n", currentStep.ActorName)
		}
		if err != nil {
			err = iris.NewRecoverable(err)
		}
		return err
	}
	return nil
}

// handleReporterResult handles the special case for the reporter actor
func (a *PlanActAgent) handleReporterResult(run *iris.AgentRunContext, currentStep entity.PlanStep) error {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	conclusion := ""
	if currentStep.ActorName == reporteractor.Identifier {
		conclusion = responseactor.Conclude(run, lo.Ternary(len(currentStep.Output) == 0, currentStep.Input, currentStep.Output))
	} else {
		conclusion = currentStep.Output
	}
	attachments := a.uploadAttachments(run, iris.ReferenceString(currentStep.Reference), currentStep.ProjectReference)
	if len(attachments) == 0 && len(currentStep.Output) > 0 {
		attachments = a.uploadFinalResult(run, currentStep.Output)
	}
	logger.Debugf("send message [brief result to user]: %s", conclusion)
	currentStep.Output = conclusion

	if run.Err() != nil {
		return iris.NewCompleted(nil)
	}

	publisher.ReportMessage(iris.EventAgentMessage{
		ID:          uuid.New().String(),
		Content:     conclusion,
		Attachments: attachments,
	})
	run.State.Conversation.AddAgentMessage(&iris.Message{
		ID:      uuid.New().String(),
		From:    iris.MessageResult,
		Content: conclusion,
	})
	run.GetTelemetryTracer().StartSpan(run, "agent_message", map[string]interface{}{
		"type":        tracing.SpanEventResponse,
		"message":     conclusion,
		"attachments": attachments,
	}, tracing.SpanTypeEvent, nil)

	return iris.NewCompleted(nil)
}

func (a *PlanActAgent) execute(run *iris.AgentRunContext, step *iris.AgentRunStep, planVersion int, originPlan entity.Plan, trace []entity.PlanStep) (entity.Plan, error) {
	logger, publisher := run.GetLogger(), run.GetPublisher()
	executedPlan := entity.Plan{}

	var previousActor actors.WrappedActor

	withTemplateExpSOP := iris.RetrieveStoreByKey[genexp.TemplateExpSOPStore](run, genexp.TemplateExpSOPStoreKey).Enabled

	for idx := 0; idx < len(originPlan.Steps); idx++ {
		currentStep := originPlan.Steps[idx]
		execErr := func() (err error) {
			span, ctx := run.GetTracer(run).
				StartCustomSpan(run, agentrace.SpanTypeStep, "execute")
			subRun := run.WithContext(ctx)
			defer func() {
				if err != nil {
					logger.Warnf("execute step failed(with template exp sop: %v): %v", withTemplateExpSOP, err)
				}
				agentrace.AddErrorTag(span, err)
				span.Finish()
			}()

			span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
				"plan":  originPlan,
				"actor": currentStep.ActorName,
			}))

			// 识别并写入经验注入的脚本文件
			if len(currentStep.Reference) > 0 {
				for _, ref := range currentStep.Reference {
					os.MkdirAll(filepath.Dir(ref.URI), 0755)
					os.WriteFile(filepath.Join(ref.URI), []byte(conv.DefaultAny[string](ref.MetaData["content"])), 0644)
				}
				currentStep.Reference = iris.Reference{}
				currentStep.ProjectReference = iris.ProjectReference{}
			}

			// 创建actor并准备输入
			actorInput := a.prepareActorInput(subRun, currentStep, trace)

			actor, planStepInstance, err := a.createActor(subRun, CreateActorOption{
				actorInput:    actorInput,
				currentStep:   currentStep,
				trace:         trace,
				previousActor: previousActor,
				planVersion:   planVersion,
				idx:           idx,
				step:          step,
			})
			previousActor = actor
			if err != nil {
				return iris.NewRecoverable(err)
			}

			parsedActorInput, err := prompt.ParseTopTagsV2(actorInput)
			taskContent := currentStep.Input
			if err == nil {
				task, _ := lo.Find(parsedActorInput, func(tag prompt.Tag) bool {
					return tag.XMLName.Local == "task"
				})
				taskContent = task.Content
			}

			if err == nil {
				span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
					"inputs": lo.SliceToMap(parsedActorInput, func(item prompt.Tag) (string, any) {
						return item.XMLName.Local, item.Content
					}),
				}))
			} else {
				span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
					"inputs": map[string]any{
						"raw": actorInput,
					},
				}))
			}

			subRun.State.CurrentStep = nil
			a.reportPlanStepUpdate(publisher, planStepInstance, "running")

			if currentStep.ActorName != ReplanActorName {
				tSpan, _ := run.GetTelemetryTracer().StartSpan(run, actor.Name(), map[string]interface{}{
					"type":   tracing.SpanEventExecute,
					"inputs": currentStep.Input,
				}, tracing.SpanTypeSpan, nil)
				defer func() {
					if err != nil {
						tSpan.SetError(err)
					}
					tSpan.SetTag("outputs", currentStep.Output)
					tSpan.End()
				}()
				// 执行actor
				result, execTime, err := a.executeActor(subRun, actor, planStepInstance, actorInput, currentStep.ReuseTrace)
				if err != nil {
					logger.Warnf("actor %s execution error: %v", currentStep.ActorName, err)
					// 记录错误但继续执行
				}
				if result.Evaluation == controltool.ConclusionEvaluationFatal {
					logger.Errorf("actor %s execution fatal, terminate the task: %v", currentStep.ActorName, err)
					publisher.ReportMessage(iris.EventAgentMessage{
						ID:      uuid.New().String(),
						Content: err.Error(),
					})
					run.GetTelemetryTracer().StartSpan(run, "agent_message_error", map[string]interface{}{
						"type":    tracing.SpanEventResponse,
						"message": err.Error(),
					}, tracing.SpanTypeEvent, nil)
					return err
				}

				// 处理actor结果
				// 创建一个临时变量来存储currentStep
				tempStep := currentStep
				a.handleActorResult(subRun, &tempStep, planStepInstance, result, execTime)
				// 将修改后的值复制回currentStep
				currentStep = tempStep
			} else {
				currentStep.Output, currentStep.Evaluation = "Please remake the plan according to current situation.", controltool.ConclusionEvaluationPartialCompleted
			}
			a.reportPlanStepUpdate(publisher, planStepInstance, "completed")
			span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
				"outputs": map[string]any{
					"output":           currentStep.Output,
					"evaluation":       currentStep.Evaluation,
					"reference":        currentStep.Reference,
					"projectReference": currentStep.ProjectReference,
				},
			}))
			if currentStep.ActorName == dynamicactor.Identifier {
				currentStep.Input = taskContent
			}
			executedPlan.Steps = append(executedPlan.Steps, currentStep)
			trace = append(trace, currentStep)

			// 处理评估结果
			if err := a.handleEvaluationResult(currentStep); err != nil {
				if subRun.Err() != nil {
					canceledReason := "\n\n The current step is canceled due to the user's interruption. Please re-generate the plan according to the user's new requirements."
					executedPlan.Steps[len(executedPlan.Steps)-1].Output += canceledReason
				}

				// 如果是经验注入的任务，且当前步骤失败，则直接跳到 task concluder
				experience, _ := run.Parameters[agententity.RuntimeParametersRunWithExperience]
				taskConcluderID := len(originPlan.Steps) - 1
				if conv.DefaultAny[string](experience) == ExperienceAlreadyUsed && idx != taskConcluderID {
					idx = max(0, taskConcluderID-1) // index 设成 task concluder 的上一步, for 循环下一步就会走到 task concluder
				} else {
					return err
				}
			}

			// 如果有下一步，优化下一步的输入
			// if idx+1 < len(originPlan.Steps) && originPlan.Steps[idx+1].ActorName != "planner" {
			// 	nextStep := &originPlan.Steps[idx+1]
			// 	refinedInput, err := a.refineNextStepInput(run, trace, originPlan.Steps, idx, nextStep.Input)
			// 	if err != nil {
			// 		logger.Warnf("failed to refine next step input: %v", err)
			// 	} else {
			// 		logger.Debugf("refined next step input: original=%s, refined=%s", nextStep.Input, refinedInput)
			// 		originPlan.Steps[idx+1].Input = refinedInput
			// 	}
			// }
			// 如果这是最后一步且是reporter，处理reporter结果
			if (currentStep.ActorName == reporteractor.IdentifierV2 || currentStep.ActorName == reporteractor.Identifier) && idx == len(originPlan.Steps)-1 {
				err := a.handleReporterResult(subRun, currentStep)
				return err
			}
			return nil
		}()
		if execErr != nil {
			return executedPlan, execErr
		}
	}

	return executedPlan, nil
}

var markdownTitleReg = regexp.MustCompile(`^#(.*)\n`)

func (a *PlanActAgent) reportWebsearchResult(run *iris.AgentRunContext, actorStep *iris.AgentRunStep, result, refs string) {
	if len(result) == 0 {
		return
	}
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: actorStep.ExecutorAgent,
		Parent:        actorStep,
	})
	publisher := run.GetPublisher()
	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)
	step.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: string(workspace.ActionCreateFile)}}
	filePath := "investigation.md"
	titleMatches := markdownTitleReg.FindStringSubmatch(result)
	// 使用标题作为文件名
	if len(titleMatches) > 1 {
		filePath = fmt.Sprintf("%s.md", strings.Trim(titleMatches[1], " #\t\r\n*一、.1"))
	}
	filePath = a.generateFilePath(filePath)
	run.GetLogger().Infof("upload report to file %s", filePath)
	a.websearchReportFileName[filePath] = true
	content := fmt.Sprintf("%s\n\n# Reference\n%s", result, refs)
	step.Inputs = map[string]any{
		"file_path": filePath,
		"content":   content,
	}
	service := run.GetArtifactService()
	artifact, err := service.NewFileArtifact(run, nextentity.FileArtifactTypeMetadata{})
	if err != nil {
		run.GetLogger().Errorf("failed to create artifact: %+v", err)
	} else {
		err = service.UploadFiles(run, artifact, []iris.ArtifactFile{{
			Path:    filePath,
			Content: []byte(content),
		}})
		if err != nil {
			run.GetLogger().Errorf("failed to upload file: %+v", err)
		}
	}
	publisher.ReportToolCall(step, iris.ToolCallStatusCompleted, lo.Ternary(
		agents.GetUserLanguage(run.Parameters) == "Chinese (Simplified)",
		"正在总结调查结果",
		"Summarizing investigation results",
	))
}

func (a *PlanActAgent) generateFilePath(filePath string) string {
	extIndex := strings.LastIndex(filePath, ".")
	if extIndex == -1 {
		return filePath
	}
	baseName := filePath[:extIndex]
	ext := filePath[extIndex:]

	newFilePath := filePath
	for counter := 0; counter < 100; counter++ {
		if _, exists := a.websearchReportFileName[newFilePath]; !exists {
			break
		}
		newFilePath = fmt.Sprintf("%s_%d%s", baseName, counter, ext)
	}
	return newFilePath
}

func (a *PlanActAgent) executePlan(run *iris.AgentRunContext, step *iris.AgentRunStep) (err error) {
	publisher := run.GetPublisher()
	span, ctx := run.GetTracer(run).StartCustomSpan(
		run,
		agentrace.SpanTypeStep,
		"execute_plan",
		agentrace.WithSpanID(step.StepID),
	)
	run = run.WithContext(ctx)
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	var (
		planErr    error
		originPlan entity.Plan
	)
	if _, ok := step.Conclusion["plan"]; !ok {
		planErr = fmt.Errorf("plan is not provided")
	} else {
		originPlan = a.getPlan(step)
		if len(originPlan.Steps) == 0 {
			planErr = fmt.Errorf("plan steps are empty")
		}
	}
	if planErr != nil {
		step.Error = planErr
		step.Status = iris.AgentRunStepStatusFailed
		publisher.ReportStep(step)
		return planErr
	}

	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"plan": originPlan,
	}))

	planVersion := a.planVersion(run)
	a.reportPlanUpdate(run, publisher, originPlan.Steps, planVersion, "running")
	defer func() {
		// TODO(zsc): report canceled status
		a.reportPlanUpdate(run, publisher, originPlan.Steps, planVersion, "completed")
	}()

	trace := a.loadPreviousExecution(run)
	outputs, err := a.execute(run, step, planVersion, originPlan, trace)
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"outputs": outputs,
	}))

	reportErr := err
	if iris.IsCompleted(err) {
		reportErr = errors.Unwrap(err)
	}
	step.Finish(util.ValueToMap(outputs), reportErr)
	publisher.ReportStep(step)
	return err
}

// refineNextStepInput 使用LLM根据执行轨迹优化下一步的输入
func (a *PlanActAgent) refineNextStepInput(run *iris.AgentRunContext, executedPlan []entity.PlanStep, originalPlan []entity.PlanStep, currentStepIdx int, nextStepInput string) (string, error) {
	logger := run.GetLogger()
	logger.Infof("refine next step input with trace: %+v", executedPlan)

	// 检查索引边界
	if currentStepIdx+1 >= len(originalPlan) {
		return nextStepInput, fmt.Errorf("next step index out of range: %d >= %d", currentStepIdx+1, len(originalPlan))
	}

	execution, err := executionPrompt(executedPlan, true, MaxExecutionSteps)
	if err != nil {
		return nextStepInput, err
	}
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)

	var originalPlanStr string
	for i, step := range originalPlan {
		if i <= currentStepIdx {
			// 跳过已经执行的步骤
			continue
		}
		originalPlanStr += fmt.Sprintf("Step %d: Agent %s, Input: %s\n", i+1, step.ActorName, step.Input)
	}

	nextStep := originalPlan[currentStepIdx+1]
	nextAction := fmt.Sprintf("The next step will be executed by %s agent with input: %s", nextStep.ActorName, nextStep.Input)

	templates := prompts.GetRefineTemplate("")
	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		knowledges.WithRichSystemMessage(run, knowledges.RichSystemPromptOption{
			Template: templates.SystemTmpl,
			Variables: map[string]any{
				"Actors": lo.FilterMap(a.actorFactory.creators,
					func(actorDef ActorDef, _ int) (map[string]any, bool) {
						actor, ok := a.actorFactory.Create(run, actorDef.Name, ActorDefOption{})
						if !ok {
							return nil, false
						}
						if actorDef.Name == nextStep.ActorName {
							return map[string]any{
								"Name":        actor.Name(),
								"Description": actor.Description(),
								"Parameters":  actor.GetParametersDescription(run),
							}, true
						}
						return nil, false
					},
				),
			},
		}),
		prompt.WithUserMessage(templates.UserTmpl, map[string]any{
			"ExecutionTrace": execution,
			"OriginalPlan":   originalPlanStr,
			"CurrentTask":    nextAction,
			"Requirements":   store.Requirements,
		}),
	})
	if err != nil {
		return nextStepInput, err
	}

	response, err := agents.Think(run, "refine_task", messages, agents.ThinkOption{
		StreamFilter: streamparser.StreamFilter{
			MaxCheckSize: 10,
			FlushSize:    1,
			AheadSize:    10,
			StartTokens:  []string{},
			EndTokens:    []string{},
			BreakTokens:  []string{},
		},
	})
	if err != nil {
		return nextStepInput, err
	}
	tags, err := prompt.ParseTopTagsV2(response.Content)
	if err != nil {
		return nextStepInput, err
	}
	refined := lo.Reduce(tags, func(agg string, tag prompt.Tag, _ int) string {
		if tag.XMLName.Local == "rationale" || tag.XMLName.Local == "reference" {
			return agg
		}
		return agg + "<" + tag.XMLName.Local + ">" + tag.Content + "</" + tag.XMLName.Local + ">"
	}, "")
	reference, exist := lo.Find(tags, func(tag prompt.Tag) bool {
		return tag.XMLName.Local == "reference"
	})
	if exist {
		editor := workspace.GetEditor(run)
		attachReg := regexp.MustCompile(`\[(.*)\]\((.*)\)`)
		matches := attachReg.FindAllStringSubmatch(reference.Content, -1)
		relatedContent := ""
		for _, match := range matches {
			if len(match) < 3 {
				continue
			}
			path := match[2]
			if strings.HasSuffix(path, ".png") || strings.HasSuffix(path, ".jpg") || strings.HasSuffix(path, ".jpeg") || strings.HasSuffix(path, ".pdf") || strings.HasSuffix(path, ".svg") {
				continue
			}
			file, err := editor.ReadFile(workspace.ReadFileArgs{
				Path: path,
			})
			if err != nil || bytes.Contains([]byte(file.Content), []byte{0}) {
				continue
			}
			relatedContent += "<file path=" + file.RealPath + ">\n"
			relatedContent += file.Content[:min(3000, len(file.Content))]
			relatedContent += lo.Ternary(len(file.Content) > 3000, "......\n</file>\n", "\n</file>\n")
		}
		if len(relatedContent) > 0 {
			refined += "\n<related_files>\n" + relatedContent + "</related_files>"
		}
	}

	run.GetLogger().Debugf("refined next step input: %s", refined)
	return refined, nil
}

func (a *PlanActAgent) planVersion(run *iris.AgentRunContext) int {
	store := iris.RetrieveStoreByKey[entity.PlanStore](run, entity.PlanStoreKey)
	if store.Plans == nil {
		return 0
	}
	return len(store.Plans)
}

func (a *PlanActAgent) reportPlanUpdate(run *iris.AgentRunContext, publisher *iris.AgentEventPublisher, originPlan []entity.PlanStep, planVersion int, status string) {
	store := iris.RetrieveStoreByKey[entity.PlanStore](run, entity.PlanStoreKey)
	if store.Plans == nil {
		store.Plans = make(map[int]iris.EventPlanUpdated)
	}
	var planUpdated iris.EventPlanUpdated
	if existingPlan, ok := store.Plans[planVersion]; !ok {
		planUpdated = iris.EventPlanUpdated{
			ID:      uuid.New().String(),
			Status:  status,
			Content: "", // content is not used for now
			PlanSteps: lo.Map(originPlan, func(step entity.PlanStep, _ int) iris.PlanStep {
				return iris.PlanStep{
					ID:          uuid.New().String(),
					Actor:       step.ActorName,
					Input:       step.Input,
					Status:      "created",
					Description: step.Description,
				}
			}),
		}
	} else {
		planUpdated = existingPlan
		planUpdated.Status = status
	}

	store.Plans[planVersion] = planUpdated
	iris.UpdateStoreByKey(run, entity.PlanStoreKey, store)

	// filter out the replan step
	publisher.ReportPlanUpdateData(iris.EventPlanUpdated{
		ID:      planUpdated.ID,
		Status:  planUpdated.Status,
		Content: planUpdated.Content,
		PlanSteps: lo.FilterMap(planUpdated.PlanSteps, func(step iris.PlanStep, _ int) (iris.PlanStep, bool) {
			if step.Actor == ReplanActorName {
				return iris.PlanStep{}, false
			}
			return step, true
		}),
	})
}

type PlanStep struct {
	planID    string
	planStep  iris.PlanStep
	actorStep *iris.AgentRunStep
}

func (a *PlanActAgent) createPlanStep(run *iris.AgentRunContext, planVersion int, stepIdx int, parentStep *iris.AgentRunStep, actorName string, actorInput string) (*PlanStep, error) {
	store := iris.RetrieveStoreByKey[entity.PlanStore](run, entity.PlanStoreKey)
	if store.Plans == nil {
		return nil, fmt.Errorf("plan store is nil")
	}
	plan, ok := store.Plans[planVersion]
	if !ok {
		return nil, fmt.Errorf("plan %d is not found", planVersion)
	}
	if stepIdx >= len(plan.PlanSteps) {
		return nil, fmt.Errorf("step %d is not found", stepIdx)
	}
	planStep := plan.PlanSteps[stepIdx]

	// create step will report the step created event
	actorStep := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: actorName,
		Parent:        parentStep,
	})
	paramTags, err := prompt.ParseTopTagsV2(actorInput)
	if err != nil {
		actorStep.Inputs = map[string]any{
			"input": actorInput,
		}
		return &PlanStep{
			planID:    plan.ID,
			planStep:  planStep,
			actorStep: actorStep,
		}, nil
	}
	for _, paramTag := range paramTags {
		paramName := paramTag.XMLName.Local
		paramValue := strings.Trim(paramTag.Content, "\n")
		actorStep.Inputs[paramName] = paramValue
	}

	return &PlanStep{
		planID:    plan.ID,
		planStep:  planStep,
		actorStep: actorStep,
	}, nil
}

func (a *PlanActAgent) reportPlanStepUpdate(publisher *iris.AgentEventPublisher, planStep *PlanStep, status string) {
	planStepUpdate := iris.EventPlanStepUpdated{
		ID:          planStep.planStep.ID,
		PlanID:      planStep.planID,
		Status:      status,
		Description: planStep.planStep.Description,
		StepID:      planStep.actorStep.StepID,
	}

	publisher.ReportPlanStepUpdate(planStepUpdate)
}

func executionPrompt(planSteps []entity.PlanStep, showInput bool, maxSteps int) (string, error) {
	if len(planSteps) == 0 {
		return "", nil // 如果没有步骤，返回空字符串
	}

	// 限制 planSteps 数量，从后往前保留最新的 maxSteps 个
	if maxSteps > 0 && len(planSteps) > maxSteps {
		planSteps = planSteps[len(planSteps)-maxSteps:]
	}

	return prompts.Set.ExecutePrompt(prompts.ExecutionPrompt, "", map[string]any{
		"PlanSteps": planSteps,
		"ShowInput": showInput,
	})
}

func (a *PlanActAgent) loadPreviousExecution(run *iris.AgentRunContext) []entity.PlanStep {
	mem := memory.GetAgentMemory(run)
	plannerSteps := lo.Filter(mem.ActionMemory, func(step *memory.ActionMemoryItem, _ int) bool {
		return step.ExecutorAgent == a.planner.Name()
	})
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	previousSteps := lo.Slice(plannerSteps, len(plannerSteps)-store.CurRound, len(plannerSteps))
	execution := []entity.PlanStep{}
	for _, step := range previousSteps {
		var plan entity.Plan
		_ = mapstructure.Decode(step.Outputs, &plan)
		execution = append(execution, plan.Steps...)
	}
	run.GetLogger().Infof("loading %d previous executions with id: %s", len(execution), a.planner.Name())
	return execution
}

func (a *PlanActAgent) UpdateReference(run *iris.AgentRunContext, newResult *controltool.ConclusionOutput, refType entity.ReferenceType) {
	answer, citations := websearchtool.UpdateReference(run, newResult.Content, newResult.Reference, refType)
	newResult.Content = answer
	newResult.Reference = citations
}
