package planact_agent

import (
	"errors"
	"fmt"
	"reflect"

	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"gopkg.in/yaml.v3"
)

func (a *PlanActAgent) executeSingleStep(run *iris.AgentRunContext, step *iris.AgentRunStep) (err error) {
	publisher := run.GetPublisher()

	planVersion := a.planVersion(run)

	var originPlan entity.Plan
	if planValue, ok := step.Conclusion["plan"]; ok {
		mapstructure.Decode(planValue, &originPlan)
	} else {
		if step.Thought == nil {
			return iris.NewRecoverable(fmt.Errorf("step thought is nil"))
		}
		input := ""
		for key, value := range step.Thought.Parameters {
			strValue := conv.DefaultAny[string](value)
			if reflect.TypeOf(value).Kind() != reflect.String {
				bytes, _ := yaml.Marshal(value)
				strValue = string(bytes)
			}
			input += fmt.Sprintf("<%s> %s </%s>\n", key, strValue, key)
		}
		originPlan = entity.Plan{
			Steps: []entity.PlanStep{
				{
					ActorName:   step.Thought.Tool,
					Description: genTaskBrief(run, step.Thought.Content),
					Input:       input,
				},
			},
		}
	}

	a.reportPlanUpdate(run, publisher, originPlan.Steps, planVersion, "running")
	defer func() {
		// TODO(zsc): report canceled status
		a.reportPlanUpdate(run, publisher, originPlan.Steps, planVersion, "completed")
	}()

	trace := a.loadPreviousExecution(run)
	outputs, err := a.execute(run, step, planVersion, originPlan, trace)

	reportErr := err
	if iris.IsCompleted(err) {
		reportErr = errors.Unwrap(err)
	}

	step.Finish(util.ValueToMap(outputs), reportErr)
	publisher.ReportStep(step)
	return err
}

func genTaskBrief(run *iris.AgentRunContext, task string) string {

	messages := []*framework.ChatMessage{
		{
			Role: "system",
			Content: fmt.Sprintf(`用简洁的短语（不超过20个字）总结当前任务。
重点关注任务的最终目的或主要动作。尤其关注其中的 agent name。
- 如果 agent name 是 task concluder 相关的，就表示这是在总结任务的执行结果，你的总结也应该是类似'报告xxx任务执行结果'的一段话。
- 如果 agent name 是 ask user 相关的，就表示这是在请求用户澄清，你的总结也应该是类似'向用户询问xxx'的一段话。

中文示例:
- 调研xxx场景并生成飞书文档
- 报告xxx任务的执行结果
- 克隆仓库 example/repo 并分析关键代码

英文示例:
- research xxx scene and generate a feishu document
- report the execution result of xxx task
- Clone repository example/repo and analyze the key code files

注意:
- 你必须使用回应语言为：%s
- 语言是 en 参考英文示例，zh 参考中文示例
- 只需要使用一种语言回复
`, agents.GetUserLanguage(run.Parameters)),
		},
		{
			Role:    "user",
			Content: task,
		},
	}
	content, err := agents.Think(run, "summarize", messages, agents.ThinkOption{
		StreamFilter: streamparser.StreamFilter{
			MaxCheckSize: 10,
			FlushSize:    1,
			AheadSize:    10,
			StartTokens:  []string{},
			EndTokens:    []string{},
			BreakTokens:  []string{},
		},
	})
	if err != nil {
		run.GetLogger().Warn("failed to generate tool call summary: %v", err)
		return ""
	}
	return content.Content
}
