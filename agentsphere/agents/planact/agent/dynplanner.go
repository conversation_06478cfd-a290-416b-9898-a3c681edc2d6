package planact_agent

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	action_prompts "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	reporteractor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/reporter"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/prompts"
	queryrecognizer "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/query_preprocessor"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	agentsphereentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	condenser "code.byted.org/devgpt/kiwis/agentsphere/memory/condenser/impl"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/search/dal/llm"
)

type DynamicPlanner struct {
	iris.AgentInfo
	ProgreAct *agents.ProgreActAgent
	Variant   string
}

var _ GeneralPlanner = &DynamicPlanner{}

func (a *DynamicPlanner) NextStep(ctx context.Context, run *iris.AgentRunContext) (*iris.AgentRunStep, error) {
	defer func() {
		store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
		if run.Err() == nil {
			store.CurRound++
		}
		iris.UpdateStoreByKey(run, entity.PlannerStoreKey, store)
	}()
	step, err := a.ProgreAct.NextStep(ctx, run)
	// ProgreAct 正常生成 plan 也会返回报错 tool `xxx` not found，此时需要忽略掉
	if iris.IsRecoverable(err) && step != nil {
		return step, nil
	}
	return step, err
}

type DynamicPlannerCreateOption struct {
	MaxSteps                              int
	Variant                               string
	LLMHooks                              *prompt.LLMCallHook
	ThinkCallbacks                        []agents.ThinkCallback
	Knowledgebase                         knowledges.Knowledgebase
	KGCache                               *sync.Map
	ExperienceProgressPlan                string // a reference history progress plan that can be reused to produce similar output
	SerialIdentifier, ProgreActIdentifier string
}

func (a *DynamicPlanner) ResetRound(run *iris.AgentRunContext, option ResetRoundOption) {
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	a.ProgreAct.ExpandMaxStep(store.CurRound + option.ExpandMaxSteps)
	a.ProgreAct.ResetFailures(run)
}

func (a *DynamicPlanner) IncreaseCurrentRound(run *iris.AgentRunContext) {
	store := iris.RetrieveStoreByKey[agents.ProgreActState](run, a.ProgreAct.StoreKey())
	store.CurrentRound++
	iris.UpdateStoreByKey(run, a.ProgreAct.StoreKey(), store)
}

func NewDynamicPlanner(opt DynamicPlannerCreateOption) *DynamicPlanner {
	tools := []iris.Action{}

	info := iris.AgentInfo{
		Identifier: "dynamic_planner",
		Desc:       "dynamic planner",
	}

	progreactor := agents.NewProgreActAgent(agents.ProgreActAgentConfig{
		Info:        info,
		Tools:       tools,
		MaxSteps:    opt.MaxSteps,
		MaxFailures: 3,
		ValidateThought: func(thought *iris.Thought) error {
			// sometimes LLM write rationale without `## Rationale` and we consider it as a valid thought
			// only check if agent is presented
			if len(thought.Tool) == 0 {
				return iris.ThoughtParseError("no `## Action` found in the thought, specify the agent you want to use and provide parameters.")
			}
			return nil
		},
		LLMHook:             opt.LLMHooks,
		ThinkCallbacks:      opt.ThinkCallbacks,
		SerialIdentifier:    opt.SerialIdentifier,
		ProgreActIdentifier: opt.ProgreActIdentifier,
	})
	info.Identifier = progreactor.Agent.Name()

	agent := &DynamicPlanner{
		AgentInfo: info,
		Variant:   opt.Variant,
	}

	// cachePoints := map[int]bool{}

	composer := func(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *agents.ProgreActState) []*framework.ChatMessage {
		store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
		promptset := prompts.GetDynamicPlannerTemplate(agent.Variant)
		preprocessedResults := lo.Map([]queryrecognizer.MentionPreprocessor{
			&queryrecognizer.CodebaseMentionPreprocessor{},
			&queryrecognizer.LarkDocMentionPreprocessor{},
			&queryrecognizer.AttachmentMentionPreprocessor{},
		}, func(preprocessor queryrecognizer.MentionPreprocessor, _ int) queryrecognizer.PreprocessedItem {
			return preprocessor.GetPreprocessedItems(run)
		})

		// Extract MentionItems for existing logic
		preprocessedItems := lo.FlatMap(preprocessedResults, func(item queryrecognizer.PreprocessedItem, _ int) []queryrecognizer.PreprocessedMentionItem {
			return lo.Map(item.MentionItems, func(mentionItem *queryrecognizer.PreprocessedMentionItem, _ int) queryrecognizer.PreprocessedMentionItem {
				return *mentionItem
			})
		})

		condenser := condenser.NewAutoSummarizer(&condenser.NewAutoSummarizerConfig{
			Name:    "dynamic_planner",
			LLM:     run.GetLLM(),
			Config:  run.Config,
			Variant: opt.Variant,
		})

		run.GetLogger().Infof("preprocessed items: %+v", preprocessedItems)

		scenario := iris.RetrieveStoreByKey[knowledges.Scenario](run, knowledges.ScenarioStoreKey)

		run.GetLogger().Infof("scenario: %s", scenario)

		composeOptions := []prompt.ComposeVaryMessageOption{
			prompt.WithSystemMessage(promptset.SystemTmpl,
				map[string]any{
					"Actors": store.ActorsDesc,
					"Repos": lo.Filter(lo.MapToSlice(workspace.GetWorkspace(run).Repositories,
						func(k string, v *workspace.Repository) map[string]string {
							return map[string]string{
								"Name":      k,
								"Directory": v.Directory,
							}
						}), func(item map[string]string, index int) bool {
						return item["Name"] != "." || item["Directory"] != "."
					}),
					"User":    run.User,
					"ExpTips": strings.TrimSpace(lo.Ternary(store.ExperiencesHistory != "", "- In particular, if you have referred to any [Historical Experiences], you need to specifically indicate what experience you have referred to.", "")),
				}),
		}
		expetedArtifacts := iris.RetrieveStoreByKey[queryrecognizer.ArtifactsAnnotationStore](run, queryrecognizer.ArtifactsAnnotationStoreKey).ExpectedArtifacts

		group := errgroup.Group{}
		mu := sync.Mutex{}
		knowledgeItems := make([]knowledges.KnowledgeItem, 0)

		agent := "dynamic_planner"
		variant := run.GetConfig().GetVariantByScene("planner")
		group.Go(func() error {
			sysKnowledgeItems, _ := knowledges.RetrieveKnowledgesWithCache(run, knowledges.WithKnowledgeRetrieveMessageOption{
				Query:     store.Requirements + expetedArtifacts,
				Knowledge: opt.Knowledgebase,
				Strategy:  knowledges.KgRetrieveStrategyLLM,
				Category:  knowledges.KgRetrieveCategorySystem,
				Param: knowledges.RetrieveParam{
					Agent:   agent,
					Variant: variant,
				},
				Cache: opt.KGCache,
			})
			mu.Lock()
			knowledgeItems = append(knowledgeItems, sysKnowledgeItems...)
			mu.Unlock()
			return nil
		})
		group.Go(func() error {
			toolKnowledge, _ := knowledges.RetrieveKnowledgesWithCache(run, knowledges.WithKnowledgeRetrieveMessageOption{
				Query:     store.Requirements + expetedArtifacts,
				Knowledge: opt.Knowledgebase,
				Strategy:  knowledges.KgRetrieveStrategyLLM,
				Category:  knowledges.KgRetrieveCategoryTool,
				Param: knowledges.RetrieveParam{
					Agent:   agent,
					Variant: variant,
				},
				Cache: opt.KGCache,
			})
			mu.Lock()
			knowledgeItems = append(knowledgeItems, toolKnowledge...)
			mu.Unlock()
			return nil
		})
		group.Go(func() error {
			scenarioKnowledge, _ := knowledges.RetrieveKnowledgesWithCache(run, knowledges.WithKnowledgeRetrieveMessageOption{
				Query:     store.Requirements + expetedArtifacts,
				Knowledge: opt.Knowledgebase,
				Strategy:  knowledges.KgRetrieveStrategyLLM,
				Category:  knowledges.KgRetrieveCategoryScenario,
				Param: knowledges.RetrieveParam{
					Agent:    agent,
					Variant:  variant,
					Scenario: scenario,
				},
				Cache: opt.KGCache,
			})
			mu.Lock()
			knowledgeItems = append(knowledgeItems, scenarioKnowledge...)
			mu.Unlock()
			return nil
		})
		err := group.Wait()
		if err != nil {
			run.GetLogger().Errorf("failed to retrieve knowledges: %v", err)
		}
		// 项目空间场景，召回项目空间知识输入用户提示词中
		if agentsphereentity.CheckParamIsProjectSpace(run.Parameters[agentsphereentity.RuntimeParametersSpaceInfomation]) {
			projectSpaceKnowledges := knowledges.FilterKnowledgeItems(knowledges.LoadKnowledge(), []string{"system.projectspace_default_prompt"})
			run.GetLogger().Infof("[NewDynamicPlanner]:[project_space_logic]: project_space_scenario retrieve project_space_knowledges:%+v", projectSpaceKnowledges)
			knowledgeItems = append(knowledgeItems, projectSpaceKnowledges...)
		}

		composeOptions = append(composeOptions, knowledges.WithKnowledgeMessage(run, knowledgeItems))

		composeOptions = append(composeOptions, prompt.WithUserMessage(promptset.UserTmpl, map[string]any{
			"Requirements":               lo.Ternary(store.EnhancedRequirements != "", store.EnhancedRequirements, store.InitialRequirements) + expetedArtifacts,
			"User":                       run.User,
			"RelatedKnowledge":           store.RelatedKnowledge,
			"ExperienceProgressPlan":     lo.Ternary(store.ExperienceProgressPlan != "", store.ExperienceProgressPlan, opt.ExperienceProgressPlan),
			"PreprocessedItems":          preprocessedItems,
			"RelatedWorkspaceKnowledges": store.RelatedWorkspaceKnowledges,
			"Experience":                 store.ExperiencesHistory,
		}))
		composeOptions = append(composeOptions, action_prompts.WithReActToolHistory(run, previousSteps, promptset.ThoughtTmpl, nil, func(run *iris.AgentRunContext, step *iris.AgentRunStep) framework.ChatMessage {
			var executedPlan entity.Plan
			mapstructure.Decode(step.Outputs, &executedPlan)
			execution, _ := executionPrompt(executedPlan.Steps, false, MaxExecutionSteps)
			if step.Error != nil {
				execution = step.Error.Error() + execution
			}
			return framework.ChatMessage{
				Role:    llm.RoleUser,
				Content: execution,
			}
		}, condenser))

		messages, err := prompt.ComposeVaryMessages(composeOptions)
		if err != nil {
			run.GetLogger().Errorf("failed to compose messages: %v", err)
			return nil
		}
		// TODO(cyx): 因为目前 cache ttl 只有 5min，单个 actor 执行很容易超过，效果不大，暂时不加
		// cacheIdx := len(messages) - 1
		// // messages[cacheIdx].CacheControl = "ephemeral"
		// // FIXME(cyx): 目前 claude cache 标记必须放到 content parts 里面，已要求 gpt 平台支持。
		// messages[cacheIdx].ContentParts = append(messages[cacheIdx].ContentParts, &framework.LLMChatMessageContentPart{
		// 	Text:         lo.ToPtr(messages[cacheIdx].Content),
		// 	CacheControl: "ephemeral",
		// })
		// messages[cacheIdx].Content = ""
		return messages
	}

	progreactor.ForceConcludeResult = &iris.Thought{
		Tool: reporteractor.Identifier,
		Content: fmt.Sprintf(`
## Rationale

Due to the limited context, I should conclude the task and report the current result.

## Action (%s)


%s
param="task_brief"
报告任务完成情况
%s
%s
param="task"
Please provide a detailed report on the completed progress and any outstanding tasks.
%s
		`, reporteractor.Identifier, "```", "```", "```", "```"),
		Parameters: map[string]any{
			"task_brief": "报告任务完成情况",
			"task":       "Please provide a detailed report on the completed progress and any outstanding tasks.",
		},
	}
	progreactor.Composer = composer
	agent.ProgreAct = progreactor

	return agent
}
