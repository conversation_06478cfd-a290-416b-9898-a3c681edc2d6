package planact_agent

import (
	"github.com/samber/lo"
	"github.com/spf13/cast"

	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/askuser"
	browseractor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/browseract"
	codeact "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/codeact"
	codebaseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/codebase"
	coderactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/coder"
	dynamicactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/dynamic"
	mcpcalleractor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/mcpcaller"
	reporteractor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/reporter"
	websearchactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
)

type ActorDef struct {
	Name   string
	Create func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor
}

type ActorDefOption struct {
	ActorStep  *iris.AgentRunStep
	ActorInput string
}

type ActorFactory struct {
	creators []ActorDef
}

func isEnabledBytedTools(run *iris.AgentRunContext) bool {
	// Enabled by default.
	enabled := true
	return enabled
}

// getMCPToolNames 获取可用的 MCP 工具名称列表
func getMCPToolNames(run *iris.AgentRunContext) []string {
	// 从 ProviderRegistry 动态获取已注册的 MCP 工具名称
	mcpTools := []string{}

	// 获取当前注册的 MCP providers
	for _, provider := range mcptool.ProviderRegistry.Providers {
		if provider != nil {
			// 根据是否启用字节跳动内部工具来过滤
			if provider.Byted && !isEnabledBytedTools(run) {
				continue
			}
			mcpTools = append(mcpTools, provider.Name)
		}
	}

	return mcpTools
}

func getLLMHooks(variant string, enableBytedBool bool) *prompt.LLMCallHook {
	// url validation hook
	reqURLHook, resURLHook := prompt.URLValidationHook()
	hook := &prompt.LLMCallHook{
		RequestHooks: []prompt.LLMCallRequestHook{
			reqURLHook,
		},
		ResultHooks: []prompt.LLMCallResultHook{
			resURLHook,
		},
	}
	if variant != agententity.VariantExpert {
		return hook
	}

	// cyx: disabled temporarily.
	// safe filter hook
	// reqSafeHook, resSafeHook := prompt.SimpleFilterPromptMessagesHook(prompt.SimpleFilterOption{
	// 	ReplaceKeywords: [][2]string{
	// 		{"bytedance", "company_name"},
	// 		{"BYTEDANCE", "COMPANY_NAME"},
	// 		{"byted", "company_name"},
	// 		{"BYTED", "COMPANY_NAME"},
	// 		{"字节跳动", "company_name"},
	// 	},
	// })
	// hook.RequestHooks = append(hook.RequestHooks, reqSafeHook)
	// hook.ResultHooks = append(hook.ResultHooks, resSafeHook)

	return hook
}

func NewActorFactory(enableDynamicActor bool) *ActorFactory {
	factory := &ActorFactory{
		creators: []ActorDef{},
	}

	if !enableDynamicActor {
		factory.Register(ReplanActorName, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			return &actors.BaseActor{
				AgentInfo: iris.AgentInfo{
					Identifier: ReplanActorName,
					Desc:       "Remake the plan according to the new situation.",
				},
				Parameters: []actors.ParameterDescription{
					{
						Name:        "task",
						Description: "Briefly describe the task to complete",
					},
				},
			}
		})
		factory.Register(coderactor.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			variant := run.GetConfig().GetVariantByScene(coderactor.Identifier)
			return coderactor.New(coderactor.CreateOption{
				MaxSteps:         50,
				Variant:          variant,
				LLMHooks:         getLLMHooks(variant, isEnabledBytedTools(run)),
				Knowledgebase:    knowledges.CreateKnowledgebase(run),
				EnableBytedRepos: isEnabledBytedTools(run),
				ActorStep:        opt.ActorStep,
			})
		})
		factory.Register(codeact.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			variant := run.GetConfig().GetVariantByScene(codeact.Identifier)
			return codeact.New(codeact.CreateOption{
				MaxSteps:      50,
				Variant:       variant,
				LLMHooks:      getLLMHooks(variant, isEnabledBytedTools(run)),
				Knowledgebase: knowledges.CreateKnowledgebase(run),
				ActorStep:     opt.ActorStep,
			})
		})
		factory.Register(codebaseactor.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			variant := run.GetConfig().GetVariantByScene(codebaseactor.Identifier)
			return codebaseactor.New(codebaseactor.CreateOption{
				MaxSteps:         20,
				Variant:          variant,
				EnableBytedRepos: isEnabledBytedTools(run),
				LLMHooks:         getLLMHooks(variant, isEnabledBytedTools(run)),
				ActorStep:        opt.ActorStep,
			})
		})
		factory.Register(browseractor.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			return browseractor.New(browseractor.CreateOption{
				MaxSteps:      100, // it may browse many pages or do very complex operations
				MaxFailures:   4,
				Variant:       run.GetConfig().GetVariantByScene(browseractor.Identifier),
				Knowledgebase: knowledges.CreateKnowledgebase(run),
				ActorStep:     opt.ActorStep,
			})
		})
		factory.Register(mcpcalleractor.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			return mcpcalleractor.New(mcpcalleractor.CreateOption{
				MaxSteps:         50,
				Variant:          run.GetConfig().GetVariantByScene(mcpcalleractor.Identifier),
				EnableBytedTools: isEnabledBytedTools(run),
				ActorStep:        opt.ActorStep,
			})
		})
		factory.Register(websearchactor.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			enableInternalSearch := cast.ToBool(run.Parameters[websearchactor.EnableInternalSearchParameterKey])
			run.GetLogger().Infof("enable_internal_search: %v", enableInternalSearch)
			return websearchactor.New(websearchactor.CreateOption{
				EnableBytedSearch: enableInternalSearch,
				DeepOnly:          enableDynamicActor,
				ActorStep:         opt.ActorStep,
			})
		})
	} else {
		factory.Register(dynamicactor.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
			variant := run.GetConfig().GetVariantByScene(dynamicactor.Identifier)
			return dynamicactor.Create(run, opt.ActorInput, opt.ActorStep, getLLMHooks(variant, isEnabledBytedTools(run)))
		})
	}

	factory.Register(reporteractor.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
		variant := run.GetConfig().GetVariantByScene(reporteractor.Identifier)
		return reporteractor.New(reporteractor.CreateOption{
			MaxSteps:         1,
			Variant:          variant,
			IfDynamicSetting: enableDynamicActor,
			LLMHooks:         getLLMHooks(variant, isEnabledBytedTools(run)),
			// 使用 claude 的话需要开启脱敏
			// LLMHooks: getSafeLLMHook(variant, isEnabledBytedTools(run)),
			ActorStep: opt.ActorStep,
		})
	})
	factory.Register(reporteractor.IdentifierV2, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
		variant := run.GetConfig().GetVariantByScene(reporteractor.IdentifierV2)
		return reporteractor.NewV2(reporteractor.CreateOption{
			MaxSteps:  1,
			Variant:   variant,
			ActorStep: opt.ActorStep,
		})
	})
	factory.Register(askuser.Identifier, func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor {
		return askuser.New(askuser.CreateOption{ActorStep: opt.ActorStep})
	})
	return factory
}

func (f *ActorFactory) Register(name string, creator func(run *iris.AgentRunContext, opt ActorDefOption) actors.WrappedActor) {
	f.creators = append(f.creators, ActorDef{
		Name:   name,
		Create: creator,
	})
}

func (f *ActorFactory) HasActor(run *iris.AgentRunContext, name string) bool {
	_, exists := lo.Find(f.creators, func(creator ActorDef) bool {
		return creator.Name == name
	})
	return exists
}

func (f *ActorFactory) Create(run *iris.AgentRunContext, name string, opt ActorDefOption) (actors.WrappedActor, bool) {
	creator, exists := lo.Find(f.creators, func(creator ActorDef) bool {
		return creator.Name == name
	})
	if !exists {
		return nil, false
	}
	return creator.Create(run, opt), true
}

func (a *PlanActAgent) initActorsDesc(run *iris.AgentRunContext) any {
	store := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	store.ActorsDesc = lo.Filter(lo.Map(a.actorFactory.creators,
		func(creator ActorDef, index int) map[string]any {
			actor, _ := a.actorFactory.Create(run, creator.Name, ActorDefOption{})
			return map[string]any{
				"Name":        actor.Name(),
				"Description": actor.Description(),
				"Parameters":  actor.GetParametersDescription(run),
			}
		},
	), func(item map[string]any, index int) bool {
		return item["Name"] != reporteractor.Identifier
	})
	iris.UpdateStoreByKey(run, entity.PlannerStoreKey, store)
	return store.ActorsDesc
}
