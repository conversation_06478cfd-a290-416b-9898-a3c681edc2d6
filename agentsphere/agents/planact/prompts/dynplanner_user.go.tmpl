[User Information]
Username: {{ .User.Username }}
Current DateTime: {{ date }}
- For any user request involving a relative time frame (e.g., 'today', 'past 24 hours', 'this week'), you MUST calculate the exact time range based on the current date and time.

[Problem Statement]
{{ .Requirements }}

{{ if .PreprocessedItems }}
[Mentions]
Here are some stuffs mentioned by the user, and they are already preprocessed to the workspace. You don't need to download them again.
{{ range .PreprocessedItems }}
[{{ .ID }}]: {{ .Content }}
{{ end -}}
Note: some stuffs might not be automatically recognized and processed, in this case you need to download them manually.
{{ end }}

{{ if .RelatedKnowledge }}
[Related Knowledge]
{{ .RelatedKnowledge }}
{{ end }}

{{ if .Experience -}}
{{ .Experience }}
{{- end }}

{{ if .ExperienceProgressPlan }}
[SOP]
Here is a SOP from previous satisfying tasks, reuse the SOP to complete the task with the same outcome:

{{ .ExperienceProgressPlan }}

# In completing this task, your primary instruction is the [Problem Statement]. The [SOP] is provided as a guideline. If there is any conflict between the [SOP] and the [Problem Statement], you must prioritize and follow the [Problem Statement].
{{ end }}