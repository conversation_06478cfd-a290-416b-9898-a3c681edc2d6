{{ if eq .Actor "mewtwo" -}}
<task>
## Objective:
    {{ indent .Phase.Objective "    " }}

## Actions(You should adapt the actions to the current task):
    {{ indent .Phase.Actions "    " }}

## Deliverable:
{{ range .Phase.Deliverable -}}
    {{ indent . "    "}}
{{ end -}}

{{ if .Phase.References -}}
## Note: 
Here are some helpful examples for you. When you need to create these files, you can first take a look at the corresponding references and modify them to suit your own requirements.

{{ range .PhaseRefs }}
{{ if .Binary -}}
### File Name: {{ .Filepath }}
<This file may be a binary file.>
{{ else }}
### File Path: {{ .Filepath }}
{{ .Content }}
{{- end }}
{{ end }}

{{ end -}}
</task>

<persona>
{{ .Persona }}
</persona>

<tools>
{{- range .Tools -}}
{{.}},
{{- end -}}
</tools>
{{- else -}}
{{ range $key, $value := .Parameters -}}
<{{ $key }}>
{{ $value }}
</{{ $key }}>
{{- end }}
{{- end -}}