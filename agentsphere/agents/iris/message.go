package iris

import (
	"sync"
	"time"

	"github.com/samber/lo"
)

const (
	MessageFromUser = "user"
	MessageResponse = "response_ack"
	MessageResult   = "response_conclude"
)

// Message communication between agents (and user).
// not the entity displayed to the user
type Message struct {
	ID            string         `json:"id" mapstructure:"id"`                         // unique id of the message
	Content       string         `json:"content" mapstructure:"content"`               // content visible to user & other agents
	From          string         `json:"from" mapstructure:"from"`                     // 'user' or <agent_name>
	Mentions      []Mention      `json:"mentions" mapstructure:"-"`                    // mentioned entities
	StructContent map[string]any `json:"struct_content" mapstructure:"struct_content"` // extra data carried by the message, such as rich text of user input or the output of an agent
	CreatedAt     time.Time      `json:"created_at" mapstructure:"created_at"`         // time at which the message is sent
	Attachments   []Attachment   `json:"attachments" mapstructure:"attachments"`       // attachments of the message
}

type Attachment struct {
	ArtifactID string         `json:"artifact_id" mapstructure:"artifact_id"`
	Name       string         `json:"name" mapstructure:"name"` // 产物文件名(目前用于 project_artifact 类型)
	Path       string         `json:"path" mapstructure:"path"` // 产物文件路径/Title/URL（旧逻辑）
	Type       AttachmentType `json:"type" mapstructure:"type"`
	URL        string         `json:"url" mapstructure:"url"`         // 部署链接 / 飞书文档链接
	Version    int            `json:"version" mapstructure:"version"` // for artifact version, default 1, project artifact need show version
}

type AttachmentType string

const (
	AttachmentTypeFile       AttachmentType = "file"
	AttachmentTypeDeployment AttachmentType = "deployment"
	AttachmentTypeLarkDoc    AttachmentType = "lark_doc"
	AttachmentTypeLarkSheet  AttachmentType = "lark_sheet"
	AttachmentTypeURL        AttachmentType = "url"
	AttachmentTypeProject    AttachmentType = "project"
)

var _ lo.Clonable[Message] = Message{}

// Clone returns a shallow copy of the message
func (m Message) Clone() Message {
	return Message{
		Content:       m.Content,
		From:          m.From,
		Mentions:      m.Mentions,
		StructContent: lo.Assign(m.StructContent),
		CreatedAt:     m.CreatedAt,
	}
}

// A 16-bit State to represent the state of a message
// 0-bit: isDeprecated
// 1-bit: isDirty

const (
	DeprecatedMessage MessageState = 1 << 0
	DirtyMessage      MessageState = 1 << 1
)

type MessageState int16

// Conversation shared between multiple agents
type Conversation struct {
	m             sync.RWMutex
	Messages      []*Message     `json:"messages"`
	MessageStates []MessageState `json:"message_states"`

	callbacks []func()
}

func (c *Conversation) isDeprecated(idx int) bool {
	return c.MessageStates[idx]&DeprecatedMessage == DeprecatedMessage
}

func (c *Conversation) isDirty(idx int) bool {
	return c.MessageStates[idx]&DirtyMessage == DirtyMessage
}

func (c *Conversation) RegisterUpdateCallback(f func()) {
	c.m.Lock()
	defer c.m.Unlock()
	c.callbacks = append(c.callbacks, f)
}

// AddUserMessage adds a user message to the conversation
// and interrupts the current agent run by setting the Dirty flag
func (c *Conversation) AddUserMessage(msg *Message) {
	c.m.Lock()
	defer c.m.Unlock()
	c.Messages = append(c.Messages, msg)
	c.MessageStates = append(c.MessageStates, DirtyMessage)
	for _, cb := range c.callbacks {
		cb()
	}
}

// AddAgentMessage adds an agent message to the conversation
func (c *Conversation) AddAgentMessage(msg *Message) {
	c.m.Lock()
	defer c.m.Unlock()
	c.Messages = append(c.Messages, msg)
	c.MessageStates = append(c.MessageStates, 0)
}

func (c *Conversation) GetMessages() []*Message {
	c.m.RLock()
	defer c.m.RUnlock()
	return lo.Filter(c.Messages, func(msg *Message, idx int) bool {
		return !c.isDeprecated(idx)
	})
}

func (c *Conversation) GetDirtyMessage() []*Message {
	c.m.RLock()
	defer c.m.RUnlock()
	return lo.Filter(c.Messages, func(msg *Message, idx int) bool {
		return c.isDirty(idx)
	})
}

func (c *Conversation) CleanDirtyMessage(ids []string) {
	c.m.Lock()
	defer c.m.Unlock()
	for _, id := range ids {
		for idx, msg := range c.Messages {
			if msg.ID == id {
				c.MessageStates[idx] &= ^DirtyMessage
			}
		}
	}
}

func (c *Conversation) MarkDeprecated(id string) {
	c.m.Lock()
	defer c.m.Unlock()
	for idx, msg := range c.Messages {
		if msg.ID == id {
			c.MessageStates[idx] |= DeprecatedMessage
			c.MessageStates[idx] &= ^DirtyMessage
		}
	}
}

// HasNewMessage returns true if the conversation has been modified
// since the last time it was cleared
// agents should check this at each checkpoint
func (c *Conversation) HasNewMessage() bool {
	c.m.RLock()
	defer c.m.RUnlock()
	return lo.Reduce(c.MessageStates, func(agg MessageState, state MessageState, idx int) MessageState {
		return agg | state
	}, 0)&DirtyMessage != 0
}

func NewConversation() *Conversation {
	return &Conversation{
		Messages:      make([]*Message, 0),
		MessageStates: make([]MessageState, 0),
	}
}
