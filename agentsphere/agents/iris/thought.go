package iris

import (
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

type ToolCall struct {
	ToolCallID string     `json:"tool_call_id" mapstructure:"tool_call_id"`
	ToolName   string     `json:"tool_name" mapstructure:"tool_name"`
	Parameters Parameters `json:"parameters" mapstructure:"parameters"`
}

type Thought struct {
	// raw LLM output
	Content          string `json:"content" mapstructure:"content"`
	ReasoningContent string `json:"reasoning_content" mapstructure:"reasoning_content"`

	// tool call
	Rationale    string     `json:"rationale" mapstructure:"rationale"`
	FunctionCall bool       `json:"function_call" mapstructure:"function_call"` // whether native function calling is used
	ToolCalls    []ToolCall `json:"tool_calls" mapstructure:"tool_calls"`       // tool calls produced by LLM

	// Deprecated: use ToolCalls instead
	ToolCallID string `json:"tool_call_id" mapstructure:"tool_call_id"` // function call id corresponding to original tool call produced by LLM
	// Deprecated: use ToolCalls instead
	Tool string `json:"tool" mapstructure:"tool"` // tool name
	// Deprecated: use Tool<PERSON>alls instead
	Parameters Parameters `json:"parameters" mapstructure:"parameters"` // parameters of the tool call

	// extra data parsed from LLM
	Data Parameters `json:"data" mapstructure:"data"`

	// Usually for debugging.
	LLMCall LLMCall `json:"llm_call" mapstructure:"llm_call"`
}

type LLMCall struct {
	ModelName    string                   `json:"model_name" mapstructure:"model_name"`
	Temperature  float64                  `json:"temperature" mapstructure:"temperature"`
	Usage        *framework.TokenUsage    `json:"usage" mapstructure:"usage"`
	Prompt       []*framework.ChatMessage `json:"prompt" mapstructure:"prompt"`
	FinishReason string                   `json:"finish_reason" mapstructure:"finish_reason"`
	TraceID      string                   `json:"trace_id" mapstructure:"trace_id"`
}

func (t *Thought) IsNilOrEmpty() bool {
	return t == nil || (t.Content == "" &&
		t.Rationale == "" &&
		t.Tool == "" &&
		len(t.Parameters) == 0 &&
		len(t.Data) == 0)
}
