package context_storage

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"

	codfishmodel "code.byted.org/codebase/codfish/core/model"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"github.com/mohae/deepcopy"
	"github.com/samber/lo"
)

// 存储目录常量
const (
	DefaultStorageDir = "/workspace/log/context_storage"
	FilePermissions   = 0600
	DirPermissions    = 0700
)

// StorageWriter 定义存储接口，用于处理 []byte 的存储
type StorageWriter interface {
	Write(filePath string, data []byte) error
}

// StorageReader 定义读取接口，用于处理 []byte 的读取
type StorageReader interface {
	Read(filePath string) ([]byte, error)
}

// FileStorageWriter 默认的文件存储实现
type FileStorageWriter struct {
	Dir string
}

func (f *FileStorageWriter) Write(filePath string, data []byte) error {
	fullPath := filepath.Join(f.Dir, filePath)
	return os.WriteFile(fullPath, data, FilePermissions)
}

// FileStorageReader 默认的文件读取实现
type FileStorageReader struct {
	Dir string
}

func (f *FileStorageReader) Read(filePath string) ([]byte, error) {
	fullPath := filepath.Join(f.Dir, filePath)
	return os.ReadFile(fullPath)
}

func (f *FileStorageReader) ReadFile(filePath string) ([]byte, error) {
	fullPath := filepath.Join(f.Dir, filePath)
	return os.ReadFile(fullPath)
}

// MemStorageWriter 内存存储实现
type MemStorageWriter struct {
	Data map[string][]byte
}

func NewMemStorageWriter() *MemStorageWriter {
	return &MemStorageWriter{
		Data: make(map[string][]byte),
	}
}

func (m *MemStorageWriter) Write(filePath string, data []byte) error {
	// 深拷贝数据，避免外部修改影响存储的数据
	dataCopy := make([]byte, len(data))
	copy(dataCopy, data)
	m.Data[filePath] = dataCopy
	return nil
}

// MemStorageReader 内存读取实现
type MemStorageReader struct {
	Data map[string][]byte
}

func NewMemStorageReader(data map[string][]byte) *MemStorageReader {
	return &MemStorageReader{
		Data: data,
	}
}

func (m *MemStorageReader) Read(filePath string) ([]byte, error) {
	data, exists := m.Data[filePath]
	if !exists {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}
	// 返回数据的副本，避免外部修改影响存储的数据
	dataCopy := make([]byte, len(data))
	copy(dataCopy, data)
	return dataCopy, nil
}

type ContextStorage struct {
	logger    iris.Logger
	dir       string
	isRestart *bool
	result    StoreResult
	// 事件日志文件句柄
	eventFile      *os.File
	eventFilePath  string
	eventFileMutex sync.Mutex
}

func NewContextStorage(dir string, logger iris.Logger) *ContextStorage {
	if dir == "" {
		dir = DefaultStorageDir
	}
	return &ContextStorage{dir: dir, result: StoreResult{}, logger: logger}
}

func (a *ContextStorage) Close() error {
	if a.eventFile != nil {
		return a.eventFile.Close()
	}
	return nil
}

type StoreResult struct {
	AgentRunContext      *iris.AgentRunContext
	AgentRunContextStore map[string]json.RawMessage
	AgentRunEvent        *iris.AgentRunEventBuffer
	RunAgentRequest      *entity.RunAgentRequest
	RPCMeta              *codfishmodel.RPCMeta
}

// Store 根据存储类型将数据保存到存储中
// 支持三种类型的数据存储：
// - StorageTypeAgentRunContext：对应 AgentRunContext 结构，每次全量覆盖存储
// - StorageTypeAgentEvent：对应 AgentRunEvent 结构，每次增量保存
// - StorageTypeRunAgentRequest：对应 RunAgentRequest 结构，每次全量覆盖保存
// - StorageTypeAgentRequestContext:对应 RPCMeta 结构，每次全量覆盖保存
// - StorageTypeAgentRunContextStore: 对应 map[string]any 结构，每次全量覆盖保存，解析成 map[string]json.RawMessage 方便反序列化
func (a *ContextStorage) Store(ctx context.Context, storageType iris.StorageType, opt iris.StoreOption) error {
	if ctx == nil {
		return fmt.Errorf("context is nil")
	}

	// 创建存储目录（如果不存在）
	if err := os.MkdirAll(a.dir, DirPermissions); err != nil {
		return fmt.Errorf("failed to create storage directory %s: %w", a.dir, err)
	}

	// 构造 StorageWriter 实例
	writer := &FileStorageWriter{
		Dir: a.dir,
	}

	switch storageType {
	case iris.StorageTypeAgentRunContext:
		if opt.AgentRunContext == nil {
			return fmt.Errorf("agent run context is nil")
		}
		return a.storeAgentRunContext(storageType.FileName(), opt.AgentRunContext, writer)
	case iris.StorageTypeAgentRunContextStore:
		if opt.AgentRunContextStore == nil {
			return fmt.Errorf("agent run context store is nil")
		}
		return a.storeRunAgentContextStore(storageType.FileName(), opt.AgentRunContextStore, writer)
	case iris.StorageTypeAgentEvent:
		if opt.AgentRunEvent == nil {
			return fmt.Errorf("agent run event is nil")
		}
		return a.storeAgentRunEvent(storageType.FileName(), opt.AgentRunEvent)
	case iris.StorageTypeRunAgentRequest:
		if opt.RunAgentRequest == nil {
			return fmt.Errorf("run agent request is nil")
		}
		return a.storeRunAgentRequest(storageType.FileName(), opt.RunAgentRequest, writer)
	case iris.StorageTypeAgentRequestMeta:
		if opt.RPCMeta == nil {
			return fmt.Errorf("rpc meta is nil")
		}
		return a.storeRPCMeta(storageType.FileName(), opt.RPCMeta, writer)
	default:
		return fmt.Errorf("unknown storage type: %s", storageType)
	}
}

func (a *ContextStorage) SaveAgentRunContext(ctx *iris.AgentRunContext, writer StorageWriter) error {
	runContext := iris.StorageTypeAgentRunContext
	return a.storeAgentRunContext(runContext.FileName(), ctx, writer)
}

func (a *ContextStorage) LoadAgentRunContext(reader StorageReader) (*iris.AgentRunContext, error) {
	runContext := iris.StorageTypeAgentRunContext
	return a.recoverAgentRunContext(runContext.FileName(), reader)
}

// storeAgentRunContext 将 AgentRunContext 存储到文件（全量覆盖）
func (a *ContextStorage) storeAgentRunContext(filePath string, ctx *iris.AgentRunContext, writer StorageWriter) error {
	ctxData, err := json.MarshalIndent(ctx, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal agent run context: %w", err)
	}

	if err := writer.Write(filePath+".ctx", ctxData); err != nil {
		return fmt.Errorf("failed to write agent run context to %s: %w", filePath+".ctx", err)
	}

	memData, err := json.MarshalIndent(ctx.State.Memory, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal agent memory: %w", err)
	}

	if err := writer.Write(filePath+".mem", memData); err != nil {
		return fmt.Errorf("failed to write agent memory to %s: %w", filePath+".mem", err)
	}

	return nil
}

func (a *ContextStorage) storeRunAgentContextStore(filePath string, store map[string]any, writer StorageWriter) error {
	newStore := deepcopy.Copy(store).(map[string]any)
	if a.result.AgentRunContextStore != nil {
		// 如果存储的 store 里面有，传入的 store 没有，则进行合并
		for k, _ := range a.result.AgentRunContextStore {
			if oldValue, ok := newStore[k]; !ok {
				newStore[k] = oldValue
			}
		}
	}
	data, err := json.MarshalIndent(newStore, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal agent run context store: %w", err)
	}

	if err := writer.Write(filePath, data); err != nil {
		return fmt.Errorf("failed to write agent run context store to %s: %w", filePath, err)
	}

	return nil
}

// storeAgentRunEvent 将 AgentRunEvent 存储到文件（增量保存）
func (a *ContextStorage) storeAgentRunEvent(filePath string, event *iris.AgentRunEventBuffer) error {
	// 使用专门的互斥锁保护事件文件的访问
	a.eventFileMutex.Lock()
	defer a.eventFileMutex.Unlock()

	fullPath := filepath.Join(a.dir, filePath)
	// 如果文件句柄为空或者路径不匹配，则打开文件
	if a.eventFile == nil || a.eventFilePath != fullPath {
		// 如果已有打开的文件，先关闭
		if a.eventFile != nil {
			if err := a.eventFile.Close(); err != nil {
				return fmt.Errorf("failed to close previous event file %s: %w", a.eventFilePath, err)
			}
			a.eventFile = nil
		}

		// 以追加模式打开文件，如果不存在则创建
		file, err := os.OpenFile(fullPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, FilePermissions)
		if err != nil {
			return fmt.Errorf("failed to open event file %s: %w", fullPath, err)
		}
		a.eventFile = file
		a.eventFilePath = fullPath
	}

	// 序列化并追加事件
	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal agent run event: %w", err)
	}

	// 添加换行符以符合 JSONL 格式
	data = append(data, '\n')

	if _, err := a.eventFile.Write(data); err != nil {
		// 如果写入失败，关闭文件并重置状态，下次会重新打开
		a.eventFile.Close()
		a.eventFile = nil
		return fmt.Errorf("failed to write agent run event to %s: %w", fullPath, err)
	}

	// 确保数据被写入磁盘
	if err := a.eventFile.Sync(); err != nil {
		return fmt.Errorf("failed to sync event file %s: %w", fullPath, err)
	}

	return nil
}

// storeRunAgentRequest 将 RunAgentRequest 存储到文件（全量覆盖）
func (a *ContextStorage) storeRunAgentRequest(filePath string, req *entity.RunAgentRequest, writer StorageWriter) error {
	data, err := json.MarshalIndent(req, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal run agent request: %w", err)
	}

	if err := writer.Write(filePath, data); err != nil {
		return fmt.Errorf("failed to write run agent request to %s: %w", filePath, err)
	}

	return nil
}

// storeRPCMeta 将 RPCMeta 存储到文件（全量覆盖）
func (a *ContextStorage) storeRPCMeta(filePath string, meta *codfishmodel.RPCMeta, writer StorageWriter) error {
	data, err := json.MarshalIndent(meta, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal request meta: %w", err)
	}

	if err := writer.Write(filePath, data); err != nil {
		return fmt.Errorf("failed to write request meta to %s: %w", filePath, err)
	}

	return nil
}

// Recover 从存储中读取数据并反序列化为适当的结构
func (a *ContextStorage) Recover(ctx context.Context, storageType iris.StorageType) (interface{}, bool, error) {
	if result, ok := a.loadData(storageType); ok {
		return result, true, nil
	}

	if ctx == nil {
		return nil, false, fmt.Errorf("context is nil")
	}

	reader := &FileStorageReader{
		Dir: a.dir,
	}

	filePath := storageType.FileName()

	// 检查文件是否存在
	if storageType != iris.StorageTypeAgentRunContext {
		fullPath := filepath.Join(a.dir, filePath)
		fileInfo, err := os.Stat(fullPath)
		if os.IsNotExist(err) {
			return nil, false, nil
		}
		if err != nil {
			return nil, false, fmt.Errorf("failed to stat file %s: %w", fullPath, err)
		}

		// 检查文件是否为空
		if fileInfo.Size() == 0 {
			return nil, false, fmt.Errorf("storage file is empty: %s", fullPath)
		}
		a.logger.Infof("recovering storage type: %s, file path: %s, size: %d", storageType, fullPath, fileInfo.Size())
	} else {
		fullPath := filepath.Join(a.dir, filePath+".ctx")
		fileInfo, err := os.Stat(fullPath)
		if os.IsNotExist(err) {
			return nil, false, nil
		}
		if err != nil {
			return nil, false, fmt.Errorf("failed to stat file %s: %w", fullPath, err)
		}

		// 检查文件是否为空
		if fileInfo.Size() == 0 {
			return nil, false, fmt.Errorf("storage file is empty: %s", fullPath)
		}
		a.logger.Infof("recovering storage type: %s, file path: %s, size: %d", storageType, fullPath, fileInfo.Size())
	}
	switch storageType {
	case iris.StorageTypeAgentRunContext:
		resp, err := a.recoverAgentRunContext(filePath, reader)
		return resp, true, err
	case iris.StorageTypeAgentEvent:
		resp, err := a.recoverAgentRunEvents(filepath.Join(a.dir, filePath))
		return resp, true, err
	case iris.StorageTypeRunAgentRequest:
		resp, err := a.recoverRunAgentRequest(filePath, reader)
		return resp, true, err
	case iris.StorageTypeAgentRequestMeta:
		resp, err := a.recoverRequestMeta(filePath, reader)
		return resp, true, err
	case iris.StorageTypeAgentRunContextStore:
		resp, err := a.recoverAgentRunContextStore(filePath, reader)
		return resp, true, err
	default:
		return nil, false, fmt.Errorf("unknown storage type: %s", storageType)
	}
}

// recoverAgentRunContext 从文件中读取并反序列化 AgentRunContext
func (a *ContextStorage) recoverAgentRunContext(filePath string, reader StorageReader) (*iris.AgentRunContext, error) {
	ctxData, err := reader.Read(filePath + ".ctx")
	if err != nil {
		return nil, fmt.Errorf("failed to read agent run context from %s: %w", filePath+".ctx", err)
	}

	var runContext iris.AgentRunContext
	if err := json.Unmarshal(ctxData, &runContext); err != nil {
		return nil, fmt.Errorf("failed to unmarshal agent run context: %w", err)
	}

	memData, err := reader.Read(filePath + ".mem")
	if err != nil {
		return nil, fmt.Errorf("failed to read agent memory from %s: %w", filePath+".mem", err)
	}

	var mem memory.AgentMemory
	if err := json.Unmarshal(memData, &mem); err != nil {
		return nil, fmt.Errorf("failed to unmarshal agent memory: %w", err)
	}
	if runContext.State == nil {
		runContext.State = &iris.AgentRunState{}
	}
	runContext.State.Memory = &mem

	return &runContext, nil
}

// recoverAgentRunContextStore 从文件中读取并反序列化 map[string]json.RawStorage
func (a *ContextStorage) recoverAgentRunContextStore(filePath string, reader StorageReader) (map[string]json.RawMessage, error) {
	if a.result.AgentRunContextStore != nil {
		return a.result.AgentRunContextStore, nil
	}
	data, err := reader.Read(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read agent run context from %s: %w", filePath, err)
	}

	var store map[string]json.RawMessage
	if err := json.Unmarshal(data, &store); err != nil {
		return nil, fmt.Errorf("failed to unmarshal agent run context: %w", err)
	}
	a.result.AgentRunContextStore = store

	return store, nil
}

// recoverAgentRunEvents 从文件中读取并反序列化 AgentRunEvent 条目
func (a *ContextStorage) recoverAgentRunEvents(filePath string) ([]iris.AgentRunEventBuffer, error) {
	// 加锁以确保读取过程中不会有写入操作
	a.eventFileMutex.Lock()
	defer a.eventFileMutex.Unlock()

	var file *os.File
	var err error
	file, err = os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open agent run events file %s: %w", filePath, err)
	}
	defer file.Close()

	var events []iris.AgentRunEventBuffer
	reader := bufio.NewReaderSize(file, 10*1024*1024)
	lineNum := 0

	for {
		lineRaw, _, err := reader.ReadLine()
		if err != nil {
			if err != io.EOF {
				return nil, fmt.Errorf("failed to read line %d: %w", lineNum, err)
			}
			break
		}
		lineNum++
		line := string(lineRaw)
		if line == "" {
			continue
		}

		var event iris.AgentRunEventBuffer
		if err := json.Unmarshal([]byte(line), &event); err != nil {
			return nil, fmt.Errorf("failed to unmarshal agent run event at line %d: %w", lineNum, err)
		}
		events = append(events, event)
	}

	if len(events) == 0 {
		return nil, fmt.Errorf("no valid events found in file %s", filePath)
	}

	return events, nil
}

// recoverRunAgentRequest 从文件中读取并反序列化 RunAgentRequest
func (a *ContextStorage) recoverRunAgentRequest(filePath string, reader StorageReader) (*entity.RunAgentRequest, error) {
	data, err := reader.Read(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read run agent request from %s: %w", filePath, err)
	}

	var request entity.RunAgentRequest
	if err := json.Unmarshal(data, &request); err != nil {
		return nil, fmt.Errorf("failed to unmarshal run agent request: %w", err)
	}

	return &request, nil
}

// recoverRequestMeta 从文件中读取并反序列化 RPCMeta
func (a *ContextStorage) recoverRequestMeta(filePath string, reader StorageReader) (*codfishmodel.RPCMeta, error) {
	data, err := reader.Read(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read request meta from %s: %w", filePath, err)
	}

	var meta codfishmodel.RPCMeta
	if err := json.Unmarshal(data, &meta); err != nil {
		return nil, fmt.Errorf("failed to unmarshal request meta: %w", err)
	}

	return &meta, nil
}

func (a *ContextStorage) loadData(storageType iris.StorageType) (interface{}, bool) {
	switch storageType {
	case iris.StorageTypeAgentRunContextStore:
		if a.result.AgentRunContextStore != nil {
			return a.result.AgentRunContextStore, true
		}
	}
	return nil, false
}

func (a *ContextStorage) IsRestart() (bool, error) {
	if a.isRestart != nil {
		return *a.isRestart, nil
	}
	t := iris.StorageTypeRunAgentRequest
	filePath := filepath.Join(a.dir, t.FileName())
	_, err := os.Stat(filePath)
	if err != nil {
		a.isRestart = lo.ToPtr(false)
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, err
	}
	a.isRestart = lo.ToPtr(true)
	return true, nil
}
