package iris

import (
	"code.byted.org/devgpt/kiwis/lib/config"
	"github.com/samber/lo"
)

// AgentRunConfig is a wrapper around config.AgentRunConfig
// But provides some additional utilities
type AgentRunConfig struct {
	config.AgentRunConfig
}

type SceneModelConfig struct {
	Model           string
	Temperature     float32
	TopP            *float32
	MaxTokens       int32
	PromptMaxTokens int32
	Thinking        *config.ThinkingConfig
	Tag             string
	FallbackModels  []string
	Verbosity       *string
}

func (c *AgentRunConfig) GetModelByScene(scene string) SceneModelConfig {
	res, ok := lo.Find(c.ModelScenesConfig.SceneSpecifications, func(item config.ScenesModelConfig) bool {
		return lo.Contains(item.Scenes, scene)
	})
	if !ok {
		return SceneModelConfig{
			Model:           c.ModelScenesConfig.Default.Name,
			Temperature:     c.ModelScenesConfig.Default.Temperature,
			TopP:            lo.ToPtr(c.ModelScenesConfig.Default.TopP),
			MaxTokens:       int32(c.ModelScenesConfig.Default.MaxTokens),
			PromptMaxTokens: int32(c.ModelScenesConfig.Default.PromptMaxTokens),
			Thinking:        c.ModelScenesConfig.Default.Thinking,
			Tag:             scene + "_selected_by_default",
			FallbackModels:  []string{},
			Verbosity:       c.ModelScenesConfig.Default.Verbosity,
		}
	}
	return SceneModelConfig{
		Model:           res.ModelConfig.Name,
		Temperature:     res.ModelConfig.Temperature,
		TopP:            lo.ToPtr(res.ModelConfig.TopP),
		MaxTokens:       int32(res.ModelConfig.MaxTokens),
		PromptMaxTokens: int32(res.ModelConfig.PromptMaxTokens),
		Thinking:        res.ModelConfig.Thinking,
		Tag:             scene,
		FallbackModels:  res.ModelConfig.FallbackModels,
		Verbosity:       res.ModelConfig.Verbosity,
	}
}
func (c *AgentRunConfig) TryGetModelByScene(scene string) (SceneModelConfig, bool) {
	res, ok := lo.Find(c.ModelScenesConfig.SceneSpecifications, func(item config.ScenesModelConfig) bool {
		return lo.Contains(item.Scenes, scene)
	})
	if !ok {
		return SceneModelConfig{
			Model:           c.ModelScenesConfig.Default.Name,
			Temperature:     c.ModelScenesConfig.Default.Temperature,
			TopP:            lo.ToPtr(c.ModelScenesConfig.Default.TopP),
			MaxTokens:       int32(c.ModelScenesConfig.Default.MaxTokens),
			PromptMaxTokens: int32(c.ModelScenesConfig.Default.PromptMaxTokens),
			Thinking:        c.ModelScenesConfig.Default.Thinking,
			Tag:             scene + "_selected_by_default",
			FallbackModels:  c.ModelScenesConfig.Default.FallbackModels,
			Verbosity:       c.ModelScenesConfig.Default.Verbosity,
		}, false
	}
	return SceneModelConfig{
		Model:           res.ModelConfig.Name,
		Temperature:     res.ModelConfig.Temperature,
		TopP:            lo.ToPtr(res.ModelConfig.TopP),
		MaxTokens:       int32(res.ModelConfig.MaxTokens),
		PromptMaxTokens: int32(res.ModelConfig.PromptMaxTokens),
		Tag:             scene,
		Thinking:        res.ModelConfig.Thinking,
		FallbackModels:  res.ModelConfig.FallbackModels,
		Verbosity:       res.ModelConfig.Verbosity,
	}, true
}

func (c *AgentRunConfig) GetVariantByScene(scene string) string {
	res, ok := lo.Find(c.AgentVariantConfig.Variants, func(item config.AgentVariantDetail) bool {
		return lo.Contains(item.Scenes, scene)
	})
	if !ok {
		return c.AgentVariantConfig.Default.Variant
	}
	return res.Variant
}
