package iris

import (
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
)

type MentionType string

const (
	MentionTypeCodebase     MentionType = "codebase"
	MentionTypeLarkDoc      MentionType = "lark_doc"
	MentionTypeAttachment   MentionType = "attachment"
	MentionTypeAeolus       MentionType = "aeolus"
	MentionTypeSlardarIssue MentionType = "slardar_issue"
	MentionTypeSnippet      MentionType = "snippet"
)

type Mention interface {
	GetID() string
	GetType() MentionType
	PromptString() string  // for agent
	DisplayString() string // for display preprocessing items to the user
	CopilotString() string // for copilot prompt
}

type BaseMention struct {
	ID   string      `json:"id" mapstructure:"id"`
	Type MentionType `json:"type" mapstructure:"type"`
}

func (m BaseMention) GetID() string {
	return m.ID
}

func (m BaseMention) GetType() MentionType {
	return m.Type
}

func (m BaseMention) PromptString() string {
	return fmt.Sprintf("@[%s](%s)", m.Type, m.ID)
}

func (m BaseMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.ID)
}

func (m BaseMention) CopilotString() string { return "" }

// mention a codebase/github repository
type CodebaseMention struct {
	BaseMention `json:",inline"`
	RawURL      string             `json:"raw_url" mapstructure:"raw_url"` // raw repo url if parsed from query
	RepoName    string             `json:"repo_name" mapstructure:"repo_name"`
	Platform    entity.GitPlatform `json:"platform" mapstructure:"platform"` // one of the following: codebase, github, bitbucket

	// one of the following fields, or none for default branch
	Branch   string `json:"branch" mapstructure:"branch"`
	Tag      string `json:"tag" mapstructure:"tag"`
	CommitID string `json:"commit_id" mapstructure:"commit_id"`

	// mentions a specific file or directory in the codebase
	Path string `json:"path" mapstructure:"path"`
}

var _ Mention = &CodebaseMention{}

func (m CodebaseMention) PromptString() string {
	buf := strings.Builder{}
	buf.WriteString(lo.Ternary(
		m.RawURL != "",
		fmt.Sprintf("@[%s](%s)", m.RepoName, m.RawURL),
		fmt.Sprintf("@[%s](https://code.byted.org/%s)", m.RepoName, m.RepoName),
	))
	if m.Branch != "" {
		buf.WriteString(fmt.Sprintf(" branch: %s", m.Branch))
	}
	if m.Tag != "" {
		buf.WriteString(fmt.Sprintf(" tag: %s", m.Tag))
	}
	if m.CommitID != "" {
		buf.WriteString(fmt.Sprintf(" commit: %s", m.CommitID))
	}
	if m.Path != "" {
		buf.WriteString(fmt.Sprintf(" path: %s", m.Path))
	}
	return buf.String()
}

func (m CodebaseMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.RepoName)
}

// mention a lark document or knowledge
type LarkDocMention struct {
	BaseMention     `json:",inline"`
	DocID           string            `json:"doc_id" mapstructure:"doc_id"` // 飞书文档 token
	Title           string            `json:"title" mapstructure:"title"`
	URL             string            `json:"url" mapstructure:"url"`
	KnowledgeBaseID string            `json:"knowledge_base_id" mapstructure:"knowledge_base_id"`
	DocumentID      string            `json:"document_id" mapstructure:"document_id"`
	Comments        []*LarkDocComment `json:"comments" mapstructure:"comments"`
}

type LarkDocComment struct {
	CommentID string   `json:"comment_id" mapstructure:"comment_id"`
	BlockID   string   `json:"block_id" mapstructure:"block_id"`
	Quote     string   `json:"quote" mapstructure:"quote"`
	UserID    string   `json:"user_id" mapstructure:"user_id"`
	Username  string   `json:"username" mapstructure:"username"`
	Content   string   `json:"content" mapstructure:"content"`
	Replies   []string `json:"replies" mapstructure:"replies"` // format Reply to string
}

var _ Mention = &LarkDocMention{}

func (m LarkDocMention) PromptString() string {
	return fmt.Sprintf("@[%s](%s)", lo.Ternary(m.Title != "", m.Title, m.URL), m.URL)
}

// XML template for single document with comments using iris.LarkDocMention structure
const commentTemplateStr = `<doc url="{{.URL}}">
{{- range .Comments}}
    <quote_comment id={{.CommentID}}>
		<quote>{{if .Quote}}{{.Quote}}{{else}}None{{end}}</quote>
		<comment>{{.Content}}</comment>
    </quote_comment>
{{- end}}
</doc>`

var CommentTemplate = template.Must(template.New("comments").Parse(commentTemplateStr))

func (m LarkDocMention) CopilotString() string {
	var buf strings.Builder
	if err := CommentTemplate.Execute(&buf, m); err != nil {
		return ""
	} else if xmlContent := buf.String(); xmlContent != "" {
		return xmlContent
	}
	return ""
}

func (m LarkDocMention) DisplayString() string {
	return fmt.Sprintf("@%s", lo.Ternary(m.Title != "", m.Title, m.URL))
}

// mention an user uploaded attachment
type AttachmentMention struct {
	BaseMention `json:",inline"`
	ArtifactID  string `json:"artifact_id" mapstructure:"artifact_id"`
	Path        string `json:"path" mapstructure:"path"`
	IsFromQuery bool   `json:"is_from_query" mapstructure:"is_from_query"` // 附件是否从用户 query 转换而来，如果是可能需要拼接默认 query
}

var _ Mention = &AttachmentMention{}

func (m AttachmentMention) PromptString() string {
	return fmt.Sprintf("@[%s](file://%s)", m.Path, m.Path)
}

func (m AttachmentMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.Path)
}

// mention an Aeolus dashboard or data query
type AeolusMention struct {
	BaseMention `json:",inline"`
	URL         string `json:"url" mapstructure:"url"`
}

var _ Mention = &AeolusMention{}

func (m AeolusMention) PromptString() string {
	return fmt.Sprintf("@[%s](%s)", m.URL, m.URL)
}

func (m AeolusMention) DisplayString() string {
	return fmt.Sprintf("@%s", m.URL)
}

// mention a slardar issue, e.g. https://t.wtturl.cn/XqjdTj5a87Q/
// internal only
type SlardarIssueMention struct {
	BaseMention `json:",inline"`
	URL         string `json:"url" mapstructure:"url"`
	URLType     string `json:"url_type" mapstructure:"url_type"`
	OS          string `json:"os" mapstructure:"os"`
	CrashType   string `json:"crash_type" mapstructure:"crash_type"`

	KnowledgeMatchingType         string `json:"knowledge_matching_type" mapstructure:"knowledge_matching_type"`                   // one of the following: "accurate"
	ScenarioKnowledgeMatchingType string `json:"scenario_knowledge_matching_type" mapstructure:"scenario_knowledge_matching_type"` // one of the following: "SlardarApp"
	SubScenario                   string `json:"sub_scenario" mapstructure:"sub_scenario"`                                         // "{url_type}_{os}_{crash_type}" e.g. "long_url_android_crash"
}

var _ Mention = &SlardarIssueMention{}

func (m SlardarIssueMention) PromptString() string {
	return fmt.Sprintf("@[%s](%s)", m.URL, m.URL)
}

func (m SlardarIssueMention) DisplayString() string {
	return m.URL
}

type SnippetMention struct {
	BaseMention `json:",inline"`
	Path        string `json:"path" mapstructure:"path"`
	StartLine   int32  `json:"start_line" mapstructure:"start_line"`
	EndLine     int32  `json:"end_line" mapstructure:"end_line"`
	Content     string `json:"content" mapstructure:"content"`
}

var _ Mention = &SnippetMention{}

const maxLinesToShow = 500

func (m SnippetMention) PromptString() string {
	lines := strings.Split(m.Content, "\n")
	var content string
	totalLines := len(lines)
	if totalLines > maxLinesToShow {
		// 只保留前 500 行，并加省略提示
		head := lines[:maxLinesToShow]
		content = strings.Join(head, "\n") + "\n...(content too long, please read the file by code location if necessary)"
	} else {
		content = m.Content
	}
	content = strings.ReplaceAll(content, "\n", "\n>")
	return fmt.Sprintf("@%s %d-%d\n>%s\n", filepath.Base(m.Path), m.StartLine, m.EndLine, content)
}

func (m SnippetMention) DisplayString() string {
	return fmt.Sprintf("@%s %d-%d", filepath.Base(m.Path), m.StartLine, m.EndLine)
}

func UnmarshalMention(mention string) Mention {
	var m BaseMention
	err := json.Unmarshal([]byte(mention), &m)
	if err != nil {
		return nil
	}
	switch m.Type {
	case MentionTypeCodebase:
		var codebaseMention CodebaseMention
		err = json.Unmarshal([]byte(mention), &codebaseMention)
		if err != nil {
			return nil
		}
		return &codebaseMention
	case MentionTypeLarkDoc:
		var larkDocMention LarkDocMention
		err = json.Unmarshal([]byte(mention), &larkDocMention)
		if err != nil {
			return nil
		}
		return &larkDocMention
	case MentionTypeAttachment:
		var attachmentMention AttachmentMention
		err = json.Unmarshal([]byte(mention), &attachmentMention)
		if err != nil {
			return nil
		}
		return &attachmentMention
	case MentionTypeAeolus:
		var aeolusMention AeolusMention
		err = json.Unmarshal([]byte(mention), &aeolusMention)
		if err != nil {
			return nil
		}
		return &aeolusMention
	case MentionTypeSlardarIssue:
		var slardarIssueMention SlardarIssueMention
		err = json.Unmarshal([]byte(mention), &slardarIssueMention)
		if err != nil {
			return nil
		}
		return &slardarIssueMention
	case MentionTypeSnippet:
		var snippetMention SnippetMention
		err = json.Unmarshal([]byte(mention), &snippetMention)
		if err != nil {
			return nil
		}
		return &snippetMention
	}

	// return the base mention if the type is not supported
	return &m
}

func GetMention(mention entity.Mention) Mention {
	// the `type` field in frontend-server is not the same `type` we use in agent
	// use fields to determine the type
	switch true {
	case mention.CodebaseMention != nil:
		return &CodebaseMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeCodebase,
			},
			Tag:      mention.CodebaseMention.Tag,
			Platform: entity.GitPlatformCodebase, // @ now only supports codebase platform
			RepoName: mention.CodebaseMention.RepoName,
			Branch:   mention.CodebaseMention.Branch,
			Path:     mention.CodebaseMention.Path,
		}
	case mention.LarkDocMention != nil:
		return &LarkDocMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeLarkDoc,
			},
			DocID:           mention.LarkDocMention.DocID,
			Title:           mention.LarkDocMention.Title,
			URL:             mention.LarkDocMention.URL,
			KnowledgeBaseID: mention.LarkDocMention.KnowledgeBaseID,
			DocumentID:      mention.LarkDocMention.ID,
		}
	case mention.AttachmentMention != nil:
		// Check if SubType is lark_doc, if so create LarkDocMention
		if mention.AttachmentMention.Type == entity.AttachmentMentionLarkDocCommentType {
			// Convert comments from CommentMetadata to LarkDocComment
			comments := make([]*LarkDocComment, len(mention.AttachmentMention.Comments))
			for idx, comment := range mention.AttachmentMention.Comments {
				replies := lo.Map(comment.Replies, func(reply *entity.Reply, _ int) string {
					return strings.Join(lo.Map(reply.ReplyContent.Elements, func(el *entity.ReplyElement, _ int) string {
						content := el.TextRun.Text
						if el.DocsLink != nil && len(el.DocsLink.Link) > 0 {
							content = fmt.Sprintf("%s [url]%s", content, el.DocsLink.Link)
						}
						return content
					}), "")
				})
				comments[idx] = &LarkDocComment{
					CommentID: comment.CommentID,
					BlockID:   comment.BlockID,
					Quote:     comment.Quote,
					UserID:    comment.UserID,
					Username:  comment.Username,
					Content:   strings.Join(replies, "; "),
					Replies:   replies,
				}
			}

			return &LarkDocMention{
				BaseMention: BaseMention{
					ID:   mention.ID,
					Type: MentionTypeLarkDoc,
				},
				DocID:           mention.AttachmentMention.ArtifactID, // Use ArtifactID as DocID for lark_doc
				Title:           mention.AttachmentMention.Path,       // Use Path as Title for now
				URL:             mention.AttachmentMention.URL,
				KnowledgeBaseID: "",         // Not available from AttachmentMention
				DocumentID:      mention.ID, // Use mention ID as DocumentID
				Comments:        comments,
			}
		}

		// Default AttachmentMention handling
		return &AttachmentMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeAttachment,
			},
			ArtifactID:  mention.AttachmentMention.ArtifactID,
			Path:        mention.AttachmentMention.Path,
			IsFromQuery: mention.AttachmentMention.IsFromQuery,
		}
	case mention.AeolusMention != nil:
		return &AeolusMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeAeolus,
			},
			URL: mention.AeolusMention.URL,
		}
	case mention.SnippetMention != nil:
		return &SnippetMention{
			BaseMention: BaseMention{
				ID:   mention.ID,
				Type: MentionTypeSnippet,
			},
			Path:      mention.SnippetMention.Path,
			StartLine: mention.SnippetMention.SnippetRange.StartLine,
			EndLine:   mention.SnippetMention.SnippetRange.EndLine,
			Content:   mention.SnippetMention.Content,
		}
	default:
		return nil
	}
}
