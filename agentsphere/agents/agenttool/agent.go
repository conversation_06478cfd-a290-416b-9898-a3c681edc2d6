package agenttool

import (
	"embed"
	"strings"

	"github.com/samber/lo"
	"github.com/spf13/cast"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	websearchtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/toolset"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	websearchactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/knowledges"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	condenser "code.byted.org/devgpt/kiwis/agentsphere/memory/condenser/impl"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
)

var (
	Identifier = "mewtwo_run"
	parameters = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "A self contained task prompt that can be completed by the agent. The description should give out background context and the specific goals, but not detailed datapoints or libraries to use.",
		},
		{
			Name:        "persona",
			Description: "Persona to put at the very first of the system prompt. Describe the agent's role, capabilities, and any other relevant information",
		},
		{
			Name:        "tools_selection_thought",
			Description: "Iterate through the toolset list ONE BY ONE, consider each toolset's suitability for the task. Format: `<toolset_name>:yes/maybe/nice to have/no`. Do not skip any toolset.",
		},
		{
			Name:        "tools",
			Description: "Tools given to the created agent in comma separated strings, e.g. `tool1,tool2`. Include all toolsets possible to be used by the agent",
		},
	}
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")

	_ = lo.Must(promptset.ExecutePrompt("description", "", nil))
)

type DynamicAgentStore struct {
	CurrentTask string
}

func init() {
	websearchtool.SearchAgentCreator = func(run *iris.AgentRunContext) websearchtool.InternalAISearcher {
		enableInternalSearch := cast.ToBool(run.Parameters[websearchactor.EnableInternalSearchParameterKey])
		run.GetLogger().Infof("enable_internal_search: %v", enableInternalSearch)
		return websearchactor.New(websearchactor.CreateOption{EnableBytedSearch: enableInternalSearch})
	}
}

type Agent struct {
	actors.BaseActor
	ProgreAct *agents.ProgreActAgent
	Condenser *condenser.AutoSummarizer
	Variant   string

	Persona          string
	SystemKnowledges []knowledges.KnowledgeItem

	ContextParts ContextParts

	Toolsets []toolset.Toolset

	PromptCache *agents.PromptCache
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	Name          string
	MaxSteps      int
	LLMHooks      *prompt.LLMCallHook
	ExecutionStep *actors.ExecutionStep

	// dynamically decided by model
	Variant        string
	Persona        string
	KnowledgeBase  knowledges.Knowledgebase
	Toolsets       []string
	TaskContext    string
	ExecutionTrace string

	DisableSummarizer bool // whether to summarize tool calls, defaults to true, disabled for testing&benchmarking
	DisableBuildInMCP bool // disable mcp helper
}

func New(run *iris.AgentRunContext, opt CreateOption) *Agent {
	toolsets, _ := toolset.GetToolsets(run, opt.ExecutionStep.Step)
	userDefineToolsets, _ := toolset.GetUserDefineToolsets(run)
	plannerStore := iris.RetrieveStoreByKey[planactentity.PlannerStore](run, planactentity.PlannerStoreKey)
	toolsets = append(toolsets, userDefineToolsets...)
	// toolsets和userDefineToolsets一起放进去过滤
	tools := lo.Filter(append(toolsets, userDefineToolsets...), func(toolset toolset.Toolset, _ int) bool {
		_, ok := lo.Find(opt.Toolsets, func(name string) bool {
			return toolset.Identifier == name
		})
		return ok
	})
	if !opt.DisableBuildInMCP {
		tools = append(tools, toolset.GetMCPLoopToolset(run)...)
	}
	info := iris.AgentInfo{
		Identifier: lo.Ternary(opt.Name == "", Identifier, opt.Name),
		Desc: lo.Must(promptset.ExecutePrompt("description", opt.Variant, map[string]any{
			"Toolsets":           toolsets,
			"UserDefineToolsets": userDefineToolsets,
		})),
	}
	reactor := agents.NewProgreActAgent(agents.ProgreActAgentConfig{
		Info:              info,
		Tools:             toolset.BuildTools(run, tools, opt.ExecutionStep, opt.KnowledgeBase, opt.Name),
		MaxSteps:          opt.MaxSteps,
		MaxFailures:       3,
		DisableSummarizer: opt.DisableSummarizer,
		LLMHook:           opt.LLMHooks,
		ToolChoiceMode:    agents.ToolCallChoiceMode(conv.DefaultAny[string](run.GetConfig().ExtraOptions["tool_choice_mode"])),
	})

	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parameters,
		},
		Variant: opt.Variant,
		Persona: opt.Persona,
		ContextParts: ContextParts{
			TaskContext:    opt.TaskContext,
			ExecutionTrace: opt.ExecutionTrace,
			Locale:         agents.GetUserLanguage(run.Parameters),
			UserRequest:    strings.TrimSpace(plannerStore.Requirements),
		},
		Condenser: condenser.NewAutoSummarizer(&condenser.NewAutoSummarizerConfig{
			Name:    info.Identifier,
			LLM:     run.GetLLM(),
			Config:  run.Config,
			Variant: opt.Variant,
		}),
		Toolsets: tools,
	}
	reactor.Composer = agent.Compose
	reactor.Condenser = agent.Condenser
	agent.ProgreAct = reactor

	agent.ExecutionStep = opt.ExecutionStep

	switch opt.Variant {
	case agententity.VariantExpert: // It usually uses claude model, need to set cache points in messages.
		agent.PromptCache = &agents.PromptCache{
			PromptCacheKey:       reactor.UID(),
			MaxCachePoints:       3, // `tools` list uses 1 cache point.
			CacheType:            "ephemeral",
			NoCacheLastNMessages: 1,
			NewCachePointChars:   5000,
			CachePoints:          map[int]bool{},
		}
	case "gpt":
		// GPT series models only needs to set prompt cache key to make the LLM requests route to the same machine or cluster to increase cache hit ratio.
		agent.PromptCache = &agents.PromptCache{
			PromptCacheKey:       reactor.UID(),
			MaxCachePoints:       0,
			CacheType:            "",
			NoCacheLastNMessages: 0,
			NewCachePointChars:   0,
			CachePoints:          map[int]bool{},
		}
	default:
		// Others not set prompt cache key to make the LLM requests more balanced.
	}
	reactor.PromptCache = agent.PromptCache

	if opt.Variant == agententity.VariantExpert {
		agent.PromptCache = &agents.PromptCache{
			MaxCachePoints:       3, // `tools` list uses 1 cache point.
			CacheType:            "ephemeral",
			NoCacheLastNMessages: 1,
			NewCachePointChars:   5000,
			CachePoints:          map[int]bool{},
		}
	}

	return agent
}

func (a *Agent) Compose(run *iris.AgentRunContext, previousSteps []*iris.AgentRunStep, state *agents.ProgreActState) []*framework.ChatMessage {
	systemMessages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
	})
	cacheType := ""
	if a.PromptCache != nil {
		cacheType = a.PromptCache.CacheType
	}
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}

	a.Condenser.SetPrefix(run, systemMessages) // 压缩时估算前序消息 token 数防止超 token
	workspaceStructure := GetWorkspaceStructure(run)
	mem := memory.GetAgentMemory(run)
	lastStep := mem.LastStep()

	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(promptset.GetTemplate("system", a.Variant), util.ValueToMap(a.GetSystemParts(run))),
		prompt.WithPromptCache(
			prompt.WithUserMessage(promptset.GetTemplate("user", a.Variant), util.ValueToMap(a.ContextParts)),
			cacheType,
		),
		mem.WithCurrentContext(run, &memory.MemoryComposeOptions{
			ThoughtPrompt:         promptset.GetTemplate("thought", a.Variant),
			ObservationPrompt:     promptset.GetTemplate("user", a.Variant),
			StoreKey:              memory.GetMemoryStoreKey(a.ProgreAct.Agent.Name()),
			Variant:               a.Variant,
			ObservationFormatFunc: nil,
			Condenser:             a.Condenser,
		}),
		prompt.WithUserMessage(promptset.GetTemplate("last_message", a.Variant), map[string]any{
			"Workspace":             workspaceStructure.Workspace,
			"WorkspaceRepositories": workspaceStructure.WorkspaceRepositories,
			"RootStructure":         workspaceStructure.RootStructure,
			"LastActions": lo.TernaryF(lastStep != nil && lastStep.Action != nil, func() string {
				return lastStep.Action.Name() // TODO: parallel function call
			}, func() string {
				return "none"
			}),
		}),
	})
	if err != nil {
		run.GetLogger().Errorf("failed to compose messages: %s", err)
	}
	if a.PromptCache != nil {
		a.PromptCache.UpdateCachePoints(messages)
	}
	return messages
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	// mem := memory.GetAgentMemory(run)
	// if options.ParentExecutor != "" {
	// actorSteps := lo.Filter(mem.ActionMemory, func(step *memory.ActionMemoryItem, _ int) bool {
	// 	return step.AgentRunStep.ExecutorAgent == options.ParentExecutor
	// })
	// input += "\n\n[Execution Trace]\n\n## Notes:\nThis is the execution trace of the previous steps. You should focus on the goal of the current task, and use the information below if needed.\n"
	// input += strings.Join(lo.Map(actorSteps, func(step *memory.ActionMemoryItem, _ int) string {
	// 	return fmt.Sprintf("\nInput:\n%s\n\ntool:%s\n\nparameters:%s\n\nOutput:\n%s",
	// 		step.Thought.Rationale,
	// 		step.Thought.Tool,
	// 		conv.JSONFormatString(step.Thought.Parameters),
	// 		action_prompts.ToolObservation(run, step.StepID, step.Action.Name(), step.Inputs, step.Outputs, step.Error))
	// }), "")
	// }

	span, ctx := agentrace.GetRuntimeTracerFromContext(run).
		StartCustomSpan(
			run,
			agentrace.SpanTypeStep,
			a.Name(),
			agentrace.WithObjectSpanData(
				map[string]any{
					"input": input,
				},
			),
		)
	a.ContextParts.TaskContext = input
	if len(options.DynamicPersona) > 0 {
		a.Persona = options.DynamicPersona
	}

	kb := knowledges.CreateKnowledgebase(run)
	knowledge := make([]knowledges.KnowledgeItem, 0)
	if len(options.ToolKnowledgesID) > 0 {
		accurateKnowledge, err := kb.RetrieveKnowledge(run, knowledges.KgRetrieveOption{
			Category: knowledges.KgRetrieveCategoryTool,
			Strategy: knowledges.KgRetrieveStrategyAccurate,
			Param: knowledges.RetrieveParam{
				Agent:        a.Name(),
				Variant:      a.Variant,
				Tools:        []string{a.Name()},
				KnowledgeIDs: options.ToolKnowledgesID,
			},
		})
		if err != nil {
			run.GetLogger().Errorf("failed to retrieve knowledge: %s", err)
		} else {
			//sort by origin id
			for _, id := range options.ToolKnowledgesID {
				for _, k := range accurateKnowledge {
					if k.ID == id {
						knowledge = append(knowledge, k)
						break
					}
				}
			}
		}
	}
	if len(options.SysKnowledgesID) > 0 {
		accurateKnowledge, err := kb.RetrieveKnowledge(run, knowledges.KgRetrieveOption{
			Strategy: knowledges.KgRetrieveStrategyAccurate,
			Category: knowledges.KgRetrieveCategorySystem,
			Param: knowledges.RetrieveParam{
				Agent:        a.Name(),
				Variant:      a.Variant,
				Tools:        []string{a.Name()},
				KnowledgeIDs: options.SysKnowledgesID,
			},
		})
		if err != nil {
			run.GetLogger().Errorf("failed to retrieve knowledge: %s", err)
		} else {
			knowledge = append(knowledge, accurateKnowledge...)
		}
		// sort by origin id
		for _, id := range options.SysKnowledgesID {
			for _, k := range a.SystemKnowledges {
				if k.ID == id {
					knowledge = append(knowledge, k)
					break
				}
			}
		}
	}
	a.SystemKnowledges = append(a.SystemKnowledges, knowledge...)
	a.SystemKnowledges = lo.UniqBy(a.SystemKnowledges, func(item knowledges.KnowledgeItem) string {
		return item.ID
	})
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(
			map[string]any{
				"result": result,
			},
		))
		span.Finish()
	}()

	mem := memory.GetAgentMemory(run)
	memoryMessages, _ := mem.WithPreviousContext(run, &memory.MemoryComposeOptions{
		Variant:     a.Variant,
		TaskContext: input,
		Condenser:   a.Condenser,
	})
	iris.UpdateStoreByKey(run, memory.GetMemoryStoreKey(a.ProgreAct.Agent.Name()), memoryMessages)

	err := a.ProgreAct.RunWithOption(run, &agents.ProgreActAgentRunOption{
		Step: a.ExecutionStep.Step,
	})
	if err != nil {
		// the actor need to return a iris.NewFatal(err) with a non-nil error to send a fatal error message to user
		isFatal := iris.IsFatal(err)
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: lo.Ternary(isFatal, controltool.ConclusionEvaluationFatal, controltool.ConclusionEvaluationFailed),
		}
	}

	lastStep := run.State.LastStep()
	mapstructure.Decode(lastStep.Outputs, &result)
	return result
}
