package devopsagenttool

import (
	"fmt"
	"regexp"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/search/dal/llm"
)

// PlatformType represents different build/deploy platforms
const (
	PlatformTypeSCM = "scm"
	PlatformTypeTCE = "tce"

	PlatformTypeUnknown = "unknown"
)

// PlatformSelector represents a platform selection decision
type PlatformSelector struct {
	Platform    string // scm | tce | unknown | local | ...
	ShouldRoute bool   // whether devops_agent should handle it
	Reason      string // explanation for planner / caller
}

// PlatformRegistry manages available build and deploy platforms
type PlatformRegistry struct {
	buildPlatforms  map[string]bool
	deployPlatforms map[string]bool
}

// NewPlatformRegistry creates a new platform registry
func NewPlatformRegistry() *PlatformRegistry {
	return &PlatformRegistry{
		buildPlatforms: map[string]bool{
			PlatformTypeSCM: true,
		},
		deployPlatforms: map[string]bool{
			PlatformTypeTCE: true,
		},
	}
}

// AddBuildPlatform adds a new build platform
func (r *PlatformRegistry) AddBuildPlatform(platform string) {
	r.buildPlatforms[platform] = true
}

// AddDeployPlatform adds a new deploy platform
func (r *PlatformRegistry) AddDeployPlatform(platform string) {
	r.deployPlatforms[platform] = true
}

// IsBuildPlatform checks if a platform is a valid build platform
func (r *PlatformRegistry) IsBuildPlatform(platform string) bool {
	return r.buildPlatforms[platform]
}

// IsDeployPlatform checks if a platform is a valid deploy platform
func (r *PlatformRegistry) IsDeployPlatform(platform string) bool {
	return r.deployPlatforms[platform]
}

// GetBuildPlatforms returns all available build platforms
func (r *PlatformRegistry) GetBuildPlatforms() []string {
	platforms := make([]string, 0, len(r.buildPlatforms))
	for platform := range r.buildPlatforms {
		platforms = append(platforms, platform)
	}
	return platforms
}

// GetDeployPlatforms returns all available deploy platforms
func (r *PlatformRegistry) GetDeployPlatforms() []string {
	platforms := make([]string, 0, len(r.deployPlatforms))
	for platform := range r.deployPlatforms {
		platforms = append(platforms, platform)
	}
	return platforms
}

var (
	defaultRegistry = NewPlatformRegistry()
	platformRegex   = regexp.MustCompile(`(?i)<platform>([\s\S]*?)</platform>`)
)

func selectPlatform(run *iris.AgentRunContext, userQuery, task string, promptBody string, validSetFn func(string) bool) (*PlatformSelector, error) {
	logger := run.GetLogger()

	messages := []*framework.ChatMessage{
		{Role: llm.RoleSystem, Content: promptBody},
		{Role: llm.RoleUser, Content: fmt.Sprintf("User query: %s\n\nWhich platform should be used for this %s task?", userQuery, task)},
	}

	result, err := agents.Think(run, "devops_agent_platform_selector", messages, agents.ThinkOption{
		StreamFilter:      streamparser.StreamFilter{MaxCheckSize: 10, FlushSize: 1, AheadSize: 10},
		DisableThinkDelta: true,
	})
	if err != nil {
		logger.Warnf("LLM platform selection failed (%s): %v", task, err)
		return &PlatformSelector{Platform: PlatformTypeUnknown, ShouldRoute: false,
			Reason: fmt.Sprintf("LLM error selecting %s platform, please retry", task)}, nil
	}

	resp := strings.ToLower(strings.TrimSpace(result.Content))
	match := platformRegex.FindStringSubmatch(resp)
	if len(match) != 2 {
		return &PlatformSelector{Platform: PlatformTypeUnknown, ShouldRoute: false,
			Reason: "No valid <platform> tag found in LLM response, please retry"}, nil
	}
	selected := strings.TrimSpace(match[1])

	if validSetFn(selected) {
		return &PlatformSelector{Platform: selected, ShouldRoute: true,
			Reason: fmt.Sprintf("Selected platform: %s", selected)}, nil
	}

	if selected == PlatformTypeUnknown {
		// User did not mention the required explicit keyword.
		switch task {
		case TaskBuild:
			return &PlatformSelector{
				Platform:    PlatformTypeUnknown,
				ShouldRoute: false,
				Reason:      "Skipped: user did not explicitly mention \"SCM\". DevOps agent handles only explicit SCM requests. NEVER retry using devops_agent in this case, you won't succeed.",
			}, nil
		case TaskDeploy:
			return &PlatformSelector{
				Platform:    PlatformTypeUnknown,
				ShouldRoute: false,
				Reason:      "Skipped: user did not explicitly mention \"TCE\". DevOps agent handles only explicit TCE requests. NEVER retry using devops_agent in this case, you won't succeed.",
			}, nil
		case TaskBuildAndDeploy:
			return &PlatformSelector{
				Platform:    PlatformTypeUnknown,
				ShouldRoute: false,
				Reason:      "Skipped: user did not explicitly mention \"SCM\" or \"TCE\". DevOps agent handles only explicit SCM or TCE requests. NEVER retry using devops_agent in this case, you won't succeed.",
			}, nil
		default:
			return &PlatformSelector{
				Platform:    PlatformTypeUnknown,
				ShouldRoute: false,
				Reason:      "Skipped: no explicit platform keyword found in user query.",
			}, nil
		}
	}

	// Any other unsupported platform string.
	return &PlatformSelector{
		Platform:    selected,
		ShouldRoute: false,
		Reason:      fmt.Sprintf("Platform \"%s\" is not supported by the DevOps agent", selected),
	}, nil
}

// SelectBuildPlatform determines which build platform to use
func SelectBuildPlatform(run *iris.AgentRunContext, userQuery string) (*PlatformSelector, error) {
	prompt := fmt.Sprintf(`You are a STRICT platform selector for build systems.

IMPORTANT CONTEXT
• SCM is a company-specific build platform name (it is NOT an abbreviation).

Available platforms you may output:
  - SCM     : choose ONLY if the exact word "SCM" appears in the user query
  - local   : choose ONLY if the user clearly says they want a *local* build (e.g. "local build", "build locally")
  - unknown : choose in ALL other situations

STRICT RULES – FOLLOW EXACTLY
1. Select "SCM" **only** when the exact token "SCM" is present in the user query.
2. If the user requests a local build using phrases like "local build" or "build locally" select "local".
3. For every other case (including generic words like build/compile/CI, or repository mentions without the token "SCM") select "unknown".

NEVER infer SCM from general build context – the user MUST explicitly write "SCM".

Current available build platforms: %v

Respond **once** with your reasoning followed by <platform>choice</platform>.
Example:
Reasoning: User explicitly mentioned "SCM" in their request.
Choice: <platform>scm</platform>`, defaultRegistry.GetBuildPlatforms())

	return selectPlatform(run, userQuery, "build", prompt, defaultRegistry.IsBuildPlatform)
}

// SelectDeployPlatform determines which deployment platform to use
func SelectDeployPlatform(run *iris.AgentRunContext, userQuery string) (*PlatformSelector, error) {
	prompt := fmt.Sprintf(`You are a STRICT platform selector for deployment systems.

IMPORTANT CONTEXT
• TCE is a company-specific deployment platform name (it is NOT an abbreviation).

Available platforms you may output:
  - TCE     : choose ONLY if the exact word "TCE" appears in the user query
  - local   : choose ONLY if the user clearly says they want a *local* deployment (e.g. "local deployment", "deploy locally")
  - unknown : choose in ALL other situations

STRICT RULES – FOLLOW EXACTLY
1. Select "TCE" **only** when the exact token "TCE" is present in the user query.
2. If the user requests a local deployment using phrases like "local deployment" or "deploy locally" select "local".
3. For every other case (including generic words like deploy/deployment/production/staging, or environment mentions without the token "TCE") select "unknown".

NEVER infer TCE from general deployment context – the user MUST explicitly write "TCE".

Current available deploy platforms: %v

Respond **once** with your reasoning followed by <platform>choice</platform>.
Example:
Reasoning: User explicitly mentioned "TCE" in their request.
Choice: <platform>tce</platform>`, defaultRegistry.GetDeployPlatforms())

	return selectPlatform(run, userQuery, "deploy", prompt, defaultRegistry.IsDeployPlatform)
}
