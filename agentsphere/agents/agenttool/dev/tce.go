package devopsagenttool

import (
	"bytes"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
)

// parseTCESingleJSON parses the JSON string from text_contents[0] for TCE tools
func (c *CustomDevOpsAgent) parseTCESingleJSON(outputs map[string]any) (map[string]interface{}, error) {

	if outputs == nil {
		return nil, fmt.Errorf("outputs is nil, original output: %#v", outputs)
	}

	textContentsRaw, ok := outputs["text_contents"]
	if !ok {
		return nil, fmt.Errorf("text_contents field not found in outputs, original output: %#v", outputs)
	}

	textContents, ok := textContentsRaw.([]string)
	if !ok {
		return nil, fmt.Errorf("text_contents is not []string, got: %T, original output: %#v", textContentsRaw, outputs)
	}

	if len(textContents) == 0 {
		return nil, fmt.Errorf("text_contents array is empty, original output: %#v", outputs)
	}

	jsonStr := textContents[0]

	var result map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return nil, fmt.Errorf("failed to parse TCE JSON string: %v, json_string: %s, original output: %#v", err, jsonStr, outputs)
	}
	return result, nil
}

// TCEStatusInfo represents the status information from get_tce_deploy_status
type TCEStatusInfo struct {
	Status    string `json:"status"`
	Message   string `json:"message"`
	StartTime int    `json:"start_time"`
	Elapse    int    `json:"elapse"`
}

// TCEDeployResult represents the structured result from a TCE deployment operation
// following the required JSON schema
type TCEDeployResult struct {
	TCEStatus       string `json:"tce_status"`        // 部署状态: success, failed, pending, cancelled, rollbacked
	TCEPSM          string `json:"tce_psm"`           // 服务名
	TCEVersion      string `json:"tce_version"`       // 部署的SCM版本
	TCEElapse       int    `json:"tce_elapse"`        // 部署耗时/秒
	TCEStartTime    int    `json:"tce_start_time"`    // 部署开始时间戳
	TCEEnv          string `json:"tce_env"`           // 部署环境/泳道
	TCEURL          string `json:"tce_url"`           // 部署任务链接
	TCEDeploymentID string `json:"tce_deployment_id"` // 部署工单id
}

func (c *CustomDevOpsAgent) executeTCEDeploy(run *iris.AgentRunContext, psm, scmVersion, env, envType string, parentStepID string) (controltool.ConclusionOutput, error) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	if parentStepID == "" {
		return controltool.ConclusionOutput{}, fmt.Errorf("parentStepID must not be empty")
	}

	deployStep := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: c.name,
	})

	deployStep.Parent = parentStepID

	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(deployStep))

	deployStep.Status = iris.AgentRunStepStatusRunning
	deployStep.Inputs = map[string]any{
		"task":        "deploy",
		"psm":         psm,
		"scm_version": scmVersion,
	}
	publisher.ReportStep(deployStep)

	deployStep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:tce_tool"}}
	publisher.ReportToolCall(deployStep, iris.ToolCallStatusStarted, "starting TCE deploy")

	defer func() {
		publisher.ReportToolCall(deployStep, iris.ToolCallStatusCompleted, "TCE deploy 结果汇总")
		deployStep.Finish(deployStep.Outputs, deployStep.Error)
		publisher.ReportStep(deployStep)
	}()

	logger.Infof("Starting TCE deployment for PSM: %s, SCM Version: %s", psm, scmVersion)

	// Get TCE deploy tools
	tceTools, err := c.getDevOpsAgentMCPTools(run, "tce_deploy")
	if err != nil {
		deployStep.Error = fmt.Errorf("failed to get TCE deploy tools: %v", err)
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Failed to get TCE deploy tools: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	deploymentID, extractedEnv, rawURL, err := c.createTCEDeploy(run, tceTools, psm, scmVersion, env, envType, parentStepID)
	if err != nil {
		deployStep.Error = err
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Failed to create deploy task: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	statusInfo, err := c.watchTCEDeployStatus(run, psm, deploymentID, extractedEnv, 20*time.Second, parentStepID)

	tceDeployResult := TCEDeployResult{
		TCEPSM:          psm,
		TCEVersion:      scmVersion,
		TCEDeploymentID: deploymentID,
		TCEEnv:          extractedEnv,
		TCEURL:          rawURL,
	}

	if statusInfo != nil {
		tceDeployResult.TCEStatus = statusInfo.Status
		tceDeployResult.TCEStartTime = statusInfo.StartTime
		tceDeployResult.TCEElapse = statusInfo.Elapse
	} else {
		tceDeployResult.TCEStatus = "failed"
	}

	if err != nil {
		deployStep.Error = err
		c.createTCEConclusionStep(run, tceDeployResult, err, parentStepID)
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Deploy monitoring failed: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	c.createTCEConclusionStep(run, tceDeployResult, nil, parentStepID)
	var buffer bytes.Buffer
	encoder := json.NewEncoder(&buffer)
	encoder.SetEscapeHTML(false)
	marshalErr := encoder.Encode(tceDeployResult)
	var tceDeployResultJSON []byte
	if marshalErr != nil {
		logger.Warnf("Failed to marshal TCE deploy result: %v", marshalErr)
		tceDeployResultJSON = []byte("{}")
	} else {
		tceDeployResultJSON = buffer.Bytes()
		// Remove trailing newline that Encode adds
		if len(tceDeployResultJSON) > 0 && tceDeployResultJSON[len(tceDeployResultJSON)-1] == '\n' {
			tceDeployResultJSON = tceDeployResultJSON[:len(tceDeployResultJSON)-1]
		}
	}

	deployStep.Outputs = map[string]any{
		"tce_status":        tceDeployResult.TCEStatus,
		"tce_psm":           psm,
		"tce_version":       scmVersion,
		"tce_elapse":        tceDeployResult.TCEElapse,
		"tce_start_time":    tceDeployResult.TCEStartTime,
		"tce_env":           extractedEnv,
		"tce_url":           rawURL,
		"tce_deployment_id": deploymentID,
		"url":               c.addOnesiteParams(rawURL),
	}

	return controltool.ConclusionOutput{
		Content:    string(tceDeployResultJSON),
		Evaluation: controltool.ConclusionEvaluationSuccess,
	}, nil
}

func (c *CustomDevOpsAgent) monitorTCEDeployStatus(run *iris.AgentRunContext, deploymentID, env string, interval time.Duration) (*TCEStatusInfo, error) {
	logger := run.GetLogger()
	maxAttempts := 60 // Maximum monitoring attempts

	tceTools, err := c.getDevOpsAgentMCPTools(run, "tce_deploy")
	if err != nil {
		return nil, fmt.Errorf("failed to get TCE deploy tools: %v", err)
	}
	var statusTool iris.Action
	for _, tool := range tceTools {
		if tool.Name() == "mcp:tce_deploy_get_tce_deploy_status" ||
			tool.Name() == "get_tce_deploy_status" {
			statusTool = tool
			break
		}
	}

	if statusTool == nil {
		return nil, fmt.Errorf("get_tce_deploy_status tool not found")
	}

	for attempt := 0; attempt < maxAttempts; attempt++ {
		logger.Infof("Checking deploy status (attempt %d/%d) for deployment_id: %s, env: %s",
			attempt+1, maxAttempts, deploymentID, env)

		stepInputs := map[string]any{
			"deployment_id": deploymentID,
			"env":           env,
		}
		step := &iris.AgentRunStep{Inputs: stepInputs}

		startTime := time.Now()
		err = statusTool.Execute(run, step)
		duration := time.Since(startTime)
		status := "success"
		if err != nil {
			status = "failed"
			telemetry.EmitToolUsage(run, statusTool.Name(), status, duration)
			logger.Warnf("Status check failed: %v", err)
			time.Sleep(interval)
			continue
		}
		telemetry.EmitToolUsage(run, statusTool.Name(), status, duration)

		if step.Outputs != nil {
			jsonData, err := c.parseTCESingleJSON(step.Outputs)
			if err != nil {
				logger.Warnf("Failed to parse TCE JSON: %v", err)
				time.Sleep(interval)
				continue
			}

			statusInfo := c.extractTCEStatusInfoFromJSON(jsonData)
			logger.Infof("Deploy status info extracted: %+v", statusInfo)

			switch strings.ToLower(statusInfo.Status) {
			case "completed", "success", "succeeded", "deployed", "succeed":
				return statusInfo, nil
			case "failed", "error", "failure", "cancelled":
				return nil, fmt.Errorf("deployment task failed with status: %s", statusInfo.Status)
			case "running", "pending", "in_progress", "deploying":
				logger.Infof("Deploy task still running (status: %s), waiting %v", statusInfo.Status, interval)
				time.Sleep(interval)
				continue
			default:
				if statusInfo.Status != "" {
					logger.Infof("Deploy task status: %s, waiting %v", statusInfo.Status, interval)
				} else {
					logger.Infof("No clear status found, waiting %v", interval)
				}
				time.Sleep(interval)
				continue
			}
		} else {
			logger.Infof("No outputs received, waiting %v", interval)
			time.Sleep(interval)
		}
	}

	return nil, fmt.Errorf("deploy task monitoring timeout after %d attempts", maxAttempts)
}

func (c *CustomDevOpsAgent) extractTCEDeploymentIDFromJSON(jsonData map[string]interface{}) string {
	logger := c.run.GetLogger()
	if deploymentIDRaw, exists := jsonData["deployment_id"]; exists {
		if deploymentID, ok := deploymentIDRaw.(string); ok {
			return deploymentID
		}
	}

	logger.Warnf("No deployment_id found in TCE JSON data")
	return ""
}

func (c *CustomDevOpsAgent) extractTCEEnvFromJSON(jsonData map[string]interface{}) string {
	logger := c.run.GetLogger()
	if envRaw, exists := jsonData["env"]; exists {
		if env, ok := envRaw.(string); ok {
			return env
		}
	}

	logger.Warnf("No env found in TCE JSON data")
	return ""
}

func (c *CustomDevOpsAgent) extractTCEURLFromJSON(jsonData map[string]interface{}) string {
	logger := c.run.GetLogger()
	if urlRaw, exists := jsonData["url"]; exists {
		if rawURL, ok := urlRaw.(string); ok {
			return rawURL
		}
	}

	logger.Warnf("No url found in TCE JSON data")
	return ""
}

func (c *CustomDevOpsAgent) addOnesiteParams(rawURL string) string {
	logger := c.run.GetLogger()

	if rawURL == "" {
		return rawURL
	}
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		logger.Warnf("Failed to parse URL %s: %v", rawURL, err)
		return rawURL // Return original URL if parsing fails
	}

	if parsedURL == nil {
		logger.Warnf("Parsed URL is nil for: %s", rawURL)
		return rawURL
	}

	queryParams := parsedURL.Query()
	queryParams.Set("onesite_header_visible", "0")
	queryParams.Set("onesite_sider_visible", "0")
	parsedURL.RawQuery = queryParams.Encode()

	modifiedURL := parsedURL.String()
	return modifiedURL
}

func (c *CustomDevOpsAgent) extractTCEStatusInfoFromJSON(jsonData map[string]interface{}) *TCEStatusInfo {
	statusInfo := &TCEStatusInfo{}
	if statusRaw, exists := jsonData["status"]; exists {
		if status, ok := statusRaw.(string); ok {
			statusInfo.Status = status
		}
	}

	if messageRaw, exists := jsonData["message"]; exists {
		if message, ok := messageRaw.(string); ok {
			statusInfo.Message = message
		}
	}
	if startTimeRaw, ok := jsonData["start_time"]; ok {
		switch v := startTimeRaw.(type) {
		case int:
			statusInfo.StartTime = v
		case float64:
			statusInfo.StartTime = int(v)
		}
	}
	if elapseRaw, ok := jsonData["elapse"]; ok {
		switch v := elapseRaw.(type) {
		case int:
			statusInfo.Elapse = v
		case float64:
			statusInfo.Elapse = int(v)
		}
	}

	return statusInfo
}

func (c *CustomDevOpsAgent) extractTCEDeployResultFromContent(content string) (*TCEDeployResult, error) {
	if content == "" {
		return nil, fmt.Errorf("content is empty")
	}

	var deployResult TCEDeployResult
	if err := json.Unmarshal([]byte(content), &deployResult); err != nil {
		return nil, fmt.Errorf("failed to parse TCE deploy result JSON: %v", err)
	}

	if deployResult.TCEEnv == "" {
		return nil, fmt.Errorf("environment not found in TCE deploy result")
	}

	return &deployResult, nil
}

func (c *CustomDevOpsAgent) createTCEDeploy(run *iris.AgentRunContext, tceTools []iris.Action, psm, scmVersion, env, envType string, parentStepID string) (string, string, string, error) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"psm":         psm,
		"scm_version": scmVersion,
		"env":         env,
		"env_type":    envType,
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:tce_deploy"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting tce_deploy")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "finished tce_deploy")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting tce_deploy substep for psm: %s, scm_version: %s", psm, scmVersion)

	var tceDeployTool iris.Action
	for _, tool := range tceTools {
		if tool.Name() == "mcp:tce_deploy_tce_deploy" ||
			tool.Name() == "tce_deploy" {
			tceDeployTool = tool
			break
		}
	}

	if tceDeployTool == nil {
		substep.Outputs = map[string]any{
			"error":   "TCE deploy tool (tce_deploy) not found",
			"success": false,
		}
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", "", fmt.Errorf("TCE deploy tool (tce_deploy) not found")
	}

	stepInputs := map[string]any{
		"psm":         psm,
		"scm_version": scmVersion,
	}

	if env != "" {
		stepInputs["env"] = env
		logger.Infof("Calling tce_deploy with psm: %s, scm_version: %s, env: %s", psm, scmVersion, env)
	} else {
		if envType != "" {
			stepInputs["env_type"] = envType
			logger.Infof("Calling tce_deploy with psm: %s, scm_version: %s, env_type: %s (env will be auto-generated)", psm, scmVersion, envType)
		} else {
			logger.Infof("Calling tce_deploy with psm: %s, scm_version: %s (env will be auto-generated with default type)", psm, scmVersion)
		}
	}

	step := &iris.AgentRunStep{
		Inputs: stepInputs,
	}
	startTime := time.Now()
	err := tceDeployTool.Execute(run, step)
	duration := time.Since(startTime)
	status := "success"
	if err != nil {
		substep.Outputs = map[string]any{
			"error":   fmt.Sprintf("failed to create deploy task: %v", err),
			"success": false,
		}
		status = "failed"
		telemetry.EmitToolUsage(run, tceDeployTool.Name(), status, duration)
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", "", fmt.Errorf("failed to create deploy task: %v", err)
	}
	telemetry.EmitToolUsage(run, tceDeployTool.Name(), status, duration)

	jsonData, err := c.parseTCESingleJSON(step.Outputs)
	if err != nil {
		substep.Outputs = map[string]any{
			"error":   fmt.Sprintf("failed to parse TCE deploy output: %v", err),
			"success": false,
		}
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", "", fmt.Errorf("failed to parse TCE deploy output: %v", err)
	}

	deploymentID := c.extractTCEDeploymentIDFromJSON(jsonData)
	extractedEnv := c.extractTCEEnvFromJSON(jsonData)
	rawURL := c.extractTCEURLFromJSON(jsonData)

	if deploymentID == "" || extractedEnv == "" {
		substep.Outputs = map[string]any{
			"error":   "deploy task created but no deployment_id or env returned",
			"success": false,
		}
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", "", fmt.Errorf("deploy task created but no deployment_id or env returned")
	}

	logger.Infof("Deploy task created with deployment_id: %s, env: %s, url: %s", deploymentID, extractedEnv, rawURL)

	substep.Outputs = map[string]any{
		"tce_psm":           psm,
		"tce_version":       scmVersion,
		"tce_deployment_id": deploymentID,
		"tce_env":           extractedEnv,
		"tce_url":           c.addOnesiteParams(rawURL),
		"success":       true,
	}
	substep.Error = nil

	return deploymentID, extractedEnv, rawURL, nil
}

func (c *CustomDevOpsAgent) watchTCEDeployStatus(run *iris.AgentRunContext, psm, deploymentID, env string, interval time.Duration, parentStepID string) (*TCEStatusInfo, error) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"deployment_id": deploymentID,
		"env":           env,
		"interval":      interval.String(),
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:get_tce_deploy_status"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting get_tce_deploy_status monitoring")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "finished get_tce_deploy_status monitoring")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting get_tce_deploy_status monitoring substep for deployment: %s", deploymentID)

	statusInfo, err := c.monitorTCEDeployStatus(run, deploymentID, env, interval)
	if statusInfo != nil {
		substep.Outputs = map[string]any{
			"tce_psm":           psm,
			"tce_deployment_id": deploymentID,
			"tce_env":           env,
			"tce_status":        statusInfo.Status,
			"message":           statusInfo.Message,
			"tce_start_time":    statusInfo.StartTime,
			"tce_elapse":        statusInfo.Elapse,
		}
	} else {
		substep.Outputs = map[string]any{
			"tce_psm":           psm,
			"tce_deployment_id": deploymentID,
			"tce_env":           env,
			"tce_status":        "failed",
		}
	}

	if err != nil {
		substep.Outputs["error"] = err.Error()
		substep.Outputs["success"] = false
	} else {
		substep.Outputs["success"] = true
	}
	substep.Error = nil // Keep nil to prevent display logic skip

	return statusInfo, err
}

func (c *CustomDevOpsAgent) createTCEConclusionStep(run *iris.AgentRunContext, result TCEDeployResult, deployError error, parentStepID string) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"task": "tce_conclusion",
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:tce_tool_conclusion"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting TCE deploy conclusion")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "TCE deploy 结果汇总")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting TCE conclusion substep")

	substep.Outputs = map[string]any{
		"tce_status":        result.TCEStatus,
		"tce_psm":           result.TCEPSM,
		"tce_version":       result.TCEVersion,
		"tce_elapse":        result.TCEElapse,
		"tce_start_time":    result.TCEStartTime,
		"tce_env":           result.TCEEnv,
		"tce_url":           result.TCEURL,
		"tce_deployment_id": result.TCEDeploymentID,
		"url":               result.TCEURL,
		"conclusion":        fmt.Sprintf("TCE deployment process completed with status: %s", result.TCEStatus),
		"has_error":         deployError != nil,
	}
	if deployError != nil {
		substep.Outputs["error"] = deployError.Error()
	}
	substep.Error = nil // Keep nil to prevent display logic skip
}
