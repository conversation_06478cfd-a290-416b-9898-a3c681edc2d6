package devopsagenttool

import (
	"bytes"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/search/dal/llm"
)

// parseSCMSingleJSON parses the JSON string from text_contents[0] for SCM tools
func (c *CustomDevOpsAgent) parseSCMSingleJSON(outputs map[string]any) (map[string]interface{}, error) {

	if outputs == nil {
		return nil, fmt.Errorf("outputs is nil, original output: %#v", outputs)
	}

	textContentsRaw, ok := outputs["text_contents"]
	if !ok {
		return nil, fmt.Errorf("text_contents field not found in outputs, original output: %#v", outputs)
	}

	textContents, ok := textContentsRaw.([]string)
	if !ok {
		return nil, fmt.Errorf("text_contents is not []string, got: %T, original output: %#v", textContentsRaw, outputs)
	}

	if len(textContents) == 0 {
		return nil, fmt.Errorf("text_contents array is empty, original output: %#v", outputs)
	}

	jsonStr := textContents[0]

	var result map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return nil, fmt.Errorf("failed to parse SCM JSON string: %v, json_string: %s, original output: %#v", err, jsonStr, outputs)
	}
	return result, nil
}

// SCMStatusInfo represents the status information from get_scm_build_status
type SCMStatusInfo struct {
	Status    string `json:"status"`
	CommitSha string `json:"commit_sha"`
	Branch    string `json:"branch"`
	StartTime int    `json:"start_time"`
	Elapse    int    `json:"elapse"`
}

// BuildLogInfo holds both raw and extracted build log information
type BuildLogInfo struct {
	RawBuildLog      string `json:"raw_build_log"`
	ExtractedErrors  string `json:"extracted_errors"`
	GetBuildLogError error  `json:"get_build_log_error,omitempty"`
}

// SCMBuildResult represents the structured result from a SCM build operation
// following the required JSON schema
type SCMBuildResult struct {
	SCMVersion       string `json:"scm_version"`                // SCM版本号
	SCMName          string `json:"scm_name"`                   // SCM仓库名
	SCMURL           string `json:"scm_url"`                    // SCM任务链接
	SCMBranch        string `json:"scm_branch"`                 // SCM分支名
	SCMCommit        string `json:"scm_commit"`                 // SCM commit sha
	SCMStartTime     int    `json:"scm_start_time"`             // SCM任务开始时间戳
	SCMElapse        int    `json:"scm_elapse"`                 // SCM任务耗时/秒
	SCMStatus        string `json:"scm_status"`                 // SCM任务状态: success, fail, pending, cancelled
	RawBuildLog      string `json:"raw_build_log,omitempty"`    // 原始构建日志
	ExtractedErrors  string `json:"extracted_errors,omitempty"` // LLM提取的错误信息
	GetBuildLogError string `json:"get_build_log_error,omitempty"`
}

func (c *CustomDevOpsAgent) executeBuild(run *iris.AgentRunContext, args DevOpsAgentToolArgs, parentStepID string) (controltool.ConclusionOutput, error) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	if parentStepID == "" {
		return controltool.ConclusionOutput{}, fmt.Errorf("parentStepID must not be empty")
	}

	buildStep := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: c.name,
	})

	buildStep.Parent = parentStepID

	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(buildStep))

	buildStep.Status = iris.AgentRunStepStatusRunning
	buildStep.Inputs = map[string]any{
		"task":          "build",
		"codebase_repo": args.CodebaseRepo,
		"scm_repo":      args.SCMRepo,
		"branch":        args.Branch,
	}
	publisher.ReportStep(buildStep)

	buildStep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:scm_tool"}}
	publisher.ReportToolCall(buildStep, iris.ToolCallStatusStarted, "starting SCM build")

	defer func() {
		publisher.ReportToolCall(buildStep, iris.ToolCallStatusCompleted, "SCM build 结果汇总")
		buildStep.Finish(buildStep.Outputs, buildStep.Error)
		publisher.ReportStep(buildStep)
	}()

	logger.Infof("Starting SCM build for repo: %s, branch: %s", args.CodebaseRepo, args.Branch)

	// Get SCM build tools
	scmTools, err := c.getDevOpsAgentMCPTools(run, "scm_build")
	if err != nil {
		buildStep.Error = fmt.Errorf("failed to get SCM build tools: %v", err)
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Failed to get SCM build tools: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	// Determine SCM name (scm_repo) from input
	var scmName string
	if args.SCMRepo != "" {
		scmName = args.SCMRepo
		logger.Infof("Using provided SCMRepo: %s", scmName)
	} else if args.CodebaseRepo != "" {
		scmName, err = c.getSCMName(run, scmTools, args.CodebaseRepo, parentStepID)
		if err != nil {
			buildStep.Error = fmt.Errorf("failed to get SCM name from codebase repo: %v", err)
			return controltool.ConclusionOutput{
				Content:    fmt.Sprintf("Failed to get SCM name from codebase repo: %v", err),
				Evaluation: controltool.ConclusionEvaluationFailed,
			}, nil
		}
		logger.Infof("Resolved SCMRepo from CodebaseRepo: %s -> %s", args.CodebaseRepo, scmName)
	} else {
		buildStep.Error = fmt.Errorf("either codebase_repo or scm_repo must be provided")
		return controltool.ConclusionOutput{
			Content:    "Either codebase_repo or scm_repo must be provided",
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	scmVersion, scmURL, err := c.executeSCMBuild(run, scmTools, scmName, args.Branch, parentStepID)
	if err != nil {
		buildStep.Error = err
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Failed to create build task: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	statusInfo, buildLogInfo, err := c.monitorBuildStatus(run, scmName, args.Branch, scmURL, scmVersion, 15*time.Second, parentStepID)

	var finalStatus string
	var finalBranch, finalCommit string
	var finalStartTime, finalElapse int

	if statusInfo != nil {
		finalStatus = statusInfo.Status
		finalBranch = statusInfo.Branch
		finalCommit = statusInfo.CommitSha
		finalStartTime = statusInfo.StartTime
		finalElapse = statusInfo.Elapse
	} else {
		finalStatus = "failed"
	}

	scmBuildResult := SCMBuildResult{
		SCMVersion:   scmVersion,
		SCMName:      scmName,
		SCMURL:       scmURL,
		SCMBranch:    finalBranch,
		SCMCommit:    finalCommit,
		SCMStartTime: finalStartTime,
		SCMElapse:    finalElapse,
		SCMStatus:    finalStatus,
	}

	// Include build log information if available (especially for failed builds)
	if buildLogInfo != nil {
		if buildLogInfo.GetBuildLogError != nil {
			scmBuildResult.GetBuildLogError = buildLogInfo.GetBuildLogError.Error()
		} else if scmBuildResult.ExtractedErrors != "" {
			scmBuildResult.ExtractedErrors = buildLogInfo.ExtractedErrors
		} else if scmBuildResult.RawBuildLog != "" {
			scmBuildResult.RawBuildLog = buildLogInfo.RawBuildLog
		}
	}

	buildStep.Outputs = map[string]any{
		"scm_version":    scmVersion,
		"scm_name":       scmName,
		"scm_url":        scmURL,
		"scm_branch":     finalBranch,
		"scm_commit":     finalCommit,
		"scm_start_time": finalStartTime,
		"scm_elapse":     finalElapse,
		"scm_status":     finalStatus,
	}

	if err != nil {
		buildStep.Error = err
		c.createSCMConclusionStep(run, scmBuildResult, err, parentStepID)

		// Marshal the result to include build logs in the conclusion content
		var buffer bytes.Buffer
		encoder := json.NewEncoder(&buffer)
		encoder.SetEscapeHTML(false)
		marshalErr := encoder.Encode(scmBuildResult)
		var scmBuildResultJSON []byte
		if marshalErr != nil {
			logger.Errorf("Failed to marshal SCM build result: %v", marshalErr)
			scmBuildResultJSON = []byte(fmt.Sprintf(`{"error": "failed to marshal result: %s", "scm_status": "failed"}`, marshalErr.Error()))
		} else {
			scmBuildResultJSON = buffer.Bytes()
			// Remove trailing newline that Encode adds
			if len(scmBuildResultJSON) > 0 && scmBuildResultJSON[len(scmBuildResultJSON)-1] == '\n' {
				scmBuildResultJSON = scmBuildResultJSON[:len(scmBuildResultJSON)-1]
			}
		}

		return controltool.ConclusionOutput{
			Content:    string(scmBuildResultJSON),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	c.createSCMConclusionStep(run, scmBuildResult, nil, parentStepID)
	var buffer bytes.Buffer
	encoder := json.NewEncoder(&buffer)
	encoder.SetEscapeHTML(false)
	marshalErr := encoder.Encode(scmBuildResult)
	var scmBuildResultJSON []byte
	if marshalErr != nil {
		logger.Errorf("Failed to marshal SCM build result: %v", marshalErr)
		scmBuildResultJSON = []byte(fmt.Sprintf(`{"error": "failed to marshal result: %s", "scm_status": "failed"}`, marshalErr.Error()))
	} else {
		scmBuildResultJSON = buffer.Bytes()
		// Remove trailing newline that Encode adds
		if len(scmBuildResultJSON) > 0 && scmBuildResultJSON[len(scmBuildResultJSON)-1] == '\n' {
			scmBuildResultJSON = scmBuildResultJSON[:len(scmBuildResultJSON)-1]
		}
	}

	return controltool.ConclusionOutput{
		Content:    string(scmBuildResultJSON),
		Evaluation: controltool.ConclusionEvaluationSuccess,
	}, nil
}

func (c *CustomDevOpsAgent) extractNameFromJSON(jsonData map[string]interface{}) string {
	logger := c.run.GetLogger()
	if nameRaw, exists := jsonData["name"]; exists {
		if name, ok := nameRaw.(string); ok {
			return name
		}
	}

	logger.Warnf("No name found in SCM JSON data")
	return ""
}

func (c *CustomDevOpsAgent) extractURLFromJSON(jsonData map[string]interface{}) string {
	logger := c.run.GetLogger()
	if urlRaw, exists := jsonData["url"]; exists {
		if url, ok := urlRaw.(string); ok {
			return url
		}
	}

	logger.Warnf("No url found in SCM JSON data")
	return ""
}

func (c *CustomDevOpsAgent) extractVersionFromJSON(jsonData map[string]interface{}) string {
	logger := c.run.GetLogger()
	if versionRaw, exists := jsonData["version"]; exists {
		if version, ok := versionRaw.(string); ok {
			return version
		}
	}

	logger.Warnf("No version found in SCM JSON data")
	return ""
}

func (c *CustomDevOpsAgent) extractBuildLogFromOutput(outputs map[string]any) (string, error) {
	if outputs == nil {
		return "", fmt.Errorf("outputs is nil")
	}

	textContentsRaw, ok := outputs["text_contents"]
	if !ok {
		return "", fmt.Errorf("text_contents field not found in outputs")
	}

	textContents, ok := textContentsRaw.([]string)
	if !ok {
		return "", fmt.Errorf("text_contents is not []string, got: %T", textContentsRaw)
	}

	if len(textContents) == 0 {
		return "", fmt.Errorf("text_contents array is empty")
	}

	jsonStr := textContents[0]

	// Try to parse as JSON first (old format: {"log": "xxx"})
	var jsonData map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &jsonData)
	if err == nil {
		// Successfully parsed as JSON, extract log field
		if logRaw, exists := jsonData["log"]; exists {
			if rawLog, ok := logRaw.(string); ok {
				cleanLog := strings.ReplaceAll(rawLog, "\\n", "\n")
				cleanLog = strings.ReplaceAll(cleanLog, "\\t", "\t")
				cleanLog = strings.ReplaceAll(cleanLog, "\\u003e", ">")
				cleanLog = strings.ReplaceAll(cleanLog, "\\u0026", "&")
				return cleanLog, nil
			}
		}
		return "", nil
	}

	// Failed to parse as JSON, treat as raw log string (new format with raw=true)
	cleanLog := strings.ReplaceAll(jsonStr, "\\n", "\n")
	cleanLog = strings.ReplaceAll(cleanLog, "\\t", "\t")
	cleanLog = strings.ReplaceAll(cleanLog, "\\u003e", ">")
	cleanLog = strings.ReplaceAll(cleanLog, "\\u0026", "&")
	return cleanLog, nil
}

// extractBuildErrorsFromLog uses LLM to extract and analyze build error information from raw build logs
func (c *CustomDevOpsAgent) extractBuildErrorsFromLog(run *iris.AgentRunContext, rawBuildLog string) string {
	if rawBuildLog == "" {
		return ""
	}

	// If log is short enough, return as-is without LLM processing
	if len(rawBuildLog) <= 1000 {
		return rawBuildLog
	}

	logger := run.GetLogger()
	logger.Infof("Using LLM to extract build errors from %d character build log", len(rawBuildLog))

	// Construct messages for LLM
	messages := []*framework.ChatMessage{
		{
			Role: llm.RoleSystem,
			Content: `You are a build log analyzer. Your task is to extract and summarize the most important build error information from build logs.

Focus on:
1. Critical error messages and their causes
2. File paths and line numbers where errors occurred
3. Missing dependencies or compilation failures
4. First few errors (ignore repetitive errors)

Keep your response concise but informative. Include specific file paths and error messages when available.
If there are many similar errors, summarize them and mention the count.
Limit your response to the most critical 5-10 error lines with brief context.`,
		},
		{
			Role:    llm.RoleUser,
			Content: fmt.Sprintf("Please analyze this build log and extract the key error information:\n\n%s", rawBuildLog),
		},
	}

	// Call Think function
	result, err := agents.Think(run, "devops_agent_build_error_extraction", messages, agents.ThinkOption{
		StreamFilter: streamparser.StreamFilter{
			MaxCheckSize: 10,
			FlushSize:    1,
			AheadSize:    10,
			StartTokens:  []string{},
			EndTokens:    []string{},
			BreakTokens:  []string{},
		},
		DisableThinkDelta: true,
	})

	if err != nil {
		logger.Warnf("Failed to extract build errors using LLM: %v", err)
		// Fallback: return last 2000 characters if LLM fails (errors usually at end)
		if len(rawBuildLog) > 2000 {
			startIdx := len(rawBuildLog) - 2000
			return "... (truncated)\n" + rawBuildLog[startIdx:]
		}
		return rawBuildLog
	}

	logger.Infof("Successfully extracted build errors using LLM (model: %s)", result.Model)
	return result.Content
}

// extractSCMStatusInfoFromJSON extracts full status information from already-parsed JSON data
func (c *CustomDevOpsAgent) extractSCMStatusInfoFromJSON(jsonData map[string]interface{}) *SCMStatusInfo {

	statusInfo := &SCMStatusInfo{}

	if status, ok := jsonData["status"].(string); ok {
		statusInfo.Status = status
	}

	if commitSha, ok := jsonData["commit_sha"].(string); ok {
		statusInfo.CommitSha = commitSha
	}

	if branch, ok := jsonData["branch"].(string); ok {
		statusInfo.Branch = branch
	}
	if startTimeRaw, ok := jsonData["start_time"]; ok {
		switch v := startTimeRaw.(type) {
		case int:
			statusInfo.StartTime = v
		case float64:
			statusInfo.StartTime = int(v)
		}
	}
	if elapseRaw, ok := jsonData["elapse"]; ok {
		switch v := elapseRaw.(type) {
		case int:
			statusInfo.Elapse = v
		case float64:
			statusInfo.Elapse = int(v)
		}
	}

	return statusInfo
}

// getSCMBuildLog retrieves build log when build fails using get_scm_build_log tool
// Returns extracted error information or error if unavailable
func (c *CustomDevOpsAgent) getSCMBuildLog(run *iris.AgentRunContext, scmName, scmURL, scmVersion string) (string, error) {
	logger := run.GetLogger()

	// Get SCM build tools
	scmTools, err := c.getDevOpsAgentMCPTools(run, "scm_build")
	if err != nil {
		logger.Errorf("Failed to get SCM build tools for log retrieval: %v", err)
		return "", fmt.Errorf("failed to get SCM build tools: %w", err)
	}

	var buildLogTool iris.Action
	for _, tool := range scmTools {
		if tool.Name() == "mcp:scm_build_get_scm_build_log" || tool.Name() == "get_scm_build_log" {
			buildLogTool = tool
			logger.Infof("Found get_scm_build_log tool: %s", tool.Name())
			break
		}
	}

	if buildLogTool == nil {
		logger.Warnf("get_scm_build_log tool not found, cannot retrieve build logs")
		return "", fmt.Errorf("get_scm_build_log tool not found")
	}

	stepInputs := map[string]any{
		"scm_name":    scmName,
		"scm_version": scmVersion,
		"scm_url":     scmURL,
		"raw":         "true",
	}
	step := &iris.AgentRunStep{Inputs: stepInputs}

	logger.Infof("Build failed - retrieving build log for analysis: scm_name=%s, scm_version=%s, scm_url=%s", scmName, scmVersion, scmURL)

	startTime := time.Now()
	err = buildLogTool.Execute(run, step)
	duration := time.Since(startTime)
	status := "success"
	if err != nil {
		status = "failed"
		telemetry.EmitToolUsage(run, buildLogTool.Name(), status, duration)
		logger.Errorf("Failed to retrieve build log: %v", err)
		return "", fmt.Errorf("failed to execute build log tool: %w", err)
	}
	telemetry.EmitToolUsage(run, buildLogTool.Name(), status, duration)

	logger.Infof("Retrieved build log for failed build: scm_name=%s, scm_version=%s", scmName, scmVersion)
	buildLog, err := c.extractBuildLogFromOutput(step.Outputs)
	if err != nil {
		logger.Errorf("Failed to extract build log: %v", err)
		return "", fmt.Errorf("failed to extract build log: %w", err)
	}
	if buildLog != "" {
		logger.Errorf("")
		logger.Errorf("=== BUILD FAILURE LOG ===")
		logger.Errorf("%s", buildLog)
		logger.Errorf("=== END BUILD FAILURE LOG ===")
		logger.Errorf("")
		logger.Infof("Build log retrieval and parsing completed")
		return buildLog, nil
	} else {
		logger.Warnf("Could not extract build log from output")
		logger.Infof("Build log retrieval completed but parsing failed")
		return "", nil
	}
}

func (c *CustomDevOpsAgent) extractBuildResultFromContent(content string) (*SCMBuildResult, error) {
	if content == "" {
		return nil, fmt.Errorf("content is empty")
	}

	var scmBuildResult SCMBuildResult
	if err := json.Unmarshal([]byte(content), &scmBuildResult); err != nil {
		return nil, fmt.Errorf("failed to parse SCM build result JSON: %v", err)
	}

	if scmBuildResult.SCMVersion == "" {
		return nil, fmt.Errorf("SCM version not found in SCM build result")
	}

	return &scmBuildResult, nil
}

func (c *CustomDevOpsAgent) getSCMNameFromCodebase(run *iris.AgentRunContext, scmTools []iris.Action, codebaseRepo string) (string, error) {
	logger := run.GetLogger()

	// Validate that codebaseRepo has 2 parts (namespace/repo)
	parts := strings.Split(codebaseRepo, "/")
	if len(parts) != 2 {
		return "", fmt.Errorf("codebase_repo should have format {namespace}/{repo}, got: %s", codebaseRepo)
	}

	// Find get_scm_name tool
	var getSCMNameTool iris.Action
	for _, tool := range scmTools {
		if tool.Name() == "mcp:scm_build_get_scm_name" ||
			tool.Name() == "get_scm_name" {
			getSCMNameTool = tool
			logger.Infof("Found get_scm_name tool: %s", tool.Name())
			break
		}
	}

	if getSCMNameTool == nil {
		return "", fmt.Errorf("get_scm_name tool not found")
	}

	// Call get_scm_name with codebase_repo
	step := &iris.AgentRunStep{
		Inputs: map[string]any{
			"codebase_repo": codebaseRepo,
		},
	}

	logger.Infof("Calling get_scm_name with codebase_repo: %s", codebaseRepo)
	startTime := time.Now()
	err := getSCMNameTool.Execute(run, step)
	duration := time.Since(startTime)
	status := "success"
	if err != nil {
		status = "failed"
		telemetry.EmitToolUsage(run, getSCMNameTool.Name(), status, duration)
		return "", fmt.Errorf("failed to get SCM name: %v", err)
	}
	telemetry.EmitToolUsage(run, getSCMNameTool.Name(), status, duration)

	jsonData, err := c.parseSCMSingleJSON(step.Outputs)
	if err != nil {
		return "", fmt.Errorf("failed to parse get_scm_name JSON output: %v", err)
	}

	scmName := c.extractNameFromJSON(jsonData)
	if scmName == "" {
		return "", fmt.Errorf("no valid SCM name found in get_scm_name output: %#v", jsonData)
	}

	scmParts := strings.Split(scmName, "/")
	if len(scmParts) != 3 || scmParts[0] == "" || scmParts[1] == "" || scmParts[2] == "" {
		return "", fmt.Errorf("invalid SCM name format: %s (expected format: {namespace}/{group}/{repo})", scmName)
	}

	logger.Infof("Successfully extracted SCM name: %s", scmName)
	return scmName, nil
}

// getSCMName wraps getSCMNameFromCodebase with substep creation following apitest.go pattern
func (c *CustomDevOpsAgent) getSCMName(run *iris.AgentRunContext, scmTools []iris.Action, codebaseRepo string, parentStepID string) (string, error) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"codebase_repo": codebaseRepo,
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:get_scm_name"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting get_scm_name")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "finished get_scm_name")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting get_scm_name substep for codebase_repo: %s", codebaseRepo)

	scmName, err := c.getSCMNameFromCodebase(run, scmTools, codebaseRepo)
	substep.Outputs = map[string]any{
		"scm_name": scmName,
	}
	if err != nil {
		substep.Outputs["error"] = err.Error()
		substep.Outputs["success"] = false
	} else {
		substep.Outputs["success"] = true
	}
	substep.Error = nil // Keep nil to prevent display logic skip

	return scmName, err
}

// executeSCMBuild wraps SCM build execution with substep creation
func (c *CustomDevOpsAgent) executeSCMBuild(run *iris.AgentRunContext, scmTools []iris.Action, scmName, branch string, parentStepID string) (string, string, error) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"scm_name": scmName,
		"branch":   branch,
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:scm_build"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting scm_build")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "finished scm_build")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting scm_build substep for scm_name: %s, branch: %s", scmName, branch)

	var scmBuildTool iris.Action
	for _, tool := range scmTools {
		if tool.Name() == "mcp:scm_build_scm_build" ||
			tool.Name() == "scm_build" {
			scmBuildTool = tool
			logger.Infof("Found scm_build tool: %s", tool.Name())
			break
		}
	}

	if scmBuildTool == nil {
		substep.Outputs = map[string]any{
			"error":   "SCM build tool not found",
			"success": false,
		}
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", fmt.Errorf("SCM build tool not found")
	}

	step := &iris.AgentRunStep{
		Inputs: map[string]any{
			"scm_name": scmName,
			"branch":   branch,
		},
	}

	logger.Infof("Calling scm_build with scm_name: %s, branch: %s", scmName, branch)
	startTime := time.Now()
	err := scmBuildTool.Execute(run, step)
	duration := time.Since(startTime)
	status := "success"
	if err != nil {
		substep.Outputs = map[string]any{
			"error":   fmt.Sprintf("failed to create build task: %v", err),
			"success": false,
		}
		status = "failed"
		telemetry.EmitToolUsage(run, scmBuildTool.Name(), status, duration)
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", fmt.Errorf("failed to create build task: %v", err)
	}
	telemetry.EmitToolUsage(run, scmBuildTool.Name(), status, duration)

	jsonData, err := c.parseSCMSingleJSON(step.Outputs)
	if err != nil {
		substep.Outputs = map[string]any{
			"error":   fmt.Sprintf("failed to parse SCM build output: %v", err),
			"success": false,
		}
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", fmt.Errorf("failed to parse SCM build output: %v", err)
	}

	scmVersion := c.extractVersionFromJSON(jsonData)
	scmURL := c.extractURLFromJSON(jsonData)
	if scmVersion == "" || scmURL == "" {
		substep.Outputs = map[string]any{
			"error":   "failed to extract SCM version or url",
			"success": false,
		}
		substep.Error = nil // Keep nil to prevent display logic skip
		return "", "", fmt.Errorf("failed to extract SCM version or url")
	}

	// Set substep fields directly
	substep.Outputs = map[string]any{
		"scm_name":    scmName,
		"scm_branch":  branch,
		"scm_version": scmVersion,
		"scm_url":     scmURL,
		"success":     true,
	}
	substep.Error = nil

	return scmVersion, scmURL, nil
}

func (c *CustomDevOpsAgent) monitorBuildStatus(run *iris.AgentRunContext, scmName, branch, scmURL, scmVersion string, interval time.Duration, parentStepID string) (*SCMStatusInfo, *BuildLogInfo, error) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"scm_name":    scmName,
		"scm_url":     scmURL,
		"scm_version": scmVersion,
		"interval":    interval.String(),
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:get_scm_build_status"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting get_scm_build_status monitoring")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "finished get_scm_build_status monitoring")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting get_scm_build_status monitoring substep for scm: %s", scmName)

	statusInfo, buildLogInfo, err := c.monitorBuildStatusWithLog(run, scmName, branch, scmURL, scmVersion, 15*time.Second, parentStepID)
	if statusInfo != nil {
		substep.Outputs = map[string]any{
			"scm_name":       scmName,
			"scm_url":        scmURL,
			"scm_version":    scmVersion,
			"scm_status":     statusInfo.Status,
			"scm_commit":     statusInfo.CommitSha,
			"scm_branch":     statusInfo.Branch,
			"scm_start_time": statusInfo.StartTime,
			"scm_elapse":     statusInfo.Elapse,
		}
	} else {
		substep.Outputs = map[string]any{
			"scm_name":    scmName,
			"scm_url":     scmURL,
			"scm_version": scmVersion,
			"scm_branch":  branch,
			"scm_status":  "failed",
		}
	}

	// Add build log information if available
	if buildLogInfo != nil {
		if buildLogInfo.GetBuildLogError != nil {
			substep.Outputs["build_log"] = buildLogInfo.GetBuildLogError.Error()
		} else {
			if buildLogInfo.ExtractedErrors != "" {
				substep.Outputs["extracted_errors"] = buildLogInfo.ExtractedErrors
				substep.Outputs["has_extraction"] = true
			} else {
				if buildLogInfo.RawBuildLog != "" {
					// Truncate RawBuildLog to last 8k characters if too long
					buildLog := buildLogInfo.RawBuildLog
					if len(buildLog) > 8192 {
						buildLog = "...(truncated)\n" + buildLog[len(buildLog)-8192:]
					}
					substep.Outputs["build_log"] = buildLog
					substep.Outputs["has_log"] = true
				}
			}
		}
	}

	if err != nil {
		substep.Outputs["error"] = err.Error()
		substep.Outputs["success"] = false
	} else {
		substep.Outputs["success"] = true
	}
	substep.Error = nil // Keep nil to prevent display logic skip

	return statusInfo, buildLogInfo, err
}

// getBuildLog wraps getSCMBuildLog with substep creation and returns BuildLogInfo
func (c *CustomDevOpsAgent) getBuildLog(run *iris.AgentRunContext, scmName, branch, scmURL, scmVersion string, parentStepID string) *BuildLogInfo {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	// Create substep for get_scm_build_log
	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"scm_name":    scmName,
		"scm_url":     scmURL,
		"scm_version": scmVersion,
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:get_scm_build_log"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting get_scm_build_log")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "finished get_scm_build_log")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting get_scm_build_log substep for failed build: %s", scmName)

	// Execute the actual operation
	buildLog, err := c.getSCMBuildLog(run, scmName, scmURL, scmVersion)
	if err != nil {
		logger.Errorf("Failed to get SCM build log: %v", err)
	}

	// Extract errors using LLM if build log is available
	var extractedErrors string
	if buildLog != "" {
		extractedErrors = c.extractBuildErrorsFromLog(run, buildLog)
		logger.Infof("Build error extraction completed - original: %d chars, extracted: %d chars",
			len(buildLog), len(extractedErrors))
	}

	// Create BuildLogInfo struct to return
	buildLogInfo := &BuildLogInfo{
		RawBuildLog:     buildLog,
		ExtractedErrors: extractedErrors,
	}

	// Set substep fields directly with both original and extracted logs
	substep.Outputs = map[string]any{
		"scm_name":         scmName,
		"scm_url":          scmURL,
		"scm_version":      scmVersion,
		"scm_branch":       branch,
		"build_log":        buildLog,
		"extracted_errors": extractedErrors,
		"has_log":          buildLog != "",
		"has_extraction":   extractedErrors != "",
	}
	if err != nil {
		buildLogInfo.GetBuildLogError = err
		substep.Outputs["build_log"] = err.Error()
		substep.Outputs["success"] = false
	} else {
		substep.Outputs["success"] = true
	}
	if substep.Outputs["build_log"] == "" && err != nil {
		substep.Outputs["error"] = "could not retrieve build log"
	}
	substep.Error = nil // Keep nil to prevent display logic skip

	return buildLogInfo
}

func (c *CustomDevOpsAgent) monitorBuildStatusWithLog(run *iris.AgentRunContext, scmName, branch, scmURL, scmVersion string, interval time.Duration, parentStepID string) (*SCMStatusInfo, *BuildLogInfo, error) {
	logger := run.GetLogger()
	maxAttempts := 240 // Maximum monitoring attempts

	// Get SCM build tools
	scmTools, err := c.getDevOpsAgentMCPTools(run, "scm_build")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get SCM build tools: %v", err)
	}

	// Find get_scm_build_status tool
	var statusTool iris.Action
	for _, tool := range scmTools {
		if tool.Name() == "mcp:scm_build_get_scm_build_status" || tool.Name() == "get_scm_build_status" {
			statusTool = tool
			logger.Infof("Found get_scm_build_status tool: %s", tool.Name())
			break
		}
	}

	if statusTool == nil {
		return nil, nil, fmt.Errorf("get_scm_build_status tool not found")
	}

	for attempt := 0; attempt < maxAttempts; attempt++ {
		logger.Infof("Checking build status (attempt %d/%d) for SCM: %s", attempt+1, maxAttempts, scmName)

		// Prepare inputs required by get_scm_build_status
		stepInputs := map[string]any{
			"scm_name":    scmName,
			"scm_version": scmVersion,
			"scm_url":     scmURL,
		}
		step := &iris.AgentRunStep{Inputs: stepInputs}

		startTime := time.Now()
		err = statusTool.Execute(run, step)
		duration := time.Since(startTime)
		status := "success"
		if err != nil {
			status = "failed"
			telemetry.EmitToolUsage(run, statusTool.Name(), status, duration)
			logger.Warnf("Status check failed: %v", err)
			time.Sleep(interval)
			continue
		}
		telemetry.EmitToolUsage(run, statusTool.Name(), status, duration)

		if step.Outputs != nil {
			// Parse JSON once for this iteration
			jsonData, err := c.parseSCMSingleJSON(step.Outputs)
			if err != nil {
				logger.Warnf("Failed to parse SCM JSON: %v", err)
				time.Sleep(interval)
				continue
			}

			// Extract full status information from parsed JSON
			statusInfo := c.extractSCMStatusInfoFromJSON(jsonData)
			logger.Infof("Build status info extracted: %+v", statusInfo)

			switch strings.ToLower(statusInfo.Status) {
			case "completed", "success", "succeeded":
				return statusInfo, nil, nil
			case "failed", "error", "fail":
				logger.Errorf("Build task failed with status: %s, retrieving build log", statusInfo.Status)
				buildLogInfo := c.getBuildLog(run, scmName, branch, scmURL, scmVersion, parentStepID)
				errorMsg := fmt.Sprintf("build task failed with status: %s", statusInfo.Status)
				return nil, buildLogInfo, errors.New(errorMsg)
			case "running", "pending", "in_progress":
				logger.Infof("Build task still running (status: %s), waiting %v", statusInfo.Status, interval)
				time.Sleep(interval)
				continue
			default:
				if statusInfo.Status != "" {
					logger.Infof("Build task status: %s, waiting %v", statusInfo.Status, interval)
				} else {
					logger.Infof("No clear status found, waiting %v", interval)
				}
				time.Sleep(interval)
				continue
			}
		} else {
			logger.Infof("No outputs received, waiting %v", interval)
			time.Sleep(interval)
		}

		time.Sleep(interval)
	}

	return nil, nil, fmt.Errorf("build task monitoring timeout after %d attempts", maxAttempts)
}

// createSCMConclusionStep creates a conclusion step that runs after all SCM substeps are complete
func (c *CustomDevOpsAgent) createSCMConclusionStep(run *iris.AgentRunContext, result SCMBuildResult, buildError error, parentStepID string) {
	logger := run.GetLogger()
	publisher := run.GetPublisher()

	// Create substep for SCM conclusion
	substep := run.CreateStep(&iris.CreateStepOption{ExecutorAgent: c.name})
	substep.Parent = parentStepID
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(substep))
	substep.Status = iris.AgentRunStepStatusRunning
	substep.Inputs = map[string]any{
		"task": "scm_conclusion",
	}
	publisher.ReportStep(substep)

	substep.Action = &actions.Tool{ActionInfo: iris.ActionInfo{ActionName: "devops_agent:scm_tool_conclusion"}}
	publisher.ReportToolCall(substep, iris.ToolCallStatusStarted, "starting SCM build conclusion")
	defer func() {
		publisher.ReportToolCall(substep, iris.ToolCallStatusCompleted, "SCM build 结果汇总")
		substep.Finish(substep.Outputs, substep.Error)
		publisher.ReportStep(substep)
	}()

	logger.Infof("Starting SCM conclusion substep")

	// Set substep fields directly with complete SCM result
	substep.Outputs = map[string]any{
		"scm_version":    result.SCMVersion,
		"scm_name":       result.SCMName,
		"scm_url":        result.SCMURL,
		"scm_branch":     result.SCMBranch,
		"scm_commit":     result.SCMCommit,
		"scm_start_time": result.SCMStartTime,
		"scm_elapse":     result.SCMElapse,
		"scm_status":     result.SCMStatus,
		"conclusion":     fmt.Sprintf("SCM build process completed with status: %s", result.SCMStatus),
		"has_error":      buildError != nil,
	}
	if buildError != nil {
		substep.Outputs["error"] = buildError.Error()
	}
	substep.Error = nil // Keep nil to prevent display logic skip
}
