package devopsagenttool

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"sync"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
)

const (
	TaskBuild          = "build"
	TaskDeploy         = "deploy"
	TaskBuildAndDeploy = "build_and_deploy"
	TaskAPITest        = "api_test"
	TaskFullWorkflow   = "full_workflow"
)

const (
	EnvTypePPE = "ppe"
	EnvTypeBOE = "boe"
)

type DevOpsAgentToolArgs struct {
	Task         string      `json:"task" mapstructure:"task" description:"The development task to perform: build, deploy, build_and_deploy, api_test, or full_workflow"`
	CodebaseRepo string      `json:"codebase_repo" mapstructure:"codebase_repo" description:"The codebase repository in format {namespace}/{repo}, e.g., cld/bits-ai-showcase"`
	SCMRepo      string      `json:"scm_repo" mapstructure:"scm_repo" description:"The SCM repository in format {a}/{b}/{c}, e.g., bits/ai/showcase"`
	Branch       string      `json:"branch" mapstructure:"branch" description:"The branch to build from (default: master)"`
	PSM          string      `json:"psm" mapstructure:"psm" description:"The PSM (Platform Service Management) identifier for both TCE deployment and API testing"`
	SCMVersion   string      `json:"scm_version" mapstructure:"scm_version" description:"The SCM version for deployment (only required when using deploy alone)"`
	Env          string      `json:"env" mapstructure:"env" description:"Environment/lane for both TCE deployment and API testing. Optional for TCE deploy (generates random ppe_/boe_ if empty). Required for standalone API testing"`
	EnvType      string      `json:"env_type" mapstructure:"env_type" description:"Environment type for TCE deployment when env is not provided. 'ppe' generates ppe_xxx, 'boe' generates boe_xxx. Ignored if env is provided"`
	APITargets   []APITarget `json:"api_targets" mapstructure:"api_targets" description:"List of API targets to test. Leave empty to test all APIs in the PSM"`
	IsOnline     bool        `json:"is_online,omitempty" mapstructure:"is_online" description:"Whether to use online environment. Used for API testing and to determine env_type when both env and env_type are not provided. true=ppe, false=boe. Default is false"`
}

type APITarget struct {
	FuncName   *string `json:"func_name,omitempty" mapstructure:"func_name" description:"Function name for testing"`
	HTTPPath   *string `json:"http_path,omitempty" mapstructure:"http_path" description:"HTTP path for testing"`
	HTTPMethod *string `json:"http_method,omitempty" mapstructure:"http_method" description:"HTTP method for testing"`
}

// validateAndNormalizeArgs validates and normalizes the arguments, including env validation and IsOnline inference
func (c *CustomDevOpsAgent) validateAndNormalizeArgs(args *DevOpsAgentToolArgs) error {
	// Validate task type
	taskType := strings.ToLower(args.Task)
	validTasks := map[string]bool{
		TaskBuild:          true,
		TaskDeploy:         true,
		TaskBuildAndDeploy: true,
		TaskAPITest:        true,
		TaskFullWorkflow:   true,
	}

	if !validTasks[taskType] {
		return fmt.Errorf("invalid task type: %s. Supported tasks: build, deploy, build_and_deploy, api_test, full_workflow", args.Task)
	}

	// Task-specific validations
	switch taskType {
	case TaskBuild, TaskBuildAndDeploy, TaskFullWorkflow:
		if args.CodebaseRepo == "" && args.SCMRepo == "" {
			return fmt.Errorf("either codebase_repo or scm_repo must be provided for %s task", args.Task)
		}
	case TaskDeploy:
		if args.PSM == "" {
			return fmt.Errorf("psm is required for deploy task")
		}
		if args.SCMVersion == "" {
			return fmt.Errorf("scm_version is required for standalone deploy task")
		}
	case TaskAPITest:
		if args.PSM == "" {
			return fmt.Errorf("psm is required for api_test task")
		}
	}

	// Validate env parameter if provided
	if args.Env != "" {
		if !strings.HasPrefix(args.Env, "ppe_") && !strings.HasPrefix(args.Env, "boe_") {
			return fmt.Errorf("env parameter must start with 'ppe_' or 'boe_', got: %s", args.Env)
		}
	}

	// Validate env_type parameter if provided
	if args.EnvType != "" && args.EnvType != EnvTypePPE && args.EnvType != EnvTypeBOE {
		return fmt.Errorf("env_type parameter must be 'ppe' or 'boe', got: %s", args.EnvType)
	}

	// Validate API targets if provided for API testing
	if (taskType == TaskAPITest || taskType == TaskFullWorkflow) && len(args.APITargets) > 0 {
		for i, target := range args.APITargets {
			if (target.FuncName == nil || *target.FuncName == "") &&
				(target.HTTPPath == nil || *target.HTTPPath == "") {
				return fmt.Errorf("api_targets[%d] must have either func_name or http_path specified", i)
			}

			// If HTTP path is provided, HTTP method should also be provided
			if target.HTTPPath != nil && *target.HTTPPath != "" &&
				(target.HTTPMethod == nil || *target.HTTPMethod == "") {
				return fmt.Errorf("api_targets[%d] has http_path but missing http_method", i)
			}
		}
	}

	// Handle bidirectional relationship between IsOnline and env/env_type
	if args.Env != "" {
		// If env is provided, infer IsOnline from env prefix
		args.IsOnline = strings.HasPrefix(args.Env, "ppe_")
	} else if args.EnvType != "" {
		// If env_type is provided, infer IsOnline from env_type
		args.IsOnline = (args.EnvType == EnvTypePPE)
	} else {
		// If neither env nor env_type is provided, use IsOnline to set env_type
		// This allows IsOnline to control environment generation for all tasks
		if args.IsOnline {
			args.EnvType = EnvTypePPE
		} else {
			args.EnvType = EnvTypeBOE
		}
	}

	return nil
}

// CustomDevOpsAgent implements a custom agent that doesn't use ProgreActAgent
type CustomDevOpsAgent struct {
	name        string
	description string
	run         *iris.AgentRunContext

	// Cache for expensive operations
	userQueryCache     string
	userQueryCacheErr  error
	userQueryCacheLock sync.Mutex
}

// getOriginalUserQuery retrieves the original user query from AgentRunContext with caching
func (c *CustomDevOpsAgent) getOriginalUserQuery() (string, error) {
	// Check cache first
	c.userQueryCacheLock.Lock()
	defer c.userQueryCacheLock.Unlock()

	if c.userQueryCache != "" || c.userQueryCacheErr != nil {
		return c.userQueryCache, c.userQueryCacheErr
	}

	// Not in cache, retrieve it
	if c.run == nil {
		c.userQueryCacheErr = fmt.Errorf("agent run context is nil")
		return "", c.userQueryCacheErr
	}

	if c.run.CtxStorage == nil {
		c.userQueryCacheErr = fmt.Errorf("context storage is nil")
		return "", c.userQueryCacheErr
	}

	// Recover the RunAgentRequest from context storage
	requestData, exist, err := c.run.CtxStorage.Recover(context.Background(), iris.StorageTypeRunAgentRequest)
	if err != nil {
		c.userQueryCacheErr = fmt.Errorf("failed to recover run agent request: %w", err)
		return "", c.userQueryCacheErr
	}
	if !exist {
		c.userQueryCacheErr = fmt.Errorf("run agent request not found in storage")
		return "", c.userQueryCacheErr
	}

	// Type assertion to get the RunAgentRequest
	req, ok := requestData.(*entity.RunAgentRequest)
	if !ok {
		c.userQueryCacheErr = fmt.Errorf("failed to assert run agent request type")
		return "", c.userQueryCacheErr
	}

	// Get the original user query
	if req == nil {
		c.userQueryCacheErr = fmt.Errorf("run agent request is nil")
		return "", c.userQueryCacheErr
	}

	if req.Input == nil {
		c.userQueryCacheErr = fmt.Errorf("input message is nil")
		return "", c.userQueryCacheErr
	}

	userQuery := req.Input.Content.Content
	if userQuery == "" {
		c.userQueryCacheErr = fmt.Errorf("user query content is empty")
		return "", c.userQueryCacheErr
	}

	// Cache the result
	c.userQueryCache = userQuery
	c.run.GetLogger().Infof("userQuery from context storage: %s", userQuery)

	return userQuery, nil
}

// shouldRunTask determines if the task should run based on platform routing
func (c *CustomDevOpsAgent) shouldRunTask(run *iris.AgentRunContext, taskType string) error {
	// Normalize task type for comparison
	taskType = strings.ToLower(taskType)

	// Skip platform routing for API testing (it's a separate concern)
	if taskType == TaskAPITest {
		return nil
	}

	userQuery, err := c.getOriginalUserQuery()
	if err != nil {
		return fmt.Errorf("cannot access original user query: %w", err)
	}

	// Handle different task types
	switch taskType {
	case TaskBuild:
		return c.shouldRunBuildTask(run, userQuery)
	case TaskDeploy:
		return c.shouldRunDeployTask(run, userQuery)
	case TaskBuildAndDeploy, TaskFullWorkflow:
		return c.shouldRunCombinedTask(run, userQuery, taskType)
	default:
		// Allow other task types by default
		return nil
	}
}

// shouldRunBuildTask checks if build task should run
func (c *CustomDevOpsAgent) shouldRunBuildTask(run *iris.AgentRunContext, userQuery string) error {
	selector, err := SelectBuildPlatform(run, userQuery)
	if err != nil {
		return fmt.Errorf("failed to select build platform: %w", err)
	}

	if !selector.ShouldRoute {
		return fmt.Errorf("build platform routing decision: %s. The devops agent will not proceed. Please use appropriate tools or local build methods.", selector.Reason)
	}

	run.GetLogger().Infof("Build platform routing: %s (%s)", selector.Platform, selector.Reason)
	return nil
}

// shouldRunDeployTask checks if deploy task should run
func (c *CustomDevOpsAgent) shouldRunDeployTask(run *iris.AgentRunContext, userQuery string) error {
	selector, err := SelectDeployPlatform(run, userQuery)
	if err != nil {
		return fmt.Errorf("failed to select deploy platform: %w", err)
	}

	if !selector.ShouldRoute {
		return fmt.Errorf("deploy platform routing decision: %s. The devops agent will not proceed. Please use appropriate tools or local deployment methods.", selector.Reason)
	}

	run.GetLogger().Infof("Deploy platform routing: %s (%s)", selector.Platform, selector.Reason)
	return nil
}

// shouldRunCombinedTask checks if combined task should run
func (c *CustomDevOpsAgent) shouldRunCombinedTask(run *iris.AgentRunContext, userQuery string, taskType string) error {
	// For combined workflows, check both platforms
	buildSelector, buildErr := SelectBuildPlatform(run, userQuery)
	if buildErr != nil {
		return fmt.Errorf("failed to select build platform: %w", buildErr)
	}

	deploySelector, deployErr := SelectDeployPlatform(run, userQuery)
	if deployErr != nil {
		return fmt.Errorf("failed to select deploy platform: %w", deployErr)
	}

	// Both must route to our platforms for combined workflow
	if !buildSelector.ShouldRoute {
		return fmt.Errorf("build platform routing decision: %s. The devops agent will not proceed. Please use appropriate tools or local build methods.", buildSelector.Reason)
	}

	if !deploySelector.ShouldRoute {
		return fmt.Errorf("deploy platform routing decision: %s. The devops agent will not proceed. Please use appropriate tools or local deployment methods.", deploySelector.Reason)
	}

	run.GetLogger().Infof("Combined task routing: build=%s, deploy=%s", buildSelector.Platform, deploySelector.Platform)
	return nil
}

var (
	validProviderMap = map[string]bool{
		"scm_build":  true,
		"tce_deploy": true,
		"api_test":   true,
		"api_doc":    true,
	}
)

// getDevOpsAgentMCPTools returns tools from a specific devops agent MCP provider
func (c *CustomDevOpsAgent) getDevOpsAgentMCPTools(run *iris.AgentRunContext, providerID string) ([]iris.Action, error) {
	// Check if the provider ID is valid for devops agent using map lookup (O(1))
	if !validProviderMap[providerID] {
		return nil, fmt.Errorf("devops agent MCP provider '%s' not found. Valid providers: scm_build, tce_deploy, api_test, api_doc", providerID)
	}

	// Use the dedicated DevOpsAgentProviderRegistry instead of the global registry
	tools, err := mcptool.DevOpsAgentProviderRegistry.ListTools(run, providerID)
	if err != nil {
		return nil, fmt.Errorf("failed to list tools for provider '%s': %w", providerID, err)
	}

	if len(tools) == 0 {
		run.GetLogger().Warnf("No tools found for provider '%s'", providerID)
	}

	return tools, nil
}

func (c *CustomDevOpsAgent) Name() string {
	return c.name
}

func (c *CustomDevOpsAgent) Description() string {
	return c.description
}

func (c *CustomDevOpsAgent) InputSpec() map[string]any {
	schema := util.TypeToJSONSchema(reflect.TypeOf(DevOpsAgentToolArgs{}))
	return map[string]any{
		"type":       schema.Type,
		"properties": schema.Properties,
		"required":   schema.Required,
	}
}

func (c *CustomDevOpsAgent) OutputSpec() map[string]any {
	schema := util.TypeToJSONSchema(reflect.TypeOf(controltool.ConclusionOutput{}))
	return map[string]any{
		"type":       schema.Type,
		"properties": schema.Properties,
		"required":   schema.Required,
	}
}

func (c *CustomDevOpsAgent) Execute(run *iris.AgentRunContext, step *iris.AgentRunStep) error {
	// Set the run context for this agent instance
	c.run = run

	// Parse input arguments
	var args DevOpsAgentToolArgs
	if step == nil {
		return iris.NewRecoverable(fmt.Errorf("agent run step is nil"))
	}

	if step.Inputs == nil {
		return iris.NewRecoverable(fmt.Errorf("agent run step inputs are nil"))
	}

	jsonStr, err := json.Marshal(step.Inputs)
	if err != nil {
		return iris.NewRecoverable(fmt.Errorf("failed to marshal devops agent arguments: %w", err))
	}

	err = json.Unmarshal(jsonStr, &args)
	if err != nil {
		return iris.NewRecoverable(fmt.Errorf("failed to parse devops agent arguments: %w", err))
	}

	// Check user intent via LLM analysis
	err = c.shouldRunTask(run, args.Task)
	if err != nil {
		return iris.NewRecoverable(err)
	}

	// Validate and normalize arguments
	err = c.validateAndNormalizeArgs(&args)
	if err != nil {
		return iris.NewRecoverable(fmt.Errorf("invalid arguments: %w", err))
	}

	// Execute the custom logic based on task
	result, err := c.executeCustomLogic(run, args, step.StepID)
	if err != nil {
		return iris.NewRecoverable(fmt.Errorf("execution error: %w", err))
	}

	// Set outputs
	outputs := make(map[string]any)
	data, _ := json.Marshal(result)
	unmarshalErr := json.Unmarshal(data, &outputs)
	if unmarshalErr != nil {
		run.GetLogger().Error("failed to unmarshal devops agent output", "error", unmarshalErr)
	}
	step.Outputs = outputs
	return nil
}

func (c *CustomDevOpsAgent) executeCustomLogic(run *iris.AgentRunContext, args DevOpsAgentToolArgs, parentStepID string) (controltool.ConclusionOutput, error) {
	logger := run.GetLogger()
	taskType := strings.ToLower(args.Task)

	switch taskType {
	case TaskBuild:
		return c.executeBuild(run, args, parentStepID)
	case TaskDeploy:
		return c.executeDeploy(run, args, parentStepID)
	case TaskBuildAndDeploy:
		return c.executeBuildAndDeploy(run, args, parentStepID)
	case TaskAPITest:
		return c.executeAPITest(run, args, parentStepID)
	case TaskFullWorkflow:
		return c.executeFullWorkflow(run, args, parentStepID)
	default:
		logger.Errorf("Unknown task type: %s", args.Task)
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Unknown task type: %s. Supported tasks: build, deploy, build_and_deploy, api_test, full_workflow", args.Task),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}
}

func (c *CustomDevOpsAgent) executeDeploy(run *iris.AgentRunContext, args DevOpsAgentToolArgs, parentStepID string) (controltool.ConclusionOutput, error) {
	if args.SCMVersion == "" {
		return controltool.ConclusionOutput{
			Content:    "SCM version is required for standalone deployment",
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	logger := run.GetLogger()
	logger.Infof("Starting TCE deployment for PSM: %s, SCM Version: %s", args.PSM, args.SCMVersion)

	return c.executeTCEDeploy(run, args.PSM, args.SCMVersion, args.Env, args.EnvType, parentStepID)
}

func (c *CustomDevOpsAgent) executeBuildAndDeploy(run *iris.AgentRunContext, args DevOpsAgentToolArgs, parentStepID string) (controltool.ConclusionOutput, error) {
	logger := run.GetLogger()
	logger.Infof("Starting build and deploy workflow")

	// Phase 1: Build
	buildResult, err := c.executeBuild(run, args, parentStepID)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Build phase failed with error: %s", err.Error()),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	if buildResult.Evaluation != controltool.ConclusionEvaluationSuccess {
		return controltool.ConclusionOutput{
			Content:    buildResult.Content,
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	// Extract SCM version from build result
	buildData, err := c.extractBuildResultFromContent(buildResult.Content)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Failed to extract build result data: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	logger.Infof("Build phase completed, extracted SCM version: %s, SCM URL: %s", buildData.SCMVersion, buildData.SCMURL)

	// Phase 2: Deploy
	deployResult, err := c.executeTCEDeploy(run, args.PSM, buildData.SCMVersion, args.Env, args.EnvType, parentStepID)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Deploy phase failed: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	return controltool.ConclusionOutput{
		Content:    fmt.Sprintf("Build and deploy workflow completed successfully.\nBuild: %s\nDeploy: %s", buildResult.Content, deployResult.Content),
		Evaluation: controltool.ConclusionEvaluationSuccess,
	}, nil
}

func (c *CustomDevOpsAgent) executeFullWorkflow(run *iris.AgentRunContext, args DevOpsAgentToolArgs, parentStepID string) (controltool.ConclusionOutput, error) {
	logger := run.GetLogger()
	logger.Infof("Starting full workflow: build → deploy → api test")

	// Phase 1: Build
	buildResult, err := c.executeBuild(run, args, parentStepID)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Build phase failed with error: %s", err.Error()),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	if buildResult.Evaluation != controltool.ConclusionEvaluationSuccess {
		return controltool.ConclusionOutput{
			Content:    buildResult.Content,
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	// Extract SCM version from build result
	buildData, err := c.extractBuildResultFromContent(buildResult.Content)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Failed to extract build result data: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	logger.Infof("Build phase completed, extracted SCM version: %s", buildData.SCMVersion)

	// Phase 2: Deploy
	deployResult, err := c.executeTCEDeploy(run, args.PSM, buildData.SCMVersion, args.Env, args.EnvType, parentStepID)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Deploy phase failed: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	// Extract environment from deploy result
	deployData, err := c.extractTCEDeployResultFromContent(deployResult.Content)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("Failed to extract deploy result data: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	logger.Infof("Deploy phase completed, extracted environment: %s", deployData.TCEEnv)

	// Phase 3: API Test
	updatedArgs := args
	updatedArgs.Env = deployData.TCEEnv
	logger.Infof("Using environment from TCE deployment for API testing: %s", updatedArgs.Env)

	apiTestResult, err := c.executeAPITest(run, updatedArgs, parentStepID)
	if err != nil {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("API test phase failed: %v", err),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	if apiTestResult.Evaluation != controltool.ConclusionEvaluationSuccess {
		return controltool.ConclusionOutput{
			Content:    fmt.Sprintf("API test phase failed: %s", apiTestResult.Content),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}, nil
	}

	return controltool.ConclusionOutput{
		Content:    fmt.Sprintf("Full workflow completed successfully.\nBuild: %s\nDeploy: %s\nAPI Test: %s", buildResult.Content, deployResult.Content, apiTestResult.Content),
		Evaluation: controltool.ConclusionEvaluationSuccess,
	}, nil
}

func NewDevOpsAgentTool() iris.Action {
	var args DevOpsAgentToolArgs
	var output controltool.ConclusionOutput

	return actions.NewTool(actions.NewToolOption{
		Name: "devops_agent",
		Description: `Development Agent Tool for SCM builds and TCE deployments with custom orchestration:

### Core Capabilities (Production Ready):
1. **SCM Build**: Create and monitor build tasks from codebase or SCM repositories
2. **TCE Deploy**: Deploy using SCM versions to specified PSM
3. **Build and Deploy**: Combined workflow with automatic SCM version passing

// TEMPORARILY DISABLED - API TEST NOT READY FOR PRODUCTION
// 3. **API Test**: Execute comprehensive API testing with function and HTTP endpoint testing
// 5. **Full Workflow**: Complete build → deploy → test pipeline

### Repository Systems:
- **CodebaseRepo**: Format {namespace}/{repo} (2 parts), e.g., "cld/bits-ai-showcase"
- **SCMRepo**: Format {a}/{b}/{c} (3 parts), e.g., "bits/ai/showcase"
- If CodebaseRepo is provided, the tool will call get_scm_name to resolve SCMRepo
- If SCMRepo is provided directly, it will be used for building

### Task Types (Production Ready):
- **build**: Build from codebase repository, outputs SCM version
- **deploy**: Deploy using provided SCM version
- **build_and_deploy**: Build then deploy, automatically passing SCM version

// TEMPORARILY DISABLED - API TEST NOT READY FOR PRODUCTION
// - **api_test**: Execute API tests with specified targets
// - **full_workflow**: Complete build → deploy → test pipeline

### Data Flow:
- **SCM Version**: Automatically passed from build to deploy in combined workflows
- **PSM**: Used for deployment consistently

// TEMPORARILY DISABLED - API TEST NOT READY FOR PRODUCTION
// - **Environment**: Automatically extracted from TCE deployment and passed to API testing in full_workflow
// - **PSM**: Used for both deployment and API testing consistently

// TEMPORARILY DISABLED - API TEST NOT READY FOR PRODUCTION
// ### API Test Schema:
// API test targets must follow this schema:
// - Either function testing: {"func_name": "function_name"}
// - Or HTTP testing: {"http_path": "/api/path", "http_method": "GET"}

### Usage Examples:

**1. Build with CodebaseRepo:**
` + "```" + `param="task"
build
` + "```" + `
` + "```" + `param="codebase_repo"
cld/bits-ai-showcase
` + "```" + `
` + "```" + `param="branch"
main
` + "```" + `

**2. Build with SCMRepo:**
` + "```" + `param="task"
build
` + "```" + `
` + "```" + `param="scm_repo"
bits/ai/showcase
` + "```" + `
` + "```" + `param="branch"
main
` + "```" + `

**3. Deploy with SCM Version:**
` + "```" + `param="task"
deploy
` + "```" + `
` + "```" + `param="psm"
my-service-psm
` + "```" + `
` + "```" + `param="scm_version"
v1.2.3-abc123
` + "```" + `
` + "```" + `param="env"
ppe_my_env (optional - auto-generated if empty)
` + "```" + `
` + "```" + `param="env_type"
ppe (optional - 'ppe' for ppe_xxx, 'boe' for boe_xxx, ignored if env provided)
` + "```" + `

**4. Build and Deploy:**
` + "```" + `param="task"
build_and_deploy
` + "```" + `
` + "```" + `param="codebase_repo"
cld/bits-ai-showcase
` + "```" + `
` + "```" + `param="branch"
main
` + "```" + `
` + "```" + `param="psm"
my-service-psm
` + "```" + `

// TEMPORARILY DISABLED - API TEST NOT READY FOR PRODUCTION
// **5. API Test:**
// ` + "```" + `param="task"
// api_test
// ` + "```" + `
// ` + "```" + `param="psm"
// my-service-psm
// ` + "```" + `
// ` + "```" + `param="env"
// staging
// ` + "```" + `
// ` + "```" + `param="api_targets"
// [{"func_name": "test_function"}, {"http_path": "/api/health", "http_method": "GET"}]
// ` + "```" + `
// ` + "```" + `param="is_online"
// true
// ` + "```" + `

// TEMPORARILY DISABLED - API TEST NOT READY FOR PRODUCTION
// **6. Full Workflow (Build → Deploy → API Test):**
// ` + "```" + `param="task"
// full_workflow
// ` + "```" + `
// ` + "```" + `param="codebase_repo"
// cld/bits-ai-showcase
// ` + "```" + `
// ` + "```" + `param="branch"
// main
// ` + "```" + `
// ` + "```" + `param="psm"
// my-service-psm
// ` + "```" + `
// ` + "```" + `param="api_targets"
// [{"func_name": "test_function"}, {"http_path": "/api/health", "http_method": "GET"}]
// ` + "```" + `
// ` + "```" + `param="is_online"
// true
// ` + "```" + `

**Note**: For deploy tasks, env is optional - if empty, TCE will auto-generate one based on env_type: "ppe" generates "ppe_xxx", "boe" generates "boe_xxx". is_online works bidirectionally: it's inferred from env/env_type when they're provided, or used to set env_type when both are missing (true=ppe, false=boe).

// TEMPORARILY DISABLED - API TEST NOT READY FOR PRODUCTION
// **Note**: In full_workflow, env is automatically extracted from TCE deployment result. For deploy tasks, env is optional - if empty, TCE will auto-generate one based on env_type: "ppe" generates "ppe_xxx", "boe" generates "boe_xxx". is_online works bidirectionally: it's inferred from env/env_type when they're provided, or used to set env_type when both are missing (true=ppe, false=boe).

The tool provides custom orchestration logic without using ProgreActAgent to avoid errors with many MCP tools.`,
		Input:  util.TypeToJSONSchema(reflect.TypeOf(args)),
		Output: util.TypeToJSONSchema(reflect.TypeOf(output)),
		Impl: func(run *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
			agent := &CustomDevOpsAgent{
				name:        "devops_agent",
				description: "Custom development agent tool with orchestration logic",
				run:         run,
			}

			err := agent.Execute(run, step)
			if err != nil {
				return nil, err
			}

			return step.Outputs, nil
		},
	})
}
