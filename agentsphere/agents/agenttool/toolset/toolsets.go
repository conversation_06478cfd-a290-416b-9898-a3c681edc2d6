package agenttoolset

import (
	"github.com/samber/lo"

	llmtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/llm"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/toolset"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/browseract"
	argosloganalysistool "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/argos_log_analysis"
	browseragenttool "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/browser"
	chartagenttool "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/chart"
	devopsagenttool "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/dev"
	larkagenttool "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/lark"
	webagenttool "code.byted.org/devgpt/kiwis/agentsphere/agents/agenttool/web"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

func NewChartToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		chartagenttool.NewChartAgentTool(run, step),
	}, nil
}

func NewLarkAgentToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		larkagenttool.NewLarkAgentTool(run, step),
	}, nil
}

func NewWebAppAgentToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		webagenttool.NewWebAppAgentTool(run, step),
	}, nil
}

func NewDevOpsAgentToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		devopsagenttool.NewDevOpsAgentTool(),
	}, nil
}

func NewArgosLogAnalysisAgentToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		argosloganalysistool.NewArgosLogAnalysisAgentTool(run, step),
	}, nil
}

func NewCopilotToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	return []iris.Action{
		llmtool.NewCopilotDiagnoseTool(),
		llmtool.NewCopilotGptTool(),
	}, nil
}

func NewBrowserAgentToolset(run *iris.AgentRunContext, step *actors.ExecutionStep) ([]iris.Action, error) {
	toolsets := []iris.Action{}
	tools, err := browseract.NewBrowserToolset(run)
	if err != nil {
		return nil, err
	}
	// toolsets = append(toolsets, tools...)
	if tool, ok := lo.Find(tools, func(item iris.Action) bool {
		return item.Name() == browseract.ToolQuickExtraction
	}); ok {
		toolsets = append(toolsets, tool)
	}
	toolsets = append(toolsets, browseragenttool.NewBrowserAgentTool(run, step))

	return toolsets, nil
}

func GetAgentToolsets(run *iris.AgentRunContext, step *iris.AgentRunStep) []toolset.Toolset {
	toolsets := []toolset.Toolset{

		{
			Identifier: "browser",
			Description: `visit specific URLs/html pages, navigate websites, interact with elements, and extract content. 
- Use this to validate generated html pages before deploying.
Limitation: Can not open binary files like docx, pptx, xlsx, etc. Can only be used for specific URLs.
**CRITICAL RESTRICTION**: This tool is STRICTLY FORBIDDEN from accessing Feishu/Lark document links under ANY circumstances. Do NOT attempt to use browser tool with any Feishu or Lark URLs.`,
			New: NewBrowserAgentToolset,
		},
		{
			Identifier: "devops_agent",
			Description: `devops_agent can perform development operations including SCM builds, TCE deployments. It supports both codebase repositories (2-part format: namespace/repo) and SCM repositories (3-part format: a/b/c). Tasks include: build, deploy, build_and_deploy. 

IMPORTANT CONSTRAINTS:
- Only use build task when "SCM" is explicitly mentioned
- Only use deploy task when "TCE" is explicitly mentioned  
- Only use build_and_deploy when both "SCM" and "TCE" are explicitly mentioned
- Do not use for generic "build" or "deploy" requests
- Do not use mcp tools like SCM编译 or TCE部署. Use this agent tool instead.`,
			New: NewDevOpsAgentToolset,
		},
		// 飞书文档创建目前由 lark toolset 负责，但是过往经验中还会使用这个工具，因此需要兜底。
		// 注意：这个工具不暴露给 planner，正常流程 planner 不会分配这个工具。
		{
			Identifier: "lark_creation",
			Description: `lark_creation can do the following things:
- Generate a Lark/Feishu document from a html file or markdown file, please prepare the required pictures, charts and diagrams before using this tool.
- Update a Lark/Feishu document URL from a modified markdown file, please prepare the required pictures, charts and diagrams before using this tool.
- Convert csv、xlsx、xls files to Lark/Feishu sheets/table or Lark/Feishu Base. (将csv、xlsx、xls文件转换为飞书表格或多维表格)`,
			New:               NewLarkAgentToolset,
			HiddenFromPlanner: true,
		},
		{
			Identifier:  "copilot_diagnose",
			Description: "copilot_diagnose can analyze agent execution trajectory and diagnose issues in artifact generation workflows, providing root cause analysis and solutions for fixing failed deliverables",
			New:         NewCopilotToolset,
		},
		{
			Identifier: "web_creation",
			Description: `web_creation can do the following things:
- Creating visualized reports(可视化报告), HTML reports(HTML报告), fullstack apps, and frontend applications
- Building data visualization dashboards, showcase websites and interactive reports
- Developing demo apps, prototypes and MVPs, transforming static documents into dynamic web experiences`,
			New: NewWebAppAgentToolset,
		},
	}
	if iris.CurrentRegion() != iris.RegionI18n {
		toolsets = append(toolsets, toolset.Toolset{
			Identifier: "argos_log_analysis",
			Description: `Argos log analysis tool can do the following things:
1. Get the original log record based on the log ID.
2. Perform statistical analysis of the log.
3. Associate the log with the corresponding code.`,
			New: NewArgosLogAnalysisAgentToolset,
		})
	}
	return toolsets
}
