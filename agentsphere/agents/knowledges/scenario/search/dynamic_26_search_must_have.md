---
ID: "dynamic_26"
Title: search must have knowledge
EnabledIf: "agent in ['mewtwo'] and ('search' in tools or 'research' in tools)"
UsedWhen: You MUST use this knowledge if the following steps need use search tool or research tool.
---

## Document Best Practice

- When writing any documents, use markdown as an intermediate format.
- Add citations in your documents, ensure all citation numbers remain consistent with the given information in the
  execution trace.
- After a series of consecutive searches, use create_file to generate a final summary document. The summary must include
  citations that correspond to the search results.

## ADD Citations in Documents

### 1. Citation Formatting for Markdown Files (`.lark.md`)

`lark.md` is an intermediate result, not a Feishu/Lark doc. It must follow Feishu/lark specific rules, as list in "Lark/Feishu Document Export and Formatting".

- **Purpose:** This format is used for internal markdown files, which are intermediate outputs.
- **Format:** `::cite[ref number]` (e.g., `::cite[50]`). For multiple citations, list them one by one (e.g.,
  `::cite[23]::cite[65]`).
- **Example:**
    - "...xxx 的数据是 105.5，数据来源是::cite[23]." # one citation
    - "...xxx 的数据是 23.4，数据来源是::cite[53]::cite[65]." # two citations
    - "The above data is sourced from ::cite[2]::cite[43]."
- **Placement:** Place the citation marker immediately after the specific fact or statement.
- **Forbidden:** Do not wrap `::cite[ref number]` in quotes or backquotes.

### 2. Citation Generation from Search Results

**You MUST generate citation when you use `create_file` to generate search results. It is a strict rule.**

- **Purpose:** These rules apply when generating new content based on information from `<search-info>` (e.g., from
  `search` or `research` tool outputs).
- **Requirements:** If the model's response references an info fragment, a citation must be provided. Do not cite
  information that is irrelevant to the user's query.
- **Scope:** Citations should only refer to info fragments within the `<search-info>` XML structure.
- **Format:** The format for a single citation is `::cite[1]`, and for multiple citations, it is `::cite[1]::cite[2]`.
  The numbers `1` and `2` represent the IDs of the cited info fragments.
- **Avoid Invalid Citations:** Do not generate empty or invalid citations (e.g., `::cite[None]` or `::cite[无]`).

**--- STRICT FORMATTING RULES ---**
a. **Mandatory Bracket Style:** ALL citation markers MUST use standard square brackets `::cite[...]`.
b. **Single Citation:** A single citation is formatted as `::cite[N]`.
c. **Multiple Citations:** If multiple sources support the same information, use separate, adjacent brackets:
`::cite[1]::cite[5]::cite[6]`.
d. **Strictly Forbidden Formats:**

- Comma-separated lists (`::cite[1, 5, 6]`)
- Escaped brackets (`::cite\[1]`)
- Repeated citations (`::cite[1]::cite[1]`)
  e. **Placement and Consolidation:**
- Place citation(s) immediately after the fact, with no leading space.
- If identical citations appear consecutively (e.g., `::cite[1]::cite[1]`), consolidate them into a single instance (
  `::cite[1]`).

**--- EXAMPLE ---**

- CORRECT: Shanghai offers diverse cuisine`::cite[2]::cite[7]`.
- CORRECT: The Bund is a famous landmark`::cite[1]`.
- INCORRECT: Shanghai offers diverse cuisine`::cite[75]::cite[6]::cite[75]`.
- INCORRECT: The west side of the Bund... styles
  `::cite[3]::cite[4]::cite[5]::cite[1]::cite[6]::cite[7]::cite[60]::cite[7]`.


