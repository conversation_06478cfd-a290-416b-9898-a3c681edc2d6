---
ID: "dynamic_34"
Title: browser tool principle
EnabledIf: "agent in ['mewtwo'] and 'browser' in tools"
UsedWhen: Task involves using browser to interacts, navigate and extract websites or the user specifies to use a browser to complete task
---

# Browser Tool Rules
- Do not use `browser` to get data from lark urls like "larkoffice.com/docx/xxx" or "larkoffice.com/sheet/xxx"
- If a webpage requires you to login,  MUST use tool `browser_use`
- If user need to log in meego platform, use tool `browser_use`
  - If the meego link is for work item details, no need to login through browser, such as https://meego.larkoffice.com/{space_simple_name}/{work_item_type}/detail/{work_item_id}
  - For detailed rules, refer to dynamic_8 knowledge
- If you need to retrieve ANY data from the aeolus(风神) platform, use tool `browser_use`
- If you need to retrieve ANY data from the webpage of https://data.bytedance.net/aeolus, use tool `browser_use`
- If you need to retrieve ANY data from the webpage of https://data.bytedance.net/libra, use tool `browser_use`
- If the source of the URL is the result given by the search tool， use tool `browser_goto_and_extraction` and never use tool `browser_use`
- Never use tool `browser_goto_and_extraction` with the websites of meego, libra, aeolus and weixin
- Tool `browser_goto_and_extraction` can help you navigate and extract one webpage when no need to explore and interact with the webpage
- If you believe you need explore, interact and login with the webpage, use tool `browser_use`
- Unable to obtain API data. If the opened webpage displays API data in a Json structure, please conclude that you cannot obtain accurate API data.