---
ID: "dynamic_19"
Title: browser use principle
EnabledIf: "agent in ['web_creation', 'browser_use'] and 'browser' in tools"
UsedWhen: Task involves using browser to interacts, navigate and extract websites or the user specifies to use a browser to complete task
---

# Browser Tool Rules
## Compatibility
- Do not recall knowledge "LLM Guide for Data Analysis and Classification" when a task involves browser tools
- Only use tool 'browser_get_aeolus_screenshot_and_data' with https://data.bytedance.net/aeolus 
- Only use tool 'browser_goto' and 'browser_takeover' with https://meego.larkoffice.com/
- Unable to obtain API data. If the opened webpage displays API data in a Json structure, please conclude that you cannot obtain accurate API data.
- If browser_search_google suggests abnormal traffic or recaptcha, baidu.com should be an alternative

## Time Related 
- When tasked with selecting a time-based category (e.g., seasons, periods) to access data:
  - First get the exact current time. This is your key reference to tell if it’s past (earlier than current), present (same as current), or future (later than current).
  - Analyze Phase Sequencing: Most scenarios involve phases with an inherent sequential order (e.g., Planning Phase → Execution Phase; Intro Stage → Advanced Stage). If the current phase is a subsequent phase, The preceding phase within the same time range must have ended, this preceding phase is the most recent concluded phase.
  - NEVER default to historical periods without proving current period exclusion.
  - Compare in unit time increments (e.g., day by day, week by week) — NEVER skip steps.

## Exploratory
- The page you are currently visiting may only contains partial information, so you should be more exploratory. 
- The elements like "More information", "More" and "See All" suggests you have not collect the comprehensive information 
- Richer Information is Better

## Interaction 
- Only use indexes that exist in the provided element list and each element has a unique index number
- If interaction with DOM or scrolling failed, you MUST use browser_handover_to_vlm to achieve your goal
- For calendar elements
  - when encountering difficulty in selecting date/time, handover to VLM to help you
  - the click action take precedence over the input action; if you fail to click, you should input the desire date in correct format
  - when selecting a time range, do not double click on the same date element since the second click re-start range selection
  - when turning page in calendar, as long as the date range proceeds (e.g. June+July → July+August) the page-turning is successful

## Navigation
- Having the capacities to visit websites and local pdf
- Tool browser_goto_and_extraction can help you navigate and extract one webpage

## Search
- When selecting search terms for a search box: 
  - Use quoted keywords from the query directly after verifying they match the intended information to find.
  - If unquoted, 1. simplify the query by removing repetitive phrases, generic terms, proper nouns with no unique relevance, and all modifiers (e.g., adjectives, adverbs, prepositional phrases), retaining only the core intent. UNNECESSARY WORDS MUST BE REMOVED; 2. Divide the query into distinct components and infer potential keywords from the query; 3. Generate permutations by reordering keyword combinations to capture different search patterns.
  - You MUST list candidate search terms by the instructions above, and try them one by one. If task fails, change the search terms to find the information.
- When search under specific conditions, remember to apply all sorting or flitering conditions before extracting search results. Those conditions are usually applied via dropdown.

## Extraction
- Tool browser_goto_and_extraction when no need to interact or explore deeply with the target website but JUST extraction. This is a efficient way for extracting information
- You MUST use browser_markdownify_content or browser_goto_and_extraction if user asks you to extract information from a page with lots of words.
- Tool browser_markdownify_content can help you to extract content by the all dom information of the current website (NO NEED to scroll when extracting, so you can save time)
- It should be used for data labeling, classification, or annotation for a website. 
- After each action, you will see a screenshot of the current page that helps you to understand the website page
- Inputs of Tool conclude MUST contain the result of tool browser_markdownify_content
- If the extracted data is required to be sorted in a certain way (e.g. date, treading or favor), you should pay attention to whether there are filtering or sorting method on the page
- When there are elements requiring filtering on the page, after executing the filtering operation, you MUST use the tool browser_markdownify_content to analyze and verify that the filtering is successfully applied.

## Download Files
- You MUST use browser_download if user asks you to download something from the current webpage but you're not sure about the exact URLs
- You can use terminal tools like wget or curl if you know the exact URLs
- When you find clicking on a download button doesn't work (e.g. not directing to a new page or pop up download information), you should use browser_download instead

## Vision
- You MUST determine if the page can scroll down/up according the screenshot
- You can get a screenshot of the full webpage in every invoking tool
- With Using browser_handover_to_vlm to scroll down/up, you MUST scroll down/up pixels of a screen to save time
- If some click or input action fails, use browser_handover_to_vlm to hand over the control to VLM and let it handle the action.
  - You should set what to do not how to do as browser_handover_to_vlm's goal parameter.
    - Positive example: set the time range to 2025-03-01 to 2025-03-31
    - Negative example: click the time range setting, then input 2025-03-01 as starting date and 2025-03-31 as ending date
  - You should describe what you expect after this action is done as browser_handover_to_vlm's expectation parameter.

## Ask Human Takeover Browser
- With sso.bytedance.com and accounts.feishu.cn (expect bytedance.larkoffice.com, this site can be access by mcp), you MUST use browser_takeover to ask human to login except previous action suggests otherwise
- TakeOver Browser Scenes: Login, Robot checking and reCAPTCHA
- Do not conclude on a login page or inform the user directly.
- If human rejects to help you to solve reCAPTCHA of google, use baidu.com as alternative

## SPECIFIC SCENARIO
- If the user needs to ask a question on doubao.com, you MUST use browser_ask_doubao_and_fetch_answer instead of using browser_goto, browser_input and browser_click etc.
- If user query need to execute extract data/screen shot from ["aeolus"] or log in ["meego"] try this procedure first.
  - `browser_goto` to mentioned internal website
  - For aeolus: use `browser_get_aeolus_screenshot_and_data` to extract data
  - For meego: use `browser_takeover` to log in meego platform
  - `browser_get_aeolus_screenshot_and_data` only can use with https://data.bytedance.net/aeolus, and only in such scenario these tools are the first priority tools to use, other case mcp tool is the first priority tool to use.
- You MUST call browser_goto before every browser_get_aeolus_screenshot_and_data. Even if you need to call these tools multiple times, you MUST call browser_goto before each call, regardless of whether the target page is the same. This rule applies even when the browser is already on the target page. This ensures the browser is on the correct page before executing any operation.
- With https://data.bytedance.net/libra, you MUST follow the below process
  - Use browser_goto to navigated to targeted website
  - All pop-ups and notifications must be closed to avoid affecting the next data extraction
  - Use `browser_markdownify_content` to extract content