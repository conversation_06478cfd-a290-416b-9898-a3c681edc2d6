---
# This knowledge significantly affects the performance of newbie with Gemini model series
# Providing a very detailed and well-structured example can help newbie to plan the task better and (also create content rich results)
# Use this knowledge whenever possible
ID: "tool_lark_progress_example_newbie"
Title: progress example - Lark
EnabledIf: "agent in ['mewtwo'] and variant in ['newbie']"
UsedWhen: when the task needs designing and generating lark reports or documents, be aware that this is only for new lark document creation.
---

## Lark/Feishu Document Creation Plan Guide

**You MUST carefully plan your output format, document organization, and the use of Feishu-specific elements (e.g., `table`, `grid`, `callout`) according to the task and the user’s requirements, without being influenced by the collected materials or reference documents.**
**When you need to create new lark reports or documents, you must create a plan (todo list) first, please strictly adhere to the following critical instructions:**
1.  **First Action:** Ensure `progress_think` (or a similar planning/thinking step) is your very first action before generating any report content.
2.  **Include Section Design:** Your generated todo list *must* explicitly include tasks or a phase related to "Section Design", make sure your report looks different and contains more demonstration compared to all reference documents generated from previous steps.
3.  **Include Special Element Instructions:** Your todo list *must* contain specific operational instructions for diagrams and other special elements (e.g., `table`, `grid`, `callout`). You must use special elements (e.g., `table`, `grid`, `callout`) extensively to ensure the report is optimally presented.

### Special Element Code Examples
Here are some code examples for special elements (e.g., `table`, `grid`, `callout`):

```callout
background_color: 11
border_color: 5
emoji_id: bulb
content:|
    **优点：**
    - 能够产出专家级报告，提升研究效率
    - 显著节省研究时间，自动化完成复杂任务
```

```grid
grid_column:
- width_ratio: 50
  content:|
    **短期规划：**
    - 未来几周内将支持移动端和桌面端
    - 计划添加嵌入式图片、数据可视化和其他分析性输出
- width_ratio: 50
  content:|
    **中长期规划：**
    - 将扩展功能，支持连接更多专业数据源，包括订阅资源和企业内部资源
    - 未来将结合OpenAI的Operator，实现AI研究与现实任务执行的结合
```

| 产品 | 主要优势 | 主要劣势 |
| -------- | -------- | -------- |
| ChatGPT Deep Research | 基于o3模型，推理能力强；专业级报告输出 | 价格昂贵；处理时间长 |
| Grok 3 Deep Research | 免费使用；实时舆情整合 | 功能相对简单 |
| Perplexity | 响应速度快 | 深度研究能力有限 |
| Google Gemini | 集成Google生态 | 在复杂推理上表现一般 |

### Lark/Feishu Document Creation Plan Example

Please refer to the following successful task progress plan (in <Example_include_rationale> tag). Your objective is to generate a new, similarly structured, fine-grained task progress plan, also in todo list format, based on the **current task description and available resources**.

<example>
assistant: The user wants a Feishu document with charts generated from the provided text about De'Longhi. The process will involve:

1. **Data Analysis and Visualization** - through the information from execution trace, I could make: A bar chart will be created to represent the market share data, and a line chart for patent trends.
2. **Defining a clear structure for the report** based on the input text. I will reorganize the report structure based on the provided research to make it more user-friendly and well-structured.
3. **Analyze Data Visualization.** The market share data and patent trend data will be visualized.
4.  **Carefully transferring the content into a Feishu-compatible markdown format.** This includes ensuring all citations (`::cite[xx]`) are preserved and correctly formatted for Feishu.
5.  **Incorporating the visualization** into the Feishu-compatible markdown document. Also, check that the resource paths are correctly embedded into the final report, following ordinary coding knowledge.
6.  **Using appropriate Feishu elements** like `callout` or `grid` to enhance readability and presentation, as per best practices.
7.  **Finally, using the `mcp:lark_create_lark_doc` tool** to convert the markdown file into a Feishu document. Double-check that all the special formats are in Feishu grammar.

I will start by outlining the plan using `progress_think`. The plan will detail the sections of the report, the data visualization step, and the steps to generate the final Feishu document.

<tool name="progress_think">
<progress>
- [ ] **Phase 1: Data Analysis, Report Blueprint & Outline**
  - [x] Identify key data for visualization (image and interactive charts):
    - Market share data: "2023年1-11月在中国淘宝+天猫平台，按销售额计算，德龙的市场占有率为28.9%，位居行业第一. 按销量计算，德龙的市场占有率为13.5%，位居行业第二" (matplot chart).
    - Patent trend data: "2023年1-11月，德龙公司在中国大陆市场获得了10个专利授权，其中发明专利授权数量为5个，实用新型专利授权数量为5个。2024年同期为12个以及15个" (interactive line chart).
  - [x] Define core sections based on the provided "德龙公司调研报告":
    -  `## 1. 公司历史和发展沿革`
    -  `## 2. 主要产品类别和代表性产品`
    -  `## 3. 全球市场表现、主要市场区域和市场份额`
    -  `## 4. 近几年的财务业绩和关键财务指标`
    -  `## 5. 主要竞争对手及其对比分析`
    -  `## 6. 最新的公司动态、新闻报道和行业分析`
    -  `## 7. 公司的技术创新和专利情况`
    -  `## 8. 消费者口碑和评价`
  - [x] Identify potential special elements for enhanced presentation:
    -  Use `callout` to highlight key market share figures or strategic statements.
    -  Consider `grid` for comparing competitor features if data allows for a concise summary.
- [ ] **Phase 2: Data Visualization**
  - [ ] Create a Python script (`create_market_share_chart.py`) to generate a bar chart (`output/market_share_chart.html`) for the market share data.
    - [ ] Extract data: De'Longhi sales share (28.9%), De'Longhi volume share (13.5%). Note: The report mentions "仅次于小熊电器（13.0%）" for volume share, implying Xiaoxiong is first at 13.0% and De'Longhi is second at 13.5%. This seems contradictory or a typo in the source. For the chart, I will represent De'Longhi's stated shares and note the context. Or, more accurately, represent De'Longhi (28.9% sales, 1st) and De'Longhi (13.5% volume, 2nd). For volume, it's better to show De'Longhi vs. "Industry Leader (if not De'Longhi)" or "Next Competitor". The text says "仅次于小熊电器（13.0%）" for volume, meaning Xiaoxiong is 13.0% and De'Longhi is 13.5%. This means De'Longhi is *ahead* of Xiaoxiong. The phrasing "位居行业第二，仅次于小熊电器（13.0%）" is confusing. Let's assume De'Longhi is 13.5% (2nd) and Xiaoxiong is the one at 13.0% (implied 3rd or a typo meaning Xiaoxiong is 1st with a higher share). Given the phrasing "位居行业第二", it's safer to assume there's another brand at #1 for volume. I will create two separate charts or a grouped bar chart for clarity: one for sales share (De'Longhi vs. Rest of Market or vs. #2 if known) and one for volume share (De'Longhi vs. #1 and Xiaoxiong).
    - Simpler approach: Create one chart showing De'Longhi's Sales Share (28.9%) and Volume Share (13.5%) with clear labels indicating their rank.
  - [ ] Use `matplotlib` or `seaborn` for chart generation. Ensure Chinese characters are displayed correctly.
  - [ ] Save the chart as a PNG file (e.g., `output/delonghi_market_share_china_tmall_2023.png`).
  - [ ] Create an HTML script (`output/patent_trend.html`) to generate the HTML file for the interactive line chart for patent trend data.
  - [ ] Use `echart` or `plotly` for the interactive chart generation. Ensure Chinese characters are displayed correctly.
  - [ ] Deploy `patent_trend.html` and prepare the resource to be embedded in the final report.
  - [ ] Make sure there are no citations in diagrams, as guided by the knowledge.
- [ ] **Phase 3: Content Generation for Feishu Document**
  - [ ] Create the path and content of markdown file: `delonghi_report.lark.md`. Paths for resources should be relative paths.
  - [ ] Populate Section 1: 公司历史和发展沿革.
  - [ ] Populate Section 2: 主要产品类别和代表性产品.
  - [ ] Populate Section 3: 全球市场表现、主要市场区域和市场份额.
    - [ ] Check the path and embed the generated `delonghi_market_share_china_tmall_2023.png` image.
    - [ ] Add a `callout` to highlight the market share statistics.
  - [ ] Populate Section 4: 近几年的财务业绩和关键财务指标.
    - [ ] Use a `callout` for the 2021 and 2023 financial highlights.
  - [ ] Populate Section 5: 主要竞争对手及其对比分析.
    - [ ] Consider using a `grid` or bullet points within a `callout` to summarize key competitor differences if it enhances clarity.
  - [ ] Populate Section 6: 最新的公司动态、新闻报道和行业分析.
  - [ ] Populate Section 7: 公司的技术创新和专利情况.
    - [ ] Embed the deployed URL of `patent_trend.html`.
  - [ ] Populate Section 8: 消费者口碑和评价.
  - [ ] Make sure all elements with special formatting meet Lark requirements, such as callout, grid, formula, etc.
  - [ ] Use appropriate Feishu elements like `callout` or `grid` to enhance readability and presentation, as per best practices.
- [ ] **Phase 4: Review & Feishu Document Generation**
  - [ ] Conduct a thorough review of `delonghi_report.lark.md` for accuracy, logical flow, completeness, clarity, and Feishu markdown compatibility.
  - [ ] If a Lark document template exists, strictly follow the template's structure and content. Ensure that no sections or modules from the template are missing in the generated document.
  - [ ] Verify that all visualizations are correctly embedded and formatted. Make sure the paths of all resources are correctly embedded.
  - [ ] Verify all citations are correctly formatted (e.g., `::cite[1]::cite[2]`) and in the right place (no citations in figures).
  - [ ] Use the `mcp:lark_create_lark_doc` command to generate the Feishu document.
</progress>
<next_stage>
Proceed with Phase 2: Data Visualization. Create the Python script to generate the market share chart and HTML script for the patent trend chart.
</next_stage>
</tool>

</example>

### Lark/Feishu Document Creation Example

Please refer to the following successful lark.md creation example, your objective is to generate a new lark.md file, based on the **current task description and available resources**.

<example>
assistant: I will create a `.lark.md` file and optimize the layout using Feishu-specific formatting elements (such as `callout`, `grid`, and `table`).

<tool name="create_file">
<file_path>delonghi_report.lark.md</file_path>
<content>
......
</content>
</tool>

</example>