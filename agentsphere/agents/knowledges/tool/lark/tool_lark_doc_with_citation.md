---
ID: "tool_lark_doc_with_citation"
Title: Feishu/Lark Doc Generation (citation needed)
EnabledIf: "agent in ['mewtwo']"
UsedWhen: |
  when generating Feishu/Lark docs
---

## ADD Citations in Feishu/Lark Documents and Reports

- Add citations in your report, ensure all citation numbers remain consistent with the given information in execution trace.
- Citations (format as "text content ::cite[ref number]") are provided in the Execution Trace somewhere, you must locate and include citations in your Feishu/Lark report/doc at appropriate location.
- There might be some information in task description and Execution Trace that summarized from the cited information, you have to locate the original citations and add citation back to the Feishu/Lark docs.
- The citation format in your `.lark.md` files must be ::cite[ref number] (e.g. ::cite[50]), for multiple citations, list them one by one (e.g. cite number 23 and 65: ::cite[23] ::cite[65]).
  - here is an example for citation in text (include text in table):
    ...xxx 的数据是 105.5，数据来源是::cite[23]. # one citation
    ...xxx 的数据是 23.4，数据来源是::cite[53] ::cite[65]. # two citations
- Chart citations should be placed in lark doc main context, e.g.:
  - if the figure is an image:
    ![This image shows a chart of sales data.](./output/sales_chart.png)
    The sales data is sourced from ::cite[45] ::cite[78].
  - if the figure is a preview of deployed html link:
    [preview](URL_ADDRESS)
    The above data is sourced from ::cite[2] ::cite[43].
- Forbidden generation of Individual Reference Section, unless the user has clearly preference.
- In report, do not mention how you process the citation format and organize the report, focus on the main story.
- Never wrap ::cite[ref number] in quotes or backquotes, as it will cause parsing error.