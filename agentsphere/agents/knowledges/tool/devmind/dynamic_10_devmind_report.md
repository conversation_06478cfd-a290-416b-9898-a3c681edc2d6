---
ID: "dynamic_10_devmind"
Title: Devmind Insight Report Data Tool Principle
EnabledIf: "agent in ['data_analyzer', 'mewtwo'] and 'devmind' in tools"
UsedWhen: when working with devmind data structures and analysis(only for insight_report_data tool).
---

# Devmind Insight Report Data Tool Principle

## Data Structure Understanding
Before conducting any data analysis, you must completely understand the data structure. For each metric that needs to be calculated, provide clear and explicit calculation logic. It is strictly prohibited to make assumptions or guesses about how metrics should be calculated. Always seek clarification when the calculation logic is not evident.

## Data Structure Description
The file path of report_info_reference is the report information. 
The file path of report_metric_stories_data_reference is metric stories of the report. 
- The first sheet is a collection of metric information.
  - The first sheet name like "ReportMetricStoriesInfo".
  - The first sheet field "Metric Story Name" is the name of the metric story.
  - The first sheet field "Metric Story ID" is the id of the metric story.
  - The first sheet field "Metric Story Description" is the description of the metric story.
  - The first sheet field "Metric Story Caliber" represents the calculation caliber of the metric story.
  - The first sheet field "Metric Story Analysis Direction" represents the analysis direction of the metric story.
- The other sheets are all metric stories result data returned from queries.
  - The other sheets name include the id of the metric story, and the chart type of the metric story query results.
  - The other sheets name like "7504194837596342308 line_graph". "7504194837596342308" is the id of the metric story, and "line_graph" is the chart type of the metric story query results, each chart type is as follows:
    - line_graph:
      This is a line chart. The first column is the analysis dimensions(here may be multiple analysis dimensions, if there are multiple, they are separated by commas), the second is time, the third is the value corresponding to each time node on the chart, the fourth is the baseline value.
    - card_graph:
      This is a metric card. The first column is the name of the metric story, the second is time, the third is the value corresponding to each time node on the chart, the fourth is the baseline value, and the fifth and sixth columns are the baseline analysis conclusion and historical analysis conclusion respectively.
    - chart_sheet:
      This is tabular data. The first column is the analysis dimension(there may be multiple analysis dimensions), followed by the corresponding values of the metric story, month-on-month change, month-on-month ratio, proportion, year-on-year change, year-on-year ratio, and then the second metric story, the third metric story, etc. (if any).
    - dim_i:
      This is a metric card with dimension analysis. The first column is the analysis dimension(there may be multiple analysis dimensions), followed by the corresponding values of the metric story, month-on-month change, month-on-month ratio, proportion, year-on-year change, year-on-year ratio, and then the second metric story, the third metric story, etc. (if any).
