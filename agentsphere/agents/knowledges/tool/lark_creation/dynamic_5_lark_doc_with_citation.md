---
ID: "dynamic_5_citation"
Title: Feishu/Lark Doc Generation (citation needed)
EnabledIf: "false"
UsedWhen: |
  when generating Feishu/Lark docs
---

When task assigned to you involves generating reports or documents in Feishu/Lark docs, you should follow the following guidelines:

## Lark/Feishu Best Practice

- When exporting analysis results to Feishu documents, use markdown as an intermediate format, as markdown can be directly imported into Feishu documents.
- However, you must follow some Feishu/lark specific rules, as list in "Lark/Feishu Document Export and Formatting".
- Add citations in your report, ensure all citation numbers remain consistent with the given information in execution trace.
- If images, make sure you first generate them individually and separately, before embedding them into Lark/Feishu Doc through file. Only through file path. And remember to check the file path. DO NOT use any links that do not exist in the context.
- Your output should be stored as a markdown file (with `.lark.md` as suffix). You should decide the file name and path by yourself like so: `{name}.lark.md`.
  - Must check your working directory before generating the file.
  - If content is too long, you may generate the doc snippet within separate `.lark.md` docs, but you MUST `cat` them together into only one file (target `lark.md` file) before calling mcp tools to generate the target lark doc.
  - And remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file afterward.
- When generating html files for interactive charts, make sure html files contains code for charts only, text descriptions should be placed in the main content area of the Lark docs.
- Some additional guides:
  - Some appropriate line breaks will make the report more use-friendly.
  - Do not generate Table of Contents, as lark support TOC natively.
  - Only generate report/doc main body, without the main title of the doc - feishu/lark reports and docs do not need main title.
  - It is strongly recommended that all supporting resources for the final `.lark.md` report (including interactive HTML charts, figures, and document snippets) be placed in the './output/' directory.
    - Move all needed files (html, png, etc.) into `./output` folder before you generate and deploy the final report.
    - In your final report, please TAKE SPECIAL ATTENTION to the PATH of the resource files.
        - Situation one: If the final lark doc placed on the father directory of the folder for resources, make sure the resource paths in the lark doc are relative path.
          - For example, if the final lark doc is placed in `./output/`, and all resource to be embedded are placed in `./output/resources/`, the resource path in the lark doc should be `./resources/xxx.xxx`.
        - Situation two: If the final lark doc placed in the folder for resources, make sure the resource paths in the lark doc are file name only.
          - For example, if the final lark doc is placed in `./output/`, and all resource to be embedded are placed directly in `./output/`, the resource path in the lark doc should be only the file name e.g. `xxx.xxx`.
        - For other situations, please organize the files refer to Situation 1.
    - When previous task use git clone, make sure the report and resource are not with the same folder to the cloned project (the folder contains report will be deployed, which should not contain the cloned project).

## Lark/Feishu Document Export and Formatting

### Feishu Document Formatting Rules

- Heading format: Must add a space after `##` symbols (e.g., `## Heading 2`), supports heading levels 2-9 (by the number of `#`).
  - Please note, as you are required to generate only main body, your report should start with level 2 headings `## Heading 2`, as the main title will be decided latter.
- List formatting:
  - Ordered lists: Number followed by a period and space (`1. Item`)
  - Unordered lists: Hyphen or asterisk followed by a space (`- Item` or `* Item`). Avoid using `•` or `·` for lists.
  - Task lists: Square brackets followed by a space (`[] Task`), note no content in brackets and must followed by a space.
  - Please note, list only accept pure text contents, do not include any other elements (e.g. img, files).
- Text formatting: remember to add a space after the last symbol, such as `*text* `, `**text** `, `~~text~~ `, `~text~ `, `$$text$$ `. Note, there is no space between symbols and text.
  - Bold: `**text** `
  - Italic: `*text* `
  - Underline: `~text~ ` (note this differs from standard Markdown)
  - Strikethrough: `~~text~~ `
  - Font Color: `<font color="colorname">text</font>`
    - Supported colors: red, orange, yellow, green, blue, purple, grey (or gray)
    - Example: `<font color="red">This text is red</font>`
    - Example: `<font color="blue">This text is blue</font>`
    - **Important for mixed formatting**: When combining font color with other formatting (bold, italic, etc.)
      - Correct: `<font color="red">**bold red text**</font>`
      - Correct: `<font color="red">~~red text~~</font>`
      - Incorrect: `**<font color="red">bold red text</font>**` (this will not work properly)
  - Spaces: Use `&nbsp;` for non-breaking spaces if you need spaces at the beginning or in the middle of paragraphs
    - Example: `&nbsp;This is a paragraph with spaces.`
- Table:
  | Header 1 | Header 2 |
  | -------- | -------- |
  | First line<br>Second line | Content 2 |
  - limitations:
    - Supports basic table syntax but does not support complex features like cell merging.
    - Use <br> inside tables cells to add line breaks, ONLY IF NECESSARY, and **ONLY INSIDE TABLE CELLS**.
    - Always add an empty new line before the table header to ensure proper separation from the section title, if the table is right after a heading.
    - Do not break columns into multiple lines by `\n`, you can only separate lines by <br> as this definitely cause formatting errors.
    - Do not modify the original table structure, change table to headings, etc.
    - **CRITICAL CLARIFICATION**: Table cells have DIFFERENT rules from other document elements:
        - NEVER use `\n` in table cells (even though formulas elsewhere require "individual lines")
        - Use ONLY `<br>` tags for line breaks in table cells, regardless of other formatting rules in the document
        - **IMPORTANT**: Even when including callout and grid special formats within table cells, ALL line breaks must be converted to `<br>` tags
        - **SPECIAL CHARACTER ESCAPING**: When using special formats in table cells, the pipe character `|` must be escaped as `\|` (e.g., `content:\|` instead of `content:|`)
- Quotes: `> space`
- Code: `code`, this is code in text, do not need to specify language type.
- Code blocks: ```language_type code_content```, this is an individual code block, specify language type directly after start symbol '`', here are some examples:
    - e.g. for Python: ```Python ...```
    - e.g. for Go: ```Go ...```
- PlantUML diagrams: For 时序图 sequence diagrams（注意：是时序图，不是流程图）、mind maps and class diagrams, **prioritize using PlantUML** over other image generation methods. **CRITICAL: ALL PlantUML code MUST be placed in plantuml code blocks** with the following format:
    ```plantuml
    @startuml
    ' your PlantUML code here
    @enduml
    ```
    - **MANDATORY REQUIREMENT**: PlantUML code MUST always be wrapped in plantuml code blocks
    - **Supported diagram types only**: Only supports 时序图 sequence diagrams（时序图，不是流程图）, mind maps, class diagrams. All other diagram types are not supported
    - PlantUML diagrams will be automatically rendered when the Lark document is created
    - Examples of when to use PlantUML:
      - 时序图 Sequence diagrams for API interactions（时序图展示API调用的时间顺序和对象交互）
      - Class diagrams for code structure
    - **PlantUML Syntax Limitations - STRICTLY ENFORCED**: The following syntax restrictions MUST be strictly followed when using PlantUML. **VIOLATION OF THESE RULES WILL CAUSE RENDERING FAILURES**:
      - **ABSOLUTELY FORBIDDEN - Define syntax**: The `!define` syntax is STRICTLY PROHIBITED and WILL CAUSE ERRORS
      - **ABSOLUTELY FORBIDDEN - Theme syntax**: The `!theme` syntax is STRICTLY PROHIBITED and WILL CAUSE ERRORS
      - **ABSOLUTELY FORBIDDEN - Asterisk syntax**: The `*` syntax is STRICTLY PROHIBITED and WILL CAUSE ERRORS
      - **SEVERELY LIMITED - Relationship syntax**: ONLY `<|--`, `*--`, and `o--` relationship notations are allowed. ANY complex relationship syntax like `||o--{` is STRICTLY FORBIDDEN
      - **ABSOLUTELY FORBIDDEN - Theme and style settings**: ALL theme and style configurations are STRICTLY PROHIBITED
      - **MANDATORY REQUIREMENT - @startuml placement**: The `@startuml` directive MUST be on a line by itself with ABSOLUTELY NO other content
      - **ABSOLUTELY FORBIDDEN - Skinparam syntax**: The `skinparam` syntax is STRICTLY PROHIBITED and WILL CAUSE ERRORS
      - **CRITICAL WARNING - NO COLOR CONFIGURATIONS**: Any color-related configurations, including but not limited to color names, hex color codes, RGB values, or any color styling syntax, are ABSOLUTELY FORBIDDEN and WILL IMMEDIATELY CAUSE RENDERING FAILURES
      - **STRICTLY FORBIDDEN - And syntax**: The `and` syntax in `par` concurrent blocks is ABSOLUTELY NOT ALLOWED
      - **STRICTLY FORBIDDEN - Stage name syntax**: The `== stage name ==` syntax for activity phases is ABSOLUTELY NOT ALLOWED
      - **Class diagrams - CRITICAL RESTRICTIONS**: 
        - **FORBIDDEN**: `package` syntax is STRICTLY PROHIBITED
        - **FORBIDDEN**: Configuring parameter requirements using `*` symbol is STRICTLY PROHIBITED
      - **Activity diagrams - CRITICAL RESTRICTIONS**:
        - **FORBIDDEN**: `goto` syntax is STRICTLY PROHIBITED
        - **FORBIDDEN**: `note` comments are STRICTLY PROHIBITED
      - **CRITICAL FILE EMBEDDING WARNING**: Direct embedding of PlantUML files (.puml files) will NOT be rendered. PlantUML content MUST be written directly in plantuml code blocks within the markdown content. File references to external PlantUML files are ABSOLUTELY NOT SUPPORTED and will result in no diagram being displayed.
      - **ABSOLUTELY FORBIDDEN - Mermaid syntax mixing**: Mermaid syntax is STRICTLY PROHIBITED within plantuml code blocks. PlantUML and Mermaid have completely different syntax systems and CANNOT be mixed. Any Mermaid syntax within PlantUML blocks will cause rendering failures.
- Formula:
    - Inline Formulas
      Format: $formula content$
      Example: The formula for calculating the area of a circle is $A = \pi r^2$. So ...

    - Block Formulas
      Format requirements:
      Opening delimiter $$ must be on its own line
      Formula content on the middle line(s)
      Closing delimiter $$ must be on its own line

      Correct block formula example:
      `
        plaintext
        The formula for calculating the area of a circle:
        $$ # must in individual line
        A = \pi r^2 # must in another individual line
        $$ # must in another individual line
      `
    - Important Notes
        - Lark's formula format differs from standard Markdown
        - The *three-line structure* for block formulas (opening delimiter, formula content, closing delimiter) must be strictly followed
        - Failure to follow this format will result in parsing errors
- Dividers: `---` or `***`
- Links: `[#text description](url)`
- Link with Preview: `[preview](链接)` # 在[]直接填入"preview"，在()中填入已经部署的链接，会使 link 中的页面在 feishu 文档中被渲染，你需要根据 link 出现的位置以及 link 的内容灵活使用（例如生成的可交互式 HTML 图表可以使用这种方式，但是需要保证使用`deploy`工具，部署过过这个 html 文件，然后这里填入部署后的 url, eg. https://581078988bf7.aime-app.bytedance.net。如果你没有`deploy`工具，则选用下方File with Preview的方式，填写HTML文件地址）。
- Images: `![#text description](image_path)` # ()中只可以写图片的文件路径，不支持写图片的url，即，只支持嵌入本地图片文件，不支持嵌入网络图片。
- File with Preview: `![preview](文件地址)` # 在[]直接填入"preview"，在()中填入文件地址，会使文件的内容在 feishu 文档中被渲染，你需要根据文件类型和出现的位置灵活使用。
  - 注意，Lark report与资源文件不能放在不同的子文件夹下，lark report必须在资源的同一级目录或father directory下
  - 你需要特别注意资源文件与飞书（Lark）报告文件的相对路径，如果他们在同一目录下，你只需要提供文件名即可，如果他们不在同一目录下，你需要提供文件的相对路径。
- Excel File with Preview: `![preview](Excel文件地址)` # 格式与File with Preview相同，在[]直接填入"preview"，在()中填入Excel文件地址，会使Excel文件的内容在飞书文档中自动渲染成飞书电子表格形式。

### Additional Feishu/Lark Document Element Formatting Rules

Please not, when using Feishu Elements, the special element type name should be added directly after triple `, e.g.  ```callout xxx```.
Make sure special format blocks (e.g. callout, table and grid) start with new lines, do not put them directly after the text (may cause format parsing errors).
These special format blocks are only supported in the main body of the report, nesting of special blocks is not allowed.

- Highlight Blocks (callout):
  ```callout
    background_color: ${CalloutBackgroundColor} # 背景颜色,枚举值 1-15
    border_color: ${CalloutBorderColor} # 边框颜色,枚举值 1-7
    emoji_id: ${Emoji} # enum: thought_balloon, speech_balloon, first_place_medal, second_place_medal, third_place_medal, star, bulb
    content:|
        xxxxx # 须符合 yaml 多行字符串语法进行缩进；支持 markdown 粗体/斜体/有序/无序列表、引用、超链接、任务、公式，参考 Lark/Feishu Document Export and Formatting 中的 List formatting 和 Text formatting 部分
  ```
  **务必使用标准的Markdown列表语法**(如`-`或`*`)来创建列表,以确保格式正确渲染。
  **避免非标准符号**:**严禁**使用`•`或`·`其他特殊字符来手动创建列表,这些符号不会被正确渲染为列表项，并且导致列表项挤在一起，失去换行和缩进，严重影响文档的可读性
  **检查缩进和换行**:确保每个列表项前有正确的缩进(通常是2或或4个空格),并且每个列表项都独立成行,以符合Markdown规范。
  **注意：当在表格单元格中使用callout时，需要特殊处理：**
  - 所有换行符必须使用 `<br>` 而不是 `\n`
  - `content:|` 必须写成 `content:\|` （转义竖线字符）
  1. CalloutBackgroundColor 的值是可枚举的
     1 浅红色 8 中红色
     2 浅橙色 9 中橙色
     3 浅黄色 10 中黄色
     4 浅绿色 11 中绿色
     5 浅蓝色 12 中蓝色
     6 浅紫色 13 中紫色
     7 中灰色 14 灰色 15 浅灰色
  2. CalloutBorderColor 的值是可枚举的
     1 红色
     2 橙色
     3 黄色
     4 绿色
     5 蓝色
     6 紫色
     7 灰色

- Grid Layout:
  ```grid
    grid_column: # 分栏的数量仅支持2-3个
    - width_ratio: 50 # 第一个分栏的占比是总宽度的百分之50
      content:|
        xxx # 文本、图片、链接、列表都支持，遵从yaml多行格式的语法
    - width_ratio: 50 # 第二个分栏的占比是总宽度的百分之50
      content:|
        xxx # 文本、图片、链接、列表都支持，遵从yaml多行格式的语法
  ```
  **注意：当在表格单元格中使用grid时，需要特殊处理：**
  - 所有换行符必须使用 `<br>` 而不是 `\n`
  - `content:|` 必须写成 `content:\|` （转义竖线字符）
  attention:
    1. width_rational must be an integer [1:99], and the sum of all width_rational must be less than 100, so make your column 50,50 (two column) or 30,30,40 (three column) are recommended.
    2. When generate multiple grid, generate them one by one. Using any special characters (e.g. ---) to separate the characters will cause a parsing error!
    3. Use grid sparingly! Too many grids will make the report look messy. Maximum 2-3 grids in one report. Maximum 2-3 grids in one report. Maximum 2-3 grids in one report.
    4. Don't use table inside grid, as the cells would be too narrow to display the content.
    5. Texts inside callouts are black, use medium colors with caution and keep good contrast.

## ADD Citations in Feishu/Lark Documents and Reports

- Citations (format as "text content ::cite[ref number]") are provided in the Execution Trace somewhere, you must locate and include citations in your Feishu/Lark report/doc at appropriate location.
- There might be some information in task description and Execution Trace that summarized from the cited information, you have to locate the original citations and add citation back to the Feishu/Lark docs.
- The citation format in your `.lark.md` files must be ::cite[ref number] (e.g. ::cite[50]), for multiple citations, list them one by one (e.g. cite number 23 and 65: ::cite[23] ::cite[65]).
  - here is an example for citation in text (include text in table):
    ...xxx 的数据是 105.5，数据来源是::cite[23]. # one citation
    ...xxx 的数据是 23.4，数据来源是::cite[53] ::cite[65]. # two citations
- Chart citations should be placed in lark doc main context, e.g.:
  - if the figure is an image:
    ![This image shows a chart of sales data.](./output/sales_chart.png)
    The sales data is sourced from ::cite[45] ::cite[78].
  - if the figure is a preview of deployed html link:
    [preview](URL_ADDRESS)
    The above data is sourced from ::cite[2] ::cite[43].
- Forbidden generation of Individual Reference Section, unless the user has clearly preference.
- In report, do not mention how you process the citation format and organize the report, focus on the main story.
- Never wrap ::cite[ref number] in quotes or backquotes, as it will cause parsing error.

## Image Handling in Feishu/Lark Documents and Reports

- For images in the Lark/Feishu document, they must be provided by file path, using: ![description](image_path).
  - Must check image path beforehand, ensure image path is correct relative to the .lark.md file path
- Ensure image descriptions do not contain reference numbers; instead, add data source references separately below the image.
- Example 1: ![This image shows a chart of sales data.](image_path)
- Example 2: ![This image link shows a picture of xxx.](https://xxx.xxx/xxx) # when the resource is an online image link.
- Example 3: When you wanna preview a deployed html file with its link, use [preview](url_of_the_deployed_html).

## Convert .lark.md to Feishu/Lark Doc
- Remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file if need to present to user.
- Remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file if need to present to user.
- Remember to call the `mcp:lark_create_lark_doc` tool to create a Feishu/Lark doc based on the target one `.lark.md` file if need to present to user.