---
# This knowledge significantly affects the performance of newbie with Gemini model series
# Providing a very detailed and well-structured example can help newbie to plan the task better and (also create content rich results)
# Use this knowledge whenever possible
ID: "dynamic_10_lark_creation"
Title: progress example - Lark
EnabledIf: "false"
UsedWhen: when the task needs design and generating lark reports or documents, be aware of this is only for a new lark document creation.
---

Please refer to the following successful task progress plan (in <Example_include_rationale> tag). Your objective is to generate a new, similarly structured, fine-grained task progress plan, also in todo list format, based on the **current task description and available resources**.

<Example_include_rationale>
## Rationale

The user wants a Feishu document generated from the provided text about <PERSON><PERSON><PERSON><PERSON>. The process will involve:

1. **Data Analysis and visualization** through the information from execution trace, I could make: A bar chart will be created to represent the market share data, a line chart for patent trend.
2. **Defining a clear structure for the report** based on the input text. I will re-organize the report structure based on the provided research, to make it more user-friendly and well-structured.
3. **Analyse Data Visualization.** The market share data, and patent trend data will be visualized.
4.  **Carefully transferring the content into a Feishu-compatible markdown format.** This includes ensuring all citations (`::cite[xx]`) are preserved and correctly formatted for Feishu.
5.  **Incorporating the visualization** into the Feishu-compatible markdown document, also, check the path of resource are correctly embedded into the final report, this should follow the ordinary coding knowledge.
6.  **Using appropriate Feishu elements** like `callout` or `grid` to enhance readability and presentation, as per best practices.
7.  **Finally, using the `mcp:lark_create_lark_doc` tool** to convert the markdown file into a Feishu document, double check all the special formats are in feishu grammar.

I will start by outlining the plan using `progress_think`. The plan will detail the sections of the report, the data visualization step, and the steps to generate the final Feishu document.

## Action (progress_think)

```param="progress"
- [ ] **Phase 1: Data Analysis, Report Blueprint & Outline**
  - [x] Identify key data for visualization (image and interactable charts):
    - Market share data: "2023年1-11月在中国淘宝+天猫平台，按销售额计算，德龙的市场占有率为28.9%，位居行业第一. 按销量计算，德龙的市场占有率为13.5%，位居行业第二" (matplot chart).
    - Patent trend data: "2023年1-11月，德龙公司在中国大陆市场获得了10个专利授权，其中发明专利授权数量为5个，实用新型专利授权数量为5个。2024年同期为12个以及15个" (interactable line chart).
  - [x] Define core sections based on the provided "德龙公司调研报告":
    -  `## 1. 公司历史和发展沿革`
    -  `## 2. 主要产品类别和代表性产品`
    -  `## 3. 全球市场表现、主要市场区域和市场份额`
    -  `## 4. 近几年的财务业绩和关键财务指标`
    -  `## 5. 主要竞争对手及其对比分析`
    -  `## 6. 最新的公司动态、新闻报道和行业分析`
    -  `## 7. 公司的技术创新和专利情况`
    -  `## 8. 消费者口碑和评价`
  - [x] Identify potential special elements for enhanced presentation:
    -  Use `callout` to highlight key market share figures or strategic statements.
    -  Consider `grid` for comparing competitor features if data allows for a concise summary.
- [ ] **Phase 2: Data Visualization**
  - [ ] Create a Python script (`create_market_share_chart.py`) to generate a bar chart (`output/market_share_chart.html`) for the market share data.
    - [ ] Extract data: De'Longhi sales share (28.9%), De'Longhi volume share (13.5%). Note: The report mentions "仅次于小熊电器（13.0%）" for volume share, implying Xiaoxiong is first at 13.0% and De'Longhi is second at 13.5%. This seems contradictory or a typo in the source. For the chart, I will represent De'Longhi's stated shares and note the context. Or, more accurately, represent De'Longhi (28.9% sales, 1st) and De'Longhi (13.5% volume, 2nd). For volume, it's better to show De'Longhi vs. "Industry Leader (if not De'Longhi)" or "Next Competitor". The text says "仅次于小熊电器（13.0%）" for volume, meaning Xiaoxiong is 13.0% and De'Longhi is 13.5%. This means De'Longhi is *ahead* of Xiaoxiong. The phrasing "位居行业第二，仅次于小熊电器（13.0%）" is confusing. Let's assume De'Longhi is 13.5% (2nd) and Xiaoxiong is the one at 13.0% (implied 3rd or a typo meaning Xiaoxiong is 1st with a higher share). Given the phrasing "位居行业第二", it's safer to assume there's another brand at #1 for volume. I will create two separate charts or a grouped bar chart for clarity: one for sales share (De'Longhi vs. Rest of Market or vs. #2 if known) and one for volume share (De'Longhi vs. #1 and Xiaoxiong).
    - Simpler approach: Create one chart showing De'Longhi's Sales Share (28.9%) and Volume Share (13.5%) with clear labels indicating their rank.
  - [ ] Use `matplotlib` or `seaborn` for chart generation. Ensure Chinese characters are displayed correctly.
  - [ ] Save the chart as a PNG file (e.g., `output/delonghi_market_share_china_tmall_2023.png`).
  - [ ] Creator a HTML script (`output/patent_trend.html`) to generate the HTML file for the interactable Line Chart for patent trend data.
  - [ ] Use `echart` or `plotly` for the interactable chart generation. Ensure Chinese characters are displayed correctly.
  - [ ] Deploy `patent_trend.html`, prepare the resource to be embedded in the final report.
  - [ ] Make sure no citation in diagram, as one of the knowledge guided.
- [ ] **Phase 3: Content Generation for Feishu Document**
  - [ ] Create the path and content of markdown file: `delonghi_report.lark.md`, path for resource should be relative path.
  - [ ] Populate Section 1: 公司历史和发展沿革.
  - [ ] Populate Section 2: 主要产品类别和代表性产品.
  - [ ] Populate Section 3: 全球市场表现、主要市场区域和市场份额.
    - [ ] Check the path and embed the generated `delonghi_market_share_china_tmall_2023.png` image.
    - [ ] Add a `callout` to highlight the market share statistics.
  - [ ] Populate Section 4: 近几年的财务业绩和关键财务指标.
    - [ ] Use a `callout` for the 2021 and 2023 financial highlights.
  - [ ] Populate Section 5: 主要竞争对手及其对比分析.
    - [ ] Consider using a `grid` or bullet points within a `callout` to summarize key competitor differences if it enhances clarity.
  - [ ] Populate Section 6: 最新的公司动态、新闻报道和行业分析.
  - [ ] Populate Section 7: 公司的技术创新和专利情况.
    - [ ] Embed the deployed url of `patent_trend.html`.
  - [ ] Populate Section 8: 消费者口碑和评价.
  - [ ] Make sure all element with special format meets lark requirements, such as callout, grid, formula etc.
  - [ ] Use appropriate Feishu elements like `callout` or `grid` to enhance readability and presentation, as per best practices.
- [ ] **Phase 4: Review & Feishu Document Generation**
  - [ ] Conduct a thorough review of `delonghi_report.lark.md` for accuracy, logical flow, completeness, clarity, and Feishu markdown compatibility.
  - [ ] If lark document template exists, strictly follow the template’s structure and content. Ensure that no sections or modules from the template are missing in the generated document.
  - [ ] Verify whether all visualizations are correctly embedded and formatted, make sure the paths of all resource are correctly embedded.
  - [ ] Verify all citations are correctly formatted (e.g., `::cite[1]::cite[2]`), and in the right place (no citation in figure).
  - [ ] Use the `mcp:lark_create_lark_doc` command to generate the Feishu document.
```
```param="next_stage"
Proceed with Phase 2: Data Visualization. Create the Python script to generate the market share chart, and HTML script for patent trend chart.
```
</Example_include_rationale>

---
**When generating your new task plan (todo list), please strictly adhere to the following critical instructions:**
1.  **First Action:** Ensure `progress_think` (or a similar planning/thinking step) is your very first action before generating any report content.
2.  **Include Section Design:** Your generated todo list *must* explicitly include tasks or a phase related to "Section Design", make sure your report looks different and contains more demonstration compared to all reference documents generated from previous steps.
3.  **Include Special Element Instructions:** Your todo list *must* contain specific operational instructions for diagrams and other special elements (e.g., `table`, `grid`, `callout`).
4.  **Include Data Visualization:** Your todo list *must* include a task or a phase related to "Data Visualization".
5.  **Originality and Insight:** Avoid directly and entirely repeating content from other files. You must synthesize information based on the current task and resources, and incorporate your own insights and planning.
6.  **No Reference Section:** Reference information only in main content, strictly NO individual reference section in the final report.
7.  **No TOC:** No table of contents in the final report, Feishu document have a table of contents by default.
8.  **Do not try to write script to generate lark.md, please write the document directly**
---
