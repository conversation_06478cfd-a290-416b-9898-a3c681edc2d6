---
ID: "planner_26"  
Title: Agent Copilot Standard Operating Procedure
EnabledIf: "agent in ['dynamic_planner']"  
UsedWhen: When users fix artifact issues, ask questions about artifacts, or resolve lark/feishu document comments.
---

# Standard Operating Procedure

You should refer to this document to develop a similar plan, this task should be completed in single Logical Action Unit (LAU).

[Start of plan example]

- [ ] **Stage 1: Classify user request type**.
  - [ ] Analyze user input to determine if it's:
    - **Issue Fixing**: User wants to fix problems, modify artifacts,
    - **Explanation/Question**: User asks questions about artifacts, needs clarification
  - [ ] Proceed to appropriate stage based on classification. we should first deal with **Explanation/Question**.

- [ ] **Stage 2A: Question Answering Path** (if classified as Explanation/Question).
  - [ ] Use `copilot_gpt` to get question's answer (e.g., template-based checks).
  - [ ] Provide direct answer to user's question with clear explanation, save as markdown.

- [ ] **Stage 2B: Issue Resolution Path** (if classified as Issue Fixing).
  - [ ] Analyze the user trajectory to identify issue reason and solution with `copilot_diagnose`.
  - [ ] Check if the problematic Artifact was generated from a Lark template document. If so, read the relevant sections of the template file and combine with diagnose report to develop better solutions.
  - [ ] Try using any tool to fix the issue, generating the final artifact through patches.
  - [ ] Use `lark`'s update tool update document

[End of Plan Example]

## Mandatory Requirements

### For Question Answering Path (Stage 2A)
1. **Never embed explanations into user artifacts** - provide answers directly in conclusions to users instead.
2. Include relevant context from artifacts when explaining concepts or behaviors.


### For Issue Resolution Path (Stage 2B)
1. The final artifact **MUST** be as same as user's original artifact type such as Lark/Feishu document.
2. If based on lark template document, read the relevant template sections and pass the template content to the Query parameter when using the copilot_diagnose tool.
3. Solutions and fixes should only modify the user-specified parts of the product, **never regenerate it entirely**.
4. This Stage must be completed within single LAU.
