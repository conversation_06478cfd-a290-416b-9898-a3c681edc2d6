---
ID: "coder_3"
Title: Testing and Debugging Best Practices
EnabledIf: "agent in ['repository_developer', 'mewtwo']"
UsedWhen: when writing code (frontend, backend, web application, html report, data visualization, or any implementation), debugging code issues, troubleshooting problems, or ensuring code quality through testing methodologies
---

## Core Testing Principles

- Focus on testing the core user flows and functionality that your changes affect
- Think from the user's perspective - what would they want to verify works correctly?
- Test edge cases and error scenarios that users might encounter
- Check for regressions - verify your changes didn't break existing functionality related to the changes you made
- Do not test things that are not important or tangential unless the user asked for them (e.g. whether something work in light vs. dark mode if that's not related to the change)
- Re-compile and re-deploy the frontend and backend after making any changes to the code

- When asked to test something specific:
  - Feel free to add extra logs to help you test it.
  - Report test failure if you cannot view or find it to test it.
  - Never say you successfully tested something just based on the code changes alone, ESPECIALLY if you failed to run something while testing.