---
ID: "dynamic_41"
Title: artifact issue fix
EnabledIf: "agent in ['mewtwo']"
UsedWhen: |
  Fix Lark/Feishu document and any other artifact issues
---

## Agent Issue Resolution Guide

### 1. Issue Summary
Based on the user's query, sort out multiple issues and generate a concise issue summary, including:
- Overview of each key issue area
- Severity assessment of each identified issue
- Impact evaluation of each issue on overall task completion
- Clear statement and scope definition for each issue
- **Error Classification**: Categorize errors by type (Format errors, Structure errors, Content errors, Data errors.)

### 2. Error Analysis And Root Cause Analysis Guidelines

Use the corresponding guidelines based on error classification, if none use the general guidelines.

#### **Data Error** - When fix data or logic errors/issues, follow workflow:
1. Use `copilot_gpt` tool with Query: "判断这个任务是基于飞书模版文档的吗?如果是,给出飞书模版文档的.larkmd的文件地址"
2. If based on lark template document, read the relevant sections of the template file. Use `copilot_diagnose` tool with Query:
```
{用户修复问题}，分析其计算过程，数据源选择，是否和模版文档要求保持一致。
你必须仔细阅读模版文档对应部分:
{模版文档对应部分的内容}
```

3. If not based on lark template document, Use `copilot_diagnose` tool with Query:
```
{用户修复问题}，分析计算过程中，使用的数据源文件和关键代码文件，以及数据源定位的逻辑和计算的逻辑，是否符合用户的要求”
```

#### **General Error**
Use `copilot_diagnose` tool to analyze the user trajectory to identify error patterns and root causes:

`copilot_diagnose` will return the error analysis result, including:
- **Source Tracing**: Track error origins and propagation pathways through the execution chain
- **Decision Process Review**: Examine the agent's decision-making process and reasoning chains
- **Failure Point Identification**: Pinpoint critical failure points and triggering conditions
- **Environmental Factor Assessment**: Consider external dependencies and environmental influences

### 3. Solution Design
Provide specific, actionable solutions for each identified Issue:
- **Clear Remediation Steps**: Provide detailed, step-by-step repair procedures
- **Precise Modification Targets**: Specify exact locations and content for modifications
- **Rationale and Expected Outcomes**: Explain the underlying principles and anticipated effects of each modification
- **Scope Compliance**: Ensure all modifications remain strictly within the user-specified Issue scope

### 4. Solution Implementation
- Execute the proposed solutions by directly applying fixes to the original artifacts
- Present the final corrected deliverables to the user
- Validate that all identified issues have been properly addressed
- Generating the final artifact through patches, Ensure the artifact type matches the input


## Output Constraints
1. **Evidence-Based Analysis**: Base all analysis strictly on the provided user trajectory and workspace data. Do not speculate about non-existent Issues.
2. **Workspace Utilization**: The workspace contains complete file snapshots from agent execution. All data and scripts used in the repair process must be sourced from the workspace.
3. **Issue Scope Adherence**: Limit all modification suggestions strictly to the Issue areas explicitly mentioned by the user.
4. **Direct Artifact Modification**: Apply fixes directly to the original artifacts without altering the underlying execution logic.
5. Verify that the artifact type matches the input type.

## Modification Constraints
1. **Targeted Modifications**: Only restrict all changes to user-explicitly mentioned Issue areas.
2. **Functional Preservation**: Do not modify unmentioned features, components, or functionalities.
3. **Style Consistency**: Maintain original code style, naming conventions, and architectural patterns.
4. **Change Documentation**: For each modification, provide clear justification and expected outcomes.
5. **Minimal Intervention Principle**: Apply the least invasive changes necessary to resolve the identified issues.