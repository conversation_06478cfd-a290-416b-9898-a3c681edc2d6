package a2acaller

import (
	"embed"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/a2a"
	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/interaction"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	responseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/response"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	queryrecognizer "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/query_preprocessor"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/google/uuid"
	"github.com/samber/lo"

	"trpc.group/trpc-go/trpc-a2a-go/protocol"
)

var (
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")
)

type Agent struct {
	actors.BaseActor
	AgentCardID string

	initialized      bool
	toolCallRequired bool
	taskID           string
	taskStatus       iris.AgentTaskStatus
	reconnectCount   int

	askUserAction iris.Action
	params        map[string]any
	textPart      string
	artifacts     []protocol.Part
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	Card      AgentCard
	Version   string
	ActorStep *iris.AgentRunStep
}

func New(opt CreateOption) *Agent {
	info := iris.AgentInfo{
		Identifier: opt.Card.Name,
		Desc:       getDescription(opt.Card),
	}

	return &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo: info,
			Parameters: []actors.ParameterDescription{
				{
					Name:        "task",
					Description: "Briefly describe the task to complete",
				},
				{
					Name:        "related_files",
					Description: "list the related file names if needed, e.g., \"file1.go\",\"file2.txt\"",
				},
			},
			ExecutionStep: &actors.ExecutionStep{
				Step: opt.ActorStep,
			},
		},
		AgentCardID: opt.Version,
	}
}

func getDescription(card AgentCard) string {
	return lo.Must(promptset.ExecutePrompt("agent_desc", "", map[string]any{
		"Description":  card.Description,
		"Capabilities": card.Capabilities,
		"Skills":       card.Skills,
	}))
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	logger := run.GetLogger()
	logger.Infof("run with input: %s", input)

	// default result
	result = controltool.ConclusionOutput{
		Content:    fmt.Sprintf("The %s Agent is not available", a.AgentCardID),
		Evaluation: controltool.ConclusionEvaluationFailed,
	}

	cli, err := a2a.NewA2AClient("", a.AgentCardID)
	if err != nil {
		run.GetLogger().Errorf("new a2a client failed, err: %v", err)
		return
	}
	a.askUserAction = interaction.NewAskUserAction(interaction.NewAskUserActionOption{
		Timeout:                     300 * time.Second,
		DownloadAttachmentsFilepath: ".",
		RequiredForm:                false,
		RequiredIntentDetect:        false,
	})

	// prepare input
	tags, err := prompt.ParseTopTagsV2(input)
	if err != nil {
		a.textPart = input
		a.artifacts = make([]protocol.Part, 0)
	} else {
		for _, tag := range tags {
			if tag.XMLName.Local == "related_files" {
				fileNames := strings.Split(tag.Content, ",")
				for _, fileName := range fileNames {
					fileName = strings.TrimSpace(fileName)
					// read file content
					content, err := os.ReadFile(fileName)
					if err != nil {
						logger.Errorf("read file %s failed, err: %v", fileName, err)
						continue
					}
					a.artifacts = append(a.artifacts, protocol.FilePart{
						File: &protocol.FileWithBytes{
							Name:  &fileName,
							Bytes: base64.StdEncoding.EncodeToString([]byte(content)),
						},
					})
				}
			}
			if tag.XMLName.Local == "task" {
				a.textPart = tag.Content
			}
		}
	}

	for {
		var recv *stream.RecvChannel[*iris.AgentRunEvent[any]]
		if !a.initialized || a.toolCallRequired {
			a.initialized = true
			a.toolCallRequired = false
			parts := []protocol.Part{
				protocol.TextPart{
					Text: a.textPart,
				},
			}
			parts = append(parts, a.artifacts...)
			recv, err = cli.StreamMessage(run, parts, lo.Ternary(a.taskID == "", nil, &a.taskID))
			if err != nil {
				logger.Errorf("stream message failed, err: %v", err)
				return
			}
		} else {
			recv, err = cli.ResubscribeTask(run, a.taskID)
			if err != nil {
				logger.Errorf("resubscribe task failed, err: %v", err)
				return
			}
		}
		for data := range recv.DataChannel {
			switch data.Event {
			case iris.AgentRunEventTask:
				event := data.Data.(*iris.EventAgentTask)
				a.taskID = event.TaskID
				a.taskStatus = event.Status
				if a.taskStatus == iris.AgentTaskStatusCompleted {
					result.Content = event.Conclusion
					if event.Reference != nil {
						for _, ref := range event.Reference {
							if ref == nil {
								continue
							}

							// 转换为相对路径
							ref.Path = strings.TrimPrefix(ref.Path, "/")

							os.MkdirAll(ref.Path, 0755)
							os.WriteFile(ref.Path, []byte(ref.Content), 0644)
							result.Reference = append(result.Reference, iris.ReferenceItem{
								Title: ref.Description,
								URI:   ref.Path,
							})
						}
					}
				}
				logger.Infof("step: %s", event.Status)
			case iris.AgentRunEventToolCall:
				event := data.Data.(*iris.EventAgentToolCall)
				a.reportToolCall(run, event)
			case iris.AgentRunEventToolCallRequired:
				event := data.Data.(*iris.EventAgentToolCallRequired)
				logger.Infof("tool call required: %s", event.Parameters)
				a.taskStatus = iris.AgentTaskStatusInputRequired
				a.params = event.Parameters
			case iris.AgentRunEventStepArtifact:
				event := data.Data.(*iris.EventAgentStepArtifact)
				logger.Infof("step artifact: %s", event.Content)
			}
		}
		if a.isFinished(run, &result) {
			break
		}
	}
	return result
}

func (a *Agent) reportToolCall(run *iris.AgentRunContext, event *iris.EventAgentToolCall) {
	publisher := run.GetPublisher()
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: a.Name(),
		Parent:        a.ExecutionStep.Step,
	})
	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)
	outputs := event.Outputs
	err := errors.New(event.Error)
	defer func() {
		step.Finish(outputs, err)
		publisher.ReportToolCall(step, iris.ToolCallStatusCompleted, event.Description)
		publisher.ReportStep(step)
	}()

	step.Inputs = event.Inputs
	step.Action = &iris.DumbAction{
		ActionInfo: iris.ActionInfo{
			ActionName:        event.Tool,
			ActionDescription: "",
		},
	}
	publisher.ReportToolCall(step, iris.ToolCallStatusStarted, event.Description)
}

func (a *Agent) reportToolCallRequired(run *iris.AgentRunContext, params map[string]any) bool {
	publisher := run.GetPublisher()
	var (
		answer interaction.AskUserOutput
		err    error
	)

	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: a.Name(),
		Parent:        a.ExecutionStep.Step,
	})
	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)

	defer func() {
		outputs := util.ValueToMap(answer)
		step.Finish(outputs, err)
		publisher.ReportToolCall(step, iris.ToolCallStatusCompleted, "")
		publisher.ReportStep(step)
	}()

	question := conv.DefaultAny[string](params["text"])

	step.Inputs = map[string]any{
		"message": question,
	}
	step.Action = a.askUserAction
	publisher.ReportToolCall(step, iris.ToolCallStatusStarted, "")

	err = a.askUserAction.Execute(run, step)
	if err != nil {
		a.textPart = "failed to ask user"
		return false
	}
	if err = mapstructure.Decode(step.Outputs, &answer); err != nil {
		a.textPart = "got invalid ask user answer, please try again"
		return false
	}
	a.textPart = answer.Answer + answer.Form
	if answer.Attachments != nil {
		a.artifacts = make([]protocol.Part, 0)
		for _, attachment := range answer.Attachments {
			content, err := os.ReadFile(attachment.Path)
			if err != nil {
				logger.Errorf("read file %s failed, err: %v", attachment.Path, err)
				continue
			}
			a.artifacts = append(a.artifacts, protocol.FilePart{
				File: &protocol.FileWithBytes{
					Name:  &attachment.Path,
					Bytes: base64.StdEncoding.EncodeToString(content),
				},
			})
		}
	}

	// add to conversation
	msgQuestion := uuid.New().String()
	run.State.Conversation.AddAgentMessage(&iris.Message{
		ID:      msgQuestion,
		From:    iris.MessageResponse,
		Content: question,
	})
	if answer.MessageID != "" {
		userMessage := &iris.Message{
			ID:          answer.MessageID,
			From:        iris.MessageFromUser,
			Content:     answer.Answer,
			Attachments: answer.Attachments,
			Mentions: lo.Map(answer.Mentions, func(mention entity.Mention, _ int) iris.Mention {
				return iris.GetMention(mention)
			}),
		}
		_ = queryrecognizer.RewriteMentions([]*iris.Message{userMessage})
		run.State.Conversation.AddAgentMessage(userMessage)

		// ack user's response
		harmful, _, replyTo, response := responseactor.AckAndRespond(run)
		messageID := uuid.New().String()
		run.State.Conversation.AddAgentMessage(&iris.Message{
			ID:      messageID,
			From:    iris.MessageResponse,
			Content: response,
		})
		if harmful {
			run.State.Conversation.MarkDeprecated(messageID)
		}

		logger.Infof("ack ask user response: %s", response)
		publisher.ReportMessage(iris.EventAgentMessage{
			ID:      uuid.New().String(),
			ReplyTo: replyTo,
			Content: conv.DefaultAny[string](response),
		})
	}

	return answer.IsBreak
}

func (a *Agent) isFinished(run *iris.AgentRunContext, result *controltool.ConclusionOutput) bool {
	switch a.taskStatus {

	case iris.AgentTaskStatusCompleted:
		result.Evaluation = controltool.ConclusionEvaluationSuccess
		return true

	case iris.AgentTaskStatusFailed:
		result.Evaluation = controltool.ConclusionEvaluationFailed
		return true

	case iris.AgentTaskStatusInputRequired:
		a.toolCallRequired = true
		return a.reportToolCallRequired(run, a.params)

	case iris.AgentTaskStatusRunning, iris.AgentTaskStatusCreated:
		a.reconnectCount += 1
		if a.reconnectCount >= 3 {
			result.Evaluation = controltool.ConclusionEvaluationFailed
			logger.Errorf("reconnect count >= 3, exit")
			return true
		}

	default:
		result.Evaluation = controltool.ConclusionEvaluationFailed
		return true
	}
	return false
}

type AgentCard struct {
	// identifier for the agent card
	ID string `json:"id"`
	// The version of the A2A protocol this agent supports
	ProtocolVersion string `json:"protocolVersion"`
	// Human readable name of the agent
	Name string `json:"name"`
	// A human-readable description of the agent, used to assist users and other agents in understanding what the agent can do
	Description string `json:"description"`
	// A URL to the address the agent is hosted at. This represents the preferred endpoint as declared by the agent
	URL string `json:"url"`
	// The transport of the preferred endpoint. If empty, defaults to JSONRPC
	PreferredTransport string `json:"preferredTransport,omitempty"`
	// A URL to an icon for the agent
	IconUrl string `json:"iconUrl,omitempty"`
	// The version of the agent - format is up to the provider
	Version string `json:"version"`
	// A URL to documentation for the agent
	DocumentationUrl string `json:"documentationUrl,omitempty"`
	// Optional capabilities supported by the agent
	Capabilities AgentCapabilities `json:"capabilities"`
	// Supported media types for input
	DefaultInputModes []string `json:"defaultInputModes"`
	// Supported media types for output
	DefaultOutputModes []string `json:"defaultOutputModes"`
	// Skills are a unit of capability that an agent can perform
	Skills []*AgentSkill `json:"skills"`
	// True if the agent supports providing an extended agent card when the user is authenticated
	// Defaults to false if not specified
	SupportsAuthenticatedExtendedCard bool `json:"supportsAuthenticatedExtendedCard,omitempty"`
	// Parameter description (JSON schema or similar)
	ParameterDescription map[string]string `json:"parameterDescription,omitempty"`
	// Schedule mode
	ScheduleMode int64 `json:"scheduleMode"`
	// Timestamp when this agent card was created
	CreatedAt time.Time `json:"createdAt"`
	// Timestamp when this agent card was last updated
	UpdatedAt time.Time `json:"updatedAt"`
}

// AgentSkill represents a specific skill or function of the agent.
type AgentSkill struct {
	// A unique identifier for the skill.
	ID string `json:"id"`
	// The name of the skill.
	Name string `json:"name"`
	// A short description of the skill.
	Description string `json:"description"`
	// Tags associated with the skill.
	Tags []string `json:"tags"`
	// Examples of how to use the skill.
	Examples []string `json:"examples,omitempty"`
	// Supported input modes for the skill.
	InputModes []string `json:"inputModes,omitempty"`
	// Supported output modes for the skill.
	OutputModes []string `json:"outputModes,omitempty"`
}

// AgentCapabilities describes the capabilities of the agent.
type AgentCapabilities struct {
	// Indicates if the agent supports streaming.
	Streaming *bool `json:"streaming,omitempty"`
	// Indicates if the agent supports push notifications.
	PushNotifications *bool `json:"pushNotifications,omitempty"`
	// Indicates if the agent provides state transition history.
	StateTransitionHistory *bool `json:"stateTransitionHistory,omitempty"`
}
