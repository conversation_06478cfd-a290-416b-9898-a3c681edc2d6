package actors

import (
	"github.com/samber/lo"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type Actor interface {
	Name() string
	Description() string
	// 输入的参数
	GetParametersDescription(ctx *iris.AgentRunContext) []ParameterDescription
	// 输入为半结构化的数据，输出为半结构化的结论
	// 例：
	// 输入：
	// ```
	// <script>
	// pnpm run dev
	// </script>
	// <watch>
	// until service started
	// <watch>
	// ```
	// 输出：
	// ```
	// <conclusion>
	// Failed to start the service:
	// err: xxx
	// </conclusion>
	// ```
	Run(ctx *iris.AgentRunContext, input string, options RunOptions) (result controltool.ConclusionOutput)
}

type WrappedActor interface {
	Actor
	SetExecutionStep(step *iris.AgentRunStep)
}

type RunOptions struct {
	ReuseTrace       bool
	DynamicPersona   string
	ParentExecutor   string
	ToolKnowledgesID []string
	SysKnowledgesID  []string
}

type ParameterDescription struct {
	Name        string
	Description string
}

type BaseActor struct {
	Actor
	iris.AgentInfo
	Parameters    []ParameterDescription
	ExecutionStep *ExecutionStep
}

type ExecutionStep struct {
	Step *iris.AgentRunStep
}

var _ Actor = &BaseActor{}

func (a *BaseActor) SetExecutionStep(step *iris.AgentRunStep) {
	a.ExecutionStep.Step = step
}

func (a *BaseActor) Name() string {
	return a.Identifier
}

func (a *BaseActor) Description() string {
	return a.Desc
}

const (
	TaskBriefKey = "task_brief"
)

func (a *BaseActor) GetParametersDescription(ctx *iris.AgentRunContext) []ParameterDescription {
	builtInParameters := []ParameterDescription{}
	// 	{
	// 		Name:        TaskBriefKey,
	// 		Description: fmt.Sprintf("The brief description of the sub-task assigned to agent, output in %s by default, unless there is an explicit language specified in the task", agents.GetUserLanguage(ctx.Parameters)),
	// 	},
	// }

	isBuiltInConflict := lo.ContainsBy(a.Parameters, func(parameter ParameterDescription) bool {
		return lo.Contains(builtInParameters, parameter)
	})
	if isBuiltInConflict {
		ctx.GetLogger().Warnf("built-in parameter %s is conflict with the custom parameter %s", builtInParameters, a.Parameters)
		return a.Parameters
	}
	return append(builtInParameters, a.Parameters...)
}
