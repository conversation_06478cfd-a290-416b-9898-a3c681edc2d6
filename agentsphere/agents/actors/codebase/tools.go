package codebaseactor

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-git/go-git/v5/config"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/go-git/go-git/v5/plumbing/transport/client"
	githttp "github.com/go-git/go-git/v5/plumbing/transport/http"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/panics"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/projectartifact"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory/project_memory"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/port/codebase"
)

var (
	ToolGitClone           = "git_clone"
	ToolGitCommit          = "git_commit"
	ToolGitPush            = "git_push"
	ToolSubmitMergeRequest = "submit_merge_request"

	ToolGitCloneDescription           = "Clone a git repository from codebase/github with given name and branch/tag/commit. Only this tool has access to user's ssh key."
	ToolGitCommitDescription          = "Commit changes to a git repository"
	ToolSubmitMergeRequestDescription = "Submit a merge request to codebase"
	ToolGitPushDescription            = "Push changes to a git repository"

	GitHubProxy = lo.Must(url.Parse("http://sys-proxy-rd-relay.byted.org:3128"))
)

type GitCloneArgs struct {
	Directory     string             `json:"directory,omitempty" mapstructure:"directory" description:"directory to clone to. The current directory (i.e., .) is not allowed. If not specified, the repository will be cloned into a directory named after the repository (i.e., the part after the last '/') in the current directory. For example, cloning 'a/b' will create a directory named 'b' in the current directory."`
	RepoPath      string             `json:"repo_path" mapstructure:"repo_path" description:"required, path of the repository to clone, can be a repo url in https or ssh format or a repo name. If you unsure about the repo name, please use the repo url."`
	Platform      entity.GitPlatform `json:"platform" mapstructure:"platform" description:"required, codebase(ByteDance private) or github(open source)"`
	ReferenceName string             `json:"reference_name,omitempty" mapstructure:"reference_name" description:"branch name/tag/commit sha to clone, defaults to remote default branch(main/master/other custom branch)"`
	UnShallow     bool               `json:"unshallow,omitempty" mapstructure:"unshallow" description:"whether to clone all history commits, default is false which is shallow clone"`
}

type GitCloneOutputs struct {
	RepoName string `json:"repo_name" mapstructure:"repo_name" description:"name of the repository"`
	Message  string `json:"message" mapstructure:"message" description:"message of the clone operation"`
}

func NewGitCloneAction(enabledBytedRepos bool) iris.Action {
	return actions.ToTool(ToolGitClone, ToolGitCloneDescription, GitClone)
}

func GitClone(run *iris.AgentRunContext, args GitCloneArgs) (*GitCloneOutputs, error) {
	uninstall := installGitHubProxy()
	defer uninstall()
	shallow := !args.UnShallow
	ws, term := workspace.GetWorkspace(run), workspace.GetTerminal(run, "")
	term.PrintCommandPrompt(exec.Command(lo.Ternary(args.Platform == entity.GitPlatformCodebase, "codebase", "git"), "clone", args.RepoPath, args.ReferenceName))

	url := args.RepoPath
	repoName := args.RepoPath
	info, parseError := workspace.ParseRepoPath(args.RepoPath)
	if parseError == nil && info != nil {
		run.GetLogger().Infof("parsed repo info: %+v", info)
		repoName = info.RepoName
		if args.Platform == "" {
			args.Platform = info.Platform
		}
	}

	codebasePlatform := "gitlab"
	switch args.Platform {
	case entity.GitPlatformCodebase:
		url = fmt.Sprintf("<EMAIL>:%s", repoName)
		cli, err := NewCodebaseClient(run)
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to create codebase client"))
		}

		// 1. check user permission
		role, err := cli.GetRepoUserRole(run, run.User.Username, repoName)
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "user do not have permission to clone %s", repoName))
		}
		clonePermissionRoles := []string{"master", "owner", "developer", "reporter"}
		if !lo.Contains(clonePermissionRoles, role) {
			return nil, iris.NewRecoverable(errors.Errorf("user do not have permission to clone %s", repoName))
		}

		// 2. get repo info to check if it's gerrit repo
		repoInfo, err := cli.GetRepo(run, repoName)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get repo info")
		}

		if strings.Contains(repoInfo.Platform, "gerrit") {
			codebasePlatform = "gerrit"
			url = fmt.Sprintf("<EMAIL>:%s", repoName)
			run.GetLogger().Infof("gerrit repo detected: %s %s", repoName, repoInfo.GitURL)
		}

	case entity.GitPlatformGithub:
		url = fmt.Sprintf("https://github.com/%s", repoName)
	}
	dir := lo.Ternary(args.Directory != "", args.Directory, path.Base(repoName))
	option := workspace.CloneOption{
		Directory: dir,
		URL:       url,
		Checkout:  args.ReferenceName,
		Shallow:   shallow,
		Terminal:  term,
		Progress:  false,
	}
	if args.Platform == entity.GitPlatformCodebase {
		option = option.WithSSHCredential(codebasePlatform, run.GetEnv(entity.RuntimeEnvironUserCodebaseJWT))
	}
	run.GetLogger().Infof("clone option: %+v", option)
	start := time.Now()
	repo, err := workspace.CloneRepository(run, option)
	_ = metrics.AR.ToolGitCloneCost.WithTags(&metrics.ToolGitCloneTag{
		RepoName: repoName,
		Platform: string(args.Platform),
		Shallow:  shallow,
		Success:  err == nil,
	}).Observe(float64(time.Since(start).Milliseconds()))
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to clone %s", repoName))
	}

	// load project memory
	memory, err := loadProjectMemory(run, repoName, repo.Directory)
	if err != nil {
		run.GetLogger().Warnf("unable to load repo memory: %v", err)
		repo.Memory = nil
	} else {
		repo.Memory = memory
	}

	ws.AddRawRepository(repo)
	// cosy set up
	if err = setupCosy(run, filepath.Join(workspace.GetEditor(run).WorkingDirectory, repo.Directory), 10*time.Second); err != nil {
		run.GetLogger().Warnf("unable to setup cosy: %v", err)
	}

	var message string
	if args.ReferenceName == "" {
		// 没有指定 ref，只克隆了默认分支
		message = lo.Ternary(shallow,
			fmt.Sprintf("Cloned 1 latest commit from %s (%s platform) to `%s` directory (default branch only). Execute `git fetch --unshallow` if history commits is required, or `git fetch origin <branch/tag>` to fetch other branches/tags.", repoName, args.Platform, dir),
			fmt.Sprintf("Successfully cloned %s (%s platform) to `%s` directory (default branch only). Execute `git fetch origin <branch/tag>` to fetch other branches/tags.", repoName, args.Platform, dir),
		)
	} else {
		// 指定了 ref
		message = lo.Ternary(shallow,
			fmt.Sprintf("Cloned 1 latest commit from %s (%s platform) to `%s` directory, checked out to %s. Execute `git fetch --unshallow` if history commits is required to complete the task.", repoName, args.Platform, dir, args.ReferenceName),
			fmt.Sprintf("Successfully cloned %s (%s platform) to `%s` directory, checked out to %s.", repoName, args.Platform, dir, args.ReferenceName),
		)
	}

	if args.Platform == entity.GitPlatformCodebase {
		projectartifact.GetProjectArtifactManager(run).MarkCodebaseProjectArtifact(run, projectartifact.CodebaseRepo{
			Name:     repo.Directory,
			RepoName: repoName,
			Platform: codebasePlatform,
			URL:      url,
		})
	}
	if _, err = projectartifact.GetProjectArtifactManager(run).ProcessProjectArtifacts(run, iris.ProjectReference{
		{
			Name: repo.Directory,
			Path: filepath.Join(workspace.GetEditor(run).WorkingDirectory, repo.Directory),
		},
	}, projectartifact.StashProjectArtifactsOptions...); err != nil {
		run.GetLogger().Errorf("failed to process project artifacts: %v", err)
	}

	// add repo memory
	if repo.Memory != nil {
		message += "\n\n" + repo.Memory.String()
	}

	return &GitCloneOutputs{
		RepoName: repoName,
		Message:  message,
	}, nil
}

// setupCosy 异步设置 cosy，并设置最长 setupTimeout 秒的等待时间
func setupCosy(run *iris.AgentRunContext, workspace string, setupTimeout time.Duration) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(run, setupTimeout)
	defer cancel()
	// 启动一个 goroutine 来执行 cosy 设置
	setupDone := make(chan error, 1)
	go panics.Try(func() {
		defer close(setupDone)
		setupDone <- runCosySetup(run, workspace)
	})
	// 等待设置完成或超时
	select {
	case err := <-setupDone:
		if err != nil {
			run.GetLogger().Errorf("cosy setup completed with error: %v", err)
			return err
		}
		run.GetLogger().Infof("cosy setup completed successfully")
		return nil
	case <-ctx.Done():
		// 超时后继续执行，但记录日志
		run.GetLogger().Infof("cosy setup did not complete within 10s, continuing anyway...")
		return nil
	}
}

// runCosySetup 执行实际的 cosy 设置命令
func runCosySetup(run *iris.AgentRunContext, repo string) error {
	// 检查 repo 路径
	if strings.TrimSpace(repo) == "" {
		return fmt.Errorf("cosy setup repository path cannot be empty")
	}
	// 准备 cosy 设置命令
	cmd := exec.Command("/usr/local/cosy/dgit-cosy-local", "setup",
		"--serve-config", "/usr/local/cosy/config.cosy_rpc.local.toml",
		"--workspace", fmt.Sprintf("local://%s", repo),
		"--wait")
	// 获取命令输出
	run.GetLogger().Infof("cosy setup start, command: %s", cmd.String())
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("cosy setup failed: %v, output: %s", err, string(output))
	}
	run.GetLogger().Infof("cosy setup output: %s", string(output))
	return nil
}

type GitPushArgs struct {
	Directory string `json:"directory,omitempty" mapstructure:"directory" description:"directory to push, default is current directory"`
	Refspec   string `json:"refspec,omitempty" mapstructure:"refspec" description:"refspec to push"`
	Force     bool   `json:"force,omitempty" mapstructure:"force" description:"force push"`
}

type GitPushOutputs struct{}

func NewGitPushAction() iris.Action {
	return actions.ToTool(ToolGitPush, ToolGitPushDescription, func(run *iris.AgentRunContext, args GitPushArgs) (*GitPushOutputs, error) {
		ws := workspace.GetWorkspace(run)
		repo := ws.GetRepository(args.Directory)
		if repo == nil {
			return nil, iris.NewRecoverable(errors.New(fmt.Sprintf("repository not found in directory %s", args.Directory)))
		}
		refspec := config.RefSpec(args.Refspec)
		if refspec.Validate() != nil {
			return nil, iris.NewRecoverable(fmt.Errorf("invalid refspec: '%s', example: 'refs/heads/<branch-name>:refs/heads/<branch-name>' or 'refs/tags/<tag-name>:refs/tags/<tag-name>' or '<commit-sha>:refs/heads/<branch-name>'", args.Refspec))
		}

		err := repo.Push(run, workspace.PushOption{
			RefSpec: refspec,
			Force:   args.Force,
		})
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to push to %s", args.Refspec))
		}
		return &GitPushOutputs{}, nil
	})
}

type GitCommitArgs struct {
	Directory string   `json:"directory,omitempty" mapstructure:"directory" description:"directory to commit, default is current directory"`
	Title     string   `json:"title" mapstructure:"title" description:"title of the commit"`
	Body      string   `json:"body,omitempty" mapstructure:"body" description:"body of the commit"`
	Add       []string `json:"add,omitempty" mapstructure:"add" description:"files to add to the commit"`
}

type GitCommitOutputs struct {
	CommitSHA string `json:"commit_sha" mapstructure:"commit_sha" description:"sha of the commit"`
}

func NewGitCommitAction() iris.Action {
	return actions.ToTool(ToolGitCommit, ToolGitCommitDescription, func(run *iris.AgentRunContext, args GitCommitArgs) (*GitCommitOutputs, error) {
		ws := workspace.GetWorkspace(run)
		repo := ws.GetRepository(args.Directory)
		if repo == nil {
			return nil, iris.NewRecoverable(errors.New("repository not found"))
		}
		if len(args.Add) > 0 {
			add := append([]string{"add"}, args.Add...)
			cmd := exec.Command("git", add...)
			cmd.Dir = args.Directory
			err := cmd.Run()
			if err != nil {
				return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to add files to the commit"))
			}
		}
		commit, err := repo.Commit(workspace.CommitOption{
			Message: workspace.CommitMessageOption{
				Title: args.Title,
				Body:  args.Body,
			},
			Author: &object.Signature{
				Name:  run.User.Username,
				Email: run.User.Username + "@bytedance.com",
				When:  time.Now(),
			},
			CoAuthors: <AUTHORS>
				{
					Name:  "Genie",
					Email: "<EMAIL>",
					When:  time.Now(),
				},
			},
		})
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to commit changes"))
		}
		return &GitCommitOutputs{
			CommitSHA: commit.Hash.String(),
		}, nil
	})
}

type SubmitMergeRequestArgs struct {
	RepoName     string `json:"repo_name" mapstructure:"repo_name" description:"name of the repository to submit merge request"`
	SourceBranch string `json:"source_branch" mapstructure:"source_branch" description:"source branch to submit merge request"`
	TargetBranch string `json:"target_branch" mapstructure:"target_branch" description:"target branch to submit merge request"`
	Title        string `json:"title" mapstructure:"title" description:"title of the merge request"`
	Description  string `json:"description" mapstructure:"description" description:"description of the merge request"`
	Draft        bool   `json:"draft" mapstructure:"draft" description:"whether to create a draft merge request, defaults to true"`
}

type SubmitMergeRequestOutputs struct {
	MergeRequestURL string `json:"merge_request_url" mapstructure:"merge_request_url" description:"url of the merge request"`
}

func NewSubmitMergeRequestAction(enabledBytedRepos bool) iris.Action {
	return actions.ToTool(ToolSubmitMergeRequest, ToolSubmitMergeRequestDescription, func(run *iris.AgentRunContext, args SubmitMergeRequestArgs) (*SubmitMergeRequestOutputs, error) {
		if !enabledBytedRepos {
			return nil, iris.NewRecoverable(errors.New("codebase repos are not supported"))
		}
		isDraft := true
		if !args.Draft {
			isDraft = false
		}

		cli, err := NewCodebaseClient(run)
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to create codebase client"))
		}
		mr, err := cli.CreateMergeRequest(run, args.RepoName, codebase.CreateMergeRequestOption{
			SourceBranch: args.SourceBranch,
			TargetBranch: args.TargetBranch,
			Title:        args.Title,
			Description:  args.Description + "\n\n\n Suggested-By: [Aime](https://aime.bytedance.net)",
			Draft:        isDraft,
		})
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to submit merge request to %s", args.RepoName))
		}
		telemetry.EmitSubmitMergeRequest(run, args.RepoName, telemetry.MergeRequestTypeCodebase, strconv.FormatInt(mr.ID, 10))
		return &SubmitMergeRequestOutputs{
			MergeRequestURL: mr.ExternalURL,
		}, nil
	})
}

const (
	codebaseAPIBaseURL = "https://codebase-api.byted.org"
)

func NewCodebaseClient(run *iris.AgentRunContext) (codebase.Client, error) {
	baseURL := lo.Ternary(run.GetEnv(entity.RuntimeEnvironCodebaseAPIBaseURL) != "", run.GetEnv(entity.RuntimeEnvironCodebaseAPIBaseURL), codebaseAPIBaseURL)
	return codebase.NewClient(baseURL, run.GetEnv(entity.RuntimeEnvironUserCodebaseJWT), time.Second*15)
}

func installGitHubProxy() (uninstall func()) {
	// Codebase repos can only be cloned via SSH
	// It's safe to install global proxy for open source repos
	oldTransport := client.Protocols["https"]
	client.InstallProtocol("https", githttp.NewClient(&http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyURL(GitHubProxy),
		},
	}))
	return func() {
		client.InstallProtocol("https", oldTransport)
	}
}

// loadProjectMemory loads repository memory from aime.md and CLAUDE.md files
func loadProjectMemory(run *iris.AgentRunContext, repoName string, repoDirectory string) (*project_memory.ProjectMemory, error) {
	logger := run.GetLogger()

	// Define memory files to load
	memoryFiles := []string{"AGENT.md", "AGENTS.md"}

	// Load and merge memories from the specified files
	result, err := project_memory.LoadAndMergeMultipleMemories(run, memoryFiles, repoName, repoDirectory, logger)
	if err != nil {
		logger.Warnf("unable to load repo memory: %v", err)
		return nil, err
	}
	return result, nil
}
