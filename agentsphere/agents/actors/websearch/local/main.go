package main

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	actorstest "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/testhelper"
	planact_agent "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/agent"
	"code.byted.org/devgpt/kiwis/lib/config"

	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/devai"

	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	runtimeentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"gopkg.in/yaml.v3"
)

type Report struct {
	Answer  string `json:"answer"`
	UseTime int    `json:"use_time"`
}

type Config struct {
	LarkToken    string `yaml:"LarkToken"`
	DefaultModel string `yaml:"DefaultModel"`
	Models       []struct {
		Scene string `yaml:"Scene"`
		Model string `yaml:"Model"`
	} `yaml:"Models"`
}

func main() {
	var (
		configPath string
	)
	flag.StringVar(&configPath, "config", "config.yaml", "config file path")
	flag.Parse()

	// Load config
	configFile, err := os.ReadFile(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to read config file: %v\n", err)
		os.Exit(1)
	}
	var conf Config
	err = yaml.Unmarshal(configFile, &conf)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to parse config file: %v\n", err)
		os.Exit(1)
	}

	larkToken := conf.LarkToken
	if len(larkToken) > 0 {
		err := checkLarkToken(larkToken)
		fmt.Printf("visit https://open.larkoffice.com/document/server-docs/docs/drive-v1/export_task/download?appId=cli_9a31b280a1f3d101 to refresh token")
		if err != nil {
			panic(err)
		}
	} else {
		log.Warnf("lark token is empty, bytedance search is disabled")
	}
	agent := websearch.New(websearch.CreateOption{
		EnableBytedSearch: len(larkToken) > 0,
	})
	agent.Local = true
	start := time.Now()

	// Prepare models by scene
	models := map[string]string{}
	for _, m := range conf.Models {
		models[m.Scene] = m.Model
	}

	wd, _ := os.Getwd()
	runtime := actorstest.SetupTestRuntime(actorstest.SetupTestRuntimeOption{
		WorkDir:   wd,
		SessionID: "local_session_id",
		APIServer: "https://aime.bytedance.net/api/agents/v2",
	})
	runtime.RegisterAgent(planact_agent.NewPlanActAgent(planact_agent.AgentIDV1))

	// The following oConfig types are not defined, so we use the config values directly
	// and pass them to the agent as needed. If you need to use specific types, import or define them.

	// For demonstration, we skip sceneSpec and related code, and just use conf.DefaultModel
	agentRunConfig := &config.AgentRunConfig{}
	agentRunConfig.ModelScenesConfig.SceneSpecifications = nil
	for _, m := range conf.Models {
		agentRunConfig.ModelScenesConfig.SceneSpecifications = append(agentRunConfig.ModelScenesConfig.SceneSpecifications, config.ScenesModelConfig{
			Scenes:      []string{m.Scene},
			ModelConfig: config.ModelDetailConfig{Name: m.Model},
		})
	}
	runContext, err := runtime.CreateRunContext(context.Background(), runtimeentity.RunAgentRequest{
		AgentName: planact_agent.AgentIDV1,
		Config:    agentRunConfig,
		Environ: map[string]string{
			runtimeentity.RuntimeEnvironUserCloudJWT: "",
			runtimeentity.RunTimeLarkUserAccessToken: larkToken,
		},
		ExitOnFinish: false,
	})
	if err != nil {
		panic(err)
	}
	//var a struct {
	//	Task     string   `json:"Task"`
	//	SubTasks []string `json:"SubTasks"`
	//}
	//err = json.NewDecoder(os.Stdin).Decode(&a)
	runContext.Parameters[runtimeentity.RuntimeParametersDatasetID] = "1fa6cf98-f5b6-4291-9ebf-fc9daa892956"
	runContext.State.CurrentStep = &iris.AgentRunStep{StepID: "step_1"}
	response, err := agent.InternalKnowledgeBaseSearch(runContext,
		[]string{"libra实验详情接口文档", "结构水位法代码实现", "bc联动商详维度相关文档"})
	if err != nil {
		panic(err)
	}
	fmt.Printf("final answer:\n%s\n", response.FinalReport)
	report := Report{
		Answer:  response.FinalReport,
		UseTime: int(time.Now().Sub(start).Seconds()),
	}
	marshal, err := json.Marshal(report)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%s", string(marshal))
}

func checkLarkToken(token string) error {
	r := devai.CheckLarkTokenRequest{
		Token: token,
	}
	reqPayload, err := json.Marshal(r)
	if err != nil {
		panic(err)
	}
	req, err := http.NewRequestWithContext(context.Background(), http.MethodPost,
		"https://bitsai.bytedance.net/openapi/knowledge/v1/debug/check_lark_token", bytes.NewReader(reqPayload))
	if err != nil {
		return err
	}
	req.Header.Set("content-type", "application/json")
	do, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	if do.StatusCode != http.StatusOK {
		respBody, err := io.ReadAll(do.Body)
		if err != nil {
			return err
		}
		return errors.Errorf("Bad Status: %s, body: %s", do.Status, string(respBody))
	}
	return nil
}
