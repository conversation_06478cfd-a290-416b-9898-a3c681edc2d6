package response

import (
	"embed"
	"fmt"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/toolset"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"github.com/samber/lo"
)

var (

	//go:embed explain_system.go.tmpl
	explainSystemPrompt         string
	ExplainSystemPromptTemplate = prompt.MustGetTemplate("ExplainSystemPrompt", explainSystemPrompt)
	//go:embed explain_user.go.tmpl
	explainUserPrompt         string
	ExplainUserPromptTemplate = prompt.MustGetTemplate("ExplainUserPrompt", explainUserPrompt)

	//go:embed conclusion_system.go.tmpl
	conclusionSystemPrompt         string
	ConclusionSystemPromptTemplate = prompt.MustGetTemplate("ConclusionSystemPrompt", conclusionSystemPrompt)
	//go:embed conclusion_user.go.tmpl
	conclusionUserPrompt         string
	ConclusionUserPromptTemplate = prompt.MustGetTemplate("ConclusionUserPrompt", conclusionUserPrompt)

	//go:embed thought.go.tmpl
	thoughtPrompt         string
	ThoughtPromptTemplate = prompt.MustGetTemplate("ThoughtPrompt", thoughtPrompt)

	//go:embed *.go.tmpl
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, ".")
)

func Conclude(run *iris.AgentRunContext, result string) (conluded string) {
	span, ctx := run.GetTracer(run).
		StartCustomSpan(run, agentrace.SpanTypeStep, "response_conclude",
			agentrace.WithObjectSpanData(map[string]any{"result": result}),
		)
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"concluded": conluded,
		}))
		span.Finish()
	}()
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: "response_conclude",
	})
	message := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: prompt.TemplateSet{
			SystemTmpl:  ConclusionSystemPromptTemplate,
			UserTmpl:    ConclusionUserPromptTemplate,
			ThoughtTmpl: ThoughtPromptTemplate,
		},
		SystemPromptVariables: map[string]any{
			"Language": agents.GetUserLanguage(run.Parameters),
		},
		UserPromptVariables: map[string]any{
			"Conversations": lo.Reduce(run.State.Conversation.GetMessages(), func(agg string, msg *iris.Message, index int) string {
				return agg + lo.Ternary(msg.From == iris.MessageFromUser || msg.From == iris.MessageResult, fmt.Sprintf("%s:\n%s\n\n", msg.From, msg.Content), "")
			}, ""),
			"Results": result,
		},
	})
	content, _ := agents.Think(run, "response_conclude", message,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)
	step.Conclusion["conclusion"] = content
	step.Finish(step.Conclusion, nil)
	return content.Content
}

func AckAndRespond(run *iris.AgentRunContext) (harmful bool, intercepted bool, replyTo string, response string) {
	messages := run.State.Conversation.GetMessages()
	lastUserMessage, _, ok := lo.FindLastIndexOf(messages, func(msg *iris.Message) bool {
		return msg.From == iris.MessageFromUser
	})
	if ok {
		replyTo = lastUserMessage.ID
	} else {
		run.GetLogger().Warn("no user message found")
		// todo: then what?
	}

	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: "explain",
	})
	span, ctx := run.GetTracer(run).
		StartCustomSpan(run, agentrace.SpanTypeStep, "explain",
			agentrace.WithSpanID(step.StepID),
			agentrace.WithObjectSpanData(map[string]any{"last_user_message": lastUserMessage}),
		)
	run = run.WithContext(ctx)
	defer func() {
		span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
			"harmful":     harmful,
			"intercepted": intercepted,
			"reply_to":    replyTo,
			"response":    response,
		}))
		span.Finish()
	}()

	userDefineToolsets, err := toolset.GetUserDefineToolsets(run)
	if err != nil {
		run.GetLogger().Errorf("failed to get user define toolsets, err: %s", err)
	}
	toolsets, err := toolset.GetToolsets(run, step)
	if err != nil {
		run.GetLogger().Errorf("failed to get toolsets, err: %s", err)
	}
	isProjectSpace := entity.CheckParamIsProjectSpace(run.Parameters[entity.RuntimeParametersSpaceInfomation])
	message := prepExplainPrompt(ExplainPromptOption{
		Conversation:       run.State.Conversation,
		Parameters:         run.Parameters,
		UserDefineToolsets: userDefineToolsets,
		Toolsets:           toolsets,
		IsProjectSpace:     isProjectSpace,
	})
	content, err := agents.Think(run, "intent_detect", message,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)
	step.Thought = &iris.Thought{
		Content: content.Content,
		LLMCall: iris.LLMCall{
			ModelName:    content.Model,
			Temperature:  content.Temperature,
			Prompt:       message,
			Usage:        content.Usage,
			FinishReason: content.FinishReason,
			TraceID:      content.TraceID,
		},
	}
	if err != nil {
		run.GetLogger().Errorf("failed to detect intent, err: %s", err)
	}

	run.GetPublisher().ReportThought(step)
	step.Conclusion["conclusion"] = content
	step.Finish(step.Conclusion, nil)

	run.GetPublisher().ReportStep(step)
	run.GetLogger().Infof("%s step finished: %s, step status %s,parent: %s, %s", step.ExecutorAgent, step.Status, step.StepID, step.Parent, step.ExecutorAgent)

	// 这种时候不直接用 rawResponse，而是用一个兜底 msg，因为可能被攻击了
	fallbackResponse := "ok~"
	if len(content.Content) == 0 {
		response = fallbackResponse
	}

	var decision string
	if strings.Contains(content.Content, "<result>") {
		content := strings.TrimSpace(strings.TrimPrefix(strings.TrimSuffix(content.Content, "</result>"), "<result>"))
		tags, err := prompt.ParseTopTagsV2(content)
		if err == nil {
			tag, ok := lo.Find(tags, func(tag prompt.Tag) bool {
				return strings.HasPrefix(tag.XMLName.Local, "decision")
			})
			if ok {
				decision = tag.Content
			}
			resp, ok := lo.Find(tags, func(tag prompt.Tag) bool {
				return strings.HasPrefix(tag.XMLName.Local, "response")
			})
			if ok {
				response = resp.Content
			}
		}
	} else {
		lines := strings.SplitN(content.Content, "\n", 2)
		if len(lines) < 2 {
			return false, false, replyTo, response
		}
		detection := strings.Split(strings.TrimSpace(lines[0]), ":")
		if len(detection) != 3 {
			return false, false, replyTo, fallbackResponse
		}
		decision = detection[2]
		response = lines[1]
	}

	switch decision {
	case "pause":
		intercepted = true
	case "continue":
		intercepted = false
	default:
		intercepted = false // todo: 这里要不要写true
	}
	if response == "" {
		response = "ok~" // 这种时候不直接用rawResponse，而是用一个兜底msg，因为可能被攻击到了？
	}
	if strings.Contains(content.Content, "ask_for_hack") {
		harmful = true
	}

	mentions := lo.FlatMap(messages, func(msg *iris.Message, _ int) []iris.Mention {
		if msg.From == iris.MessageFromUser {
			return msg.Mentions
		}
		return nil
	})
	// if model outputs mention prompt string, replace it with display string as it's readable to the user
	lo.ForEach(mentions, func(mention iris.Mention, _ int) {
		response = strings.ReplaceAll(response, mention.PromptString(), mention.DisplayString())
	})

	return harmful, intercepted, replyTo, response
}

type ExplainPromptOption struct {
	Conversation       *iris.Conversation
	Parameters         map[entity.RuntimeParameterKey]any
	UserDefineToolsets []toolset.Toolset
	Toolsets           []toolset.Toolset
	IsProjectSpace     bool
}

func prepExplainPrompt(opt ExplainPromptOption) []*framework.ChatMessage {
	messages, _ := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(ExplainSystemPromptTemplate, map[string]any{
			"Language":           agents.GetUserLanguage(opt.Parameters),
			"UserDefineToolsets": opt.UserDefineToolsets,
			"Toolsets":           opt.Toolsets,
		}),
		prompt.WithUserMessage(ExplainUserPromptTemplate, map[string]any{
			"Conversations": lo.Reduce(opt.Conversation.GetMessages(), func(agg string, msg *iris.Message, index int) string {
				currentMsg := lo.Ternary(msg.From == iris.MessageFromUser || msg.From == iris.MessageResponse, fmt.Sprintf("%s:\n%s\n\n", msg.From, msg.Content), "")
				if msg.From == iris.MessageFromUser && len(msg.Attachments) > 0 {
					currentMsg += fmt.Sprintf("Attachments:\n%s", lo.Map(msg.Attachments, func(attachment iris.Attachment, _ int) string {
						return attachment.Path + "\n"
					}))
				}
				return agg + currentMsg
			}, ""),
			"UserQueryAttachments": getUserQueryAttachmentsDesc(opt.Conversation),
			"IsProjectSpace":       opt.IsProjectSpace,
		}),
	})
	return messages
}

// getUserQueryAttachmentsDesc 参考(a *PlanActAgent) updatePlannerRequirements实现的，todo：搬到server的话要换数据源
func getUserQueryAttachmentsDesc(conversation *iris.Conversation) (userQueryAttachments string) {
	newMessages := conversation.GetDirtyMessage()
	if len(newMessages) == 0 {
		return ""
	}

	for _, message := range newMessages {
		for _, attachment := range message.Attachments {
			userQueryAttachments += attachment.Path + "\n"
		}
	}

	return userQueryAttachments
}
