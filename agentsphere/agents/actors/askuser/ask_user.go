package askuser

import (
	_ "embed"
	"fmt"
	"strings"
	"text/template"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/interaction"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	responseactor "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/response"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/tracing"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	queryrecognizer "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/query_preprocessor"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/lib/util"
)

var (
	Identifier = "ask_user"
	//go:embed description.go.tmpl
	description string
	parameters  = []actors.ParameterDescription{
		{
			Name:        "previous_request",
			Description: "Verbatim request submitted by the user.",
		},
		{
			Name:        "question",
			Description: "Detailed but concise questions that you want the user to answer, clarify, and make clear.",
		},
	}

	//go:embed system.go.tmpl
	systemPrompt         string
	systemPromptTemplate *template.Template
	//go:embed user.go.tmpl
	userPrompt         string
	userPromptTemplate *template.Template
	//go:embed thought.go.tmpl
	thoughtPrompt         string
	thoughtPromptTemplate *template.Template
)

type Agent struct {
	actors.BaseActor
	AskUser *AskUser
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	ActorStep *iris.AgentRunStep
}

func New(opt CreateOption) *Agent {
	info := iris.AgentInfo{
		Identifier: Identifier,
		Desc:       string(description),
	}

	askUser := &AskUser{
		AgentInfo: info,
		action: interaction.NewAskUserAction(interaction.NewAskUserActionOption{
			Timeout:                     0,
			DownloadAttachmentsFilepath: "",
			RequiredForm:                false,
			RequiredIntentDetect:        false,
		}),
	}
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parameters,
			ExecutionStep: &actors.ExecutionStep{
				Step: opt.ActorStep,
			},
		},
		AskUser: askUser,
	}

	return agent
}

type AskUserStore struct {
	CurrentTask string
	AgentStepID string
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	run.GetLogger().Infof("ask user run with input: %s", input)
	iris.UpdateStoreByKey(run, a.AskUser.UID(), AskUserStore{
		CurrentTask: input,
	})
	err := a.AskUser.RunWithOption(run, &agents.ReActAgentRunOption{
		Step: a.ExecutionStep.Step,
	})
	if err != nil {
		result = controltool.ConclusionOutput{
			Content:    err.Error(),
			Evaluation: controltool.ConclusionEvaluationFailed,
		}
		return result
	}
	lastStep := run.State.LastStep()
	mapstructure.Decode(lastStep.Conclusion, &result)
	result.Evaluation = controltool.ConclusionEvaluationSuccess
	return result
}

type AskUser struct {
	iris.AgentInfo
	uid    string
	action iris.Action
}

func (r *AskUser) UID() string {
	if r.uid == "" {
		r.uid = fmt.Sprintf("%s-agent-%s", r.Identifier, util.RandomString(8))
	}
	return r.uid
}

var _ iris.RunnableAgent = &AskUser{}

func (r *AskUser) Run(run *iris.AgentRunContext) error {
	publisher := run.GetPublisher()
	logger := run.GetLogger()
	store := iris.RetrieveStoreByKey[AskUserStore](run, r.UID())
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: r.Name(),
		Parent:        lo.Ternary(store.AgentStepID != "", run.GetStep(store.AgentStepID), nil),
	})

	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	// generate question to user
	thought, err := r.think(run)
	if err != nil {
		return err
	}

	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)

	defer func() {
		last := run.State.LastStep()
		step.Finish(last.Conclusion, last.Error)
		publisher.ReportStep(step)
	}()

	question := strings.TrimSpace(thought.Content)

	// get user's answer
	logger.Infof("asking user question: %s", question)
	step.Inputs = map[string]any{"message": question}
	step.Action = r.action
	publisher.ReportToolCall(step, iris.ToolCallStatusStarted, "asking user question")
	defer func() {
		publisher.ReportToolCall(step, iris.ToolCallStatusCompleted, "user ask completed")
	}()
	err = r.action.Execute(run, step)
	if err != nil {
		return errors.WithMessage(err, "failed to ask user")
	}
	var answer interaction.AskUserOutput
	if err := mapstructure.Decode(step.Outputs, &answer); err != nil {
		return errors.WithMessage(err, "got invalid ask user answer, please try again")
	}

	logger.Infof("user answer: %v", answer)

	// get attachments
	artifact := run.GetArtifactService()
	artifactsInfoBuilder := strings.Builder{}
	if !artifact.IsNil() && len(answer.Attachments) > 0 {
		artifactsInfoBuilder.WriteString("## Attachments Files:\n")
		attachments := make(map[string][]string)
		for _, attachment := range answer.Attachments {
			attachments[attachment.ArtifactID] = append(attachments[attachment.ArtifactID], attachment.Path)
		}
		files := []iris.ArtifactFile{}
		for artifactID, paths := range attachments {
			downloaded, err := artifact.DownloadFiles(run, iris.ArtifactDownloadOptions{
				ArtifactID: artifactID,
				Files:      paths,
				Directory:  ".",
			})
			if err != nil {
				logger.Errorf("failed to download attachment %s-%s: %v", artifactID, paths, err)
				continue
			}
			files = append(files, downloaded...)
			for _, file := range downloaded {
				artifactsInfoBuilder.WriteString("./" + file.Path + "\n")
			}
		}
		logger.Infof("downloaded %d attachments", len(files))
	} else {
		logger.Warnf("artifact service is nil or no attachments, skipping %d attachment download", len(answer.Attachments))
	}

	var conclusion string

	// add to conversation
	msgQuestion := uuid.New().String()
	run.State.Conversation.AddAgentMessage(&iris.Message{
		ID:      msgQuestion,
		From:    iris.MessageResponse,
		Content: question,
	})
	if answer.MessageID != "" {
		userMessage := &iris.Message{
			ID:          answer.MessageID,
			From:        iris.MessageFromUser,
			Content:     answer.Answer,
			Attachments: answer.Attachments,
			Mentions: lo.Map(answer.Mentions, func(mention entity.Mention, _ int) iris.Mention {
				return iris.GetMention(mention)
			}),
		}
		_ = queryrecognizer.RewriteMentions([]*iris.Message{userMessage})
		run.State.Conversation.AddAgentMessage(userMessage)

		// ack user's response
		harmful, _, replyTo, response := responseactor.AckAndRespond(run)
		messageID := uuid.New().String()
		run.State.Conversation.AddAgentMessage(&iris.Message{
			ID:      messageID,
			From:    iris.MessageResponse,
			Content: response,
		})
		if harmful {
			run.State.Conversation.MarkDeprecated(messageID)
		}

		logger.Infof("ack ask user response: %s", response)
		publisher.ReportMessage(iris.EventAgentMessage{
			ID:      uuid.New().String(),
			ReplyTo: replyTo,
			Content: conv.DefaultAny[string](response),
		})
		run.GetTelemetryTracer().StartSpan(run, "ask_user_response", map[string]interface{}{
			"type":    tracing.SpanEventResponse,
			"message": response,
		}, tracing.SpanTypeEvent, nil)
		conclusion = fmt.Sprintf(`# User's answer
%s
%s`, userMessage.Content, artifactsInfoBuilder.String())
		logger.Infof("ask user conclusion: %s", conclusion)
	} else {
		conclusion = answer.Form
	}

	step.Conclusion = map[string]any{"content": conclusion}
	return nil
}

func (r *AskUser) think(run *iris.AgentRunContext) (*iris.Thought, error) {
	messages := r.Compose(run)

	content, err := agents.Think(run, r.Identifier, messages,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    50,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
		},
	)
	if err != nil {
		return nil, errors.WithMessage(err, "llm api not available")
	}

	thought := &iris.Thought{
		Content: content.Content,
		LLMCall: iris.LLMCall{
			ModelName:    content.Model,
			Temperature:  content.Temperature,
			Usage:        content.Usage,
			Prompt:       messages,
			FinishReason: content.FinishReason,
			TraceID:      content.TraceID,
		},
	}
	run.UpdateThought(thought)
	return thought, nil
}

func (r *AskUser) RunWithOption(run *iris.AgentRunContext, opt *agents.ReActAgentRunOption) error {
	publisher := run.GetPublisher()
	store := iris.RetrieveStoreByKey[AskUserStore](run, r.UID())
	// the step represents the agent's task
	// it will be used as the parent step for all the steps created by the agent
	step := opt.Step
	if step == nil {
		return errors.New("step is nil")
	}
	store.AgentStepID = step.StepID
	iris.UpdateStoreByKey(run, r.UID(), store)

	// push current step to steps
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	originalStep := run.State.CurrentStep
	run.State.CurrentStep = nil
	defer func() {
		last := run.State.LastStep()
		step.Finish(last.Conclusion, last.Error)
		publisher.ReportStep(step)
		run.State.CurrentStep = originalStep
	}()

	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)
	return r.Run(run)
}

func (r *AskUser) Compose(run *iris.AgentRunContext) []*framework.ChatMessage {
	store := iris.RetrieveStoreByKey[AskUserStore](run, r.UID())

	systemPromptTemplate = prompt.MustGetTemplate("system_brief", systemPrompt)
	userPromptTemplate = prompt.MustGetTemplate("user", userPrompt)
	thoughtPromptTemplate = prompt.MustGetTemplate("thought", thoughtPrompt)
	templates := prompt.TemplateSet{
		SystemTmpl:  systemPromptTemplate,
		UserTmpl:    userPromptTemplate,
		ThoughtTmpl: thoughtPromptTemplate,
	}

	messages := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: templates,
		SystemPromptVariables: map[string]any{
			"Language": agents.GetUserLanguage(run.Parameters),
		},
		UserPromptVariables: map[string]any{
			"Content": store.CurrentTask,
		},
		MemoryRounds: 20,
	})
	return messages
}
