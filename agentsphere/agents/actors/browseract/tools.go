package browseract

import (
	"bytes"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"io/ioutil"
	"math"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/websearch"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"

	"github.com/disintegration/imaging"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.com/mark3labs/mcp-go/mcp"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

const (
	ToolBrowserScreenshot              = "browser_screenshot"
	ToolBrowserClearScreenshot         = "browser_clear_screenshot"
	ToolBrowserState                   = "browser_get_state"
	ToolGoto                           = "browser_goto"
	ToolQuickExtraction                = "browser_goto_and_extraction"
	ToolBrowserDownload                = "browser_download"
	ToolBrowserMarkdownifyContent      = "browser_markdownify_content"
	ToolBrowserGetDom                  = "browser_get_dom"
	ToolBrowserAskDoubaoAndFetchAnswer = "browser_ask_doubao_and_fetch_answer"
	ToolBrowserRemoveHighlights        = "browser_remove_highlights"
	ToolBrowserHandoverToVlm           = "browser_handover_to_vlm"
	ToolBrowserGetLibraScreenshot      = "browser_get_libra_screenshot_and_data"
	ToolBrowserGetAeolusScreenshot     = "browser_get_aeolus_screenshot_and_data"
	ToolBrowserGetMeegoScreenshot      = "browser_get_meego_screenshot_and_data"
	ToolBrowserTake                    = "browser_takeover"
	ToolBrowserExecuteScript           = "browser_execute_script"
	ToolBrowserSaveCookies             = "browser_save_cookies"
)

var (
	// some tools are used for internal control, we don't want to expose them to the LLM
	disabledTools []string
)

type BrowserAction struct {
	Name       string         `json:"name"`
	Parameters map[string]any `json:"parameters"`
}

// screenshot store is used to store the screenshot of the browser
type ScreenshotStore struct {
	ArtifactID string `json:"artifact_id"`
}

type BrowserStateStore struct {
	BrowserState map[string]any `json:"browser_state"`
}

func rejectContinuingBrowserTake(run *iris.AgentRunContext, toolName string) (bool, string) {
	mem := memory.GetAgentMemory(run)
	if toolName != ToolBrowserTake || len(mem.ActionMemory) <= 1 {
		return false, ""
	}

	for i := len(mem.ActionMemory) - 2; i >= 0; i-- {
		step := mem.ActionMemory[i]
		if step.Thought == nil {
			continue
		}
		if strings.HasPrefix(step.Thought.Tool, "browser_") {
			if step.Thought != nil && step.Thought.Tool == ToolBrowserTake {
				reason := conv.DefaultAny[string](step.Thought.Parameters["reason"])
				return true, reason
			} else {
				return false, ""
			}
		}
	}
	return false, ""
}

func getBrowser(run *iris.AgentRunContext, service *browser.MCPClient, r *mcp.CallToolResult, e error, toolName string) (*browser.ScreenshotArtifact, *browser.ScreenshotArtifact, map[string]any) {
	logger := run.GetLogger()
	var gif *mcp.ImageContent
	if e == nil && r != nil && (toolName == ToolBrowserAskDoubaoAndFetchAnswer || toolName == ToolBrowserHandoverToVlm) {
		imageList := mcptool.ExtractImageContent(r.Content)
		if len(imageList) > 0 {
			gif = &imageList[0]
		} else {
			gif = nil
		}
	}

	ctx, cancel := context.WithTimeout(run, 1*time.Minute)
	defer cancel()
	browserState, browserStateErr := getBrowserState(run.ForkWithContext(ctx), service)
	ctx, cancel = context.WithTimeout(run, 1*time.Minute)
	defer cancel()
	screenshot, screenshotErr := takeScreenshot(run.ForkWithContext(ctx), service, false, gif)
	ctx, cancel = context.WithTimeout(run, 1*time.Minute)
	defer cancel()
	clearScreenshot, clearScreenshotErr := takeScreenshot(run.ForkWithContext(ctx), service, true, gif)
	if screenshotErr != nil {
		logger.Errorf("failed to take screenshot: %v", screenshotErr)
	}
	if clearScreenshotErr != nil {
		logger.Errorf("failed to take clear screenshot: %v", clearScreenshotErr)
	}
	if browserStateErr != nil {
		logger.Errorf("failed to get browser state: %v", browserStateErr)
	}
	return screenshot, clearScreenshot, browserState
}

func NewBrowserToolset(run *iris.AgentRunContext) (tools []iris.Action, err error) {
	service, err := browser.NewBrowserMCPClient(run)
	if err != nil {
		run.GetLogger().Errorf("failed to new a browser mcp service")
		return nil, err
	}
	resp, err := service.Client.ListTools(run, mcp.ListToolsRequest{})
	if err != nil {
		return nil, err
	}

	disabledTools = []string{
		ToolBrowserScreenshot,
		ToolBrowserClearScreenshot,
		ToolBrowserState,
		ToolBrowserGetDom,
		ToolBrowserRemoveHighlights,
		ToolBrowserSaveCookies,
	}

	return lo.FilterMap(resp.Tools, func(tool mcp.Tool, _ int) (iris.Action, bool) {
		if lo.Contains(disabledTools, tool.Name) {
			return nil, false
		}
		inputSchema, err := mcptool.FromMCPSchema(tool.InputSchema)
		if err != nil {
			run.GetLogger().Errorf("failed to convert mcp tool schema %s: %v", tool.Name, err)
			return nil, false
		}
		action := actions.NewTool(actions.NewToolOption{
			Name:        tool.Name,
			Description: tool.Description,
			Input:       inputSchema,
			Impl: func(run *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
				logger := run.GetLogger()
				var (
					ctx    context.Context
					cancel context.CancelFunc
				)
				if reject, reason := rejectContinuingBrowserTake(run, tool.Name); reject {
					return nil, iris.NewRecoverable(errors.Errorf("Do not ask user to takeover browser consecutively for '%s' and try alternative methods to conitnue task", reason))
				}
				var timeout time.Duration
				if tool.Name == ToolBrowserMarkdownifyContent || tool.Name == ToolBrowserHandoverToVlm ||
					tool.Name == ToolBrowserGetAeolusScreenshot || tool.Name == ToolBrowserExecuteScript ||
					tool.Name == ToolQuickExtraction || tool.Name == ToolBrowserGetMeegoScreenshot {
					timeout = 30 * time.Minute
				} else if tool.Name == ToolBrowserDownload {
					timeout = 30 * time.Minute
				} else if tool.Name == ToolBrowserGetLibraScreenshot {
					timeout = 60 * time.Minute
				} else {
					timeout = 3 * time.Minute
				}
				ctx, cancel = context.WithTimeout(run, timeout)
				defer cancel()

				var result = map[string]any{
					"name": tool.Name,
				}
				logger.Debugf("[browser] start to call tool %s with input %s", tool.Name, step.Inputs)
				arguments := rewriteArguments(run, tool.Name, step.Inputs)

				if tool.Name == ToolGoto {
					parsed, err := url.Parse(conv.DefaultAny[string](arguments["url"]))
					if err != nil {
						// 不是一个正常的url
						result["error"] = "url is illegal and do not use browser_goto to visit a illegal url"
						return result, iris.NewRecoverable(err)
					}
					if parsed.Scheme == "file" {
						ext := filepath.Ext(parsed.Path)
						if lo.Contains([]string{".csv", ".txt", ".docx", ".pptx"}, ext) {
							result["error"] = fmt.Sprintf("local file with extensions like csv, txt, docx and pptx cannot be open by browser")
							return result, iris.NewRecoverable(errors.New("local file with extensions like csv, txt, docx and pptx cannot be open by browser"))
						}
						filePath := parsed.Host + "/" + parsed.Path
						_, err = os.Stat(filePath)
						if err != nil {
							// 无法打开
							result["error"] = fmt.Sprintf("local file %s cannot be opened", filePath)
							return result, iris.NewRecoverable(err)
						}
					}
				}
				callRequest := mcp.CallToolRequest{}
				callRequest.Params.Name = tool.Name
				callRequest.Params.Arguments = arguments
				resp, err := service.Client.CallTool(run.ForkWithContext(ctx), callRequest)

				// ensure we take screenshot and get browser state even if the tool call fails
				defer func(r *mcp.CallToolResult, e error, toolName string) {

					screenshot, clearScreenshot, browserState := getBrowser(run, service, r, e, toolName)

					if tool.Name == ToolBrowserTake {
						cubeID := os.Getenv("CUBE_ID")
						clusterEnv := ""
						wildcardDomain := os.Getenv("CUBE_WILDCARD_DOMAIN")
						if wildcardDomain != "" {
							domainItems := strings.Split(wildcardDomain, ".")
							if len(domainItems) > 0 {
								clusterEnv = domainItems[0]
							}
						}
						if cubeID != "" && clusterEnv != "" {
							if responseString := conv.DefaultAny[map[string]any](result["response"]); responseString != nil && conv.DefaultAny[bool](responseString["success"]) {
								content := conv.DefaultAny[string](responseString["message"])
								askSaveCookies := conv.DefaultAny[bool](responseString["ask_save_cookies"])

								askUserToTakeQuestion := AskUserInput{
									TakeReason:     content,
									StreamUrl:      fmt.Sprintf("https://strato-https-proxy.bytedance.net/%s.%s/novnc/vnc.html?autoconnect=true&reconnect=true&reconnect_delay=1000&password=password&resize=scale", cubeID, clusterEnv),
									Timeout:        3600, // 60 mins
									AskSaveCookies: askSaveCookies,
								}
								logger.Infof("start to ask user to take browser, input: %s", askUserToTakeQuestion)
								ctx, cancel = context.WithTimeout(run, 3*time.Minute)
								defer cancel()
								removeHighlightsErr := removeHighlights(run.ForkWithContext(ctx), service)
								if removeHighlightsErr != nil {
									logger.Errorf("failed to remove highlights: %v", removeHighlightsErr)
								}
								ctx, cancel := context.WithTimeout(run, 3600*time.Second)
								defer cancel()
								takeOutputs, err := askHumanToTake(run.ForkWithContext(ctx), askUserToTakeQuestion, step.Parent)
								if err != nil {
									takeOutputs["error"] = err
									conv.DefaultAny[map[string]any](result["response"])["success"] = false
									conv.DefaultAny[map[string]any](result["response"])["error"] = err.Error()
								}
								screenshot, clearScreenshot, browserState = getBrowser(run, service, r, e, toolName)

								confirmHumanTake(run, takeOutputs, step.Parent, result, clearScreenshot, browserState, service)

								result["screenshot"] = screenshot
								result["screenshot_without_highlights"] = clearScreenshot
								result["browser_state"] = browserState

								return
							}
						}
					}

					result["screenshot"] = screenshot
					result["screenshot_without_highlights"] = clearScreenshot
					result["browser_state"] = browserState
				}(resp, err, tool.Name)
				if errors.Is(err, context.DeadlineExceeded) {
					logger.Infof("tool call timed out, taking screenshot and browser state")
					result["error"] = fmt.Sprintf("waiting for browser response exceeded %.0f minutes", timeout.Minutes())
					return result, iris.NewRecoverable(err)
				}
				if err != nil {
					logger.Errorf("failed to call tool %s: %v", tool.Name, err)
					return result, iris.NewRecoverable(err)
				}

				logger.Debugf("[browser] succeed to call tool %s with input %s", tool.Name, step.Inputs)
				logger.Infof("[browser] strat to extract text content")
				texts := mcptool.ExtractTextContent(resp.Content)
				if resp.IsError {
					logger.Errorf("browser tool %s returned error: %v", tool.Name, texts)
					result["error"] = texts
					return result, iris.NewRecoverable(errors.New(strings.Join(texts, ",")))
				}
				logger.Infof("[browser] succeed to extract text content")
				var response map[string]any
				// now only get first elem
				err = json.Unmarshal([]byte(texts[0]), &response)
				if err != nil {
					result["message"] = texts
				} else {
					result["response"] = response
				}
				if respErr, ok := response["error"]; ok {
					if errStr, ok := respErr.(string); ok {
						logger.Errorf("[browser] browser use return a response err: %s", errStr)
						return result, iris.NewRecoverable(errors.New(errStr))
					}
				} else {
					// no error
					if tool.Name == ToolBrowserMarkdownifyContent || tool.Name == ToolQuickExtraction {
						if conv.DefaultAny[bool](response["need_login"]) {
							return result, nil
						}
					}
				}
				return result, nil
			},
		})
		if action.ActionName == ToolBrowserMarkdownifyContent || action.ActionName == ToolQuickExtraction {
			// extraction和create file的toolcall completed时序问题
			action = actions.WithPostRunHook(action, func(run *iris.AgentRunContext, step *iris.AgentRunStep, output map[string]any) error {
				response := conv.DefaultAny[map[string]any](output["response"])
				if response == nil {
					return iris.NewRecoverable(errors.New("failed to obtain extraction of webpage"))
				}
				if conv.DefaultAny[bool](response["need_login"]) {
					return nil
				}
				if conv.DefaultAny[bool](response["pdf"]) {
					return nil
				}
				run.GetLogger().Infof("run post hook of extraction")
				run.GetLogger().Infof("response of extraction: %s", response)
				mkResult := conv.DefaultAny[string](response["result"])
				refs := iris.ParseReference(conv.DefaultAny[string](response["result"]))
				content, refs := websearch.UpdateReference(run, mkResult, refs, "search")
				fileName := conv.DefaultAny[string](response["file_name"])
				networkFilesAny := conv.DefaultAny[[]interface{}](response["network_files"])

				var networkFiles []string
				for _, fileAny := range networkFilesAny {
					file := conv.DefaultAny[string](fileAny)
					networkFiles = append(networkFiles, file)
				}

				run.GetLogger().Infof("Get network files: %s", networkFiles)
				finalFileName := generateFileName(run, fileName, "")

				if _, err := reportResult(run, content, iris.ReferenceSimpleString(refs), finalFileName, step); err != nil {
					return iris.NewRecoverable(err)
				}

				var csvFiles []string
				networkMessage := []string{}
				if networkFiles != nil && len(networkFiles) > 0 {
					goal := conv.DefaultAny[string](response["goal"])
					csvFiles = extractNetworkFiles(run, networkFiles, goal, mkResult, step)
					for _, file := range csvFiles {
						networkMessage = append(networkMessage, file)
					}
				}
				response["result"] = fmt.Sprintf("Extracted Content result in file(s) below: %s\n%s", finalFileName, strings.Join(networkMessage, "\n\n"))
				return nil
			})
			return action, true
		}
		return action, true
	}), nil
}

func generateFileName(run *iris.AgentRunContext, originFileName string, suffix string) string {
	filePath := originFileName

	if suffix == "" && !strings.HasSuffix(strings.ToLower(filePath), ".md") {
		if lastDot := strings.LastIndex(filePath, "."); lastDot != -1 {
			filePath = filePath[:lastDot]
		}
		filePath = filePath + ".md"
	}

	markdownDir := "/workspace/iris_" + run.GetEnv(entity.RuntimeEnvironSessionID)
	counter := 1
	for {
		fullPath := path.Join(markdownDir, filePath)
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			break
		}
		// 有重复，自动加编号
		filePath = fmt.Sprintf("%d_%s", counter, originFileName)
		counter++
	}

	run.GetLogger().Infof("generate file name %s", filePath)
	return filePath
}

func reportResult(run *iris.AgentRunContext, result, refs, fileName string, step *iris.AgentRunStep) (string, error) {
	publisher := run.GetPublisher()

	extractStep := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: step.ExecutorAgent,
	})
	extractStep.Parent = step.Parent
	extractStep.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(extractStep)

	extractStep.Action = &actions.Tool{
		ActionInfo: iris.ActionInfo{
			ActionName: string(workspace.ActionCreateFile),
			ActionDescription: lo.Ternary(
				agents.GetUserLanguage(run.Parameters) == "Chinese (Simplified)",
				"正在总结调查结果",
				"Summarizing web data results",
			),
		},
	}

	publisher.ReportToolCall(extractStep, iris.ToolCallStatusStarted, "create file for web page investigation result")

	content := fmt.Sprintf("%s\n\n# Reference\n%s", result, refs)

	extractStep.Inputs = map[string]any{
		"file_path": fileName,
		"content":   content,
	}

	createFileAction := workspace.NewCreateFileAction(workspace.CreateFileOption{})

	err := createFileAction.Execute(run, extractStep)

	publisher.ReportToolCall(extractStep, iris.ToolCallStatusCompleted, "created file for web page investigation result")

	if err != nil {
		extractStep.Status = iris.AgentRunStepStatusFailed
		extractStep.Error = err
		publisher.ReportStep(extractStep)
		return "", err
	} else {
		extractStep.Status = iris.AgentRunStepStatusSuccess
		publisher.ReportStep(extractStep)
	}

	return fileName, nil
}

func removeHighlights(run *iris.AgentRunContext, service *browser.MCPClient) error {
	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = ToolBrowserRemoveHighlights
	callRequest.Params.Arguments = map[string]any{}
	_, err := service.Client.CallTool(run, callRequest)
	return err
}

func takeScreenshot(run *iris.AgentRunContext, client *browser.MCPClient,
	withoutHighlights bool, providedImage *mcp.ImageContent) (screenshot *browser.ScreenshotArtifact, err error) {
	logger := run.GetLogger()
	var image mcp.ImageContent
	if !withoutHighlights {
		var (
			resp *mcp.CallToolResult
		)
		callRequest := mcp.CallToolRequest{}
		callRequest.Params.Name = ToolBrowserScreenshot
		callRequest.Params.Arguments = map[string]any{}
		resp, err = client.Client.CallTool(run, callRequest)
		if err != nil {
			return nil, err
		}
		images := mcptool.ExtractImageContent(resp.Content)
		if len(images) == 0 {
			logger.Info("browser screenshot: no image found")
			return nil, nil
		}
		logger.Infof("browser screenshot: found %d images: %s", len(images), images[0].MIMEType)
		image = images[0]
	} else {
		if providedImage == nil {
			var (
				resp *mcp.CallToolResult
			)
			callRequest := mcp.CallToolRequest{}
			callRequest.Params.Name = ToolBrowserClearScreenshot
			callRequest.Params.Arguments = map[string]any{}
			resp, err = client.Client.CallTool(run, callRequest)

			if err != nil {
				return nil, err
			}
			images := mcptool.ExtractImageContent(resp.Content)
			if len(images) == 0 {
				logger.Info("browser screenshot: no image found")
				return nil, nil
			}
			logger.Infof("browser screenshot: found %d images: %s", len(images), images[0].MIMEType)
			image = images[0]
		} else {
			logger.Infof("browser screenshot: found providedImage: %s", providedImage.MIMEType)
			image = *providedImage
		}
	}

	localPath := image.Data
	imageData, err := readImageData(localPath)
	if err != nil {
		return nil, err
	}

	if !withoutHighlights && run.GetConfig().GetVariantByScene(Identifier) == agententity.VariantIntern {
		if err := compressImage(run, localPath); err != nil {
			logger.Errorf("failed to compress image: %s", err)
			return nil, err
		}
	}
	path := path.Base(localPath)
	screenshot = &browser.ScreenshotArtifact{
		ID:        "",
		MimeType:  image.MIMEType,
		Path:      path,
		LocalPath: localPath,
	}

	artifactService := run.GetArtifactService()
	var artifact *iris.ResultArtifact
	store := iris.RetrieveStore[ScreenshotStore](run)
	if store.ArtifactID == "" {
		logger.Info("creating new screenshot artifact")
		a, err := artifactService.NewImageArtifact(run, nextentity.ImageArtifactTypeMetadata{})
		if err != nil {
			return screenshot, err
		}
		store.ArtifactID = a.ID()
		iris.UpdateStore(run, store)
		artifact = a
		logger.Info("created new screenshot artifact %s", store.ArtifactID)
	} else {
		logger.Info("using existing screenshot artifact %s", store.ArtifactID)
		artifact = &iris.ResultArtifact{
			ArtifactID: store.ArtifactID,
			Metadata:   entity.ResultArtifactMetadata{},
			Files:      []string{},
		}
	}
	if artifact == nil {
		return screenshot, nil
	}
	screenshot.ID = artifact.ID()
	err = artifactService.UploadFiles(run, artifact, []iris.ArtifactFile{
		{
			Path:    path,
			Content: imageData,
		},
	})
	if err != nil {
		return screenshot, err
	}
	logger.Infof("upload screenshot: %s %s", artifact.ID(), path)
	return screenshot, nil
}

func readImageData(localPath string) ([]byte, error) {
	imgbuff, err := ioutil.ReadFile(localPath)
	if err != nil {
		return nil, err
	}

	return imgbuff, nil
}

func compressImage(run *iris.AgentRunContext, imagePath string) error {
	logger := run.GetLogger()
	imgbuff, err := ioutil.ReadFile(imagePath)
	if err != nil {
		logger.Errorf("[compressImage] read file failed: %s", err)
		return err
	}

	r := bytes.NewReader(imgbuff)
	img, _, err := image.Decode(r) //解码
	if err != nil {
		logger.Errorf("[compressImage] decode failed: %s", err)
		return err
	}

	width := int(math.Ceil(1920 / 1.5))  //指定缩略图宽，单位像素
	height := int(math.Ceil(1650 / 1.5)) //指定缩略图高，单位像素

	dst := imaging.Resize(img, width, height, imaging.Lanczos) //调用缩略图接口

	fp, err := os.Create(imagePath)
	if err != nil {
		logger.Errorf("[compressImage] open file failed: %s", err)
		return err
	}
	err = imaging.Encode(fp, dst, imaging.JPEG) //编码输出
	if err != nil {
		logger.Errorf("[compressImage] encode to file failed: %s", err)
		return err
	}
	return nil
}

func getBrowserState(run *iris.AgentRunContext, client *browser.MCPClient) (map[string]any, error) {
	store := iris.RetrieveStore[BrowserStateStore](run)

	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = ToolBrowserState
	callRequest.Params.Arguments = map[string]any{}
	resp, err := client.Client.CallTool(run, callRequest)
	if err != nil {
		return store.BrowserState, err
	}
	texts := mcptool.ExtractTextContent(resp.Content)
	if resp.IsError {
		run.GetLogger().Errorf("[debug] failed to call tool browser_get_state: %v", resp.IsError)
		return store.BrowserState, iris.NewRecoverable(errors.New(strings.Join(texts, ",")))
	}
	result := make(map[string]any)
	// now only get first elem
	if err = json.Unmarshal([]byte(texts[0]), &result); err != nil {
		run.GetLogger().Errorf("[debug] unmarshal get state failure: %s. text: %v", err, texts)
	}

	store.BrowserState = result
	iris.UpdateStore(run, store)
	return result, nil
}

func rewriteArguments(run *iris.AgentRunContext, toolName string, arguments map[string]any) map[string]any {
	if toolName == ToolGoto {
		arguments["url"] = rewriteFileProtocol(run, conv.DefaultAny[string](arguments["url"]))
		arguments["url"] = revertHtmlUnescape(conv.DefaultAny[string](arguments["url"]))
	}

	return arguments
}

var revertMap = map[string]string{
	"®": "&reg",
}

func revertHtmlUnescape(url string) string {
	for k, v := range revertMap {
		url = strings.ReplaceAll(url, k, v)
	}
	return url
}

// rewriteFileProtocol rewrites the file protocol to the workspace path
func rewriteFileProtocol(run *iris.AgentRunContext, url string) string {
	if strings.HasPrefix(url, "file://") {
		if strings.HasPrefix(url, "file:///") {
			return url
		}
		url = strings.TrimPrefix(url, "file://")
		ws := workspace.GetWorkspace(run)
		return fmt.Sprintf("file://%s/%s", ws.Editor.WorkingDirectory, url)
	}
	return url
}

type BrowserDownloadArgs struct {
	URL    string `json:"url" description:"url of the file to download"`
	Output string `json:"output" description:"path to save the file"`
}

func NewBrowserDownloadAction() iris.Action {
	return actions.ToTool(ToolBrowserDownload, "download a file from the internet", func(run *iris.AgentRunContext, args BrowserDownloadArgs) (map[string]any, error) {
		cmd := exec.CommandContext(run, "curl", args.URL, "-o", args.Output)
		terminal := workspace.GetTerminal(run, "")
		out, err := terminal.ExecuteCmd(cmd, workspace.ExecuteCmdOption{
			Timeout: 1800,
		})
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		return map[string]any{"result": out}, nil
	})
}
