package browseract

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"context"
	_ "embed"
	"fmt"
	"github.com/samber/lo"
	"os"
	"regexp"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
)

var (
	//go:embed extract_network_system.go.tmpl
	extractNetworkSystemPrompt         string
	ExtractNetworkSystemPromptTemplate = prompt.MustGetTemplate("ExtractNetworkSystemPrompt", extractNetworkSystemPrompt)
	//go:embed extract_network_user.go.tmpl
	extractNetworkUserPrompt         string
	ExtractNetworkUserPromptTemplate = prompt.MustGetTemplate("ExtractNetworkUserPrompt", extractNetworkUserPrompt)
)

type ExtractResult struct {
	CSVFilePath string
	Headers     string
	Content     string
	FileName    string
}

func extractNetworkFiles(run *iris.AgentRunContext, networkFiles []string, goal string, webpageContent string, step *iris.AgentRunStep) []string {
	logger := run.GetLogger()

	run.State.Memory.HasSteps()

	g, _ := errgroup.WithContext(run)
	g.SetLimit(5)
	lock := &sync.Mutex{}

	var networkAnalysisFiles []string

	for i := 0; i < len(networkFiles); i++ {
		networkFile := networkFiles[i]
		g.Go(func() error {

			defer func() {
				if r := recover(); r != nil {
					logger.Errorf("recover from panic %+v, stack: %s", r, debug.Stack())
				}
			}()

			csvFilePath, err := extractOneByLLM(run, networkFile, goal, webpageContent, step)
			if err != nil {
				logger.Errorf("Failed to extract csv from network file %s: %v", networkFile, err)
				return nil
			}

			lock.Lock()
			defer lock.Unlock()

			networkAnalysisFiles = append(networkAnalysisFiles, csvFilePath)

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		logger.Errorf("Error in parallel extraction: %v", err)
	}

	return networkAnalysisFiles
}

func extractOneByLLM(run *iris.AgentRunContext, networkFile string, goal string, webpageContent string, step *iris.AgentRunStep) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	runCtx := run.ForkWithContext(ctx)

	logger := run.GetLogger()

	networkContent, err := readNetworkFile(networkFile)
	if err != nil {
		return "", errors.WithMessage(err, "failed to read network file")
	}

	messages, err := prompt.ComposeVaryMessages([]prompt.ComposeVaryMessageOption{
		prompt.WithSystemMessage(ExtractNetworkSystemPromptTemplate, map[string]any{}),
		prompt.WithUserMessage(ExtractNetworkUserPromptTemplate, map[string]any{
			"WebpageContent": webpageContent,
			"NetworkFile":    networkContent,
		}),
	})

	if err != nil {
		return "", errors.WithMessage(err, "failed to compose messages")
	}

	res, err := agents.Think(runCtx, "browser_use", messages, agents.ThinkOption{DisableThinkDelta: true})

	if err != nil {
		return "", errors.WithMessage(err, "failed to call llm")
	}

	thought := &iris.Thought{
		Content: res.Content,
		LLMCall: iris.LLMCall{
			ModelName:    res.Model,
			Temperature:  res.Temperature,
			Usage:        res.Usage,
			Prompt:       messages,
			FinishReason: res.FinishReason,
		},
	}

	result, err := parseExtractResponse(res.Content)
	if err != nil {
		return "", errors.WithMessage(err, "failed to parse extract response")
	}

	csvFilePath, err := createCSVFile(run, result, thought, step)
	if err != nil {
		logger.Errorf("Failed to create CSV file for %s: %v", networkFile, err)
		return "", err
	}

	return csvFilePath, nil
}

// readNetworkFile 读取网络文件内容
func readNetworkFile(filePath string) (string, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

// parseExtractResponse 解析LLM响应，提取CSV标题和内容
func parseExtractResponse(response string) (*ExtractResult, error) {
	// 提取CSV标题
	headersRegex := regexp.MustCompile(`<csv_headers>\s*([^<]+)\s*</csv_headers>`)
	headersMatch := headersRegex.FindStringSubmatch(response)
	if len(headersMatch) < 2 {
		return nil, errors.New("failed to extract CSV headers from response")
	}
	headers := strings.TrimSpace(headersMatch[1])

	// 提取CSV内容
	contentRegex := regexp.MustCompile(`<csv_content>\s*([^<]+)\s*</csv_content>`)
	contentMatch := contentRegex.FindStringSubmatch(response)
	if len(contentMatch) < 2 {
		return nil, errors.New("failed to extract CSV content from response")
	}
	content := strings.TrimSpace(contentMatch[1])

	// 提取文件名
	filenameRegex := regexp.MustCompile(`<filename>\s*([^<]+)\s*</filename>`)
	filenameMatch := filenameRegex.FindStringSubmatch(response)
	fileName := "network_data"
	if len(filenameMatch) >= 2 {
		fileName = strings.TrimSpace(filenameMatch[1])
	}

	return &ExtractResult{
		Headers:  headers,
		Content:  content,
		FileName: fileName,
	}, nil
}

func createCSVFile(run *iris.AgentRunContext, result *ExtractResult, thought *iris.Thought, step *iris.AgentRunStep) (string, error) {
	publisher := run.GetPublisher()

	extractStep := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: step.ExecutorAgent,
	})
	extractStep.Parent = step.Parent
	extractStep.Thought = thought
	extractStep.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(extractStep)

	extractStep.Action = &actions.Tool{
		ActionInfo: iris.ActionInfo{
			ActionName: string(workspace.ActionCreateFile),
			ActionDescription: lo.Ternary(
				agents.GetUserLanguage(run.Parameters) == "Chinese (Simplified)",
				"正在总结调查结果",
				"Summarizing web data results",
			),
		},
	}

	publisher.ReportToolCall(extractStep, iris.ToolCallStatusStarted, "create file for web data investigation result")

	csvContent := result.Headers + "\n" + result.Content
	fileName := fmt.Sprintf("%s.csv", result.FileName)
	finalFileName := generateFileName(run, fileName, "csv")

	extractStep.Inputs = map[string]any{
		"file_path": finalFileName,
		"content":   csvContent,
	}

	createFileAction := workspace.NewCreateFileAction(workspace.CreateFileOption{})
	err := createFileAction.Execute(run, extractStep)

	publisher.ReportToolCall(extractStep, iris.ToolCallStatusCompleted, "created file for web data investigation result")

	if err != nil {
		extractStep.Status = iris.AgentRunStepStatusFailed
		extractStep.Error = err
		publisher.ReportStep(extractStep)
		return "", iris.NewRecoverable(err)
	} else {
		extractStep.Status = iris.AgentRunStepStatusSuccess
		publisher.ReportStep(extractStep)
	}

	return fileName, nil
}
