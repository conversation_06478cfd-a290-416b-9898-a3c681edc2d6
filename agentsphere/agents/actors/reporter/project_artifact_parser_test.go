package reporter

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestParseProjectArtifacts(t *testing.T) {
	markdown := `
xxxxxx

### Project Artifacts
<project_reference>
- path: /path/to/repo1
- path: /path/to/repo2
</project_reference>
### Attachments
- [desc](url)
`
	projects := ParseProjectArtifacts(markdown, false)
	require.Equal(t, 2, len(projects))
	require.Equal(t, "/path/to/repo1", projects[0].Path)
	require.Equal(t, "/path/to/repo2", projects[1].Path)
}

func TestParseProjectArtifactsDirect(t *testing.T) {
	markdown := `
- path: /path/to/repo1
- path: /path/to/repo2
`
	projects := ParseProjectArtifacts(markdown, true)
	require.Equal(t, 2, len(projects))
	require.Equal(t, "/path/to/repo1", projects[0].Path)
	require.Equal(t, "/path/to/repo2", projects[1].Path)
}
