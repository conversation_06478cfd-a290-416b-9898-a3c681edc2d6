package reporter

import (
	"fmt"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/lib/util"
	"github.com/samber/lo"
)

var (
	IdentifierV2        = "task_concluder_v2"
	TagResponse         = "response_to_user"
	TagReference        = "reference"
	TagProjectReference = "project_reference"
	parametersV2        = []actors.ParameterDescription{
		{
			Name:        TagResponse,
			Description: "A simple response to the user. It should be in 2-3 sentences (in one paragraph) summarizing the key points from the detailed results in the first person. The language should be the same as the user's input.",
		},
		{
			Name:        TagReference,
			Description: "the final documents or url artifacts, e.g.,\n - [description_1](relative_file_path)\n- [description_2](deployed_url)",
		},
		{
			Name:        TagProjectReference,
			Description: "the final code project (if applicable), e.g.,\n - path: project1_path\n- path: project2_path\nNote: project path must be relative path based on the current working directory",
		},
	}
)

type AgentV2 struct {
	actors.BaseActor
	Reporter *ReporterV2
}

var _ actors.Actor = &AgentV2{}

func NewV2(opt CreateOption) *AgentV2 {

	desc := lo.Must(promptset.ExecutePrompt("description.v2", opt.Variant, nil))

	info := iris.AgentInfo{
		Identifier: IdentifierV2,
		Desc:       desc,
	}

	reporter := &ReporterV2{
		AgentInfo: info,
		Variant:   opt.Variant,
	}
	agent := &AgentV2{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parametersV2,
		},
		Reporter: reporter,
	}

	agent.ExecutionStep = &actors.ExecutionStep{
		Step: opt.ActorStep,
	}

	return agent
}

func (a *AgentV2) GetParametersDescription(run *iris.AgentRunContext) []actors.ParameterDescription {
	return lo.Map(a.Parameters, func(item actors.ParameterDescription, index int) actors.ParameterDescription {
		if item.Name == TagResponse {
			item.Description += fmt.Sprintf(" (%s by default)", agents.GetUserLanguage(run.Parameters))
		}
		return item
	})
}

func (a *AgentV2) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {

	tags, err := prompt.ParseTopTagsV2(input)
	if err != nil {
		result.Evaluation = controltool.ConclusionEvaluationFailed
		return result
	}
	for _, tag := range tags {
		switch tag.XMLName.Local {
		case TagResponse:
			result.Content = tag.Content
		case TagReference:
			result.Reference = iris.ParseReference(tag.Content)
		case TagProjectReference:
			run.GetLogger().Infof("project reference: %+v", ParseProjectArtifacts(tag.Content, true))
			result.ProjectReference = ParseProjectArtifacts(tag.Content, true).Normalize(workspace.GetEditor(run).WorkingDirectory)
			run.GetLogger().Infof("project reference normalized: %+v", result.ProjectReference)
		}
	}
	result.Evaluation = controltool.ConclusionEvaluationSuccess
	return result
}

type ReporterV2 struct {
	iris.AgentInfo
	uid     string
	Variant string
}

func (r *ReporterV2) UID() string {
	if r.uid == "" {
		r.uid = fmt.Sprintf("%s-agent-%s", r.Identifier, util.RandomString(8))
	}
	return r.uid
}

var _ iris.RunnableAgent = &Reporter{}
