package reporter

import (
	"embed"
	"fmt"
	"regexp"

	controltool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/control"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actors/htmlreporter"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/agentrace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	agentutil "code.byted.org/devgpt/kiwis/agentsphere/agents/util"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
	"code.byted.org/devgpt/kiwis/lib/util"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

var (
	Identifier = "task_concluder"
	parameters = []actors.ParameterDescription{
		{
			Name:        "task",
			Description: "the task to complete",
		},
	}

	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")
)

type ReporterStore struct {
	CurrentTask  string
	AgentStepID  string
	ParentStepID string
}

type Agent struct {
	actors.BaseActor
	Reporter *Reporter
}

var _ actors.Actor = &Agent{}

type CreateOption struct {
	MaxSteps         int
	Variant          string
	IfDynamicSetting bool
	LLMHooks         *prompt.LLMCallHook
	ActorStep        *iris.AgentRunStep
}

func New(opt CreateOption) *Agent {
	// 为了区分1.0和1.5两种诉求的reporter pe
	desc := lo.Must(promptset.ExecutePrompt("description", opt.Variant, nil))
	if opt.IfDynamicSetting {
		desc = lo.Must(promptset.ExecutePrompt("description.v1_5", opt.Variant, nil))
	}

	info := iris.AgentInfo{
		Identifier: Identifier,
		Desc:       desc,
	}

	reporter := &Reporter{
		AgentInfo:        info,
		Variant:          opt.Variant,
		IfDynamicSetting: opt.IfDynamicSetting,
		LLMHooks:         opt.LLMHooks,
	}
	agent := &Agent{
		BaseActor: actors.BaseActor{
			AgentInfo:  info,
			Parameters: parameters,
		},
		Reporter: reporter,
	}

	agent.ExecutionStep = &actors.ExecutionStep{
		Step: opt.ActorStep,
	}

	return agent
}

func (a *Agent) Run(run *iris.AgentRunContext, input string, options actors.RunOptions) (result controltool.ConclusionOutput) {
	logger := run.GetLogger()
	plannerStore := iris.RetrieveStoreByKey[entity.PlannerStore](run, entity.PlannerStoreKey)
	input = fmt.Sprintf("[Background]\n<user_request>\n%s\n</user_request>\n\n%s", plannerStore.Requirements, input)
	logger.Infof("reporter run with input: %s", input)
	iris.UpdateStoreByKey(run, a.Reporter.UID(), ReporterStore{
		CurrentTask: input,
	})
	err := a.Reporter.RunWithOption(run, &agents.ReActAgentRunOption{
		Step: a.ExecutionStep.Step,
	})
	if err != nil {
		tags, _ := prompt.ParseTopTagsV2(input)
		executionResult, ok := lo.Find(tags, func(tag prompt.Tag) bool {
			return tag.XMLName.Local == "execution_trace"
		})
		logger.Warnf("reporter is failed due to error: %v\n conclude by input directly(found = %v): %s, err: %v", err, ok, executionResult.Content)
		result = controltool.ConclusionOutput{
			Content:    lo.Ternary(len(executionResult.Content) == 0, input, executionResult.Content),
			Evaluation: controltool.ConclusionEvaluationPartialCompleted,
		}
		return result
	}
	mapstructure.Decode(run.State.LastStep().Conclusion, &result)

	switch a.Reporter.Variant {
	case agententity.VariantDeepseek:
		htmlReporter := htmlreporter.New(htmlreporter.CreateOption{
			MaxSteps:       1,
			Variant:        a.Reporter.Variant,
			DeployStrategy: htmlreporter.DeployStrategySingleFile,
		})
		htmlResult := htmlReporter.CreateReport(run, a.ExecutionStep.Step, htmlreporter.CreateReportOption{
			CurrentTask:     conv.DefaultAny[string](run.State.LastStep().Conclusion["content"]),
			UserRequirement: conv.DefaultAny[string](input),
		})
		result.Reference = append(result.Reference, htmlResult.Reference...)
		logger.Debugf("reporter finish with output: %+v, htmlResult: %+v", result, htmlResult.Reference)

	case agententity.VariantIntern:
		htmlReporter := htmlreporter.New(htmlreporter.CreateOption{
			MaxSteps:       1,
			Variant:        a.Reporter.Variant,
			DeployStrategy: htmlreporter.DeployStrategyWholeDir,
		})
		htmlResult := htmlReporter.CreateReport(run, a.ExecutionStep.Step, htmlreporter.CreateReportOption{
			CurrentTask:     result.Content,
			UserRequirement: iris.ReferenceString(result.Reference),
		})

		result.Reference = append(result.Reference, htmlResult.Reference...)
		logger.Debugf("reporter finish with output: %+v, htmlResult: %+v", result, htmlResult.Reference)
	default:
	}
	return result
}

type Reporter struct {
	iris.AgentInfo
	uid              string
	Variant          string
	IfDynamicSetting bool
	LLMHooks         *prompt.LLMCallHook
}

func (r *Reporter) UID() string {
	if r.uid == "" {
		r.uid = fmt.Sprintf("%s-agent-%s", r.Identifier, util.RandomString(8))
	}
	return r.uid
}

var _ iris.RunnableAgent = &Reporter{}

func (r *Reporter) Run(run *iris.AgentRunContext) (err error) {
	span, ctx := run.GetTracer(run).
		StartCustomSpan(run, agentrace.SpanTypeStep, r.Identifier+"_run")
	run = run.WithContext(ctx)
	defer func() {
		agentrace.AddErrorTag(span, err)
		span.Finish()
	}()
	publisher := run.GetPublisher()
	store := iris.RetrieveStoreByKey[ReporterStore](run, r.UID())
	step := run.CreateStep(&iris.CreateStepOption{
		ExecutorAgent: r.Name(),
		Parent:        lo.Ternary(store.AgentStepID != "", run.GetStep(store.AgentStepID), nil),
	})
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)

	thought, err := r.think(run)
	if err != nil {
		return err
	}

	// extract reference
	var reference string
	refReg := regexp.MustCompile(`\[(.*)\]\((.*)\)`)
	refMatches := refReg.FindAllStringSubmatch(thought.Content, -1)

	for _, match := range refMatches {
		if len(match) < 3 {
			continue
		}
		reference += fmt.Sprintf("- [%s](%s)\n", match[1], match[2])
	}

	defer func() {
		last := run.State.LastStep()
		step.Finish(last.Conclusion, last.Error)
		publisher.ReportStep(step)
	}()
	result := controltool.ConclusionOutput{
		Content:          thought.Content,
		Evaluation:       controltool.ConclusionEvaluationSuccess,
		Reference:        iris.ParseReference(reference),
		ProjectReference: ParseProjectArtifacts(thought.Content, false).Normalize(workspace.GetEditor(run).WorkingDirectory),
	}
	span.UpdateData(agentrace.NewObjectSpanData(map[string]any{
		"result": result,
	}))
	step.Conclusion = agentutil.ValueToMap(result)
	return nil
}

func (r *Reporter) think(run *iris.AgentRunContext) (*iris.Thought, error) {
	messages := r.Compose(run)

	content, err := agents.Think(run, r.Identifier, messages,
		agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{
				MaxCheckSize: 10,
				FlushSize:    1,
				AheadSize:    10,
				StartTokens:  []string{},
				EndTokens:    []string{},
				BreakTokens:  []string{},
			},
			Hooks: r.LLMHooks,
		},
	)
	if err != nil {
		return nil, errors.WithMessage(err, "llm api not available")
	}

	thought := &iris.Thought{
		Content: content.Content,
		LLMCall: iris.LLMCall{
			ModelName:    content.Model,
			Temperature:  content.Temperature,
			Usage:        content.Usage,
			Prompt:       messages,
			FinishReason: content.FinishReason,
			TraceID:      content.TraceID,
		},
	}
	run.UpdateThought(thought)
	return thought, nil
}

func (r *Reporter) RunWithOption(run *iris.AgentRunContext, opt *agents.ReActAgentRunOption) error {
	publisher := run.GetPublisher()
	store := iris.RetrieveStoreByKey[ReporterStore](run, r.UID())
	// the step represents the agent's task
	// it will be used as the parent step for all the steps created by the agent
	step := opt.Step
	if step == nil {
		return errors.New("step is nil")
	}
	store.AgentStepID = step.StepID
	iris.UpdateStoreByKey(run, r.UID(), store)

	// push current step to steps
	mem := memory.GetAgentMemory(run)
	mem.AddAction((*memory.ActionMemoryItem)(step))

	originalStep := run.State.CurrentStep
	run.State.CurrentStep = nil
	defer func() {
		last := run.State.LastStep()
		step.Finish(last.Conclusion, last.Error)
		publisher.ReportStep(step)
		run.State.CurrentStep = originalStep
	}()

	step.Status = iris.AgentRunStepStatusRunning
	publisher.ReportStep(step)
	return r.Run(run)
}

func (r *Reporter) Compose(run *iris.AgentRunContext) []*framework.ChatMessage {
	store := iris.RetrieveStoreByKey[ReporterStore](run, r.UID())

	templates := prompt.TemplateSet{
		SystemTmpl:  promptset.GetTemplate("system_brief", r.Variant),
		UserTmpl:    promptset.GetTemplate("user", r.Variant),
		ThoughtTmpl: promptset.GetTemplate("thought", r.Variant),
	}

	messages := agents.ComposeMessage(run, agents.ComposerOptions{
		Templates: templates,
		SystemPromptVariables: map[string]any{
			"Language":         agents.GetUserLanguage(run.Parameters),
			"WorkingDirectory": workspace.GetEditor(run).WorkingDirectory,
		},
		UserPromptVariables: map[string]any{
			"Content": store.CurrentTask,
		},
		MemoryRounds: 20,
	})
	return messages
}
