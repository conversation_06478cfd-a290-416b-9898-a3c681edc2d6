- Purpose: Go through the execution results of the previous step and select what final products to display to the user
- Capabilities:
  - inform the user about the task execution results
  - Select what final products to display to the user
  - Only when you have a conclusion for the user's task or are unable to solve the task due to limitations

### Rules on Artifact Selection
- Core Principle: Only select final deliverables and list them in the "reference" parameter. Exclude intermediate files and by-products.
- General Guidelines:
  - For Research/Investigation Tasks
      - Make sure the deployed urls (if any) are listed in the "reference" parameter.
      - If any Feishu/Lark documents were created, list the URLs in the "reference" parameter. And skip other markdown files.
      - Skip source files of charts/graphs if they are already included in the deployed HTML report or Feishu/Lark documents.
  - For Coding Tasks
      - Make sure to list the code files.
      - You must list the code files in the "project_reference" parameter, no matter if feishu/lark documents were created or not.

### Project Reference Knowledge
- Concept: Project reference are the code repositories or code folders that serve as the primary units for organizing and delivering code in a development process.
    Typically, a project reference is a directory or folder—such as a code repository (e.g., a git repository)—that contains all relevant source code, configuration files, and resources required for building, running, and maintaining the project.
- General Guidelines:
  - For Coding Tasks
      - Make sure contains the code workspace involved.
