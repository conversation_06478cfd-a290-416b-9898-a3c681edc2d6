package reporter

import (
	"fmt"
	"regexp"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"gopkg.in/yaml.v3"
)

func ParseProjectArtifacts(markdown string, direct bool) iris.ProjectReference {
	var (
		projectReference        iris.ProjectReference
		err                     error
		projectArtifactsContent string
	)
	if direct {
		projectArtifactsContent = markdown
	} else {
		projectArtifactsContent, err = ExtractProjectArtifactsContent(markdown)
		if err != nil {
			return projectReference
		}
	}

	err = yaml.Unmarshal([]byte(projectArtifactsContent), &projectReference)
	if err != nil {
		return projectReference
	}
	return projectReference
}

// ExtractProjectArtifactsContent 提取 <project_reference> ... </project_reference> 之间的内容
func ExtractProjectArtifactsContent(input string) (string, error) {
	// (?s) 让 . 匹配换行符
	re := regexp.MustCompile(`(?s)<project_reference>(.*?)</project_reference>`)
	matches := re.FindStringSubmatch(input)
	if len(matches) < 2 {
		return "", fmt.Errorf("no <project_reference> block found")
	}
	// 去除首尾空白
	content := strings.TrimSpace(matches[1])
	return content, nil
}
