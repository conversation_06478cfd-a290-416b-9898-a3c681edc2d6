package actorstest

import (
	"context"
	"fmt"
	"math/rand"
	"net"
	"os"
	"strings"
	"testing"

	"code.byted.org/codebase/codfish/core/daemon"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/require"

	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/client"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/cloud_oauth"
)

const (
	// Here we only use api server to relay llm calls
	// tests are only enabled locally not in CI, and boe is enough for testing
	localAPIServer = "https://aime-boe.bytedance.net/api/agents/v2"
	// localAPIServer = "http://localhost:6789/api/agents/v2"

	// placeholder agent name, not executable, used for creating run context
	TestAgentName = "placeholder"
)

type SetupTestRuntimeOption struct {
	WorkDir   string
	SessionID string
	APIServer string
}

func (o *SetupTestRuntimeOption) setDefaults() {
	if o.SessionID == "" {
		o.SessionID = "test"
	}
}

// SetupTestRuntime 设置一个测试用的 runtime，并返回 runtime 实例
func SetupTestRuntime(opt SetupTestRuntimeOption) *runtime.RemoteRuntime {
	opt.setDefaults()
	runtimeAddr := fmt.Sprintf("127.0.0.1:%d", rand.Intn(20000)+10000)
	listener, err := net.Listen("tcp", runtimeAddr)
	if err != nil {
		panic(err)
	}
	server := daemon.CreateDaemonServer()
	done := make(chan error)
	go func() {
		err := server.Serve(listener)
		done <- err
	}()

	logger := logrus.New()
	logger.SetOutput(os.Stderr)
	// to start integration test, local api server must be running
	if len(opt.APIServer) == 0 {
		opt.APIServer = localAPIServer
	}
	api, err := client.NewRuntimeAPIClient(opt.APIServer, logger, "")
	if err != nil {
		panic(err)
	}

	runtime, err := runtime.NewRemoteRuntime(runtime.RuntimeOption{
		Addr:      runtimeAddr,
		Workspace: opt.WorkDir,
		SessionID: opt.SessionID,
		Config: entity.RuntimeDaemonConfig{
			Debug:      true,
			APIBaseURL: localAPIServer,
			LLMBaseURL: localAPIServer,
		},
		API:              api,
		Logger:           logger,
		CloudOAuthClient: cloud_oauth.TryNewCloudOAuthClient(),
	})
	if err != nil {
		panic(err)
	}
	runtime.RegisterAgent(&iris.DefaultConfigurableAgent{
		RunnableAgent: agents.NewSerialAgent(
			iris.AgentInfo{
				Identifier: TestAgentName,
				Desc:       "placeholder agent, not executable",
			},
		),
	})
	_ = runtime.Ready()
	return runtime
}

func AssertConclusion(t *testing.T, result string, expected string) {
	llmapi, err := framework.NewAzureOpenAILLM(os.Getenv("OPENAI_API_KEY"), fmt.Sprintf("%s/runtime", localAPIServer))
	if err != nil {
		t.Fatalf("failed to build LLM client: %v", err)
	}
	resp, err := llmapi.ChatCompletion(context.Background(), []*framework.ChatMessage{
		{Role: "system", Content: "Check if the given task result meets the expected result. If it does, return 'true'. Otherwise, return 'false'."},
		{Role: "user", Content: fmt.Sprintf("result: %s\nexpected: %s", result, expected)},
	}, framework.LLMCompletionOption{
		Model: string(agententity.Qwen25Coder),
		Tag:   "assert_conlusion",
	})
	require.NoError(t, err)
	if !strings.Contains(resp.Content, "true") {
		require.Failf(t, "result does not meet expectation", "result: %s\nexpected: %s\njudgment: %s", result, expected, resp.Content)
	}
}

func SkipCI(t *testing.T) {
	if len(os.Getenv("CI_REPO_NAME")) > 0 {
		t.Skip("Skipping CI")
	}
}
