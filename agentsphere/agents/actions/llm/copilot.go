package llmtool

import (
	"errors"
	"fmt"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/genexp"
	"code.byted.org/devgpt/kiwis/agentsphere/copilot"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"

	pkgerrors "code.byted.org/gopkg/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

const (
	CopilotLLMTag              = "copilot"
	CopilotLLMModelName        = "gemini-2.5-pro"
	CopilotLLMMaxTokens        = 12000
	CopilotLLMTemperature      = 0.2
	CopilotDiagnoseToolName    = "copilot_diagnose"
	CopilotDiagnoseDescription = `Generate a diagnosis report for user issues by analyzing the trace and generating insights using LLM.
diagnosis includes:
1) Diagnosis Summary (诊断摘要): Brief description of agent's original goal, failure reason, and key failure round;
2) Root Cause Analysis (根本原因分析): Detailed failure analysis with trajectory evidence and template validation; 
3) Failure Classification (失败分类): Categorize failure type (communication, model defects, planning, execution, etc.)`
	CopilotGptToolName    = "copilot_gpt"
	CopilotGptDescription = `Provide informal, direct answers to user questions by analyzing the trace and trajectory.
Give concise, straightforward responses without formal diagnosis. Examples:
- For template document verification: Simply answer "Yes" or "No"  
- For calculation explanations: Answer directly based on trajectory
- For calculation errors: Just inform "calculation error detected" without formal diagnosis`
)

type CopilotDiagnoseToolArgs struct {
	Query     string `json:"query" mapstructure:"query" description:"your issue to diagnose, as much detail as possible."`
	TraceFile string `json:"trace_file" mapstructure:"trace_file" description:"agent trace file path"`
}

type CopilotDiagnoseToolResult struct {
	DiagnoseReport string `json:"diagnose_report" mapstructure:"diagnose_report"`
}

type CopilotGptToolArgs struct {
	Query     string `json:"query" mapstructure:"query" description:"your question or issue to get a direct answer from LLM"`
	TraceFile string `json:"trace_file" mapstructure:"trace_file" description:"agent trace file path"`
}

type CopilotGptToolResult struct {
	Answer string `json:"answer" mapstructure:"answer"`
}

// CompressLevel trajectory compress level
type CompressLevel int

const (
	CompressLoose  CompressLevel = iota // 宽松
	CompressStrict                      // 严格
)

// 压缩过滤关键词
var compressFilterKeywords = []string{
	"knowledge_renders",
	"line",
	"Knowledges",
}

// 判断是否为空（nil、空字符串、空数组、空map）
func isEmptyValue(v interface{}) bool {
	switch vv := v.(type) {
	case nil:
		return true
	case string:
		return vv == ""
	case []interface{}:
		return len(vv) == 0
	case map[string]interface{}:
		return len(vv) == 0
	}
	return false
}

func compressJSON(val interface{}, level CompressLevel) interface{} {
	switch v := val.(type) {
	case map[string]interface{}:
		if len(v) == 0 {
			return nil
		}
		res := make(map[string]interface{}, len(v))
		for k, vv := range v {
			if level == CompressStrict {
				if lo.Contains(compressFilterKeywords, k) {
					continue
				}
			}
			if cv := compressJSON(vv, level); !isEmptyValue(cv) {
				res[k] = cv
			}
		}
		if len(res) == 0 {
			return nil
		}
		return res

	case []interface{}:
		if len(v) == 0 {
			return nil
		}
		arr := make([]interface{}, 0, len(v))
		for _, item := range v {
			if cv := compressJSON(item, level); !isEmptyValue(cv) {
				arr = append(arr, cv)
			}
		}
		if len(arr) == 0 {
			return nil
		}
		return arr
	default:
		return v
	}
}

// GetTrajectoryContent returns trajectory content with strict compression applied
func GetTrajectoryContent(c *iris.AgentRunContext, tracePath string, compressLevel CompressLevel) string {
	ws := workspace.GetWorkspace(c)
	var trajectoryNodes interface{}

	if tracePath != "" {
		traceFile, err := ws.Editor.ReadFile(workspace.ReadFileArgs{
			Path: tracePath,
		})
		if err != nil {
			c.GetLogger().Errorf("failed to read user trace file: %v", err)
			return ""
		}
		trajectoryNodes, err = genexp.ParseTraceFile(traceFile.RealPath)
		if err != nil {
			c.GetLogger().Errorf("failed to parse trace file: %v", err)
			return ""
		}
	} else {
		traceFile, err := ws.Editor.ReadFile(workspace.ReadFileArgs{
			Path: fmt.Sprintf("../log/iris-trace-%s.jsonl", c.State.RunID),
		})
		if err != nil {
			c.GetLogger().Errorf("failed to read trace file: %v", err)
			return ""
		}
		trajectoryNodes, err = genexp.ParseTraceFile(traceFile.RealPath)
		if err != nil {
			c.GetLogger().Errorf("failed to parse trace file: %v", err)
			return ""
		}
	}

	// Apply strict compression to reduce content size
	compressed := compressJSON(trajectoryNodes, compressLevel)
	return conv.JSONFormatString(compressed)
}

func NewCopilotDiagnoseTool() *actions.Tool {
	return actions.ToTool(CopilotDiagnoseToolName, CopilotDiagnoseDescription, func(c *iris.AgentRunContext, args CopilotDiagnoseToolArgs) (*CopilotDiagnoseToolResult, error) {
		// Generate prompt message
		composeOptions := []prompt.ComposeVaryMessageOption{
			copilot.GetSystemPrompt(GetTrajectoryContent(c, args.TraceFile, CompressLoose), args.Query),
		}

		messages, err := prompt.ComposeVaryMessages(composeOptions)
		if err != nil {
			return nil, iris.NewRecoverable(pkgerrors.WithMessage(err, "failed to compose messages"))
		}

		// Call LLM
		result, err := c.GetLLM().ChatCompletion(c, messages, framework.LLMCompletionOption{
			Model:       CopilotLLMModelName,
			Temperature: CopilotLLMTemperature,
			MaxTokens:   CopilotLLMMaxTokens,
			Tag:         CopilotLLMTag,
		})

		if err != nil {
			// Check if the error is an OpenAI APIError with "text fields that are too large"
			var apiError *openai.APIError
			if errors.As(err, &apiError) && apiError.Code == 0 &&
				strings.Contains(apiError.Message, "Request contains text fields that are too large") {

				c.GetLogger().Infof("Detected OpenAI APIError for text too large, retrying with strict compression")

				// Retry with compressed trajectory content
				compressedComposeOptions := []prompt.ComposeVaryMessageOption{
					copilot.GetSystemPrompt(GetTrajectoryContent(c, args.TraceFile, CompressStrict), args.Query),
				}

				compressedMessages, compressErr := prompt.ComposeVaryMessages(compressedComposeOptions)
				if compressErr != nil {
					return nil, iris.NewRecoverable(pkgerrors.WithMessage(compressErr, "failed to compose compressed messages"))
				}

				// Retry LLM call with compressed content
				retryResult, retryErr := c.GetLLM().ChatCompletion(c, compressedMessages, framework.LLMCompletionOption{
					Model:       CopilotLLMModelName,
					Temperature: CopilotLLMTemperature,
					MaxTokens:   CopilotLLMMaxTokens,
					Tag:         CopilotLLMTag,
				})

				if retryErr != nil {
					return nil, iris.NewRecoverable(pkgerrors.WithMessage(retryErr, "failed to generate diagnosis report with compressed content"))
				}

				return &CopilotDiagnoseToolResult{
					DiagnoseReport: retryResult.Content,
				}, nil
			}

			// For other errors, return the original error
			return nil, iris.NewRecoverable(pkgerrors.WithMessage(err, "failed to generate diagnosis report"))
		}

		return &CopilotDiagnoseToolResult{
			DiagnoseReport: result.Content,
		}, nil
	})
}

func NewCopilotGptTool() *actions.Tool {
	return actions.ToTool(CopilotGptToolName, CopilotGptDescription, func(c *iris.AgentRunContext, args CopilotGptToolArgs) (*CopilotGptToolResult, error) {
		// Generate prompt message for informal, direct answers
		composeOptions := []prompt.ComposeVaryMessageOption{
			copilot.GetGptSystemPrompt(GetTrajectoryContent(c, args.TraceFile, CompressLoose), args.Query),
		}

		messages, err := prompt.ComposeVaryMessages(composeOptions)
		if err != nil {
			return nil, iris.NewRecoverable(pkgerrors.WithMessage(err, "failed to compose messages"))
		}

		// Call LLM
		result, err := c.GetLLM().ChatCompletion(c, messages, framework.LLMCompletionOption{
			Model:       CopilotLLMModelName,
			Temperature: CopilotLLMTemperature,
			MaxTokens:   CopilotLLMMaxTokens,
			Tag:         CopilotLLMTag,
		})

		if err != nil {
			// Check if the error is an OpenAI APIError with "text fields that are too large"
			var apiError *openai.APIError
			if errors.As(err, &apiError) && apiError.Code == 0 &&
				strings.Contains(apiError.Message, "Request contains text fields that are too large") {

				c.GetLogger().Infof("Detected OpenAI APIError for text too large, retrying with strict compression")

				// Retry with compressed trajectory content
				compressedComposeOptions := []prompt.ComposeVaryMessageOption{
					copilot.GetGptSystemPrompt(GetTrajectoryContent(c, args.TraceFile, CompressStrict), args.Query),
				}

				compressedMessages, compressErr := prompt.ComposeVaryMessages(compressedComposeOptions)
				if compressErr != nil {
					return nil, iris.NewRecoverable(pkgerrors.WithMessage(compressErr, "failed to compose compressed messages"))
				}

				// Retry LLM call with compressed content
				retryResult, retryErr := c.GetLLM().ChatCompletion(c, compressedMessages, framework.LLMCompletionOption{
					Model:       CopilotLLMModelName,
					Temperature: CopilotLLMTemperature,
					MaxTokens:   CopilotLLMMaxTokens,
					Tag:         CopilotLLMTag,
				})

				if retryErr != nil {
					return nil, iris.NewRecoverable(pkgerrors.WithMessage(retryErr, "failed to generate answer with compressed content"))
				}

				return &CopilotGptToolResult{
					Answer: retryResult.Content,
				}, nil
			}

			// For other errors, return the original error
			return nil, iris.NewRecoverable(pkgerrors.WithMessage(err, "failed to generate answer"))
		}

		return &CopilotGptToolResult{
			Answer: result.Content,
		}, nil
	})
}
