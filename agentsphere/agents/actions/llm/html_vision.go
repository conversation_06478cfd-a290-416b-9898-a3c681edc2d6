package llmtool

import (
	"context"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"code.byted.org/bcc/conf_engine/jsoniter"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
)

type ScratchResult struct {
	URL              string   `json:"url"`
	ConsoleLogs      []string `json:"console_logs"`
	Screenshot       []byte   `json:"-"`
	Base64Screenshot string   `json:"base64_screenshot"`
}

type ScratchError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
	Stack   string `json:"stack,omitempty"`
}

func (e *ScratchError) Error() string {
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

const (
	ToolHTMLVision            = "html_vision"
	ToolHTMLVisionDescription = `Verify if a webpage is available and functional. This tool will render the webpage as an image, capture console logs, and analyze the page to determine if it's working properly.
	Note: this tool only performs static checks, not interactive testing. For local HTML files, use file:///path/to/file.html directly - no need to start a local server.`
)

type HTMLVisionArgs struct {
	Task string `json:"task" mapstructure:"task" description:"specific static verification points to check (visual-only, no interactive testing)"`
	Url  string `json:"url" mapstructure:"url" description:"the url of html destination, https://xxx for deployed url and file:///path/xxx.html for local file"`
}

var (
	systemPrompt = `You are an AI assistant with powerful vision capabilities. You can ONLY analyze static screenshots and console logs of web pages.
Your main goal is to help the user identify visual issues in the HTML page based on what you can see in the static image and console logs. 

IMPORTANT LIMITATIONS:
- You can ONLY analyze static visual content and console logs
- You CANNOT perform any interactive testing (clicking, typing, scrolling, etc.)
- If the task requests interactive operations or anything beyond static analysis, simply ignore those requirements
- Focus only on what is visually observable in the provided screenshot

# Verification Checklist
Analyze the screenshot based on the following criteria. For each category, report any issues you find.
## 1. Layout & Alignment
- **Grid & Structure:** Does the page follow a consistent grid system? Are major sections and components logically structured?
- **Alignment:** Check for misalignment of elements. Are buttons, text fields, labels, and cards properly aligned horizontally and vertically with related elements?
- **Overlap & Overflow:** Are there any elements overlapping each other unintentionally? Is any text or content cut off or overflowing its container?
- **Consistency:** Are similar components (e.g., buttons, input fields) consistent in size, shape, and placement across the page?
## 2. Content Rendering & Loading
- **Images & Icons:**
  - Are all images and icons loaded correctly? Look for broken image placeholders or missing icons.
  - Are images sharp and high-resolution, or are they pixelated, blurry, or stretched?
- **Charts & Data Visualizations:**
  - Are charts and graphs fully rendered? Check for missing data points, broken axes, error messages, or empty states.
  - Is the data legible and clearly presented?
- **Text:** Is all text rendered correctly? Check for font loading issues or garbled characters.
## 3. Spacing & Typography
- **Whitespace:** Evaluate the use of margins and padding. Is there enough negative space to prevent a cluttered appearance? Is the spacing consistent and balanced?
- **Typography Hierarchy:** Is there a clear visual hierarchy for text (e.g., headings, subheadings, body text)? Are font sizes, weights, and styles applied consistently and logically?
- **Readability:** Is the line height and letter spacing adequate for comfortable reading?
## 4. Color, Contrast & Theming
- **Color Palette:** Is the application's color scheme used consistently? Are there any jarring or out-of-place colors?
- **Contrast Ratio:** Critically assess the text-to-background contrast. Is all important text easily readable? Pay special attention to body text, labels, and text on colored buttons. Flag any potential accessibility issues (low contrast).
## 5. Console Logs
- **Console Logs:** Analyze the console logs for meaningful errors and issues
- **Ignore Common Warnings:** Skip non-critical warnings such as Tailwind CSS CDN warnings that don't affect functionality

If no issues are found, state that "No significant visual issues were detected." DO NOT fabricate or invent problems where none exist. Only report genuine, observable issues.

## Remember your task is to help user find actual issues with the html page, do not tell user how to test or view the html page, that's your job. 
Be honest and accurate - if the page looks fine, say so. Don't create problems just to have something to report.
`
)

func NewHTMLVisionTool() iris.Action {
	return actions.ToTool(ToolHTMLVision, ToolHTMLVisionDescription, func(run *iris.AgentRunContext, args HTMLVisionArgs) (out map[string]any, err error) {
		defer func() {
			if err != nil {
				err = iris.NewRecoverable(err)
			}
		}()
		urlParse, err := url.Parse(args.Url)
		if err != nil {
			return nil, err
		}
		if urlParse.Scheme == "file" {
			path := filepath.Clean(urlParse.Path)
			if _, err = os.Stat(path); err != nil {
				if strings.HasPrefix(path, "/workspace/") {
					path = strings.TrimPrefix(path, "/workspace/")
					if _, err = os.Stat(path); err != nil {
						return nil, errors.Errorf("file %s not exist", path)
					}
					path, err = filepath.Abs(path)
					if err != nil {
						run.GetLogger().Errorf("filepath.Abs failed, err: %v", err)
						return nil, errors.Errorf("file %s not exist", path)
					}
					urlParse.Path = path
					args.Url = urlParse.String()
				} else {
					return nil, errors.Errorf("file %s not exist", path)
				}
			}
		}
		res, err := ScratchURL(run, args.Url)
		if err != nil {
			run.GetLogger().Errorf("ScratchURL failed, err: %v", err)
			// 不暴露内部错误给模型
			return map[string]any{
				"llm_response": "no issue",
			}, nil
		}
		if res == nil {
			return nil, errors.New("failed to scrape screenshot of given url, there might have some error in the page")
		}

		logs := jsoniter.MustMarshal(res.ConsoleLogs)
		if len(logs) > 20_000 {
			logs = logs[:20_000] + "\n Too many logs, truncated..."
		}

		// if failed to take screenshot, return the console logs only
		if len(res.Base64Screenshot) == 0 {
			return map[string]any{
				"llm_response": fmt.Sprintf("failed to take screenshot of given url, there might have some error in the page, the console logs are: \n %s", logs),
			}, nil
		}

		messages := []*framework.ChatMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role: "user",
				ContentParts: []*framework.LLMChatMessageContentPart{
					{
						Text: lo.ToPtr(fmt.Sprintf("The console logs are: \n %s", logs)),
					},
					{
						ImageURL: lo.ToPtr(res.Base64Screenshot),
					},
					{
						Text: lo.ToPtr(args.Task),
					},
				},
				Content: "",
			},
		}

		result, err := agents.Think(run, "vision", messages, agents.ThinkOption{
			StreamFilter: streamparser.StreamFilter{},
		})
		if err != nil {
			return nil, err
		}

		// Save image to artifact
		artifactService := run.GetArtifactService()
		artifact, err := artifactService.NewImageArtifact(run, nextentity.ImageArtifactTypeMetadata{})
		screenshotFile := fmt.Sprintf("screenshot_%s.png", time.Now().Format("20060102150405"))
		if err != nil {
			run.GetLogger().Errorf("Error creating image artifact: %v", err)
		} else {
			err = artifactService.UploadFiles(run, artifact, []iris.ArtifactFile{
				{
					Path:    screenshotFile,
					Content: res.Screenshot,
				},
			})
			if err != nil {
				run.GetLogger().Errorf("Error uploading image artifact: %v", err)
			}
		}

		response := map[string]any{
			"llm_response":  result.Content,
			"path":          screenshotFile,
			"image_content": res.Base64Screenshot,
		}
		if artifact != nil {
			response["artifact_id"] = artifact.ArtifactID
		}
		return response, nil
	})
}

func ScratchURL(run *iris.AgentRunContext, viewURL string) (result *ScratchResult, err error) {
	result = &ScratchResult{}
	result.URL = viewURL

	browserClient, err := browser.NewBrowserMCPClient(run)
	if err != nil {
		run.GetLogger().Errorf("failed to new a browser mcp service: %v", err)
		return nil, err
	}

	ctx, cancel := context.WithTimeout(run, 3*time.Minute)
	defer cancel()

	// 1. First goto the URL
	gotoRequest := mcp.CallToolRequest{}
	gotoRequest.Params.Name = "browser_goto"
	gotoRequest.Params.Arguments = map[string]any{
		"url": viewURL,
	}
	gotoResult, err := browserClient.Client.CallTool(ctx, gotoRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to goto url: %v", err)
		return nil, err
	}

	// Check if goto operation had errors
	if gotoResult.IsError {
		err := errors.New("browser goto operation failed")
		run.GetLogger().Errorf("Browser goto returned error: %v", err)
		return nil, err
	}

	// 2. Get console
	consoleRequest := mcp.CallToolRequest{}
	consoleRequest.Params.Name = "browser_get_console"
	consoleRequest.Params.Arguments = map[string]any{}
	r, err := browserClient.Client.CallTool(ctx, consoleRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to get console: %v", err)
		return nil, err
	}

	// Check if console operation had errors
	if r.IsError {
		err := errors.New("browser get console operation failed")
		run.GetLogger().Errorf("Browser get console returned error: %v", err)
		return nil, err
	}

	// Extract text content from result
	textContents := extractTextContent(r.Content)
	run.GetLogger().Infof("Successfully got console text result, result: %v", textContents)

	result.ConsoleLogs = textContents

	// 3. Get screenshot
	content, screenshot, err := takeScreenshot(run, browserClient)
	if err != nil || len(content) == 0 || len(screenshot) == 0 {
		run.GetLogger().Errorf("Failed to get screenshot: %v", err)
		// the page might have error and we can't get screenshot, but we can still use the console logs to analyze the page
		return result, nil
	}

	result.Screenshot = content
	result.Base64Screenshot = screenshot

	return result, nil
}

func takeScreenshot(run *iris.AgentRunContext, client *browser.MCPClient) (content []byte, imageData string, err error) {
	logger := run.GetLogger()

	var (
		image mcp.ImageContent
		resp  *mcp.CallToolResult
	)

	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = "browser_clear_screenshot"
	callRequest.Params.Arguments = map[string]any{}
	resp, err = client.Client.CallTool(run, callRequest)

	if err != nil {
		return nil, "", err
	}
	images := extractImageContent(resp.Content)
	if len(images) == 0 {
		logger.Info("browser screenshot: no image found")
		return nil, "", errors.New("no screenshot image found")
	}
	logger.Infof("browser screenshot: found %d images: %s", len(images), images[0].MIMEType)
	image = images[0]

	localPath := image.Data
	data, err := readImageData(localPath)
	if err != nil {
		return nil, "", err
	}

	base64Content := fmt.Sprintf("data:image/%s;base64,%s", image.MIMEType, base64.StdEncoding.EncodeToString(data))

	return data, base64Content, nil
}

// extractTextContent extracts text content from MCP Content array
func extractTextContent(contents []mcp.Content) []string {
	result := make([]string, 0, len(contents))
	for _, content := range contents {
		if content == nil {
			continue
		}

		if textContent, ok := content.(mcp.TextContent); ok {
			result = append(result, textContent.Text)
		}
	}
	return result
}

func extractImageContent(contents []mcp.Content) []mcp.ImageContent {
	result := make([]mcp.ImageContent, 0)
	for _, content := range contents {
		val := reflect.ValueOf(content)
		if val.FieldByName("Type").String() == "image" {
			result = append(result, content.(mcp.ImageContent))
		}
	}
	return result
}

func readImageData(localPath string) ([]byte, error) {
	imgbuff, err := ioutil.ReadFile(localPath)
	if err != nil {
		return nil, err
	}

	return imgbuff, nil
}
