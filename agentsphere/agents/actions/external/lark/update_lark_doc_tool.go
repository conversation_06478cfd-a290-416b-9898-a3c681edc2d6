package lark

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/port/lark"
	"github.com/samber/lo"
)

// UpdateLarkDocArgs defines the arguments for updating a Lark document
type UpdateLarkDocArgs struct {
	FilePath    string   `json:"file_path" mapstructure:"file_path" description:"必填，更新后的文件绝对路径，支持Markdown文件（.md、.markdown、.lark.md），比如：/workspace/iris_e7c707a5-ae78-42d0-b045-1882a9f0a4d7/updated.md，注意：1. 禁止填url 2. 文件路径必须真实存在（必须提前使用 pwd && ls 确认文件路径）"`
	DocumentURL string   `json:"document_url" mapstructure:"document_url" description:"必填，要更新的飞书文档完整链接，比如：https://bytedance.larkoffice.com/docx/TIPddm2mLog88Sxeq7JccYL3nJh"`
	CommentIDs  []string `json:"comment_ids" mapstructure:"comment_ids" description:"选填，要更新的飞书文档评论 ID 列表，列表中ID形如\"7545829559087759362\""`
}

const (
	ToolUpdateLarkDoc            = "update_lark_doc"
	ToolUpdateLarkDocDescription = "Update an existing Lark document based on a modified Markdown file (.md, .markdown, .lark.md). Must Use pwd && ls to confirm the file path before use.optional: resolve lark document comments"
)

// NewUpdateLarkDoc creates a new tool for updating Lark documents
func NewUpdateLarkDoc() iris.Action {
	return actions.ToTool(ToolUpdateLarkDoc, ToolUpdateLarkDocDescription, UpdateLarkDoc)
}

// UpdateLarkDoc updates an existing Lark document from a markdown file
func UpdateLarkDoc(run *iris.AgentRunContext, args UpdateLarkDocArgs) (map[string]any, error) {
	// Validate input parameters
	if args.FilePath == "" || args.DocumentURL == "" {
		return nil, fmt.Errorf("file_path and document_url are required")
	}

	// Check if file exists
	if _, err := os.Stat(args.FilePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file does not exist: %s", args.FilePath)
	}

	// Validate file extension
	ext := strings.ToLower(filepath.Ext(args.FilePath))
	if ext != ".md" && ext != ".markdown" && !strings.HasSuffix(strings.ToLower(args.FilePath), ".lark.md") {
		return nil, fmt.Errorf("unsupported file format: %s. Only .md, .markdown, and .lark.md files are supported", ext)
	}

	// Read the updated markdown content
	modifiedMarkdown, err := os.ReadFile(args.FilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %v", err)
	}
	lines := strings.Split(string(modifiedMarkdown), "\n")
	var filteredLines []string
	commentRegex := regexp.MustCompile(`<!--\s*comment\s+[^>]*-->`)
	for _, line := range lines {
		cleanedLine := commentRegex.ReplaceAllString(line, "")
		filteredLines = append(filteredLines, cleanedLine)
	}
	modifiedMarkdown = []byte(strings.Join(filteredLines, "\n"))

	// Parse document URL to get document ID
	client := larkClient(run)
	_, fileType, documentID, _, err := PrepareLarkURL(client, args.DocumentURL, "")
	if err != nil {
		return nil, fmt.Errorf("failed to parse document URL: %v", err)
	}

	// Only support docx format for updates
	if fileType != "docx" {
		return nil, fmt.Errorf("unsupported document type: %s. Only docx documents are supported for updates", fileType)
	}

	userAccessToken := run.GetEnv(entity.RunTimeLarkUserAccessToken)
	// Download the original document to get current content
	emptyFilter := []string{"empty"}
	downloadResult, err := downloadLarkDocumentAndCommentFilter(run, client, args.DocumentURL, userAccessToken, false, emptyFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to download original document: %v", err)
	}

	// Read the original markdown content from the downloaded file
	var originalMarkdown string
	if downloadResult != nil && downloadResult.FilePath != "" {
		originalMarkdownBytes, readErr := os.ReadFile(downloadResult.FilePath)
		if readErr != nil {
			return nil, fmt.Errorf("failed to read original document file: %v", readErr)
		}
		originalMarkdown = string(originalMarkdownBytes)
	} else {
		return nil, fmt.Errorf("no file path returned from document download")
	}

	// Update the document using the markdown updater
	ctx := context.Background()
	result, err := UpdateLarkDocumentFromMarkdown(
		ctx,
		run,
		client,
		documentID,
		originalMarkdown,
		string(modifiedMarkdown),
		"",
		args.FilePath,
	)
	if err != nil && !result.Success {
		return nil, fmt.Errorf("failed to update document: %v", err)
	}

	// Resolve all comments for URL-based copilot items
	for _, commentID := range lo.Filter(args.CommentIDs, func(id string, _ int) bool {
		return id != ""
	}) {
		_, err := client.ResolveLarkDocComment(ctx, documentID, fileType, commentID, true, userAccessToken)
		if err != nil {
			run.GetLogger().Errorf("failed to resolve comment %s for document %s: %v", commentID, documentID, err)
		} else {
			run.GetLogger().Infof("successfully resolved comment %s for document %s", commentID, documentID)
		}
	}

	// Get document title through downloadResult.FilePath
	fileName := filepath.Base(downloadResult.FilePath)
	fileSufix := lo.Ternary(strings.HasSuffix(fileName, ".lark.md"), ".lark.md", filepath.Ext(fileName))
	title := strings.TrimSuffix(fileName, fileSufix)
	documentURL := fmt.Sprintf("https://bytedance.larkoffice.com/docx/%s", documentID)
	return map[string]any{
		"document_id":  documentID,
		"document_url": documentURL,
		"description":  fmt.Sprintf("飞书文档更新成功，请在%s查看", documentURL),
		"title":        title,
		"reference":    fmt.Sprintf("[%s]%s", title, documentURL),
	}, nil
}

// UpdateLarkDocumentFromMarkdown updates a Lark document from modified markdown content
func UpdateLarkDocumentFromMarkdown(
	ctx context.Context,
	run *iris.AgentRunContext,
	client lark.Client,
	documentID string,
	originalMarkdown string,
	modifiedMarkdown string,
	larkAccessToken string,
	filePath string,
) (*UpdateResult, error) {
	logger := run.GetLogger()
	logger.Infof("Starting document update for document ID: %s", documentID)

	// Step 1: Create markdown updater
	updater := NewMarkdownUpdater(run, client)

	// Step 2: Get original document blocks
	blocks, err := client.GetLarkDocxBlocks(ctx, documentID, larkAccessToken)
	if err != nil {
		logger.Errorf("Failed to get document blocks: %v", err)
		return &UpdateResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to get document blocks: %v", err),
		}, err
	}

	logger.Infof("Retrieved %d blocks from original document", len(blocks))

	// Step 3: Create block mapping for original document
	originalMapping, err := updater.CreateBlockMapping(ctx, documentID, blocks, originalMarkdown, larkAccessToken)
	if err != nil {
		logger.Errorf("Failed to create block mapping: %v", err)
		return &UpdateResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to create block mapping: %v", err),
		}, err
	}

	logger.Infof("Created block mapping with %d containers", len(originalMapping.ContainerMaps))

	// Step 4: Compare original and modified markdown
	diff := updater.CompareMarkdown(originalMarkdown, modifiedMarkdown)
	logger.Infof("Detected %d changes in markdown", diff.LineMappings)

	// Step 5: Parse modified markdown to get resource information
	markdownResult, err := ProcessLarkMarkdownForUpdate(run, modifiedMarkdown, filePath, &ProcessLarkMarkdownFileOptions{})
	if err != nil {
		logger.Errorf("Failed to parse modified markdown: %v", err)
		return &UpdateResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to parse modified markdown: %v", err),
		}, err
	}

	// Step 6: Convert blocks slice to map for efficient lookup
	blocksMap := createBlockMap(blocks)

	// Step 7: Generate update operations (delete list and insert groups)
	deleteList, insertGroups, err := updater.GenerateUpdateOperations(originalMapping, markdownResult, diff, blocksMap)
	if err != nil {
		logger.Errorf("Failed to generate update operations: %v", err)
		return &UpdateResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to generate update operations: %v", err),
		}, err
	}

	logger.Infof("Generated %d delete operations and %d insert groups", len(deleteList), len(insertGroups))

	// Step 7: Execute the document update with resource processing
	result := updater.ExecuteDocumentUpdate(ctx, documentID, deleteList, insertGroups, larkAccessToken, markdownResult, filePath)

	if result.Success {
		logger.Infof("Document update completed successfully: updated=%d, created=%d, deleted=%d",
			result.UpdatedBlocks, result.CreatedBlocks, result.DeletedBlocks)
	} else {
		logger.Errorf("Document update failed: %s", result.Error)
	}

	return result, nil
}
