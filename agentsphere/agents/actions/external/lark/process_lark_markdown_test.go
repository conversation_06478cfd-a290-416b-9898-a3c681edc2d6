package lark

import (
	"strings"
	"testing"
)

func TestCheckContentIndentation(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		wantErr  bool
		errMsg   string
	}{
		{
			name: "正确的缩进 - content:|后内容有正确缩进",
			input: `  content:|
    这是正确缩进的内容
    另一行内容`,
			wantErr: false,
		},
		{
			name: "缺少缩进 - content:|后内容缩进不足",
			input: `  content:|
  这是缩进不足的内容`,
			wantErr: true,
			errMsg: "第1行content:|后的内容（第2行）缺少缩进，需要在content:|缩进基础上增加2个空格",
		},
		{
			name: "遇到width_ratio结束检查",
			input: `  content:|
    正确缩进的内容
  - width_ratio: 0.5
  错误缩进但不检查`,
			wantErr: false,
		},
		{
			name: "遇到小缩进的```结束检查",
			input: "    content:|\n      正确缩进的内容\n  ```\n错误缩进但不检查",
			wantErr: false,
		},
		{
			name: "```有其他字符不结束检查",
			input: "  content:|\n    正确缩进的内容\n```python\n错误缩进应该检查",
			wantErr: true,
			errMsg: "第1行content:|后的内容（第3行）缺少缩进，需要在content:|缩进基础上增加2个空格",
		},
		{
			name: "跳过空行",
			input: `  content:|
    正确缩进的内容

    另一行正确缩进`,
			wantErr: false,
		},
		{
			name: "无缩进的content:|",
			input: `content:|
  正确缩进的内容
  另一行内容`,
			wantErr: false,
		},
		{
			name: "无缩进content:|后内容缺少缩进",
			input: `content:|
错误缩进的内容`,
			wantErr: true,
			errMsg: "第1行content:|后的内容（第2行）缺少缩进，需要在content:|缩进基础上增加2个空格",
		},
		{
			name: "多个content:|块",
			input: `  content:|
    第一个块正确缩进
  - width_ratio: 0.5
    content:|
      第二个块正确缩进`,
			wantErr: false,
		},
		{
			name: "第二个content:|块缩进错误",
			input: `  content:|
    第一个块正确缩进
  - width_ratio: 0.5
    content:|
    第二个块缩进错误`,
			wantErr: true,
			errMsg: "第4行content:|后的内容（第5行）缺少缩进，需要在content:|缩进基础上增加2个空格",
		},
		{
			name: "```缩进等于content:|缩进结束检查",
			input: "  content:|\n    正确缩进的内容\n  ```\n错误缩进应该检查",
			wantErr: false,
		},
		{
			name: "```缩进大于content:|缩进不结束",
			input: "  content:|\n    正确缩进的内容\n    ```\n错误缩进应该检查",
			wantErr: true,
			errMsg: "第1行content:|后的内容（第4行）缺少缩进，需要在content:|缩进基础上增加2个空格",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			lines := strings.Split(tt.input, "\n")
			err := checkContentIndentation(lines)

			if tt.wantErr {
				if err == nil {
					t.Errorf("checkContentIndentation() 期望返回错误，但没有错误")
					return
				}
				if !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("checkContentIndentation() 错误信息 = %v, 期望包含 %v", err.Error(), tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("checkContentIndentation() 期望无错误，但返回错误 = %v", err)
				}
			}
		})
	}
}

func TestCheckContentIndentationEdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		input    []string
		wantErr  bool
	}{
		{
			name:    "空切片",
			input:   []string{},
			wantErr: false,
		},
		{
			name:    "没有content:|的内容",
			input:   []string{"普通内容", "另一行内容"},
			wantErr: false,
		},
		{
			name:    "content:|是最后一行",
			input:   []string{"  content:|"},
			wantErr: false,
		},
		{
			name:    "content:|后只有空行",
			input:   []string{"  content:|", "", "   "},
			wantErr: false,
		},
		{
			name:    "使用制表符缩进",
			input:   []string{"\tcontent:|", "\t\t正确缩进"},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkContentIndentation(tt.input)
			if tt.wantErr && err == nil {
				t.Errorf("checkContentIndentation() 期望返回错误，但没有错误")
			}
			if !tt.wantErr && err != nil {
				t.Errorf("checkContentIndentation() 期望无错误，但返回错误 = %v", err)
			}
		})
	}
}