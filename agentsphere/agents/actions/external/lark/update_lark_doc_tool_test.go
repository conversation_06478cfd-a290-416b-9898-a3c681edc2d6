package lark

// import (
// 	"context"
// 	"testing"

// 	"code.byted.org/gopkg/jsonx"
// 	"github.com/bytedance/mockey"
// 	"github.com/sirupsen/logrus"

// 	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
// 	planentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
// 	"code.byted.org/devgpt/kiwis/agentsphere/entity"
// )

// func TestLarkDocTool_UpdateLarkMarkdownFile(t *testing.T) {
// 	defer mockey.UnPatchAll()

// 	appID := "cli_a752fda7a2f9d01c"
// 	appSecret := "QRW7OJ6RI9qihsS8OfZTDbKjSuq0B2sN"
// 	environ := iris.NewRunEnviron()
// 	environ.Set(entity.RunTimeLarkAppID, appID)
// 	environ.Set(entity.RunTimeLarkAppSecret, appSecret)
// 	environ.Set(entity.RuntimeTCEHostEnv, "boe")
// 	environ.Set(entity.RuntimeEnvironUserCloudJWT, "")
// 	environ.Set(entity.RunTimeLarkUserAccessToken, "**********************************************")
// 	c := &iris.AgentRunContext{
// 		Environ: environ,
// 		User: entity.User{
// 			Type:     "user",
// 			Username: "xujiguang.kuper",
// 		},
// 	}

// 	mockey.Mock(iris.Logger.Infof).Return().Build()
// 	mockey.Mock(iris.Logger.Errorf).Return().Build()
// 	mockey.Mock((*iris.AgentRunContext).GetLogger).Return(&logrus.Logger{}).Build()
// 	mockey.MockGeneric(iris.RetrieveStoreByKey[planentity.ReferenceStore]).Return(planentity.ReferenceStore{}).Build()
// 	mockey.Mock(uploadRelatedFiles).Return(nil).Build()

// 	args := UpdateLarkDocArgs{
// 		FilePath:    "/Users/<USER>/Documents/workspace/aime/kiwis/agentsphere/agents/actions/external/lark/飞书文档调试模板1.lark.md",
// 		DocumentURL: "https://bytedance.larkoffice.com/docx/EjBYdvKvlo713dxYh2Qc30mNnQb",
// 		//CommentIDs:  []string{"7546078148799217666"},
// 	}

// 	doc, err := UpdateLarkDoc(c, args)
// 	if err != nil {
// 		t.Errorf("ListWorkItems() error = %v", err)
// 		return
// 	}
// 	t.Logf("ListWorkItems() got = %s", jsonx.ToString(doc))
// }

// func TestLarkDocTool_Resolve_Comment(t *testing.T) {
// 	defer mockey.UnPatchAll()

// 	appID := "cli_9a31b280a1f3d101"
// 	appSecret := "E98UDT0US21Cz4UNlvWycdDBiOpLwuaz"
// 	environ := iris.NewRunEnviron()
// 	environ.Set(entity.RunTimeLarkAppID, appID)
// 	environ.Set(entity.RunTimeLarkAppSecret, appSecret)
// 	environ.Set(entity.RuntimeTCEHostEnv, "boe")
// 	environ.Set(entity.RuntimeEnvironUserCloudJWT, "")
// 	environ.Set(entity.RunTimeLarkUserAccessToken, "**********************************************")
// 	c := &iris.AgentRunContext{
// 		Environ: environ,
// 		User: entity.User{
// 			Type:     "user",
// 			Username: "xujiguang.kuper",
// 		},
// 	}

// 	mockey.Mock(iris.Logger.Infof).Return().Build()
// 	mockey.Mock(iris.Logger.Errorf).Return().Build()
// 	mockey.Mock((*iris.AgentRunContext).GetLogger).Return(&logrus.Logger{}).Build()
// 	mockey.MockGeneric(iris.RetrieveStoreByKey[planentity.ReferenceStore]).Return(planentity.ReferenceStore{}).Build()
// 	mockey.Mock(uploadRelatedFiles).Return(nil).Build()

// 	client := larkClient(c)
// 	_, err := client.ResolveLarkDocComment(context.Background(), "MtTTdeWXioPmvvxj54ZcP0Yknjg", "docx", "7548075027775750145", true, c.GetEnv(entity.RunTimeLarkUserAccessToken))
// 	print("%s", err)
// }
