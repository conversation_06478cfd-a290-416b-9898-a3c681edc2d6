package renderer

import (
	json "github.com/bytedance/sonic"
	"github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"

	"code.byted.org/devgpt/kiwis/port/lark"
)

// CodeBlockRenderer is a dedicated renderer for code blocks that outputs Lark blocks
// instead of the default HTML <pre><code> tags
type CodeBlockRenderer struct {
	html.Config
}

// NewCodeBlockRenderer creates a new CodeBlockRenderer
func NewCodeBlockRenderer(opts ...html.Option) renderer.NodeRenderer {
	r := &CodeBlockRenderer{
		Config: html.NewConfig(),
	}
	for _, opt := range opts {
		opt.SetHTMLOption(&r.Config)
	}
	return r
}

// RegisterFuncs registers the renderer function for indented code blocks
func (r *CodeBlockRenderer) RegisterFuncs(reg renderer.NodeRendererFuncRegisterer) {
	reg.Register(ast.KindCodeBlock, r.renderCodeBlock)
}

// renderCodeBlock renders an indented code block as a Lark block instead of <pre><code>
func (r *CodeBlockRenderer) renderCodeBlock(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		n := node.(*ast.CodeBlock)

		// 缩进代码块没有语言信息，默认为纯文本
		codeLanguage := lark.DocxCodeLanguagePlainText

		// 生成代码块元素
		codeElements := r.generateCodeBlockElements(n, source)

		if len(codeElements) == 0 {
			codeElements = []*lark.DocxTextElement{
				{
					TextRun: &lark.DocxTextElementTextRun{
						Content: "\n",
					},
				},
			}
		}

		// 创建 Lark 代码块
		codeBlock := &lark.DocxBlockText{
			Elements: codeElements,
			Style: &lark.DocxTextStyle{
				Language: codeLanguage,
				Wrap:     true, // 自动换行
			},
		}

		blockID := generateBlockID()

		// 生成 Lark DocxBlock JSON
		jsonData, err := json.Marshal(&lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeCode,
			Code:      codeBlock,
		})
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)

		// 跳过子节点处理
		return ast.WalkSkipChildren, nil
	}

	return ast.WalkContinue, nil
}

// generateCodeBlockElements creates text elements from an indented code block node
func (r *CodeBlockRenderer) generateCodeBlockElements(n *ast.CodeBlock, source []byte) []*lark.DocxTextElement {
	elements := []*lark.DocxTextElement{}
	if n.Lines() != nil {
		lines := n.Lines()

		// 遍历所有代码行
		for i := 0; i < lines.Len(); i++ {
			line := lines.At(i)
			content := string(line.Value(source))

			// 创建文本元素
			textElement := &lark.DocxTextElement{
				TextRun: &lark.DocxTextElementTextRun{
					Content: content,
				},
			}

			elements = append(elements, textElement)
		}
	}
	return elements
}
