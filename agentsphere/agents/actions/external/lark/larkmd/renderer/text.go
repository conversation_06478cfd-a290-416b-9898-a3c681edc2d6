package renderer

import (
	"encoding/json"
	"regexp"
	"strings"

	"github.com/yuin/goldmark/ast"
	extast "github.com/yuin/goldmark/extension/ast"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"

	larkmdast "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark/larkmd/ast"

	"github.com/yuin/goldmark"
	"github.com/yuin/goldmark/extension"
	"github.com/yuin/goldmark/text"

	"code.byted.org/devgpt/kiwis/port/lark"
)

const userOpenIDPattern = `ou_[a-f0-9]{32}`

// parseHTMLEntities replaces HTML entities with their corresponding characters
func parseHTMLEntities(content string) string {
	content = strings.ReplaceAll(content, "&emsp;", " ")
	content = strings.ReplaceAll(content, "&ensp;", " ")
	content = strings.ReplaceAll(content, "&nbsp;", " ")
	return content
}

// TextRenderer is a unified renderer for all text-related nodes including
// text, string, text block, emphasis, heading and list nodes
type TextRenderer struct {
	html.Config
}

// NewTextRenderer returns a new TextRenderer
func NewTextRenderer(opts ...html.Option) renderer.NodeRenderer {
	r := &TextRenderer{}
	for _, opt := range opts {
		opt.SetHTMLOption(&r.Config)
	}
	return r
}

// RegisterFuncs registers renderer functions for all supported node types
func (r *TextRenderer) RegisterFuncs(reg renderer.NodeRendererFuncRegisterer) {
	// Basic text nodes
	reg.Register(ast.KindText, r.renderText)
	reg.Register(ast.KindString, r.renderString)
	reg.Register(ast.KindTextBlock, r.renderTextBlock)

	// Additional nodes that use TextRenderer
	reg.Register(ast.KindEmphasis, r.renderEmphasis)
	reg.Register(ast.KindHeading, r.renderHeading)
	reg.Register(ast.KindLink, r.renderLink)
	reg.Register(ast.KindCodeSpan, r.renderCodeSpan)
	reg.Register(ast.KindFencedCodeBlock, r.renderFencedCodeBlock)
	reg.Register(ast.KindParagraph, r.renderParagraph)
	reg.Register(ast.KindBlockquote, r.renderBlockquote)

	// 注册任务列表节点
	reg.Register(extast.KindTaskCheckBox, r.renderTaskCheckBox)

	// 注册删除线节点
	reg.Register(extast.KindStrikethrough, r.renderStrikethrough)

	// 注册字体颜色节点
	reg.Register(larkmdast.KindFontColor, r.renderFontColor)

	// 注册原始HTML节点
	reg.Register(ast.KindRawHTML, r.renderRawHTML)
}

// renderText renders a text node
func (r *TextRenderer) renderText(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		n := node.(*ast.Text)

		// Generate text block
		textBlock := r.generateTextBlock(n, source)
		blockID := generateBlockID()
		// Generate and render the text element as JSON
		jsonData, err := json.Marshal(&lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeText,
			Text:      textBlock,
		})
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)

		// Skip further processing since we've handled all children
		return ast.WalkSkipChildren, nil
	}

	return ast.WalkContinue, nil
}

// renderString renders a string node
func (r *TextRenderer) renderString(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		n := node.(*ast.String)

		// Generate text element
		textElement := r.generateStringTextElement(n)

		// Create text block with the string element
		textBlock := &lark.DocxBlockText{
			Elements: []*lark.DocxTextElement{textElement},
			Style:    &lark.DocxTextStyle{},
		}

		// Generate and render the text element as JSON
		blockID := generateBlockID()
		jsonData, err := json.Marshal(&lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeText,
			Text:      textBlock,
		})
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)

		// Skip further processing since we've handled all children
		return ast.WalkSkipChildren, nil
	}

	return ast.WalkContinue, nil
}

// renderTextBlock renders a text block node
func (r *TextRenderer) renderTextBlock(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		n := node.(*ast.TextBlock)
		if r.containsNoTextNode(n) {
			if node.Parent() != nil && node.Parent().Kind() == ast.KindListItem && r.isSimpleTextNode(n.FirstChild()) {
				node.Parent().SetAttributeString("text_elements", r.NodeToTextElements(n.FirstChild(), source))
				n.RemoveChild(n, n.FirstChild())
			}
			return ast.WalkContinue, nil
		}

		// 使用 generateTextBlockElements 方法创建文本元素列表
		elements := r.generateTextBlockElements(n, source)

		// 创建文本块
		textBlock := &lark.DocxBlockText{
			Elements: elements,
			Style:    &lark.DocxTextStyle{},
		}

		// 生成并渲染JSON
		blockID := generateBlockID()
		jsonData, err := json.Marshal(&lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeText,
			Text:      textBlock,
		})
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)

		// 跳过进一步处理，因为我们已经处理了所有子节点
		return ast.WalkSkipChildren, nil
	}

	if _, ok := node.AttributeString("block_id"); ok {
		return ast.WalkSkipChildren, nil
	}
	childrenBlockIDs := GetChildrenBlockIDs(node)
	if len(childrenBlockIDs) > 0 {
		node.SetAttributeString("block_id", childrenBlockIDs)
	}

	return ast.WalkContinue, nil
}

// generateTextElement creates DocxTextElements from a text node
func (r *TextRenderer) generateTextElement(n *ast.Text, source []byte) []*lark.DocxTextElement {
	var textContent string

	// 检查是否有合并后的内容
	if mergedContent, ok := n.AttributeString("merged_content"); ok {
		if content, ok := mergedContent.(string); ok {
			textContent = parseHTMLEntities(content)
		} else {
			// 如果属性存在但类型不对，fallback到原始内容
			textContent = parseHTMLEntities(string(n.Segment.Value(source)))
		}
	} else {
		// 使用原始的segment内容
		textContent = parseHTMLEntities(string(n.Segment.Value(source)))
	}

	// 查找所有用户OpenID
	re := regexp.MustCompile(userOpenIDPattern)
	matches := re.FindAllString(textContent, -1)

	if len(matches) == 0 {
		// 没有找到OpenID，返回普通文本元素
		textRun := &lark.DocxTextElementTextRun{
			Content: textContent,
		}
		return []*lark.DocxTextElement{{
			TextRun: textRun,
		}}
	}

	// 有OpenID，直接转换为mention元素
	var elements []*lark.DocxTextElement
	for _, openID := range matches {
		elements = append(elements, &lark.DocxTextElement{
			MentionUser: &lark.DocxTextElementMentionUser{
				UserID: openID,
			},
		})
	}

	return elements
}

// generateTextBlock creates a DocxBlockText from a text node
func (r *TextRenderer) generateTextBlock(n *ast.Text, source []byte) *lark.DocxBlockText {
	textElements := r.generateTextElement(n, source)
	if len(textElements) == 0 {
		textElements = []*lark.DocxTextElement{{
			TextRun: &lark.DocxTextElementTextRun{
				Content: "\n",
			},
		}}
	}

	// Create DocxBlockText with the text elements
	return &lark.DocxBlockText{
		Elements: textElements,
		Style:    &lark.DocxTextStyle{},
	}
}

// generateTextBlockElement 将 TextBlock 节点转换为文本元素列表
func (r *TextRenderer) generateTextBlockElements(n *ast.TextBlock, source []byte) []*lark.DocxTextElement {
	elements := []*lark.DocxTextElement{}

	// 遍历所有子节点，收集文本元素
	for c := n.FirstChild(); c != nil; c = c.NextSibling() {
		elements = append(elements, r.NodeToTextElements(c, source)...)
	}

	// 如果没有找到有效内容，返回一个空文本元素
	if len(elements) == 0 {
		elements = append(elements, &lark.DocxTextElement{
			TextRun: &lark.DocxTextElementTextRun{
				Content: "\n",
			},
		})
	}

	return elements
}

// generateStringTextElement creates a DocxTextElement from a string node
func (r *TextRenderer) generateStringTextElement(n *ast.String) *lark.DocxTextElement {
	// Get string content and parse HTML entities
	content := parseHTMLEntities(string(n.Value))

	// Apply appropriate style for code if needed
	var style *lark.DocxTextElementStyle
	if n.IsCode() {
		style = &lark.DocxTextElementStyle{
			InlineCode: true,
		}
	}

	// Create text element
	textRun := &lark.DocxTextElementTextRun{
		Content:          content,
		TextElementStyle: style,
	}

	return &lark.DocxTextElement{
		TextRun: textRun,
	}
}

// NodeToTextElements converts an ast.Node to []*lark.DocxTextElement based on node type
// If processAllSiblings is true, it will process all siblings of the node as well
func (r *TextRenderer) NodeToTextElements(node ast.Node, source []byte, processAllSiblings ...bool) []*lark.DocxTextElement {
	elements := []*lark.DocxTextElement{}

	// Handle the case when node is nil
	if node == nil {
		return elements
	}

	// Process the current node
	currentNode := node

	// Handle node based on its Kind
	switch currentNode.Kind() {
	case ast.KindText:
		// Convert text node
		textNode := currentNode.(*ast.Text)
		elements = append(elements, r.generateTextElement(textNode, source)...)

	case ast.KindString:
		// Convert string node
		stringNode := currentNode.(*ast.String)
		elements = append(elements, r.generateStringTextElement(stringNode))

	case ast.KindEmphasis:
		// Convert emphasis (italic) node
		emphNode := currentNode.(*ast.Emphasis)
		elements = append(elements, r.generateEmphasisTextElement(emphNode, source)...)

	case ast.KindLink:
		// Convert link node
		linkNode := currentNode.(*ast.Link)
		elements = append(elements, r.generateLinkTextElement(linkNode, source)...)

	case ast.KindCodeSpan:
		// Convert code span node
		codeNode := currentNode.(*ast.CodeSpan)
		elements = append(elements, r.generateCodeSpanTextElement(codeNode, source)...)

	case ast.KindTextBlock:
		textBlockNode := currentNode.(*ast.TextBlock)
		elements = append(elements, r.generateTextBlockElements(textBlockNode, source)...)
	case extast.KindStrikethrough:
		strikethroughNode := currentNode.(*extast.Strikethrough)
		elements = append(elements, r.generateStrikethroughElements(strikethroughNode, source)...)
	case ast.KindParagraph:
		paragraphNode := currentNode.(*ast.Paragraph)
		elements = append(elements, r.generateParagraphElements(paragraphNode, source)...)
	case larkmdast.KindMathInline:
		// Convert inline math node using MathRenderer
		mathRenderer := NewMathRenderer().(*MathRenderer)
		inlineEqElement := mathRenderer.NodeToInlineEquationElement(currentNode, source)
		if inlineEqElement != nil {
			elements = append(elements, inlineEqElement)
		}
	case larkmdast.KindMathBlock:
		mathRenderer := NewMathRenderer().(*MathRenderer)
		blockEqElement := mathRenderer.NodeToBlockEquationElement(currentNode, source)
		if blockEqElement != nil {
			elements = append(elements, blockEqElement)
		}
	case larkmdast.KindFontColor:
		// Convert font color node
		fontColorNode := currentNode.(*larkmdast.FontColor)
		elements = append(elements, r.generateFontColorElements(fontColorNode, source)...)
	case ast.KindRawHTML:
		// Convert raw HTML node
		rawHTMLNode := currentNode.(*ast.RawHTML)
		htmlContent := string(rawHTMLNode.Segments.Value(source))
		elements = append(elements, r.generateHTMLTextElement(htmlContent))
	default:
		// For other node types, try to extract text if there are child nodes
		if currentNode.FirstChild() != nil {
			childElements := r.NodeToTextElements(currentNode.FirstChild(), source, true)
			elements = append(elements, childElements...)
		}
	}

	if len(elements) == 0 {
		elements = append(elements, &lark.DocxTextElement{
			TextRun: &lark.DocxTextElementTextRun{
				Content: "",
			},
		})
	}
	return elements
}

// renderFontColor renders a font color node
func (r *TextRenderer) renderFontColor(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		n := node.(*larkmdast.FontColor)

		// Generate text block with font color
		textBlock := r.generateFontColorTextBlock(n, source)
		blockID := generateBlockID()

		// Generate and render the text element as JSON
		jsonData, err := json.Marshal(&lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeText,
			Text:      textBlock,
		})
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)

		// Skip further processing since we've handled all children
		return ast.WalkSkipChildren, nil
	}

	return ast.WalkContinue, nil
}

// generateFontColorElements creates DocxTextElements from a font color node
func (r *TextRenderer) generateFontColorElements(n *larkmdast.FontColor, source []byte) []*lark.DocxTextElement {
	elements := []*lark.DocxTextElement{}

	// Convert color string to DocxFontColor enum
	colorValue := stringToFontColor(n.Color)

	// Use TextContent if available, otherwise process child nodes
	if n.TextContent != "" {
		// Parse the TextContent as markdown to handle formatting like **bold**
		// Create a temporary markdown parser to process the content
		md := goldmark.New(
			goldmark.WithExtensions(
				extension.GFM,
			),
		)

		// Parse the text content as markdown
		doc := md.Parser().Parse(text.NewReader([]byte(n.TextContent)))

		// Process the parsed content
		for child := doc.FirstChild(); child != nil; child = child.NextSibling() {
			childElements := r.NodeToTextElements(child, []byte(n.TextContent))
			for _, element := range childElements {
				if element.TextRun != nil {
					// Ensure TextElementStyle exists
					if element.TextRun.TextElementStyle == nil {
						element.TextRun.TextElementStyle = &lark.DocxTextElementStyle{}
					}
					// Apply font color
					element.TextRun.TextElementStyle.TextColor = colorValue
				}
			}
			elements = append(elements, childElements...)
		}

		// If no elements were generated, create a simple text element
		if len(elements) == 0 {
			textRun := &lark.DocxTextElementTextRun{
				Content: n.TextContent,
				TextElementStyle: &lark.DocxTextElementStyle{
					TextColor: colorValue,
				},
			}
			elements = append(elements, &lark.DocxTextElement{
				TextRun: textRun,
			})
		}
	} else {
		// Process all child nodes (fallback for backward compatibility)
		for c := n.FirstChild(); c != nil; c = c.NextSibling() {
			childElements := r.NodeToTextElements(c, source)
			for _, element := range childElements {
				if element.TextRun != nil {
					// Ensure TextElementStyle exists
					if element.TextRun.TextElementStyle == nil {
						element.TextRun.TextElementStyle = &lark.DocxTextElementStyle{}
					}
					// Apply font color
					element.TextRun.TextElementStyle.TextColor = colorValue
				}
			}
			elements = append(elements, childElements...)
		}
	}

	return elements
}

// generateFontColorTextBlock creates a DocxBlockText from a font color node
func (r *TextRenderer) generateFontColorTextBlock(n *larkmdast.FontColor, source []byte) *lark.DocxBlockText {
	textElements := r.generateFontColorElements(n, source)

	// Create DocxBlockText with the font color elements
	return &lark.DocxBlockText{
		Elements: textElements,
		Style:    &lark.DocxTextStyle{},
	}
}

// stringToFontColor converts color string to DocxFontColor enum
func stringToFontColor(colorStr string) lark.DocxFontColor {
	switch colorStr {
	case "1":
		return 1 // 红色
	case "2":
		return 2 // 橙色
	case "3":
		return 3 // 黄色
	case "4":
		return 4 // 绿色
	case "5":
		return 5 // 蓝色
	case "6":
		return 6 // 紫色
	case "7":
		return 7 // 灰色
	default:
		return 1 // 默认红色
	}
}

// renderRawHTML renders a raw HTML node as a text block
func (r *TextRenderer) renderRawHTML(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		n := node.(*ast.RawHTML)

		// Get the HTML content and create text element
		htmlContent := string(n.Segments.Value(source))
		textElement := r.generateHTMLTextElement(htmlContent)

		// Create text block
		textBlock := &lark.DocxBlockText{
			Elements: []*lark.DocxTextElement{textElement},
			Style:    &lark.DocxTextStyle{},
		}

		blockID := generateBlockID()

		// Generate and render the text element as JSON
		jsonData, err := json.Marshal(&lark.DocxBlock{
			BlockID:   blockID,
			BlockType: lark.DocxBlockTypeText,
			Text:      textBlock,
		})
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)

		// Skip further processing since we've handled all children
		return ast.WalkSkipChildren, nil
	}

	return ast.WalkContinue, nil
}

// generateHTMLTextElement creates a DocxTextElement from raw HTML content
func (r *TextRenderer) generateHTMLTextElement(htmlContent string) *lark.DocxTextElement {
	// Parse HTML entities and create a text element
	content := parseHTMLEntities(htmlContent)

	// Create text element as plain text
	textRun := &lark.DocxTextElementTextRun{
		Content: content,
	}

	return &lark.DocxTextElement{
		TextRun: textRun,
	}
}
