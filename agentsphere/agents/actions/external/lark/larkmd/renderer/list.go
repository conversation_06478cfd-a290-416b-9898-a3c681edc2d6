package renderer

import (
	"strconv"

	json "github.com/bytedance/sonic"
	"github.com/yuin/goldmark/ast"
	extast "github.com/yuin/goldmark/extension/ast"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"

	larkmdast "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark/larkmd/ast"

	"code.byted.org/devgpt/kiwis/port/lark"
)

// NewListHTMLRenderer returns a new TextRenderer for list blocks
func NewListHTMLRenderer(opts ...html.Option) renderer.NodeRenderer {
	r := &TextRenderer{
		Config: html.NewConfig(),
	}
	for _, opt := range opts {
		opt.SetHTMLOption(&r.Config)
	}
	return &ListRenderer{TextRenderer: r}
}

// ListRenderer wraps TextRenderer to provide list-specific functionality
type ListRenderer struct {
	*TextRenderer
}

// RegisterFuncs registers renderer functions for list-related nodes only
func (r *ListRenderer) RegisterFuncs(reg renderer.NodeRendererFuncRegisterer) {
	// Only register list-related nodes
	reg.Register(ast.KindList, r.renderList)
	reg.Register(ast.KindListItem, r.renderListItem)
}

// renderList renders a list block - only collects child IDs and passes them up
func (r *TextRenderer) renderList(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.List)

	if entering {
		return ast.WalkContinue, nil
	}

	if !entering {
		// After processing all children, collect their IDs
		var childrenIDs []string
		for c := n.FirstChild(); c != nil; c = c.NextSibling() {
			if childBlockID, ok := c.AttributeString("block_id"); ok {
				switch v := childBlockID.(type) {
				case string:
					childrenIDs = append(childrenIDs, v)
				case []string:
					childrenIDs = append(childrenIDs, v...)
				}
			}
		}
		// Set the children attribute for the list block to pass up to parent
		node.SetAttributeString("block_id", childrenIDs)
	}

	return ast.WalkContinue, nil
}

// renderListItem renders a list item - handles text content and children separately
func (r *TextRenderer) renderListItem(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.ListItem)

	if entering {
		// Process the first child to extract text content and remove it from children
		firstChild := n.FirstChild()
		if firstChild != nil && r.isTextNode(firstChild) {
			// Extract text elements from the first child
			textElements := r.NodeToTextElements(firstChild, source)
			// Store the text elements in the node's attributes for later use
			n.SetAttributeString("text_elements", textElements)
			// Remove the first child from the list item
			n.RemoveChild(n, firstChild)
		}
		// Continue processing remaining children
		if n.FirstChild() != nil && n.FirstChild().FirstChild() != nil && n.FirstChild().FirstChild().Kind() == extast.KindTaskCheckBox {
			todoBlock := r.createTodoBlockFromTextBlock(n.FirstChild(), source)
			n.SetAttributeString("todo", todoBlock)
			n.RemoveChild(n, n.FirstChild())
		}
		return ast.WalkContinue, nil
	}

	if !entering {
		// After processing all children, generate the list item block
		blockID := generateBlockID()

		// Handle task list items
		if storedTodoBlock, ok := n.AttributeString("todo"); ok {
			todoBlock, ok := storedTodoBlock.(*lark.DocxBlock)
			if ok {
				todoBlock.Children = GetChildrenBlockIDs(n)
				jsonData, err := json.Marshal(todoBlock)
				if err == nil {
					w.Write(jsonData)
				}
				w.WriteString(",")
				node.SetAttributeString("block_id", todoBlock.BlockID)
				return ast.WalkContinue, nil
			}
		}

		// Determine if this is an ordered or unordered list
		parentList := n.Parent().(*ast.List)
		isOrdered := parentList.IsOrdered()
		isFirst := n == parentList.FirstChild()

		// Get text elements from attributes (processed during entering phase)
		var textElements []*lark.DocxTextElement
		if storedElements, ok := n.AttributeString("text_elements"); ok {
			if elements, ok := storedElements.([]*lark.DocxTextElement); ok {
				textElements = elements
			}
		}

		// Process remaining children to collect their block IDs
		var childrenIDs = GetChildrenBlockIDs(n)

		// If no text elements were found, add an empty one
		if len(textElements) == 0 {
			textElements = append(textElements, &lark.DocxTextElement{
				TextRun: &lark.DocxTextElementTextRun{
					Content: "",
				},
			})
		}

		// Create the text block
		textBlock := &lark.DocxBlockText{
			Elements: textElements,
			Style:    &lark.DocxTextStyle{},
		}

		var block *lark.DocxBlock
		// Create block based on list type
		if isOrdered {
			// Add sequence for ordered lists
			if isFirst {
				textBlock.Style.Sequence = strconv.Itoa(parentList.Start)
			} else {
				textBlock.Style.Sequence = "auto"
			}
			block = &lark.DocxBlock{
				BlockID:   blockID,
				BlockType: lark.DocxBlockTypeOrdered,
				Ordered:   textBlock,
			}
		} else {
			block = &lark.DocxBlock{
				BlockID:   blockID,
				BlockType: lark.DocxBlockTypeBullet,
				Bullet:    textBlock,
			}
		}

		// Add children if any
		if len(childrenIDs) > 0 {
			block.Children = childrenIDs
		}

		// Render the block
		jsonData, err := json.Marshal(block)
		if err == nil {
			w.Write(jsonData)
		}
		w.WriteString(",")
		node.SetAttributeString("block_id", blockID)
	}

	return ast.WalkContinue, nil
}

// isTextNode checks if a node should be converted to text elements
func (r *TextRenderer) isTextNode(node ast.Node) bool {
	switch node.Kind() {
	case ast.KindText, ast.KindString, ast.KindEmphasis, ast.KindLink, ast.KindCodeSpan, ast.KindRawHTML:
		return true
	case extast.KindStrikethrough:
		return true
	case larkmdast.KindFontColor:
		return true
	case ast.KindTextBlock:
		// Check if text block contains only text nodes
		textBlockNode := node.(*ast.TextBlock)
		for c := textBlockNode.FirstChild(); c != nil; c = c.NextSibling() {
			if !r.isSimpleTextNode(c) {
				return false
			}
		}
		return true
	case ast.KindParagraph:
		// Check if paragraph contains only text nodes
		paragraphNode := node.(*ast.Paragraph)
		for c := paragraphNode.FirstChild(); c != nil; c = c.NextSibling() {
			if !r.isSimpleTextNode(c) {
				return false
			}
		}
		return true
	default:
		return false
	}
}

// isSimpleTextNode checks if a node is a simple text node (for paragraph content checking)
func (r *TextRenderer) isSimpleTextNode(node ast.Node) bool {
	switch node.Kind() {
	case ast.KindText, ast.KindString, ast.KindEmphasis, ast.KindLink, ast.KindCodeSpan, ast.KindRawHTML, larkmdast.KindMathInline, larkmdast.KindMathBlock:
		return true
	case extast.KindStrikethrough:
		return true
	case larkmdast.KindFontColor:
		return true
	default:
		return false
	}
}
