package larkmd

import (
	"github.com/yuin/goldmark"
	"github.com/yuin/goldmark/extension"
	"github.com/yuin/goldmark/parser"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/util"

	larkmdparser "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark/larkmd/parser"
	larkmdrenderer "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark/larkmd/renderer"
)

// LarkMarkdownExtension is a goldmark extension for Lark Markdown
type LarkMarkdownExtension struct{}

// NewLarkMarkdownExtension returns a new LarkMarkdownExtension
func NewLarkMarkdownExtension() goldmark.Extender {
	return &LarkMarkdownExtension{}
}

// Extend extends the goldmark parser and renderer with Lark Markdown extensions
func (e *LarkMarkdownExtension) Extend(m goldmark.Markdown) {
	// 添加 TaskList 扩展
	extension.TaskList.Extend(m)
	// 添加 Strikethrough 扩展
	extension.Strikethrough.Extend(m)

	// Register parsers
	m.Parser().AddOptions(
		parser.WithBlockParsers(
			util.Prioritized(larkmdparser.NewCalloutParser(), 102),
			util.Prioritized(larkmdparser.NewGridParser(), 100),
			util.Prioritized(larkmdparser.NewGridColumnParser(), 101),       // Higher priority than GridParser
			util.Prioritized(larkmdparser.NewPreviewLinkBlockParser(), 103), // Higher priority for preview link blocks
			util.Prioritized(larkmdparser.NewFilePreviewParser(), 104),      // File preview block parser
		),
		parser.WithInlineParsers(
			util.Prioritized(larkmdparser.NewMathParser(), 0),
			util.Prioritized(larkmdparser.NewFontColorParser(), 100),    // Font color parser
			util.Prioritized(larkmdparser.NewLarkEmphasisParser(), 100), // Custom emphasis parser
		),
		parser.WithParagraphTransformers(
			// Use our custom table paragraph transformer with higher priority than default
			util.Prioritized(larkmdparser.NewLarkTableParagraphTransformer(), 0),
		),
	)

	// Register renderers
	m.Renderer().AddOptions(
		renderer.WithNodeRenderers(
			util.Prioritized(larkmdrenderer.NewCalloutHTMLRenderer(), 500),
			util.Prioritized(larkmdrenderer.NewGridHTMLRenderer(), 500),
			util.Prioritized(larkmdrenderer.NewPreviewLinkHTMLRenderer(), 500),
			util.Prioritized(larkmdrenderer.NewTaskListHTMLRenderer(), 90),       // 添加任务列表渲染器
			util.Prioritized(larkmdrenderer.NewStrikethroughHTMLRenderer(), 200), // 添加删除线渲染器
			util.Prioritized(larkmdrenderer.NewBlockquoteHTMLRenderer(), 200),    // 添加引用块渲染器
			util.Prioritized(larkmdrenderer.NewMathRenderer(), 0),                // 添加数学公式渲染器
			// The following use TextRenderer
			util.Prioritized(larkmdrenderer.NewListHTMLRenderer(), 100),    // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewHeadingHTMLRenderer(), 100), // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewDividerHTMLRenderer(), 100),
			util.Prioritized(larkmdrenderer.NewEmphasisHTMLRenderer(), 100), // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewLinkHTMLRenderer(), 100),     // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewCodeSpanHTMLRenderer(), 100), // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewCodeBlockRenderer(), 150),    // Dedicated code block renderer
			util.Prioritized(larkmdrenderer.NewTextRenderer(), 100),         // The main TextRenderer
			util.Prioritized(larkmdrenderer.NewTableHTMLRenderer(), 500),
			util.Prioritized(larkmdrenderer.NewFileRenderer(), 500),
		),
	)
}

// NewLarkMarkdownParser creates a new goldmark parser with Lark Markdown extensions
func NewLarkMarkdownParser() goldmark.Markdown {
	return goldmark.New(
		goldmark.WithExtensions(
			NewLarkMarkdownExtension(),
			// Note: We use our custom table paragraph transformer instead of extension.Table
		),
	)
}

// LarkMarkdownWithoutTableExtension is a goldmark extension for Lark Markdown without table support
type LarkMarkdownWithoutTableExtension struct{}

// NewLarkMarkdownWithoutTableExtension returns a new LarkMarkdownWithoutTableExtension
func NewLarkMarkdownWithoutTableExtension() goldmark.Extender {
	return &LarkMarkdownWithoutTableExtension{}
}

// Extend extends the goldmark parser and renderer with Lark Markdown extensions but excludes table-related components
func (e *LarkMarkdownWithoutTableExtension) Extend(m goldmark.Markdown) {
	// 添加 TaskList 扩展
	extension.TaskList.Extend(m)
	// 添加 Strikethrough 扩展
	extension.Strikethrough.Extend(m)

	// Register parsers (without table paragraph transformer)
	m.Parser().AddOptions(
		parser.WithBlockParsers(
			util.Prioritized(larkmdparser.NewCalloutParser(), 102),
			util.Prioritized(larkmdparser.NewGridParser(), 100),
			util.Prioritized(larkmdparser.NewGridColumnParser(), 101),       // Higher priority than GridParser
			util.Prioritized(larkmdparser.NewPreviewLinkBlockParser(), 103), // Higher priority for preview link blocks
			util.Prioritized(larkmdparser.NewFilePreviewParser(), 104),      // File preview block parser
		),
		parser.WithInlineParsers(
			util.Prioritized(larkmdparser.NewMathParser(), 0),
			util.Prioritized(larkmdparser.NewFontColorParser(), 100),    // Font color parser
			util.Prioritized(larkmdparser.NewLarkEmphasisParser(), 100), // Custom emphasis parser
		),
		// Note: No table paragraph transformer added here
	)

	// Register renderers (without table renderer)
	m.Renderer().AddOptions(
		renderer.WithNodeRenderers(
			util.Prioritized(larkmdrenderer.NewCalloutHTMLRenderer(), 500),
			util.Prioritized(larkmdrenderer.NewGridHTMLRenderer(), 500),
			util.Prioritized(larkmdrenderer.NewPreviewLinkHTMLRenderer(), 500),
			util.Prioritized(larkmdrenderer.NewTaskListHTMLRenderer(), 90),       // 添加任务列表渲染器
			util.Prioritized(larkmdrenderer.NewStrikethroughHTMLRenderer(), 200), // 添加删除线渲染器
			util.Prioritized(larkmdrenderer.NewBlockquoteHTMLRenderer(), 200),    // 添加引用块渲染器
			util.Prioritized(larkmdrenderer.NewMathRenderer(), 0),                // 添加数学公式渲染器
			// The following use TextRenderer
			util.Prioritized(larkmdrenderer.NewListHTMLRenderer(), 100),    // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewHeadingHTMLRenderer(), 100), // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewDividerHTMLRenderer(), 100),
			util.Prioritized(larkmdrenderer.NewEmphasisHTMLRenderer(), 100), // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewLinkHTMLRenderer(), 100),     // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewCodeSpanHTMLRenderer(), 100), // Uses TextRenderer
			util.Prioritized(larkmdrenderer.NewCodeBlockRenderer(), 150),    // Dedicated code block renderer
			util.Prioritized(larkmdrenderer.NewTextRenderer(), 100),         // The main TextRenderer
			// Note: No table renderer added here
			util.Prioritized(larkmdrenderer.NewFileRenderer(), 500),
		),
	)
}

// NewTableCellMarkdownParser creates a new goldmark parser with Lark Markdown extensions but without table support
func NewTableCellMarkdownParser() goldmark.Markdown {
	return goldmark.New(
		goldmark.WithExtensions(
			NewLarkMarkdownWithoutTableExtension(),
		),
	)
}
