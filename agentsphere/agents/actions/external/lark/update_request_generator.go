package lark

import (
	"context"
	"crypto/md5"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/lark"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	"github.com/samber/lo"
	"github.com/sergi/go-diff/diffmatchpatch"
)

// ChangeType represents the type of change made to a block
type ChangeType string

const (
	ChangeTypeModify    ChangeType = "modify"
	ChangeTypeCreate    ChangeType = "create"
	ChangeTypeDelete    ChangeType = "delete"
	ChangeTypeUnchanged ChangeType = "unchanged"
)

// BlockLineMapping represents the mapping between a block and its line range in markdown
type BlockLineMapping struct {
	BlockID   string `json:"block_id"`
	StartLine int    `json:"start_line"`
	EndLine   int    `json:"end_line"`
	Content   string `json:"content"`
	Hash      string `json:"hash"`
}

// ContentIndex provides fast lookup capabilities
type ContentIndex struct {
	LineToBlock map[int]string    `json:"line_to_block"` // line number -> block ID
	HashToBlock map[string]string `json:"hash_to_block"` // content hash -> block ID
	BlockToHash map[string]string `json:"block_to_hash"` // block ID -> content hash
}

// BlockContentMapping represents the mapping between Lark blocks and markdown content
type BlockContentMapping struct {
	DocumentID        string              `json:"document_id"`
	Version           int                 `json:"version"`
	ContainerMaps     []*BlockLineMapping `json:"container_maps"`
	ContentIndex      *ContentIndex       `json:"content_index"`
	UpdateGranularity string              `json:"update_granularity"` // "first_level_children"
}

// LineChange 表示一行的变更信息
type LineChange struct {
	LineNumber int        `json:"line_number"`
	Content    string     `json:"content"`
	ChangeType ChangeType `json:"change_type"`
}

type DiffResult struct {
	OldFileChanges []LineChange `json:"old_file_changes"`
	NewFileChanges []LineChange `json:"new_file_changes"`
	LineMappings   []int        `json:"line_mappings"` // 新文档每一行对应旧文档的行号，-1表示无对应
}

// InsertGroup represents a group of consecutive blocks to insert at a specific index
type InsertGroup struct {
	Index            int      `json:"index"`               // Position index in first-level children
	ChildBlockIDList []string `json:"child_block_id_list"` // Consecutive block IDs to insert
}

// UpdateResult represents the result of a document update operation
type UpdateResult struct {
	Success         bool                               `json:"success"`
	UpdatedBlocks   int                                `json:"updated_blocks"`
	CreatedBlocks   int                                `json:"created_blocks"`
	DeletedBlocks   int                                `json:"deleted_blocks"`
	Error           string                             `json:"error,omitempty"`
	ProcessedAt     string                             `json:"processed_at"`
	DeletedBlockIDs []string                           `json:"deleted_block_ids,omitempty"`
	CreatedBlockIDs []string                           `json:"created_block_ids,omitempty"`
	DescendantsResp *lark.DocxBlockDescendantsResponse `json:"descendants_resp,omitempty"`
}

// ProcessLarkMarkdownResult represents the result of processing markdown content
type ProcessLarkMarkdownResult struct {
	LarkBlocks   []*larkdocx.Block     `json:"lark_blocks"`
	MediaTokens  []*MediaToken         `json:"media_tokens"`
	SheetMap     map[string]*SheetInfo `json:"sheet_map"`
	BoardMap     map[string]string     `json:"board_map"`
	RootBlockIDs []string              `json:"root_block_ids"`
	Content      string                `json:"content"`
	RelatedFiles []string              `json:"related_files"`
}

// MarkdownUpdater handles the document update process
type MarkdownUpdater struct {
	run    *iris.AgentRunContext
	client lark.Client
}

// NewMarkdownUpdater creates a new MarkdownUpdater instance
func NewMarkdownUpdater(run *iris.AgentRunContext, client lark.Client) *MarkdownUpdater {
	return &MarkdownUpdater{
		run:    run,
		client: client,
	}
}

// generateContentHash generates a hash for content comparison
func generateContentHash(content string) string {
	content = strings.TrimSpace(content)
	hash := md5.Sum([]byte(content))
	return fmt.Sprintf("%x", hash)
}

// createBlockMap converts a slice of blocks to a map for efficient lookup
func createBlockMap(blocks []*larkdocx.Block) map[string]*larkdocx.Block {
	blockMap := make(map[string]*larkdocx.Block, len(blocks))
	for _, block := range blocks {
		if block.BlockId != nil {
			blockMap[*block.BlockId] = block
		}
	}
	return blockMap
}

// processBlockMapping processes a single block and creates its mapping entry
func (m *MarkdownUpdater) processBlockMapping(
	ctx context.Context,
	blockID string,
	block *larkdocx.Block,
	allBlocks []*larkdocx.Block,
	parser *LarkParser,
	markdownLines []string,
	currentLine int,
	mapping *BlockContentMapping,
) int {
	if block == nil {
		return currentLine
	}

	// Generate markdown content for this individual block
	blockMarkdown := parser.ParseDocxContent(ctx, blockID, allBlocks)

	var startLine, endLine int

	if strings.TrimSpace(blockMarkdown) == "" {
		if *block.BlockType == lark.DocxBlockTypeImage ||
			*block.BlockType == lark.DocxBlockTypeText ||
			*block.BlockType == lark.DocxBlockTypeSheet {
			linkPattern := regexp.MustCompile(`\[[^\]]+\]\([^\)]+\)`)

			// 从currentLine开始搜索
			for i := currentLine - 1; i < len(markdownLines); i++ {
				if linkPattern.MatchString(markdownLines[i]) {
					startLine = i + 1 // 转换为1基索引
					endLine = i + 1
					break
				}
			}
			if startLine == 0 {
				startLine = currentLine
				endLine = currentLine
			}
		} else {
			// For empty blocks, create a minimal mapping at current position
			startLine = currentLine
			endLine = currentLine
		}
	} else {
		blockLines := strings.Split(strings.TrimRight(blockMarkdown, "\n"), "\n")

		// Find where this block's content appears in the full markdown
		startLine = m.findBlockStartLine(markdownLines, blockLines, currentLine)
		if startLine == -1 {
			// Fallback: use current line position
			startLine = currentLine
		}

		endLine = startLine + len(blockLines) - 1

		// Ensure we don't go beyond the full markdown length
		if endLine >= len(markdownLines) {
			endLine = len(markdownLines)
		} else if strings.TrimSpace(markdownLines[endLine]) == "" {
			// block default subfix with /n
			endLine++
		}
	}

	hash := generateContentHash(blockMarkdown)

	blockMapping := &BlockLineMapping{
		BlockID:   blockID,
		StartLine: startLine,
		EndLine:   endLine,
		Content:   blockMarkdown,
		Hash:      hash,
	}

	mapping.ContainerMaps = append(mapping.ContainerMaps, blockMapping)

	// Update indices
	for lineNum := startLine; lineNum <= endLine; lineNum++ {
		mapping.ContentIndex.LineToBlock[lineNum] = blockID
	}
	mapping.ContentIndex.HashToBlock[hash] = blockID
	mapping.ContentIndex.BlockToHash[blockID] = hash

	// Return next line position
	return endLine + 1
}

// CreateBlockMapping creates a block-to-content mapping from Lark document blocks
func (m *MarkdownUpdater) CreateBlockMapping(
	ctx context.Context,
	documentID string,
	allBlocks []*larkdocx.Block,
	fullMarkdown string,
	larkAccessToken string) (*BlockContentMapping, error) {
	mapping := &BlockContentMapping{
		DocumentID:    documentID,
		Version:       1,
		ContainerMaps: make([]*BlockLineMapping, 0),
		ContentIndex: &ContentIndex{
			LineToBlock: make(map[int]string),
			HashToBlock: make(map[string]string),
			BlockToHash: make(map[string]string),
		},
		UpdateGranularity: "first_level_children",
	}

	// Create block map for quick lookup
	blockMap := createBlockMap(allBlocks)

	fullLines := strings.Split(fullMarkdown, "\n")
	currentLine := 1

	// Process only first-level children (root block's direct children)
	var rootBlock *larkdocx.Block
	for _, block := range allBlocks {
		if block.ParentId != nil && *block.ParentId == "" {
			rootBlock = block
			break
		}
	}

	if rootBlock == nil {
		return nil, fmt.Errorf("no root block found in document")
	}

	parser := NewLarkParser(m.run, m.client, WithEnableImage(true), WithDisableDownload(true))
	// For each first-level child block, determine its line range in the full markdown
	for _, blockID := range rootBlock.Children {
		currentLine = m.processBlockMapping(ctx, blockID, blockMap[blockID], allBlocks, parser, fullLines, currentLine, mapping)
	}

	return mapping, nil
}

// findBlockByID finds a block by its ID in the blocks array
func (m *MarkdownUpdater) findBlockByID(blocks []*larkdocx.Block, blockID string) *larkdocx.Block {
	for _, block := range blocks {
		if block.BlockId != nil && *block.BlockId == blockID {
			return block
		}
	}
	return nil
}

// matchChildBlocks filters out blocks whose IDs are present in the childBlockIDs list
func (m *MarkdownUpdater) matchChildBlocks(allBlocks []*larkdocx.Block, childBlockIDs []string) []*larkdocx.Block {
	if len(childBlockIDs) == 0 {
		return []*larkdocx.Block{}
	}

	matchedBlocks := make([]*larkdocx.Block, 0)
	for _, childID := range childBlockIDs {
		matchedBlocks = append(matchedBlocks, m.findBlockByID(allBlocks, childID))
		block := m.findBlockByID(allBlocks, childID)
		if block != nil {
			matchedBlocks = append(matchedBlocks, m.matchChildBlocks(allBlocks, block.Children)...)
		} else {
			return matchedBlocks
		}
	}

	return matchedBlocks
}

// findBlockStartLine finds where a block's content appears in the full markdown
func (m *MarkdownUpdater) findBlockStartLine(fullLines, blockLines []string, startSearchFrom int) int {
	if len(blockLines) == 0 {
		return -1
	}

	// Search for the first line of the block content in the full markdown
	firstBlockLine := strings.TrimSpace(blockLines[0])
	if firstBlockLine == "" && len(blockLines) > 1 {
		firstBlockLine = strings.TrimSpace(blockLines[1])
	}

	if firstBlockLine == "" {
		return -1
	}

	// Start searching from the specified line
	for i := startSearchFrom - 1; i < len(fullLines); i++ {
		if strings.TrimSpace(fullLines[i]) == firstBlockLine {
			// Check if subsequent lines also match
			match := true
			for j := 1; j < len(blockLines) && i+j < len(fullLines); j++ {
				if strings.TrimSpace(blockLines[j]) != strings.TrimSpace(fullLines[i+j]) {
					match = false
					break
				}
			}
			if match {
				return i + 1 // Convert to 1-based line number
			}
		}
	}

	return -1 // Not found
}

// createBlockMappingFromBlocks creates a block mapping from processed blocks and markdown content
func (m *MarkdownUpdater) createBlockMappingFromBlocks(
	rootBlockIDs []string,
	allBlocks []*larkdocx.Block,
	markdownContent string) (*BlockContentMapping, error) {
	mapping := &BlockContentMapping{
		DocumentID:    "new_document", // placeholder for new document
		Version:       1,
		ContainerMaps: make([]*BlockLineMapping, 0),
		ContentIndex: &ContentIndex{
			LineToBlock: make(map[int]string),
			HashToBlock: make(map[string]string),
			BlockToHash: make(map[string]string),
		},
		UpdateGranularity: "first_level_children",
	}

	// Create block map for quick lookup
	blockMap := createBlockMap(allBlocks)

	markdownLines := strings.Split(markdownContent, "\n")
	currentLine := 1

	parser := NewLarkParser(m.run, m.client)

	// For each root block, process its mapping using the common logic
	for _, blockID := range rootBlockIDs {
		currentLine = m.processBlockMapping(m.run, blockID, blockMap[blockID], allBlocks, parser, markdownLines, currentLine, mapping)
	}

	return mapping, nil
}

// findBlockEndLine determines the end line of a block based on its content and next blocks
func (m *MarkdownUpdater) findBlockEndLine(blockID string, rootBlockIDs []string, blockMap map[string]*larkdocx.Block, markdownLines []string, startLine int) int {
	// Find the index of current block in rootBlockIDs
	currentIndex := -1
	for i, id := range rootBlockIDs {
		if id == blockID {
			currentIndex = i
			break
		}
	}

	// If this is the last block, end at the end of document
	if currentIndex == -1 || currentIndex == len(rootBlockIDs)-1 {
		return len(markdownLines)
	}

	// For now, estimate based on content structure
	// This is a simplified approach, should be improved with goldmark parsing info
	endLine := startLine
	for i := startLine - 1; i < len(markdownLines); i++ {
		line := strings.TrimSpace(markdownLines[i])
		// Stop at empty line or at the start of next major section
		if line == "" && i > startLine {
			break
		}
		// Stop at next heading
		if strings.HasPrefix(line, "#") && i > startLine-1 {
			break
		}
		endLine = i + 1
	}

	return endLine
}

// findBlockContentInMarkdown finds where block content appears in markdown lines
func (m *MarkdownUpdater) findBlockContentInMarkdown(markdownLines, blockLines []string, startFrom int) int {
	if len(blockLines) == 0 {
		return -1
	}

	// Search for the first line of the block content
	firstBlockLine := strings.TrimSpace(blockLines[0])
	if firstBlockLine == "" && len(blockLines) > 1 {
		firstBlockLine = strings.TrimSpace(blockLines[1])
	}

	if firstBlockLine == "" {
		return -1
	}

	// Start searching from the specified line
	for i := startFrom - 1; i < len(markdownLines); i++ {
		if strings.TrimSpace(markdownLines[i]) == firstBlockLine {
			// Check if subsequent lines also match
			match := true
			for j := 1; j < len(blockLines) && i+j < len(markdownLines); j++ {
				if strings.TrimSpace(blockLines[j]) != strings.TrimSpace(markdownLines[i+j]) {
					match = false
					break
				}
			}
			if match {
				return i + 1 // Convert to 1-based line number
			}
		}
	}

	return -1 // Not found
}

// CompareMarkdown compares original and modified markdown and returns differences
func (m *MarkdownUpdater) CompareMarkdown(oldContent, newContent string) *DiffResult {
	oldLines := strings.Split(oldContent, "\n")
	newLines := strings.Split(newContent, "\n")

	dmp := diffmatchpatch.New()

	oldText := strings.Join(oldLines, "\n")
	newText := strings.Join(newLines, "\n")

	// 计算差异
	diffs := dmp.DiffMain(oldText, newText, false)

	// 转换为行级别的差异
	chars1, chars2, lineArray := dmp.DiffLinesToChars(oldText, newText)
	diffs = dmp.DiffMain(chars1, chars2, false)
	diffs = dmp.DiffCharsToLines(diffs, lineArray)

	// 解析差异结果
	result := &DiffResult{
		OldFileChanges: make([]LineChange, len(oldLines)),
		NewFileChanges: make([]LineChange, len(newLines)),
		LineMappings:   make([]int, len(newLines)), // 初始化为新文档的长度
	}

	// 初始化 LineMappings 为 -1（表示无映射）
	for i := range result.LineMappings {
		result.LineMappings[i] = -1
	}

	// 初始化所有行为未变更
	for i, line := range oldLines {
		result.OldFileChanges[i] = LineChange{
			LineNumber: i + 1,
			Content:    line,
			ChangeType: ChangeTypeUnchanged,
		}
	}

	for i, line := range newLines {
		result.NewFileChanges[i] = LineChange{
			LineNumber: i + 1,
			Content:    line,
			ChangeType: ChangeTypeUnchanged,
		}
	}

	// 使用更精确的行级对比
	oldLineIdx := 0
	newLineIdx := 0

	for _, diff := range diffs {
		lines := strings.Split(strings.TrimSuffix(diff.Text, "\n"), "\n")

		switch diff.Type {
		case diffmatchpatch.DiffEqual:
			// 相同的行，创建映射关系
			for i := 0; i < len(lines); i++ {
				if newLineIdx+i < len(newLines) && oldLineIdx+i < len(oldLines) {
					// 新文档的第 newLineIdx+i 行对应旧文档的第 oldLineIdx+i 行
					result.LineMappings[newLineIdx+i] = oldLineIdx + i
				}
			}
			oldLineIdx += len(lines)
			newLineIdx += len(lines)

		case diffmatchpatch.DiffDelete:
			// 标记旧文件中被删除的行
			for i := 0; i < len(lines) && oldLineIdx+i < len(oldLines); i++ {
				if lines[i] != "" {
					result.OldFileChanges[oldLineIdx+i].ChangeType = ChangeTypeDelete
				}
			}
			oldLineIdx += len(lines)

		case diffmatchpatch.DiffInsert:
			// 标记新文件中新增的行，这些行在旧文档中没有对应，保持 -1
			for i := 0; i < len(lines) && newLineIdx+i < len(newLines); i++ {
				if lines[i] != "" {
					result.NewFileChanges[newLineIdx+i].ChangeType = ChangeTypeCreate
				}
			}
			newLineIdx += len(lines)
		}
	}

	// 检测修改的行（删除后立即添加的情况）
	detectModifiedLines(result, oldLines, newLines)

	return result
}

// detectModifiedLines 检测修改的行（而不是简单的删除和添加）
func detectModifiedLines(result *DiffResult, oldLines, newLines []string) {
	// 查找连续的删除和添加，这通常表示修改
	for i := 0; i < len(result.OldFileChanges); i++ {
		if result.OldFileChanges[i].ChangeType == ChangeTypeDelete {
			// 查找对应的新增行
			for j := 0; j < len(result.NewFileChanges); j++ {
				if result.NewFileChanges[j].ChangeType == ChangeTypeCreate {
					// 如果内容相似，标记为修改
					if isSimilar(oldLines[i], newLines[j]) {
						result.OldFileChanges[i].ChangeType = ChangeTypeModify
						result.NewFileChanges[j].ChangeType = ChangeTypeModify

						// 更新映射关系：新文档的第 j 行对应旧文档的第 i 行
						result.LineMappings[j] = i
						break
					}
				}
			}
		}
	}
}

// isSimilar 判断两行是否相似（简单的相似度判断）
func isSimilar(line1, line2 string) bool {
	// 如果两行都是空的，认为相似
	if line1 == "" && line2 == "" {
		return true
	}

	// 如果一行为空，另一行不为空，认为不相似
	if (line1 == "") != (line2 == "") {
		return false
	}

	// 使用 Levenshtein 距离判断相似度
	dmp := diffmatchpatch.New()
	diffs := dmp.DiffMain(line1, line2, false)

	// 计算相似度
	var commonLen, totalLen int
	for _, diff := range diffs {
		if diff.Type == diffmatchpatch.DiffEqual {
			commonLen += len(diff.Text)
		}
		totalLen += len(diff.Text)
	}

	if totalLen == 0 {
		return false
	}

	similarity := float64(commonLen) / float64(totalLen)
	return similarity > 0.5 // 如果相似度超过50%，认为是修改而不是删除+新增
}

// GenerateUpdateOperations generates the delete and insert operations based on diff
func (m *MarkdownUpdater) GenerateUpdateOperations(
	originalMapping *BlockContentMapping,
	markdownResult *ProcessLarkMarkdownResult,
	diff *DiffResult,
	originalBlocks map[string]*larkdocx.Block,
) (deleteList []string, insertGroups []*InsertGroup, err error) {

	// 1. Create block mapping for new document before generating rootBlockIDs
	newMapping, err := m.createBlockMappingFromBlocks(markdownResult.RootBlockIDs, markdownResult.LarkBlocks, markdownResult.Content)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create new document block mapping: %v", err)
	}

	// 2. Track which blocks are affected by changes based on old changes
	affectedBlocks := make(map[string]bool)
	for _, change := range diff.GetOldChanges() {
		// Find which block this line belongs to in the original document
		if blockID, exists := originalMapping.ContentIndex.LineToBlock[change.LineNumber]; exists {
			affectedBlocks[blockID] = true
		}
	}

	// Special handling: mark empty DocxBlockTypeText blocks for deletion
	lo.ForEach(originalMapping.ContainerMaps, func(blockMapping *BlockLineMapping, _ int) {
		if originalBlock, exists := originalBlocks[blockMapping.BlockID]; exists &&
			originalBlock.BlockType != nil &&
			*originalBlock.BlockType == lark.DocxBlockTypeText &&
			strings.TrimSpace(blockMapping.Content) == "" {
			affectedBlocks[blockMapping.BlockID] = true
		}
	})

	// 3. Check NewChanges for missing delete items
	for _, change := range diff.GetNewChanges() {
		if change.ChangeType == ChangeTypeCreate || change.ChangeType == ChangeTypeModify {
			// Find corresponding original block that might need deletion
			originalBlockID := m.findCorrespondingOriginalBlock(change, originalMapping, diff)
			if originalBlockID != "" {
				affectedBlocks[originalBlockID] = true
			}
		}
	}

	// 4. Generate final delete and insert lists
	deleteList = make([]string, 0)
	for blockID := range affectedBlocks {
		deleteList = append(deleteList, blockID)
	}

	// 5. Generate insert groups based on new document mapping and changes
	insertGroups = m.generateInsertGroupsFromNewMapping(newMapping, diff, markdownResult.LarkBlocks)

	return deleteList, insertGroups, nil
}

// findCorrespondingOriginalBlock finds the original block that corresponds to a new change
func (m *MarkdownUpdater) findCorrespondingOriginalBlock(change LineChange, originalMapping *BlockContentMapping, diff *DiffResult) string {
	// Use line mapping to find corresponding original line
	if change.LineNumber > 0 && change.LineNumber <= len(diff.LineMappings) {
		originalLineNum := diff.LineMappings[change.LineNumber-1] // Convert to 0-based index
		if originalLineNum >= 0 {
			// Convert back to 1-based and find block
			if blockID, exists := originalMapping.ContentIndex.LineToBlock[originalLineNum+1]; exists {
				return blockID
			}
		}
	}

	return ""
}

// generateInsertGroupsFromNewMapping generates optimized insert groups based on new document mapping and changes
func (m *MarkdownUpdater) generateInsertGroupsFromNewMapping(newMapping *BlockContentMapping, diff *DiffResult, allBlocks []*larkdocx.Block) []*InsertGroup {
	// Create block map for quick lookup
	blockMap := createBlockMap(allBlocks)

	// Find blocks that contain changes and should be inserted
	affectedNewBlocks := make(map[string]bool)

	for _, change := range diff.GetNewChanges() {
		if change.ChangeType == ChangeTypeCreate || change.ChangeType == ChangeTypeModify {
			// Find which new block this line belongs to
			if blockID, exists := newMapping.ContentIndex.LineToBlock[change.LineNumber]; exists {
				affectedNewBlocks[blockID] = true
			}
		}
	}

	// Collect affected indices and their block IDs in order
	type IndexedBlock struct {
		Index   int
		BlockID string
	}

	var affectedIndices []IndexedBlock

	for i, blockMapping := range newMapping.ContainerMaps {
		if affectedNewBlocks[blockMapping.BlockID] {
			// Verify block exists in blockMap
			if _, exists := blockMap[blockMapping.BlockID]; exists {
				affectedIndices = append(affectedIndices, IndexedBlock{
					Index:   i, // Use position in ContainerMaps as index
					BlockID: blockMapping.BlockID,
				})
			}
		}
	}

	// Group consecutive indices
	if len(affectedIndices) == 0 {
		return []*InsertGroup{}
	}

	var insertGroups []*InsertGroup
	currentGroup := &InsertGroup{
		Index:            affectedIndices[0].Index,
		ChildBlockIDList: []string{affectedIndices[0].BlockID},
	}

	for i := 1; i < len(affectedIndices); i++ {
		if affectedIndices[i].Index == affectedIndices[i-1].Index+1 {
			// Consecutive index, add to current group
			currentGroup.ChildBlockIDList = append(currentGroup.ChildBlockIDList, affectedIndices[i].BlockID)
		} else {
			// Non-consecutive, start new group
			insertGroups = append(insertGroups, currentGroup)
			currentGroup = &InsertGroup{
				Index:            affectedIndices[i].Index,
				ChildBlockIDList: []string{affectedIndices[i].BlockID},
			}
		}
	}

	// Add the last group
	insertGroups = append(insertGroups, currentGroup)

	return insertGroups
}

// executeDocumentBlockUpdate performs the actual document block update operations
func (m *MarkdownUpdater) executeDocumentBlockUpdate(
	ctx context.Context,
	documentID string,
	deleteList []string,
	insertGroups []*InsertGroup,
	larkAccessToken string,
	markdownResult *ProcessLarkMarkdownResult,
) *UpdateResult {
	result := &UpdateResult{
		Success:         true,
		ProcessedAt:     time.Now().Format(time.RFC3339),
		DeletedBlockIDs: make([]string, 0),
		CreatedBlockIDs: make([]string, 0),
	}

	logger := m.run.GetLogger()

	// Phase 1: Delete affected blocks with optimized batch operations
	if len(deleteList) > 0 {
		// Get block indices for all blocks to be deleted
		blockIndices, err := m.getBlockIndices(ctx, documentID, deleteList, larkAccessToken)
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Failed to get block indices: %v", err)
			return result
		}

		// Merge consecutive indices to reduce API calls
		deleteRanges := m.mergeConsecutiveIndices(blockIndices)

		// Delete ranges in reverse order to avoid index shifting
		for i := len(deleteRanges) - 1; i >= 0; i-- {
			deleteRange := deleteRanges[i]
			err := m.deleteBatchBlocks(ctx, documentID, deleteRange.startIndex, deleteRange.endIndex, larkAccessToken)
			time.Sleep(1 * time.Second)
			if err != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Failed to delete blocks [%d-%d]: %v", deleteRange.startIndex, deleteRange.endIndex, err)
				return result
			}

			// Count deleted blocks
			deletedCount := deleteRange.endIndex - deleteRange.startIndex + 1
			result.DeletedBlocks += deletedCount

			// Add block IDs to result (based on original indices)
			for j := deleteRange.startIndex; j <= deleteRange.endIndex; j++ {
				if j < len(deleteList) {
					result.DeletedBlockIDs = append(result.DeletedBlockIDs, deleteList[j])
				}
			}
		}
	}

	// Create a merged result to collect all insertion results
	mergedResult := &lark.DocxBlockDescendantsResponse{
		BlockIDRelations: []struct {
			BlockID          string `json:"block_id"`
			TemporaryBlockID string `json:"temporary_block_id"`
		}{},
		Children:           []*larkdocx.Block{},
		DocumentRevisionID: 0,
		ClientToken:        "",
	}
	result.DescendantsResp = mergedResult
	logger.Infof("Beginning block insertion process, total insert groups: %d", len(insertGroups))

	// Phase 2: Insert new blocks using optimized groups
	for _, insertGroup := range insertGroups {
		// Filter out blocks that are already children to avoid duplication
		filteredBlocks := m.matchChildBlocks(markdownResult.LarkBlocks, insertGroup.ChildBlockIDList)
		resp, blockIDs, err := m.insertBlocksAtIndex(ctx, documentID, filteredBlocks, insertGroup.Index, insertGroup.ChildBlockIDList, larkAccessToken)
		time.Sleep(1 * time.Second)
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Failed to insert blocks at index %d, child block ids: %v, err: %v", insertGroup.Index, insertGroup.ChildBlockIDList, err)
			return result
		}

		// Merge the result with our merged result
		mergedResult.BlockIDRelations = append(mergedResult.BlockIDRelations, resp.BlockIDRelations...)
		mergedResult.Children = append(mergedResult.Children, resp.Children...)
		mergedResult.DocumentRevisionID = resp.DocumentRevisionID // Use the latest revision ID
		if resp.ClientToken != "" {
			mergedResult.ClientToken = resp.ClientToken
		}

		result.CreatedBlocks += len(blockIDs)
		result.CreatedBlockIDs = append(result.CreatedBlockIDs, blockIDs...)
	}

	return result
}

// ExecuteDocumentUpdate performs the actual document update operations
func (m *MarkdownUpdater) ExecuteDocumentUpdate(
	ctx context.Context,
	documentID string,
	deleteList []string,
	insertGroups []*InsertGroup,
	larkAccessToken string,
	markdownResult *ProcessLarkMarkdownResult,
	markdownFilePath string,
) *UpdateResult {
	if markdownResult == nil {
		return &UpdateResult{Success: false, Error: "markdownResult is nil"}
	}
	result := m.executeDocumentBlockUpdate(ctx, documentID, deleteList, insertGroups, larkAccessToken, markdownResult)
	logger := m.run.GetLogger()
	mergedResult := result.DescendantsResp

	// If result false, refresh document
	if !result.Success {
		logger.Infof("Document update failed, refresh document")
		// Delete all exist blocks
		blocks, err := m.client.GetLarkDocxBlocks(ctx, documentID, larkAccessToken)
		if err != nil {
			logger.Errorf("Failed to get blocks for document %s: %v", documentID, err)
			return result
		}
		err = m.deleteBatchBlocks(ctx, documentID, 0, len(blocks), larkAccessToken)
		if err != nil {
			logger.Errorf("Failed to delete blocks for document %s: %v", documentID, err)
			return result
		}

		// Insert blocks as descendants of the document
		insertBlockDescendantsResponse, err := insertBlocksInChunks(
			m.run,
			documentID,
			markdownResult.RootBlockIDs,
			markdownResult.LarkBlocks,
		)
		if err != nil {
			logger.Errorf("%v\nfailed to insert block descendants: %v", result.Error, err)
			return result
		} else {
			result.Success = true
			mergedResult = insertBlockDescendantsResponse
		}
	}

	// Process resources (media, sheets, boards) if update succeeded
	if result.Success && len(result.CreatedBlockIDs) > 0 {
		// Process media files (images and files)
		if len(markdownResult.MediaTokens) > 0 {
			_, err := processMediaTokens(m.run, markdownResult.MediaTokens, mergedResult, documentID, markdownFilePath)
			if err != nil {
				// Record warning but don't fail the entire update
				logger.Warnf("Failed to process media tokens during update: %v", err)
			}
		}

		// Process sheet tokens
		if len(markdownResult.SheetMap) > 0 {
			err := processSheetTokens(m.run, markdownResult.SheetMap, mergedResult, documentID, markdownFilePath, "")
			if err != nil {
				logger.Warnf("Failed to process sheet tokens during update: %v", err)
			}
		}

		// Process board tokens
		if len(markdownResult.BoardMap) > 0 {
			err := processBoardTokens(m.run, markdownResult.BoardMap, mergedResult, markdownFilePath, false)
			if err != nil {
				logger.Warnf("Failed to process board tokens during update: %v", err)
			}
		}
	}

	return result
}

// DeleteRange represents a range of consecutive block indices to delete
type DeleteRange struct {
	startIndex int
	endIndex   int
}

// getBlockIndices gets the indices of blocks in the document for deletion
func (m *MarkdownUpdater) getBlockIndices(ctx context.Context, documentID string, blockIDs []string, larkAccessToken string) ([]int, error) {
	// Get all blocks to find indices
	blocks, err := m.client.GetLarkDocxBlocks(ctx, documentID, larkAccessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to get document blocks: %v", err)
	}

	// Find root-level blocks (direct children of document)
	rootBlocks := make([]*larkdocx.Block, 0)
	for _, block := range blocks {
		if block.ParentId != nil && *block.ParentId == documentID {
			rootBlocks = append(rootBlocks, block)
		}
	}

	// Create mapping from block ID to index
	blockToIndex := make(map[string]int)
	for i, block := range rootBlocks {
		if block.BlockId != nil {
			blockToIndex[*block.BlockId] = i
		}
	}

	// Get indices for blocks to be deleted
	indices := make([]int, 0, len(blockIDs))
	for _, blockID := range blockIDs {
		if index, exists := blockToIndex[blockID]; exists {
			indices = append(indices, index)
		}
	}

	return indices, nil
}

// mergeConsecutiveIndices merges consecutive indices into ranges to reduce API calls
func (m *MarkdownUpdater) mergeConsecutiveIndices(indices []int) []DeleteRange {
	if len(indices) == 0 {
		return []DeleteRange{}
	}

	// Sort indices
	sort.Ints(indices)

	ranges := make([]DeleteRange, 0)
	start := indices[0]
	end := indices[0]

	for i := 1; i < len(indices); i++ {
		if indices[i] == end+1 {
			// Consecutive index, extend current range
			end = indices[i]
		} else {
			// Non-consecutive, create new range
			ranges = append(ranges, DeleteRange{startIndex: start, endIndex: end + 1})
			start = indices[i]
			end = indices[i]
		}
	}

	// Add the last range
	ranges = append(ranges, DeleteRange{startIndex: start, endIndex: end + 1})
	return ranges
}

// deleteBatchBlocks deletes a batch of consecutive blocks
func (m *MarkdownUpdater) deleteBatchBlocks(ctx context.Context, documentID string, startIndex, endIndex int, larkAccessToken string) error {
	// Use BatchDeleteDocxBlockChildren to delete the consecutive blocks
	// documentID is both the document and the parent block (root level)
	// token must be user token?
	_, err := m.client.BatchDeleteDocxBlockChildren(ctx, documentID, documentID, startIndex, endIndex, larkAccessToken)
	return err
}

// insertBlocksAtIndex inserts multiple blocks at a specific index position
func (m *MarkdownUpdater) insertBlocksAtIndex(ctx context.Context, documentID string, blocksToInsert []*larkdocx.Block, index int, childIDs []string, larkAccessToken string) (*lark.DocxBlockDescendantsResponse, []string, error) {
	// Use InsertDocxBlockDescendants to insert the blocks at specified index
	resp, err := m.client.InsertDocxBlockDescendants(
		ctx,
		documentID,
		documentID,
		index,
		childIDs,
		blocksToInsert,
		larkAccessToken,
	)

	if err != nil {
		return resp, nil, err
	}

	// Extract created block IDs from response
	var blockIDs []string
	if resp != nil && len(resp.Children) > 0 {
		for _, child := range resp.Children {
			if child.BlockId != nil {
				blockIDs = append(blockIDs, *child.BlockId)
			}
		}
	}

	return resp, blockIDs, nil
}

func (d *DiffResult) GetNewChanges() []LineChange {
	return lo.Filter(d.NewFileChanges, func(item LineChange, index int) bool {
		return item.ChangeType != ChangeTypeUnchanged
	})
}

func (d *DiffResult) GetOldChanges() []LineChange {
	return lo.Filter(d.OldFileChanges, func(item LineChange, index int) bool {
		return item.ChangeType != ChangeTypeUnchanged
	})
}
