package lark

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	planactentity "code.byted.org/devgpt/kiwis/agentsphere/agents/planact/entity"
	"code.byted.org/devgpt/kiwis/port/lark"
)

// BlockClosingError represents an error when callout or grid blocks are missing closing marks
type BlockClosingError struct {
	Message string
}

func (e *BlockClosingError) Error() string {
	return e.Message
}

// PreviewExtensionError represents an error when preview syntax uses forbidden file extensions
type PreviewExtensionError struct {
	Message string
}

func (e *PreviewExtensionError) Error() string {
	return e.Message
}

// ContentIndentationError represents an error when content after "content|" lacks proper indentation
type ContentIndentationError struct {
	Message string
}

func (e *ContentIndentationError) Error() string {
	return e.Message
}

// EnsureNewlineBeforeCalloutAndGrid 确保在```callout和```grid代码块前有换行符
func EnsureNewlineBeforeCalloutAndGrid(content string) string {
	patterns := []string{
		"```callout",
		"```grid",
		"![preview]",
	}

	result := content
	for _, pattern := range patterns {
		// 按行分割来精确处理每一行
		lines := strings.Split(result, "\n")

		for i, line := range lines {
			// 检查当前行是否包含pattern
			if idx := strings.Index(line, pattern); idx != -1 {
				// 检查pattern前面的内容
				beforePattern := line[:idx]

				// 如果pattern前面只有空格，说明已经在行首，不需要处理
				if strings.TrimSpace(beforePattern) == "" {
					continue
				}

				// 如果pattern前面有<br>标签，也视为有换行符，不需要处理
				if strings.Contains(beforePattern, "<br>") {
					continue
				}

				// pattern前面有非空格内容，需要添加换行
				// 分离出pattern前面的空格
				spaceStart := idx
				for spaceStart > 0 && (line[spaceStart-1] == ' ' || line[spaceStart-1] == '\t') {
					spaceStart--
				}

				beforeSpaces := line[:spaceStart]
				spaces := line[spaceStart:idx]
				afterPattern := line[idx+len(pattern):]

				// 重建：beforeSpaces保留在当前行，spaces+pattern+afterPattern作为新行
				lines[i] = beforeSpaces
				newLine := spaces + pattern + afterPattern

				// 插入新行
				if i+1 < len(lines) {
					lines = append(lines[:i+1], append([]string{newLine}, lines[i+1:]...)...)
				} else {
					lines = append(lines, newLine)
				}
			}
		}

		result = strings.Join(lines, "\n")
	}

	return result
}

// EnsureNewlineAfterImageLinks 确保图片或文件链接后有两个换行符
// 匹配形如 ![描述](路径) 的图片链接，如果后面只有一个换行符，则添加一个额外的换行符
func EnsureNewlineAfterImageLinks(content string) string {
	re := regexp.MustCompile(`^!\[([^\]]+)\]\(([^)]+)\)\s*$`)

	// 按行分割内容
	lines := strings.Split(content, "\n")
	result := make([]string, 0, len(lines))

	for i, line := range lines {
		result = append(result, line)

		// 检查当前行是否匹配图片链接模式
		if re.MatchString(line) {
			// 检查是否是最后一行或者下一行不是空行
			if i == len(lines)-1 || lines[i+1] != "" {
				// 添加一个空行
				result = append(result, "")
			}
		}
	}

	return strings.Join(result, "\n")
}

// ProcessMarkdownHTMLImages 处理Markdown文件中的HTML图片引用
// 查找形如 ![星巴克中国2024财年季度营收柱状图](./output/quarterly_revenue_chart.hmtl) 的图片引用
// 部署HTML文件所在目录，并替换原始引用为部署后的URL
func ProcessMarkdownHTMLImages(c *iris.AgentRunContext, markdownFilePath string) (string, error) {
	// 读取Markdown文件内容
	content, err := os.ReadFile(markdownFilePath)
	if err != nil {
		return "", errors.Wrap(err, "读取Markdown文件失败")
	}

	// 验证表格格式
	if err := ValidateMarkdownTableRows(string(content)); err != nil {
		return "", errors.Wrap(err, "Markdown表格格式验证失败")
	}

	// 一些格式兜底
	processedContent := EnsureNewlineBeforeCalloutAndGrid(string(content))
	// 确保图片链接后有足够的换行
	processedContent = EnsureNewlineAfterImageLinks(processedContent)
	// 替换@User
	processedContent = ProcessMarkdownEmailToOpenID(c, processedContent)

	// 正则表达式匹配图片引用和预览链接
	// 匹配形如 ![图片描述](路径.html) 或 [preview](路径.html) 的模式，其中.html可能有不同的大小写
	re := regexp.MustCompile(`(?:!\[(.*?)\]|\[preview\])\((.*?\.(?i)html?)\)`)
	matches := re.FindAllStringSubmatch(processedContent, -1)

	if len(matches) == 0 {
		return processedContent, nil // 没有找到HTML图片引用，返回处理过换行标签的内容
	}

	htmlPathDirMap := make(map[string]string)
	htmlDirs := make(map[string]bool)

	for _, match := range matches {
		if len(match) < 3 {
			continue
		}

		htmlPath := match[2]

		// 检查是否为远程URL（以http://或https://开头）
		if strings.HasPrefix(htmlPath, "http://") || strings.HasPrefix(htmlPath, "https://") {
			continue
		}

		baseDir := filepath.Dir(markdownFilePath)
		// 如果是相对路径，转换为绝对路径
		if !filepath.IsAbs(htmlPath) {
			htmlPath = filepath.Join(baseDir, htmlPath)
		}

		// 检查原始路径是否存在
		if _, err := os.Stat(htmlPath); os.IsNotExist(err) {
			// 如果原始路径不存在，尝试在目录中查找文件
			htmlFileName := filepath.Base(htmlPath)
			var foundPath string

			// 在baseDir及其子目录中查找文件
			err := filepath.Walk(baseDir, func(path string, info os.FileInfo, err error) error {
				if err != nil {
					return err
				}
				if !info.IsDir() && info.Name() == htmlFileName {
					foundPath = path
					return filepath.SkipAll // 找到文件后停止搜索
				}
				return nil
			})

			if err == nil && foundPath != "" {
				htmlPath = foundPath
			}
		}

		// 获取HTML文件所在目录
		htmlDir := filepath.Dir(htmlPath)
		htmlDirs[htmlDir] = true
		htmlPathDirMap[match[2]] = htmlDir
	}

	// 部署每个包含HTML文件的目录
	deployedURLs := make(map[string]string)
	for dir := range htmlDirs {
		// 检查目录是否存在
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			c.GetLogger().Warnf("目录不存在: %s", dir)
			continue
		}

		// 部署目录
		deployOutput, err := workspace.DeployDirectory(c, workspace.DeployArgs{
			Directory: dir,
		}, workspace.DeployOption{
			ProcessHTML: true,
		})

		if err != nil {
			c.GetLogger().Errorf("部署目录失败 %s: %v", dir, err)
			continue
		}

		// 记录部署URL
		deployedURLs[dir] = deployOutput.URL
	}

	// 替换Markdown内容中的HTML引用
	updatedContent := processedContent
	for _, match := range matches {
		if len(match) < 3 {
			continue
		}

		htmlPath := match[2]

		// 处理远程URL的情况
		if strings.HasPrefix(htmlPath, "http://") || strings.HasPrefix(htmlPath, "https://") {
			// 直接将 ![描述](URL) 改为 [preview](URL)，不进行部署
			originalRef := fmt.Sprintf("![%s](%s)", match[1], htmlPath)
			newRef := fmt.Sprintf("[preview](%s)", htmlPath)
			updatedContent = strings.Replace(updatedContent, originalRef, newRef, -1)
			continue
		}

		htmlFileName := filepath.Base(htmlPath)
		htmlDirAbs := htmlPathDirMap[htmlPath]

		// 检查该目录是否已成功部署
		deployedURL, ok := deployedURLs[htmlDirAbs]
		if !ok {
			continue
		}

		// 构建新的URL，直接拼接HTML文件名
		newURL := strings.TrimRight(deployedURL, "/") + "/" + htmlFileName

		// 替换原始引用
		originalRef := fmt.Sprintf("![%s](%s)", match[1], match[2])
		// 检查是否为预览链接格式
		if match[1] == "" {
			originalRef = fmt.Sprintf("[preview](%s)", match[2])
		}
		newRef := fmt.Sprintf("[preview](%s)", newURL)
		updatedContent = strings.Replace(updatedContent, originalRef, newRef, -1)
	}

	return updatedContent, nil
}

// UpdateMarkdownWithDeployedHTML 处理Markdown文件中的HTML引用并更新文件
func UpdateMarkdownWithDeployedHTML(c *iris.AgentRunContext, markdownFilePath string) error {
	updatedContent, err := ProcessMarkdownHTMLImages(c, markdownFilePath)
	if err != nil {
		return err
	}

	// 写回文件
	return os.WriteFile(markdownFilePath, []byte(updatedContent), 0644)
}

// ProcessMarkdownCitations 处理Markdown文件中的引用格式 ::cite[1] 转换为 [1](url)
func ProcessMarkdownCitations(c *iris.AgentRunContext, markdownFilePath string) (err error) {

	// 读取Markdown文件内容
	content, err := os.ReadFile(markdownFilePath)
	if err != nil {
		return errors.Wrap(err, "读取Markdown文件失败")
	}

	err = ValidateMarkdownContent(string(content))
	if err != nil {
		return err
	}

	// 获取引用信息
	store := iris.RetrieveStoreByKey[planactentity.ReferenceStore](c, planactentity.ReferenceStoreKey)
	refs := make(map[string]string)
	for i, r := range store.SearchedRef.List {
		key := fmt.Sprintf("[%v]", i+1)
		refs[key] = r.URI
	}
	c.GetLogger().Debugf("process markdown citations, get refs: %v", refs)

	contentCitationLimited := limitCitationNum(string(content))

	// 正则表达式匹配引用格式 ::cite[1]
	re := regexp.MustCompile(`::cite\[(\d+)\]`)
	updatedContent := re.ReplaceAllStringFunc(contentCitationLimited, func(match string) string {
		// 提取引用编号
		numRe := regexp.MustCompile(`\[(\d+)\]`)
		numMatch := numRe.FindStringSubmatch(match)
		if len(numMatch) < 2 {
			return match // 如果没有匹配到编号，保持原样
		}

		// 构建新的引用格式
		refKey := fmt.Sprintf("[%s]", numMatch[1])
		if url, ok := refs[refKey]; ok {
			// 使用双方括号格式，确保在 Markdown 中正确显示
			return fmt.Sprintf("[[%s]](%s)", numMatch[1], url)
			// 或者使用转义方式
			// return fmt.Sprintf("\\[%s\\](%s)", numMatch[1], url)
		}
		return "" // 如果没有找到对应的URL，删除该引用
	})

	// 写回文件
	return os.WriteFile(markdownFilePath, []byte(updatedContent), 0644)
}

func limitCitationNum(content string) string {
	// 正则表达式匹配连续的引用格式 ::cite[1]
	re := regexp.MustCompile(`(::cite\[\d+\])+`)
	updatedContent := re.ReplaceAllStringFunc(content, func(match string) string {
		// 提取引用编号
		numRe := regexp.MustCompile(`::cite\[(\d+)\]`)
		matches := numRe.FindAllStringSubmatch(match, -1) // 这里改用 match 而不是 content
		// 提取所有数字
		numbers := make([]int, 0, len(matches))
		for _, numMatch := range matches {
			if len(numMatch) > 1 {
				num, err := strconv.Atoi(numMatch[1])
				if err != nil {
					return match // 如果转换失败，返回原始匹配
				}
				numbers = append(numbers, num)
			}
		}
		// 去重、排序并限制数量
		numbers = lo.Uniq(numbers)
		sort.Ints(numbers)
		numbers = lo.Slice(numbers, 0, 3) // 只取前三个数字
		// 构建新的引用字符串
		var builder strings.Builder
		for _, num := range numbers {
			builder.WriteString("::cite[")
			builder.WriteString(strconv.Itoa(num))
			builder.WriteString("]")
		}
		return builder.String()
	})
	return updatedContent
}

// ProcessMarkdownEmailToOpenID 处理Markdown文件中的@(email)格式文本，转换为@openid
func ProcessMarkdownEmailToOpenID(c *iris.AgentRunContext, content string) string {
	// 正则表达式匹配@(email)格式，例如：@(<EMAIL>)
	re := regexp.MustCompile(`@\(([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\)`)
	matches := re.FindAllStringSubmatch(content, -1)

	if len(matches) == 0 {
		return content // 没有找到@(email)格式，返回原始内容
	}

	// 提取所有匹配的email地址
	emails := make([]string, 0, len(matches))
	for _, match := range matches {
		if len(match) > 1 {
			emails = append(emails, match[1])
		}
	}

	// 去重
	emails = lo.Uniq(emails)

	// 调用MGetUserIDByEmail获取openid
	emailToOpenID, err := larkClient(c).MGetUserIDByEmail(context.Background(), emails, lark.UserIDTypeOpenID, false)
	if err != nil {
		c.GetLogger().Warnf("failed to get user openid by email: %v", err)
		return content
	}

	// 替换@(email)为@openid
	updatedContent := content
	for email, openID := range emailToOpenID {
		if openID != "" {
			// 替换@(email)为@openid
			oldText := fmt.Sprintf("@(%s)", email)
			newText := fmt.Sprintf("`%s`", openID)
			updatedContent = strings.ReplaceAll(updatedContent, oldText, newText)
		}
	}

	return updatedContent
}

// ValidateMarkdownTableRows 检测未完成的表格行
// 如果一行以｜开头但不以｜结尾，并且上一行是表格行，则视为未完成的表格行，抛出错误
func ValidateMarkdownTableRows(content string) error {
	lines := strings.Split(content, "\n")

	for i, line := range lines {
		// 跳过空行
		if strings.TrimSpace(line) == "" {
			continue
		}

		// 检查当前行是否以｜开头但不以｜结尾
		trimmedLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimmedLine, "|") && !strings.HasSuffix(trimmedLine, "|") {
			// 检查上一行是否是表格行
			if i > 0 {
				prevLine := strings.TrimSpace(lines[i-1])
				// 判断上一行是否是表格行（以｜开头且以｜结尾，或者是表格分隔行）
				if isTableRow(prevLine) {
					return fmt.Errorf("发现未完成的表格行（第%d行）: %s\n错误说明：表格中所有单元格内的换行都必须使用<br>标签，禁止使用任何实际换行符",
						i+1, line)
				}
			}
		}
	}

	return nil
}

// isTableRow 判断是否是表格行
func isTableRow(line string) bool {
	if line == "" {
		return false
	}

	// 标准表格行：以｜开头且以｜结尾
	if strings.HasPrefix(line, "|") && strings.HasSuffix(line, "|") {
		return true
	}

	// 表格分隔行：形如 |---|---|---| 或 | --- | --- | --- |
	if strings.HasPrefix(line, "|") && strings.Contains(line, "-") {
		// 简单检查是否包含表格分隔符模式
		parts := strings.Split(line, "|")
		for _, part := range parts {
			trimmed := strings.TrimSpace(part)
			if trimmed != "" && strings.Contains(trimmed, "-") {
				return true
			}
		}
	}

	return false
}

// ValidateMarkdownContent 验证markdown内容是否符合规范
func ValidateMarkdownContent(content string) error {
	lines := strings.Split(content, "\n")

	// 检查规则1: 高亮块(callout)和分栏(grid)缺少结束标记
	if err := checkBlockClosing(lines); err != nil {
		return err
	}

	// 检查规则2: preview语法中的文件后缀限制
	if err := checkPreviewExtensions(lines); err != nil {
		return err
	}

	// 检查规则3: content|后的内容缩进检查
	if err := checkContentIndentation(lines); err != nil {
		return err
	}

	return nil
}

// checkBlockClosing 检查callout和grid块是否有正确的结束标记
func checkBlockClosing(lines []string) error {
	openBlocks := make([]struct {
		blockType string
		lineNum   int
	}, 0)

	for i, line := range lines {
		trimmed := strings.TrimSpace(line)

		// 检查开始标记
		if strings.HasPrefix(trimmed, "```callout") {
			openBlocks = append(openBlocks, struct {
				blockType string
				lineNum   int
			}{"callout", i + 1})
		} else if strings.HasPrefix(trimmed, "```grid") {
			openBlocks = append(openBlocks, struct {
				blockType string
				lineNum   int
			}{"grid", i + 1})
		} else if trimmed == "```" && len(openBlocks) > 0 {
			// 找到结束标记，移除最后一个开放的块
			openBlocks = openBlocks[:len(openBlocks)-1]
		}
	}

	// 检查是否有未关闭的块
	if len(openBlocks) > 0 {
		for _, block := range openBlocks {
			message := fmt.Sprintf("第%d行的%s块缺少结束标记```", block.lineNum, block.blockType)
			return &BlockClosingError{
				Message: message,
			}
		}
	}

	return nil
}

// checkPreviewExtensions 检查preview语法中是否使用了禁止的文件后缀
func checkPreviewExtensions(lines []string) error {
	forbiddenExts := []string{".md", ".plantuml", ".puml", ".drawio"}
	previewRegex := regexp.MustCompile(`\[preview\]\(([^)]+)\)`)

	for i, line := range lines {
		matches := previewRegex.FindAllStringSubmatch(line, -1)
		for _, match := range matches {
			if len(match) > 1 {
				url := match[1]
				for _, ext := range forbiddenExts {
					if strings.HasSuffix(strings.ToLower(url), ext) {
						message := fmt.Sprintf("第%d行的preview语法中不允许使用%s文件后缀: %s，把文件里面的内容整合到lark.md文档中", i+1, ext, url)
						return &PreviewExtensionError{
							Message: message,
						}
					}
				}
			}
		}
	}

	return nil
}

// checkContentIndentation 检查content|后的内容是否有正确的缩进
// calculateIndentLength 计算行的缩进长度，制表符按4个空格计算
func calculateIndentLength(line string) int {
	indent := 0
	for _, char := range line {
		if char == ' ' {
			indent++
		} else if char == '\t' {
			indent += 4
		} else {
			break
		}
	}
	return indent
}

func checkContentIndentation(lines []string) error {
	for i, line := range lines {
		if strings.Contains(line, "content:|") {
			// 获取content:|行的缩进长度（制表符按4个空格计算）
			contentIndent := calculateIndentLength(line)
			requiredIndent := contentIndent + 2
			
			// 从content:|的下一行开始检查
			for j := i + 1; j < len(lines); j++ {
				currentLine := lines[j]
				
				// 如果遇到- width_ratio:，结束检查
				if strings.Contains(currentLine, "- width_ratio:") {
					break
				}
				
				// 如果遇到```且其缩进小于等于content:|的缩进，结束检查
				if strings.Contains(currentLine, "```") {
					currentIndent := calculateIndentLength(currentLine)
					// 检查```行除了缩进外是否还有其他字符
					trimmed := strings.TrimSpace(currentLine)
					if currentIndent <= contentIndent && trimmed == "```" {
						break
					}
					// 如果```行不满足结束条件，继续检查其缩进
				}
				
				// 跳过空行
				if strings.TrimSpace(currentLine) == "" {
					continue
				}
				
				// 检查当前行的缩进是否符合要求
				currentIndent := calculateIndentLength(currentLine)
				if currentIndent < requiredIndent {
					message := fmt.Sprintf("第%d行content:|后的内容（第%d行）缺少缩进，需要在content:|缩进基础上增加2个空格", i+1, j+1)
					return &ContentIndentationError{
						Message: message,
					}
				}
			}
		}
	}

	return nil
}
