package knowledge_graph

import (
	"context"
	"encoding/json"
	"fmt"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	agentsphereentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/port/knowledge_graph"
	"code.byted.org/gopkg/logid"
)

const (
	KnowledgeGraphSearchBam            = "knowledge_graph_search_service_info"
	KnowledgeGraphSearchBamDescription = `knowledge_graph_search_service_info perform full-text search in service knowledge. Supports two typical scenarios:
1. Reverse lookup of service information by "purpose/function". Service information includes code repository information corresponding to the service: e.g., "Meta information related to services responsible for grayscale release"
2. View the list of interfaces exposed by a service: e.g., "Interface capabilities related to creating development tasks"
3. View the downstream dependency list of a specific method of a service
Only a natural language question is required
Return content may include:
- Service name psm: The name of the service, consisting of three segments, e.g.: a.b.c
- Description tce_service_summary_info: A summary of the service function, which may be empty
- Meta information tce_service_meta_info: The service's own repository information, such as SCM repository and Git repository, where SCM controls the version of the binary, and the Git repository is the code repository
- Included methods service_bam: Interfaces exposed by the service, which may be empty or inaccurate
- Call chain information topology: Downstream interface call chain of the service
If the question is of a summary nature, the return may not be accurate enough. Please describe the service information to be obtained as accurately as possible
If the question hits multiple services, the return may also include ratings for these services`

	KnowledgeGraphSearchRepo            = "knowledge_graph_search_repo"
	KnowledgeGraphSearchRepoDescription = `Knowledge_graph_search_repo use one sentence to query git repos. Describe through a complete sentence of natural language, as specific as possible, and include terms of specific business functions as much as possible.Return one or more code repositories that match the current description.
Return content:
<list>
functionName: The currently matched function name
gitRepoUrl: The recalled repository URL
Description: Description of the function
Reason: The reason for recalling the current function
editor Info: List of editors in the past three months, empty if no one edited in the past three months, in the format {editor email prefix: number of edits}
Return example:
functionName: function:cloud-fe/rhino/src/page/PIPE/record/components/CronTaskList.tsx:anonymous_578b48352d7be1aa1167ca812c184e34
gitRepoUrl: https://code.byted.org/cloud-fe/rhino
Description: When clicked, set the edit data to type new with task ID 0, and set the visibility status of the new task to true
Reason: This function implements the creation of scheduled tasks. When clicked, it can set the status of new tasks, which is highly relevant to the \"development task creation\" requirement and is a direct entry point for the task creation function editor Info:
{\"laihongjie\":2}
functionName: function:devinfra/hagrid/app/rd-process/dev/pkg/dal/es/client.go:NewDevelopmentTaskClient
gitRepoUrl: https://code.byted.org/devinfra/hagrid
Description: Create and return a client instance of type DevelopmentTask
Reason: This function creates a DevelopmentTask client instance, which is a core data access layer component for the development task creation function, highly relevant to user needs, and can be used as an important entry point for task creation
editor Info:`
)

type KnowledgeGraphToolArgs struct {
	SpaceID    string   `json:"-" mapstructure:"space_id" description:"required, id of the space, it can be obtained from AgentRunContext.Parameters[\"space_information\"].SpaceID.Please note the distinction between session_id and space_id; space_id must be used strictly here."`
	Query      string   `json:"query" mapstructure:"query" description:"required, a sentence used to retrieve code repositories, service metadata, etc."`
	Developers []string `json:"developers" mapstructure:"developers" description:"required, Developers of services, requirements, etc.If the user has specified a developer, use the developer designated by the user; if the user has not specified one, use User as the developer for the current task."`
}

func (kgta KnowledgeGraphToolArgs) ToString() string {
	rslt, err := json.Marshal(kgta)
	if err != nil {
		return ""
	}

	return string(rslt)
}

type KnowledgeGraphToolResp struct {
	Result string `json:"result" mapstructure:"result"`
}

func KnowledgeGraphSearchBamTool(c *iris.AgentRunContext, args KnowledgeGraphToolArgs) (KnowledgeGraphToolResp, error) {
	spaceID, ok := c.Parameters[agentsphereentity.RuntimeParametersSpaceID].(string)
	c.GetLogger().Infof("[AgentRun]-[knowledge_graph_debug]-[KnowledgeGraphSearchBamTool]: get space_id from AgentRunContext, space_id:%+v", spaceID)
	if ok && spaceID != "" && spaceID != args.SpaceID {
		args.SpaceID = spaceID
	}

	client, err := knowledge_graph.NewClient()
	if err != nil {
		return KnowledgeGraphToolResp{}, fmt.Errorf("new knowledge_graph http client error: %w", err)
	}
	logId := logid.GenLogID()
	ctx := context.WithValue(c, "x-tt-logid", logId)
	c.GetLogger().Infof("[AgentRun]-[knowledge_graph_debug]-[KnowledgeGraphSearchBamTool],search_bam with args:%+v, logid:%v", args, logId)

	rslt, err := client.SearchServiceInfo(ctx, args.SpaceID, args.Query, "TCEService")
	//rslt, err := knowledge_graph.SearchBam(c, args.SpaceID, args.Query, "TCEService")
	if err != nil {
		return KnowledgeGraphToolResp{}, fmt.Errorf("failed to get knowledge from bam: %w", err)
	}

	return KnowledgeGraphToolResp{Result: rslt}, nil
}

func KnowledgeGraphSearchRepoTool(c *iris.AgentRunContext, args KnowledgeGraphToolArgs) (KnowledgeGraphToolResp, error) {
	spaceID, ok := c.Parameters[agentsphereentity.RuntimeParametersSpaceID].(string)
	c.GetLogger().Infof("[AgentRun]-[knowledge_graph_debug]-[KnowledgeGraphSearchRepoTool]: get space_id from AgentRunContext, space_id:%+v", spaceID)
	if ok && spaceID != "" && spaceID != args.SpaceID {
		args.SpaceID = spaceID
	}

	if len(args.Developers) == 0 {
		user := c.User.Username
		args.Developers = []string{user}
	}

	client, err := knowledge_graph.NewClient()
	if err != nil {
		return KnowledgeGraphToolResp{}, fmt.Errorf("new knowledge_graph http client error: %w", err)
	}
	logId := logid.GenLogID()
	ctx := context.WithValue(c, "x-tt-logid", logId)
	c.GetLogger().Infof("[AgentRun]-[knowledge_graph_debug]-[KnowledgeGraphSearchRepoTool],search_repo with args:%+v,logid:%+v", args, logId)

	rslt, err := client.SearchRepo(ctx, args.SpaceID, args.Query, "Code", args.Developers)
	if err != nil {
		return KnowledgeGraphToolResp{}, fmt.Errorf("failed to get knowledge from repo: %w", err)
	}

	return KnowledgeGraphToolResp{Result: rslt}, nil
}

func NewKnowledgeGraphSearchBamTool() iris.Action {
	return actions.ToTool(KnowledgeGraphSearchBam, KnowledgeGraphSearchBamDescription, KnowledgeGraphSearchBamTool)
}

func NewKnowledgeGraphSearchRepoTool() iris.Action {
	return actions.ToTool(KnowledgeGraphSearchRepo, KnowledgeGraphSearchRepoDescription, KnowledgeGraphSearchRepoTool)
}
