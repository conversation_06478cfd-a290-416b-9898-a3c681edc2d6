package devmind

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/devmind"
	"code.byted.org/lang/gg/gptr"
)

//var c = &iris.AgentRunContext{
//	Environ: &iris.RunEnviron{
//		Map: map[string]string{
//			entity.RuntimeEnvironUserCloudJWT: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//		},
//	},
//}
//
//func TestInsightReportTask(t *testing.T) {
//	type args struct {
//		run  *iris.AgentRunContext
//		args QueryInsightReportTaskArgs
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    map[string]any
//		wantErr bool
//	}{
//		{
//			name: "test",
//			args: args{
//				run: c,
//				args: QueryInsightReportTaskArgs{
//					ExecuteId: "7536057342375249964",
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := InsightReportTask(tt.args.run, tt.args.args)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("QueryInsightReportTask() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			t.Logf("QueryInsightReportTaskArgs() got = %v", got)
//		})
//	}
//}

func TestInsightReportTaskNew(t *testing.T) {
	executionID := "7536057342375249964"
	ctx := context.Background()
	jwtToken := "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	api, err := devmind.NewTestReportClient()
	_, err = api.QueryInsightReportTask(ctx, jwtToken, "", executionID)
	if err != nil {
		fmt.Printf("QueryInsightReportTask failed: %v", err)
	}
}

func TestWriteTaskResult(t *testing.T) {
	taskInstanceId := "7536057342375315500"
	result := "### 指标关联性分析：合入有效代码行 vs. 合入MR数\n\n**核心结论:**\n“合入有效代码行”与“合入MR数”两个指标通常呈正相关关系。更多的合并请求（MR）往往伴随着更多的代码提交，从而导致有效代码行数的增加。这种关系反映了研发活动的投入量和产出量。\n\n---\n\n**详细分析:**\n\n通过对这两个指标的关联性分析，我们可以识别出不同的研发模式，并洞察其背后的潜在原因：\n\n1.  **强正相关（高MR数 & 高代码行数）:**\n    *   **表现:** 两个指标同步增长。\n    *   **解读:** 这是最健康的模式，表明团队保持着持续、活跃的开发节奏，并且提交的MR具有实质性的代码产出。\n\n2.  **高MR数 & 低代码行数:**\n    *   **表现:** MR数量很多，但总代码行数增长缓慢。\n    *   **解读:**\n        *   **正面可能:** 团队遵循“小步快跑、频繁集成”的最佳实践，每次提交的变更范围很小，有助于降低集成风险、提升代码评审效率。\n        *   **负面可能:** 大量的提交可能源于非功能性变更，如配置文件修改、文档更新或少量代码格式化，实际的核心功能产出有限。\n\n3.  **低MR数 & 高代码行数:**\n    *   **表现:** MR数量不多，但单个或少数几个MR包含大量的代码行变更。\n    *   **解读:**\n        *   **正面可能:** 可能涉及大型新功能模块的初次提交或大规模的代码重构。\n        *   **负面可能:** 这种模式通常会带来较高的风险。大的变更集难以进行有效的代码评审，一旦出现问题，定位和回滚的难度也更大。这可能与持续集成的原则相悖。\n\n4.  **弱相关或无相关（低MR数 & 低代码行数）:**\n    *   **表现:** 两个指标都处于较低水平。\n    *   **解读:** 可能表示项目当前处于非编码阶段（如需求分析、架构设计、集中测试等），或者团队的研发资源投入较少。\n\n**总结与建议:**\n\n在解读这两个指标时，建议结合具体的业务周期和团队的研发实践。单一的关联性分析不足以全面评估研发效能，但它可以作为一个有力的切入点，帮助管理者识别潜在的流程问题或风险，并引导团队向更健康的研发模式演进。"
	ctx := context.Background()
	jwtToken := "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	api, err := devmind.NewTestReportClient()
	_, err = api.WriteTaskResult(ctx, jwtToken, "", taskInstanceId, result)
	if err != nil {
		fmt.Printf("WriteTaskResult failed: %v", err)
	}
}

func TestNextStepInstruction(t *testing.T) {
	executionID := "7536057342375249964"
	ctx := context.Background()
	jwtToken := "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	api, err := devmind.NewTestReportClient()
	_, err = api.NextStepInstruction(ctx, jwtToken, "", executionID)
	if err != nil {
		fmt.Printf("NextStepInstruction failed: %v", err)
	}
}

func TestWriteReportMetricsDataToExcel(t *testing.T) {
	// 假设创建一个 AgentRunContext 实例
	run := &iris.AgentRunContext{}
	executeID := "1234"
	// 构造 dataList
	dataList := []devmind.AimeMetricQueryInfo{
		{
			MetricName: "分析[合入有效代码行]",
			MetricId:   "7536057342375282732",
			MetricQueryDataList: []devmind.AimeMetricQueryData{
				{
					GraphType: "card_dimension_graph",
					ChartSheet: &devmind.AimeSheetData{
						Titles: []devmind.AimeSheetTitle{
							{
								Title:    "雇员类型（当前周期）",
								IsMetric: false,
							},
							{
								Title:    "合入有效代码行",
								IsMetric: true,
							},
						},
						DataList: [][]devmind.AimeSheetSubData{
							{
								{
									Value: "正式",
								},
								{
									Value:           "4893",
									PeriodDiffValue: gptr.Of(-16435.0),
									PeriodDiffRatio: gptr.Of(-0.7706),
									ProportionRatio: gptr.Of(1.0),
								},
							},
						},
					},
				},
				{
					GraphType: "card_dimension_graph",
					ChartSheet: &devmind.AimeSheetData{
						Titles: []devmind.AimeSheetTitle{
							{
								Title:    "人员地域（国家）",
								IsMetric: false,
							},
							{
								Title:    "合入有效代码行",
								IsMetric: true,
							},
						},
						DataList: [][]devmind.AimeSheetSubData{
							{
								{
									Value: "中国大陆",
								},
								{
									Value:           "4893",
									PeriodDiffValue: gptr.Of(-16435.0),
									PeriodDiffRatio: gptr.Of(-0.7706),
									ProportionRatio: gptr.Of(1.0),
								},
							},
						},
					},
				},
			},
		},
	}
	// 调用 writeReportMetricsDataToExcel 函数
	filePath, err := writeReportMetricsDataToExcel(run, dataList, executeID)
	if err != nil {
		fmt.Printf("写入 Excel 文件时出错: %v\n", err)
	} else {
		fmt.Printf("Excel 文件已保存至: %s\n", filePath)
	}
}
