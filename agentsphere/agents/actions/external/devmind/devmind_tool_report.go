package devmind

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	rtentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/port/devmind"
	"code.byted.org/gopkg/facility/ternary"
	"code.byted.org/gopkg/lang/conv"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
)

// Tool constants
const (
	// Insight report task tools
	ToolInsightReportTask            = "insight_report_data_task"
	ToolInsightReportTaskDescription = "Query task info for a specific insight report, including user's customized prompt and data info about how to analyze this task"

	// Task next step instruction tools
	ToolNextStepInstruction            = "next_step_instruction"
	ToolNextStepInstructionDescription = "Query next step instruction for task"

	// Task write result tools
	ToolWriteTaskResult            = "write_task_result"
	ToolWriteTaskResultDescription = "Write result to task instance"
)

// QueryInsightReportTaskArgs defines the arguments for querying insight report task
type QueryInsightReportTaskArgs struct {
	ExecuteId string `json:"execute_id" mapstructure:"execute_id" description:"The ID of task to execute"`
}

// WriteTaskResultArgs defines the arguments for writing task result
type WriteTaskResultArgs struct {
	TaskInstanceId string `json:"task_instance_id" mapstructure:"task_instance_id" description:"The ID of task to write"`
	Result         string `json:"result" mapstructure:"result" description:"The result to write"`
}

// NewInsightReportTaskTool create a new tool for querying insight report task data
func NewInsightReportTaskTool() iris.Action {
	return actions.ToTool(ToolInsightReportTask, ToolInsightReportTaskDescription, InsightReportTask)
}

// NewWriteTaskResultTool create a new tool for writing a task result
func NewWriteTaskResultTool() iris.Action {
	return actions.ToTool(ToolWriteTaskResult, ToolWriteTaskResultDescription, WriteTaskResult)
}

// NewNextStepInstructionTool create a new tool for querying next step instruction for task
func NewNextStepInstructionTool() iris.Action {
	return actions.ToTool(ToolNextStepInstruction, ToolNextStepInstructionDescription, NextStepInstruction)
}

// InsightReportTask query data for specific insight report task info
func InsightReportTask(run *iris.AgentRunContext, args QueryInsightReportTaskArgs) (map[string]any, error) {
	ctx := context.Background()

	if args.ExecuteId == "" {
		return nil, iris.NewRecoverable(errors.New("execute_id cannot be empty"))
	}

	//api, err := devmind.NewTestReportClient()
	api, err := devmind.New()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}

	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	sessionId := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	result, err := api.QueryInsightReportTask(ctx, jwtToken, sessionId, args.ExecuteId)
	if err != nil {
		//run.GetLogger().Errorf("query insight report task data failed, err: %v", err)
		return nil, err
	}

	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		//run.GetLogger().Errorf("failed to create devmind directory, err: %v", err)
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}

	// Generate a filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("insight_report_task_%s_%s.json", args.ExecuteId, timestamp)
	jsonFilePath := filepath.Join(logsDir, filename)

	// Open file for writing
	file, err := os.Create(jsonFilePath)
	if err != nil {
		//run.GetLogger().Errorf("failed to create file, err: %v", err)
		return nil, errors.Wrap(err, "failed to create file")
	}
	defer file.Close()

	// 使用json.Encoder避免Unicode转义
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	encoder.SetEscapeHTML(false)
	// 根据图表类型，将数据写入不同的sheet
	filePath, err := writeReportMetricsDataToExcel(run, result.ReportMetricQueryInfo, args.ExecuteId)
	if err != nil {
		//run.GetLogger().Errorf("failed to write insight report metric stories data to excel, err: %v", err)
		return nil, err
	}

	return map[string]any{
		"task_instance_id":                   result.TaskInstanceId,
		"user_prompt":                        result.UserPrompt,
		"task_metric_stories_data_reference": filePath,
		"message": fmt.Sprintf("Successfully retrieved and saved insight report task data for execute id %s. "+
			"The task_instance_id is the id of the task instance and define where the analyze result is stored to. "+
			"The user_prompt is the user's customized prompt about how to analyze this task. "+
			"The file path of task_metric_stories_data_reference is metric stories data detail of this task. "+
			"The first sheet is a collection of information about all metric stroies of this task. "+
			"The other sheets name of task_metric_stories_data_reference is the id and chart type of each metric story data. "+
			"The other sheets detail of task_metric_stories_data_reference is data detail of each metric story data.", args.ExecuteId),
	}, nil
}

// NextStepInstruction query next step instruction for task
func NextStepInstruction(run *iris.AgentRunContext, args QueryInsightReportTaskArgs) (map[string]any, error) {
	ctx := context.Background()
	if args.ExecuteId == "" {
		return nil, iris.NewRecoverable(errors.New("execute_id cannot be empty"))
	}
	//api, err := devmind.NewTestReportClient()
	api, err := devmind.New()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	sessionId := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	result, err := api.NextStepInstruction(ctx, jwtToken, sessionId, args.ExecuteId)
	if err != nil {
		//run.GetLogger().Errorf("query insight report task data failed, err: %v", err)
		return nil, err
	}
	return map[string]any{
		"success":                      true,
		"next_step_instruction_prompt": result.NextStepPrompt,
	}, nil
}

// WriteTaskResult write task result
func WriteTaskResult(run *iris.AgentRunContext, args WriteTaskResultArgs) (map[string]any, error) {
	ctx := context.Background()
	if args.TaskInstanceId == "" {
		return nil, iris.NewRecoverable(errors.New("task_instance_id cannot be empty"))
	}
	if args.Result == "" {
		return nil, iris.NewRecoverable(errors.New("result cannot be empty"))
	}
	//api, err := devmind.NewTestReportClient()
	api, err := devmind.New()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	sessionId := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	_, err = api.WriteTaskResult(ctx, jwtToken, sessionId, args.TaskInstanceId, args.Result)
	if err != nil {
		//run.GetLogger().Errorf("query insight report task data failed, err: %v", err)
		return nil, err
	}
	return map[string]any{
		"success": true,
		"message": fmt.Sprintf("Successfully write task result for task instance id %s. ", args.TaskInstanceId),
	}, nil
}

func writeReportMetricsDataToExcel(run *iris.AgentRunContext, dataList []devmind.AimeMetricQueryInfo, executeID string) (string, error) {
	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err := os.MkdirAll(logsDir, 0750)
	if err != nil {
		//run.GetLogger().Errorf("failed to create devmind directory, err: %v", err)
		return "", errors.Wrap(err, "failed to create devmind directory")
	}
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("report_metric_stories_data_%s_%s.xlsx", executeID, timestamp)
	filePath := filepath.Join(logsDir, filename)

	excelFile := excelize.NewFile()
	sheetName := "TaskMetricStoriesInfo"
	excelFile.SetSheetName("Sheet1", sheetName)
	// 指标故事信息
	metricHeader := []string{"Metric Story Name", "Metric Story ID", "Metric Story Description", "Metric Story DataUnit", "Metric Story Caliber", "Metric Story Analysis Direction"}
	for colIdx, key := range metricHeader {
		cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
		excelFile.SetCellValue(sheetName, cell, key)
	}
	for i, data := range dataList {
		rowIdx := i + 2
		excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIdx), data.MetricName)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIdx), data.MetricId)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIdx), data.MetricDescription)
		dataUnitJsonValue, _ := json.Marshal(data.MetricDataUnit)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIdx), dataUnitJsonValue)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("E%d", rowIdx), data.MetricCaliber)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("F%d", rowIdx), ternary.TernaryString(data.MetricAnalysisDirection == "Negative", "负向指标，越低越好", "正向指标，越高越好"))
	}

	for _, data := range dataList {
		// 处理表头
		dimensionIndex := 0 // 如果是指标卡AimeCardDimensionGraph类型，加个序列号，防止sheetName重复
		for _, queryData := range data.MetricQueryDataList {
			if queryData.GraphType == "" {
				run.GetLogger().Errorf("devmind api query data GraphType is null, metricName: %s, metricId: %s", data.MetricName, data.MetricId)
				continue
			}
			sheetName = fmt.Sprintf("%v %v", data.MetricId, queryData.GraphType)
			// 如果是指标卡AimeCardDimensionGraph类型，sheetName从card_dimension_graph改为dim_i
			if queryData.GraphType == AimeCardDimensionGraph {
				sheetName = fmt.Sprintf("%v %v_%v", data.MetricId, "dim", strconv.Itoa(dimensionIndex))
				dimensionIndex++
			}
			excelFile.NewSheet(sheetName)
			switch queryData.GraphType {
			case AimeCardGraph:
				cardGraph := queryData.CardGraph
				if cardGraph == nil {
					run.GetLogger().Errorf("devmind api query data cardGraph is nil")
					continue
				}
				// 定义表头
				header := []string{"Metric Story Name", "Date", "Value", "Baseline Value", "Baseline Conclusion", "History Conclusion"}
				colStartIdx := 1
				for colIdx, key := range header {
					cell, _ := excelize.CoordinatesToCellName(colIdx+colStartIdx, 1)
					excelFile.SetCellValue(sheetName, cell, key)
				}

				rowIdx := 2
				for _, series := range cardGraph.DataList {
					for xIdx := len(cardGraph.XAxis) - 1; xIdx >= 0; xIdx-- {
						xVal := cardGraph.XAxis[xIdx]
						// 写入 Series Name
						excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIdx), series.Name)
						// 写入 XAxis 值
						excelFile.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIdx), xVal)
						// 写入 Data 值
						if xIdx < len(series.SubDataList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIdx), series.SubDataList[xIdx])
						}
						// 写入 Baseline Value List 值
						if xIdx < len(series.BaselineValueList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIdx), series.BaselineValueList[xIdx])
						}
						if xIdx == len(cardGraph.XAxis)-1 {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("E%d", rowIdx), cardGraph.BaselineConclusion)
							excelFile.SetCellValue(sheetName, fmt.Sprintf("F%d", rowIdx), cardGraph.HistoryConclusion)
						}
						// 行号递增
						rowIdx++
					}
					// 将 Avg 和 Sum 写到第一列最下面
					//avgRow := rowIdx + 1
					//sumRow := rowIdx + 2
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", avgRow), "Avg")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", avgRow), series.Avg)
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", sumRow), "Sum")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", sumRow), series.Sum)
				}
			case AimeCardDimensionGraph:
				cardDimensionGraph := queryData.ChartSheet
				if cardDimensionGraph == nil {
					run.GetLogger().Errorf("devmind api query data cardDimensionGraph is nil")
					continue
				}
				// 写入表头（固定为titles） 补充子表头（在原表头下方）
				var index int
				subHeaders := []string{"", "", "环比值", "环比率", "占比", "同比值", "同比率"}
				for i, title := range cardDimensionGraph.Titles {
					// 非指标不添加子表头
					if !title.IsMetric {
						cell, _ := excelize.CoordinatesToCellName(i+1, 1)
						excelFile.SetCellValue(sheetName, cell, title.Title)
						index++
						continue
					}
					// 计算子表头位置，每个指标对应的子表头间隔 len(subHeaders) - 1 列
					baseCol := ((i - index) * (len(subHeaders) - 1)) + index + 1
					cell, _ := excelize.CoordinatesToCellName(baseCol, 1)
					excelFile.SetCellValue(sheetName, cell, title.Title)
					for j, subHeader := range subHeaders[2:] {
						cell, _ = excelize.CoordinatesToCellName(baseCol+j+1, 1)
						excelFile.SetCellValue(sheetName, cell, fmt.Sprintf("%s_%s", title.Title, subHeader))
					}
				}
				// 写入数据行
				rowIdx := 2
				for _, rowData := range cardDimensionGraph.DataList {
					for colIdx, item := range rowData {
						// 计算主值的列位置，每个指标对应的主值间隔 len(subHeaders) - 1 列
						var mainCol int
						if cardDimensionGraph.Titles[colIdx].IsMetric && colIdx >= index {
							mainCol = ((colIdx - index) * (len(subHeaders) - 1)) + index + 1
						} else {
							mainCol = colIdx + 1
						}
						cell, _ := excelize.CoordinatesToCellName(mainCol, rowIdx)
						// 写入Value
						excelFile.SetCellValue(sheetName, cell, item.Value)

						// 为非第一列并且有占比值那种添加子字段（PeriodDiffValue等）
						if cardDimensionGraph.Titles[colIdx].IsMetric {
							// 计算子字段的单元格位置（右侧相邻列）
							pdvCol := mainCol + 1
							pdrCol := pdvCol + 1
							prCol := pdrCol + 1
							ydCol := prCol + 1
							yrCol := ydCol + 1

							// 写入PeriodDiffValue（环比-差异值）
							if item.PeriodDiffValue != nil {
								pdvCell, _ := excelize.CoordinatesToCellName(pdvCol, rowIdx)
								excelFile.SetCellValue(sheetName, pdvCell, conv.Float32Default(item.PeriodDiffValue, 0))
							}
							// 写入PeriodDiffRatio（环比-差异率，格式化为百分比）
							if item.PeriodDiffRatio != nil {
								pdrCell, _ := excelize.CoordinatesToCellName(pdrCol, rowIdx)
								excelFile.SetCellValue(sheetName, pdrCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.PeriodDiffRatio, 0)*100))
							}

							// 写入ProportionRatio（占比，格式化为百分比）
							if item.ProportionRatio != nil {
								prCell, _ := excelize.CoordinatesToCellName(prCol, rowIdx)
								excelFile.SetCellValue(sheetName, prCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.ProportionRatio, 0)*100))
							}

							// 写入YearDiffValue（同比-差异值）
							if item.YearDiffValue != nil {
								ydCell, _ := excelize.CoordinatesToCellName(ydCol, rowIdx)
								excelFile.SetCellValue(sheetName, ydCell, conv.Float32Default(item.YearDiffValue, 0))
							}
							// 写入YearDiffRatio（同比-差异率，格式化为百分比）
							if item.YearDiffRatio != nil {
								yrCell, _ := excelize.CoordinatesToCellName(yrCol, rowIdx)
								excelFile.SetCellValue(sheetName, yrCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.YearDiffRatio, 0)*100))
							}
						}
					}
					rowIdx++
				}
			case AimeLineGraph:
				// 折线图数据处理逻辑
				lineGraph := queryData.LineGraph
				if lineGraph == nil {
					run.GetLogger().Errorf("devmind api query data lineGraph is nil")
					continue
				}
				// 定义表头
				analysisDimensionsTitle := ternary.TernaryString(lineGraph.AnalysisDimensions == nil,
					"Analysis Dimension", conv.StringDefault(lineGraph.AnalysisDimensions, ""))
				header := []string{analysisDimensionsTitle, "Date", "Value", "Baseline Value"}
				colStartIdx := 1
				for colIdx, key := range header {
					cell, _ := excelize.CoordinatesToCellName(colIdx+colStartIdx, 1)
					excelFile.SetCellValue(sheetName, cell, key)
				}
				// 写入数据
				rowIdx := 2
				for _, series := range lineGraph.DataList {
					for xIdx := len(lineGraph.XAxis) - 1; xIdx >= 0; xIdx-- {
						xVal := lineGraph.XAxis[xIdx]
						// 写入 Series Name
						excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIdx), series.Name)
						// 写入 XAxis 值
						excelFile.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIdx), xVal)
						// 写入 Data 值
						if xIdx < len(series.SubDataList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIdx), series.SubDataList[xIdx])
						}
						// 写入 Baseline Value List 值
						if xIdx < len(series.BaselineValueList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIdx), series.BaselineValueList[xIdx])
						}
						// 行号递增
						rowIdx++
					}
					// 将 Avg 和 Sum 写到第一列最下面
					//avgRow := rowIdx + 1
					//sumRow := rowIdx + 2
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", avgRow), "Avg")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", avgRow), series.Avg)
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", sumRow), "Sum")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", sumRow), series.Sum)
				}
			case AimeChartSheet:
				// 图表表数据处理逻辑
				chartSheet := queryData.ChartSheet
				if chartSheet == nil {
					run.GetLogger().Errorf("devmind api query data chartSheet is nil")
					continue
				}
				// 写入表头（固定为titles） 补充子表头（在原表头下方）
				var index int
				subHeaders := []string{"", "", "环比值", "环比率", "占比", "同比值", "同比率"}
				for i, title := range chartSheet.Titles {
					// 非指标不添加子表头
					if !title.IsMetric {
						cell, _ := excelize.CoordinatesToCellName(i+1, 1)
						excelFile.SetCellValue(sheetName, cell, title.Title)
						index++
						continue
					}
					// 计算子表头位置，每个指标对应的子表头间隔 len(subHeaders) - 1 列
					baseCol := ((i - index) * (len(subHeaders) - 1)) + index + 1
					cell, _ := excelize.CoordinatesToCellName(baseCol, 1)
					excelFile.SetCellValue(sheetName, cell, title.Title)
					for j, subHeader := range subHeaders[2:] {
						cell, _ = excelize.CoordinatesToCellName(baseCol+j+1, 1)
						excelFile.SetCellValue(sheetName, cell, fmt.Sprintf("%s_%s", title.Title, subHeader))
					}
				}
				// 写入数据行
				rowIdx := 2
				for _, rowData := range chartSheet.DataList {
					for colIdx, item := range rowData {
						// 计算主值的列位置，每个指标对应的主值间隔 len(subHeaders) - 1 列
						var mainCol int
						if chartSheet.Titles[colIdx].IsMetric && colIdx >= index {
							mainCol = ((colIdx - index) * (len(subHeaders) - 1)) + index + 1
						} else {
							mainCol = colIdx + 1
						}
						cell, _ := excelize.CoordinatesToCellName(mainCol, rowIdx)
						// 写入Value
						excelFile.SetCellValue(sheetName, cell, item.Value)

						// 为非第一列并且有占比值那种添加子字段（PeriodDiffValue等）
						if chartSheet.Titles[colIdx].IsMetric {
							// 计算子字段的单元格位置（右侧相邻列）
							pdvCol := mainCol + 1
							pdrCol := pdvCol + 1
							prCol := pdrCol + 1
							ydCol := prCol + 1
							yrCol := ydCol + 1

							// 写入PeriodDiffValue
							if item.PeriodDiffValue != nil {
								pdvCell, _ := excelize.CoordinatesToCellName(pdvCol, rowIdx)
								excelFile.SetCellValue(sheetName, pdvCell, conv.Float32Default(item.PeriodDiffValue, 0))
							}
							// 写入PeriodDiffRatio（格式化为百分比）
							if item.PeriodDiffRatio != nil {
								pdrCell, _ := excelize.CoordinatesToCellName(pdrCol, rowIdx)
								excelFile.SetCellValue(sheetName, pdrCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.PeriodDiffRatio, 0)*100))
							}

							// 写入ProportionRatio（格式化为百分比）
							if item.ProportionRatio != nil {
								prCell, _ := excelize.CoordinatesToCellName(prCol, rowIdx)
								excelFile.SetCellValue(sheetName, prCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.ProportionRatio, 0)*100))
							}

							// 写入YearDiffValue
							if item.YearDiffValue != nil {
								ydCell, _ := excelize.CoordinatesToCellName(ydCol, rowIdx)
								excelFile.SetCellValue(sheetName, ydCell, conv.Float32Default(item.YearDiffValue, 0))
							}
							// 写入YearDiffRatio（格式化为百分比）
							if item.YearDiffRatio != nil {
								yrCell, _ := excelize.CoordinatesToCellName(yrCol, rowIdx)
								excelFile.SetCellValue(sheetName, yrCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.YearDiffRatio, 0)*100))
							}
						}
					}
					rowIdx++
				}
			default:
				header := []string{}
				itemMap := make(map[string]any)
				raw, _ := json.Marshal(data)
				json.Unmarshal(raw, &itemMap)
				for key := range itemMap {
					header = append(header, key)
				}
				for i, key := range header {
					cell, _ := excelize.CoordinatesToCellName(i+1, 1)
					excelFile.SetCellValue(sheetName, cell, key)
				}

				// 处理数据行
				itemRaw, _ := json.Marshal(data)
				itemMap = make(map[string]any)
				json.Unmarshal(itemRaw, &itemMap)
				for colIdx, key := range header {
					cell, _ := excelize.CoordinatesToCellName(colIdx+1, 2)
					value := itemMap[key]
					switch v := value.(type) {
					case map[string]any, []any:
						jsonValue, _ := json.Marshal(v)
						excelFile.SetCellValue(sheetName, cell, string(jsonValue))
					default:
						excelFile.SetCellValue(sheetName, cell, v)
					}
				}
			}
		}
	}

	if err := excelFile.SaveAs(filePath); err != nil {
		return "", errors.Wrap(err, "failed to save excel file")
	}
	return filePath, nil
}
