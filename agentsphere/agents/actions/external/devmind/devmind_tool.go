package devmind

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	rtentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/gopkg/facility/ternary"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gptr"
	"github.com/xuri/excelize/v2"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/devmind"
	"github.com/pkg/errors"
)

// Tool constants
const (
	// Story metrics tools
	ToolQueryStoryMetrics            = "query_story_metrics"
	ToolQueryStoryMetricsDescription = "Query the list of story metrics related to the user"

	// Node tools
	ToolQueryBusinessNodes            = "query_business_nodes"
	ToolQueryBusinessNodesDescription = "Query the list of business line nodes related to the user"

	ToolQueryReportNodes            = "query_report_nodes"
	ToolQueryReportNodesDescription = "Query the list of reporting line nodes related to the user"

	// Metric data tools
	ToolQueryMetricData            = "query_metric_data"
	ToolQueryMetricDataDescription = "Query data for a specific metric"

	// Favorite tools
	ToolManageFavorite            = "manage_favorite"
	ToolManageFavoriteDescription = "Create or delete a favorite metric"

	// Insight report data tools
	ToolInsightReports            = "insight_report_data"
	ToolInsightReportsDescription = "Query data for a specific insight report, including metric data"

	// Query Data Model tools
	ToolQueryDataModel            = "query_data_model"
	ToolQueryDataModelDescription = "Query the specific public data model"

	// Query Analysis Metric tools
	ToolQueryAnalysisMetric            = "query_analysis_metric"
	ToolQueryAnalysisMetricDescription = "Query the specific analysis metric related to the user"

	// Query Analysis Dimension tools
	ToolQueryAnalysisDimension            = "query_analysis_dimension"
	ToolQueryAnalysisDimensionDescription = "Query the specific analysis dimension related to the user"

	// Query Chart Url tools
	ToolQueryChartUrl            = "query_chart_url"
	ToolQueryChartUrlDescription = "Query the charts url based on the returned dimensions, metrics, and data model."

	// Query Chart Info tools
	ToolQueryChartInfo            = "query_chart_info"
	ToolQueryChartInfoDescription = "Query the chart info based on the chart url."
)

// reportData or MetricData Graph constants
const (
	AimeCardGraph          = "card_graph"           // 指标卡
	AimeCardDimensionGraph = "card_dimension_graph" // 指标卡-对比分析
	AimeLineGraph          = "line_graph"           // 折线图
	AimeChartSheet         = "chart_sheet"          // 图表表
	AimeBarGraph           = "bar_graph"            // 柱状图
	AimeDoubleAxisGraph    = "double_axis_graph"    // 双轴图
	AimePieGraph           = "pie_graph"            // 饼图
	AimeBarRowGraph        = "bar_row_graph"        // 条形图
)

// QueryStoryMetricsArgs defines the arguments for querying story metrics
type QueryStoryMetricsArgs struct {
	// No specific arguments needed for this endpoint
}

// QueryNodesArgs defines the arguments for querying nodes
type QueryNodesArgs struct {
	// No specific arguments needed for this endpoint
}

// QueryMetricDataArgs defines the arguments for querying metric data
type QueryMetricDataArgs struct {
	StoryID         string `json:"story_id" mapstructure:"story_id" description:"The ID of the story metric to query"`
	TimeGranularity string `json:"time_granularity" mapstructure:"time_granularity" description:"Time granularity for the query. Valid values include: 日、周、双周、月、双月、季度、半年、年、半年绩效周期、全年绩效周期"`
	QueryDate       string `json:"query_date" mapstructure:"query_date" description:"Date to query in YYYY-MM-DD format"`
	NodeID          string `json:"node_id" mapstructure:"node_id" description:"ID of the node to query data for. The node ID can be obtained from the results of the ToolQueryBusinessNodes or ToolQueryReportNodes tools. Leave empty to query metrics related to the current user."`
}

// ManageFavoriteArgs defines the arguments for managing favorites
type ManageFavoriteArgs struct {
	StoryID string `json:"story_id" mapstructure:"story_id" description:"The ID of the story metric to manage"`
	Status  string `json:"status" mapstructure:"status" description:"Status: '1' to create, '0' to delete"`
}

// QueryInsightReportArgs defines the arguments for querying insight report
type QueryInsightReportArgs struct {
	ReportID        string `json:"report_id" mapstructure:"report_id" description:"The ID of the insight report to query"`
	TimeGranularity string `json:"time_granularity" mapstructure:"time_granularity" description:"Time granularity for the query. Valid values include: 日、周、双周、月、双月、季度、半年、年、半年绩效周期、全年绩效周期"`
	QueryDate       string `json:"query_date" mapstructure:"query_date" description:"Date to query in YYYY-MM-DD format"`
}

// QueryPublicDataModelArgs defines the arguments for querying public data model
type QueryPublicDataModelArgs struct {
	RawQuery          string  `json:"raw_query" mapstructure:"raw_query" description:"用户输入的prompt的原生输入，来源于模版参数raw_query，原封不动的取给我，不需要做任何改动。The raw query of the public data model to query, extract the user's prompt (raw_query) exactly as it is, no modifications of any kind are needed."`
	TaskID            *string `json:"task_id" mapstructure:"task_id" description:"The ID of the task to query, 可选"`
	AnalysisModel     string  `json:"analysis_model" mapstructure:"analysis_model" description:"The analysis data model for generating visual analytical charts extracted from the user's input prompt"`
	AnalysisMetric    string  `json:"analysis_metric" mapstructure:"analysis_metric" description:"The analysis metric for generating visual analytical charts extracted from the user's input prompt"`
	AnalysisDimension string  `json:"analysis_dimension" mapstructure:"analysis_dimension" description:"The analysis dimension for generating visual analytical charts extracted from the user's input prompt, include time dimension"`
	FilterCondition   string  `json:"filter_condition" mapstructure:"filter_condition" description:"The filtering conditions for generating visual analytical charts extracted from the user's input prompt, include time condition dimension"`
}

// QueryAnalysisMetricArgs defines the arguments for querying analysis metric
type QueryAnalysisMetricArgs struct {
	ModelID           string `json:"model_id" mapstructure:"model_id" description:"The ID of the data model to query"`
	RawQuery          string `json:"raw_query" mapstructure:"raw_query" description:"用户输入的prompt的原生输入，来源于模版参数raw_query，原封不动的取给我，不需要做任何改动。The raw query of the public data model to query, extract the user's prompt (raw_query) exactly as it is, no modifications of any kind are needed."`
	AnalysisMetric    string `json:"analysis_metric" mapstructure:"analysis_metric" description:"The analysis metric for generating visual analytical charts extracted from the user's input prompt"`
	AnalysisDimension string `json:"analysis_dimension" mapstructure:"analysis_dimension" description:"The analysis dimension for generating visual analytical charts extracted from the user's input prompt, include time dimension"`
	FilterCondition   string `json:"filter_condition" mapstructure:"filter_condition" description:"The filtering conditions for generating visual analytical charts extracted from the user's input prompt, include time condition dimension"`
}

// QueryAnalysisDimensionArgs defines the arguments for querying analysis dimension
type QueryAnalysisDimensionArgs struct {
	ModelID           string `json:"model_id" mapstructure:"model_id" description:"The ID of the data model to query"`
	AnalysisMetric    string `json:"analysis_metric" mapstructure:"analysis_metric" description:"The analysis metric for generating visual analytical charts extracted from the user's input prompt"`
	AnalysisDimension string `json:"analysis_dimension" mapstructure:"analysis_dimension" description:"The analysis dimension for generating visual analytical charts extracted from the user's input prompt, include time dimension"`
	FilterCondition   string `json:"filter_condition" mapstructure:"filter_condition" description:"The filtering conditions for generating visual analytical charts extracted from the user's input prompt, include time condition dimension"`
}

// QueryChartUrlArgs defines the arguments for querying chart url
type QueryChartUrlArgs struct {
	AnalysisMetric     string                  `json:"analysis_metric" mapstructure:"analysis_metric" description:"The analysis metric for generating visual analytical charts extracted from the user's input prompt"`
	AnalysisDimension  string                  `json:"analysis_dimension" mapstructure:"analysis_dimension" description:"The analysis dimension for generating visual analytical charts extracted from the user's input prompt, include time dimension"`
	FilterCondition    string                  `json:"filter_condition" mapstructure:"filter_condition" description:"The filtering conditions for generating visual analytical charts extracted from the user's input prompt, include time condition dimension"`
	GetChartUrlRequest *devmind.GetChartByUser `json:"chart_url_request" mapstructure:"chart_url_request" description:"The request body for querying chart data,It could be different requests resulting from combinations of various dimensions, metrics, or filter values."`
}

// QueryChartInfoArgs defines the arguments for querying chart info
type QueryChartInfoArgs struct {
	ChartUrl string  `json:"chart_url" mapstructure:"chart_url" description:"The chart url to query chart info, 不需要进行任何的转义，直接使用"`
	TaskID   *string `json:"task_id" mapstructure:"task_id" description:"The task id to query chart info, 可选"`
}

// NewQueryStoryMetricsTool creates a new tool for querying story metrics
func NewQueryStoryMetricsTool() iris.Action {
	return actions.ToTool(ToolQueryStoryMetrics, ToolQueryStoryMetricsDescription, QueryStoryMetrics)
}

// NewQueryBusinessNodesTool creates a new tool for querying business nodes
func NewQueryBusinessNodesTool() iris.Action {
	return actions.ToTool(ToolQueryBusinessNodes, ToolQueryBusinessNodesDescription, QueryBusinessNodes)
}

// NewQueryReportNodesTool creates a new tool for querying report nodes
func NewQueryReportNodesTool() iris.Action {
	return actions.ToTool(ToolQueryReportNodes, ToolQueryReportNodesDescription, QueryReportNodes)
}

// NewQueryMetricDataTool creates a new tool for querying metric data
func NewQueryMetricDataTool() iris.Action {
	return actions.ToTool(ToolQueryMetricData, ToolQueryMetricDataDescription, QueryMetricData)
}

// NewManageFavoriteTool creates a new tool for managing favorites
func NewManageFavoriteTool() iris.Action {
	return actions.ToTool(ToolManageFavorite, ToolManageFavoriteDescription, ManageFavorite)
}

// NewInsightReportsTool creates a new tool for querying report data
func NewInsightReportsTool() iris.Action {
	return actions.ToTool(ToolInsightReports, ToolInsightReportsDescription, InsightReport)
}

// NewQueryDataModelTool creates a new tool for querying public data model
func NewQueryDataModelTool() iris.Action {
	return actions.ToTool(ToolQueryDataModel, ToolQueryDataModelDescription, QueryPublicDataModel)
}

// NewQueryAnalysisMetricTool creates a new tool for querying analysis metric data
func NewQueryAnalysisMetricTool() iris.Action {
	return actions.ToTool(ToolQueryAnalysisMetric, ToolQueryAnalysisMetricDescription, QueryAnalysisMetric)
}

// NewQueryAnalysisDimensionTool creates a new tool for querying analysis dimension
func NewQueryAnalysisDimensionTool() iris.Action {
	return actions.ToTool(ToolQueryAnalysisDimension, ToolQueryAnalysisDimensionDescription, QueryAnalysisDimension)
}

// NewQueryChartUrlTool creates a new tool for querying chart url
func NewQueryChartUrlTool() iris.Action {
	return actions.ToTool(ToolQueryChartUrl, ToolQueryChartUrlDescription, QueryChartUrl)
}

// NewQueryChartInfoTool creates a new tool for querying chart info
func NewQueryChartInfoTool() iris.Action {
	return actions.ToTool(ToolQueryChartInfo, ToolQueryChartInfoDescription, QueryChartInfo)
}

var TimeGranularityMap = map[string]string{
	"日":      "1",
	"天":      "1",
	"周":      "2",
	"双周":     "14",
	"月":      "3",
	"双月":     "4",
	"季度":     "49",
	"半年":     "27",
	"年":      "34",
	"半年绩效周期": "51",
	"全年绩效周期": "52",
}

// QueryStoryMetrics queries the list of metrics related to the user
func QueryStoryMetrics(run *iris.AgentRunContext, args QueryStoryMetricsArgs) (map[string]any, error) {
	ctx := context.Background()

	api, err := devmind.New()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}

	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.GetStoryMetrics(ctx, jwtToken)
	if err != nil {
		//run.GetLogger().Errorf("query story metrics failed, err: %v", err)
		return nil, err
	}

	list := result.StoryInfoList
	data := make([]any, len(list))
	for i, v := range list {
		data[i] = v
	}
	filePath, err := writeStoryMetricsToExcel(data, "story_metrics")
	if err != nil {
		//run.GetLogger().Errorf("failed to write story metrics to excel, err: %v", err)
		return nil, err
	}

	return map[string]any{
		"reference": filePath,
		"message":   "Successfully retrieved and saved story metrics data",
	}, nil
}

// QueryBusinessNodes queries the list of business line nodes related to the user
func QueryBusinessNodes(run *iris.AgentRunContext, args QueryNodesArgs) (map[string]any, error) {
	ctx := context.Background()

	api, err := devmind.New()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.GetBusinessNodes(ctx, jwtToken)
	if err != nil {
		//run.GetLogger().Errorf("query business nodes failed, err: %v", err)
		return nil, err
	}
	list := result.NodeList
	data := make([]any, len(list))
	for i, v := range list {
		data[i] = v
	}
	filePath, err := writeStoryMetricsToExcel(data, "business_nodes")
	if err != nil {
		//run.GetLogger().Errorf("failed to write business nodes to excel, err: %v", err)
		return nil, err
	}

	return map[string]any{
		"reference": filePath,
		"message":   "Successfully retrieved and saved business nodes data",
	}, nil
}

// QueryReportNodes queries the list of reporting line nodes related to the user
func QueryReportNodes(run *iris.AgentRunContext, args QueryNodesArgs) (map[string]any, error) {
	ctx := context.Background()

	api, err := devmind.New()
	//api, err := devmind.NewTestClient()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}

	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.GetReportNodes(ctx, jwtToken)
	if err != nil {
		//run.GetLogger().Errorf("query report nodes failed, err: %v", err)
		return nil, err
	}
	list := result.NodeList
	data := make([]any, len(list))
	for i, v := range list {
		data[i] = v
	}
	filePath, err := writeStoryMetricsToExcel(data, "reporting_line_nodes")
	if err != nil {
		//run.GetLogger().Errorf("failed to write report nodes to excel, err: %v", err)
		return nil, err
	}

	return map[string]any{
		"reference": filePath,
		"message":   "Successfully retrieved and saved report nodes data",
	}, nil
}

// QueryMetricData queries data for a specific metric
func QueryMetricData(run *iris.AgentRunContext, args QueryMetricDataArgs) (map[string]any, error) {
	ctx := context.Background()

	api, err := devmind.New()
	if err != nil {
		run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)

	if args.StoryID == "" {
		return nil, iris.NewRecoverable(errors.New("index_id cannot be empty"))
	}
	if args.TimeGranularity == "" {
		return nil, iris.NewRecoverable(errors.New("time_granularity cannot be empty"))
	}
	if args.QueryDate == "" {
		return nil, iris.NewRecoverable(errors.New("query_date cannot be empty"))
	}
	if args.NodeID == "" {
		node, err := api.GetCurrentUserNode(ctx, jwtToken)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		args.NodeID = node.NodeID
	}

	// check time_granularity is valid, if not valid, return error
	if _, ok := TimeGranularityMap[args.TimeGranularity]; !ok {
		return nil, iris.NewRecoverable(errors.New("time_granularity is not valid"))
	}
	timeGranularity := TimeGranularityMap[args.TimeGranularity]

	result, err := api.QueryMetricData(ctx, jwtToken, args.StoryID, timeGranularity, args.QueryDate, args.NodeID)
	if err != nil {
		//run.GetLogger().Errorf("query metric data failed, err: %v", err)
		return nil, err
	}

	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		//run.GetLogger().Errorf("failed to create devmind directory, err: %v", err)
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}

	// Generate a filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("metric_data_%s_%s.json", args.StoryID, timestamp)
	filePath := filepath.Join(logsDir, filename)

	// Open file for writing
	file, err := os.Create(filePath)
	if err != nil {
		//run.GetLogger().Errorf("failed to create file, err: %v", err)
		return nil, errors.Wrap(err, "failed to create file")
	}
	defer file.Close()

	// 使用json.Encoder避免Unicode转义
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(result); err != nil {
		return nil, errors.Wrap(err, "failed to marshal and write metric data")
	}

	return map[string]any{
		"reference": filePath,
		"message":   fmt.Sprintf("Successfully retrieved and saved metric data for ID %s", args.StoryID),
	}, nil
}

// ManageFavorite creates or deletes a favorite metric
func ManageFavorite(run *iris.AgentRunContext, args ManageFavoriteArgs) (map[string]any, error) {
	ctx := context.Background()

	if args.StoryID == "" {
		return nil, iris.NewRecoverable(errors.New("collector_id cannot be empty"))
	}
	if args.Status == "" || (args.Status != "0" && args.Status != "1") {
		return nil, iris.NewRecoverable(errors.New("status must be either '0' (delete) or '1' (create)"))
	}

	api, err := devmind.New()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}

	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.ManageFavorite(ctx, jwtToken, args.StoryID, args.Status)
	if err != nil {
		//run.GetLogger().Errorf("manage favorite failed, err: %v", err)
		return nil, err
	}

	action := "created"
	if args.Status == "0" {
		action = "deleted"
	}

	return map[string]any{
		"success": true,
		"message": fmt.Sprintf("Successfully %s favorite for collector ID %s", action, args.StoryID),
		"result":  result,
	}, nil
}

// InsightReport query data for a specific insight report
func InsightReport(run *iris.AgentRunContext, args QueryInsightReportArgs) (map[string]any, error) {
	ctx := context.Background()

	if args.ReportID == "" {
		return nil, iris.NewRecoverable(errors.New("index_id cannot be empty"))
	}
	if args.TimeGranularity == "" {
		return nil, iris.NewRecoverable(errors.New("time_granularity cannot be empty"))
	}
	if args.QueryDate == "" {
		return nil, iris.NewRecoverable(errors.New("query_date cannot be empty"))
	}

	// check time_granularity is valid, if not valid, return error
	if _, ok := TimeGranularityMap[args.TimeGranularity]; !ok {
		return nil, iris.NewRecoverable(errors.New("time_granularity is not valid"))
	}
	timeGranularity := TimeGranularityMap[args.TimeGranularity]

	//api, err := devmind.NewTestClient()
	api, err := devmind.New()
	if err != nil {
		//run.GetLogger().Errorf("create devmind api client failed, err: %v", err)
		return nil, err
	}

	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.QueryInsightReportData(ctx, jwtToken, args.ReportID, timeGranularity, args.QueryDate)
	if err != nil {
		//run.GetLogger().Errorf("query metric data failed, err: %v", err)
		return nil, err
	}

	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		//run.GetLogger().Errorf("failed to create devmind directory, err: %v", err)
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}

	// Generate a filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("insight_report_%s_%s.json", args.ReportID, timestamp)
	jsonFilePath := filepath.Join(logsDir, filename)

	// Open file for writing
	file, err := os.Create(jsonFilePath)
	if err != nil {
		//run.GetLogger().Errorf("failed to create file, err: %v", err)
		return nil, errors.Wrap(err, "failed to create file")
	}
	defer file.Close()

	// 使用json.Encoder避免Unicode转义
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(result.ReportInfo); err != nil {
		return nil, errors.Wrap(err, "failed to marshal and write metric data")
	}

	// 根据图表类型，将数据写入不同的sheet
	filePath, err := writeReportMetricsToExcel(run, result.ReportMetricQueryInfo, args.ReportID)
	if err != nil {
		//run.GetLogger().Errorf("failed to write report nodes to excel, err: %v", err)
		return nil, err
	}

	return map[string]any{
		"report_info_reference":                jsonFilePath,
		"report_metric_stories_data_reference": filePath,
		"message": fmt.Sprintf("Successfully retrieved and saved insight report data for ID %s,"+
			"The file path of report_info_reference is the report information. The file path of report_metric_stories_data_reference is metric stories of the report."+
			"The first sheet is a collection of information about all metric stroies in this report, and the other sheets are all metric stories result data(different chart types data) returned from queries."+
			"in addition, the sheet name include the id of the metric story, and the chart type of the metric story query results.", args.ReportID),
	}, nil
}

// QueryPublicDataModel query the specific public data model
func QueryPublicDataModel(run *iris.AgentRunContext, args QueryPublicDataModelArgs) (map[string]any, error) {
	ctx := context.Background()

	//api, err := devmind.NewTestClient()
	api, err := devmind.New()
	if err != nil {
		return nil, err
	}

	sessionID := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.QueryPublicDataModel(ctx, jwtToken, sessionID, args.RawQuery, args.AnalysisModel, args.AnalysisMetric, args.AnalysisDimension, args.FilterCondition, args.TaskID)
	if err != nil {
		logs.CtxError(ctx, "QueryPublicDataModel failed, err: %v", err)
		return nil, err
	}

	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}
	chartRequestName := fmt.Sprintf("public_recommend_model.json")
	filePath := filepath.Join(logsDir, chartRequestName)

	// Open file for writing
	file, err := os.Create(filePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create file")
	}
	defer file.Close()

	// 使用json.Encoder避免Unicode转义
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(result.PublicRecommendModelList); err != nil {
		return nil, errors.Wrap(err, "failed to marshal chart data")
	}

	return map[string]any{
		"public_recommend_model_json": filePath,
		"message":                     "Successfully retrieved and saved query public recommend data model, the public recommend data model data is in the public_recommend_model_json, the model ID is included in it.",
	}, nil
}

// QueryAnalysisMetric query the specific analysis metric related to the user
func QueryAnalysisMetric(run *iris.AgentRunContext, args QueryAnalysisMetricArgs) (map[string]any, error) {
	ctx := context.Background()

	if args.ModelID == "" {
		return nil, iris.NewRecoverable(errors.New("QueryAnalysisMetric model_id cannot be empty"))
	}
	//api, err := devmind.NewTestClient()
	api, err := devmind.New()
	if err != nil {
		return nil, err
	}

	sessionID := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.QueryAnalysisMetric(ctx, jwtToken, sessionID, args.ModelID, args.RawQuery, args.AnalysisMetric, args.AnalysisDimension, args.FilterCondition)
	if err != nil {
		logs.CtxError(ctx, "QueryAnalysisMetric failed, err: %v", err)
		return nil, err
	}

	// 公有推荐指标存JSON
	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}
	// Generate a filename
	publicMetricFileName := fmt.Sprintf("public_recommend_metric_%s.json", args.ModelID)
	publicMetricFilePath := filepath.Join(logsDir, publicMetricFileName)
	// Open file for writing
	file, err := os.Create(publicMetricFilePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create file")
	}
	defer file.Close()
	// 使用json.Encoder避免Unicode转义
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(result.PublicRecommendMetricList); err != nil {
		return nil, errors.Wrap(err, "failed to marshal and write public metric data")
	}

	// 私有维度写Excel
	privateMetricList := result.PrivateMetricList
	privateMetricData := make([]any, len(privateMetricList))
	for i, v := range privateMetricList {
		privateMetricData[i] = v
	}
	privateMetricFilePath, err := writeDataToExcel(privateMetricData, "private_analysis_metric_related_user", "PrivateAnalysisMetrics")
	if err != nil {
		return nil, err
	}

	return map[string]any{
		"public_recommend_metric_reference": publicMetricFilePath,
		"private_metric_reference":          privateMetricFilePath,
		"message": "Successfully retrieved and saved query analysis metric data," +
			"The public recommend metric data is in the public_recommend_metric_reference, public recommend metrics are recommended by RAG, with a maximum of ten. Their structure includes recommendation reasons and scores, and the weights used can be relatively high. " +
			"The private metric data is in the private_metric_reference, private metric data refers to all metrics that the user has permission to access.",
	}, nil
}

// QueryAnalysisDimension query the specific analysis dimension related to the user
func QueryAnalysisDimension(run *iris.AgentRunContext, args QueryAnalysisDimensionArgs) (map[string]any, error) {
	ctx := context.Background()

	if args.ModelID == "" {
		return nil, iris.NewRecoverable(errors.New("QueryAnalysisDimension model_id cannot be empty"))
	}
	//api, err := devmind.NewTestClient()
	api, err := devmind.New()
	if err != nil {
		return nil, err
	}

	sessionID := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	result, err := api.QueryAnalysisDimension(ctx, jwtToken, sessionID, args.ModelID, args.AnalysisMetric, args.AnalysisDimension, args.FilterCondition)
	if err != nil {
		logs.CtxError(ctx, "QueryAnalysisDimension failed, err: %v", err)
		return nil, err
	}

	// 私有维度
	privateDimList := result.PrivateDimList
	privateDimListData := make([]any, len(privateDimList))
	for i, v := range privateDimList {
		privateDimListData[i] = v
	}
	privateDimFilePath, err := writeDataToExcel(privateDimListData, "private_analysis_dimension_related_user", "PrivateAnalysisDimension")
	if err != nil {
		return nil, err
	}

	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}

	// 公有维度
	publicDimFileName := fmt.Sprintf("public_recommend_dimension_%s.json", args.ModelID)
	publicDimFilePath := filepath.Join(logsDir, publicDimFileName)
	publicDimFile, err := os.Create(publicDimFilePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create public dimension file")
	}
	defer publicDimFile.Close()
	publicDimEncoder := json.NewEncoder(publicDimFile)
	publicDimEncoder.SetIndent("", "  ")
	publicDimEncoder.SetEscapeHTML(false)
	if err := publicDimEncoder.Encode(result.PublicRecommendDimensionList); err != nil {
		return nil, errors.Wrap(err, "failed to marshal and write public dimension data")
	}

	// 筛选条件
	publicConditionFileName := fmt.Sprintf("public_recommend_condition_dimension_%s.json", args.ModelID)
	publicFilterConditionDimFilePath := filepath.Join(logsDir, publicConditionFileName)
	publicConditionFile, err := os.Create(publicFilterConditionDimFilePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create public condition dimension file")
	}
	defer publicConditionFile.Close()
	publicConditionEncoder := json.NewEncoder(publicConditionFile)
	publicConditionEncoder.SetIndent("", "  ")
	publicConditionEncoder.SetEscapeHTML(false)
	if err := publicConditionEncoder.Encode(result.PublicRecommendConditionDimensionList); err != nil {
		return nil, errors.Wrap(err, "failed to marshal and write public condition dimension data")
	}

	return map[string]any{
		"public_recommend_dimension_reference":        publicDimFilePath,
		"private_dimension_reference":                 privateDimFilePath,
		"public_filter_condition_dimension_reference": publicFilterConditionDimFilePath,
		"message": "Successfully retrieved and saved query analysis dimension data, the private dimension data is in the private_dimension_reference, private dimension data refers to all dimensions that the user has permission to access. Incidentally, dimensions can be used as filtering conditions." +
			"The public dimension data is in the public_recommend_dimension_reference, public dimension are recommended by RAG, with a maximum of ten. Their structure includes recommendation reasons and scores, and the weights used can be relatively high. " +
			"The public filter condition dimension data is in the public_filter_condition_dimension_reference, public recommend filter condition dimension are recommended by RAG, with a maximum of ten. Their structure includes recommendation reasons and scores, and the weights used can be relatively high. This is also a form of dimension, but it includes operational filtering conditions (operation symbols and filter values).",
	}, nil
}

// QueryChartUrl query the specific chart
func QueryChartUrl(run *iris.AgentRunContext, args QueryChartUrlArgs) (map[string]any, error) {
	ctx := context.Background()

	//api, err := devmind.NewTestClient()
	api, err := devmind.New()
	if err != nil {
		return nil, err
	}
	if args.GetChartUrlRequest == nil || args.GetChartUrlRequest.ModelID == "" {
		return nil, iris.NewRecoverable(errors.New("QueryChartUrl model_id cannot be empty"))
	}
	// 兜底查询折线图
	if args.GetChartUrlRequest.ChartType == "" {
		args.GetChartUrlRequest.ChartType = "line"
	}

	sessionID := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	queryRequest := devmind.AimeChartQueryByUser{
		SessionID:         sessionID,
		AnalysisMetric:    args.AnalysisMetric,
		AnalysisDimension: args.AnalysisDimension,
		FilterCondition:   args.FilterCondition,
		GetChartByUser:    gptr.Indirect(args.GetChartUrlRequest),
	}

	res, getChartResErr := api.QueryChartUrl(ctx, jwtToken, gptr.Of(queryRequest))
	if getChartResErr != nil {
		logs.CtxError(ctx, "QueryChartUrl failed, err: %v", err)
		return nil, err
	}

	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}
	chartRequestName := fmt.Sprintf("chart_request.json")
	filePath := filepath.Join(logsDir, chartRequestName)

	// Open file for writing
	file, err := os.Create(filePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create file")
	}
	defer file.Close()

	// 使用json.Encoder避免Unicode转义
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(args); err != nil {
		return nil, errors.Wrap(err, "failed to marshal chart data")
	}

	return map[string]any{
		"chart_jump_url":          res.ChartJumpUrl,
		"chart_preview_url":       res.ChartPreviewUrl,
		"chart_request_json_path": filePath,
		"message":                 "Successfully retrieved the query chart result. The chart jump URL will be returned. users can click the jump URL to view the chart. The preview URL will be returned. users can preview the chart on the lark doc.",
	}, nil
}

// QueryChartInfo query the specific chart
func QueryChartInfo(run *iris.AgentRunContext, args QueryChartInfoArgs) (map[string]any, error) {
	ctx := context.Background()

	//api, err := devmind.NewTestClient()
	api, err := devmind.New()
	if err != nil {
		return nil, err
	}
	if args.ChartUrl == "" {
		return nil, iris.NewRecoverable(errors.New("QueryChartInfo chart_url cannot be empty"))
	}

	sessionID := run.GetEnv(rtentity.RuntimeEnvironSessionID)
	jwtToken := run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)
	res, getChartResErr := api.QueryChartInfo(ctx, jwtToken, sessionID, args.ChartUrl, args.TaskID)
	if getChartResErr != nil {
		logs.CtxError(ctx, "QueryChartInfo failed, err: %v", err)
		return nil, err
	}

	logsDir := "devmind"
	err = os.MkdirAll(logsDir, 0750)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create devmind directory")
	}
	chartInfoResName := fmt.Sprintf("chart_info_result.json")
	filePath := filepath.Join(logsDir, chartInfoResName)

	// Open file for writing
	file, err := os.Create(filePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create file")
	}
	defer file.Close()

	// 使用json.Encoder避免Unicode转义
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(res.ChartInfo); err != nil {
		return nil, errors.Wrap(err, "failed to marshal chart data")
	}

	return map[string]any{
		"chart_info_result_json_path": filePath,
		"message":                     "Successfully retrieved the query chart info result. The chart info result will be returned, include the modelID, metric, dimension, filter condition, chart type.",
	}, nil
}

func writeStoryMetricsToExcel(data []any, name string) (string, error) {
	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err := os.MkdirAll(logsDir, 0750)
	if err != nil {
		//run.GetLogger().Errorf("failed to create devmind directory, err: %v", err)
		return "", errors.Wrap(err, "failed to create devmind directory")
	}
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("%s_%s.xlsx", name, timestamp)
	filePath := filepath.Join(logsDir, filename)

	excelFile := excelize.NewFile()
	sheetName := "StoryMetrics"
	excelFile.NewSheet(sheetName)

	// 删除默认的Sheet1
	excelFile.DeleteSheet("Sheet1")

	if len(data) > 0 {
		header := []string{}
		itemMap := make(map[string]any)
		raw, _ := json.Marshal(data[0])
		json.Unmarshal(raw, &itemMap)
		for key := range itemMap {
			header = append(header, key)
		}
		for i, key := range header {
			cell, _ := excelize.CoordinatesToCellName(i+1, 1)
			excelFile.SetCellValue(sheetName, cell, key)
		}

		for rowIdx, item := range data {
			itemRaw, _ := json.Marshal(item)
			itemMap := make(map[string]any)
			json.Unmarshal(itemRaw, &itemMap)
			for colIdx, key := range header {
				cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx+2)
				value := itemMap[key]
				switch v := value.(type) {
				case map[string]any, []any:
					jsonValue, _ := json.Marshal(v)
					excelFile.SetCellValue(sheetName, cell, string(jsonValue))
				default:
					excelFile.SetCellValue(sheetName, cell, v)
				}
			}
		}
	}

	if err := excelFile.SaveAs(filePath); err != nil {
		return "", errors.Wrap(err, "failed to save excel file")
	}
	return filePath, nil
}

func writeReportMetricsToExcel(run *iris.AgentRunContext, dataList []devmind.AimeMetricQueryInfo, reportID string) (string, error) {
	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err := os.MkdirAll(logsDir, 0750)
	if err != nil {
		//run.GetLogger().Errorf("failed to create devmind directory, err: %v", err)
		return "", errors.Wrap(err, "failed to create devmind directory")
	}
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("report_metric_stories_data_%s_%s.xlsx", reportID, timestamp)
	filePath := filepath.Join(logsDir, filename)

	excelFile := excelize.NewFile()
	sheetName := "ReportMetricStoriesInfo"
	excelFile.SetSheetName("Sheet1", sheetName)
	// 指标故事信息
	metricHeader := []string{"Metric Story Name", "Metric Story ID", "Metric Story Description", "Metric Story DataUnit", "Metric Story Caliber", "Metric Story Analysis Direction"}
	for colIdx, key := range metricHeader {
		cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
		excelFile.SetCellValue(sheetName, cell, key)
	}
	for i, data := range dataList {
		rowIdx := i + 2
		excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIdx), data.MetricName)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIdx), data.MetricId)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIdx), data.MetricDescription)
		dataUnitJsonValue, _ := json.Marshal(data.MetricDataUnit)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIdx), dataUnitJsonValue)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("E%d", rowIdx), data.MetricCaliber)
		excelFile.SetCellValue(sheetName, fmt.Sprintf("F%d", rowIdx), ternary.TernaryString(data.MetricAnalysisDirection == "Negative", "负向指标，越低越好", "正向指标，越高越好"))
	}

	for _, data := range dataList {
		// 处理表头
		for _, queryData := range data.MetricQueryDataList {
			if queryData.GraphType == "" {
				run.GetLogger().Errorf("devmind api query data GraphType is null, metricName: %s, metricId: %s", data.MetricName, data.MetricId)
				continue
			}
			sheetName = fmt.Sprintf("%v %v", data.MetricId, queryData.GraphType)
			excelFile.NewSheet(sheetName)
			switch queryData.GraphType {
			case AimeCardGraph:
				cardGraph := queryData.CardGraph
				if cardGraph == nil {
					run.GetLogger().Errorf("devmind api query data cardGraph is nil")
					continue
				}
				// 定义表头
				header := []string{"Metric Story Name", "Date", "Value", "Baseline Value", "Baseline Conclusion", "History Conclusion"}
				colStartIdx := 1
				for colIdx, key := range header {
					cell, _ := excelize.CoordinatesToCellName(colIdx+colStartIdx, 1)
					excelFile.SetCellValue(sheetName, cell, key)
				}

				rowIdx := 2
				for _, series := range cardGraph.DataList {
					for xIdx := len(cardGraph.XAxis) - 1; xIdx >= 0; xIdx-- {
						xVal := cardGraph.XAxis[xIdx]
						// 写入 Series Name
						excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIdx), series.Name)
						// 写入 XAxis 值
						excelFile.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIdx), xVal)
						// 写入 Data 值
						if xIdx < len(series.SubDataList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIdx), series.SubDataList[xIdx])
						}
						// 写入 Baseline Value List 值
						if xIdx < len(series.BaselineValueList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIdx), series.BaselineValueList[xIdx])
						}
						if xIdx == len(cardGraph.XAxis)-1 {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("E%d", rowIdx), cardGraph.BaselineConclusion)
							excelFile.SetCellValue(sheetName, fmt.Sprintf("F%d", rowIdx), cardGraph.HistoryConclusion)
						}
						// 行号递增
						rowIdx++
					}
					// 将 Avg 和 Sum 写到第一列最下面
					//avgRow := rowIdx + 1
					//sumRow := rowIdx + 2
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", avgRow), "Avg")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", avgRow), series.Avg)
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", sumRow), "Sum")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", sumRow), series.Sum)
				}
			case AimeLineGraph:
				// 折线图数据处理逻辑
				lineGraph := queryData.LineGraph
				if lineGraph == nil {
					run.GetLogger().Errorf("devmind api query data lineGraph is nil")
					continue
				}
				// 定义表头
				analysisDimensionsTitle := ternary.TernaryString(lineGraph.AnalysisDimensions == nil,
					"Analysis Dimension", conv.StringDefault(lineGraph.AnalysisDimensions, ""))
				header := []string{analysisDimensionsTitle, "Date", "Value", "Baseline Value"}
				colStartIdx := 1
				for colIdx, key := range header {
					cell, _ := excelize.CoordinatesToCellName(colIdx+colStartIdx, 1)
					excelFile.SetCellValue(sheetName, cell, key)
				}
				// 写入数据
				rowIdx := 2
				for _, series := range lineGraph.DataList {
					for xIdx := len(lineGraph.XAxis) - 1; xIdx >= 0; xIdx-- {
						xVal := lineGraph.XAxis[xIdx]
						// 写入 Series Name
						excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", rowIdx), series.Name)
						// 写入 XAxis 值
						excelFile.SetCellValue(sheetName, fmt.Sprintf("B%d", rowIdx), xVal)
						// 写入 Data 值
						if xIdx < len(series.SubDataList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("C%d", rowIdx), series.SubDataList[xIdx])
						}
						// 写入 Baseline Value List 值
						if xIdx < len(series.BaselineValueList) {
							excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIdx), series.BaselineValueList[xIdx])
						}
						// 行号递增
						rowIdx++
					}
					// 将 Avg 和 Sum 写到第一列最下面
					//avgRow := rowIdx + 1
					//sumRow := rowIdx + 2
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", avgRow), "Avg")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", avgRow), series.Avg)
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("A%d", sumRow), "Sum")
					//excelFile.SetCellValue(sheetName, fmt.Sprintf("D%d", sumRow), series.Sum)
				}
			case AimeChartSheet:
				// 图表表数据处理逻辑
				chartSheet := queryData.ChartSheet
				if chartSheet == nil {
					run.GetLogger().Errorf("devmind api query data chartSheet is nil")
					continue
				}
				// 写入表头（固定为titles） 补充子表头（在原表头下方）
				var index int
				subHeaders := []string{"", "", "环比值", "环比率", "占比", "同比值", "同比率"}
				for i, title := range chartSheet.Titles {
					// 非指标不添加子表头
					if !title.IsMetric {
						cell, _ := excelize.CoordinatesToCellName(i+1, 1)
						excelFile.SetCellValue(sheetName, cell, title.Title)
						index++
						continue
					}
					// 计算子表头位置，每个指标对应的子表头间隔 len(subHeaders) - 1 列
					baseCol := ((i - index) * (len(subHeaders) - 1)) + index + 1
					cell, _ := excelize.CoordinatesToCellName(baseCol, 1)
					excelFile.SetCellValue(sheetName, cell, title.Title)
					for j, subHeader := range subHeaders[2:] {
						cell, _ = excelize.CoordinatesToCellName(baseCol+j+1, 1)
						excelFile.SetCellValue(sheetName, cell, fmt.Sprintf("%s_%s", title.Title, subHeader))
					}
				}
				// 写入数据行
				rowIdx := 2
				for _, rowData := range chartSheet.DataList {
					for colIdx, item := range rowData {
						// 计算主值的列位置，每个指标对应的主值间隔 len(subHeaders) - 1 列
						var mainCol int
						if chartSheet.Titles[colIdx].IsMetric && colIdx >= index {
							mainCol = ((colIdx - index) * (len(subHeaders) - 1)) + index + 1
						} else {
							mainCol = colIdx + 1
						}
						cell, _ := excelize.CoordinatesToCellName(mainCol, rowIdx)
						// 写入Value
						excelFile.SetCellValue(sheetName, cell, item.Value)

						// 为非第一列并且有占比值那种添加子字段（PeriodDiffValue等）
						if chartSheet.Titles[colIdx].IsMetric {
							// 计算子字段的单元格位置（右侧相邻列）
							pdvCol := mainCol + 1
							pdrCol := pdvCol + 1
							prCol := pdrCol + 1
							ydCol := prCol + 1
							yrCol := ydCol + 1

							// 写入PeriodDiffValue
							if item.PeriodDiffValue != nil {
								pdvCell, _ := excelize.CoordinatesToCellName(pdvCol, rowIdx)
								excelFile.SetCellValue(sheetName, pdvCell, conv.Float32Default(item.PeriodDiffValue, 0))
							}
							// 写入PeriodDiffRatio（格式化为百分比）
							if item.PeriodDiffRatio != nil {
								pdrCell, _ := excelize.CoordinatesToCellName(pdrCol, rowIdx)
								excelFile.SetCellValue(sheetName, pdrCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.PeriodDiffRatio, 0)*100))
							}

							// 写入ProportionRatio（格式化为百分比）
							if item.ProportionRatio != nil {
								prCell, _ := excelize.CoordinatesToCellName(prCol, rowIdx)
								excelFile.SetCellValue(sheetName, prCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.ProportionRatio, 0)*100))
							}

							// 写入YearDiffValue
							if item.YearDiffValue != nil {
								ydCell, _ := excelize.CoordinatesToCellName(ydCol, rowIdx)
								excelFile.SetCellValue(sheetName, ydCell, conv.Float32Default(item.YearDiffValue, 0))
							}
							// 写入YearDiffRatio（格式化为百分比）
							if item.YearDiffRatio != nil {
								yrCell, _ := excelize.CoordinatesToCellName(yrCol, rowIdx)
								excelFile.SetCellValue(sheetName, yrCell, fmt.Sprintf("%.2f%%", conv.Float32Default(item.YearDiffRatio, 0)*100))
							}
						}
					}
					rowIdx++
				}
			default:
				header := []string{}
				itemMap := make(map[string]any)
				raw, _ := json.Marshal(data)
				json.Unmarshal(raw, &itemMap)
				for key := range itemMap {
					header = append(header, key)
				}
				for i, key := range header {
					cell, _ := excelize.CoordinatesToCellName(i+1, 1)
					excelFile.SetCellValue(sheetName, cell, key)
				}

				// 处理数据行
				itemRaw, _ := json.Marshal(data)
				itemMap = make(map[string]any)
				json.Unmarshal(itemRaw, &itemMap)
				for colIdx, key := range header {
					cell, _ := excelize.CoordinatesToCellName(colIdx+1, 2)
					value := itemMap[key]
					switch v := value.(type) {
					case map[string]any, []any:
						jsonValue, _ := json.Marshal(v)
						excelFile.SetCellValue(sheetName, cell, string(jsonValue))
					default:
						excelFile.SetCellValue(sheetName, cell, v)
					}
				}
			}
		}
	}

	if err := excelFile.SaveAs(filePath); err != nil {
		return "", errors.Wrap(err, "failed to save excel file")
	}
	return filePath, nil
}

func writeDataToExcel(data []any, name, sheetName string) (string, error) {
	// Create devmind directory if it doesn't exist
	logsDir := "devmind"
	err := os.MkdirAll(logsDir, 0750)
	if err != nil {
		//run.GetLogger().Errorf("failed to create devmind directory, err: %v", err)
		return "", errors.Wrap(err, "failed to create devmind directory")
	}
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("%s_%s.xlsx", name, timestamp)
	filePath := filepath.Join(logsDir, filename)

	excelFile := excelize.NewFile()
	excelFile.NewSheet(sheetName)

	// 删除默认的Sheet1
	excelFile.DeleteSheet("Sheet1")

	if len(data) > 0 {
		header := []string{}
		itemMap := make(map[string]any)
		raw, _ := json.Marshal(data[0])
		json.Unmarshal(raw, &itemMap)
		for key := range itemMap {
			header = append(header, key)
		}
		for i, key := range header {
			cell, _ := excelize.CoordinatesToCellName(i+1, 1)
			excelFile.SetCellValue(sheetName, cell, key)
		}

		for rowIdx, item := range data {
			itemRaw, _ := json.Marshal(item)
			itemMap := make(map[string]any)
			json.Unmarshal(itemRaw, &itemMap)
			for colIdx, key := range header {
				cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx+2)
				value := itemMap[key]
				switch v := value.(type) {
				case map[string]any, []any:
					jsonValue, _ := json.Marshal(v)
					excelFile.SetCellValue(sheetName, cell, string(jsonValue))
				default:
					excelFile.SetCellValue(sheetName, cell, v)
				}
			}
		}
	}

	if err := excelFile.SaveAs(filePath); err != nil {
		return "", errors.Wrap(err, "failed to save excel file")
	}
	return filePath, nil
}
