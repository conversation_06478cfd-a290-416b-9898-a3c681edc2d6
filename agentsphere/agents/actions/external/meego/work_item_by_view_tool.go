package meego

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/port/meegosdk"
)

type WorkItemByViewArgs struct {
	ViewURL        string `json:"view_url" mapstructure:"view_url" description:"工作项视图的URL，支持以下四种格式：1. 全景视图：https://meego.larkoffice.com/{simpleName}/multiProjectView/{viewId} 2. Story视图：https://meego.larkoffice.com/{simpleName}/storyView/{viewId} 3. Issue视图：https://meego.larkoffice.com/{simpleName}/issueView/{viewId} 4. 自定义工作项视图：https://meego.larkoffice.com/{simpleName}/workObjectView/{workItemType}/{viewId}。"`
	ResultFilePath string `json:"result_file_path" mapstructure:"result_file_path" description:"工作项查询结果的文件存储路径，必须为一个xlsx文件,例如：view_items_xxx.xlsx。重点注意：如果文件名已存在，会覆盖原文件，尽量避免文件名重名！"`
}

// CookieInfo represents a single cookie in the response
type CookieInfo struct {
	Name    string  `json:"name"`
	Value   string  `json:"value"`
	Path    string  `json:"path"`
	Domain  string  `json:"domain"`
	Expires float64 `json:"expires"`
}

// CookieResponse represents the response from strato-keystore API
type CookieResponse struct {
	Code    int          `json:"code"`
	Data    []CookieInfo `json:"data"`
	Message string       `json:"message"`
}

// ViewModeResponse represents the response from get_tiny_rtc_config API
type ViewModeResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		CurrentViewMode string `json:"current_view_mode"`
	} `json:"data"`
}

// ViewModeRequest represents the request for get_tiny_rtc_config API
type ViewModeRequest struct {
	ViewID string `json:"view_id"`
}

const (
	ToolWorkItemByView            = "work_item_by_view_url"
	ToolWorkItemByViewDescription = "通过Meego视图URL获取全量的工作项列表，支持四种视图类型：1. 全景视图(multiProjectView)：获取跨空间的工作项 2. Story视图(storyView)：获取需求列表 3. Issue视图(issueView)：获取缺陷列表 4. 自定义工作项视图(workObjectView)：获取指定类型的工作项。重要提示：如果链接符合上述视图URL格式，必须调用本工具获取workitem，不要调用其他工具。"
)

func NewWorkItemByView() iris.Action {
	return actions.ToTool(ToolWorkItemByView, ToolWorkItemByViewDescription, WorkItemByView)
}

func WorkItemByView(run *iris.AgentRunContext, args WorkItemByViewArgs) (map[string]any, error) {
	ctx := context.Background()

	// 提取URL中的projectName、viewId、工作项类型和quickFilterId
	var projectSimpleName, viewId, workItemType, quickFilterID string
	var isMultiProjectViewURL bool

	// 解析URL中的quickFilterId参数
	if parsedURL, err := url.Parse(args.ViewURL); err == nil {
		params := parsedURL.Query()
		quickFilterID = params.Get("quickFilterId")
	}

	// 定义URL模式和对应的处理函数
	type urlHandler struct {
		pattern     *regexp.Regexp
		handler     func(matches []string) (project, view, itemType string, isMultiProject bool)
		description string
	}

	// 定义各种URL模式的处理器
	urlHandlers := []urlHandler{
		{
			pattern: regexp.MustCompile(`https?://meego\.larkoffice\.com/([^/]+)/multiProjectView/([^/?]+)`),
			handler: func(matches []string) (string, string, string, bool) {
				return matches[1], matches[2], "", true
			},
			description: "全景视图",
		},
		{
			pattern: regexp.MustCompile(`https?://meego\.larkoffice\.com/([^/]+)/storyView/([^/?]+)`),
			handler: func(matches []string) (string, string, string, bool) {
				return matches[1], matches[2], "story", false
			},
			description: "Story视图",
		},
		{
			pattern: regexp.MustCompile(`https?://meego\.larkoffice\.com/([^/]+)/issueView/([^/?]+)`),
			handler: func(matches []string) (string, string, string, bool) {
				return matches[1], matches[2], "issue", false
			},
			description: "Issue视图",
		},
		{
			pattern: regexp.MustCompile(`https?://meego\.larkoffice\.com/([^/]+)/workObjectView/([^/]+)/([^/?]+)`),
			handler: func(matches []string) (string, string, string, bool) {
				return matches[1], matches[3], matches[2], false
			},
			description: "自定义工作项视图",
		},
	}

	// 尝试匹配所有处理器
	matched := false
	for _, handler := range urlHandlers {
		matches := handler.pattern.FindStringSubmatch(args.ViewURL)
		if len(matches) > 0 {
			projectSimpleName, viewId, workItemType, isMultiProjectViewURL = handler.handler(matches)
			matched = true
			break
		}
	}

	// 如果没有匹配任何URL模式，返回错误
	if !matched {
		logs.CtxError(ctx, "无法从URL提取项目名称和视图ID: %s", args.ViewURL)
		return nil, iris.NewRecoverable(errors.Errorf("无法从URL提取项目名称和视图ID: %s", args.ViewURL))
	}

	// 检查 larkoffice.com 域名的 cookie 是否存在且未过期，使用实际的 viewID 进行验证
	validCookies, isValid := isLarkOfficeCookieValid(run, viewId)
	if !isValid {
		return map[string]any{
			"**NEXT_ACTION**": "使用浏览器登录meego，重新授权",
			"**RESULT**":      "获取失败，larkoffice.com域名的cookie已过期或不存在，请使用浏览器访问 https://meego.larkoffice.com 重新登录授权后再重试本工具",
		}, nil
	}

	var (
		viewMode string
		err      error
	)
	// 检查URL中是否包含viewMode=chart参数
	if strings.Contains(args.ViewURL, "viewMode=chart") {
		viewMode = "chart"
	}
	if strings.Contains(args.ViewURL, "viewMode=table") {
		viewMode = "table"
	}

	if viewMode == "" {
		// 使用有效的 cookie 获取视图模式
		viewMode, err = getViewMode(run, validCookies, viewId)
		if err != nil {
			run.GetLogger().Warnf("获取视图模式失败: %v，将继续使用默认处理逻辑", err)
			viewMode = "unknown"
		}
		run.GetLogger().Infof("视图 %s 的模式为: %s", viewId, viewMode)
	}

	if viewMode == "chart" {
		return handleChartViewMode(run, args.ViewURL)
	}

	// 获取当前用户信息
	user, err := getCurrentUserInfo(run, GetUserInfoArgs{})
	if err != nil {
		run.GetLogger().Errorf("get user info failed, err: %v", err)
		return nil, err
	}

	client, err := meegosdk.New()
	if err != nil {
		run.GetLogger().Errorf("new meego client failed, err: %v", err)
		return nil, err
	}

	// 根据视图类型处理不同的逻辑
	var workItems []*meegosdk.WorkItemInfo
	if isMultiProjectViewURL {
		workItems, err = listByMultiProjectView(ctx, run, client, user.UserKey, projectSimpleName, viewId, quickFilterID)
	} else {
		workItems, err = listByFixedView(ctx, run, client, user.UserKey, projectSimpleName, viewId, workItemType, quickFilterID)
	}

	if err != nil {
		return nil, err
	}

	if len(workItems) == 0 {
		return map[string]any{
			"output": "视图中没有找到工作项",
		}, nil
	}

	var tableRes map[string]any
	run.GetLogger().Infof("view id: %s. view mode: %s", viewId, viewMode)
	if viewMode == "table" {
		run.GetLogger().Infof("Start to capture meego table screenshot")
		tableRes, _ = handleTableViewMode(run, args.ViewURL)
		run.GetLogger().Infof("Finished to capture meego table screenshot: %s", tableRes)
	}

	_, err = CreateWorkItemsExcel(run, args.ResultFilePath, projectSimpleName, workItemType, workItems)
	if err != nil {
		run.GetLogger().Errorf("create work items excel failed, err: %v", err)
		logs.CtxError(ctx, "create work items excel failed, err: %v", err)
		return nil, err
	}

	// Build output description string
	outputDescription := fmt.Sprintf(
		"已完成从视图获取工作项列表，视图URL为：%s",
		args.ViewURL,
	)
	outputDescription += fmt.Sprintf(
		"\n查询到的数据已保存到文件中，文件路径为：%s\n可以使用这些数据进行后续数据分析或数据处理。",
		args.ResultFilePath,
	)

	// Build output
	output := map[string]any{
		"description": outputDescription,
		"file_path":   args.ResultFilePath,
		"items_count": len(workItems),
		"data_summary": fmt.Sprintf("从%s视图获取了%d个工作项，包含了工作项的详细信息，如ID、名称、状态、创建者、负责人等字段",
			lo.Ternary(isMultiProjectViewURL, "全景视图", "固定视图"),
			len(workItems),
		),
		"reference": fmt.Sprintf("[%s](%s)", args.ResultFilePath, args.ResultFilePath),
	}
	if tableRes != nil {
		output["table_screenshot_info"] = tableRes
	}

	return output, nil
}

// listByFixedView 处理固定视图，获取工作项列表
func listByFixedView(ctx context.Context, run *iris.AgentRunContext, client meegosdk.Api, userKey, projectSimpleName, viewId string, workItemType string, quickFilterID string) ([]*meegosdk.WorkItemInfo, error) {
	var allWorkItemIDs []int64
	var workItems []*meegosdk.WorkItemInfo

	// 获取业务线信息和映射
	businessIdMap, err := getBusinessMap(ctx, client, userKey, projectSimpleName)
	if err != nil {
		run.GetLogger().Warnf("list business failed, err: %v", err)
	}

	// Get all fields using the new method
	fieldsMap, err := GetProjectFieldsMap(ctx, client, userKey, projectSimpleName)
	if err != nil {
		run.GetLogger().Warnf("获取项目字段定义失败 %s, err: %v", projectSimpleName, err)
	}
	// 查询视图下的工作项，使用分页循环获取，每页最多200个工作项
	pageSize := 200
	pageNum := 1

	for {
		fixView, pagination, err := client.GetFixView(ctx, userKey, projectSimpleName, viewId, &meegosdk.Page{
			PageSize: pageSize,
			PageNum:  pageNum,
		}, quickFilterID)
		if err != nil {
			run.GetLogger().Errorf("list work items by view failed, err: %v", err)
			return nil, err
		}

		if len(fixView.WorkItemIDList) > 0 {
			allWorkItemIDs = append(allWorkItemIDs, fixView.WorkItemIDList...)
		}

		// 判断是否已经获取了所有数据
		if pagination == nil || pagination.PageNum*pagination.PageSize >= pagination.Total {
			break
		}

		// 下一页
		pageNum++
	}

	if len(allWorkItemIDs) == 0 {
		return workItems, nil
	}

	// 获取项目信息，用于字段规范化处理
	project, err := getProjectBySimpleName(ctx, client, userKey, projectSimpleName)
	if err != nil {
		run.GetLogger().Errorf("获取项目信息失败 %s, err: %v", projectSimpleName, err)
		return nil, err
	}

	// 获取迭代信息，用于字段规范化处理
	sprintIdMap := make(map[string]string)
	sprints, _, err := client.ListWorkItems(ctx, userKey, projectSimpleName, &meegosdk.ListWorkItemsRequest{
		WorkItemTypeKeys: []string{"sprint"},
		PageSize:         100,
		PageNum:          1,
	})
	if err != nil {
		run.GetLogger().Warnf("获取项目迭代失败 %s, err: %v", projectSimpleName, err)
	} else {
		for _, sprint := range sprints {
			sprintIdMap[strconv.FormatInt(sprint.ID, 10)] = sprint.Name
		}
	}

	templateMap := make(map[string]*meegosdk.Template)
	templates, err := client.ListTemplates(ctx, userKey, project.ProjectKey, workItemType)
	if err != nil {
		run.GetLogger().Warnf("list templates failed for project %s, err: %v", project.ProjectKey, err)
	} else {
		for _, template := range templates {
			templateMap[template.TemplateID] = template
		}
	}

	// Get role mappings for the specific work item type once
	allTypeRoleMap := getAllTypeRoleMapByTypes(ctx, client, userKey, project.ProjectKey, []string{workItemType})

	// 每50个ID一组批量获取工作项详情
	batchSize := 100
	for i := 0; i < len(allWorkItemIDs); i += batchSize {
		end := i + batchSize
		if end > len(allWorkItemIDs) {
			end = len(allWorkItemIDs)
		}
		batchIDs := allWorkItemIDs[i:end]
		batchItems, _, err := client.ListWorkItems(ctx, userKey, projectSimpleName, &meegosdk.ListWorkItemsRequest{
			WorkItemTypeKeys: []string{workItemType},
			WorkItemIDs:      batchIDs,
			Expand: meegosdk.ExPand{
				NeedUserDetail:       true,
				NeedMultiText:        true,
				RelationFieldsDetail: true,
				NeedWorkflow:         workItemType == "story",
			},
		})
		if err != nil {
			run.GetLogger().Errorf("获取工作项详情失败, err: %v", err)
			return nil, err
		}

		// 规范化工作项字段
		if len(batchItems) > 0 {
			err = normalizeWorkItemFields(ctx, batchItems, &NormalizeWorkItemFieldsParams{
				BusinessIdMap:    businessIdMap,
				AllTypeFieldsMap: fieldsMap,
				SprintMap:        sprintIdMap,
				Project:          project,
				TemplateMap:      templateMap,
				AllTypeRoleMap:   allTypeRoleMap,
			})
			if err != nil {
				run.GetLogger().Warnf("规范化工作项字段失败，err: %v", err)
			}
		}

		workItems = append(workItems, batchItems...)
	}

	return workItems, nil
}

// listByMultiProjectView 处理全景视图，获取工作项列表
func listByMultiProjectView(ctx context.Context, run *iris.AgentRunContext, client meegosdk.Api, userKey, projectSimpleName, viewId string, quickFilterID string) ([]*meegosdk.WorkItemInfo, error) {
	var allWorkItems []*meegosdk.WorkItemInfo

	// 全景视图处理逻辑
	run.GetLogger().Infof("处理全景视图，项目名称: %s, 视图ID: %s", projectSimpleName, viewId)

	// 全景视图特有的API调用获取工作项列表
	// 使用分页循环获取所有工作项
	pageSize := 50
	pageNum := 1

	for {
		// 使用GetMultiProjectView方法获取全景视图工作项
		workItems, pagination, err := client.GetMultiProjectView(ctx, userKey, projectSimpleName, viewId, &meegosdk.GetViewRequest{
			PageSize:      pageSize,
			PageNum:       pageNum,
			QuickFilterID: quickFilterID,
			Expand: &meegosdk.ExPand{
				NeedMultiText:        true,
				RelationFieldsDetail: true,
				NeedUserDetail:       true,
				//NeedWorkflow:         true,
			},
		})
		if err != nil {
			run.GetLogger().Errorf("获取多项目视图工作项失败, err: %v", err)
			return nil, err
		}

		// 添加到结果列表中
		allWorkItems = append(allWorkItems, workItems...)

		// 判断是否已经获取了所有数据
		if pagination == nil || pagination.PageNum*pagination.PageSize >= pagination.Total {
			break
		}

		// 下一页
		pageNum++
	}

	// 如果没有找到工作项，返回空列表
	if len(allWorkItems) == 0 {
		return allWorkItems, nil
	}

	// 跟踪唯一的项目键，用于后续获取字段和业务线信息
	uniqueProjectKeys := make(map[string]bool)

	// 收集所有工作项的项目键
	for _, item := range allWorkItems {
		if item.ProjectKey != "" {
			uniqueProjectKeys[item.ProjectKey] = true
		}
	}

	// 获取所有相关项目的详细信息
	projectMap := make(map[string]*meegosdk.Project)
	if len(uniqueProjectKeys) > 0 {
		projects, err := client.ListProjects(ctx, &meegosdk.ListProjectsRequest{
			UserKey:     userKey,
			ProjectKeys: lo.Keys(uniqueProjectKeys),
		})
		if err != nil {
			run.GetLogger().Warnf("获取项目列表失败, err: %v", err)
		} else {
			for _, p := range projects {
				projectMap[p.ProjectKey] = p
			}
		}
	}

	// 为每个项目获取字段定义和业务线数据
	projectFieldMap := make(map[string]map[string]map[string]*meegosdk.SimpleField)
	projectBusinessMap := make(map[string]map[string]string)
	projectSprintMap := make(map[string]map[string]string)
	projectTemplateMap := make(map[string]map[string]*meegosdk.Template)
	projectRoleMap := make(map[string]map[string]map[string]string)

	// 按项目分组工作项，同时收集每个项目的工作项类型
	projectWorkItemsMap := make(map[string][]*meegosdk.WorkItemInfo)
	projectWorkItemTypes := make(map[string]map[string]bool)
	for _, item := range allWorkItems {
		if item.ProjectKey != "" {
			projectWorkItemsMap[item.ProjectKey] = append(projectWorkItemsMap[item.ProjectKey], item)

			// 收集该项目的工作项类型
			if projectWorkItemTypes[item.ProjectKey] == nil {
				projectWorkItemTypes[item.ProjectKey] = make(map[string]bool)
			}
			projectWorkItemTypes[item.ProjectKey][item.WorkItemTypeKey] = true
		}
	}

	// 处理每个项目
	for projectKey := range uniqueProjectKeys {
		// 获取项目的字段定义
		fieldsMap, err := GetProjectFieldsMap(ctx, client, userKey, projectKey)
		if err != nil {
			run.GetLogger().Warnf("获取项目字段定义失败 %s, err: %v", projectKey, err)
			continue
		}
		projectFieldMap[projectKey] = fieldsMap

		// 获取项目的业务线数据
		businessMap, err := getBusinessMap(ctx, client, userKey, projectKey)
		if err != nil {
			run.GetLogger().Warnf("获取项目业务线失败 %s, err: %v", projectKey, err)
			continue
		}
		projectBusinessMap[projectKey] = businessMap

		// 获取项目的迭代信息
		sprintMap := make(map[string]string)
		sprints, _, err := client.ListWorkItems(ctx, userKey, projectKey, &meegosdk.ListWorkItemsRequest{
			WorkItemTypeKeys: []string{"sprint"},
			PageSize:         100,
			PageNum:          1,
		})
		if err != nil {
			run.GetLogger().Warnf("获取项目迭代失败 %s, err: %v", projectKey, err)
		} else {
			for _, sprint := range sprints {
				sprintMap[strconv.FormatInt(sprint.ID, 10)] = sprint.Name
			}
			projectSprintMap[projectKey] = sprintMap
		}

		// 为该项目获取角色映射
		if workItemTypes, exists := projectWorkItemTypes[projectKey]; exists {
			workItemTypesList := lo.Keys(workItemTypes)
			roleMap := getAllTypeRoleMapByTypes(ctx, client, userKey, projectKey, workItemTypesList)
			projectRoleMap[projectKey] = roleMap
		}

		templateMap := make(map[string]*meegosdk.Template)
		projectTemplateMap[projectKey] = templateMap
	}

	// 处理每个项目的工作项
	var normalizedItems []*meegosdk.WorkItemInfo
	for projectKey, items := range projectWorkItemsMap {
		businessIdMap := projectBusinessMap[projectKey]
		sprintIdMap := projectSprintMap[projectKey]
		fieldsMap := projectFieldMap[projectKey]
		currentProject := projectMap[projectKey]

		if currentProject == nil || len(fieldsMap) == 0 {
			run.GetLogger().Warnf("项目 %s 的信息不完整，跳过处理", projectKey)
			continue
		}

		templateMap := projectTemplateMap[projectKey]
		for _, item := range items {
			if templateMap[strconv.FormatInt(item.TemplateID, 10)] == nil && item.TemplateID != 0 {
				templates, err := client.ListTemplates(ctx, userKey, projectKey, item.WorkItemTypeKey)
				if err != nil {
					run.GetLogger().Warnf("list templates failed for project %s, err: %v", projectKey, err)
				} else {
					for _, template := range templates {
						templateMap[template.TemplateID] = template
					}
				}
			}
		}

		err := normalizeWorkItemFields(ctx, items, &NormalizeWorkItemFieldsParams{
			BusinessIdMap:    businessIdMap,
			AllTypeFieldsMap: fieldsMap,
			SprintMap:        sprintIdMap,
			Project:          currentProject,
			TemplateMap:      projectTemplateMap[projectKey],
			AllTypeRoleMap:   projectRoleMap[projectKey],
		})
		if err != nil {
			run.GetLogger().Warnf("规范化工作项字段失败，项目: %s, err: %v", projectKey, err)
		}
		normalizedItems = append(normalizedItems, items...)
	}

	return normalizedItems, nil
}

// getBusinessMap 获取业务线信息并生成ID到名称的映射
func getBusinessMap(ctx context.Context, client meegosdk.Api, userKey, projectKey string) (map[string]string, error) {
	businessIdMap := make(map[string]string)

	// 获取业务线信息
	business, err := client.ListBusiness(ctx, userKey, projectKey)
	if err != nil {
		return businessIdMap, err
	}

	// 递归填充业务线映射
	var populateBusinessMap func(businesses []*meegosdk.Business, prefix string)
	populateBusinessMap = func(businesses []*meegosdk.Business, prefix string) {
		for _, b := range businesses {
			businessIdMap[b.ID] = prefix + b.Name
			if len(b.Children) > 0 {
				populateBusinessMap(b.Children, b.Name+"/")
			}
		}
	}
	populateBusinessMap(business, "")

	return businessIdMap, nil
}

// loadCookiesFromSSO loads cookies from SSO directory
// Priority: meego related -> feishu/lark related -> newest file
func loadCookiesFromSSO(run *iris.AgentRunContext) ([]CookieInfo, bool) {
	logger := run.GetLogger()
	ssoDir := "/tmp/sso"

	logger.Infof("[work_item_by_view] Loading cookies from SSO directory: %s", ssoDir)

	// Check if SSO directory exists
	if _, err := os.Stat(ssoDir); os.IsNotExist(err) {
		logger.Warnf("[work_item_by_view] SSO directory does not exist: %s", ssoDir)
		return nil, false
	}

	// Get all JSON files
	pattern := filepath.Join(ssoDir, "*.json")
	ssoFiles, err := filepath.Glob(pattern)
	if err != nil {
		logger.Errorf("[work_item_by_view] Error reading SSO directory: %v", err)
		return nil, false
	}

	if len(ssoFiles) == 0 {
		logger.Warn("[work_item_by_view] No SSO files found")
		return nil, false
	}

	logger.Infof("[work_item_by_view] Found %d SSO files", len(ssoFiles))

	var selectedFile string

	// 1. Look for meego related files
	var meegoFiles []string
	for _, file := range ssoFiles {
		basename := strings.ToLower(filepath.Base(file))
		if strings.Contains(basename, "meego") {
			meegoFiles = append(meegoFiles, file)
		}
	}

	if len(meegoFiles) > 0 {
		// Select the newest meego file
		sort.Slice(meegoFiles, func(i, j int) bool {
			stat1, _ := os.Stat(meegoFiles[i])
			stat2, _ := os.Stat(meegoFiles[j])
			return stat1.ModTime().After(stat2.ModTime())
		})
		selectedFile = meegoFiles[0]
		logger.Infof("[work_item_by_view] Found meego SSO file: %s", filepath.Base(selectedFile))
	} else {
		// 2. Look for lark/feishu related files
		var larkFiles []string
		for _, file := range ssoFiles {
			basename := strings.ToLower(filepath.Base(file))
			if strings.Contains(basename, "lark") || strings.Contains(basename, "feishu") {
				larkFiles = append(larkFiles, file)
			}
		}

		if len(larkFiles) > 0 {
			// Select the newest lark file
			sort.Slice(larkFiles, func(i, j int) bool {
				stat1, _ := os.Stat(larkFiles[i])
				stat2, _ := os.Stat(larkFiles[j])
				return stat1.ModTime().After(stat2.ModTime())
			})
			selectedFile = larkFiles[0]
			logger.Infof("[work_item_by_view] Found lark SSO file: %s", filepath.Base(selectedFile))
		} else {
			// 3. Select the newest file
			sort.Slice(ssoFiles, func(i, j int) bool {
				stat1, _ := os.Stat(ssoFiles[i])
				stat2, _ := os.Stat(ssoFiles[j])
				return stat1.ModTime().After(stat2.ModTime())
			})
			selectedFile = ssoFiles[0]
			logger.Infof("[work_item_by_view] Using newest SSO file: %s", filepath.Base(selectedFile))
		}
	}

	// Load the selected SSO file
	data, err := ioutil.ReadFile(selectedFile)
	if err != nil {
		logger.Errorf("[work_item_by_view] Error loading SSO file %s: %v", selectedFile, err)
		return nil, false
	}

	var cookiesData []CookieInfo
	if err := json.Unmarshal(data, &cookiesData); err != nil {
		logger.Errorf("[work_item_by_view] Error parsing SSO file %s: %v", selectedFile, err)
		return nil, false
	}

	logger.Infof("[work_item_by_view] Loaded cookies from: %s", filepath.Base(selectedFile))

	// Filter cookies for .larkoffice.com domain and check expiry
	now := time.Now().Unix()
	var validCookies []CookieInfo
	var meegoCSRFFound bool

	for _, cookie := range cookiesData {
		// Only process cookies for .larkoffice.com domain
		if cookie.Domain != ".larkoffice.com" {
			continue
		}

		// Check if cookie is expired
		if cookie.Expires > 0 && int64(cookie.Expires) < now {
			logger.Infof("[work_item_by_view] Cookie %s is expired, expires: %f, current: %d", cookie.Name, cookie.Expires, now)
			continue
		}

		validCookies = append(validCookies, cookie)

		if cookie.Name == "meego_csrf_token" {
			meegoCSRFFound = true
			logger.Info("[work_item_by_view] Found existing meego_csrf_token in SSO cookies")
		}
	}

	if len(validCookies) == 0 {
		logger.Info("[work_item_by_view] No valid .larkoffice.com cookies found in SSO files")
		return nil, false
	}

	logger.Infof("[work_item_by_view] Loaded %d valid cookies from SSO", len(validCookies))
	if meegoCSRFFound {
		logger.Info("[work_item_by_view] meego_csrf_token found in SSO cookies")
	}

	return validCookies, true
}

// isLarkOfficeCookieValid checks if the larkoffice.com cookie is valid by calling getViewMode to verify actual validity
func isLarkOfficeCookieValid(run *iris.AgentRunContext, viewID string) ([]CookieInfo, bool) {
	logger := run.GetLogger()

	var candidateCookies []CookieInfo
	var cookieSource string

	// First, try to load cookies from SSO directory
	if cookies, found := loadCookiesFromSSO(run); found {
		logger.Info("[work_item_by_view] Successfully loaded cookies from SSO directory")
		candidateCookies = cookies
		cookieSource = "SSO"
	} else {
		// Get JWT token from environment
		jwtToken := run.GetEnv(entity.RuntimeEnvironUserCloudJWT)
		if jwtToken == "" {
			logger.Warn("[work_item_by_view] No JWT token found in environment, cannot check cookie validity")
			return nil, false
		}

		// Create HTTP request
		url := "https://strato-keystore.bytedance.net/api/v1/cookie"
		if env.GetCurrentVRegion() == env.VREGION_SINGAPORECENTRAL {
			url = "https://strato-keystore.byteintl.net/api/v1/cookie"
		}
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logger.Errorf("[work_item_by_view] Failed to create HTTP request: %v", err)
			return nil, false
		}

		req.Header.Set("x-jwt-token", jwtToken)

		// Make HTTP request
		client := &http.Client{
			Timeout: 10 * time.Second,
		}
		resp, err := client.Do(req)
		if err != nil {
			logger.Errorf("[work_item_by_view] Failed to call strato-keystore API: %v", err)
			return nil, false
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			logger.Errorf("[work_item_by_view] strato-keystore API returned non-200 status: %d", resp.StatusCode)
			return nil, false
		}

		// Parse response
		var cookieResp CookieResponse
		if err := json.NewDecoder(resp.Body).Decode(&cookieResp); err != nil {
			logger.Errorf("[work_item_by_view] Failed to decode cookie response: %v", err)
			return nil, false
		}

		if cookieResp.Code != 0 {
			logger.Errorf("[work_item_by_view] strato-keystore API returned error code: %d, message: %s", cookieResp.Code, cookieResp.Message)
			return nil, false
		}

		candidateCookies = cookieResp.Data
		cookieSource = "strato-keystore API"
	}

	// Find .larkoffice.com domain cookies and check if any are valid
	now := time.Now().Unix()
	var validCookies []CookieInfo
	for _, cookie := range candidateCookies {
		if cookie.Domain == ".larkoffice.com" {
			// Check if cookie is expired
			if cookie.Expires > 0 && int64(cookie.Expires) < now {
				logger.Infof("[work_item_by_view] .larkoffice.com cookie %s is expired, expires: %f, current: %d", cookie.Name, cookie.Expires, now)
				continue
			}
			// Found a valid .larkoffice.com cookie
			logger.Infof("[work_item_by_view] Found valid .larkoffice.com cookie: %s", cookie.Name)
			validCookies = append(validCookies, cookie)
		}
	}

	if len(validCookies) == 0 {
		logger.Infof("[work_item_by_view] No valid .larkoffice.com cookies found from %s", cookieSource)
		return nil, false
	}

	// If cookies exist, test their actual validity by calling getViewMode with the actual viewID
	logger.Infof("[work_item_by_view] Testing cookie validity by calling getViewMode with viewID: %s from %s", viewID, cookieSource)
	_, err := getViewMode(run, validCookies, viewID)
	if err != nil {
		logger.Warnf("[work_item_by_view] Cookie validation failed via getViewMode: %v", err)
		// Check if it's a 401 error indicating cookie expiry
		if strings.Contains(err.Error(), "401") || strings.Contains(err.Error(), "status 401") {
			logger.Info("[work_item_by_view] Cookie returned 401, indicating it's actually expired")
			return nil, false
		}
		// For other errors (like network issues), we still consider cookies valid
		// since the error might not be related to cookie validity
		logger.Infof("[work_item_by_view] Non-401 error from getViewMode, considering cookies valid: %v", err)
	} else {
		logger.Info("[work_item_by_view] Cookie validation successful via getViewMode")
	}

	return validCookies, true
}

// getViewMode 获取视图模式
func getViewMode(run *iris.AgentRunContext, validCookies []CookieInfo, viewID string) (string, error) {
	logger := run.GetLogger()

	// 记录和处理 CSRF token
	var meegoCSRFToken string
	var cookieStrings []string

	// 遍历 cookie，查找现有的 meego_csrf_token
	for _, cookie := range validCookies {
		if cookie.Name == "meego_csrf_token" {
			meegoCSRFToken = cookie.Value
			logger.Infof("[work_item_by_view] Found existing meego_csrf_token in cookies")
		}
		cookieStrings = append(cookieStrings, fmt.Sprintf("%s=%s", cookie.Name, cookie.Value))
	}

	// 如果没有找到 meego_csrf_token，生成一个新的
	if meegoCSRFToken == "" {
		meegoCSRFToken = generateCSRFToken()
		logger.Infof("[work_item_by_view] Generated new meego_csrf_token: %s", meegoCSRFToken)
		// 将新生成的 CSRF token 添加到 cookie 中
		cookieStrings = append(cookieStrings, fmt.Sprintf("meego_csrf_token=%s", meegoCSRFToken))
	}

	cookieHeader := strings.Join(cookieStrings, "; ")

	// 构建请求体
	reqBody := ViewModeRequest{
		ViewID: viewID,
	}
	reqBodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		logger.Errorf("[work_item_by_view] Failed to marshal request body: %v", err)
		return "", err
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", "https://meego.larkoffice.com/goapi/v5/search/general/get_tiny_rtc_config", bytes.NewBuffer(reqBodyBytes))
	if err != nil {
		logger.Errorf("[work_item_by_view] Failed to create HTTP request: %v", err)
		return "", err
	}

	// 设置请求头
	req.Header.Set("Cookie", cookieHeader)
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36")

	// 设置 x-meego-csrf-token 头部
	req.Header.Set("x-meego-csrf-token", meegoCSRFToken)

	// 发送请求
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		logger.Errorf("[work_item_by_view] Failed to call get_tiny_rtc_config API: %v", err)
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.Errorf("[work_item_by_view] get_tiny_rtc_config API returned non-200 status: %d", resp.StatusCode)
		return "", fmt.Errorf("API returned status %d", resp.StatusCode)
	}

	// 解析响应
	var viewModeResp ViewModeResponse
	if err := json.NewDecoder(resp.Body).Decode(&viewModeResp); err != nil {
		logger.Errorf("[work_item_by_view] Failed to decode view mode response: %v", err)
		return "", err
	}

	if viewModeResp.Code != 0 {
		logger.Errorf("[work_item_by_view] get_tiny_rtc_config API returned error code: %d, message: %s", viewModeResp.Code, viewModeResp.Msg)
		return "", fmt.Errorf("API returned error code %d: %s", viewModeResp.Code, viewModeResp.Msg)
	}

	logger.Infof("[work_item_by_view] Got view mode: %s for view ID: %s", viewModeResp.Data.CurrentViewMode, viewID)
	return viewModeResp.Data.CurrentViewMode, nil
}

// generateCSRFToken 生成新的CSRF token
func generateCSRFToken() string {
	// 生成类似 Python 代码的格式：16-4-4-4-12
	return generateRandomString(16) + "-" +
		generateRandomString(4) + "-" +
		generateRandomString(4) + "-" +
		generateRandomString(4) + "-" +
		generateRandomString(12)
}

// generateRandomString 生成指定长度的随机字符串
func generateRandomString(length int) string {
	const charset = "0123456789abcdef"
	b := make([]byte, length)
	for i := range b {
		randomByte := make([]byte, 1)
		rand.Read(randomByte)
		b[i] = charset[randomByte[0]%byte(len(charset))]
	}
	return string(b)
}

// handleChartViewMode 处理图表视图模式的URL
func handleChartViewMode(run *iris.AgentRunContext, viewURL string) (map[string]any, error) {
	// 获取browser client
	browserClient, err := browser.NewBrowserMCPClient(run)
	if err != nil {
		run.GetLogger().Errorf("failed to new a browser mcp service: %v", err)
		return map[string]any{
			"**RESULT**": "获取失败，无法初始化browser client，请使用浏览器访问 https://meego.larkoffice.com 重新登录授权后再试",
		}, nil
	}

	ctx, cancel := context.WithTimeout(run, 10*time.Minute)
	defer cancel()

	// First goto the URL
	gotoRequest := mcp.CallToolRequest{}
	gotoRequest.Params.Name = "browser_goto"
	gotoRequest.Params.Arguments = map[string]any{
		"url": viewURL,
	}
	_, err = browserClient.Client.CallTool(ctx, gotoRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to goto url: %v", err)
		return map[string]any{
			"**RESULT**": fmt.Sprintf("获取失败，无法访问URL: %v，请检查URL是否正确", err),
		}, nil
	}

	// Then call browser_get_meego_screenshot_and_data
	meegoRequest := mcp.CallToolRequest{}
	meegoRequest.Params.Name = "browser_get_meego_screenshot_and_data"
	region := iris.CurrentRegion()
	meegoRequest.Params.Arguments = map[string]any{
		"region": lo.Ternary(region == iris.RegionI18n, "i18n", "cn"),
	}
	_, err = browserClient.Client.CallTool(ctx, meegoRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to get meego data: %v", err)
		return map[string]any{
			"**RESULT**": fmt.Sprintf("获取失败，无法获取meego数据: %v", err),
		}, nil
	}

	return map[string]any{
		"**RESULT**": "已通过browser工具获取meego图表数据，下一步：查看工作区下载的Meego数据",
	}, nil
}

func handleTableViewMode(run *iris.AgentRunContext, viewURL string) (map[string]any, error) {
	logger := run.GetLogger()
	browserClient, err := browser.NewBrowserMCPClient(run)
	if err != nil {
		run.GetLogger().Errorf("failed to new a browser mcp service: %v", err)
		return map[string]any{
			"**RESULT**": "获取失败，无法初始化browser client，请使用浏览器访问 https://meego.larkoffice.com 重新登录授权后再试",
		}, nil
	}

	ctx, cancel := context.WithTimeout(run, 10*time.Minute)
	defer cancel()

	meegoRequest := mcp.CallToolRequest{}
	meegoRequest.Params.Name = "browser_get_meego_screenshot_and_data"
	region := iris.CurrentRegion()
	meegoRequest.Params.Arguments = map[string]any{
		"region": lo.Ternary(region == iris.RegionI18n, "i18n", "cn"),
		"mode":   "table",
		"url":    viewURL,
	}
	resp, err := browserClient.Client.CallTool(ctx, meegoRequest)
	if err != nil {
		run.GetLogger().Errorf("Failed to get meego data: %v", err)
		return map[string]any{
			"**RESULT**": fmt.Sprintf("获取失败，无法获取meego数据: %v", err),
		}, nil
	}

	texts := extractTextContent(resp.Content)
	if resp.IsError {
		logger.Errorf("browser tool returned error: %v", texts)
		return map[string]any{
			"**RESULT**": fmt.Sprintf("获取失败，无法获取meego数据: %v", texts),
		}, nil
	}

	var response map[string]any
	// now only get first elem
	err = json.Unmarshal([]byte(texts[0]), &response)
	if respErr, ok := response["error"]; ok {
		if errStr, ok := respErr.(string); ok {
			return map[string]any{
				"**RESULT**": fmt.Sprintf("获取失败，无法获取meego数据: %v", errStr),
			}, nil
		}
	}
	if filename, ok := response["filename"]; !ok {
		return map[string]any{
			"**RESULT**": fmt.Sprintf("获取失败，无法获取meege截图文件"),
		}, nil
	} else {
		return map[string]any{
			"**RESULT**": fmt.Sprintf("已通过browser工具获取meego图表截图 %s，下一步：你必须查看工作区下载的Meego截图并插入到最终的文档对应位置", filename),
		}, nil
	}
}

func extractTextContent(contents []mcp.Content) []string {
	result := make([]string, 0)
	for _, content := range contents {
		val := reflect.ValueOf(content)
		if val.FieldByName("Type").String() == "text" {
			result = append(result, val.FieldByName("Text").String())
		}
	}
	return result
}
