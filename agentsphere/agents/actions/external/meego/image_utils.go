package meego

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/minio/sha256-simd"

	"code.byted.org/devgpt/kiwis/port/meegosdk"
	"code.byted.org/gopkg/gopool"
)

// ImageDownloadResult 图片下载结果
type ImageDownloadResult struct {
	Index       int    `json:"index"`
	OriginalURL string `json:"original_url"`
	LocalPath   string `json:"local_path,omitempty"`
	FileSize    int64  `json:"file_size,omitempty"`
	Success     bool   `json:"success"`
	Error       string `json:"error,omitempty"`
}

// downloadWorkItemImages 下载工作项相关的所有图片
func downloadWorkItemImages(ctx context.Context, client meegosdk.Api, userKey string, projectKey, workItemType string, workItemId int64, uuids []string) ([]*ImageDownloadResult, error) {
	if len(uuids) == 0 {
		return nil, nil
	}

	// 创建工作项专用目录
	workItemDir := filepath.Join("downloads", "images", fmt.Sprintf("meego_workitem_%d", workItemId))

	var results []*ImageDownloadResult
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 并发下载（最多5个并发）
	pool := gopool.NewPool("download_images", 5, gopool.NewConfig())
	defer pool.Close()

	for index, uuid := range uuids {
		index := index
		uuid := uuid
		wg.Add(1)
		pool.CtxGo(ctx, func() {
			defer wg.Done()

			result := &ImageDownloadResult{
				Index:       index,
				OriginalURL: uuid,
			}

			// 生成文件名
			filename := generateImageFilename(index, uuid)
			localPath := filepath.Join(workItemDir, filename)
			bin, err := client.DownloadAsset(ctx, userKey, projectKey, workItemType, workItemId, uuid)
			if err != nil {
				result.Error = err.Error()
				result.Success = false
				return
			}
			// 下载图片
			if err := writeImageToLocal(bin, localPath); err != nil {
				result.Error = err.Error()
			} else {
				result.LocalPath = localPath
				result.Success = true

				// 获取文件信息
				if stat, err := os.Stat(localPath); err == nil {
					result.FileSize = stat.Size()
				}
			}

			mu.Lock()
			results = append(results, result)
			mu.Unlock()
		})
	}

	wg.Wait()
	return results, nil
}

// writeImageToLocal 下载图片到本地文件系统
func writeImageToLocal(bin []byte, localPath string) error {
	// 确保目录存在, 任何人能够读取
	if err := os.MkdirAll(filepath.Dir(localPath), 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	_, err = file.Write(bin)
	if err != nil {
		return err
	}
	return nil
}

// generateImageFilename 生成安全的图片文件名
func generateImageFilename(index int, imageURL string) string {
	// 生成时间戳
	timestamp := time.Now().Format("20060102_150405")

	// 生成URL哈希（用于唯一性）

	hash := fmt.Sprintf("%x", sha256.New().Sum([]byte(imageURL)))[:8]

	// 确定文件扩展名（默认为.png）
	ext := ".png"
	if strings.Contains(imageURL, ".jpg") || strings.Contains(imageURL, ".jpeg") {
		ext = ".jpg"
	} else if strings.Contains(imageURL, ".gif") {
		ext = ".gif"
	} else if strings.Contains(imageURL, ".webp") {
		ext = ".webp"
	}

	// 组合文件名
	return fmt.Sprintf("image_%d_%s_%s%s", index, timestamp, hash, ext)
}
