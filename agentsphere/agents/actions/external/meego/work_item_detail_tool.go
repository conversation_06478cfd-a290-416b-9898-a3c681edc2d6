package meego

import (
	"fmt"
	"regexp"
	"strconv"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/meegosdk"
)

type GetWorkItemDetailArgs struct {
	URL            string `json:"url" mapstructure:"url" description:"必填，Work item links, 必须满足格式https://meego.larkoffice.com/{simpleName}/{workItemType}/detail/{workItemId} 的格式，否则拒绝访问"`
	ResultFilePath string `json:"result_file_path,omitempty" mapstructure:"result_file_path" description:"必填，工作项查询结果的文件存储路径，将输出为Excel文件，例如：work_item_detail.xlsx。重点注意：如果文件名已存在，会覆盖原文件，尽量避免文件名重名！。"`
}

const (
	ToolGetWorkItemDetail            = "get_work_item_detail"
	ToolGetWorkItemDetailDescription = "get work item detail from meego work item link"
)

func NewGetWorkItemDetailByURL() iris.Action {
	return actions.ToTool(ToolGetWorkItemDetail, ToolGetWorkItemDetailDescription, GetWorkItemDetailByUrl)
}

func GetWorkItemDetailByUrl(ctx *iris.AgentRunContext, args GetWorkItemDetailArgs) (map[string]any, error) {
	// arg check
	if args.ResultFilePath == "" {
		ctx.GetLogger().Error("result file path is empty")
		return nil, iris.NewRecoverable(errors.New("result file path is empty"))
	}

	client, err := meegosdk.New()
	if err != nil {
		ctx.GetLogger().Error("create meego api client failed, err: %v", err)
		return nil, err
	}

	user, err := getCurrentUserInfo(ctx, GetUserInfoArgs{})
	if err != nil {
		ctx.GetLogger().Error("get user info failed, err: %v", err)
		return nil, err
	}

	// parse url
	simpleName, workItemType, workItemIdStr, err := extractWorkItemInfo(args.URL)
	if err != nil {
		ctx.GetLogger().Error("extract work item info failed, err: %v", err)
		return nil, err
	}
	project, err := getProjectBySimpleName(ctx, client, user.UserKey, simpleName)
	if err != nil {
		ctx.GetLogger().Error("get project by simple name failed, err: %v", err)
		return nil, err
	}

	// convert work item id to int64
	workItemId, err := strconv.ParseInt(workItemIdStr, 10, 64)
	if err != nil {
		ctx.GetLogger().Error("convert work item id to int64 failed, err: %v", err)
		return nil, err
	}
	workItemDetail, err := client.GetWorkItemDetail(ctx, user.UserKey, project.ProjectKey, workItemType,
		&meegosdk.GetWorkItemDetailRequest{
			WorkItemIDs: []int64{workItemId},
			Expand: &meegosdk.ExPand{
				NeedMultiText:        true,
				RelationFieldsDetail: true,
				NeedUserDetail:       true,
				NeedWorkflow:         lo.Ternary(workItemType == "story", true, false),
			},
		})
	if err != nil {
		ctx.GetLogger().Error("get work item detail failed, err: %v", err)
		return nil, err
	}

	// get work item fields schema
	fieldsMap, err := GetProjectFieldsMap(ctx, client, user.UserKey, project.ProjectKey)
	if err != nil {
		ctx.GetLogger().Error("get project fields map failed, err: %v", err)
		return nil, err
	}

	business, err := client.ListBusiness(ctx, user.UserKey, project.ProjectKey)
	if err != nil {
		ctx.GetLogger().Error("list business failed, err: %v", err)
		return nil, err
	}
	// Create business map for this project using the utility function
	businessIdMap := PopulateBusinessMap(business)

	sprintIdMap := make(map[string]string)
	sprints, _, err := client.ListWorkItems(ctx, user.UserKey, project.ProjectKey, &meegosdk.ListWorkItemsRequest{
		WorkItemTypeKeys: []string{"sprint"},
		PageSize:         100,
		PageNum:          1,
	})
	if err != nil {
		ctx.GetLogger().Errorf("list work items failed, err: %v", err)
		return nil, err
	}
	for _, sprint := range sprints {
		sprintIdMap[strconv.FormatInt(sprint.ID, 10)] = sprint.Name
	}

	templateMap := make(map[string]*meegosdk.Template)
	for _, itemType := range []string{"story", "issue"} {
		templates, err := client.ListTemplates(ctx, user.UserKey, project.ProjectKey, itemType)
		if err != nil {
			ctx.GetLogger().Warnf("list templates failed for project %s, err: %v", project.ProjectKey, err)
		} else {
			for _, template := range templates {
				templateMap[template.TemplateID] = template
			}
		}
	}

	// Get role mappings for the specific work item type once
	allTypeRoleMap := getAllTypeRoleMapByTypes(ctx, client, user.UserKey, project.ProjectKey, []string{workItemType})

	err = normalizeWorkItemFields(ctx, workItemDetail, &NormalizeWorkItemFieldsParams{
		BusinessIdMap:    businessIdMap,
		AllTypeFieldsMap: fieldsMap,
		SprintMap:        sprintIdMap,
		Project:          project,
		TemplateMap:      templateMap,
		AllTypeRoleMap:   allTypeRoleMap,
	})
	if err != nil {
		ctx.GetLogger().Error("normalize work item fields failed, err: %v", err)
		return nil, err
	}

	// 提取图片URL并下载
	imageUUids := extractImageUUIDsFromMultiTexts(workItemDetail)
	var downloadResults []*ImageDownloadResult
	if len(imageUUids) > 0 {
		ctx.GetLogger().Info("Found %d images in work item %d, starting download", len(imageUUids), workItemId)
		downloadResults, err = downloadWorkItemImages(ctx, client, user.UserKey, project.ProjectKey, workItemType, workItemId, imageUUids)
		if err != nil {
			ctx.GetLogger().Warn("Failed to download images: %v", err)
			// 不影响主流程，继续执行
		}
	}

	_, err = CreateWorkItemsExcel(ctx, args.ResultFilePath, project.Name, workItemType, workItemDetail)
	if err != nil {
		ctx.GetLogger().Error("create work items excel failed, err: %v", err)
		return nil, err
	}

	// 构建返回结果
	result := map[string]any{
		"output": args.ResultFilePath,
	}

	// 添加图片下载信息
	if len(imageUUids) > 0 {
		result["image_urls"] = imageUUids
		result["image_count"] = len(imageUUids)

		if len(downloadResults) > 0 {
			successfulDownloads := make([]map[string]any, 0)
			failedDownloads := make([]map[string]any, 0)

			for _, img := range downloadResults {
				imgInfo := map[string]any{
					"index":        img.Index,
					"original_url": img.OriginalURL,
				}

				if img.Success {
					imgInfo["local_path"] = img.LocalPath
					imgInfo["file_size"] = img.FileSize
					successfulDownloads = append(successfulDownloads, imgInfo)
				} else {
					imgInfo["error"] = img.Error
					failedDownloads = append(failedDownloads, imgInfo)
				}
			}

			result["downloaded_images"] = successfulDownloads
			result["downloaded_count"] = len(successfulDownloads)
			if len(failedDownloads) > 0 {
				result["failed_downloads"] = failedDownloads
				result["failed_count"] = len(failedDownloads)
			}
		}
	}

	return result, nil
}

var re = regexp.MustCompile(`https://meego\.larkoffice\.com/([^/]+)/([^/]+)/detail/(\d+)`)

func extractWorkItemInfo(url string) (simpleName, workItemType string, workItemID string, err error) {
	matches := re.FindStringSubmatch(url)

	if len(matches) != 4 {
		return "", "", "", fmt.Errorf("invalid URL format")
	}

	return matches[1], matches[2], matches[3], nil
}
