package meego

import (
	"context"
	_ "embed" // 导入embed包
	"encoding/csv"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	json "github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"

	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/meegosdk"
)

//go:embed devmind_meego_cleaned.csv
var csvData string

type roleOwners struct {
	Owners []string `json:"owners"`
	Role   string   `json:"role"`
}

// MappingField represents the detailed mapping configuration within map_config arrays.
type MappingField struct {
	MappingFieldKey       string `json:"mapping_field_key"`
	MappingFieldName      string `json:"mapping_field_name"`
	MappingFieldType      string `json:"mapping_field_type"`
	MappingFieldAttribute string `json:"mapping_field_attribute,omitempty"` // Optional field
}

// TemplateConfig defines the structure for one configuration object within the template_config JSON array
type TemplateConfig struct {
	ConfigName          string                 `json:"config_name"`
	TemplateName        []string               `json:"template_name"`
	OSField             []string               `json:"os_field"`
	IsFill              bool                   `json:"is_fill"`
	PhaseTime           []PhaseTimeMapping     `json:"phase_time"`
	PhaseDuration       []PhaseDurationMapping `json:"phase_duration"`
	CustomPhaseDuration []CustomPhaseDuration  `json:"custom_phase_duration"`
	Role                []RoleMapping          `json:"role"`
}

// PhaseTimeMapping represents the phase_time JSON structure within TemplateConfig
type PhaseTimeMapping struct {
	PhaseKey    string                    `json:"phase_key"`
	PhaseName   string                    `json:"phase_name"`
	IsOS        bool                      `json:"is_os"`
	MapConfig   map[string][]MappingField `json:"map_config"`
	OSPhaseMath map[string]string         `json:"os_phase_math"`
	PhaseMath   string                    `json:"phase_math"`
	PhaseOver   string                    `json:"phase_over,omitempty"` // Added optional field based on example
}

// PhaseDurationMapping represents the phase_duration JSON structure within TemplateConfig
type PhaseDurationMapping struct {
	DurationKey  string `json:"duration_key"`
	DurationName string `json:"duration_name"`
	Start        string `json:"start"`
	End          string `json:"end"`
}

// CustomPhaseDuration represents the custom_phase_duration JSON structure within TemplateConfig
type CustomPhaseDuration struct {
	DurationKey  string `json:"duration_key"`
	DurationName string `json:"duration_name"`
	Start        string `json:"start"`
	End          string `json:"end"`
}

// RoleMapping represents the role JSON structure within TemplateConfig
type RoleMapping struct {
	RoleType  string                    `json:"role_type"`
	IsOS      bool                      `json:"is_os"`
	RoleName  string                    `json:"role_name"`
	MapConfig map[string][]MappingField `json:"map_config"`
	IsDefault bool                      `json:"is_default,omitempty"` // Added optional field based on example
}

// Global variable to store the template configurations
var (
	templateConfigs map[string][]TemplateConfig // Map key: space_id, value: slice of configs
	mappingOnce     sync.Once
	loadErr         error // Store potential loading error
)

// LoadTemplateConfigs loads the template configurations from CSV file into memory
func LoadTemplateConfigs(ctx context.Context) (map[string][]TemplateConfig, error) {
	mappingOnce.Do(func() {
		templateConfigs = make(map[string][]TemplateConfig)

		reader := csv.NewReader(strings.NewReader(csvData))
		reader.LazyQuotes = true   // Handle quotes more flexibly
		reader.FieldsPerRecord = 2 // Expect exactly 2 fields: space_id, template_config

		// Read CSV header
		_, err := reader.Read()
		if err != nil {
			logs.CtxError(ctx, "failed to read CSV header: %v", err)
			loadErr = err
			return
		}

		// Read CSV records
		records, err := reader.ReadAll()
		if err != nil {
			logs.CtxError(ctx, "failed to read CSV records: %v", err)
			loadErr = err
			return
		}

		// Process each record
		for i, record := range records {
			if len(record) != 2 {
				logs.CtxWarn(ctx, "Skipping record %d with incorrect number of fields (%d): %v", i+2, len(record), record) // +2 for header and 1-based index
				continue
			}

			spaceID := strings.TrimSpace(record[0])
			jsonData := strings.TrimSpace(record[1])

			if spaceID == "" {
				logs.CtxWarn(ctx, "Skipping record %d with empty space_id", i+2)
				continue
			}
			if jsonData == "" {
				logs.CtxWarn(ctx, "Skipping record %d for space_id '%s' due to empty template_config", i+2, spaceID)
				continue
			}

			var configs []TemplateConfig
			if err := json.Unmarshal([]byte(jsonData), &configs); err != nil {
				logs.CtxWarn(ctx, "failed to parse template_config JSON for space_id '%s' (record %d): %v.", spaceID, i+2, err)
				continue
			}

			templateConfigs[spaceID] = configs // Store the slice of configs
		}

		logs.CtxInfo(ctx, "Loaded %d space template configurations from embedded CSV", len(templateConfigs))
	})

	return templateConfigs, loadErr
}

// GetTemplateConfigs returns the template configurations for a specific space ID
func GetTemplateConfigs(ctx context.Context, spaceID string) ([]TemplateConfig, error) {
	configsMap, err := LoadTemplateConfigs(ctx) // Ensure configs are loaded
	if err != nil {
		return nil, fmt.Errorf("error loading template configs: %w", err)
	}
	if configsMap == nil {
		return nil, fmt.Errorf("template configurations map is not initialized")
	}

	configs, exists := configsMap[spaceID]
	if !exists {
		return nil, fmt.Errorf("no template configuration found for space ID: %s", spaceID)
	}
	if len(configs) == 0 {
		logs.CtxWarn(ctx, "No template configurations defined for space ID: %s", spaceID)
		return []TemplateConfig{}, nil
	}

	return configs, nil
}

// GetTemplateConfigByType returns a specific template configuration for a space ID and template type
func GetTemplateConfigByType(ctx context.Context, spaceID string, templateType string) (*TemplateConfig, error) {
	configs, err := GetTemplateConfigs(ctx, spaceID)
	if err != nil {
		return nil, err
	}

	for i := range configs {
		config := &configs[i]
		if lo.Contains(config.TemplateName, templateType) {
			return config, nil
		}
	}

	return nil, fmt.Errorf("no matching template configuration found for space ID: %s and template type: %s", spaceID, templateType)
}

func getProjectBySimpleName(ctx context.Context, client meegosdk.Api, userKey string, simpleName string) (*meegosdk.Project,
	error) {
	resp, err := client.ListProjects(ctx, &meegosdk.ListProjectsRequest{
		UserKey:     userKey,
		SimpleNames: []string{simpleName},
	})
	if err != nil {
		logs.CtxError(ctx, "list project keys failed, err: %v", err)
		return nil, err
	}
	projects := lo.Values(resp)
	if len(projects) == 0 {
		logs.CtxError(ctx, "project not found, simple name: %s", simpleName)
		return nil, errors.New("project not found")
	}
	return projects[0], nil
}

func getProjectWorkItemTypeMap(ctx context.Context, client meegosdk.Api, userKey string, projectKey string) (map[string]string, error) {
	workItemTypes, err := client.ListWorkItemAllTypes(ctx, userKey, projectKey)
	if err != nil {
		logs.CtxError(ctx, "list work item types failed, err: %v", err)
		return nil, err
	}
	workItemTypeMap := make(map[string]string)
	for _, t := range workItemTypes {
		if t.IsDisable != 1 {
			workItemTypeMap[t.TypeKey] = t.Name
		}
	}
	return workItemTypeMap, nil
}

func getProjectRoleMap(ctx context.Context, client meegosdk.Api, userKey string, projectKey string, workItemTypeKey string) (map[string]string, error) {
	roles, err := client.ListFlowRoles(ctx, userKey, projectKey, workItemTypeKey)
	if err != nil {
		logs.CtxError(ctx, "list flow roles failed, err: %v", err)
		return nil, err
	}
	roleMap := make(map[string]string)
	for _, role := range roles {
		roleMap[role.ID] = role.Name
	}
	return roleMap, nil
}

// getAllTypeRoleMapByTypes builds a complete role map for specified work item types
func getAllTypeRoleMapByTypes(ctx context.Context, client meegosdk.Api, userKey string, projectKey string, workItemTypes []string) map[string]map[string]string {
	allTypeRoleMap := make(map[string]map[string]string)

	// Get role map for each specified work item type
	for _, workItemType := range workItemTypes {
		roleMap, err := getProjectRoleMap(ctx, client, userKey, projectKey, workItemType)
		if err != nil {
			logs.CtxWarn(ctx, "failed to get role map for work item type %s, err: %v", workItemType, err)
			continue
		}
		if len(roleMap) > 0 {
			allTypeRoleMap[workItemType] = roleMap
		}
	}

	return allTypeRoleMap
}

// NormalizeWorkItemFieldsParams contains all parameters needed for normalizing work item fields
type NormalizeWorkItemFieldsParams struct {
	BusinessIdMap    map[string]string
	AllTypeFieldsMap map[string]map[string]*meegosdk.SimpleField
	SprintMap        map[string]string
	Project          *meegosdk.Project
	TemplateMap      map[string]*meegosdk.Template
	AllTypeRoleMap   map[string]map[string]string
}

func normalizeWorkItemFields(ctx context.Context, items []*meegosdk.WorkItemInfo, params *NormalizeWorkItemFieldsParams) error {
	const (
		ownerFieldKey    = "owner"
		businessFieldKey = "business"
		roleFieldKey     = "role_owners"
	)
	userKeyMap := make(map[string]string)

	// Build userKeyMap from UserDetails in each WorkItemInfo
	for _, item := range items {
		if item.UserDetails != nil {
			for _, userDetail := range item.UserDetails {
				if userDetail.UserKey != "" && userDetail.NameCN != "" {
					userKeyMap[userDetail.UserKey] = userDetail.NameCN
				}
			}
		}
	}

	// replace user key with username
	for _, item := range items {
		fieldsMap := params.AllTypeFieldsMap[item.WorkItemTypeKey]
		if fieldsMap == nil {
			logs.CtxWarn(ctx, "work item type key not found in fields map, work item type key: %s", item.WorkItemTypeKey)
			continue
		}
		if item.FieldsMap == nil {
			item.FieldsMap = make(map[string]any)
		}
		item.RefURL = fmt.Sprintf("https://meego.larkoffice.com/%s/%s/detail/%d", item.SimpleName, item.WorkItemTypeKey, item.ID)
		if params.Project != nil {
			item.ProjectName = params.Project.Name
		}

		// 替换template
		if item.TemplateID != 0 {
			if params.TemplateMap[strconv.FormatInt(item.TemplateID, 10)] != nil {
				item.TemplateType = params.TemplateMap[strconv.FormatInt(item.TemplateID, 10)].TemplateName
			}
		}

		// 使用反射处理item的所有字段
		addItemFieldsToMap(ctx, item, fieldsMap, params.TemplateMap)

		if item.WorkflowInfos != nil {
			var point float64
			for i, node := range item.WorkflowInfos.WorkflowNodes {
				if node.NodeSchedule != nil && node.NodeSchedule.Points > 0 {
					point += node.NodeSchedule.Points
				}
				item.WorkflowInfos.WorkflowNodes[i].Schedules = nil // 差异化人员排期先忽略
			}
			item.FieldsMap["工作量预估"] = strconv.FormatFloat(point, 'f', 1, 64)
		}

		for _, field := range item.Fields {
			key := strings.ToLower(strings.TrimSpace(field.FieldKey))
			if key == ownerFieldKey {
				if field.FieldValue == nil {
					continue
				}
				item.FieldsMap["负责人"] = field.FieldValue
			}

			if key == businessFieldKey {
				if field.FieldValue == nil {
					continue
				}
				// convert field.FieldValue to string
				businessId, ok := field.FieldValue.(string)
				if !ok {
					logs.CtxWarn(ctx, "failed to convert field.FieldValue to string")
					continue
				}
				if params.BusinessIdMap[businessId] != "" {
					field.FieldValue = params.BusinessIdMap[businessId]
					item.FieldsMap["业务线"] = field.FieldValue
				}
			}
			if key == roleFieldKey {
				if field.FieldValue == nil {
					continue
				}
				jsonData, err := json.Marshal(field.FieldValue) // 序列化
				if err != nil {
					logs.CtxWarn(ctx, "failed to marshal field.FieldValue, err: %v", err)
					continue
				}

				var roleOwnersList []*roleOwners
				err = json.Unmarshal(jsonData, &roleOwnersList) // 反序列化
				if err != nil {
					logs.CtxWarn(ctx, "failed to unmarshal field.FieldValue to []*roleOwners, err: %v", err)
					continue
				}

				roleMap := params.AllTypeRoleMap[item.WorkItemTypeKey]
				for _, roleOwner := range roleOwnersList {
					if roleMap != nil && roleMap[roleOwner.Role] != "" {
						roleOwner.Role = roleMap[roleOwner.Role]
					}
				}
				item.FieldsMap["角色负责人列表"] = roleOwnersList
			}

			// other fields, normalize by fields schema
			if fieldsMap[field.FieldKey] != nil && field.FieldValue != nil {
				if (reflect.ValueOf(field.FieldValue).IsZero() && reflect.ValueOf(field.FieldValue).Kind() != reflect.Bool) ||
					(reflect.ValueOf(field.FieldValue).Kind() == reflect.Slice && reflect.ValueOf(field.FieldValue).Len() == 0) {
					continue
				}
				simpleField := fieldsMap[field.FieldKey]
				// options 转换
				if simpleField.FieldTypeKey != businessFieldKey {
					field.FieldValue = convertFieldOptions(ctx, field.FieldValue, simpleField, params.TemplateMap)
				}
				item.FieldsMap[simpleField.FieldName] = field.FieldValue
			}
		}

		for _, relationField := range item.RelationFieldsDetail {
			if _, ok := fieldsMap[relationField.FieldKey]; ok {
				relationField.FieldName = fieldsMap[relationField.FieldKey].FieldName
				relationField.WorkItemTypeKeys = lo.Uniq(lo.Map(relationField.Detail, func(t *meegosdk.RelationFieldDetail, _ int) string {
					return t.WorkItemTypeKey
				}))
				relationField.Detail = nil
			}
		}

		calculateDevMindFields(ctx, item)
	}

	for _, item := range items {
		item.Fields = nil
		itemStr, _ := json.MarshalString(item)
		for k, v := range userKeyMap {
			itemStr = strings.ReplaceAll(itemStr, k, v)
		}
		for k, v := range params.SprintMap {
			itemStr = strings.ReplaceAll(itemStr, k, "\""+v+"\"")
		}
		err := json.UnmarshalString(itemStr, item)
		if err != nil {
			logs.CtxWarn(ctx, "failed to unmarshal itemStr, err: %v", err)
		}
	}
	return nil
}

// convertFieldOptions 处理字段选项值的转换
func convertFieldOptions(ctx context.Context, fieldValue interface{}, simpleField *meegosdk.SimpleField, templateMap map[string]*meegosdk.Template) interface{} {
	jsonData, err := json.Marshal(fieldValue) // 序列化
	if err != nil {
		logs.CtxWarn(ctx, "failed to marshal field.FieldValue, err: %v", err)
		return fieldValue
	}

	switch simpleField.FieldTypeKey {
	case "multi_select", "tree_multi_select":
		fieldValue := make([]meegosdk.Option, 0)
		err = json.Unmarshal(jsonData, &fieldValue)
		if err != nil {
			logs.CtxWarn(ctx, "failed to unmarshal field.FieldValue to []Option, err: %v", err)
			return fieldValue
		}
		fieldValueStr := make([]string, 0)

		// 如果是tree_multi_select，需要递归处理每个选项
		if simpleField.FieldTypeKey == "tree_multi_select" {
			// 定义递归函数来处理所有层级的children
			var processOptions func(options *meegosdk.Option, result *[]string)
			processOptions = func(options *meegosdk.Option, result *[]string) {
				if options == nil {
					return
				}
				*result = append(*result, options.Label)
				// 如果有子选项，递归处理
				c := new(meegosdk.Option)
				jsonChild, err := json.Marshal(options.Children)
				err = json.Unmarshal(jsonChild, &c)
				if err == nil {
					processOptions(c, result)
				}
			}

			// 对每个选项进行递归处理
			for _, value := range fieldValue {
				var singleOptionLabels []string
				processOptions(&value, &singleOptionLabels)
				fieldValueStr = append(fieldValueStr, singleOptionLabels...)
			}
		} else {
			// 普通multi_select的处理逻辑
			for _, value := range fieldValue {
				for _, option := range simpleField.Options {
					if option.Value == value.Value {
						fieldValueStr = append(fieldValueStr, option.Label)
						break
					}
				}
			}
		}
		return fieldValueStr
	case "select", "radio":
		// 反序列化
		switch fieldValue.(type) {
		case string:
			for _, option := range simpleField.Options {
				if option.Value == fieldValue.(string) {
					return option.Label
				}
			}
			return fieldValue.(string)
		default:
			type Option struct {
				Value string `json:"value"`
				Label string `json:"label"`
			}
			var o Option
			err = json.Unmarshal(jsonData, &o)
			if err != nil {
				logs.CtxWarn(ctx, "failed to unmarshal field.FieldValue to Option, err: %v", err)
				return fieldValue
			}
			return o.Label
		}
	case "work_item_template":
		type Template struct {
			ID      int64 `json:"id"`
			Version int64 `json:"version"`
		}
		var t Template
		err = json.Unmarshal(jsonData, &t)
		if err != nil {
			logs.CtxWarn(ctx, "failed to unmarshal field.FieldValue to Template, err: %v", err)
			return fieldValue
		}
		if templateMap[strconv.FormatInt(t.ID, 10)] != nil {
			return templateMap[strconv.FormatInt(t.ID, 10)].TemplateName
		}
	case "sub_stage":
		for _, option := range simpleField.Options {
			if option.Value == fieldValue.(string) {
				return option.Label
			}
		}
	case "date":
		// Convert timestamp (milliseconds since epoch) to YYYY-MM-DD format
		if fieldValue != nil {
			var timestamp int64
			switch v := fieldValue.(type) {
			case float64:
				timestamp = int64(v)
			case int64:
				timestamp = v
			case string:
				// Try to parse string as int64
				if ts, err := strconv.ParseInt(v, 10, 64); err == nil {
					timestamp = ts
				}
			}

			if timestamp > 0 {
				// Convert milliseconds to seconds if needed
				t := time.UnixMilli(timestamp)
				return t.Format("2006-01-02")
			}
		}
		return fieldValue
	case "work_item_status":
		var t = &meegosdk.WorkItemStatus{}
		err = json.Unmarshal(jsonData, &t)
		if err != nil {
			logs.CtxWarn(ctx, "failed to unmarshal field.FieldValue to Template, err: %v", err)
			return fieldValue
		}
		for _, option := range simpleField.Options {
			if option.Value == t.StateKey {
				return option.Label
			}
		}
	case "tree_select":
		v := new(meegosdk.Option)
		err = json.Unmarshal(jsonData, &v)
		if err != nil {
			logs.CtxWarn(ctx, "failed to unmarshal field.FieldValue to Option, err: %v", err)
			return fieldValue
		}
		fieldValueStr := make([]string, 0)
		// 定义递归函数来处理所有层级的children
		var processOptions func(options *meegosdk.Option)
		processOptions = func(options *meegosdk.Option) {
			if options == nil {
				return
			}
			fieldValueStr = append(fieldValueStr, options.Label)
			// 如果有子选项，递归处理
			c := new(meegosdk.Option)
			jsonChild, err := json.Marshal(options.Children)
			err = json.Unmarshal(jsonChild, &c)
			if err == nil {
				processOptions(c)
			}
		}
		// 启动递归处理
		processOptions(v)
		return fieldValueStr
	case "bool":
		switch fieldValue.(type) {
		case bool:
			return lo.Ternary(fieldValue.(bool), "是", "否")
		case string:
			return lo.Ternary(fieldValue.(string) == "true", "是", "否")
		}
	default:
		return fieldValue
	}
	return fieldValue
}

// PopulateBusinessMap creates a map of business IDs to business names from a list of Business objects
// The function handles nested business hierarchies and preserves the parent-child relationship in the name
func PopulateBusinessMap(businesses []*meegosdk.Business) map[string]string {
	businessIdMap := make(map[string]string)

	// recursively populate businessIdMap from the business tree
	var populateMap func(businesses []*meegosdk.Business, prefix string)
	populateMap = func(businesses []*meegosdk.Business, prefix string) {
		for _, b := range businesses {
			businessIdMap[b.ID] = prefix + b.Name
			if len(b.Children) > 0 {
				populateMap(b.Children, b.Name+"/")
			}
		}
	}

	// Populate the map
	populateMap(businesses, "")

	return businessIdMap
}

func calculateDevMindFields(ctx context.Context, item *meegosdk.WorkItemInfo) {
	if item == nil || item.WorkItemTypeKey != "story" || item.SimpleName == "" || item.TemplateType == "" {
		return
	}

	spaceID := item.SimpleName
	foundConfig, err := GetTemplateConfigByType(ctx, spaceID, item.TemplateType)
	if err != nil {
		logs.CtxError(ctx, "failed to get template config for space ID '%s' and template type '%s' during DevMind calculation: %v",
			spaceID, item.TemplateType, err)
		return
	}

	// Ensure FieldsMap is initialized
	if item.DevMindFields == nil {
		item.DevMindFields = make(map[string]any)
	}

	// 1. Build workflow nodes map for quick lookup
	workflowNodesMap := make(map[string]*meegosdk.WorkflowNode)
	if item.WorkflowInfos != nil && len(item.WorkflowInfos.WorkflowNodes) > 0 {
		for _, node := range item.WorkflowInfos.WorkflowNodes {
			workflowNodesMap[node.ID] = node
		}
	} else {
		return
	}

	// Calculate phase times
	phaseTimeMap := calculatePhaseTime(ctx, item, foundConfig, workflowNodesMap)

	// Process phase durations
	calculatePhaseDurations(ctx, item, foundConfig, phaseTimeMap)
}

// calculatePhaseTime processes PhaseTime mappings and calculates phase time values
func calculatePhaseTime(ctx context.Context, item *meegosdk.WorkItemInfo, config *TemplateConfig, workflowNodesMap map[string]*meegosdk.WorkflowNode) map[string]int64 {
	phaseTimeMap := make(map[string]int64)

	// Process each PhaseTime mapping
	for _, phaseTime := range config.PhaseTime {
		// Create containers for storing time values for calculations
		timeValues := make([]int64, 0)
		osTimeValues := make(map[string][]int64)

		// Track if any required node is missing
		nodesMissing := false

		// Process mapping fields for each phase
		for osType, mappingFields := range phaseTime.MapConfig {
			if phaseTime.IsOS && osTimeValues[osType] == nil {
				osTimeValues[osType] = make([]int64, 0)
			}

			// Process each mapping field
			for _, mappingField := range mappingFields {
				if mappingField.MappingFieldType == "node" {
					// Find the corresponding workflow node
					node, exists := workflowNodesMap[mappingField.MappingFieldKey]
					if !exists || node.Name != mappingField.MappingFieldName {
						nodesMissing = true
						continue
					}

					var timeValue int64
					// Extract time value based on mapping_field_attribute
					switch mappingField.MappingFieldAttribute {
					case "actual_start":
						if node.ActualBeginTime != "" {
							// Parse ISO8601 timestamp string to time.Time
							t, err := time.Parse(time.RFC3339, node.ActualBeginTime)
							if err != nil {
								logs.CtxWarn(ctx, "Failed to parse ISO8601 ActualBeginTime '%s' for node %s: %v",
									node.ActualBeginTime, node.ID, err)
								continue
							}
							timeValue = t.UnixMilli()
						}
					case "actual_end":
						if node.ActualFinishTime != "" {
							// Parse ISO8601 timestamp string to time.Time
							t, err := time.Parse(time.RFC3339, node.ActualFinishTime)
							if err != nil {
								logs.CtxWarn(ctx, "Failed to parse ISO8601 ActualFinishTime '%s' for node %s: %v",
									node.ActualFinishTime, node.ID, err)
								continue
							}
							timeValue = t.UnixMilli()
						}
					case "expect_start":
						if node.NodeSchedule != nil && node.NodeSchedule.EstimateStartDate > 0 {
							timeValue = node.NodeSchedule.EstimateStartDate
						}
					case "expect_end":
						if node.NodeSchedule != nil && node.NodeSchedule.EstimateEndDate > 0 {
							timeValue = node.NodeSchedule.EstimateEndDate
						}
					default:
						logs.CtxWarn(ctx, "Unknown mapping field attribute: %s", mappingField.MappingFieldAttribute)
						continue
					}

					// Skip if no time value found
					if timeValue == 0 {
						nodesMissing = true
						continue
					}

					// Store the time value based on whether we're dealing with OS-specific values
					if phaseTime.IsOS {
						osTimeValues[osType] = append(osTimeValues[osType], timeValue)
					} else {
						timeValues = append(timeValues, timeValue)
					}
				}
			}
		}

		// Check if we should skip this phase calculation due to missing nodes
		if nodesMissing && phaseTime.PhaseOver == "every_over" {
			logs.CtxDebug(ctx, "Skipping phase %s calculation for work item %d because nodes are missing and PhaseOver=every_over",
				phaseTime.PhaseKey, item.ID)
			continue
		}

		// Calculate final phase time value based on phase_math or os_phase_math
		var finalValue int64

		// If OS specific calculations are needed
		if phaseTime.IsOS {
			// Calculate OS-specific values
			for osType, values := range osTimeValues {
				mathRule, exists := phaseTime.OSPhaseMath[osType]
				if !exists {
					logs.CtxWarn(ctx, "No OS phase math rule for OS type: %s", osType)
					continue
				}

				// Calculate based on the math rule for this OS
				osValue := calculateTimeValue(values, mathRule)
				if osValue > 0 {
					timeValues = append(timeValues, osValue) // Store the OS-specific calculated value
				}
			}
		}

		// Calculate the final value
		finalValue = calculateTimeValue(timeValues, phaseTime.PhaseMath)
		if finalValue > 0 {
			// Store the calculated phase time value
			// Convert timestamp to string in format "YYYY-MM-DD"
			timeStr := time.Unix(finalValue/1000, 0).Format("2006-01-02")
			if item.DevMindFields == nil {
				item.DevMindFields = make(map[string]any)
			}
			item.DevMindFields[phaseTime.PhaseName] = timeStr
			phaseTimeMap[phaseTime.PhaseKey] = finalValue
		}
	}

	return phaseTimeMap
}

// Helper function to calculate time value based on math rule
func calculateTimeValue(values []int64, mathRule string) int64 {
	if len(values) == 0 {
		return 0
	}

	timeValues := lo.Filter(values, func(v int64, _ int) bool {
		return v > 0
	})

	if len(timeValues) == 0 {
		return 0
	}

	// Apply math rule
	switch mathRule {
	case "max":
		return lo.Max(timeValues)
	case "min":
		return lo.Min(timeValues)
	default:
		return 0
	}
}

// calculatePhaseDurations calculates the duration between two phase time points
func calculatePhaseDurations(ctx context.Context, item *meegosdk.WorkItemInfo, config *TemplateConfig, phaseTimeMap map[string]int64) {
	// Process standard phase durations
	for _, duration := range config.PhaseDuration {
		calculateAndStoreDuration(ctx, item, duration.DurationName, duration.Start, duration.End, phaseTimeMap)
	}

	// Process custom phase durations
	for _, customDuration := range config.CustomPhaseDuration {
		calculateAndStoreDuration(ctx, item, customDuration.DurationName, customDuration.Start, customDuration.End, phaseTimeMap)
	}
}

// calculateAndStoreDuration calculates a single duration between two time points and stores the result
func calculateAndStoreDuration(ctx context.Context, item *meegosdk.WorkItemInfo, durationName, startKey, endKey string, phaseTimeMap map[string]int64) {
	if startKey == "" || endKey == "" {
		return
	}

	startTime, startExists := phaseTimeMap[startKey]
	endTime, endExists := phaseTimeMap[endKey]

	if !startExists || !endExists {
		return
	}

	// Calculate duration only if both times are valid
	if startTime > 0 && endTime > 0 && endTime >= startTime {
		// Convert timestamps to dates (truncate to day boundaries)
		startDate := time.Unix(startTime/1000, 0).Truncate(24 * time.Hour)
		endDate := time.Unix(endTime/1000, 0).Truncate(24 * time.Hour)

		// Calculate days difference
		daysDiff := int64(endDate.Sub(startDate).Hours() / 24)

		durationStr := fmt.Sprintf("%d", daysDiff)

		// Store in DevMindFields
		if item.DevMindFields == nil {
			item.DevMindFields = make(map[string]any)
		}
		item.DevMindFields[durationName] = durationStr
	}
}

// 使用反射将item的字段添加到FieldsMap中
func addItemFieldsToMap(ctx context.Context, item *meegosdk.WorkItemInfo, fieldsMap map[string]*meegosdk.SimpleField, templateMap map[string]*meegosdk.Template) {
	if item == nil {
		return
	}

	val := reflect.ValueOf(item).Elem()
	typ := val.Type()

	// 遍历结构体的所有字段
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// 跳过非导出字段和空值
		if !field.CanInterface() || field.IsZero() {
			continue
		}

		// 跳过已经处理过的特殊字段
		if fieldType.Name == "Fields" || fieldType.Name == "FieldsMap" ||
			fieldType.Name == "UserDetails" || fieldType.Name == "WorkflowInfos" ||
			fieldType.Name == "RelationFieldsDetail" || fieldType.Name == "DevMindFields" {
			continue
		}

		// 获取字段的 JSON 标签名
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag == "" {
			jsonTag = fieldType.Name
		} else {
			// 如果 JSON 标签包含逗号，只取第一部分（例如 "name,omitempty" 取 "name"）
			if commaIdx := strings.Index(jsonTag, ","); commaIdx > 0 {
				jsonTag = jsonTag[:commaIdx]
			}
		}

		if fieldsMap[jsonTag] != nil && jsonTag != fieldType.Name {
			// 尝试使用 JSON 标签名查找
			fieldValue := convertFieldOptions(ctx, field.Interface(), fieldsMap[jsonTag], templateMap)
			// 添加到FieldsMap，使用 JSON 标签名作为键
			item.FieldsMap[fieldsMap[jsonTag].FieldName] = fieldValue
		}
	}
}

// CreateWorkItemsExcel creates an Excel file from WorkItemInfo items
// filename is the base name of the file, will append simpleName and workItemType to it
// items are the workitems to include in the Excel file
func CreateWorkItemsExcel(run *iris.AgentRunContext, filename string, simpleName string, workItemType string, items []*meegosdk.WorkItemInfo) (string, error) {
	var f *excelize.File
	var err error

	// Check if file exists
	if _, err := os.Stat(filename); err == nil {
		// File exists, open it
		run.GetLogger().Infof("Excel file already exists, opening: %s", filename)
		f, err = excelize.OpenFile(filename)
		if err != nil {
			run.GetLogger().Errorf("failed to open existing Excel file: %v", err)
			return "", err
		}
	} else {
		// File doesn't exist, create new one
		run.GetLogger().Infof("Creating new Excel file: %s", filename)
		f = excelize.NewFile()
	}

	// Ensure file is properly closed
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			run.GetLogger().Errorf("failed to close Excel file: %v", closeErr)
		}
	}()

	sheet := fmt.Sprintf("%s_%s", simpleName, workItemType)
	if len(sheet) > 31 {
		// Excel sheet name can't be longer than 31 characters
		sheet = sheet[:31]
	}

	// If sheet doesn't exist, create it
	if !lo.Contains(f.GetSheetList(), sheet) {
		_, err = f.NewSheet(sheet)
		if err != nil {
			run.GetLogger().Errorf("failed to create new sheet: %v", err)
			return "", err
		}
	}
	f.DeleteSheet("Sheet1")

	// Create stream writer for the sheet
	sw, err := f.NewStreamWriter(sheet)
	if err != nil {
		run.GetLogger().Errorf("failed to create stream writer: %v", err)
		return "", err
	}

	// Collect all headers
	headers := make(map[string]bool)

	// Add standard fields
	headers["ID"] = true

	// Add all field keys from all items
	for _, item := range items {
		// Add keys from FieldsMap
		for key := range item.FieldsMap {
			headers[key] = true
		}
	}

	headerSlice := append([]string{"ID", "Relation_Fields", "Workflow_Info", "Extra_Time_Filed"}, lo.Keys(headers)...)

	// Create header row data
	headerRow := make([]interface{}, len(headerSlice))
	for i, header := range headerSlice {
		headerRow[i] = excelize.Cell{Value: header}
	}

	// Write headers to first row
	if err := sw.SetRow("A1", headerRow); err != nil {
		run.GetLogger().Errorf("failed to write header row: %v", err)
		return "", err
	}

	// Write data rows using stream writer
	for i, item := range items {
		rowNum := i + 2 // Start from row 2 (row 1 is headers)

		// Create row data
		rowData := make([]interface{}, len(headerSlice))

		// Fill row data for each header
		for j, header := range headerSlice {
			var cellValue interface{}

			// Handle different types of headers
			switch {
			case header == "ID":
				cellValue = item.ID
			case header == "Relation_Fields":
				if item.RelationFieldsDetail != nil {
					cellValue = jsonx.ToString(item.RelationFieldsDetail)
				} else {
					cellValue = ""
				}
			case header == "Workflow_Info":
				if item.WorkflowInfos != nil {
					cellValue = jsonx.ToString(item.WorkflowInfos)
				} else {
					cellValue = ""
				}
			case header == "Extra_Time_Filed":
				if item.DevMindFields != nil {
					cellValue = jsonx.ToString(item.DevMindFields)
				} else {
					cellValue = ""
				}
			default:
				// Check in FieldsMap
				if val, ok := item.FieldsMap[header]; ok {
					// Convert complex types to strings
					switch v := val.(type) {
					case []interface{}:
						jsonVal, err := json.MarshalString(v)
						if err == nil {
							cellValue = jsonVal
						} else {
							cellValue = fmt.Sprintf("%v", v)
						}
					default:
						cellValue = v
					}
				} else {
					cellValue = ""
				}
			}

			rowData[j] = excelize.Cell{Value: cellValue}
		}

		// Calculate cell address for the row
		cellAddr, err := excelize.CoordinatesToCellName(1, rowNum)
		if err != nil {
			run.GetLogger().Errorf("failed to convert coordinates to cell name for row %d: %v", rowNum, err)
			continue
		}

		// Write the row
		if err := sw.SetRow(cellAddr, rowData); err != nil {
			run.GetLogger().Errorf("failed to write data row %d: %v", rowNum, err)
			continue
		}
	}

	// Flush the stream writer to finalize the data
	if err := sw.Flush(); err != nil {
		run.GetLogger().Errorf("failed to flush stream writer: %v", err)
		return "", err
	}

	// Save Excel file
	err = f.SaveAs(filename)
	if err != nil {
		run.GetLogger().Errorf("failed to save Excel file: %v", err)
		return "", err
	}

	run.GetLogger().Infof("Excel file created/updated successfully using stream writer: %s", filename)
	return filename, nil
}

// GetProjectFieldsMap retrieves project fields and creates a two-level map.
// First level is workItemType (from work_item_scopes), second level is field_key -> SimpleField
func GetProjectFieldsMap(ctx context.Context, client meegosdk.Api, userKey string, projectKey string) (map[string]map[string]*meegosdk.SimpleField, error) {
	fields, err := client.ListSimpleFields(ctx, projectKey, userKey, &meegosdk.ListSimpleFieldsRequest{})
	if err != nil {
		logs.CtxError(ctx, "failed to list project fields, err: %v", err)
		return nil, err
	}

	// Create the two-level map: workItemType -> fieldKey -> SimpleField
	fieldMap := make(map[string]map[string]*meegosdk.SimpleField)

	for _, field := range fields {
		// Add each field to the appropriate workItemType maps
		for _, scope := range field.WorkItemScopes {
			// Initialize the inner map if it doesn't exist
			if _, exists := fieldMap[scope]; !exists {
				fieldMap[scope] = make(map[string]*meegosdk.SimpleField)
			}

			// Add the field to the map with field_key as the key
			fieldMap[scope][field.FieldKey] = field
		}
	}

	logs.CtxInfo(ctx, "Retrieved field map for project %s with %d types", projectKey, len(fieldMap))
	return fieldMap, nil
}
