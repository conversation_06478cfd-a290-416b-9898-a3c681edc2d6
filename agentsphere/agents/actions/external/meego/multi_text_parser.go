package meego

import (
	"encoding/json"

	"code.byted.org/devgpt/kiwis/port/meegosdk"
)

// extractImageUUIDsFromMultiTexts 从multi_texts字段中提取图片URL
func extractImageUUIDsFromMultiTexts(workItems []*meegosdk.WorkItemInfo) []string {
	var imageURLs []string

	for _, workItem := range workItems {
		for _, field := range workItem.MultiTexts {
			if field.FieldValue == nil {
				continue
			}

			// 类型断言获取field_value
			fieldValue, ok := field.FieldValue.(map[string]interface{})
			if !ok {
				continue
			}

			// 提取doc字段
			docStr, ok := fieldValue["doc"].(string)
			if !ok || docStr == "" {
				continue
			}

			// 解析嵌套JSON并提取图片URL
			uuids := parseDocFieldForImages(docStr)
			imageURLs = append(imageURLs, uuids...)
		}
	}

	// 去重
	return removeDuplicateURLs(imageURLs)
}

// parseDocFieldForImages 解析doc字段中的图片URL
func parseDocFieldForImages(docStr string) []string {
	var uuids []string

	// 解析嵌套JSON
	var docContent map[string]interface{}
	if err := json.Unmarshal([]byte(docStr), &docContent); err != nil {
		return uuids
	}

	// 遍历所有zone中的ops数组
	for _, zoneValue := range docContent {
		if zone, ok := zoneValue.(map[string]interface{}); ok {
			if ops, ok := zone["ops"].([]interface{}); ok {
				for _, op := range ops {
					if opMap, ok := op.(map[string]interface{}); ok {
						if attrs, ok := opMap["attributes"].(map[string]interface{}); ok {
							// 检查是否为图片
							if image, ok := attrs["image"].(string); ok && image == "true" {
								if uuid, ok := attrs["uuid"].(string); ok && uuid != "" {
									uuids = append(uuids, uuid)
								}
							}
						}
					}
				}
			}
		}
	}

	return uuids
}

// removeDuplicateURLs 去除重复的URL
func removeDuplicateURLs(urls []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, url := range urls {
		if !seen[url] {
			seen[url] = true
			result = append(result, url)
		}
	}

	return result
}
