package meego

import (
	"context"
	"testing"

	json "github.com/bytedance/sonic"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
)

func TestGetWorkItemDetailFromMeego(t *testing.T) {
	//t.Skip()
	c := iris.NewRunContext(context.Background(), nil, nil, iris.DefaultLogger, nil, nil, &iris.RunEnviron{}, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	c.UserInfo = entity.UserInfo{Name: "zhangyibo.114514", Email: "<EMAIL>"}
	c.Environ.Set(entity.RuntimeEnvironUserCloudJWT, "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
	got, err := GetWorkItemDetailByUrl(c, GetWorkItemDetailArgs{
		URL:            "https://meego.larkoffice.com/bitsdevops/story/detail/6751356198",
		ResultFilePath: "./test.xlsx",
	})
	// json dump got
	gotJson, err := json.Marshal(got)
	if err != nil {
		t.Errorf("GetWorkItemDetailByUrl() error = %v", err)
		return
	}
	t.Logf("GetWorkItemDetailByUrl() got = %v", string(gotJson))
}
