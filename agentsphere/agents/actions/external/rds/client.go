package rds

import (
	"context"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"

	"code.byted.org/paas/cloud-sdk-go/jwt"
)

func getJwt(ctx context.Context, region, secret string) (string, error) {
	return jwt.NewGenerator(jwt.WithRegion(region)).Generate(ctx, secret)
}

type areaInfo struct {
	Region     string
	ApiUrl     string
	ClientType string
}

var areaMap = map[string]areaInfo{
	// China BOE regions
	"boe": { // China-BOE
		Region:     "boe",
		ApiUrl:     "https://rds-api-boe.byted.org",
		ClientType: jwt.RegionCN,
	},
	"boe2": { // China-BOE2
		Region:     "boe2",
		ApiUrl:     "https://rds-api-boe2.byted.org",
		ClientType: jwt.RegionCN,
	},
	"boei18n": { // US-BOE / boei18n
		Region:     "boei18n",
		ApiUrl:     "https://rds-api-boei18n.byted.org",
		ClientType: jwt.RegionCN,
	},
	"boettp": { // US-BOE / boettp
		Region:     "boettp",
		ApiUrl:     "https://rds-api-tx.tiktokd-boe.org",
		ClientType: jwt.RegionTX,
	},

	// China regions
	"cn": { // China-North/cn
		Region:     "cn",
		ApiUrl:     "https://rds-api-cn.byted.org",
		ClientType: jwt.RegionCN,
	},
	"tjdt": { // China-North/tjdt
		Region:     "tjdt",
		ApiUrl:     "https://rds-api-tjdt.byted.org",
		ClientType: jwt.RegionCN,
	},
	"sdqd": { // China-Fintech/sdqd
		Region:     "sdqd",
		ApiUrl:     "https://rds-api-sdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"xjhd": { // China-Fintech/xjhd
		Region:     "xjhd",
		ApiUrl:     "https://rds-api-xjhd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"hkcj": { // China-Fintech/hkcj
		Region:     "hkcj",
		ApiUrl:     "https://rds-api-sdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"agsdqd": { // China-Aggregation/agsdqd
		Region:     "agsdqd",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"agsxxa": { // China-Aggregation/agsxxa
		Region:     "agsxxa",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"agjsnj": { // China-Aggregation/agjsnj
		Region:     "agjsnj",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"aggdsz": { // China-Aggregation/aggdsz
		Region:     "aggdsz",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"aglnsy": { // China-Aggregation/aglnsy
		Region:     "aglnsy",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"aghbwh": { // China-Aggregation/aghbwh
		Region:     "aghbwh",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"agcq": { // China-Aggregation/agcq
		Region:     "agcq",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"bjlgy": { // China-Aggregation/bjlgy
		Region:     "bjlgy",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},
	"lftobiaas": { // China-Enterprise/lftobiaas
		Region:     "lftobiaas",
		ApiUrl:     "https://rds-api-lftobiaas.byted.org",
		ClientType: jwt.RegionCN,
	},
	"China-Pay": { // China-Pay
		Region:     "China-Pay",
		ApiUrl:     "https://rds-api-china-pay.byted.org",
		ClientType: jwt.RegionCN,
	},
	"China-Pay2": { // China-Pay2
		Region:     "China-Pay2",
		ApiUrl:     "https://rds-api-china-pay2.byted.org",
		ClientType: jwt.RegionCN,
	},
	"China-HKPay": { // China-HKPay
		Region:     "China-HKPay",
		ApiUrl:     "https://rds-api-china-hkpay.byted.org",
		ClientType: jwt.RegionCN,
	},
	"China-East": { // China-East/ce
		Region:     "China-East",
		ApiUrl:     "https://rds-api-china-east.byted.org",
		ClientType: jwt.RegionCN,
	},
	"pddt": { // China-East/pddt
		Region:     "pddt",
		ApiUrl:     "https://rds-api-pddt.byted.org",
		ClientType: jwt.RegionCN,
	},
	"galinc2": { // Aliyun_NC2/galinc2
		Region:     "galinc2",
		ApiUrl:     "https://rds-api-agsdqd.byted.org",
		ClientType: jwt.RegionCN,
	},

	// Singapore regions
	"alisg": { // Singapore-Central/sg1
		Region:     "alisg",
		ApiUrl:     "https://rds-api-sg1.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"sg_sensitive": { // Singapore-Central/sg1(sensitive)
		Region:     "sg_sensitive",
		ApiUrl:     "https://rds-api-sg1-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"sg2": { // Singapore-Central/sg2
		Region:     "sg2",
		ApiUrl:     "https://rds-api-sg1.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"sgdt": { // Singapore-Central/sgdt
		Region:     "sgdt",
		ApiUrl:     "https://rds-api-sg1.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"bdsgdt": { // Singapore-Central/bdsgdt
		Region:     "bdsgdt",
		ApiUrl:     "https://rds-api-bdsgdt.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"my5a": { // Singapore-Central/my5a
		Region:     "my5a",
		ApiUrl:     "https://rds-api-my5a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"sgcomm1": { // Singapore-Common/sgcomm1
		Region:     "sgcomm1",
		ApiUrl:     "https://rds-api-sgcomm1.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"awssg": { // Singapore-Compliance/sgcompliance
		Region:     "awssg",
		ApiUrl:     "https://rds-api-sgcompliance.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"awssg_sensitive": { // Singapore-Compliance/sgcompliance(sensitive)
		Region:     "awssg_sensitive",
		ApiUrl:     "https://rds-api-sgcompliance-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"sgsaas1larkidc1": { // Singapore-SaaS/sgsaas1larkidc1
		Region:     "sgsaas1larkidc1",
		ApiUrl:     "https://rds-api-sgsaas.byted.org",
		ClientType: jwt.RegionI18N,
	},

	// Europe regions
	"bddedt": { // Europe-WestBD/bddedt
		Region:     "bddedt",
		ApiUrl:     "https://rds-api-bddedt.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"be2a": { // Europe-WestBD/be2a
		Region:     "be2a",
		ApiUrl:     "https://rds-api-be2a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"awsfr": { // Europe-Central/awsfr
		Region:     "awsfr",
		ApiUrl:     "https://rds-api-awsfr.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"awsfr_sensitive": { // Europe-Central/awsfr(sensitive)
		Region:     "awsfr_sensitive",
		ApiUrl:     "https://rds-api-awsfr-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"de": { // EU-Compliance/de
		Region:     "de",
		ApiUrl:     "https://rds-api-de.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"de_sensitive": { // EU-Compliance/de(sensitive)
		Region:     "de_sensitive",
		ApiUrl:     "https://rds-api-de-sensitive.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"ie2": { // EU-Compliance2/ie2
		Region:     "ie2",
		ApiUrl:     "https://rds-api-ie2.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"ie2_sensitive": { // EU-Compliance2/ie2(sensitive)
		Region:     "ie2_sensitive",
		ApiUrl:     "https://rds-api-ie2-sensitive.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"ie": { // EU-TTP/ie
		Region:     "ie",
		ApiUrl:     "https://rds-api-ie.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"iedt": { // EU-TTP/iedt
		Region:     "iedt",
		ApiUrl:     "https://rds-api-iedt.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"dedt": { // EU-TTP/dedt
		Region:     "dedt",
		ApiUrl:     "https://rds-api-dedt.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"no1a": { // EU-TTP2/no1a
		Region:     "no1a",
		ApiUrl:     "https://rds-api-no1a.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"ycru": { // EasternEuro-TT/ycru
		Region:     "ycru",
		ApiUrl:     "https://rds-api-sg1.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"ycru_sensitive": { // EasternEuro-TT/ycru(sensitive)
		Region:     "ycru_sensitive",
		ApiUrl:     "https://rds-api-sg1.byted.org",
		ClientType: jwt.RegionI18N,
	},

	// US regions
	"maliva": { // US-East/maliva
		Region:     "maliva",
		ApiUrl:     "https://rds-api-maliva.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"maliva_sensitive": { // US-East/maliva(sensitive)
		Region:     "maliva_sensitive",
		ApiUrl:     "https://rds-api-maliva-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"useastdt": { // US-East/useastdt
		Region:     "useastdt",
		ApiUrl:     "https://rds-api-maliva.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"uswest2": { // US-West/uswest2
		Region:     "uswest2",
		ApiUrl:     "https://rds-api-uswest2.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"awsva": { // US-EE/va
		Region:     "awsva",
		ApiUrl:     "https://rds-api-va.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"useast14a": { // US-EastBD/useast14a
		Region:     "useast14a",
		ApiUrl:     "https://rds-api-useast14a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"us_east_gcp": { // US-EastRed/useast2a
		Region:     "us_east_gcp",
		ApiUrl:     "https://rds-api-useast2a.tiktoke.org",
		ClientType: jwt.RegionI18N,
	},
	"useast11a": { // US-Compliance/useast11a
		Region:     "useast11a",
		ApiUrl:     "https://rds-api-useast11a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"useast11a_sensitive": { // US-Compliance/useast11a(sensitive)
		Region:     "useast11a_sensitive",
		ApiUrl:     "https://rds-api-useast11a-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"ova": { // US-TTP/useast5
		Region:     "ova",
		ApiUrl:     "https://rds-api-tx.tiktokd.org",
		ClientType: jwt.RegionTX,
	},
	"useast8": { // US-TTP2/useast8
		Region:     "useast8",
		ApiUrl:     "https://rds-api-useast8.tiktokd.org",
		ClientType: jwt.RegionTX,
	},

	// Asia-Pacific regions
	"jpsaas": { // Asia-SaaS/jpsaas
		Region:     "jpsaas",
		ApiUrl:     "https://rds-api-jpsaas.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"mycis": { // Asia-CIS
		Region:     "mycis",
		ApiUrl:     "https://rds-api-mycis.byted.org",
		ClientType: jwt.RegionI18N,
	},

	// Indonesia regions
	"id1a": { // ID-Compliance/id1a
		Region:     "id1a",
		ApiUrl:     "https://rds-api-id1a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"id1a_sensitive": { // ID-Compliance/id1a(sensitive)
		Region:     "id1a_sensitive",
		ApiUrl:     "https://rds-api-id1a-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"id2a": { // ID-Compliance2/id2a
		Region:     "id2a",
		ApiUrl:     "https://rds-api-id2a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"id2a_sensitive": { // ID-Compliance2/id2a(sensitive)
		Region:     "id2a_sensitive",
		ApiUrl:     "https://rds-api-id2a-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},

	// Australia regions
	"syd1a": { // Australia-SouthEast/syd1a
		Region:     "syd1a",
		ApiUrl:     "https://rds-api-syd1a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"fr1a": { // Australia-SouthEast/fr1a
		Region:     "fr1a",
		ApiUrl:     "https://rds-api-fr1a.byted.org",
		ClientType: jwt.RegionI18N,
	},

	// Malaysia regions
	"my4a": { // MY-Compliance/my4a
		Region:     "my4a",
		ApiUrl:     "https://rds-api-my4a.byted.org",
		ClientType: jwt.RegionI18N,
	},
	"my4a_sensitive": { // MY-Compliance/my4a-sensitive
		Region:     "my4a_sensitive",
		ApiUrl:     "https://rds-api-my4a-sensitive.byted.org",
		ClientType: jwt.RegionI18N,
	},
}

type Client struct {
	client *resty.Client
	region string
}

var secretMap = map[string]string{
	jwt.RegionCN:   "8e33c30a23fc858bde7c5f0bb34fd1a6",
	jwt.RegionI18N: "4d6fd7b1b776115c22e079465097c3d4",
}

func GetClient(ctx context.Context, region string) (*Client, error) {
	area, ok := areaMap[region]
	if !ok {
		return nil, errors.New("unknown region " + region)
	}
	secret, ok := secretMap[area.ClientType]
	if !ok {
		return nil, errors.New("unsupport region " + area.ClientType)
	}

	jwtStr, err := getJwt(ctx, area.Region, secret)
	if err != nil {
		return nil, errors.Wrapf(err, "getJwt failed, region: %s", area.Region)
	}
	return &Client{
		client: resty.New().SetBaseURL(area.ApiUrl).SetHeader("x-jwt-token", jwtStr),
		region: area.Region,
	}, nil
}

type tableListItem struct {
	Name             string `json:"name" form:"name" query:"name"`                                           //表名
	Comment          string `json:"comment" form:"comment" query:"comment"`                                  //表的备注信息
	Mtime            int64  `json:"mtime" form:"mtime" query:"mtime"`                                        //表的结构最新修改时间
	AutoIncrementStr string `json:"auto_increment_str" form:"auto_increment_str" query:"auto_increment_str"` //下一个自增主键值，如果没有自增主键，或者没有自增主键值时，会返回空字符串，业务解析时，应该用uint64解析
	AutoIncrement    int64  `json:"auto_increment" form:"auto_increment" query:"auto_increment"`             //字段下线中，thrift 无法定义uint64, 此字段返回uint64 自增主键会有问题，请使用auto_increment_str
}

type tablesListResponse struct {
	Code int32  `json:"code" form:"code" query:"code"` //错误码，0 表示成功
	Msg  string `json:"msg" form:"msg" query:"msg"`    //错误信息
	Data struct {
		Result   []*tableListItem `json:"result" form:"result" query:"result"`          //表列表
		Total    int32            `json:"total" form:"total" query:"total"`             //总数
		Page     int32            `json:"page" form:"page" query:"page"`                //当前页码
		PageSize int32            `json:"page_size" form:"page_size" query:"page_size"` //每页返回的数目
	} `json:"data" form:"data" query:"data"` //返回数据
}

func (c *Client) ListTable(ctx context.Context, db string) ([]*tableListItem, error) {
	res := &tablesListResponse{}
	resp, err := c.client.R().SetContext(ctx).SetResult(res).SetQueryParams(map[string]string{
		"region": c.region,
		"all":    "true",
	}).SetPathParam("db_name", db).Get("/openapi/rds/v2/dbs/{db_name}/tables/")
	if err != nil {
		return nil, errors.WithMessage(err, "request failed")
	}
	if resp.StatusCode() != 200 {
		return nil, errors.Errorf("request failed, status code: %d, body: %s", resp.StatusCode(), resp.Body())
	}
	if res.Code != 0 {
		return nil, errors.Errorf("request failed, code: %d, message: %s", res.Code, res.Msg)
	}
	return res.Data.Result, nil
}

type tablesSchemaBatchResponse struct {
	Code int32             `json:"code" form:"code" query:"code"` //错误码，0 表示成功
	Msg  string            `json:"msg" form:"msg" query:"msg"`    //错误信息
	Data map[string]string `json:"data" form:"data" query:"data"` //返回一个 map，key 为表名，val 为字段信息，如果表不存在，则不会返回
}

func (c *Client) GetUserTableSql(ctx context.Context, db, table string) (map[string]string, error) {
	res := &tablesSchemaBatchResponse{}
	resp, err := c.client.R().SetContext(ctx).SetResult(res).SetBody(map[string]any{
		"region": c.region,
		"table_list": []string{
			table,
		},
	}).SetPathParam("db_name", db).Post("/openapi/rds/v2/dbs/{db_name}/tables_batch/get_sql/")
	if err != nil {
		return nil, errors.WithMessage(err, "request failed")
	}
	if resp.StatusCode() != 200 {
		return nil, errors.Errorf("request failed, status code: %d, body: %s", resp.StatusCode(), resp.Body())
	}
	if res.Code != 0 {
		return nil, errors.Errorf("request failed, code: %d, message: %s", res.Code, res.Msg)
	}
	return res.Data, nil
}

type getSlogLogSummaryResponse struct {
	Code int32  `json:"code" form:"code" query:"code"` //错误码，0 表示成功
	Msg  string `json:"msg" form:"msg" query:"msg"`    //错误信息
	Data struct {
		Detail []slowLogDetail `json:"detail" form:"detail" query:"detail"`
	} `json:"data" form:"data" query:"data"` //返回一个 map，key 为表名，val 为字段信息，如果表不存在，则不会返回
}
type slowLogDetail struct {
	Fingerprint  string  `json:"fingerprint"`
	Rank         string  `json:"rank"`
	Example      string  `json:"example"`
	ExampleTime  float64 `json:"example_time"`
	Count        int     `json:"count"`
	AvgQueryTime float64 `json:"avg_query_time"`
	Address      string  `json:"address"`
	User         string  `json:"user"`
	Host         string  `json:"host"`
}

func (c *Client) GetSlogLogSummary(ctx context.Context, db string, queryTime float64) ([]slowLogDetail, error) {
	res := &getSlogLogSummaryResponse{}
	now := time.Now().Unix()
	start := now - 24*3600 + 1 // 最多查询24h
	resp, err := c.client.R().SetContext(ctx).SetResult(res).SetQueryParams(map[string]string{
		"region":     c.region,
		"start_ts":   strconv.FormatInt(start, 10),
		"end_ts":     strconv.FormatInt(now, 10),
		"query_time": strconv.FormatFloat(queryTime, 'f', 1, 64),
	}).SetPathParam("db_name", db).Get("/openapi/rds/v2/dbs/{db_name}/slow_log/summary")
	if err != nil {
		return nil, errors.WithMessage(err, "request failed")
	}
	if resp.StatusCode() != 200 {
		return nil, errors.Errorf("request failed, status code: %d, body: %s", resp.StatusCode(), resp.Body())
	}
	if res.Code != 0 {
		return nil, errors.Errorf("request failed, code: %d, message: %s", res.Code, res.Msg)
	}
	return res.Data.Detail, nil
}
