package rds

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/lang/gg/gslice"
)

type listRDSTableToolArgs struct {
	DBName string `json:"db_name" mapstructure:"db_name" description:"required, the name of the database."`
	Region string `json:"region" mapstructure:"region" description:"required, the region where the database is located. Example: cn, i18n"`
}

type listRDSTableToolResp struct {
	Result []*tableListItem `json:"result" mapstructure:"result"`
}

func listRDSTableTool(c *iris.AgentRunContext, args listRDSTableToolArgs) (listRDSTableToolResp, error) {
	if args.Region == "" {
		args.Region = "cn"
	}
	c.GetLogger().Infof("[AgentRun]-[rds_debug]-[listRDSTableTool],list table in %s@%s", args.DBName, args.Region)

	client, err := GetClient(c, args.Region)
	if err != nil {
		c.GetLogger().Errorf("[AgentRun]-[rds_debug]-[listRDSTableTool],new rds client in %s failed: %v", args.Region, err)
		return listRDSTableToolResp{}, err
	}
	resp, err := client.ListTable(c, args.DBName)
	if err != nil {
		c.GetLogger().Errorf("[AgentRun]-[rds_debug]-[listRDSTableTool],list table in %s@%s failed: %v", args.DBName, args.Region, err)
		return listRDSTableToolResp{}, err
	}
	return listRDSTableToolResp{Result: resp}, nil
}

type GetUserTableSchemaToolArgs struct {
	DBName    string `json:"db_name" mapstructure:"db_name" description:"required, the name of the database."`
	TableName string `json:"table_name" mapstructure:"table_name" description:"required, the name of the table."`
	Region    string `json:"region" mapstructure:"region" description:"required, the region where the database is located. Example: cn, i18n"`
}

type GetUserTableSchemaToolResp struct {
	Result map[string]string `json:"result" mapstructure:"result"`
}

func getUserTableSchemaTool(c *iris.AgentRunContext, args GetUserTableSchemaToolArgs) (GetUserTableSchemaToolResp, error) {
	if args.Region == "" {
		args.Region = "cn"
	}
	c.GetLogger().Infof("[AgentRun]-[rds_debug]-[getUserTableSchemaTool],get table schema for %s.%s@%s", args.DBName, args.TableName, args.Region)

	client, err := GetClient(c, args.Region)
	if err != nil {
		c.GetLogger().Errorf("[AgentRun]-[rds_debug]-[getUserTableSchemaTool],new rds client in %s failed: %v", args.Region, err)
		return GetUserTableSchemaToolResp{}, err
	}
	resp, err := client.GetUserTableSql(c, args.DBName, args.TableName)
	if err != nil {
		c.GetLogger().Errorf("[AgentRun]-[rds_debug]-[getUserTableSchemaTool],get table schema for %s.%s@%s failed: %v", args.DBName, args.TableName, args.Region, err)
		return GetUserTableSchemaToolResp{}, err
	}
	return GetUserTableSchemaToolResp{Result: resp}, nil
}

type GetSlogLogSummaryToolArgs struct {
	DBName    string  `json:"db_name" mapstructure:"db_name" description:"required, the name of the database."`
	Region    string  `json:"region" mapstructure:"region" description:"required, the region where the database is located. Example: cn, i18n"`
	QueryTime float64 `json:"query_time" mapstructure:"query_time" description:"optional, the threshold of slow query time in seconds. Example: 0.1."`
}

type GetSlogLogSummaryToolResp struct {
	Result []slowLog `json:"result" mapstructure:"result"`
}

type slowLog struct {
	Fingerprint  string  `json:"fingerprint"`
	Rank         string  `json:"rank"`
	Example      string  `json:"sql"`
	ExampleTime  float64 `json:"max_time"`
	Count        int     `json:"count"`
	AvgQueryTime float64 `json:"avg_query_time"`
	Address      string  `json:"address"`
	User         string  `json:"user"`
	Host         string  `json:"host"`
}

func getSlogLogSummaryTool(c *iris.AgentRunContext, args GetSlogLogSummaryToolArgs) (GetSlogLogSummaryToolResp, error) {
	if args.Region == "" {
		args.Region = "cn"
	}
	c.GetLogger().Infof("[AgentRun]-[rds_debug]-[getSlogLogSummaryTool],get slow log summary for %s@%s", args.DBName, args.Region)

	client, err := GetClient(c, args.Region)
	if err != nil {
		c.GetLogger().Errorf("[AgentRun]-[rds_debug]-[getSlogLogSummaryTool],new rds client in %s failed: %v", args.Region, err)
		return GetSlogLogSummaryToolResp{}, err
	}
	resp, err := client.GetSlogLogSummary(c, args.DBName, args.QueryTime)
	if err != nil {
		c.GetLogger().Errorf("[AgentRun]-[rds_debug]-[getSlogLogSummaryTool],get slow log summary for %s@%s failed: %v", args.DBName, args.Region, err)
		return GetSlogLogSummaryToolResp{}, err
	}
	return GetSlogLogSummaryToolResp{Result: gslice.Map(resp, func(s slowLogDetail) slowLog {
		return slowLog{
			Fingerprint:  s.Fingerprint,
			Rank:         s.Rank,
			Example:      s.Example,
			ExampleTime:  s.ExampleTime,
			Count:        s.Count,
			AvgQueryTime: s.AvgQueryTime,
			Address:      s.Address,
			User:         s.User,
			Host:         s.Host,
		}
	})}, nil
}

func NewListRDSTableTool() iris.Action {
	return actions.ToTool("rds_list_rds_table", "List all tables in the specified database.", listRDSTableTool)
}

func NewGetUserTableSchemaTool() iris.Action {
	return actions.ToTool("rds_get_user_table_schema", "Get the create table sql of a specified table in the specified database.", getUserTableSchemaTool)
}

func NewGetSlogLogSummaryTool() iris.Action {
	return actions.ToTool("rds_get_slog_log_summary", "Get slow log summary for the specified database in the last 1 days .", getSlogLogSummaryTool)
}
