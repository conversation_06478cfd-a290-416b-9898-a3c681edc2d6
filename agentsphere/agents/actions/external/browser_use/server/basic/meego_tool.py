import json
import os
import re
import time
import uuid
import glob
import traceback
import requests
import pandas as pd
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from typing import Any

from global_vars import get_browser_session
from mcp import types

from log import logger
from playwright.async_api import Page
import asyncio


# Meego相关的常量配置
MEEGO_CSRF_TOKEN_LENGTH = 16
MEEGO_API_BASE_URL = "https://meego.larkoffice.com/goapi"
MEEGO_PROXIES = {
    'http': 'http://strato-proxy-rd-relay.byted.org:8118',
    'https': 'http://strato-proxy-rd-relay.byted.org:8118'
}
MEEGO_I18N_PROXY = {
    'http': 'http://strato-proxy-rd-relay.byteintl.net:8118',
    'https': 'http://strato-proxy-rd-relay.byteintl.net:8118'
}


class MeegoViewLoader:
    def __init__(self, region=None):
        """
        初始化MeegoViewLoader
        """
        self.cookies = {}
        self.meego_csrf_token = None
        self.workspace = os.getenv("IRIS_WORKSPACE_PATH", os.getcwd())
        self.sso_dir = "/tmp/sso"
        self.region = region
        # 根据region选择代理
        if region == "i18n":
            self.proxies = MEEGO_I18N_PROXY
            logger.info(f"MeegoViewLoader initialized with i18n proxy, workspace: {self.workspace}")
        else:
            self.proxies = MEEGO_PROXIES
            logger.info(f"MeegoViewLoader initialized with default proxy, workspace: {self.workspace}")

    async def _load_cookies_from_page(self, page):
        """
        直接从页面上获取cookies
        """
        logger.info("Loading cookies from page context")
        
        try:
            # 从页面上下文获取cookies
            cookies_data = await page.context.cookies()
            logger.info(f"Found {len(cookies_data)} cookies from page context")

            # 转换cookie格式并查找meego_csrf_token
            for cookie in cookies_data:
                cookie_name = cookie.get('name', '')
                cookie_value = cookie.get('value', '')
                cookie_domain = cookie.get('domain', '')

                # 只处理domain为.larkoffice.com的cookie
                if cookie_domain != '.larkoffice.com':
                    continue

                if cookie_name:
                    self.cookies[cookie_name] = cookie_value

                if cookie_name == 'meego_csrf_token':
                    self.meego_csrf_token = cookie_value
                    logger.info("Found existing meego_csrf_token in cookies")

            logger.info(f"Loaded {len(self.cookies)} cookies from page")

        except Exception as e:
            logger.error(f"Error loading cookies from page: {str(e)}")

        # 如果没有找到meego_csrf_token，生成一个新的
        if not self.meego_csrf_token:
            self.meego_csrf_token = self._generate_csrf_token()
            self.cookies['meego_csrf_token'] = self.meego_csrf_token
            logger.info(f"Generated new meego_csrf_token: {self.meego_csrf_token}")

    def _generate_csrf_token(self):
        """生成新的CSRF token"""
        return str(uuid.uuid4()).replace('-', '')[:16] + '-' + str(uuid.uuid4()).replace('-', '')[:4] + '-' + str(uuid.uuid4()).replace('-', '')[:4] + '-' + str(uuid.uuid4()).replace('-', '')[:4] + '-' + str(uuid.uuid4()).replace('-', '')[:12]


    def _get_cookie_string(self):
        """
        将cookies转换为cookie字符串格式
        """
        return '; '.join([f"{name}={value}" for name, value in self.cookies.items()])

    def _meego_api_request(self, method, endpoint, data=None, params=None):
        """
        向Meego API发送请求
        """
        headers = {
            'Cookie': self._get_cookie_string(),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'x-meego-csrf-token': self.meego_csrf_token
        }

        # 生成curl命令用于调试
        def generate_curl_command():
            curl_parts = ['curl', '-X', method.upper()]
            
            # 添加代理
            if MEEGO_PROXIES.get('https'):
                curl_parts.extend(['--proxy', MEEGO_PROXIES['https']])
            
            # 添加headers
            for key, value in headers.items():
                curl_parts.extend(['-H', f"'{key}: {value}'"])
            
            # 构建URL
            if method.upper() == 'GET' and params:
                import urllib.parse
                query_string = urllib.parse.urlencode(params)
                full_url = f"{endpoint}?{query_string}"
            else:
                full_url = endpoint
            
            # 添加数据（POST请求）
            if method.upper() == 'POST' and data:
                import json
                curl_parts.extend(['-d', f"'{json.dumps(data, ensure_ascii=False)}'"])
            
            curl_parts.append(f"'{full_url}'")
            return ' '.join(curl_parts)

        try:
            # 打印curl命令到日志
            curl_command = generate_curl_command()
            logger.info(f"Equivalent curl command: {curl_command}")
            
            if method.upper() == 'GET':
                session = requests.Session()
                session.trust_env = False
                response = session.get(
                    endpoint, headers=headers, params=params, proxies=self.proxies)
            elif method.upper() == 'POST':
                session = requests.Session()
                session.trust_env = False
                response = session.post(endpoint, headers=headers, json=data, proxies=self.proxies)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            logger.info(f"API request: {method} {endpoint} - Status: {response.status_code}")

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API request failed: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error making API request to {endpoint}: {str(e)}")
            return None

    def parse_meego_url(self, url):
        """
        解析Meego URL，提取项目信息和视图参数
        """
        logger.info(f"Parsing Meego URL: {url}")

        parsed_url = urlparse(url)
        path = parsed_url.path
        query = parsed_url.query
        params = parse_qs(query)

        project = path.split('/')[1] if len(path.split('/')) > 1 else None
        viewtype = path.split('/')[2] if len(path.split('/')) > 2 else None

        chart_id = None
        filter_id = None
        view = None

        if viewtype == 'chart':
            chart_id = path.split('/')[-1]
            parent_url = urlparse(params.get('parentUrl', [''])[0])
            if parent_url.path:
                viewtype = parent_url.path.split('/')[2] if len(parent_url.path.split('/')) > 2 else viewtype
                view = parent_url.path.split('/')[3] if len(parent_url.path.split('/')) > 3 else None
                parent_params = parse_qs(parent_url.query)
                if 'quickFilterId' in parent_params:
                    filter_id = parent_params['quickFilterId'][0]
        else:
            if viewtype == 'workObjectView':
                view = path.split('/')[4] if len(path.split('/')) > 4 else None
            else:
                view = path.split('/')[3] if len(path.split('/')) > 3 else None
            if 'quickFilterId' in params:
                filter_id = params['quickFilterId'][0]

        wktype = 'story'
        if viewtype == 'issueView':
            wktype = 'issue'
        elif viewtype == 'workObjectView':
            wktype = path.split('/')[3] if len(path.split('/')) > 3 else wktype

        result = {
            'project': project,
            'viewtype': viewtype,
            'view': view,
            'filter_id': filter_id,
            'chart_id': chart_id,
            'wktype': wktype
        }

        logger.info(f"Parsed URL parameters: {result}")
        return result

    def get_project_key(self, project_simple_name):
        """
        通过项目简单名称获取项目key
        """
        logger.info(f"Getting project key for: {project_simple_name}")

        endpoint = f'{MEEGO_API_BASE_URL}/v1/project/trans_simple_name'
        data = {"simple_name_list": [project_simple_name]}

        response = self._meego_api_request('POST', endpoint, data=data)

        if response and response.get('code') == 0:
            project_key_map = response.get('data', {}).get('project_key_map', {})
            project_key = project_key_map.get(project_simple_name)
            logger.info(f"Found project key: {project_key}")
            return project_key
        else:
            logger.error(f"Failed to get project key for {project_simple_name}")
            return None

    def get_chart_data(self, project_key, chart_id, bql_filter=None):
        """
        获取图表数据
        """
        logger.info(f"Getting chart data for chart_id: {chart_id}")

        endpoint = f'{MEEGO_API_BASE_URL}/v5/measurement_platform/charts/data'

        chart_req = {
            "project_key": project_key,
            "chart_id": chart_id,
            "is_latest": True,
            "is_complete_config": False,
            "latest": False,
            "client_info": {"device_id": "", "reference_subscription": ""}
        }

        if bql_filter:
            chart_req['bql_filter'] = bql_filter

        response = self._meego_api_request('POST', endpoint, data=chart_req)

        if not response or response.get('code') != 0:
            logger.error(f"Failed to get chart data for chart_id: {chart_id}")
            return None

        try:
            chart_data = response['data']['data']['data_based_chart']
            chart_name = chart_data['name']

            # 构建指标映射
            metric_map = {}
            for idx, quota in enumerate(chart_data['magicDefinition']['quotas']):
                metric_map[str(idx)] = quota['display']['showName']

            # 构建维度映射
            dim_map = {}
            for idx, quota in enumerate(chart_data['magicDefinition']['dims']):
                dim_map[str(idx)] = quota['display']['showName'] + '[维度]'

            # 提取参考线数据（只取第一条参考线的值）
            reference_lines = chart_data.get('referenceLines', [])
            reference_value = None
            if reference_lines:
                reference_value = reference_lines[0].get('val', '')

            # 解析数据
            rows = []
            chart_data_list = response['data']['data']['chart_data_list']

            for x in chart_data_list:
                if 'data_sets' not in x:
                    continue
                for dim in x['data_sets']:
                    row = {}
                    for metric in dim['quotaValues']:
                        if metric in metric_map:
                            row[metric_map[metric]] = dim['quotaValues'][metric]
                    for metric in dim['dimValues']:
                        if metric in dim_map:
                            row[dim_map[metric]] = dim['dimValues'][metric]

                    # 添加参考线数据
                    if reference_value is not None:
                        row['参考线'] = reference_value

                    rows.append(row)

            # 处理分析信息（如果有）
            analyze_info = chart_data.get('analyzeInfo', [])
            if len(analyze_info) > 0 and len(rows) >= 2:
                rows[0]['_cycle'] = 'Current'
                rows[1]['_cycle'] = 'Previous'

            # 创建DataFrame
            df = pd.DataFrame(rows)

            reference_info = f", with reference line: {reference_value}" if reference_value else ""
            logger.info(f"Successfully parsed chart data: {chart_name}, {len(rows)} rows{reference_info}")

            return {
                "name": chart_name,
                "format": "csv",
                "data": df.to_csv(index=False)
            }

        except Exception as e:
            logger.error(f"Error parsing chart data: {str(e)}")
            return None

    def get_table_data(self, project_key, view, viewtype, wktype):
        """
        获取表格数据
        """
        logger.info(f"Getting table data for view: {view}")

        # 获取运行时配置
        config_endpoint = f'{MEEGO_API_BASE_URL}/v5/search/general/runtime_config/get/v2'
        config_data = {
            "locator": {"view_id": view},
            "view_context": {
                "project_key": project_key,
                "view_support_features": {
                    "view_scenario": "Unknown",
                    "view_feature_point": "Page"
                }
            },
            "fg_map": {
                "meego.view.straight_out": True,
                "meego.view.shuchaijie": True,
                "meego.view.resource_lib": True,
                "meego.view.sub_task_current_operator_edit": True,
                "meego.workitem.custom_button": True,
                "meego.workitem.node_finished_split": True,
                "meego.view.ui_batch_switch": True
            },
            "slim_mode": True,
            "need_front_setting": False
        }

        config_response = self._meego_api_request('POST', config_endpoint, data=config_data)

        if not config_response or config_response.get('code') != 0:
            logger.error("Failed to get runtime config")
            return None

        runtime_config = config_response['data']['runtime_config']
        title = runtime_config['meta']['title']
        runtime_config_id = runtime_config['runtime_config_id']

        # 获取工作项结构和详情
        detail_endpoint = f'{MEEGO_API_BASE_URL}/v5/search/general/work_item_structure_and_detail/get'
        detail_data = {
            "view_id": view,
            "instant_query": False,
            "project_key": project_key,
            "runtime_config_id": runtime_config_id,
            "tags_map": {
                "MinPermissions": False,
                "NeedRaw": False,
                "NeedEditPermission": True,
                "NeedSubTask": False
            },
            "current_view_mode": "table"
        }

        detail_response = self._meego_api_request('POST', detail_endpoint, data=detail_data)

        if not detail_response or detail_response.get('code') != 0:
            logger.error("Failed to get work item details")
            return None

        try:
            work_item_structure = detail_response['data']['work_item_structure']
            work_item_detail_v2 = detail_response['data']['work_item_detail_v2']

            # 简化处理：直接提取所有工作项
            all_items = []
            for node_id, items in work_item_detail_v2.items():
                all_items.extend(items)

            # 获取字段映射
            fields_endpoint = f'{MEEGO_API_BASE_URL}/v3/settings/fields/project_keys'
            fields_data = [{
                "language": "zh",
                "project_key": project_key,
                "work_item_type_key": wktype
            }]

            fields_response = self._meego_api_request('POST', fields_endpoint, data=fields_data)

            field_map = {}
            if fields_response and fields_response.get('code') == 0 and len(fields_response.get('data', [])) > 0:
                for field in fields_response['data'][0]['fields']:
                    field_map[field['key']] = field['name']

            # 处理工作项数据
            records = []
            for item in all_items:
                record = {}
                ui_data_map = item.get('uiDataMap', {})

                for fkey, ui_data in ui_data_map.items():
                    if 'uiValue' not in ui_data:
                        continue

                    field_name = field_map.get(fkey, fkey)
                    ui_value = ui_data['uiValue']
                    ui_type = ui_data['uiType']

                    # 简化字段值提取
                    if ui_type == 'text' and 'text' in ui_value:
                        record[field_name] = ui_value['text'].get('value', '')
                    elif ui_type == 'number' and 'number' in ui_value:
                        record[field_name] = ui_value['number'].get('value', '')
                    elif ui_type == 'date' and 'date' in ui_value:
                        record[field_name] = ui_value['date'].get('value', '')
                    elif ui_type == 'select' and 'select' in ui_value:
                        values = [item['label'] for item in ui_value['select'].get('value', [])]
                        record[field_name] = ','.join(values)
                    elif ui_type == 'user' and 'user' in ui_value:
                        users = []
                        for user in ui_value['user'].get('value', []):
                            user_name = user.get('name_en') or user.get('name_cn') or user.get('email', '').split(',')[0] or user.get('employeeId', 'Unknown')
                            users.append(user_name)
                        record[field_name] = ','.join(users)
                    else:
                        # 其他类型暂时转为字符串
                        record[field_name] = str(ui_value)

                records.append(record)

            # 创建DataFrame
            df = pd.DataFrame(records)

            logger.info(f"Successfully parsed table data: {title}, {len(records)} rows")

            return {
                "name": title,
                "format": "csv",
                "data": df.to_csv(index=False)
            }

        except Exception as e:
            logger.error(f"Error parsing table data: {str(e)}")
            return None

    def save_data_to_file(self, data_info, filter_name, output_dir=None):
        """
        将数据保存到文件
        """
        if not output_dir:
            output_dir = self.workspace

        if not data_info:
            logger.error("No data to save")
            return None

        try:
            # 清理文件名
            safe_name = re.sub(r'[<>:"/\\|?*\s]', '_', data_info['name'])
            if filter_name:
                filename = f"{safe_name}_{filter_name}.csv"
            else:
                filename = f"{safe_name}.csv"

            filepath = os.path.join(output_dir, filename)

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 保存数据
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(data_info['data'])

            logger.info(f"Data saved to: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving data to file: {str(e)}")
            return None

    def get_view_mode(self, url_params):
        """
        获取视图模式（viewMode）
        根据MeegoView.ipynb的逻辑，优先从URL参数获取，否则通过API获取
        """
        logger.info("Getting view mode...")

        # 解析URL参数
        parsed_url = urlparse(url_params.get('original_url', ''))
        query_params = parse_qs(parsed_url.query)

        # 首先检查URL参数中是否有viewMode
        if 'viewMode' in query_params:
            view_mode = query_params['viewMode'][0]
            logger.info(f"Found viewMode in URL params: {view_mode}")
            return view_mode

        # 如果URL参数中没有viewMode，通过API获取
        view_id = url_params.get('view')
        if not view_id:
            logger.error("No view_id available to get view mode")
            return None

        try:
            endpoint = f'{MEEGO_API_BASE_URL}/v5/search/general/get_tiny_rtc_config'
            data = {"view_id": view_id}

            response = self._meego_api_request('POST', endpoint, data=data)

            if response and response.get('code') == 0:
                view_mode = response['data']['current_view_mode']
                logger.info(f"Got viewMode from API: {view_mode}")
                return view_mode
            else:
                logger.error(f"Failed to get view mode from API")
                return None
        except Exception as e:
            logger.error(f"Error getting view mode: {str(e)}")
            return None

    def get_chartids(self, project_key, view, viewtype, filter_id=None):
        """
        获取图表ID列表
        根据MeegoView.ipynb的逻辑实现
        """
        logger.info(f"Getting chart IDs for view: {view}")

        subfilter = None
        bql_filter = None
        filter_name = ""
        chartids = []
        title = ""

        if viewtype in ['multi-project-view', 'multiProjectView', 'storyView', 'issueView', 'workObjectView']:
            try:
                endpoint = f'{MEEGO_API_BASE_URL}/v5/search/general/get_chart_runtime_config'
                data = {'view_id': view}

                response = self._meego_api_request('POST', endpoint, data=data)

                if not response or response.get('code') != 0:
                    logger.error(f"Failed to get chart runtime config for view: {view}")
                    return title, bql_filter, chartids

                cfgres = response

                # 处理过滤器
                if filter_id:
                    query_configs = cfgres['data']['chart_runtime_config']['query']['sub_query_configs']['queryList']
                    for subquery in query_configs:
                        if subquery['uuid'] == filter_id:
                            subfilter = subquery['value']['filter']
                            filter_name = subquery['label']
                            break

                if subfilter:
                    import json
                    mainfilter = cfgres['data']['chart_runtime_config']['query']['query']['filter']
                    bql_filter = json.dumps({
                        "conditions": [],
                        "conjunction": "AND",
                        "groups": [mainfilter, subfilter]
                    })

                # 获取标题和图表ID列表
                title = cfgres['data']['chart_runtime_config']['meta']['title']
                for chart in cfgres['data']['chart_runtime_config']['chart']['chart_coordinate_list']:
                    chartids.append(chart['chart_id'])

                logger.info(f"Found {len(chartids)} charts in view: {view}")

            except Exception as e:
                logger.error(f"Error getting chart IDs: {str(e)}")

        else:
            logger.error(f"Unsupported viewtype: {viewtype}")

        return title, bql_filter, chartids, filter_name

    def process_meego_url(self, url, region=None):
        """
        处理Meego URL，获取并保存数据
        """
        logger.info(f"Processing Meego URL: {url}")
        if region:
            logger.info(f"Using region parameter: {region}")

        try:
            # 解析URL
            url_params = self.parse_meego_url(url)
            # 保存原始URL用于获取viewMode
            url_params['original_url'] = url

            if not url_params['project']:
                logger.error("No project found in URL")
                return {"error": "No project found in URL"}

            # 获取项目key
            project_key = self.get_project_key(url_params['project'])
            if not project_key:
                return {"error": f"Failed to get project key for {url_params['project']}"}

            results = []

            # 获取viewMode来判断是图表还是表格
            view_mode = self.get_view_mode(url_params)
            logger.info(f"View mode: {view_mode}")

            if view_mode == 'table':
                # 处理表格数据
                logger.info("Processing table data")
                table_data = self.get_table_data(
                    project_key,
                    url_params['view'],
                    url_params['viewtype'],
                    url_params['wktype']
                )
                if table_data:
                    filepath = self.save_data_to_file(table_data, '')
                    if filepath:
                        results.append({
                            "type": "table",
                            "name": table_data['name'],
                            "file": os.path.basename(filepath)
                        })
            else:
                # 处理图表数据
                logger.info("Processing chart data")

                # 先获取所有图表ID
                title, bql_filter, chartids, filter_name = self.get_chartids(
                    project_key,
                    url_params['view'],
                    url_params['viewtype'],
                    url_params['filter_id']
                )

                # 如果URL中指定了chart_id，只处理那一个
                if url_params['chart_id']:
                    chartids = [str(url_params['chart_id'])]
                    logger.info(f"Using specific chart_id from URL: {url_params['chart_id']}")

                if not chartids:
                    logger.error("No chart IDs found")
                    return {"error": "No chart IDs found"}

                logger.info(f"Processing {len(chartids)} charts")

                # 处理每个图表
                for chart_id in chartids:
                    chart_data = self.get_chart_data(project_key, chart_id, bql_filter)
                    if chart_data:
                        filepath = self.save_data_to_file(chart_data, filter_name)
                        if filepath:
                            results.append({
                                "chart_id": chart_id,
                                "type": "chart",
                                "name": chart_data['name'],
                                "file": os.path.basename(filepath)
                            })


            
            if results:
                logger.info(f"Successfully processed {len(results)} data items")
                return {
                    "success": True,
                    "project": url_params['project'],
                    "project_key": project_key,
                    "results": results
                }
            else:
                return {"error": "No data was successfully processed"}

        except Exception as e:
            logger.error(f"Error processing Meego URL: {str(e)}")
            return {"error": f"Failed to process URL: {str(e)}"}

    async def screenshot_meego_charts_batch(self, page, chart_ids: list = None, current_url: str = None, output_dir: str = None, results: list = None):
        """
        批量对Meego图表进行截图，使用图表名称作为文件名
        
        Args:
            page: Playwright page对象
            chart_ids: 图表ID列表，如果未提供则自动获取当前页面所有图表
            current_url: 当前页面URL
            output_dir: 截图保存目录，如果未提供则使用workspace目录
            results: 包含图表信息的结果列表，可以直接从中获取图表名称
            
        Returns:
            dict: 包含截图结果的字典
        """
        try:
            if not current_url:
                current_url = page.url
                
            logger.info(f"Starting batch chart screenshot on page: {current_url}")
            
            # 设置输出目录
            if not output_dir:
                output_dir = self.workspace
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建chart_id到chart_name的映射（如果提供了results）
            chart_name_map = {}
            if results:
                for result in results:
                    if result.get('type') == 'chart' and result.get('chart_id') and result.get('name'):
                        chart_name_map[str(result['chart_id'])] = result['name']
                logger.info(f"Built chart name mapping from results: {len(chart_name_map)} charts")
            

            
            # 如果没有提供chart_ids，自动获取页面上所有图表
            if not chart_ids:
                logger.info("No chart_ids provided, attempting to get all charts from current page...")
                
                url_params = self.parse_meego_url(current_url)
                url_params['original_url'] = current_url
                
                if url_params['project']:
                    project_key = self.get_project_key(url_params['project'])
                    if project_key:
                        title, bql_filter, chartids, filter_name = self.get_chartids(
                            project_key,
                            url_params['view'],
                            url_params['viewtype'],
                            url_params['filter_id']
                        )
                        chart_ids = [str(cid) for cid in chartids]
                        logger.info(f"Found {len(chart_ids)} charts on page: {chart_ids}")
                    else:
                        logger.error("Failed to get project key")
                        chart_ids = []
                else:
                    logger.error("Failed to parse project from URL")
                    chart_ids = []
            
            if not chart_ids:
                return {
                    "success": False,
                    "error": "No chart IDs found or provided",
                    "results": []
                }
            
            # 执行平滑滚动脚本
            logger.info("Executing smooth scroll script...")
            scroll_script = """
            // 平滑滚动到页面底部
            const gridContainer = document.querySelector('div.grid-layout-container');
            if (gridContainer) {
                gridContainer.scrollTo({
                    top: gridContainer.scrollHeight,
                    behavior: 'smooth'
                });
            } else {
                // 如果没找到grid-layout-container，滚动整个页面
                window.scrollTo({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                });
            }
            """
            
            await page.evaluate(scroll_script)
            
            # 等待滚动完成
            logger.info("Waiting for scroll to complete...")
            await page.wait_for_timeout(3000)  # 等待3秒让滚动完成
            
            # 批量处理所有图表
            results = []
            successful_screenshots = 0
            
            for chart_id in chart_ids:
                try:
                    logger.info(f"Processing chart {chart_id} ({successful_screenshots + 1}/{len(chart_ids)})")
                    
                    # 构建chart-wrapper的选择器
                    chart_wrapper_id = f"chart-wrapper-{chart_id}"
                    chart_selector = f"#{chart_wrapper_id}"
                    
                    logger.info(f"Looking for chart element: {chart_selector}")
                    
                    # 等待图表元素出现
                    try:
                        await page.wait_for_selector(chart_selector, timeout=5000)
                        logger.info(f"Found chart element: {chart_selector}")
                    except Exception as e:
                        logger.error(f"Chart element not found: {chart_selector}, error: {str(e)}")
                        results.append({
                            "chart_id": chart_id,
                            "success": False,
                            "error": f"Chart element {chart_selector} not found on page"
                        })
                        continue
                    
                    # 滚动到图表元素位置，确保它在视口中
                    logger.info("Scrolling chart element into view...")
                    await page.evaluate(f"""
                    const chartElement = document.querySelector('{chart_selector}');
                    if (chartElement) {{
                        chartElement.scrollIntoView({{
                            behavior: 'smooth',
                            block: 'center'
                        }});
                    }}
                    """)
                    
                    # 等待滚动到元素完成
                    await page.wait_for_timeout(2000)
                    
                    # 获取图表名称
                    chart_name = None
                    
                    # 优先从chart_name_map中获取图表名称
                    if chart_id in chart_name_map:
                        chart_name = chart_name_map[chart_id]
                        logger.info(f"Got chart name from results mapping: {chart_name}")
                    else:
                        # 如果mapping中没有，则用chartID
                        logger.info("Chart name not found in mapping, searching from page...")
                        chart_name = f"Chart_{chart_id}"
                    
                    # 清理文件名，移除不合法字符
                    safe_chart_name = re.sub(r'[<>:"/\\|?*\s]', '_', chart_name)
                    if filter_name:
                        screenshot_filename = f"{safe_chart_name}_{filter_name}.png"
                    else:
                        screenshot_filename = f"{safe_chart_name}.png"
                    screenshot_path = os.path.join(output_dir, screenshot_filename)
                    
                    # 对指定的图表元素进行截图
                    logger.info(f"Taking screenshot of chart: {chart_name} -> {screenshot_filename}")
                    chart_element = page.locator(chart_selector)
                    await chart_element.screenshot(path=screenshot_path, timeout=10000)
                    
                    logger.info(f"Screenshot saved to: {screenshot_path}")
                    
                    results.append({
                        "chart_id": chart_id,
                        "success": True,
                        "chart_name": chart_name,
                        "screenshot_file": screenshot_filename,
                        "screenshot_path": screenshot_path,
                    })
                    
                    successful_screenshots += 1
                    logger.info(f"Chart {chart_id} screenshot completed successfully: {screenshot_filename}")
                    
                except Exception as e:
                    logger.error(f"Failed to screenshot chart {chart_id}: {str(e)}")
                    results.append({
                        "chart_id": chart_id,
                        "success": False,
                        "error": str(e)
                    })
            
            # 准备最终结果
            final_result = {
                "success": True,
                "message": f"Batch screenshot completed. {successful_screenshots}/{len(chart_ids)} charts processed successfully",
                "page_url": current_url,
                "total_charts": len(chart_ids),
                "successful_screenshots": successful_screenshots,
                "failed_screenshots": len(chart_ids) - successful_screenshots,
                "output_directory": output_dir,
                "timestamp": datetime.now().strftime('%Y%m%d_%H%M%S'),
                "results": results
            }
            
            logger.info(f"Batch chart screenshot completed: {successful_screenshots}/{len(chart_ids)} successful")
            return final_result
            
        except Exception as e:
            logger.error(f"Failed to screenshot Meego charts: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to screenshot Meego charts: {str(e)}",
                "results": []
            }


_download_table_screenshot_lock = asyncio.Lock()

async def download_table_screenshot(url, region=None):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # 使用锁确保串行执行
            async with _download_table_screenshot_lock:
                workspace = os.getenv("IRIS_WORKSPACE_PATH")
                if not workspace:
                    workspace = os.getcwd()
                    logger.info(f"IRIS_WORKSPACE_PATH not set, using current directory: {workspace}")

                # 设置输出目录
                output_dir = os.path.join(workspace)

                # 确保目录存在
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir, exist_ok=True)
                    logger.info(f"Created output directory: {output_dir}")

                page = await get_browser_session().create_new_tab()

                await page.set_viewport_size(viewport_size={'width': 3840, 'height': 10000})

                try:
                    logger.info(f"Goto meego table url: {url} (attempt {attempt + 1}/{max_retries})")
                    await page.goto(url, wait_until="networkidle", timeout=30000)

                    # 查找id为SecondNavWrapper的元素
                    nav_wrapper_element = page.locator('#SecondNavWrapper')

                    # 查找class为generateTable的元素
                    generate_table_element = page.locator('.scrollbar-container')

                    # 检查两个元素是否都存在
                    if await nav_wrapper_element.count() == 0:
                        error_msg = "Element with id 'SecondNavWrapper' not found"
                        logger.warning(f"{error_msg} (attempt {attempt + 1})")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            return {
                                "success": False,
                                "error": error_msg,
                                "filename": None
                            }
                        continue

                    if await generate_table_element.count() == 0:
                        error_msg = "Element with class 'generateTable' not found"
                        logger.warning(f"{error_msg} (attempt {attempt + 1})")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            return {
                                "success": False,
                                "error": error_msg,
                                "filename": None
                            }
                        continue

                    # 等待元素可见
                    await nav_wrapper_element.wait_for(state="visible", timeout=10000)
                    await generate_table_element.wait_for(state="visible", timeout=10000)

                    # 获取两个元素的边界框
                    nav_wrapper_box = await nav_wrapper_element.bounding_box()
                    generate_table_box = await generate_table_element.bounding_box()

                    if not nav_wrapper_box or not generate_table_box:
                        error_msg = "Could not get bounding box for one or both elements"
                        logger.warning(f"{error_msg} (attempt {attempt + 1})")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            return {
                                "success": False,
                                "error": error_msg,
                                "filename": None
                            }
                        continue

                    # 计算截图区域：从SecondNavWrapper右上角到generateTable右下角
                    start_x = nav_wrapper_box["x"] + nav_wrapper_box["width"]
                    start_y = nav_wrapper_box["y"]
                    end_x = generate_table_box["x"] + generate_table_box["width"]
                    end_y = generate_table_box["y"] + generate_table_box["height"]

                    clip_region = {
                        "x": start_x,
                        "y": start_y,
                        "width": end_x - start_x,
                        "height": end_y - start_y
                    }

                    # 确保截图区域有效
                    if clip_region["width"] <= 0 or clip_region["height"] <= 0:
                        error_msg = "Invalid clip region calculated"
                        logger.warning(f"{error_msg} (attempt {attempt + 1})")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            return {
                                "success": False,
                                "error": error_msg,
                                "filename": None
                            }
                        continue

                    # 生成截图文件路径
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    screenshot_filename = f"{timestamp}.png"
                    screenshot_path = os.path.join(output_dir, screenshot_filename)

                    # 对指定区域进行截图
                    await page.screenshot(path=screenshot_path, clip=clip_region)

                    logger.info(f"Screenshot saved successfully to: {screenshot_path} (attempt {attempt + 1})")
                    logger.info(f"Screenshot region: x={clip_region['x']}, y={clip_region['y']}, width={clip_region['width']}, height={clip_region['height']}")

                    return screenshot_path

                except Exception as e:
                    error_msg = f"Failed to screenshot custom region: {str(e)}"
                    logger.error(f"{error_msg} (attempt {attempt + 1})")
                    if attempt == max_retries - 1:  # 最后一次尝试
                        return {
                            "success": False,
                            "error": error_msg,
                            "filename": None
                        }
                    # 等待一段时间再重试
                    await asyncio.sleep(2)
                finally:
                    await page.close()

        except Exception as e:
            error_msg = f"Outer exception in screenshot function: {str(e)}"
            logger.error(f"{error_msg} (attempt {attempt + 1})")
            if attempt == max_retries - 1:  # 最后一次尝试
                return {
                    "success": False,
                    "error": error_msg,
                    "filename": None
                }
            # 等待一段时间再重试
            await asyncio.sleep(2)

    # 如果所有重试都失败了
    return {
        "success": False,
        "error": f"All {max_retries} attempts failed",
        "filename": None
    }

async def monitor_meego_view_and_download_data(page, region=None):
    """
    监控Meego页面并下载数据

    Args:
        page: Playwright page对象
        region: 地区参数，可选

    Returns:
        dict: 包含下载结果和统计信息的字典
    """

    # 获取workspace目录
    # 刷新
    await page.reload()

    workspace = os.getenv("IRIS_WORKSPACE_PATH")
    if not workspace:
        workspace = os.getcwd()
        logger.info(f"IRIS_WORKSPACE_PATH not set, using current directory: {workspace}")

    # 设置输出目录
    output_dir = os.path.join(workspace)

    # 确保目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Created output directory: {output_dir}")

    # 获取当前页面URL
    current_url = page.url
    logger.info(f"Starting Meego data extraction on page: {current_url}")

    try:
        # 等待页面完全加载
        logger.info("Waiting for page to fully load...")
        await page.wait_for_load_state("networkidle", timeout=10000)
        await page.wait_for_timeout(3000)  # 额外等待3秒确保页面完全渲染

        # 创建MeegoViewLoader实例
        logger.info("Initializing MeegoViewLoader...")
        meego_loader = MeegoViewLoader(region=region)
        
        # 从页面加载cookies
        logger.info("Loading cookies from page...")
        await meego_loader._load_cookies_from_page(page)

        # 处理Meego URL并获取数据
        logger.info("Processing Meego URL and extracting data...")
        result = meego_loader.process_meego_url(current_url, region=region)
        
        # 如果成功获取数据且包含图表，则进行截图
        if "error" not in result and result.get("results"):
            logger.info("Attempting to take screenshots of Meego charts...")
            try:
                # 获取图表结果用于截图
                chart_results = [item for item in result.get("results", []) if item.get("type") == "chart"]
                if chart_results:
                    logger.info(f"Found {len(chart_results)} charts to screenshot")
                    screenshot_result = await meego_loader.screenshot_meego_charts_batch(
                        page=page,
                        chart_ids=None,  # 让方法自动获取图表ID
                        current_url=current_url,
                        output_dir=output_dir,
                        results=result.get("results", [])
                    )
                    
                    if screenshot_result.get("success"):
                        logger.info(f"Screenshot completed: {screenshot_result.get('successful_screenshots', 0)} charts captured")
                        # 将截图结果添加到主结果中
                        result["screenshots"] = screenshot_result
                    else:
                        logger.error(f"Screenshot failed: {screenshot_result.get('error', 'Unknown error')}")
                        result["screenshot_error"] = screenshot_result.get('error', 'Unknown error')
                else:
                    logger.info("No charts found for screenshot")
            except Exception as e:
                logger.error(f"Error during chart screenshot: {str(e)}")
                result["screenshot_error"] = str(e)

        if "error" in result:
            logger.error(f"Error processing Meego URL: {result['error']}")
            return {
                "success": False,
                "message": f"Failed to process Meego URL: {result['error']}",
                "data_count": 0,
                "files": []
            }

        # 提取结果信息
        results = result.get("results", [])
        project = result.get("project", "unknown")
        project_key = result.get("project_key", "unknown")

        # 统计信息
        data_count = len(results)
        files = [item.get("file", "") for item in results if item.get("file")]

        logger.info(f"Meego data extraction completed successfully")
        logger.info(f"Project: {project} (key: {project_key})")
        logger.info(f"Extracted {data_count} data items")
        for i, item in enumerate(results, 1):
            logger.info(f"  {i}. {item.get('type', 'unknown')} - {item.get('name', 'unnamed')} -> {item.get('file', 'no file')}")

        # 准备最终结果
        final_result = {
            "success": True,
            "message": f"Successfully extracted data from Meego project: {project}",
            "project": project,
            "project_key": project_key,
            "data_count": data_count,
            "files": files,
            "details": results,
            "workspace": output_dir
        }

        logger.info(f"Final result: Extracted {data_count} data items from project {project}")
        return final_result

    except Exception as e:
        logger.error(f"Error in Meego data extraction: {str(e)}")

        # 返回错误结果
        return {
            "success": False,
            "message": f"Error during Meego data extraction: {str(e)}",
            "data_count": 0,
            "files": [],
            "error": str(e)
        }

 