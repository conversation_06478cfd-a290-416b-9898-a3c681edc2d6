import json
import re
import urllib.parse
import time
import uuid
import os
import traceback
import wget
from typing import Any

import markdownify

from global_vars import get_browser_session
from mcp import types
from log import logger

from playwright.async_api import Page


# https://data.bytedance.net/libra/flight/3768380/report/

iris_workspace = os.environ.get("IRIS_WORKSPACE_PATH")

libra_path = f"{iris_workspace}/libra_download/"

libra_url_re = "https://data.bytedance.net/libra/flight/.+?/report"

wait_clipboard_times = 5

def is_libra_report(url: str) -> bool:
    if len(re.compile(libra_url_re).findall(url)) != 0:
        return True

    return False


async def save_libra_image(image_name: str,a_libra_path: str, t: Any) -> str:
    image_name = image_name + ".png"
    image_path = os.path.join(a_libra_path, image_name)
    await t.screenshot(path=image_path, timeout=30000, type="png")
    # wget.download(image_url, out=image_path)
    return f"Image {image_name} Saved in {image_path}"


async def need_recall_data(t: Any):
    no_data = t.get_by_text("数据缺失")
    return await no_data.count() == 0

async def get_libra_screenshot_and_data(filename) -> list:
    if not os.path.exists(libra_path):
        os.makedirs(libra_path, exist_ok=True)

    try:
        page = await get_browser_session().get_current_page()
        if not is_libra_report(page.url):
            return [
                types.TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "success": False,
                            "message": "Only support Libra report websites",
                            "result":  "Only support Libra report websites, no image can be fetched",
                        },
                        indent=2
                    )
                )
            ]

        page_title = await page.title()
        page_title = page_title.replace(" ", "")
        page_title = page_title.replace("/", "-")

        a_libra_path = os.path.join(libra_path, page_title)

        if not os.path.exists(a_libra_path):
            os.makedirs(a_libra_path)

        tables = page.locator(".table-chart-wrapper")

        failed_list = []
        result = f"Experiment Title: {page_title}"
        data_file = os.path.join(a_libra_path, f"{page_title}.md")

        count = 0
        logger.info(f"total tables count: {len(await tables.all())}")
        for t in await tables.all():
            try:
                await t.scroll_into_view_if_needed(timeout=30000)
                data = t.locator(".arco-table-content-inner")
                await data.wait_for(state='attached', timeout=30000)
                if need_recall_data(t):
                    recall_data = t.get_by_text("回溯表格")
                    if await recall_data.count() != 0:
                        await recall_data.click(timeout=30000)
                        failed_list.append(t)
                        continue

                if len(await t.all_inner_texts()) != 0:
                    titles = await t.all_inner_texts()
                    title = titles[0].split("\n")[0].replace(" ", "")
                    title = title.replace("/", "-")
                    title = title.rsplit('.', 1)[0]
                    await save_libra_image(title, a_libra_path, t)
                else:
                    title = str(uuid.uuid4())
                    await save_libra_image(title, a_libra_path, t)

                mk = markdownify.markdownify(await t.inner_html())
                with open(data_file, "a") as file:
                    file.write(mk)
                    file.flush()
                count += 1
                logger.info(f"saved tables count: {count}")

            except Exception as e:
                logger.info(f"failed to screenshot for {t}, retry later {e}")
                failed_list.append(t)
                continue
        logger.info(f"total tables need retry count: {len(failed_list)}")
        count = 0
        # old_image_url = ""
        for t in failed_list:
            try:
                await t.scroll_into_view_if_needed(timeout=30000)
                data = t.locator(".arco-table-content-inner")
                await data.wait_for(state='attached', timeout=30000)
                if len(await t.all_inner_texts()) != 0:
                    titles = await t.all_inner_texts()
                    title = titles[0].split("\n")[0].replace(" ", "")
                    title = title.replace("/", "-")
                    title = title.rsplit('.', 1)[0]
                    await save_libra_image(title, a_libra_path, t)
                else:
                    title = str(uuid.uuid4())
                    await save_libra_image(title, a_libra_path, t)

                mk = markdownify.markdownify(await t.inner_html())
                with open(data_file, "a") as file:
                    file.write(mk)
                    file.flush()
                count += 1
                logger.info(f"saved retry tables count: {count}")
            except Exception as e:
                logger.info(f"failed to extract {t} due to {e}")
                continue

        result = result + "\n" + f"Screenshots is saved at {a_libra_path}.\n Report data is saved at {data_file}"
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {
                        "success": True,
                        "message": "Success to save libra images and data",
                        "result":  result,
                        "file_name": filename,
                    },
                    indent=2
                )
            )
        ]
    except Exception as e:
        tb = traceback.format_exc()
        logger.info(f"failed to get libra trace: {tb}")
        return [
            types.TextContent(
                type="text",
                text=json.dumps(
                    {"error": f"Failed to get libra data image: {str(e)}"}, indent=2
                ),
            )
        ]