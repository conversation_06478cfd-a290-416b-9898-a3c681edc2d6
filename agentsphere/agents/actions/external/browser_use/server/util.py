import asyncio
import urllib.parse
from pathlib import Path
import re
from log import logger
import markdownify
import time
import base64
import os
import json
from functools import partial

from global_vars import get_browser_session


def extract_result(text: str) -> str:
    start_tag = "<result>"
    end_tag = "</result>"
    start = text.find(start_tag)
    if start == -1:
        return "None"
    start += len(start_tag)
    end = text.find(end_tag, start)
    return text[start:end].strip() if end != -1 else "None"


limited_hosts = ["byted.org", "bytedance.org", "bytedance.net", "bytedance.larkoffice.com"]

white_hosts = ["bitsai.bytedance.net",
               "libra-staging.maat.bytedance.net", "test-union.bytedance.net",
               "aime.bytedance.net", "aime-boe.bytedance.net", "rdk.bytedance.net",
               "voc.bytedance.net", "vcloud-admin.bytedance.net", "vqos.bytedance.net"]

white_urls = ["https://cloud.bytedance.net/docs", "https://data.bytedance.net/libra", "https://ecop.bytedance.net/molly/experimental-report"]

white_url_patterns = ["https://meego.larkoffice.com/.+?/story/detail", "https://meego.larkoffice.com/.+?/issue/detail"]

white_wildcard_hosts = ["aime-app.bytedance.net"]


def is_limited_host(current_host: str | None, current_url: str | None):
    scheme = urllib.parse.urlparse(current_url).scheme
    if scheme == "file":
        return False

    for wildcard_host in white_wildcard_hosts:

        if wildcard_host in current_host:
            return False

    if current_host in white_hosts:
        return False

    for url in white_urls:

        if current_url.startswith(url):
            return False

    for url_re in white_url_patterns:
        if len(re.compile(url_re).findall(current_url)) != 0:
            return False

    for limited_host in limited_hosts:

        if limited_host in current_host:
            return True

    return False


def download(url: str) -> str:
    if url.startswith(("http://", "https://")):
        import tempfile
        import requests

        # Create temp file with .pdf extension
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)

        try:
            response = requests.get(url, stream=True, timeout=(20, 60 * 10), proxies={
                "http": "http://strato-proxy-rd-relay.byted.org:8118",
                "https": "http://strato-proxy-rd-relay.byted.org:8118",
            })
            response.raise_for_status()
            downloaded = 0
            total_size = int(response.headers.get('content-length', 0))

            # Write to temp file
            with open(temp_file.name, 'wb') as f:

                for chunk in response.iter_content(chunk_size=8192):

                    f.write(chunk)

                    downloaded += len(chunk)

                    progress = (downloaded / total_size) * 100

                    if progress % 10 < 0.05:
                        logger.info(f"download progress: {progress:.0f}%")

            return temp_file.name

        except requests.exceptions.Timeout as e:
            Path(temp_file.name).unlink(missing_ok=True)
            raise RuntimeError(f"Timeout downloading from {url}: {str(e)}")
        except Exception as e:
            Path(temp_file.name).unlink(missing_ok=True)
            raise RuntimeError(f"Failed to download from {url}: {str(e)}")

    elif url.startswith("file://"):
        return url[7:]  # Strip file:// prefix

    return url  # Assume local path


def is_json(j):
    try:
        json.loads(j)
    except ValueError:
        return False
    return True


network_keyword_attention = {
    "https://data.bytedance.net/tea-next": [
        "analysis",
    ],
    "https://grafana.byted.org/": [
        "query",
    ],
    "https://data.bytedance.net/libra/dashboard": [
        "chart",
    ],
    "https://titan.bytedance.net/": [
        "produceMarketGraph",
    ]
}


async def handle_response_capture(response, url, captured_requests):
    try:
        try:
            text_body = await response.text()
        except Exception as e:
            text_body = None

        keywords = []
        for config_url in network_keyword_attention:
            if config_url in url:
                keywords = network_keyword_attention.get(config_url)
                break

        current_host = urllib.parse.urlparse(url).hostname

        if text_body is not None and len(text_body) > 100 and is_json(text_body) \
                and current_host in response.url:

            if len(keywords) > 0:
                for keyword in keywords:
                    if keyword in response.url:
                        j = json.loads(text_body)
                        request = response.request
                        post_data_str = None
                        try:
                            post_data = request.post_data_buffer
                            if post_data:
                                try:
                                    post_data_str = post_data.decode('utf-8', errors='replace')
                                except UnicodeDecodeError:
                                    post_data_str = f"[Binary data: {len(post_data)} bytes]"
                        except Exception:
                            post_data_str = "[Error retrieving post data]"

                        captured_requests.append({
                            "url": response.url,
                            "post_data_str": post_data_str,
                            "timestamp": time.time(),
                            "data": j,
                        })
                        return

            else:
                j = json.loads(text_body)
                captured_requests.append({
                    "url": response.url,
                    "timestamp": time.time(),
                    "data": j,
                })

    except Exception as e:
        captured_requests.append(
            {"event_type": "response_capture_error", "url": response.url, "error": str(e), "timestamp": time.time()})


class CanvasConverter(markdownify.MarkdownConverter):
    def __init__(self, title, **options):
        super().__init__(**options)
        self.xpaths = {}
        title = title.replace(" ", "")
        title = title.replace("/", "-")
        self.title = title

    def extract_data_attributes(self, el):
        data_attrs = {}
        if hasattr(el, 'attrs') and el.attrs:
            for attr, value in el.attrs.items():
                if attr.startswith('data-'):
                    data_attrs[attr] = value
        return data_attrs

    def format_data_attributes(self, data_attrs):
        if not data_attrs:
            return ''

        attr_strings = []
        for attr, value in data_attrs.items():
            attr_strings.append(f'{attr}="{value}"')

        return f'\n<!-- {" ".join(attr_strings)} -->\n'

    def convert_div(self, el, text, parent_tags):
        data_attrs = self.extract_data_attributes(el)
        data_comment = self.format_data_attributes(data_attrs)

        if parent_tags:
            result = ' ' + text.strip() + ' '
            if data_comment:
                result = data_comment + result
            return result

        text = text.strip()
        if text:
            result = f'\n\n{text}\n\n'
            if data_comment:
                result = data_comment + result
            return result
        elif data_comment:
            return f'\n\n{data_comment}\n\n'
        else:
            return ''

    def convert_span(self, el, text, parent_tags):
        data_attrs = self.extract_data_attributes(el)
        data_comment = self.format_data_attributes(data_attrs)

        result = text.strip()
        if data_comment:
            result = data_comment + result

        if parent_tags:
            return ' ' + result + ' '
        return f'\n\n{result}\n\n'

    def get_element_xpath(self, el):
        path_segments = []
        child = el
        while child and child.name != '[document]':
            segment = child.name
            if child.parent:
                siblings = child.parent.find_all(child.name, recursive=False)
                if len(siblings) > 1:
                    count = 1
                    for sibling in siblings:
                        if sibling is child:
                            segment += f'[{count}]'
                            break
                        count += 1
            path_segments.insert(0, segment)
            child = child.parent
        return '/' + '/'.join(path_segments)

    def find_target_parent(self, el):
        current = el.parent
        first_style_parent = None

        while current and current.name != '[document]':
            has_style = current.get('style') is not None

            if has_style and first_style_parent is None:
                first_style_parent = current

            if current.name.lower() == 'body':
                return first_style_parent

            if has_style and self.has_siblings_with_attributes(current):
                return current

            current = current.parent

        return first_style_parent

    def has_siblings_with_attributes(self, el):
        if not el.parent or not el.parent.parent:
            return False

        parent_siblings = el.parent.parent.find_all(recursive=False)
        for sibling in parent_siblings:
            if sibling != el.parent and sibling.name:
                style = sibling.get('style')
                if style:
                    style_lower = style.lower()
                    if 'width' in style_lower and 'height' in style_lower:
                        return True
        return False

    def convert_canvas(self, el, text, parent_tags):
        # 寻找合适的父节点
        target_parent = self.find_target_parent(el)

        if target_parent:
            xpath = self.get_element_xpath(target_parent)
        else:
            # 如果没有找到合适的父节点，使用原始canvas元素
            xpath = self.get_element_xpath(el)

        now = time.time()
        image_dir = f'{os.getenv("IRIS_WORKSPACE_PATH")}/{self.title}'
        if not os.path.exists(image_dir):
            os.makedirs(image_dir)
        image_path = f'{image_dir}/{now}.png'
        self.xpaths[xpath] = image_path
        return f"\n\n![]({image_path})\n\n"



async def save_element_screenshot(xpath, page, image_path):
    # 首先检查元素是否存在
    element_check = await page.evaluate("""
        (xpath) => {
            const result = document.evaluate(
                xpath, 
                document, 
                null, 
                XPathResult.FIRST_ORDERED_NODE_TYPE, 
                null
            );
            const element = result.singleNodeValue;
            
            if (!element) {
                return { exists: false, error: '未找到元素，请检查XPath是否正确' };
            }
            
            return { 
                exists: true, 
                isCanvas: element instanceof HTMLCanvasElement,
                tagName: element.tagName.toLowerCase()
            };
        }
    """, xpath)

    if not element_check['exists']:
        raise Exception(element_check['error'])

    # 确保输出目录存在
    output_dir = os.path.dirname(image_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 如果是canvas元素，使用原有的canvas截图逻辑
    if element_check['isCanvas']:
        data_url = await page.evaluate("""
            (xpath) => {
                const result = document.evaluate(
                    xpath, 
                    document, 
                    null, 
                    XPathResult.FIRST_ORDERED_NODE_TYPE, 
                    null
                );
                const canvasElement = result.singleNodeValue;
                
                // 将Canvas转换为dataURL
                const mimeType = 'image/png';
                const dataURL = canvasElement.toDataURL(mimeType);
                
                return dataURL;
            }
        """, xpath)

        if data_url.startswith('ERROR:'):
            raise Exception(f"获取Canvas数据URL失败: {data_url}")

        header, encoded = data_url.split(",", 1)
        image_data = base64.b64decode(encoded)

        with open(image_path, "wb") as f:
            f.write(image_data)
    else:
        # 对于非canvas元素，使用Playwright的元素截图功能
        element = page.locator(f'xpath={xpath}').first
        await element.screenshot(path=image_path, type='png')

    return image_path


async def wait_all_tables(page):
    try:
        table_locator = page.locator('table')

        await table_locator.first.wait_for(state='visible', timeout=5000)
    except Exception as e:
        logger.info(f"no table element in page: {e}")
        return True

    start_time = time.time()

    max_wait = 30

    check_script = """
        () => {
        const tableElements = document.querySelectorAll('table');
        
        // 检查table是否有内容
        let hasContent = false;
        for (const table of tableElements) {
            // 检查table是否有行和列
            const rows = table.querySelectorAll('tr');
            if (rows.length > 0) {
                // 检查是否有实际的单元格内容
                for (const row of rows) {
                    const cells = row.querySelectorAll('td, th');
                    if (cells.length > 0) {
                        // 检查单元格是否有文本内容
                        for (const cell of cells) {
                            if (cell.textContent && cell.textContent.trim().length > 0) {
                                hasContent = true;
                                break;
                            }
                        }
                    }
                    if (hasContent) break;
                }
            }
            if (hasContent) break;
        }
        
        if (!hasContent) {
            return { ready: false, reason: 'Table content not loaded yet' };
        }
        
        return { ready: true, reason: 'Tables are ready' };
    }
    """

    while time.time() - start_time < max_wait:
        try:
            result = await page.evaluate(check_script)

            if result['ready']:
                logger.info(f"Tables ready for processing")
                return True

            await page.wait_for_timeout(1000)

        except Exception as e:
            logger.warning(f"Error during table check: {e}")
            await page.wait_for_timeout(1000)

    logger.warning(f"Tables not ready after {max_wait} seconds")
    return False


async def wait_all_canvas_loaded(page):
    start_time = time.time()

    max_wait = 30

    check_script = """
        () => {
        const canvasElements = document.querySelectorAll('canvas');
        if (canvasElements.length === 0) {
            return { ready: false, reason: 'No canvas elements found' };
        }

        // 检查Canvas是否有内容
        let hasContent = false;
        for (const canvas of canvasElements) {
            if (canvas.width > 0 && canvas.height > 0) {
                // 检查canvas是否不是完全空白
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    const imageData = ctx.getImageData(0, 0, Math.min(canvas.width, 100), Math.min(canvas.height, 100));
                    const data = imageData.data;
                    // 检查是否有非透明像素
                    for (let i = 3; i < data.length; i += 4) {
                        if (data[i] > 0) { // alpha channel
                            hasContent = true;
                            break;
                        }
                    }
                }
            }
            if (hasContent) break;
        }

        if (!hasContent) {
            return { ready: false, reason: 'Canvas content not rendered yet' };
        }

        return { ready: true, reason: 'Canvas is ready' };
    }
    """
    while time.time() - start_time < max_wait:
        try:
            result = await page.evaluate(check_script)

            if result['ready']:
                logger.info(f"Canvas ready for screenshot")
                return True

            await page.wait_for_timeout(1000)

        except Exception as e:
            logger.warning(f"Error during canvas check: {e}")
            await page.wait_for_timeout(1000)


async def get_bytedance_data_information(url: str):
    page = await get_browser_session().new_tab()

    await page.set_viewport_size(viewport_size={'width': 1920, 'height': 10000})

    captured_requests = []

    page.on("response", partial(handle_response_capture, url=url, captured_requests = captured_requests))

    await page.goto(url)

    try:
        await page.wait_for_load_state("networkidle", timeout=100000)
        await wait_all_canvas_loaded(page)
        await wait_all_tables(page)
    except:
        pass

    convert = CanvasConverter(title=await page.title(), strip=["img"])
    markdown_text = convert.convert(await page.content())

    for xpath in convert.xpaths:
        image_path = convert.xpaths.get(xpath)
        await save_element_screenshot(xpath, page, image_path)

    network_files = []


    for i in range(len(captured_requests)):
        request = captured_requests[i]
        json_dir = f'{os.getenv("IRIS_WORKSPACE_PATH")}/{convert.title}'
        output_network_path = f"{json_dir}/{time.time()}.json"
        with open(output_network_path, "w", encoding="utf-8") as f:
            f.write(json.dumps(request, ensure_ascii=False))
            network_files.append(output_network_path)

    await page.close()

    return {
        "msg": markdown_text,
        "network_files": network_files,
    }
