package imagessearch

import (
	"fmt"
	"sync"

	"code.byted.org/lang/gg/choose"
	"github.com/cenkalti/backoff/v4"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/port/toutiaosearch"
)

var (
	doubaoSearchClient *toutiaosearch.RPCClient
	initOnce           sync.Once
)

func RegisterDoubaoEngine() {
	searchEngines = append(searchEngines, &doubaoImageSearch{})
}

type doubaoImageSearch struct{}

func (*doubaoImageSearch) Name() string {
	return "Doubao"
}

func (*doubaoImageSearch) RemoteImageSearch(ctx *iris.AgentRunContext, req *ImageSearchRequest) (*ImageSearchResponse, error) {
	initOnce.Do(func() {
		doubaoSearchClient = toutiaosearch.NewToutiaoSearchRpcClientByLocalConfig(nil, toutiaosearch.ToutiaoSearchConfig{
			TrafficGroup: "aime",
			TrafficId:    "aime_images",
		})
	})

	var (
		originImagesResp *toutiaosearch.ImageSearchObservation
		err              error
		resp             = &ImageSearchResponse{}
	)

	// 搜索图片
	backoffConf := backoff.NewExponentialBackOff()
	backoffConf.InitialInterval = searchRetryInitInterval
	err = backoff.Retry(func() error {
		originImagesResp, err = doubaoSearchClient.ImageSearch(ctx, req.Query)
		if err != nil {
			return err
		}
		return nil
	}, backoff.WithMaxRetries(backoffConf, searchRetryMaxTimes))

	if err != nil {
		return nil, fmt.Errorf("图片搜索失败: %w", err)
	}
	// 对结果做加工
	for _, rr := range originImagesResp.ImageResults {
		for _, card := range rr.SingleSearchResult.SearchResult.ImageCard.CardList {
			if len(resp.Results) >= req.Limit {
				break
			}
			resp.Results = append(resp.Results, &ImageResult{
				Title: card.AbstractInfo.TitleOriginal,
				URL: choose.IfLazyL(len(card.ImageInfo.URLList) > 0, func() string {
					return card.ImageInfo.URLList[0]
				}, card.ImageInfo.ThumbnailURL),
				Format: card.ImageInfo.Format,
				Height: card.ImageInfo.Height,
				Width:  card.ImageInfo.Width,
			})
		}
	}
	return resp, nil
}
