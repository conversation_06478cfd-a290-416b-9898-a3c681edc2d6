package imagessearch

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type ImageResult struct {
	Title  string `json:"image_title"`
	URL    string `json:"image_url"`
	Format string `json:"image_format"`
	Height int    `json:"image_height"`
	Width  int    `json:"image_width"`
	LocalImageMetadata
	VisionAnalysis *VisionAnalysisResult `json:"vision_analysis,omitempty"`
}

type VisionAnalysisResult struct {
	Description       string `json:"description"`
	Suitability       string `json:"suitability"`
	SuggestedFilename string `json:"-"` // 不在JSON中返回，仅用于内部文件重命名
}

type LocalImageMetadata struct {
	Downloaded bool   `json:"downloaded"`
	FileSize   int64  `json:"file_size"`
	LocalPath  string `json:"local_path"`
}

type ImageSearchResponse struct {
	Results []*ImageResult `json:"results"`
}

type ImageSearchRequest struct {
	Query string `json:"query"`
	Limit int    `json:"limit"`
}

type ImageSearchAction interface {
	Name() string
	RemoteImageSearch(ctx *iris.AgentRunContext, req *ImageSearchRequest) (*ImageSearchResponse, error)
}

var searchEngines []ImageSearchAction
