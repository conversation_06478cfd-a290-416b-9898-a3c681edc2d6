package imagessearch

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"code.byted.org/bcc/conf_engine/jsoniter"
	"code.byted.org/lang/gg/gptr"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"

	"github.com/mark3labs/mcp-go/client"
	gomcp "github.com/mark3labs/mcp-go/mcp"
)

var (
	// 这里用指针，即使客户端被刷新，旧的客户端已经能保留使用
	googleCli      *client.MCPClient
	googleInitLock sync.RWMutex
)

var (
	GoogleMCPCmd = "npx"
	GoogleMCPEnv = []string{
		"APIFY_TOKEN=**********************************************",
	}
	GoogleMCPArgs = []string{
		"-y",
		"@apify/actors-mcp-server",
		"--actors",
		"devisty/google-image-search",
	}
	GoogleCliInitFiledErr = fmt.Errorf("google image search client init failed. plz try other way, do not try this google image search tool again")
)

func RegisterGoogleEngine() {
	searchEngines = append(searchEngines, &googleImageSearch{})
}

func newGoogleCli(run *iris.AgentRunContext) client.MCPClient {
	cli, err := client.NewStdioMCPClient(
		GoogleMCPCmd,
		GoogleMCPEnv,
		GoogleMCPArgs...)
	if err != nil {
		run.GetLogger().Errorf("[googleImageSearch] NewStdioMCPClient err: %v", err)
		return nil
	}
	// 为 Initialize 操作添加超时控制
	initCtx, initCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer initCancel()
	request := gomcp.InitializeRequest{}
	request.Params.ProtocolVersion = gomcp.LATEST_PROTOCOL_VERSION
	request.Params.ClientInfo = gomcp.Implementation{
		Name:    "google_image_search_client",
		Version: "1.0.0",
	}
	_, err = cli.Initialize(initCtx, request)
	if err != nil {
		run.GetLogger().Errorf("failed to initialize google mcp client, err: %v", err)
		return nil
	}
	return cli
}

// getOrCreateGoogleCli 获取或创建Google客户端，使用读写锁确保并发安全
func getOrCreateGoogleCli(run *iris.AgentRunContext) client.MCPClient {
	// 先用读锁检查
	googleInitLock.RLock()
	if googleCli != nil && *googleCli != nil {
		cli := *googleCli
		googleInitLock.RUnlock()
		return cli
	}
	googleInitLock.RUnlock()

	// 用写锁初始化
	googleInitLock.Lock()
	defer googleInitLock.Unlock()

	// 双重检查
	if googleCli != nil && *googleCli != nil {
		return *googleCli
	}

	// 创建新的client
	googleCli = gptr.Of(newGoogleCli(run))
	return *googleCli
}

type googleImageSearch struct{}

func (*googleImageSearch) Name() string {
	return "google_image_search"
}

func (*googleImageSearch) RemoteImageSearch(run *iris.AgentRunContext, req *ImageSearchRequest) (*ImageSearchResponse, error) {
	// 获取客户端
	cli := getOrCreateGoogleCli(run)
	if cli == nil {
		return nil, iris.NewRecoverable(GoogleCliInitFiledErr)
	}

	// ping检查当前client是否可用
	pingCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	err := cli.Ping(pingCtx)
	cancel()
	if err != nil {
		run.GetLogger().Warnf("[googleImageSearch] ping err: %v", err)
		// 重新初始化客户端
		googleInitLock.Lock()
		googleCli = gptr.Of(newGoogleCli(run))
		cli = *googleCli
		googleInitLock.Unlock()
		if cli == nil {
			return nil, iris.NewRecoverable(GoogleCliInitFiledErr)
		}
	}

	// CallTool "devisty-slash-google-image-search"
	callRequest := gomcp.CallToolRequest{}
	callRequest.Params.Name = "devisty-slash-google-image-search"
	callRequest.Params.Arguments = map[string]interface{}{
		"q":    req.Query,
		"num":  req.Limit,
		"page": 1,
	}
	callCtx, cancelFunc := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancelFunc()
	resp, err := cli.CallTool(callCtx, callRequest)
	if err != nil {
		return nil, iris.NewRecoverable(fmt.Errorf("call google image search tool err: %v", err))
	}
	res := &ImageSearchResponse{}
	for _, content := range resp.Content {
		textContent, ok := content.(gomcp.TextContent)
		if !ok {
			continue
		}
		// 排除非{开头的无用数据
		if !strings.HasPrefix(textContent.Text, "{") {
			continue
		}
		var imageResult googleImageSearchToolResult
		err := jsoniter.UnmarshalFromString(textContent.Text, &imageResult)
		if err != nil {
			run.GetLogger().Errorf("[googleImageSearch] json unmarshal err: %v, text: %s", err, textContent.Text)
			continue
		}
		res.Results = append(res.Results, &ImageResult{
			Title:  imageResult.Title,
			URL:    imageResult.OriginalImageUrl,
			Height: imageResult.Height,
			Width:  imageResult.Width,
		})
	}
	return res, nil
}

type googleImageSearchToolResult struct {
	ThumbnailImageUrl string `json:"thumbnailImageUrl"`
	Height            int    `json:"height"`
	Width             int    `json:"width"`
	Title             string `json:"title"`
	OriginalImageUrl  string `json:"originalImageUrl"`
	ContextLink       string `json:"contextLink"`
}
