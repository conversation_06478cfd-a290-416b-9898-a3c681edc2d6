package imagessearch

import (
	"crypto/md5"
	"errors"
	"fmt"
	"io"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"code.byted.org/lang/gg/gslice"
	"github.com/cenkalti/backoff/v4"
	"gopkg.in/yaml.v3"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

// 错误变量定义
var (
	ErrVisionModel            = errors.New("vision model error")
	ErrUnsupportedImageFormat = errors.New("unsupported image format")
)

const (
	ToolImageSearch            = "image_search"
	ToolImageSearchDescription = "image_search is a tool that finds existing web images and downloading them locally with mandatory AI-powered evaluation. Requires evaluation_prompt to define specific use case requirements. Use when user's requirement is related to images or creating HTML with images, content development, or needs visual materials with local file access."
	searchRetryMaxTimes        = 3
	maxResultCount             = 15 // 限制返回结果数量
	downloadTimeout            = 30 * time.Second
	maxConcurrentDownloads     = 5
	searchRetryInitInterval    = 300 * time.Millisecond

	// 视觉分析并发配置
	maxConcurrentVisionAnalysis = 5
	visionRetryMaxTimes         = 3
	visionRetryInitInterval     = 300 * time.Millisecond

	// 存储目录配置
	imageStorageDir     = "image_search_result"
	maxFilenameLength   = 100
	defaultImageFormat  = "jpg"
	analysisHistoryFile = "image_analysis_history.yaml"
)

type ImageSearchToolResponse struct {
	Results []*ImageResultDTO `json:"results"`
	Guide   string            `json:"guide"`
}

// ImageResultDTO 用于返回给用户的数据传输对象，移除了不需要的字段
type ImageResultDTO struct {
	Title          string                `json:"image_title,omitempty"`
	Height         int                   `json:"image_height"`
	Width          int                   `json:"image_width"`
	FileSize       int64                 `json:"file_size"`
	LocalPath      string                `json:"local_path"`
	VisionAnalysis *VisionAnalysisResult `json:"vision_analysis,omitempty"`
}

// ImageAnalysisRecordDTO 用于分析历史记录的数据传输对象
type ImageAnalysisRecordDTO struct {
	Filename       string                `yaml:"filename"`
	LocalPath      string                `yaml:"local_path"`
	Title          string                `yaml:"title,omitempty"`
	VisionAnalysis *VisionAnalysisResult `yaml:"vision_analysis,omitempty"`
}

type SearchImagesArgs struct {
	Query            string `json:"query" mapstructure:"query" description:"The image search keyword or phrase. Can be in English, Chinese"`
	Num              int    `json:"num" mapstructure:"num" description:"Number of images to search for. default 5, max 15"`
	EvaluationPrompt string `json:"evaluation_prompt" mapstructure:"evaluation_prompt" description:"[Required] prompt for evaluating downloaded images. This is crucial as it defines the specific use case and requirements for image selection. Maximum 300 characters. Examples: 'suitable for business presentation', 'appropriate for children content', 'high quality for website banner', etc."`
}

type downloadTask struct {
	result *ImageResult
	index  int
}

// AnalysisHistoryEntry 分析历史记录条目
type AnalysisHistoryEntry struct {
	Timestamp        time.Time                `yaml:"timestamp"`
	Query            string                   `yaml:"query"`
	EvaluationPrompt string                   `yaml:"evaluation_prompt"`
	Images           []ImageAnalysisRecordDTO `yaml:"images"`
}

// ImageAnalysisRecord 单张图片的分析记录（保留用于向后兼容）
type ImageAnalysisRecord struct {
	Filename       string                `yaml:"filename"`
	LocalPath      string                `yaml:"local_path"`
	OriginalURL    string                `yaml:"original_url"`
	Title          string                `yaml:"title"`
	VisionAnalysis *VisionAnalysisResult `yaml:"vision_analysis,omitempty"`
}

func NewImagesSearchToolset(run *iris.AgentRunContext, step *iris.AgentRunStep) ([]iris.Action, error) {
	return []iris.Action{
		NewImagesSearchTool(),
	}, nil
}

func NewImagesSearchTool() iris.Action {
	return actions.ToTool(ToolImageSearch, ToolImageSearchDescription, SearchImages)
}

func SearchImages(run *iris.AgentRunContext, args SearchImagesArgs) (resp *ImageSearchToolResponse, err error) {
	defer func() {
		if e := recover(); e != nil {
			// panic了，兜底返回工具结果
			err = iris.NewRecoverable(fmt.Errorf("the current tool is temporarily unavailable. Please try other ways"))
			run.GetLogger().Error("[SearchImages] panic: %v", e)
			return
		}
	}()
	// 验证字数限制， 虽然描述中是说300，但是万一超一点也无所谓
	if len([]rune(args.EvaluationPrompt)) > 3000 {
		return nil, iris.NewRecoverable(fmt.Errorf("evaluation_prompt must not exceed 3000 characters, current length: %d", len([]rune(args.EvaluationPrompt))))
	}

	// 初始化
	if args.Num < 1 {
		args.Num = 5
	}
	if args.Num > maxResultCount {
		args.Num = maxResultCount
	}

	// 1. 从所有源中搜素一遍图片，并合并结果
	req := &ImageSearchRequest{
		Query: args.Query,
		// 这里不做拆分，每个工具都按最大去搜素
		Limit: args.Num,
	}
	originResults := [][]*ImageResult{}
	for i := range searchEngines {
		engine := searchEngines[i]
		search, err := engine.RemoteImageSearch(run, req)
		if err != nil {
			run.GetLogger().Error("[SearchImages] %s RemoteImageSearch err: %v", engine.Name(), err)
			continue
		}
		// 确认有结果再加进去
		if search == nil || len(search.Results) == 0 {
			continue
		}
		originResults = append(originResults, search.Results)
	}

	if len(originResults) == 0 {
		return &ImageSearchToolResponse{Guide: "No relevant images found. Try using different keywords, simplifying search terms, or using English keywords."}, nil
	}

	// 根据 originResults 结果，轮询取图片
	originNum := len(originResults)
	results := []*ImageResult{}
	for i := 0; i < args.Num; i++ {
		originResult := originResults[i%originNum]
		index := i / originNum
		if index >= len(originResult) {
			continue
		}
		results = append(results, originResult[index])
	}

	// 2. 下载所有图片到本地
	// 确保存储目录存在
	if err := ensureImageDir(); err != nil {
		return nil, iris.NewRecoverable(fmt.Errorf("创建图片存储目录失败: %w", err))
	}
	// 强制下载所有图片
	if len(results) > 0 {
		downloadImages(results, args.Query)
	}
	// 仅过滤已下载的图片
	results = gslice.Filter(results, func(result *ImageResult) bool {
		return result.Downloaded
	})
	resultCount := len(results)

	// 3. 对下载的图片进行并发视觉分析并根据分析结果重命名
	if len(results) > 0 && args.EvaluationPrompt != "" {
		processVisionAnalysisConcurrently(run, results, args.EvaluationPrompt)

		// 保存分析历史记录
		if err := saveAnalysisHistory(args.Query, args.EvaluationPrompt, results); err != nil {
			run.GetLogger().Warnf("Failed to save analysis history: %v", err)
		}

		// 过滤掉除"文件格式不通过"外的所有错误结果
		results = gslice.Filter(results, func(result *ImageResult) bool {
			if result.VisionAnalysis == nil {
				return false // 移除分析失败的结果
			}
			return true // 保留成功分析的结果（包括"文件格式不通过"的情况）
		})
	}

	// 转换为DTO对象，移除不需要的字段
	dtoResults := make([]*ImageResultDTO, 0, len(results))
	for _, result := range results {
		dto := &ImageResultDTO{
			Height:         result.Height,
			Width:          result.Width,
			FileSize:       result.FileSize,
			LocalPath:      result.LocalPath,
			VisionAnalysis: result.VisionAnalysis,
		}
		// 只有在没有分析结果时才保留Title
		if result.VisionAnalysis == nil {
			dto.Title = result.Title
		}
		dtoResults = append(dtoResults, dto)
	}

	// 返回结果
	resp = &ImageSearchToolResponse{
		Results: dtoResults,
	}
	if resultCount == 0 {
		resp.Guide = "No relevant images found. Try using different keywords, simplifying search terms, or using English keywords."
	} else {
		// Count images with vision analysis
		analyzedCount := 0
		for _, dto := range dtoResults {
			if dto.VisionAnalysis != nil {
				analyzedCount++
			}
		}

		guideBuilder := strings.Builder{}
		guideBuilder.WriteString(fmt.Sprintf("Successfully downloaded %d images to local '%s' directory", resultCount, imageStorageDir))

		if analyzedCount > 0 {
			guideBuilder.WriteString(fmt.Sprintf(", %d of which have completed AI vision analysis.", analyzedCount))
		}

		guideBuilder.WriteString("\n\nField descriptions:\n")
		guideBuilder.WriteString("• local_path: Local file path of the image, ready to use\n")
		guideBuilder.WriteString("• image_title: Image title (only retained when no AI analysis available)\n")
		guideBuilder.WriteString("• image_height/image_width: Image dimension information\n")
		guideBuilder.WriteString("• file_size: File size in bytes\n")

		if analyzedCount > 0 {
			guideBuilder.WriteString("\nAI vision analysis results (important filtering criteria):\n")
			guideBuilder.WriteString("• vision_analysis.description: Detailed AI description of image content, including key visual elements\n")
			guideBuilder.WriteString("• vision_analysis.suitability: Professional suitability assessment based on your evaluation_prompt\n")
			guideBuilder.WriteString("\nFiltering recommendation: Prioritize images with suitability rated as 'suitable' or 'highly suitable', and make final decisions after understanding specific content through description.")
		}

		resp.Guide = guideBuilder.String()
	}

	return resp, nil
}

// ensureImageDir 确保图片存储目录存在
func ensureImageDir() error {
	return os.MkdirAll(imageStorageDir, 0755)
}

// downloadImages 并发下载图片
func downloadImages(results []*ImageResult, query string) {
	taskChan := make(chan downloadTask, len(results))
	var wg sync.WaitGroup

	// 启动下载工作器
	for i := 0; i < maxConcurrentDownloads; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for task := range taskChan {
				downloadSingleImage(task.result, task.index, query)
			}
		}()
	}

	// 提交下载任务
	for i, result := range results {
		taskChan <- downloadTask{result: result, index: i}
	}
	close(taskChan)

	// 等待所有下载完成
	wg.Wait()
}

// downloadSingleImage 下载单个图片
func downloadSingleImage(result *ImageResult, index int, query string) {
	if result.URL == "" {
		return
	}

	// 下载图片
	client := &http.Client{
		Timeout: downloadTimeout,
	}

	resp, err := client.Get(result.URL)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return
	}

	// 从HTTP响应头获取文件格式
	contentType := resp.Header.Get("Content-Type")
	detectedFormat := ""
	if contentType != "" {
		// 从Content-Type中提取文件扩展名
		if mediaType, _, err := mime.ParseMediaType(contentType); err == nil {
			// 特殊处理常见的图片格式
			switch mediaType {
			case "image/jpeg":
				detectedFormat = "jpg"
			case "image/png":
				detectedFormat = "png"
			case "image/gif":
				detectedFormat = "gif"
			case "image/webp":
				detectedFormat = "webp"
			case "image/bmp":
				detectedFormat = "bmp"
			default:
				// 对于其他格式，尝试从mime包获取扩展名
				if exts, err := mime.ExtensionsByType(mediaType); err == nil && len(exts) > 0 {
					detectedFormat = strings.TrimPrefix(exts[0], ".")
				}
			}
		}
	}
	// 如果无法从Content-Type获取，尝试从URL推断
	if detectedFormat == "" {
		ext := strings.ToLower(filepath.Ext(result.URL))
		if ext != "" {
			detectedFormat = strings.TrimPrefix(ext, ".")
		}
	}
	// 最后使用默认格式
	if detectedFormat == "" {
		detectedFormat = defaultImageFormat
	}
	result.Format = detectedFormat

	// 根据检测到的格式生成正确的文件名
	filename := generateSafeFilename(result.Title, result.URL, index, query, detectedFormat)
	localPath := filepath.Join(imageStorageDir, filename)

	// 检查文件是否已存在
	if _, err := os.Stat(localPath); err == nil {
		// 文件已存在，生成新的文件名
		filename = generateUniqueFilename(filename)
		localPath = filepath.Join(imageStorageDir, filename)
	}

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		return
	}
	defer file.Close()

	// 复制数据
	size, err := io.Copy(file, resp.Body)
	if err != nil {
		os.Remove(localPath) // 清理失败的文件
		return
	}

	// 更新结果
	result.LocalPath = localPath
	result.FileSize = size
	result.Downloaded = true
}

// generateSafeFilename 生成安全的文件名
func generateSafeFilename(title, url string, index int, query string, format string) string {
	// 清理标题，移除非法字符
	safeTitle := sanitizeFilename(title)
	if safeTitle == "" {
		safeTitle = sanitizeFilename(query)
	}
	if safeTitle == "" {
		safeTitle = "image"
	}

	// 限制长度（按UTF-8字符安全截断）
	if len(safeTitle) > maxFilenameLength {
		safeTitle = truncateUTF8Safe(safeTitle, maxFilenameLength)
	}

	// 生成时间戳
	timestamp := time.Now().Format("20060102_150405")

	// 生成URL哈希（用于唯一性）
	hash := fmt.Sprintf("%x", md5.Sum([]byte(url)))[:8]

	// 确定文件扩展名
	ext := strings.ToLower(format)
	if ext == "" {
		ext = defaultImageFormat
	}
	if !strings.HasPrefix(ext, ".") {
		ext = "." + ext
	}

	// 组合文件名: 标题_索引_时间戳_哈希.扩展名
	filename := fmt.Sprintf("%s_%d_%s_%s%s", safeTitle, index, timestamp, hash, ext)

	return filename
}

// sanitizeFilename 清理文件名，移除非法字符
func sanitizeFilename(filename string) string {
	// 移除或替换非法字符
	reg := regexp.MustCompile(`[<>:"/\\|?*\x00-\x1f]`)
	safe := reg.ReplaceAllString(filename, "_")

	// 移除不可见字符和非打印字符（包括控制字符）
	safe = strings.Map(func(r rune) rune {
		if r == utf8.RuneError || !strconv.IsPrint(r) {
			return -1 // 丢弃不可打印字符
		}
		return r
	}, safe)

	// 移除多余的空格和点
	safe = strings.TrimSpace(safe)
	safe = strings.Trim(safe, ".")

	// 替换多个连续的下划线或空格
	reg2 := regexp.MustCompile(`[_\s]+`)
	safe = reg2.ReplaceAllString(safe, "_")

	return safe
}

// generateUniqueFilename 生成唯一文件名（当文件已存在时）
func generateUniqueFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	nameWithoutExt := strings.TrimSuffix(originalFilename, ext)

	// 添加新的时间戳和随机哈希
	timestamp := time.Now().Format("150405_000")
	hash := fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s_%d", originalFilename, time.Now().UnixNano()))))[:6]

	return fmt.Sprintf("%s_%s_%s%s", nameWithoutExt, timestamp, hash, ext)
}

// renameFileWithSuggestedName 根据视觉分析的建议文件名重命名文件
func renameFileWithSuggestedName(result *ImageResult, suggestedFilename string) {
	if result.LocalPath == "" || suggestedFilename == "" {
		return
	}

	// 清理建议的文件名
	safeSuggestedName := sanitizeFilename(suggestedFilename)
	if safeSuggestedName == "" {
		return // 如果清理后为空，则不重命名
	}

	// 限制长度为50字符
	if len([]rune(safeSuggestedName)) > 50 {
		safeSuggestedName = truncateUTF8Safe(safeSuggestedName, 50)
	}

	// 获取原文件的扩展名
	originalExt := filepath.Ext(result.LocalPath)
	if originalExt == "" {
		// 如果没有扩展名，使用result.Format
		if result.Format != "" {
			originalExt = "." + result.Format
		} else {
			originalExt = "." + defaultImageFormat
		}
	}

	// 生成新的文件名
	newFilename := safeSuggestedName + originalExt
	newLocalPath := filepath.Join(imageStorageDir, newFilename)

	// 检查新文件名是否已存在
	if _, err := os.Stat(newLocalPath); err == nil {
		// 文件已存在，生成唯一文件名
		newFilename = generateUniqueFilename(newFilename)
		newLocalPath = filepath.Join(imageStorageDir, newFilename)
	}

	// 重命名文件
	if err := os.Rename(result.LocalPath, newLocalPath); err == nil {
		// 重命名成功，更新结果
		result.LocalPath = newLocalPath
	}
	// 如果重命名失败，保持原文件名不变
}

// processVisionAnalysisConcurrently 并发处理视觉分析
func processVisionAnalysisConcurrently(run *iris.AgentRunContext, results []*ImageResult, evaluationPrompt string) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrentVisionAnalysis)

	for _, result := range results {
		if result.LocalPath == "" {
			continue
		}

		wg.Add(1)
		go func(r *ImageResult) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 使用退避重试机制进行视觉分析
			analysis, err := analyzeImageWithVisionRetry(run, r.LocalPath, r.Title, evaluationPrompt)
			if err != nil {
				// 检查是否是不支持的图片格式错误
				if errors.Is(err, ErrUnsupportedImageFormat) {
					// 对于不支持的格式，创建一个特殊的分析结果
					r.VisionAnalysis = &VisionAnalysisResult{
						Description: "details cannot be obtained because the file format is not applicable to the llm model",
					}
				} else {
					// 其他错误，设置为nil，后续会被过滤掉
					run.GetLogger().Errorf("Vision analysis failed for %s: %v", r.LocalPath, err)
					r.VisionAnalysis = nil
				}
				return
			}

			r.VisionAnalysis = analysis

			// 如果视觉分析提供了建议文件名，则重命名文件
			if analysis != nil && analysis.SuggestedFilename != "" {
				renameFileWithSuggestedName(r, analysis.SuggestedFilename)
			}
		}(result)
	}

	wg.Wait()
}

// analyzeImageWithVisionRetry 带重试机制的视觉分析
func analyzeImageWithVisionRetry(run *iris.AgentRunContext, imagePath, originalFilename, evaluationPrompt string) (*VisionAnalysisResult, error) {
	var result *VisionAnalysisResult
	var lastErr error

	// 自定义重试条件：只对ErrVisionModel进行重试
	retryCondition := func(err error) bool {
		return errors.Is(err, ErrVisionModel)
	}

	operation := func() error {
		var err error
		result, err = analyzeImageWithVision(run, imagePath, originalFilename, evaluationPrompt)
		lastErr = err
		// 如果错误不满足重试条件，返回permanent error停止重试
		if err != nil && !retryCondition(err) {
			return backoff.Permanent(err)
		}
		return err
	}

	// 配置退避策略
	bo := backoff.NewExponentialBackOff()
	bo.InitialInterval = visionRetryInitInterval
	bo.MaxElapsedTime = 30 * time.Second
	bo.MaxInterval = 5 * time.Second

	// 执行带重试的操作
	err := backoff.RetryNotify(
		operation,
		backoff.WithMaxRetries(bo, visionRetryMaxTimes),
		func(err error, duration time.Duration) {
			run.GetLogger().Warnf("Vision analysis retry after %v: %v", duration, err)
		},
	)

	if err != nil {
		return nil, lastErr
	}

	return result, nil
}

// saveAnalysisHistory 保存分析历史记录到YAML文件
func saveAnalysisHistory(query, evaluationPrompt string, results []*ImageResult) error {
	// 构建历史记录条目
	entry := AnalysisHistoryEntry{
		Timestamp:        time.Now(),
		Query:            query,
		EvaluationPrompt: evaluationPrompt,
		Images:           make([]ImageAnalysisRecordDTO, 0, len(results)),
	}

	// 转换图片结果为记录格式
	for _, result := range results {
		if result.Downloaded {
			record := ImageAnalysisRecordDTO{
				Filename:       filepath.Base(result.LocalPath),
				LocalPath:      result.LocalPath,
				VisionAnalysis: result.VisionAnalysis,
			}
			// 只有在没有分析结果时才保留Title
			if result.VisionAnalysis == nil {
				record.Title = result.Title
			}
			entry.Images = append(entry.Images, record)
		}
	}

	// 如果没有成功下载的图片，不保存记录
	if len(entry.Images) == 0 {
		return nil
	}

	// 确保images目录存在
	if err := ensureImageDir(); err != nil {
		return fmt.Errorf("failed to ensure image directory: %w", err)
	}

	// 构建历史文件路径
	historyPath := filepath.Join(imageStorageDir, analysisHistoryFile)

	// 读取现有历史记录
	var existingEntries []AnalysisHistoryEntry
	if data, err := os.ReadFile(historyPath); err == nil {
		if err := yaml.Unmarshal(data, &existingEntries); err != nil {
			// 如果解析失败，记录警告但继续
			fmt.Printf("Warning: failed to parse existing history file: %v\n", err)
		}
	}

	// 追加新记录
	existingEntries = append(existingEntries, entry)

	// 序列化为YAML
	yamlData, err := yaml.Marshal(existingEntries)
	if err != nil {
		return fmt.Errorf("failed to marshal analysis history: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(historyPath, yamlData, 0644); err != nil {
		return fmt.Errorf("failed to write analysis history: %w", err)
	}

	return nil
}

// truncateUTF8Safe 安全地截断UTF-8字符串，确保不会在多字节字符中间截断
// 优化版本：时间复杂度O(n)，避免重复验证整个字符串
func truncateUTF8Safe(s string, maxBytes int) string {
	if len(s) <= maxBytes {
		return s
	}

	// 从maxBytes位置向前查找UTF-8字符边界
	// 一个UTF-8字符最多4个字节，所以最多向前查找4个位置
	for i := maxBytes; i >= 0 && maxBytes-i < 4; i-- {
		if utf8.RuneStart(s[i]) {
			// 检查这个位置开始的字符是否完整
			r, size := utf8.DecodeRuneInString(s[i:])
			if r != utf8.RuneError && i+size <= len(s) {
				return s[:i]
			}
		}
	}

	// 如果在4字节范围内找不到有效边界，继续向前查找
	for i := maxBytes - 4; i >= 0; i-- {
		if utf8.RuneStart(s[i]) {
			return s[:i]
		}
	}

	// 如果还是找不到，返回空字符串
	return ""
}
