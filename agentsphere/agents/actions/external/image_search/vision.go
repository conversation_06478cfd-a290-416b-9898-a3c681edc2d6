package imagessearch

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime"
	"os"
	"path/filepath"
	"strings"

	"code.byted.org/lang/gg/gslice"
	"github.com/samber/lo"

	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/streamparser"
)

const (
	visionSystemPrompt = `You are an AI assistant with powerful vision capabilities. You can view images and provide detailed analysis.

Your task is to:
1. Analyze the given image and provide a detailed description (maximum 300 characters)
2. Evaluate the image's suitability based on the user's specific requirements (maximum 300 characters)
3. Suggest an appropriate filename that reflects the image content (maximum 50 characters, no extension)

Please respond in the following JSON format:
{
  "description": "Detailed description of what you see in the image (max 300 chars)",
  "suitability": "Assessment of how well this image meets the user's requirements (max 300 chars)",
  "suggested_filename": "A descriptive filename without extension, max 50 chars (e.g., 'business_meeting_presentation')"
}

IMPORTANT JSON FORMATTING RULES:
- All JSON values MUST be on a single line without line breaks
- Do NOT use newlines within any JSON string values
- Keep all text content continuous without line breaks

Field explanations:
- "description": Objective visual content description (what you literally see)
- "suitability": Subjective evaluation against user's specific requirements (how well it fits their needs)

Be honest and accurate in your assessment. If the image doesn't meet the requirements, explain why. Keep all responses within the specified character limits.`
)

// analyzeImageWithVision 使用视觉模型分析图片
func analyzeImageWithVision(run *iris.AgentRunContext, imagePath string, originalFilename string, evaluationPrompt string) (*VisionAnalysisResult, error) {
	if evaluationPrompt == "" {
		return nil, fmt.Errorf("evaluation prompt is required")
	}

	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("image file not found: %s", imagePath)
	}

	// 检查文件格式
	ext := strings.ToLower(filepath.Ext(imagePath))
	if !gslice.Contains([]string{".jpg", ".jpeg", ".png", ".webp", ".heic", ".heif"}, ext) {
		return nil, fmt.Errorf("%w: %s", ErrUnsupportedImageFormat, ext)
	}

	// 读取图片文件
	file, err := os.Open(imagePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open image file: %v", err)
	}
	defer file.Close()

	content, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read image file: %v", err)
	}

	// 编码为base64
	base64Content := base64.StdEncoding.EncodeToString(content)
	mimeType := mime.TypeByExtension(ext)
	base64Content = fmt.Sprintf("data:%s;base64,%s", mimeType, base64Content)

	// 构建用户提示
	userPrompt := fmt.Sprintf(`Please analyze this image with the following requirements:
%s

Original filename: %s

Please provide your analysis in the requested JSON format.`, evaluationPrompt, originalFilename)

	// 构建消息
	messages := []*framework.ChatMessage{
		{
			Role:    "system",
			Content: visionSystemPrompt,
		},
		{
			Role: "user",
			ContentParts: []*framework.LLMChatMessageContentPart{
				{
					Text: lo.ToPtr(userPrompt),
				},
				{
					ImageURL: lo.ToPtr(base64Content),
				},
			},
			Content: "",
		},
	}

	// 调用视觉模型
	result, err := agents.Think(run, "vision", messages, agents.ThinkOption{
		StreamFilter: streamparser.StreamFilter{},
	})
	if err != nil {
		run.GetLogger().Errorf("Failed to analyze image: %v, image: %s", err, originalFilename)
		// agents.Think调用失败，可能是模型限流，返回特定错误类型用于重试
		return nil, fmt.Errorf("%w: %v", ErrVisionModel, err)
	}

	// 解析JSON响应
	analysisResult := &VisionAnalysisResult{}
	response := strings.TrimSpace(result.Content)

	// 尝试从响应中提取JSON
	if strings.Contains(response, "{") && strings.Contains(response, "}") {
		startIdx := strings.Index(response, "{")
		endIdx := strings.LastIndex(response, "}") + 1
		if startIdx >= 0 && endIdx > startIdx {
			jsonStr := response[startIdx:endIdx]

			// 简单的JSON解析（这里可以使用更robust的JSON库）
			if err := parseVisionResponse(jsonStr, analysisResult); err != nil {
				// 如果JSON解析失败，将整个响应作为描述
				analysisResult.Description = response
				analysisResult.Suitability = "Analysis completed but response format was not as expected"
				analysisResult.SuggestedFilename = "analyzed_image"
			}
		} else {
			analysisResult.Description = response
			analysisResult.Suitability = "Analysis completed"
			analysisResult.SuggestedFilename = "analyzed_image"
		}
	} else {
		analysisResult.Description = response
		analysisResult.Suitability = "Analysis completed"
		analysisResult.SuggestedFilename = "analyzed_image"
	}

	return analysisResult, nil
}

// parseVisionResponse 使用标准JSON解析函数
func parseVisionResponse(jsonStr string, result *VisionAnalysisResult) error {
	// 尝试直接解析JSON
	var jsonResult map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &jsonResult); err != nil {
		// 如果直接解析失败，尝试提取JSON部分
		return parseVisionResponseFallback(jsonStr, result)
	}

	// 提取字段
	if desc, ok := jsonResult["description"].(string); ok {
		result.Description = desc
	}
	if suit, ok := jsonResult["suitability"].(string); ok {
		result.Suitability = suit
	}
	if filename, ok := jsonResult["suggested_filename"].(string); ok {
		result.SuggestedFilename = filename
	}

	return nil
}

// parseVisionResponseFallback 备用的字符串解析函数
func parseVisionResponseFallback(jsonStr string, result *VisionAnalysisResult) error {
	lines := strings.Split(jsonStr, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "\"description\"") {
			if value := extractJSONValue(line); value != "" {
				result.Description = value
			}
		} else if strings.Contains(line, "\"suitability\"") {
			if value := extractJSONValue(line); value != "" {
				result.Suitability = value
			}
		} else if strings.Contains(line, "\"suggested_filename\"") {
			if value := extractJSONValue(line); value != "" {
				result.SuggestedFilename = value
			}
		}
	}
	return nil
}

// extractJSONValue 从JSON行中提取值
func extractJSONValue(line string) string {
	if colonIdx := strings.Index(line, ":"); colonIdx >= 0 {
		value := strings.TrimSpace(line[colonIdx+1:])
		value = strings.Trim(value, ",")
		value = strings.Trim(value, "\"")
		return value
	}
	return ""
}
