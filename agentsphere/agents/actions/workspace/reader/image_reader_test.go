package reader

import (
	"encoding/base64"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

func TestImageFileReader_Read(t *testing.T) {
	// Create test context
	ctx := &iris.AgentRunContext{}
	ctx.AddLogger(iris.DefaultLogger)

	// Create test data directory path
	testDataDir := filepath.Join("testdata", "image")

	tests := []struct {
		name        string
		req         ReadFileRequest
		mimeType    string
		expectError bool
		checkResult func(t *testing.T, resp *ReadFileResult)
	}{
		{
			name: "read JPG image file",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "sample.jpg"),
				ViewType: ViewTypeImage,
			},
			mimeType:    "image/jpeg",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.MetaData.ImageContent)
				assert.Equal(t, "image/jpeg", resp.MetaData.MimeType)

				base64Content := resp.MetaData.ImageContent
				assert.True(t, strings.HasPrefix(base64Content, "data:image/image/jpeg;base64,"), "Should have proper base64 prefix")

				// Verify base64 content is valid
				base64Data := strings.TrimPrefix(base64Content, "data:image/image/jpeg;base64,")
				_, err := base64.StdEncoding.DecodeString(base64Data)
				assert.NoError(t, err, "Base64 content should be valid")

				// FileRange should be empty for image files
				assert.Empty(t, resp.FileRange, "FileRange should be empty for image files")
			},
		},
		{
			name: "read PNG image file",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "sample.png"),
				ViewType: ViewTypeImage,
			},
			mimeType:    "image/png",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.MetaData.ImageContent)
				assert.Equal(t, "image/png", resp.MetaData.MimeType)

				base64Content := resp.MetaData.ImageContent
				assert.True(t, strings.HasPrefix(base64Content, "data:image/image/png;base64,"), "Should have proper base64 prefix")

				// Verify base64 content is valid
				base64Data := strings.TrimPrefix(base64Content, "data:image/image/png;base64,")
				_, err := base64.StdEncoding.DecodeString(base64Data)
				assert.NoError(t, err, "Base64 content should be valid")

				// FileRange should be empty for image files
				assert.Empty(t, resp.FileRange, "FileRange should be empty for image files")
			},
		},
		{
			name: "read non-existent image file",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "nonexistent.jpg"),
				ViewType: ViewTypeImage,
			},
			mimeType:    "image/jpeg",
			expectError: true, // Should error when file doesn't exist
			checkResult: nil,
		},
		{
			name: "read with non-image mime type",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "sample.jpg"),
				ViewType: ViewTypeImage,
			},
			mimeType:    "text/plain", // Wrong mime type
			expectError: true,         // Should error when mime type is not image/*
			checkResult: nil,
		},
	}

	reader := NewImageFileReader()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := reader.Read(ctx, tt.req, tt.mimeType)

			if tt.expectError {
				assert.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.NotNil(t, resp)

			if tt.checkResult != nil {
				tt.checkResult(t, resp)
			}
		})
	}
}
