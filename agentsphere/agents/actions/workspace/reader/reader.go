package reader

import (
	"embed"
	"fmt"
	"mime"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

const ReadActionName = "read"
const ReadActionDescription = `Read a file from the local filesystem. You can access any text/data based file directly by using this tool.
If the User provides a path to a file assume that path is valid. It is okay to read a file that does not exist; an error will be returned.

Usage:
- Prefer this tool over shell commands for file reading
- The file_path parameter must be a relative path, not an absolute path
- For text files: supports text-based or line-oriented formats, use view_type='text'
	- By default, it reads up to 2000 lines starting from the beginning of the file
	- You can optionally specify a line range (especially handy for long files), but it's recommended to read the whole file by not providing these parameters
	- Total content longer than 30000 characters will be truncated
	- Results are returned using cat -n format, with line numbers starting at 1
- For general data files (JSON, XML, TSV, YAML, CSV), use view_type='data' to preview the data
	- For excel file(.xlsx), it will preview the excel file, return the sheet count, sheet name, column count, column name, column data type, and preview data
	- For JSON, XML, TSV, YAML, CSV, it will return the column count, column name, column data type, and preview data. Use view_type='text' to read the raw content
	- Line range and page range are not supported for data files
- For pdf, docx, and pptx files: use view_type='text' to read text content
	- Use page_range for PDF, Word, and PowerPoint files to specify which page range to read (e.g., [1, 3] for pages 1-3, [1, 1] for single page)
- For image files, use vision tool instead
`

type FileReader interface {
	Read(ctx *iris.AgentRunContext, req ReadFileRequest, mimeType string) (*ReadFileResult, error)
}

type BaseFileReader struct {
}

func NewBaseFileReader() BaseFileReader {
	return BaseFileReader{}
}

type ReadFileActionOutput struct {
	ReadFileResult *ReadFileResult `json:"result" mapstructure:"result"`
	RawContent     string          `json:"raw_content" mapstructure:"raw_content"`
	Message        string          `json:"message" mapstructure:"message"`
}

func NewReadAction() iris.Action {
	return actions.ToTool(ReadActionName, ReadActionDescription, func(c *iris.AgentRunContext, args ReadFileRequest) (*ReadFileActionOutput, error) {
		e := workspace.GetEditor(c)
		normalizedPath := normalizePath(args.FilePath, e.WorkingDirectory)
		args.FilePath = normalizedPath

		// Check if file exists before proceeding
		if _, err := os.Stat(args.FilePath); err != nil {
			if os.IsNotExist(err) {
				return nil, errors.Errorf("file does not exist: %s", args.FilePath)
			}
			return nil, errors.Wrapf(err, "failed to access file: %s", args.FilePath)
		}

		mimeType, err := GetMimeType(args.FilePath)
		if err != nil {
			return nil, err
		}

		var reader FileReader
		switch args.ViewType {
		case ViewTypeText:
			reader = NewTextFileReader()
		case ViewTypeData:
			reader = NewDataFileReader()
		case ViewTypeImage:
			return nil, errors.New("This tool can not read image, try `vision` tool instead")
		default:
			return nil, errors.New("unsupported view type: " + string(args.ViewType))
		}
		result, err := reader.Read(c, args, mimeType)
		if err != nil {
			return nil, err
		}
		return &ReadFileActionOutput{
			ReadFileResult: result,
			RawContent:     result.RawContent(args.ViewType),
			Message:        result.String(args.ViewType),
		}, nil
	})
}

func (b BaseFileReader) CreateMetaData(filePath string, mimeType string) MetaData {
	stat, err := os.Stat(filePath)
	if err != nil {
		return MetaData{}
	}

	return MetaData{
		FileName: filePath,
		MimeType: mimeType,
		FileType: filepath.Ext(filePath),
		FileSize: stat.Size(),
	}
}

func GetMimeType(filePath string) (string, error) {
	ext := strings.ToLower(filepath.Ext(filePath))
	mimeType := mime.TypeByExtension(ext)

	return mimeType, nil
}

func normalizePath(path, workingDirectory string) string {
	if filepath.IsAbs(path) {
		return filepath.Clean(path)
	}
	return filepath.Join(workingDirectory, path)
}

func createTempCommand(filePath string, fs embed.FS) (string, error) {
	// Create temporary Python file
	tempFileName := fmt.Sprintf("temp_command_%s_%d.py",
		strings.ReplaceAll(time.Now().Format(time.RFC3339), ":", "-"),
		time.Now().UnixNano())

	// Read the embedded Python script content
	pythonCode, err := fs.ReadFile(filePath)
	if err != nil {
		return "", errors.Wrap(err, "failed to read embedded Python script")
	}

	if err := os.WriteFile(tempFileName, pythonCode, 0644); err != nil {
		return "", errors.Wrap(err, "failed to create temporary Python file")
	}

	return tempFileName, nil
}
