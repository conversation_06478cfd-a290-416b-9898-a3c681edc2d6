package reader

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

func TestDataFileReader_readExcel(t *testing.T) {
	tests := []struct {
		name        string
		filePath    string
		expectError bool
		validate    func(t *testing.T, resp *ReadFileResult)
	}{
		{
			name:        "read sample excel file",
			filePath:    "testdata/data/sample.xlsx",
			expectError: false,
			validate: func(t *testing.T, resp *ReadFileResult) {
				analysis := resp.DataExcelAnalysis
				assert.NotNil(t, analysis)
				assert.Equal(t, "testdata/data/sample.xlsx", analysis.FileName)
				assert.GreaterOrEqual(t, analysis.SheetCount, 1)

				if len(analysis.Sheets) > 0 {
					sheet := analysis.Sheets[0]
					assert.NotEmpty(t, sheet.Name)
					assert.GreaterOrEqual(t, sheet.RowCount, 0)
					assert.GreaterOrEqual(t, sheet.ColCount, 0)
				}
			},
		},
		{
			name:        "non-existent file",
			filePath:    "testdata/data/non_existent.xlsx",
			expectError: true,
		},
	}

	reader := NewDataFileReader()
	ctx := &iris.AgentRunContext{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := ReadFileRequest{
				FilePath: tt.filePath,
				ViewType: ViewTypeData,
			}

			resp, err := reader.Read(ctx, req, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, resp)
				return
			}

			fmt.Println(resp.String(ViewTypeData))

			require.NoError(t, err)
			require.NotNil(t, resp)

			if tt.validate != nil {
				tt.validate(t, resp)
			}
		})
	}
}

func TestDataFileReader_readJson(t *testing.T) {
	tests := []struct {
		name        string
		filePath    string
		expectError bool
		validate    func(t *testing.T, resp *ReadFileResult)
	}{
		{
			name:        "read sample excel file",
			filePath:    "testdata/data/sample.json",
			expectError: false,
			validate: func(t *testing.T, resp *ReadFileResult) {
				analysis := resp.DataFileAnalysis
				assert.NotNil(t, analysis)
				assert.Equal(t, "testdata/data/sample.json", analysis.FileName)

			},
		},
		{
			name:        "non-existent file",
			filePath:    "testdata/data/non_existent.json",
			expectError: true,
		},
	}

	reader := NewDataFileReader()
	ctx := &iris.AgentRunContext{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := ReadFileRequest{
				FilePath: tt.filePath,
				ViewType: ViewTypeData,
			}

			resp, err := reader.Read(ctx, req, "application/json")

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, resp)
				return
			}

			fmt.Println(resp.String(ViewTypeData))

			require.NoError(t, err)
			require.NotNil(t, resp)

			if tt.validate != nil {
				tt.validate(t, resp)
			}
		})
	}
}
