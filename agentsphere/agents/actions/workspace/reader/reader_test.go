package reader

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetMimeType(t *testing.T) {
	tests := []struct {
		name         string
		filePath     string
		expectedMime string
		shouldError  bool
	}{
		{
			name:         "text file",
			filePath:     "testdata/text/sample.txt",
			expectedMime: "text/plain; charset=utf-8",
			shouldError:  false,
		},
		{
			name:         "pdf file",
			filePath:     "testdata/text/sample.pdf",
			expectedMime: "application/pdf",
			shouldError:  false,
		},
		{
			name:         "docx file",
			filePath:     "testdata/text/sample.docx",
			expectedMime: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			shouldError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mimeType, err := GetMimeType(tt.filePath)

			if tt.shouldError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			assert.Equal(t, tt.expectedMime, mimeType)
		})
	}
}
