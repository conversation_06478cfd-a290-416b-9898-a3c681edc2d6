#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Extract text from selected slides of a PowerPoint presentation.

Requirements:
    pip install python-pptx

Usage examples:
    python pptx2text.py presentation.pptx -p 1-2
    python pptx2text.py presentation.pptx -p 2-3,5,8-10
    python pptx2text.py presentation.pptx -p 1- --include-notes
"""

import argparse
import sys
from typing import List, Set, Tuple
from pptx import Presentation


def parse_ranges(expr: str, max_slide: int) -> List[int]:
    """
    Parse a slide range expression into a sorted, unique list of 1-based slide numbers.
    Examples:
        "5" -> [5]
        "1-3" -> [1,2,3]
        "-3" -> [1,2,3]
        "5-" -> [5,6,...,max_slide]
        "1-2,5,8-10" -> merged and deduped
    """
    slides: Set[int] = set()
    parts = [p.strip() for p in expr.split(",") if p.strip()]
    for part in parts:
        if "-" in part:
            a, b = part.split("-", 1)
            a = a.strip()
            b = b.strip()
            if a == "" and b == "":
                continue
            if a == "":
                start = 1
            else:
                try:
                    start = int(a)
                except ValueError:
                    raise ValueError(f"Invalid start in range: {part}")
            if b == "":
                end = max_slide
            else:
                try:
                    end = int(b)
                except ValueError:
                    raise ValueError(f"Invalid end in range: {part}")
        else:
            try:
                start = end = int(part)
            except ValueError:
                raise ValueError(f"Invalid slide number: {part}")
        # normalize boundaries
        start = max(1, start)
        end = min(max_slide, end)
        if start > end:
            continue
        slides.update(range(start, end + 1))
    result = sorted(s for s in slides if 1 <= s <= max_slide)
    if not result:
        raise ValueError(
            f"The slide range is invalid. This presentation has {max_slide} slides."
        )
    return result


def extract_text_from_slide(slide, include_notes: bool = False) -> str:
    """Extract text content from a single slide."""
    text_content = []

    # Extract text from shapes
    for shape in slide.shapes:
        if hasattr(shape, "text") and shape.text.strip():
            text_content.append(shape.text.strip())

    # Extract notes if requested
    if include_notes and slide.has_notes_slide:
        notes_slide = slide.notes_slide
        for shape in notes_slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                # Skip the slide content placeholder in notes
                if shape.text.strip() not in text_content:
                    text_content.append(f"[Notes] {shape.text.strip()}")

    return "\n".join(text_content)


def extract_text_from_slides(
    prs: Presentation,
    slides_1based: List[int],
    include_notes: bool = False,
) -> List[Tuple[int, str]]:
    """
    Return list of (slide_no, content) for each selected slide.
    """
    outputs: List[Tuple[int, str]] = []
    for slide_no in slides_1based:
        slide = prs.slides[slide_no - 1]  # Convert to 0-based index
        content = extract_text_from_slide(slide, include_notes)
        outputs.append((slide_no, content))
    return outputs


def main():
    parser = argparse.ArgumentParser(
        description="Extract text from selected slides of a PowerPoint presentation.",
        add_help=False,  # Disable help to avoid usage display
    )
    parser.add_argument("pptx", help="Input PowerPoint file path")
    parser.add_argument(
        "-p",
        "--pages",
        required=True,
        help="Slide ranges, e.g. 1-2,5,8-10 or 3- (1-based). Use - for open ends.",
    )
    parser.add_argument(
        "--include-notes",
        action="store_true",
        help="Include speaker notes in the output",
    )
    args = parser.parse_args()

    try:
        prs = Presentation(args.pptx)
        max_slide = len(prs.slides)
    except Exception as e:
        print(f"Failed to open PowerPoint file: {e}", file=sys.stderr)
        sys.exit(1)

    try:
        slides = parse_ranges(args.pages, max_slide)
    except Exception as e:
        print(f"Slide range error: {e}", file=sys.stderr)
        sys.exit(1)

    try:
        outputs = extract_text_from_slides(
            prs, slides, include_notes=args.include_notes
        )
    except Exception as e:
        print(f"Extraction failed: {e}", file=sys.stderr)
        sys.exit(1)

    # Compose final output
    sep = "\n" + ("-" * 30) + "\n"
    combined = []
    for slide_no, content in outputs:
        header = f"[Slide {slide_no}]"
        combined.append(header)
        combined.append(content if content else "")
    final_text = sep.join(combined) + "\n"
    final_text += f"\n[Total pages in PowerPoint: {max_slide}]"

    sys.stdout.write(final_text)


if __name__ == "__main__":
    main()
