#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Extract text from selected pages of a PDF using PyMuPDF (fitz).

Requirements:
    pip install pymupdf

Usage examples:
    python extract_pdf_text.py input.pdf -p 1-2
    python extract_pdf_text.py input.pdf -p 2-3 -m markdown -o out.md --sort
    python extract_pdf_text.py input.pdf -p 1-2,5,8-10 --clip 50 50 550 750 -m html
"""

import argparse
import sys
from typing import List, Optional, Tuple, Set
import pymupdf  # PyMuPDF


def parse_ranges(expr: str, max_page: int) -> List[int]:
    """
    Parse a page range expression into a sorted, unique list of 1-based page numbers.
    Examples:
        "5" -> [5]
        "1-3" -> [1,2,3]
        "-3" -> [1,2,3]
        "5-" -> [5,6,...,max_page]
        "1-2,5,8-10" -> merged and deduped
    """
    pages: Set[int] = set()
    parts = [p.strip() for p in expr.split(",") if p.strip()]
    for part in parts:
        if "-" in part:
            a, b = part.split("-", 1)
            a = a.strip()
            b = b.strip()
            if a == "" and b == "":
                continue
            if a == "":
                start = 1
            else:
                try:
                    start = int(a)
                except ValueError:
                    raise ValueError(f"Invalid start in range: {part}")
            if b == "":
                end = max_page
            else:
                try:
                    end = int(b)
                except ValueError:
                    raise ValueError(f"Invalid end in range: {part}")
        else:
            try:
                start = end = int(part)
            except ValueError:
                raise ValueError(f"Invalid page number: {part}")
        # normalize boundaries
        start = max(1, start)
        end = min(max_page, end)
        if start > end:
            continue
        pages.update(range(start, end + 1))
    result = sorted(p for p in pages if 1 <= p <= max_page)
    if not result:
        raise ValueError(f"The page range is invalid. This PDF has {max_page} pages.")
    return result


def extract_text_from_pages(
    doc: pymupdf.Document,  # 改为传入已打开的文档
    pages_1based: List[int],
    mode: str = "text",
    sort: bool = False,
) -> List[Tuple[int, str]]:
    """
    Return list of (page_no, content) for each selected page.
    mode passes to page.get_text(mode, ...)
    """
    outputs: List[Tuple[int, str]] = []
    for pno in pages_1based:
        page = doc[pno - 1]
        content = page.get_text(mode, sort=sort)
        # page.get_text("dict"/"json") returns dict/str; ensure str
        if isinstance(content, dict):
            import json

            content = json.dumps(content, ensure_ascii=False, indent=2)
        elif not isinstance(content, str):
            content = str(content)
        outputs.append((pno, content))
    return outputs


def main():
    parser = argparse.ArgumentParser(
        description="Extract PDF text from selected pages using PyMuPDF.",
        add_help=False,  # Disable help to avoid usage display
    )
    parser.add_argument("pdf", help="Input PDF file path")
    parser.add_argument(
        "-p",
        "--pages",
        required=True,
        help="Page ranges, e.g. 1-2,5,8-10 or 3- (1-based). Use - for open ends.",
    )
    parser.add_argument(
        "-m",
        "--mode",
        default="text",
        choices=[
            "text",
            "blocks",
            "words",
            "lines",
            "dict",
            "rawdict",
            "json",
            "html",
            "xhtml",
            "markdown",
        ],
        help="Extraction mode (default: text)",
    )
    parser.add_argument(
        "--sort", action="store_true", help="Sort text in reading order when applicable"
    )
    parser.add_argument(
        "-o", "--out", default="-", help="Output file path (default: - for stdout)"
    )
    args = parser.parse_args()

    try:
        doc = pymupdf.open(args.pdf)
        max_page = doc.page_count
    except Exception as e:
        print(f"Failed to open PDF: {e}", file=sys.stderr)
        sys.exit(1)

    try:
        pages = parse_ranges(args.pages, max_page)
    except Exception as e:
        doc.close()
        print(f"Page range error: {e}", file=sys.stderr)
        sys.exit(1)

    try:
        outputs = extract_text_from_pages(doc, pages, mode=args.mode, sort=args.sort)
    except Exception as e:
        doc.close()
        print(f"Extraction failed: {e}", file=sys.stderr)
        sys.exit(1)

    doc.close()

    # Compose final output
    sep = "\n" + ("-" * 30) + "\n"
    combined = []
    for pno, content in outputs:
        header = f"[Page {pno}]"
        combined.append(header)
        combined.append(content if content else "")
    final_text = sep.join(combined) + "\n"
    final_text += f"\n[Total pages in PDF: {max_page}]"

    if args.out == "-" or not args.out:
        sys.stdout.write(final_text)
    else:
        with open(args.out, "w", encoding="utf-8") as f:
            f.write(final_text)


if __name__ == "__main__":
    main()
