package reader

import (
	"fmt"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
)

type ReadFileRequest struct {
	FilePath  string   `mapstructure:"file_path" json:"file_path" description:"Relative path of the file"`
	ViewType  ViewType `mapstructure:"view_type" json:"view_type" description:"The type of content to read, can be 'text' (text) or 'data' (data)"`
	LineRange *[2]int  `mapstructure:"line_range" json:"line_range,omitempty" description:"Only applies to text files, specifies the line range to read, e.g. [0, 200] means read first 200 lines. Negative values count from end of file"`
	PageRange *[2]int  `mapstructure:"page_range" json:"page_range,omitempty" description:"Only applies to PDF, Word and PowerPoint files, specifies the page range to read, e.g. [1, 3] means read pages 1 to 3. If view_type is 'image' and not specified, defaults to first 5 pages"`
}

type ViewType string

const (
	ViewTypeText  ViewType = "text"
	ViewTypeImage ViewType = "image"
	ViewTypeData  ViewType = "data"
)

type FileContent struct {
	Line    int    `json:"line"`
	Content string `json:"content"`
}

type ColumnInfo struct {
	Index        int      `json:"index,omitempty" mapstructure:"index"`
	Name         string   `json:"name,omitempty" mapstructure:"name"`
	DataType     string   `json:"data_type,omitempty" mapstructure:"data_type"`
	SampleValues []string `json:"sample_values,omitempty" mapstructure:"sample_values"`
	NullCount    int      `json:"null_count,omitempty" mapstructure:"null_count"`
}

type MetaData struct {
	FileName string `mapstructure:"file_name" json:"file_name"`
	MimeType string `mapstructure:"mime_type" json:"mime_type"`
	FileType string `mapstructure:"file_type" json:"file_type"`
	FileSize int64  `mapstructure:"file_size" json:"file_size"`
	Hint     string `json:"hint,omitempty" mapstructure:"hint"`

	// Line based pagination
	TotalLines int `mapstructure:"total_lines" json:"total_lines,omitempty"`
	StartLine  int `mapstructure:"start_line" json:"start_line,omitempty"`
	EndLine    int `mapstructure:"end_line" json:"end_line,omitempty"`

	// Page based pagination
	TotalPages int `mapstructure:"total_pages" json:"total_pages,omitempty"`
	StartPage  int `mapstructure:"start_page" json:"start_page,omitempty"`
	EndPage    int `mapstructure:"end_page" json:"end_page,omitempty"`

	// Image content in base64 format, e.g. data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
	ImageContent string `mapstructure:"image_content" json:"image_content,omitempty"`
}

type ReadFileResult struct {
	FileRange []FileContent `mapstructure:"file_range" json:"file_range"`
	MetaData  MetaData      `mapstructure:"meta_data" json:"meta_data"`

	DataExcelAnalysis *workspace.ExcelAnalysis `mapstructure:"data_excel_analysis" json:"data_excel_analysis"`
	DataFileAnalysis  *workspace.FileAnalysis  `mapstructure:"data_file_analysis" json:"data_file_analysis"`
}

func (r *ReadFileResult) String(viewType ViewType) string {
	switch viewType {
	case ViewTypeText:
		var result strings.Builder
		// paged based file
		if r.MetaData.TotalLines == 0 {
			for _, content := range r.FileRange {
				result.WriteString(fmt.Sprintf("%s\n", content.Content))
			}
			return result.String()
		}

		// line based file
		if len(r.FileRange) == 0 {
			result.WriteString("Notice: The the specified line range has no content.\n")
		}
		if len(r.FileRange) > 0 {
			first := r.FileRange[0]
			last := r.FileRange[len(r.FileRange)-1]

			result.WriteString(fmt.Sprintf(`<file path="%s" total_lines="%d" start_line="%d" end_line="%d">`,
				r.MetaData.FileName, r.MetaData.TotalLines, first.Line, last.Line))
			result.WriteString("\n")

			for _, content := range r.FileRange {
				result.WriteString(fmt.Sprintf("【%d】%s\n", content.Line, content.Content))
			}

			result.WriteString("</file>\n")
			result.WriteString("Notice: The prefix `【xx】` is the line number, and is not part of the line content.")
		}

		return result.String()
	case ViewTypeData:
		if r.DataExcelAnalysis != nil {
			return r.DataExcelAnalysis.String()
		}
		if r.DataFileAnalysis != nil {
			return r.DataFileAnalysis.String()
		}
		return ""
	case ViewTypeImage:
		return ""
	default:
		return ""
	}
}

// RawContent is to display the raw content of the file without any formatting
func (r *ReadFileResult) RawContent(viewType ViewType) string {
	switch viewType {
	case ViewTypeText:
		var result strings.Builder
		for _, content := range r.FileRange {
			result.WriteString(content.Content)
			result.WriteString("\n")
		}
		return result.String()
	case ViewTypeData:
		if r.DataExcelAnalysis != nil {
			return r.DataExcelAnalysis.String()
		}
		if r.DataFileAnalysis != nil {
			return r.DataFileAnalysis.String()
		}
		return ""
	case ViewTypeImage:
		return ""
	default:
		return ""
	}
}
