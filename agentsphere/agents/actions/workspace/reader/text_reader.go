package reader

import (
	"context"
	"embed"
	"fmt"
	"os"
	"os/exec"
	"strings"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

var (
	//go:embed tools/pdf2text.py
	pdf2textCode embed.FS
	//go:embed tools/pptx2text.py
	pptx2textCode embed.FS
)

const (
	DefaultCharsPerPage = 5000
	DefaultMaxPages     = 5
	DefaultMaxLines     = 2000
)

type TextFileReader struct {
	BaseFileReader
}

func NewTextFileReader() FileReader {
	return &TextFileReader{
		BaseFileReader: NewBaseFileReader(),
	}
}

func (t *TextFileReader) Read(ctx *iris.AgentRunContext, req ReadFileRequest, mimeType string) (*ReadFileResult, error) {
	metadata := t.CreateMetaData(req.<PERSON>, mimeType)

	switch mimeType {
	case "application/pdf":
		return t.readPDFAsText(ctx, req, metadata)
	case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
		return t.readDocxAsText(ctx, req, metadata)
	case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
		return t.readPptxAsText(ctx, req, metadata)
	case "text/plain":
		return t.readPlainText(ctx, req, metadata)
	default:
		return t.readPlainText(ctx, req, metadata)
	}

}

func (t *TextFileReader) readPlainText(ctx *iris.AgentRunContext, req ReadFileRequest, metadata MetaData) (*ReadFileResult, error) {
	if req.LineRange == nil || (req.LineRange[0] == 0 && req.LineRange[1] == 0) {
		req.LineRange = &[2]int{0, DefaultMaxLines}
	}

	f, err := workspace.GetEditor(ctx).ReadFileRangeOptimzeForLLM(workspace.ReadFileRangeArgs{
		Path:      req.FilePath,
		StartLine: req.LineRange[0],
		EndLine:   req.LineRange[1],
	})
	if err != nil {
		return nil, err
	}

	lines := make([]FileContent, len(f.Lines))
	for i, line := range f.Lines {
		lines[i] = FileContent{
			Line:    line.Line,
			Content: line.Content,
		}
	}

	metadata.TotalLines = f.Total
	if len(lines) > 0 {
		metadata.StartLine = lines[0].Line
		metadata.EndLine = lines[len(lines)-1].Line
	}

	return &ReadFileResult{
		FileRange: lines,
		MetaData:  metadata,
	}, nil
}

func (t *TextFileReader) readPDFAsText(ctx *iris.AgentRunContext, req ReadFileRequest, metadata MetaData) (*ReadFileResult, error) {
	tempFileName, err := createTempCommand("tools/pdf2text.py", pdf2textCode)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create temporary Python file")
	}
	defer os.Remove(tempFileName) // Clean up temp file

	var pageArg string
	if req.PageRange != nil && req.PageRange[0] > 0 && req.PageRange[1] > 0 {
		pageArg = fmt.Sprintf("%d-%d", req.PageRange[0], req.PageRange[1])
	} else {
		pageArg = fmt.Sprintf("1-%d", DefaultMaxPages)
	}

	cmd := exec.CommandContext(context.Background(), "python3", tempFileName, req.FilePath, "-p", pageArg)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, errors.Wrapf(err, "failed to execute PDF extraction: %s", string(output))
	}

	// Parse output and create response
	lines := strings.Split(string(output), "\n")
	fileContents := make([]FileContent, 0, len(lines))

	for i, line := range lines {
		fileContents = append(fileContents, FileContent{
			Line:    i + 1,
			Content: line,
		})
	}

	return &ReadFileResult{
		FileRange: fileContents,
		MetaData:  metadata,
	}, nil
}

func (t *TextFileReader) readDocxAsText(ctx *iris.AgentRunContext, req ReadFileRequest, metadata MetaData) (*ReadFileResult, error) {
	// Use docx2txt command to convert Word document to text
	cmd := exec.CommandContext(context.Background(), "docx2txt", req.FilePath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, errors.Wrapf(err, "failed to execute docx2txt: %s", string(output))
	}

	// 获取完整文本内容并应用分页逻辑
	fullText := string(output)
	totalPages, startPage, endPage, pageContent, err := t.applyPagination(fullText, req, &metadata)
	if err != nil {
		return nil, err
	}

	// 按行分割内容
	lines := strings.Split(pageContent, "\n")
	fileContents := make([]FileContent, 0, len(lines))

	fileContents = append(fileContents, FileContent{
		Content: fmt.Sprintf("[Content from page %d to %d]", startPage, endPage),
	})
	fileContents = append(fileContents, FileContent{
		Content: "----------------------------------------",
	})

	for _, line := range lines {
		fileContents = append(fileContents, FileContent{
			Content: line,
		})
	}

	fileContents = append(fileContents, FileContent{
		Content: "----------------------------------------",
	})
	fileContents = append(fileContents, FileContent{
		Content: fmt.Sprintf("[Total pages in docx: %d]", totalPages),
	})

	metadata.TotalPages = totalPages
	metadata.StartPage = startPage
	metadata.EndPage = endPage

	return &ReadFileResult{
		FileRange: fileContents,
		MetaData:  metadata,
	}, nil
}

func (t *TextFileReader) readPptxAsText(ctx *iris.AgentRunContext, req ReadFileRequest, metadata MetaData) (*ReadFileResult, error) {
	tempFileName, err := createTempCommand("tools/pptx2text.py", pptx2textCode)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create temporary Python file")
	}
	defer os.Remove(tempFileName) // Clean up temp file

	var pageArg string
	if req.PageRange != nil && req.PageRange[0] > 0 && req.PageRange[1] > 0 {
		pageArg = fmt.Sprintf("%d-%d", req.PageRange[0], req.PageRange[1])
	} else {
		pageArg = fmt.Sprintf("1-%d", DefaultMaxPages)
	}

	cmd := exec.CommandContext(context.Background(), "python3", tempFileName, req.FilePath, "-p", pageArg)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, errors.Wrapf(err, "failed to execute PowerPoint extraction: %s", string(output))
	}

	// Parse output and create response
	lines := strings.Split(string(output), "\n")
	fileContents := make([]FileContent, 0, len(lines))

	for i, line := range lines {
		fileContents = append(fileContents, FileContent{
			Line:    i + 1,
			Content: line,
		})
	}

	return &ReadFileResult{
		FileRange: fileContents,
		MetaData:  metadata,
	}, nil
}

// applyPagination 应用分页逻辑到文本内容，仅针对没有明确分页概念的文件，例如 docx
func (t *TextFileReader) applyPagination(fullText string, req ReadFileRequest, metadata *MetaData) (totalPages int, startPage int, endPage int, content string, err error) {
	totalChars := len(fullText)

	// 计算总页数
	totalPages = (totalChars + DefaultCharsPerPage - 1) / DefaultCharsPerPage
	metadata.TotalPages = totalPages

	// 确定要读取的页数范围
	startPage = 1
	endPage = totalPages
	if totalPages > DefaultMaxPages {
		endPage = DefaultMaxPages
	}
	if req.PageRange != nil {
		startPage = req.PageRange[0]
		endPage = req.PageRange[1]

		// 边界检查
		if startPage < 1 || endPage < 1 {
			return totalPages, 0, 0, "", errors.New("page numbers must be positive")
		}
		if startPage > endPage {
			return totalPages, 0, 0, "", errors.Errorf("invalid page range: start page %d > end page %d", startPage, endPage)
		}
		if startPage > totalPages {
			return totalPages, 0, 0, "", errors.Errorf("start page %d exceeds total pages %d", startPage, totalPages)
		}
		if endPage > totalPages {
			endPage = totalPages
		}
	}

	// 设置元数据
	metadata.StartPage = startPage
	metadata.EndPage = endPage

	// 计算字符范围 - 需要读取整个页数范围
	startChar := (startPage - 1) * DefaultCharsPerPage
	endChar := endPage * DefaultCharsPerPage
	if endChar > totalChars {
		endChar = totalChars
	}

	// 返回指定页范围的内容
	return totalPages, startPage, endPage, fullText[startChar:endChar], nil
}
