package reader

import (
	"fmt"
	"os/exec"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

// checkPyMuPDFAvailable checks if pymupdf is installed and available
func checkPyMuPDFAvailable() bool {
	cmd := exec.Command("python3", "-c", "import pymupdf")
	return cmd.Run() == nil
}

// checkPythonPptxAvailable checks if python-pptx is installed and available
func checkPythonPptxAvailable() bool {
	cmd := exec.Command("python3", "-c", "import python-pptx")
	return cmd.Run() == nil
}

func checkPythonDocxAvailable() bool {
	cmd := exec.Command("python3", "-c", "import docx2txt")
	return cmd.Run() == nil
}

func TestTextFileReader_Read_PDF(t *testing.T) {
	// Skip test if pymupdf is not available
	if !checkPyMuPDFAvailable() {
		t.Skip("pymupdf is not installed, skipping PDF tests")
	}

	// Create test context
	ctx := &iris.AgentRunContext{}

	// Create test data directory path
	testDataDir := filepath.Join("testdata", "text")

	tests := []struct {
		name        string
		req         ReadFileRequest
		mimeType    string
		expectError bool
		checkResult func(t *testing.T, resp *ReadFileResult)
	}{

		{
			name: "read PDF file with default page range",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "sample.pdf"),
				ViewType: ViewTypeText,
				// No PageRange specified, should default to first 5 pages
			},
			mimeType:    "application/pdf",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.FileRange)
				assert.Equal(t, "application/pdf", resp.MetaData.MimeType)
				assert.True(t, len(resp.FileRange) > 150)

			},
		},
		{
			name: "read PDF file with specific page range",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.pdf"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{1, 1},
			},
			mimeType:    "application/pdf",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.FileRange)
				assert.Equal(t, "application/pdf", resp.MetaData.MimeType)
				assert.True(t, len(resp.FileRange) == 52)
			},
		},
		{
			name: "read PDF file with default page range",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "sample.pdf"),
				ViewType: ViewTypeText,
				// No PageRange specified, should default to first 5 pages
			},
			mimeType:    "application/pdf",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.FileRange)
				assert.Equal(t, "application/pdf", resp.MetaData.MimeType)
				assert.True(t, len(resp.FileRange) > 150)

			},
		},
		{
			name: "read PDF file with page range beyond file size",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.pdf"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{100, 200}, // Pages beyond actual PDF size
			},
			mimeType:    "application/pdf",
			expectError: true, // Should error when pages don't exist
			checkResult: nil,
		},
		{
			name: "read PDF with invalid page range",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.pdf"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{5, 1}, // Invalid: start > end
			},
			mimeType:    "application/pdf",
			expectError: true, // Should error when page range is invalid
		},
	}

	reader := NewTextFileReader()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := reader.Read(ctx, tt.req, tt.mimeType)

			if tt.expectError {
				assert.Error(t, err)
				return
			}
			fmt.Println(resp)

			require.NoError(t, err)
			require.NotNil(t, resp)

			if tt.checkResult != nil {
				tt.checkResult(t, resp)
			}
		})
	}
}

func TestTextFileReader_Read_DOCX(t *testing.T) {
	if !checkPythonDocxAvailable() {
		t.Skip("docx2txt is not installed, skipping DOCX tests")
	}

	// Create test context
	ctx := &iris.AgentRunContext{}

	// Create test data directory path
	testDataDir := filepath.Join("testdata", "text")

	tests := []struct {
		name        string
		req         ReadFileRequest
		mimeType    string
		expectError bool
		checkResult func(t *testing.T, resp *ReadFileResult)
	}{
		{
			name: "read DOCX file with default page range",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "sample.docx"),
				ViewType: ViewTypeText,
				// No PageRange specified, should default to first 5 pages
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.FileRange)
				assert.Equal(t, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", resp.MetaData.MimeType)
				assert.True(t, len(resp.FileRange) > 0, "Should have content")
				assert.True(t, resp.MetaData.TotalPages > 0, "Should have calculated total pages")
				assert.Equal(t, 1, resp.MetaData.StartPage, "Should start from page 1")
				assert.True(t, resp.MetaData.EndPage <= 5, "Should not exceed default max pages")
				assert.True(t, len(resp.FileRange) > 50)
			},
		},
		{
			name: "read DOCX file with specific page range",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.docx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{1, 1},
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.FileRange)
				assert.Equal(t, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", resp.MetaData.MimeType)
				assert.Equal(t, 1, resp.MetaData.StartPage)
				assert.Equal(t, 1, resp.MetaData.EndPage)
				assert.True(t, len(resp.FileRange) > 0, "Should have content for page 1")
				assert.True(t, len(resp.FileRange) > 50)
			},
		},
		{
			name: "read DOCX file with page range beyond file size",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.docx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{100, 200}, // Pages beyond actual document size
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			expectError: true, // Should error when start page exceeds total pages
			checkResult: nil,
		},
		{
			name: "read DOCX with invalid page range - start greater than end",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.docx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{5, 1}, // Invalid: start > end
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			expectError: true, // Should error when page range is invalid
			checkResult: nil,
		},
		{
			name: "read DOCX with invalid page range - negative pages",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.docx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{-1, 1}, // Invalid: negative page number
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			expectError: true, // Should error when page numbers are negative
			checkResult: nil,
		},
		{
			name: "read DOCX with zero page range",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.docx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{0, 1}, // Invalid: zero page number
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			expectError: true, // Should error when page numbers are zero
			checkResult: nil,
		},
		{
			name: "read non-existent DOCX file",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "nonexistent.docx"),
				ViewType: ViewTypeText,
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			expectError: true, // Should error when file doesn't exist
			checkResult: nil,
		},
	}

	reader := NewTextFileReader()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := reader.Read(ctx, tt.req, tt.mimeType)

			if tt.expectError {
				assert.Error(t, err)
				return
			}
			for _, v := range resp.FileRange {
				fmt.Printf("%d: %s\n", v.Line, v.Content)
			}

			require.NoError(t, err)
			require.NotNil(t, resp)

			if tt.checkResult != nil {
				tt.checkResult(t, resp)
			}
		})
	}
}

func TestTextFileReader_Read_PPTX(t *testing.T) {
	// Skip test if python-pptx is not available
	if !checkPythonPptxAvailable() {
		t.Skip("python-pptx is not installed, skipping PPTX tests")
	}

	// Create test context
	ctx := &iris.AgentRunContext{}

	// Create test data directory path
	testDataDir := filepath.Join("testdata", "text")

	tests := []struct {
		name        string
		req         ReadFileRequest
		mimeType    string
		expectError bool
		checkResult func(t *testing.T, resp *ReadFileResult)
	}{
		{
			name: "read PPTX file with default page range",
			req: ReadFileRequest{
				FilePath: filepath.Join(testDataDir, "sample.pptx"),
				ViewType: ViewTypeText,
				// No PageRange specified, should default to first 5 slides
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.FileRange)
				assert.Equal(t, "application/vnd.openxmlformats-officedocument.presentationml.presentation", resp.MetaData.MimeType)
				assert.True(t, len(resp.FileRange) > 0, "Should have content")
			},
		},
		{
			name: "read PPTX file with specific slide range",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.pptx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{1, 1},
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
			expectError: false,
			checkResult: func(t *testing.T, resp *ReadFileResult) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.FileRange)
				assert.Equal(t, "application/vnd.openxmlformats-officedocument.presentationml.presentation", resp.MetaData.MimeType)
				assert.True(t, len(resp.FileRange) > 0, "Should have content for single slide")
			},
		},
		{
			name: "read PPTX file with slide range beyond file size",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.pptx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{100, 200}, // Slides beyond actual PPTX size
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
			expectError: true, // Should error when slides don't exist
			checkResult: nil,
		},
		{
			name: "read PPTX with invalid slide range",
			req: ReadFileRequest{
				FilePath:  filepath.Join(testDataDir, "sample.pptx"),
				ViewType:  ViewTypeText,
				PageRange: &[2]int{5, 1}, // Invalid: start > end
			},
			mimeType:    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
			expectError: true, // Should error when slide range is invalid
		},
	}

	reader := NewTextFileReader()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := reader.Read(ctx, tt.req, tt.mimeType)

			if tt.expectError {
				assert.Error(t, err)
				return
			}
			fmt.Println(resp)

			require.NoError(t, err)
			require.NotNil(t, resp)

			if tt.checkResult != nil {
				tt.checkResult(t, resp)
			}
		})
	}
}
