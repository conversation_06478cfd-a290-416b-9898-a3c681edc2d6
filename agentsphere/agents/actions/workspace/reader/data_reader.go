package reader

import (
	"path/filepath"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type DataFileReader struct {
	BaseFileReader
}

func NewDataFileReader() FileReader {
	return &DataFileReader{
		BaseFileReader: NewBaseFileReader(),
	}
}

func (d *DataFileReader) Read(ctx *iris.AgentRunContext, req ReadFileRequest, mimeType string) (*ReadFileResult, error) {
	metadata := d.CreateMetaData(req.FilePath, mimeType)

	ext := filepath.Ext(req.FilePath)

	switch ext {
	case ".xlsx", ".xlsm", ".xltx", ".xltm", ".xlam":
		return d.readExcel(ctx, req, metadata)
	case ".json", ".xml", ".tsv", ".yaml", ".csv":
		return d.readGeneralData(ctx, req, metadata)
	default:
		return nil, errors.New("unsupported data file type: " + ext)
	}
}

func (d *DataFileReader) readExcel(ctx *iris.AgentRunContext, req ReadFileRequest, metadata MetaData) (*ReadFileResult, error) {
	analysis, err := workspace.PreviewExcel(req.FilePath)
	if err != nil {
		return nil, err
	}
	return &ReadFileResult{
		DataExcelAnalysis: analysis,
	}, nil
}

func (d *DataFileReader) readGeneralData(ctx *iris.AgentRunContext, req ReadFileRequest, metadata MetaData) (*ReadFileResult, error) {
	analysis, err := workspace.PreviewGeneralDataFile(req.FilePath)
	if err != nil {
		return nil, err
	}
	return &ReadFileResult{
		DataFileAnalysis: analysis,
	}, nil
}
