Line 1: This is a sample text file for testing
Line 2: It contains multiple lines of text
Line 3: Each line has different content
Line 4: This helps test the line range functionality
Line 5: We can test reading specific line ranges
Line 6: And also test reading the entire file
Line 7: The file should be readable by the text reader
Line 8: And should handle various line range scenarios
Line 9: Including edge cases and normal usage
Line 10: This line is at position 10
Line 11: And this is line 11
Line 12: Line 12 content here
Line 13: Line 13 content here
Line 14: Line 14 content here
Line 15: Line 15 content here
Line 16: Line 16 content here
Line 17: Line 17 content here
Line 18: Line 18 content here
Line 19: Line 19 content here
Line 20: Line 20 content here