package reader

import (
	"encoding/base64"
	"fmt"
	"os"
	"strings"

	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

type ImageFileReader struct {
	BaseFileReader
}

func NewImageFileReader() FileReader {
	return &ImageFileReader{
		BaseFileReader: NewBaseFileReader(),
	}
}

func (i *ImageFileReader) Read(ctx *iris.AgentRunContext, req ReadFileRequest, mimeType string) (*ReadFileResult, error) {
	metadata := i.CreateMetaData(req.FilePath, mimeType)

	if !strings.HasPrefix(mimeType, "image/") {
		return nil, errors.New("not an image file")
	}

	content, err := os.ReadFile(req.FilePath)
	if err != nil {
		ctx.GetLogger().Errorf("read_file tool: failed to read screenshot: %s", err.<PERSON><PERSON><PERSON>())
		return nil, err
	}

	base64Content := fmt.Sprintf("data:image/%s;base64,%s", mimeType, base64.StdEncoding.EncodeToString(content))
	metadata.ImageContent = base64Content

	ctx.GetLogger().Infof("read_file tool: successfully read image file: %s, contentLength: %d", req.FilePath, len(base64Content))

	return &ReadFileResult{
		FileRange: []FileContent{},
		MetaData:  metadata,
	}, nil
}
