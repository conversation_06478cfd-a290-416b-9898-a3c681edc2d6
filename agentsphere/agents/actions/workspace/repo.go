package workspace

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os/exec"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.byted.org/codebase/sdk/v2/types/vcs"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/codebase"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory/project_memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/projectartifact"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"

	"github.com/go-git/go-git/v5/plumbing/transport/client"
	githttp "github.com/go-git/go-git/v5/plumbing/transport/http"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/samber/lo/mutable"
	"github.com/sourcegraph/conc/panics"
)

// Platform represents the platform type of a repository
type Platform string

const (
	URLTypeMergeRequest = "merge_request"
	URLTypeBitsDevopsMR = "bits_devops_merge_request"
	URLTypeBlob         = "blob"
	URLTypeTree         = "tree"
)

type RepoCloneInfo struct {
	Platform entity.GitPlatform
	RepoName string

	Shallow bool // default depth, default is true for internal repo, false for open source repo

	// HTTP urls may contain extra info
	URLType string // home, merge_request, file
	Ref     string // branch, tag, commit, etc.
}

type repoPattern struct {
	regex    *regexp.Regexp
	platform entity.GitPlatform
	shallow  bool
	comment  string
}

// Precompiled regex patterns for better performance
var repoPatterns = []repoPattern{
	// GitHub HTTP URLs (unified pattern for all GitHub HTTP links)
	{regexp.MustCompile(`^https:\/\/github\.com\/([^\/]+\/[\w-]+)(?:\.git|\/[^?\s\p{Han}]*)?(?:\?[^\p{Han}]*)?\/?`), entity.GitPlatformGithub, true, "GitHub HTTP"},

	// Github SSH patterns
	{regexp.MustCompile(`^git@github\.com:([^/]+/[^/]+)(?:\.git)?$`), entity.GitPlatformGithub, true, "GitHub SSH"},

	// Codebase SSH patterns
	{regexp.MustCompile(`^ssh://git@code\.byted\.org/([^/]+/[^/]+)(?:\.git)?$`), entity.GitPlatformCodebase, false, "Codebase SSH with protocol"},
	{regexp.MustCompile(`^git@code\.byted\.org:([^/]+/[^/]+)(?:\.git)?$`), entity.GitPlatformCodebase, false, "Codebase SSH"},
	{regexp.MustCompile(`^ssh://git@git\.byted\.org:29418/(.+)(?:\.git)?$`), entity.GitPlatformCodebase, false, "Git.byted SSH with protocol and port"},
	{regexp.MustCompile(`^git@git\.byted\.org:29418/(.+)(?:\.git)?$`), entity.GitPlatformCodebase, false, "Git.byted SSH with port"},
	{regexp.MustCompile(`^gitlab@git\.byted\.org:([^/]+/[^/]+)(?:\.git)?$`), entity.GitPlatformCodebase, false, "gitlab Git.byted SSH"},

	// Codebase Gerrit patterns (must be placed before general code.byted.org pattern, 多段式仓库名)
	// https://code.byted.org/gerrit/{仓库名}/-/{其他部分}
	{regexp.MustCompile(`^https://code\.byted\.org/gerrit/(.+)/-/.*$`), entity.GitPlatformCodebase, false, "Gerrit"},
	{regexp.MustCompile(`^https://code\.byted\.org/gerrit/(.+)$`), entity.GitPlatformCodebase, false, "Gerrit basic"},

	// Code.byted HTTP URLs (unified pattern for all non-gerrit code.byted.org links, 两段式仓库名)
	{regexp.MustCompile(`^https://code\.byted\.org/([^/]+/[^/]+)(?:\.git|/.*)?(?:\?.*)?/?$`), entity.GitPlatformCodebase, false, "Code.byted HTTP"},

	// Code.byted URLs without protocol (e.g., code.byted.org/xxx/yyy)
	{regexp.MustCompile(`^code\.byted\.org/([^/]+/[^/]+)(?:\.git|/.*)?(?:\?.*)?/?$`), entity.GitPlatformCodebase, false, "Code.byted without protocol"},

	// Bits Gerrit patterns (must be placed before general bits pattern, 多段式仓库名)
	{regexp.MustCompile(`^https://bits\.bytedance\.net/code/gerrit/(.+)/-/.*$`), entity.GitPlatformCodebase, false, "Bits Gerrit"},
	{regexp.MustCompile(`^https://bits\.bytedance\.net/code/gerrit/(.+)$`), entity.GitPlatformCodebase, false, "Bits Gerrit basic"},

	// Bits HTTP URLs (unified pattern for all non-gerrit bits.bytedance.net links, 两段式仓库名)
	{regexp.MustCompile(`^https://bits\.bytedance\.net/code/([^/]+/[^/]+)(?:/.*)?(?:\?.*)?/?$`), entity.GitPlatformCodebase, false, "Bits HTTP"},
	{regexp.MustCompile(`^https://bits\.bytedance\.net/devops/(?:[^/]+)/code/detail/(?:[^/?]+)`), entity.GitPlatformCodebase, false, "Bits Devops MR"}, // 注意这里没有判断结尾$，因为 bits 多仓 MR 可能有很多 query params

	// Bits URLs without protocol (e.g., bits.bytedance.net/code/xxx/yyy)
	{regexp.MustCompile(`^bits\.bytedance\.net/code/([^/]+/[^/]+)(?:/.*)?(?:\?.*)?/?$`), entity.GitPlatformCodebase, false, "Bits without protocol"},

	// Other Codebase HTTP URLs
	{regexp.MustCompile(`^https://review\.byted\.org/(.+)(?:\.git)?$`), entity.GitPlatformCodebase, false, "Review.byted HTTP"},
	{regexp.MustCompile(`^https://git\.byted\.org/(.+)(?:\.git)?$`), entity.GitPlatformCodebase, false, "Git.byted HTTP"},

	// Raw repo paths (must be last to avoid conflicts)
	{regexp.MustCompile(`^([a-zA-Z0-9_.-]+(?:/[a-zA-Z0-9_.-]+)+)$`), entity.GitPlatformUnknown, false, "Raw repo path"},
}

// ParseRepoPath parses the repo path and returns the repo clone info
// platform: codebase or github
// repoName: repo name
func ParseRepoPath(repoPath string) (*RepoCloneInfo, error) {
	repoPath = strings.TrimSpace(repoPath)
	if repoPath == "" {
		return nil, fmt.Errorf("empty repo path")
	}

	for _, p := range repoPatterns {
		if matches := p.regex.FindStringSubmatch(repoPath); matches != nil {
			return &RepoCloneInfo{
				Platform: p.platform,
				RepoName: cleanRepoName(lo.FirstOr(matches[1:], "")),
				Shallow:  p.shallow,
			}, nil
		}
	}

	return nil, fmt.Errorf("unsupported repo path format: %s", repoPath)
}

var (
	mergeRequestRegex = regexp.MustCompile(`^https://code\.byted\.org/([^/]+/[^/]+)/merge_requests/([^/?]+)`)
	// only support bits-code merge request url; not bits mr url
	bitsCodeMergeRequestRegex   = regexp.MustCompile(`^https://bits\.bytedance\.net/code/([^/]+/[^/]+)/merge_requests/([^/?]+)`)
	bitsDevopsMergeRequestRegex = regexp.MustCompile(`^https://bits\.bytedance\.net/devops/([^/]+)/code/detail/([^/?]+)`)
	// TODO: blob urls are ambiguous, e.g. `https://code.byted.org/middleware/hertz/blob/release/1.0/README.md?ref_type=heads`
	// branch name can be `release/1.0` or `release`
	blobRegex     = regexp.MustCompile(`^https://code\.byted\.org/([^/]+/[^/]+)/blob/([^/]+)/([^/?]+)`)
	bitsBlobRegex = regexp.MustCompile(`^https://bits\.bytedance\.net/code/([^/]+/[^/]+)/blob/([^/]+)/([^/?]+)`)
	treeRegex     = regexp.MustCompile(`^https://code\.byted\.org/([^/]+/[^/]+)/tree/([^/]+)/([^/?]+)`)
	bitsTreeRegex = regexp.MustCompile(`^https://bits\.bytedance\.net/code/([^/]+/[^/]+)/tree/([^/]+)/([^/?]+)`)
)

func parseMergeRequestURL(url string) (string, string, error) {
	for _, p := range []*regexp.Regexp{mergeRequestRegex, bitsCodeMergeRequestRegex} {
		if matches := p.FindStringSubmatch(url); matches != nil {
			return matches[1], matches[2], nil
		}
	}
	return "", "", fmt.Errorf("invalid merge request url: %s", url)
}

func parseBlobURL(url string) (string, string, error) {
	for _, p := range []*regexp.Regexp{blobRegex, bitsBlobRegex, treeRegex, bitsTreeRegex} {
		if matches := p.FindStringSubmatch(url); matches != nil {
			return matches[1], matches[2], nil
		}
	}
	return "", "", fmt.Errorf("invalid blob url: %s", url)
}

// ParseRepoURL parses the repo url and returns the repo clone info
// it also handles merge request url and blob url via codebase api
func ParseRepoCloneInfo(run *iris.AgentRunContext, url string) (*RepoCloneInfo, error) {
	cloneInfo, err := ParseRepoPath(url)
	// raw repo path like `middleware/hertz` is not supported
	if err != nil || cloneInfo.Platform == entity.GitPlatformUnknown {
		return nil, err
	}

	lo.ForEach([]*regexp.Regexp{mergeRequestRegex, bitsCodeMergeRequestRegex, bitsDevopsMergeRequestRegex}, func(p *regexp.Regexp, _ int) {
		if matches := p.FindStringSubmatch(url); matches != nil {
			cloneInfo.URLType = URLTypeMergeRequest
			mrID, err := strconv.ParseInt(matches[2], 10, 64)
			if err != nil {
				return
			}
			switch p {
			case mergeRequestRegex, bitsCodeMergeRequestRegex:
				mr, err := codebase.GetMergeRequest(run, codebase.GetMergeRequestDetailsArgs{
					RepoName:       cloneInfo.RepoName,
					MergeRequestID: mrID,
				})
				if err != nil {
					return
				}
				cloneInfo.Ref = lo.Ternary(mr.Source.Ref != "", mr.Source.Ref, mr.Source.Sha)
			case bitsDevopsMergeRequestRegex:
				cloneInfo.RepoName = ""
				cloneInfo.URLType = URLTypeBitsDevopsMR
				// FIXME: get mr info without depending on bits mcp tool, which introduce circular dependency
			}
		}
	})

	lo.ForEach([]*regexp.Regexp{blobRegex, bitsBlobRegex, treeRegex, bitsTreeRegex}, func(p *regexp.Regexp, _ int) {
		if matches := p.FindStringSubmatch(url); matches != nil {
			cloneInfo.URLType = URLTypeBlob
			branchPrefix := matches[2]
			branchCandidates := strings.Split(matches[3], "/")
			for i := range branchCandidates {
				branchCandidates[i] = branchPrefix + "/" + strings.Join(branchCandidates[:i+1], "/")
			}
			branchCandidates = branchCandidates[:len(branchCandidates)-1]
			if len(branchCandidates) == 0 {
				cloneInfo.Ref = branchPrefix
				return
			}

			mutable.Reverse(branchCandidates)
			branchCandidates = append(branchCandidates, branchPrefix)
			cli, err := codebase.NewNextCodeClient(run)
			if err != nil {
				return
			}
			branches, err := cli.ListBranches(run, vcs.ListBranchesRequest{
				RepoId:    cloneInfo.RepoName,
				Query:     lo.ToPtr(branchPrefix),
				QueryMode: lo.ToPtr(vcs.QueryModePrefix),
			})
			if err != nil {
				return
			}
			for _, candidate := range branchCandidates {
				if lo.ContainsBy(branches.Branches, func(branch *vcs.Branch) bool {
					return branch.Name == candidate
				}) {
					cloneInfo.Ref = candidate
					// the last (and longest) branch is the most likely one
				}
			}
			if cloneInfo.Ref == "" {
				cloneInfo.Ref = matches[3]
			}
			return
		}
	})

	return cloneInfo, nil
}

var (
	ToolGitClone            = "git_clone"
	ToolGitCloneDescription = "Clone a git repository from codebase/github with given name and branch/tag/commit. Only this tool has access to user's ssh key."
	GitHubProxy             = lo.Must(url.Parse("http://sys-proxy-rd-relay.byted.org:3128"))
)

type GitCloneArgs struct {
	Directory     string             `json:"directory,omitempty" mapstructure:"directory" description:"directory to clone to. The current directory (i.e., .) is not allowed. If not specified, the repository will be cloned into a directory named after the repository (i.e., the part after the last '/') in the current directory. For example, cloning 'a/b' will create a directory named 'b' in the current directory."`
	RepoPath      string             `json:"repo_path" mapstructure:"repo_path" description:"required, path of the repository to clone, can be a repo url in https or ssh format or a repo name. If you unsure about the repo name, please use the repo url."`
	Platform      entity.GitPlatform `json:"platform" mapstructure:"platform" description:"required, codebase(ByteDance private) or github(open source)"`
	ReferenceName string             `json:"reference_name,omitempty" mapstructure:"reference_name" description:"branch name/tag/commit sha to clone, defaults to remote default branch(main/master/other custom branch)"`
	UnShallow     bool               `json:"unshallow,omitempty" mapstructure:"unshallow" description:"whether to clone all history commits, default is false which is shallow clone"`
}

type GitCloneOutputs struct {
	RepoName string `json:"repo_name" mapstructure:"repo_name" description:"name of the repository"`
	Message  string `json:"message" mapstructure:"message" description:"message of the clone operation"`
}

func NewGitCloneAction(enabledBytedRepos bool) iris.Action {
	return actions.ToTool(ToolGitClone, ToolGitCloneDescription, GitClone)
}

func GitClone(run *iris.AgentRunContext, args GitCloneArgs) (*GitCloneOutputs, error) {
	uninstall := installGitHubProxy()
	defer uninstall()
	shallow := !args.UnShallow
	ws, term := GetWorkspace(run), GetTerminal(run, "")
	term.PrintCommandPrompt(exec.Command(lo.Ternary(args.Platform == entity.GitPlatformCodebase, "codebase", "git"), "clone", args.RepoPath, args.ReferenceName))

	url := args.RepoPath
	repoName := args.RepoPath
	info, parseError := ParseRepoPath(args.RepoPath)
	if parseError == nil && info != nil {
		run.GetLogger().Infof("parsed repo info: %+v", info)
		repoName = info.RepoName
		if args.Platform == "" {
			args.Platform = info.Platform
		}
	}

	codebasePlatform := "gitlab"
	switch args.Platform {
	case entity.GitPlatformCodebase:
		url = fmt.Sprintf("<EMAIL>:%s", repoName)
		cli, err := codebase.NewCodebaseClient(run)
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to create codebase client"))
		}

		// 1. check user permission
		role, err := cli.GetRepoUserRole(run, run.User.Username, repoName)
		if err != nil {
			return nil, iris.NewRecoverable(errors.WithMessagef(err, "user do not have permission to clone %s", repoName))
		}
		clonePermissionRoles := []string{"master", "owner", "developer", "reporter"}
		if !lo.Contains(clonePermissionRoles, role) {
			return nil, iris.NewRecoverable(errors.Errorf("user do not have permission to clone %s", repoName))
		}

		// 2. get repo info to check if it's gerrit repo
		repoInfo, err := cli.GetRepo(run, repoName)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get repo info")
		}

		if strings.Contains(repoInfo.Platform, "gerrit") {
			codebasePlatform = "gerrit"
			url = fmt.Sprintf("<EMAIL>:%s", repoName)
			run.GetLogger().Infof("gerrit repo detected: %s %s", repoName, repoInfo.GitURL)
		}

	case entity.GitPlatformGithub:
		url = fmt.Sprintf("https://github.com/%s", repoName)
	}
	dir := lo.Ternary(args.Directory != "", args.Directory, path.Base(repoName))
	option := CloneOption{
		Directory: dir,
		URL:       url,
		Checkout:  args.ReferenceName,
		Shallow:   shallow,
		Terminal:  term,
		Progress:  false,
	}
	if args.Platform == entity.GitPlatformCodebase {
		option = option.WithSSHCredential(codebasePlatform, run.GetEnv(entity.RuntimeEnvironUserCodebaseJWT))
	}
	run.GetLogger().Infof("clone option: %+v", option)
	start := time.Now()
	repo, err := CloneRepository(run, option)
	_ = metrics.AR.ToolGitCloneCost.WithTags(&metrics.ToolGitCloneTag{
		RepoName: repoName,
		Platform: string(args.Platform),
		Shallow:  shallow,
		Success:  err == nil,
	}).Observe(float64(time.Since(start).Milliseconds()))
	if err != nil {
		return nil, iris.NewRecoverable(errors.WithMessagef(err, "unable to clone %s", repoName))
	}

	// load project memory
	memory, err := loadProjectMemory(run, repoName, repo.Directory)
	if err != nil {
		run.GetLogger().Warnf("unable to load repo memory: %v", err)
		repo.Memory = nil
	} else {
		repo.Memory = memory
	}

	ws.AddRawRepository(repo)
	// cosy set up
	if err = setupCosy(run, filepath.Join(GetEditor(run).WorkingDirectory, repo.Directory), 10*time.Second); err != nil {
		run.GetLogger().Warnf("unable to setup cosy: %v", err)
	}

	var message string
	if args.ReferenceName == "" {
		// 没有指定 ref，只克隆了默认分支
		message = lo.Ternary(shallow,
			fmt.Sprintf("Cloned 1 latest commit from %s (%s platform) to `%s` directory (default branch only). Execute `git fetch --unshallow` if history commits is required, or `git fetch origin <branch/tag>` to fetch other branches/tags.", repoName, args.Platform, dir),
			fmt.Sprintf("Successfully cloned %s (%s platform) to `%s` directory (default branch only). Execute `git fetch origin <branch/tag>` to fetch other branches/tags.", repoName, args.Platform, dir),
		)
	} else {
		// 指定了 ref
		message = lo.Ternary(shallow,
			fmt.Sprintf("Cloned 1 latest commit from %s (%s platform) to `%s` directory, checked out to %s. Execute `git fetch --unshallow` if history commits is required to complete the task.", repoName, args.Platform, dir, args.ReferenceName),
			fmt.Sprintf("Successfully cloned %s (%s platform) to `%s` directory, checked out to %s.", repoName, args.Platform, dir, args.ReferenceName),
		)
	}

	if args.Platform == entity.GitPlatformCodebase {
		projectartifact.GetProjectArtifactManager(run).MarkCodebaseProjectArtifact(run, projectartifact.CodebaseRepo{
			Name:     repo.Directory,
			RepoName: repoName,
			Platform: codebasePlatform,
			URL:      url,
		})
	}
	if _, err = projectartifact.GetProjectArtifactManager(run).ProcessProjectArtifacts(run, iris.ProjectReference{
		{
			Name: repo.Directory,
			Path: filepath.Join(GetEditor(run).WorkingDirectory, repo.Directory),
		},
	}, projectartifact.StashProjectArtifactsOptions...); err != nil {
		run.GetLogger().Errorf("failed to process project artifacts: %v", err)
	}

	// add repo memory
	if repo.Memory != nil {
		message += "\n\n" + repo.Memory.String()
	}

	return &GitCloneOutputs{
		RepoName: repoName,
		Message:  message,
	}, nil
}

// setupCosy 异步设置 cosy，并设置最长 setupTimeout 秒的等待时间
func setupCosy(run *iris.AgentRunContext, workspace string, setupTimeout time.Duration) error {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(run, setupTimeout)
	defer cancel()
	// 启动一个 goroutine 来执行 cosy 设置
	setupDone := make(chan error, 1)
	go panics.Try(func() {
		defer close(setupDone)
		setupDone <- runCosySetup(run, workspace)
	})
	// 等待设置完成或超时
	select {
	case err := <-setupDone:
		if err != nil {
			run.GetLogger().Errorf("cosy setup completed with error: %v", err)
			return err
		}
		run.GetLogger().Infof("cosy setup completed successfully")
		return nil
	case <-ctx.Done():
		// 超时后继续执行，但记录日志
		run.GetLogger().Infof("cosy setup did not complete within 10s, continuing anyway...")
		return nil
	}
}

// runCosySetup 执行实际的 cosy 设置命令
func runCosySetup(run *iris.AgentRunContext, repo string) error {
	// 检查 repo 路径
	if strings.TrimSpace(repo) == "" {
		return fmt.Errorf("cosy setup repository path cannot be empty")
	}
	// 准备 cosy 设置命令
	cmd := exec.Command("/usr/local/cosy/dgit-cosy-local", "setup",
		"--serve-config", "/usr/local/cosy/config.cosy_rpc.local.toml",
		"--workspace", fmt.Sprintf("local://%s", repo),
		"--wait")
	// 获取命令输出
	run.GetLogger().Infof("cosy setup start, command: %s", cmd.String())
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("cosy setup failed: %v, output: %s", err, string(output))
	}
	run.GetLogger().Infof("cosy setup output: %s", string(output))
	return nil
}

func installGitHubProxy() (uninstall func()) {
	// Codebase repos can only be cloned via SSH
	// It's safe to install global proxy for open source repos
	oldTransport := client.Protocols["https"]
	client.InstallProtocol("https", githttp.NewClient(&http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyURL(GitHubProxy),
		},
	}))
	return func() {
		client.InstallProtocol("https", oldTransport)
	}
}

// loadProjectMemory loads repository memory from aime.md and CLAUDE.md files
func loadProjectMemory(run *iris.AgentRunContext, repoName string, repoDirectory string) (*project_memory.ProjectMemory, error) {
	logger := run.GetLogger()

	// Define memory files to load
	memoryFiles := []string{"AGENT.md", "AGENTS.md"}

	// Load and merge memories from the specified files
	result, err := project_memory.LoadAndMergeMultipleMemories(run, memoryFiles, repoName, repoDirectory, logger)
	if err != nil {
		logger.Warnf("unable to load repo memory: %v", err)
		return nil, err
	}
	return result, nil
}
