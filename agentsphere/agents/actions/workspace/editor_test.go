package workspace

import (
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestListDirectoryResult_String(t *testing.T) {
	type fields struct {
		Files []FileEntry
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "list directory result string",
			fields: fields{
				Files: []FileEntry{
					{
						Path:  "a.txt",
						IsDir: false,
					},
					{
						Path:  "b.txt",
						IsDir: false,
					},
				},
			},
			want: "a.txt\nb.txt\n",
		},
		{
			name: "symlinks",
			fields: fields{
				Files: []FileEntry{
					{
						Path:  "a.txt",
						IsDir: false,
					},
					{
						Path:     "b.txt",
						RealPath: "../b.txt",
						IsDir:    false,
					},
				},
			},
			want: `a.txt
b.txt -> ../b.txt
`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := &ListDirectoryResult{
				Files: tt.fields.Files,
			}
			if got := result.String(); got != tt.want {
				t.Errorf("ListDirectoryResult.String() = \n--- have---\n%v\n--- want ---\n%v", got, tt.want)
			}
		})
	}
}

func TestEditor_ListDirectory(t *testing.T) {
	tempDir := t.TempDir()
	tempFile, _ := os.Create(filepath.Join(tempDir, "a.txt"))
	tempFileInfo, _ := tempFile.Stat()

	type fields struct {
		WorkingDirectory string
		OpenedFiles      map[string]*File
	}
	type args struct {
		args ListDirectoryArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ListDirectoryResult
		wantErr bool
	}{
		{
			name: "list directory",
			fields: fields{
				WorkingDirectory: tempDir,
			},
			args: args{
				args: ListDirectoryArgs{
					Path: tempDir,
				},
			},
			want: &ListDirectoryResult{
				Files: []FileEntry{
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo.ModTime().String(),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &Editor{
				WorkingDirectory: tt.fields.WorkingDirectory,
				OpenedFiles:      tt.fields.OpenedFiles,
			}
			got, err := e.ListDirectory(tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("Editor.ListDirectory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Editor.ListDirectory() = %#v, want %#v", got, tt.want)
			}
		})
	}
}

func TestEditor_SearchFileName(t *testing.T) {
	tempDir := t.TempDir()
	tempFile, _ := os.Create(filepath.Join(tempDir, "a.txt"))
	tempFileInfo, _ := tempFile.Stat()
	type fields struct {
		WorkingDirectory string
		OpenedFiles      map[string]*File
	}
	type args struct {
		args SearchFileNameArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *SearchFileNameOutput
		wantErr bool
	}{
		{
			name: "search file name",
			fields: fields{
				WorkingDirectory: tempDir,
			},
			args: args{
				args: SearchFileNameArgs{
					Directory: "./",
					FileName:  "a.txt",
				},
			},
			want: &SearchFileNameOutput{
				Files: []FileEntry{
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo.ModTime().String(),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &Editor{
				WorkingDirectory: tt.fields.WorkingDirectory,
				OpenedFiles:      tt.fields.OpenedFiles,
			}
			got, err := e.SearchFileName(tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("Editor.ListDirectory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Editor.ListDirectory() = %#v, want %#v", got, tt.want)
			}
		})
	}
}

func TestEditor_GlobSearch(t *testing.T) {
	tempDir := t.TempDir()
	tempFile1, _ := os.Create(filepath.Join(tempDir, "a.txt"))
	tempFileInfo1, _ := tempFile1.Stat()

	tempFile2, _ := os.Create(filepath.Join(tempDir, "a.md"))
	tempFileInfo2, _ := tempFile2.Stat()

	nestedDirPath := filepath.Join(tempDir, "x1/x2/x3")
	err := os.MkdirAll(nestedDirPath, 0755)
	require.NoError(t, err)

	tempFile3, err := os.Create(filepath.Join(nestedDirPath, "a.md"))
	require.NoError(t, err)
	_, _ = tempFile3.Stat()

	type fields struct {
		WorkingDirectory string
		OpenedFiles      map[string]*File
	}
	type args struct {
		args GlobSearchArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *GlobSearchOutput
		wantErr bool
	}{
		{
			name: "glob search",
			fields: fields{
				WorkingDirectory: tempDir,
			},
			args: args{
				args: GlobSearchArgs{
					Path:    "./",
					Pattern: "*.txt",
				},
			},
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
				},
			},
		},
		{
			name: "global search failed",
			fields: fields{
				WorkingDirectory: tempDir,
			},
			args: args{
				args: GlobSearchArgs{
					Path:    "./",
					Pattern: "*.py",
				},
			},
			want: &GlobSearchOutput{
				TotalFiles:  0,
				HiddenFiles: 0,
				Files:       []FileEntry{},
			},
		},
		{
			name: "global search with modification time desc order",
			fields: fields{
				WorkingDirectory: tempDir,
			},
			args: args{
				args: GlobSearchArgs{
					Path:    "./",
					Pattern: "a*",
				},
			},
			want: &GlobSearchOutput{
				TotalFiles:  2,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.md",
						Name:             "a.md",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo2.ModTime().String(),
					},
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
				},
			},
		}, {
			name: "global search with dir match",
			fields: fields{
				WorkingDirectory: tempDir,
			},
			args: args{
				args: GlobSearchArgs{
					Path:    "./",
					Pattern: "x1/x2/*",
				},
			},
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "x1/x2/x3",
						Name:             "x3",
						Type:             "directory",
						IsDir:            true,
						ModificationTime: "",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &Editor{
				WorkingDirectory: tt.fields.WorkingDirectory,
				OpenedFiles:      tt.fields.OpenedFiles,
			}
			got, err := e.ExecGlobSearch(tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("Editor.GlobSearch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Editor.GlobSearch() = %#v, want %#v", got, tt.want)
			}
		})
	}
}

func TestEditor_ReadFile(t *testing.T) {
	tempDir := t.TempDir()
	e := &Editor{
		WorkingDirectory: tempDir,
		OpenedFiles:      map[string]*File{},
		IgnorePaths:      []string{tempDir},
	}
	// 禁用事件报告，避免空指针异常
	e.DisableEventReport()

	// 获取当前工作目录
	pwd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}

	filePath := filepath.Join(pwd, "editor_test.go")

	got, err := e.ReadFileRangeOptimzeForLLM(ReadFileRangeArgs{
		// 移除多余空格
		Path:      filePath,
		StartLine: 2,
		EndLine:   3,
	})

	if err != nil {
		// 使用 t.Errorf 报告错误
		t.Errorf("Editor.ReadFileRange() error = %v", err)
		return
	}

	// 打印详细的调试信息
	fmt.Printf("Got: Start=%d, End=%d, Total=%d, Lines=%d\n", got.Start, got.End, got.Total, len(got.Lines))
}

func TestEditor_GlobSearchAdvanced(t *testing.T) {
	tempDir := t.TempDir()

	// Create test files
	tempFile1, _ := os.Create(filepath.Join(tempDir, "a.txt"))
	tempFileInfo1, _ := tempFile1.Stat()

	tempFile2, _ := os.Create(filepath.Join(tempDir, "a.md"))
	tempFileInfo2, _ := tempFile2.Stat()

	tempFile3, _ := os.Create(filepath.Join(tempDir, "b.go"))
	tempFileInfo3, _ := tempFile3.Stat()

	// Create nested structure
	nestedDirPath := filepath.Join(tempDir, "src/main")
	err := os.MkdirAll(nestedDirPath, 0755)
	require.NoError(t, err)

	tempFile4, err := os.Create(filepath.Join(nestedDirPath, "app.go"))
	require.NoError(t, err)
	tempFileInfo4, _ := tempFile4.Stat()

	tempFile5, err := os.Create(filepath.Join(nestedDirPath, "config.json"))
	require.NoError(t, err)
	tempFileInfo5, _ := tempFile5.Stat()

	tests := []struct {
		name    string
		path    string
		pattern string
		want    *GlobSearchOutput
		wantErr bool
	}{
		{
			name:    "question mark wildcard - 3 chars",
			path:    "./",
			pattern: "a.???",
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "question mark wildcard - 2 chars",
			path:    "./",
			pattern: "a.??",
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.md",
						Name:             "a.md",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo2.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "character class",
			path:    "./",
			pattern: "a.[tm]*",
			want: &GlobSearchOutput{
				TotalFiles:  2,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.md",
						Name:             "a.md",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo2.ModTime().String(),
					},
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "negation pattern",
			path:    "./",
			pattern: "a.[!m]*",
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "range pattern - 3 chars",
			path:    "./",
			pattern: "[a-b].???",
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "range pattern - mixed extensions",
			path:    "./",
			pattern: "[a-b].*",
			want: &GlobSearchOutput{
				TotalFiles:  3,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.md",
						Name:             "a.md",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo2.ModTime().String(),
					},
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
					{
						Path:             "b.go",
						Name:             "b.go",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo3.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "double star recursive",
			path:    "./",
			pattern: "**/app.go",
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "src/main/app.go",
						Name:             "app.go",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo4.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "double star with extension",
			path:    "./",
			pattern: "**/*.go",
			want: &GlobSearchOutput{
				TotalFiles:  2,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "src/main/app.go",
						Name:             "app.go",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo4.ModTime().String(),
					},
					{
						Path:             "b.go",
						Name:             "b.go",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo3.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "exact file match",
			path:    "./",
			pattern: "a.txt",
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "a.txt",
						Name:             "a.txt",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo1.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "nested directory files",
			path:    "./",
			pattern: "src/main/*",
			want: &GlobSearchOutput{
				TotalFiles:  2,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "src/main/config.json",
						Name:             "config.json",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo5.ModTime().String(),
					},
					{
						Path:             "src/main/app.go",
						Name:             "app.go",
						Type:             "file",
						IsDir:            false,
						ModificationTime: tempFileInfo4.ModTime().String(),
					},
				},
			},
		},
		{
			name:    "no matches",
			path:    "./",
			pattern: "nonexistent*",
			want: &GlobSearchOutput{
				TotalFiles:  0,
				HiddenFiles: 0,
				Files:       []FileEntry{},
			},
		},
		{
			name:    "directory match",
			path:    "./",
			pattern: "**/main",
			want: &GlobSearchOutput{
				TotalFiles:  1,
				HiddenFiles: 0,
				Files: []FileEntry{
					{
						Path:             "src/main",
						Name:             "main",
						Type:             "directory",
						IsDir:            true,
						ModificationTime: "",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &Editor{
				WorkingDirectory: tempDir,
				OpenedFiles:      make(map[string]*File),
			}

			got, err := e.ExecGlobSearch(GlobSearchArgs{
				Path:    tt.path,
				Pattern: tt.pattern,
			})

			if tt.wantErr {
				require.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, got)
			//require.Equal(t, tt.want.TotalFiles, got.TotalFiles, "TotalFiles mismatch")
			//require.Equal(t, tt.want.HiddenFiles, got.HiddenFiles, "HiddenFiles mismatch")
			//require.Equal(t, len(tt.want.Files), len(got.Files), "Files length mismatch")

			// Check that all expected files are present (order may vary due to sorting)
			expectedPaths := make(map[string]FileEntry)
			for _, file := range tt.want.Files {
				expectedPaths[file.Path] = file
			}

			for _, actualFile := range got.Files {
				fmt.Println(actualFile.Path)
				expectedFile, exists := expectedPaths[actualFile.Path]
				require.True(t, exists, "Unexpected file: %s", actualFile.Path)
				require.Equal(t, expectedFile.Name, actualFile.Name)
				require.Equal(t, expectedFile.Type, actualFile.Type)
				require.Equal(t, expectedFile.IsDir, actualFile.IsDir)
				if !actualFile.IsDir {
					require.NotEmpty(t, actualFile.ModificationTime)
				}
			}
		})
	}
}

func TestEditor_ExecGlobSearch(t *testing.T) {
	tempDir := t.TempDir()

	// Create test files with different extensions and nested directories
	testFiles := []string{
		"file1.go",
		"file2.txt",
		"test_file.go",
		"README.md",
		"config.json",
		"script.py",
		"main.js",
		"style.css",
		"data.xml",
		"image.png",
		"document.pdf",
	}

	// Create nested directories with files
	nestedDirs := []string{
		"src/main",
		"src/test",
		"docs/api",
		"config/env",
		"assets/images",
	}

	nestedFiles := []string{
		"src/main/app.go",
		"src/main/handler.go",
		"src/test/app_test.go",
		"src/test/handler_test.go",
		"docs/api/swagger.yaml",
		"docs/README.md",
		"config/env/dev.env",
		"config/env/prod.env",
		"assets/images/logo.png",
		"assets/images/banner.jpg",
	}

	// Create all directories first
	for _, dir := range nestedDirs {
		err := os.MkdirAll(filepath.Join(tempDir, dir), 0755)
		require.NoError(t, err)
	}

	// Create root level files
	for _, file := range testFiles {
		_, err := os.Create(filepath.Join(tempDir, file))
		require.NoError(t, err)
	}

	// Create nested files
	for _, file := range nestedFiles {
		_, err := os.Create(filepath.Join(tempDir, file))
		require.NoError(t, err)
	}

	tests := []struct {
		name             string
		path             string
		pattern          string
		expectedCount    int
		shouldMatchPaths []string
	}{
		{
			name:             "root current dir - all go files recursively",
			path:             "./",
			pattern:          "**/*.go",
			expectedCount:    6,
			shouldMatchPaths: []string{"file1.go", "test_file.go", "src/main/app.go", "src/main/handler.go", "src/test/app_test.go", "src/test/handler_test.go"},
		},
		{
			name:             "absolute path - all txt files",
			path:             "./",
			pattern:          "*.txt",
			expectedCount:    1,
			shouldMatchPaths: []string{"file2.txt"},
		},
		{
			name:             "src subdirectory absolute - go files",
			path:             "src",
			pattern:          "**/*.go",
			expectedCount:    4,
			shouldMatchPaths: []string{"main/app.go", "main/handler.go", "test/app_test.go", "test/handler_test.go"},
		},
		{
			name:             "docs directory relative - all files",
			path:             "docs",
			pattern:          "**",
			expectedCount:    4,
			shouldMatchPaths: []string{"README.md", "api", "api/swagger.yaml"},
		},
		{
			name:             "config directory absolute - env files",
			path:             "config",
			pattern:          "**/*.env",
			expectedCount:    2,
			shouldMatchPaths: []string{"env/dev.env", "env/prod.env"},
		},
		{
			name:             "assets subdirectory - image files",
			path:             "assets",
			pattern:          "**/*.png",
			expectedCount:    1,
			shouldMatchPaths: []string{"images/logo.png"},
		},
		{
			name:             "current dir dot - exact file match",
			path:             ".",
			pattern:          "README.md",
			expectedCount:    1,
			shouldMatchPaths: []string{"README.md"},
		},
		{
			name:             "src/main directory - specific go files",
			path:             "src/main",
			pattern:          "*.go",
			expectedCount:    2,
			shouldMatchPaths: []string{"app.go", "handler.go"},
		},
		{
			name:             "empty path - defaults to current",
			path:             "",
			pattern:          "file*",
			expectedCount:    2,
			shouldMatchPaths: []string{"file1.go", "file2.txt"},
		},
		{
			name:             "nested config path - specific files",
			path:             "config/env",
			pattern:          "*.env",
			expectedCount:    2,
			shouldMatchPaths: []string{"dev.env", "prod.env"},
		},
		{
			name:             "root with trailing slash - all markdown",
			path:             "./",
			pattern:          "**/*.md",
			expectedCount:    2,
			shouldMatchPaths: []string{"README.md", "docs/README.md"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &Editor{
				WorkingDirectory: tempDir,
				OpenedFiles:      make(map[string]*File),
			}

			searchPath := tt.path
			if searchPath == "" {
				searchPath = tempDir
			} else if !filepath.IsAbs(searchPath) {
				searchPath = filepath.Join(tempDir, searchPath)
			}

			got, err := e.ExecGlobSearch(GlobSearchArgs{
				Path:    searchPath,
				Pattern: tt.pattern,
			})

			require.NoError(t, err)
			require.NotNil(t, got)
			require.Equal(t, tt.expectedCount, got.TotalFiles, "TotalFiles count mismatch")
			require.Equal(t, tt.expectedCount, len(got.Files), "Files slice length mismatch")

			// Check that all expected paths are present
			actualPaths := make([]string, len(got.Files))
			for i, file := range got.Files {
				actualPaths[i] = file.Path
			}

			for _, expectedPath := range tt.shouldMatchPaths {
				require.Contains(t, actualPaths, expectedPath, "Expected path not found: %s", expectedPath)
			}
		})
	}
}

func TestEditor_ExecGlobSearch_ErrorCases(t *testing.T) {
	tempDir := t.TempDir()

	tests := []struct {
		name        string
		pattern     string
		path        string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid glob pattern - unclosed bracket",
			pattern:     "file[.go",
			path:        "./",
			expectError: true,
			errorMsg:    "glob pattern",
		},
		{
			name:        "invalid glob pattern - unterminated range",
			pattern:     "file[a-",
			path:        "./",
			expectError: true,
			errorMsg:    "glob pattern",
		},
		{
			name:        "nonexistent directory",
			pattern:     "*.go",
			path:        "./nonexistent",
			expectError: true,
			errorMsg:    "failed to read directory",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &Editor{
				WorkingDirectory: tempDir,
				OpenedFiles:      make(map[string]*File),
			}

			got, err := e.ExecGlobSearch(GlobSearchArgs{
				Path:    tt.path,
				Pattern: tt.pattern,
			})

			if tt.expectError {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.errorMsg)
				require.Nil(t, got)
			} else {
				require.NoError(t, err)
				require.NotNil(t, got)
			}
		})
	}
}
