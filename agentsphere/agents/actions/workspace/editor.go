package workspace

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/bmatcuk/doublestar/v4"
	"github.com/go-enry/go-enry/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/lib/tokenizer"
)

// File models a real file on disk
type File struct {
	Path     string     `mapstructure:"path" json:"path"`           // path relative to working directory
	RealPath string     `mapstructure:"real_path" json:"real_path"` // actual absolute path
	Content  string     `mapstructure:"content" json:"content"`     // full content that may be displayed to the user
	Lines    []FileLine `mapstructure:"lines" json:"lines"`
}

func (f *File) Range(start, end int) []FileLine {
	return lo.Slice(f.Lines, start-1, end)
}

type Editor struct {
	WorkingDirectory string
	OpenedFiles      map[string]*File
	IgnorePaths      []string

	channel                           *iris.AgentEventPublisher
	fileHistoryOfStrReplaceEditorTool map[string][]string

	disableEventReport bool
}

func (e *Editor) DisableEventReport() {
	e.disableEventReport = true
}

func (e *Editor) GetfileHistoryOfStrReplaceEditorTool() map[string][]string {
	if e.fileHistoryOfStrReplaceEditorTool == nil {
		e.fileHistoryOfStrReplaceEditorTool = make(map[string][]string)
	}
	return e.fileHistoryOfStrReplaceEditorTool
}

func NewEditor(wd string, publisher *iris.AgentEventPublisher) *Editor {
	return &Editor{
		WorkingDirectory: wd,
		OpenedFiles:      make(map[string]*File),
		channel:          publisher,
		IgnorePaths:      []string{},
	}
}

func inferLanguageFromPath(path string) string {
	// language names should also be defined in front end to provide syntax highlight
	if strings.HasSuffix(path, ".patch") {
		return "Diff"
	} else {
		language, _ := enry.GetLanguageByExtension(filepath.Ext(path))
		return language
	}
}

func (e *Editor) AddOpenedFile(file *File) {
	e.OpenedFiles[file.Path] = file

	if !e.disableEventReport {
		e.channel.ReportEditorOpenFile(iris.EventEditorOpenFile{
			OpenedFiles: map[string]iris.EditorFile{
				file.Path: {
					Path:     file.Path,
					Content:  file.Content,
					Language: inferLanguageFromPath(file.Path),
				},
			},
		})
	}
}

// readFile reads a file and convert its content.
// this is not visible to the user neither update cache
func (e *Editor) readFile(path string) (*File, error) {
	path = e.normalizePath(path)
	relpath, convertErr := filepath.Rel(e.WorkingDirectory, path)
	content, err := os.ReadFile(path)
	if err != nil {
		return nil, errors.Errorf("failed to read file %s: %s", relpath, strings.Split(err.Error(), ":")[1])
	}

	f := &File{
		Path:     lo.Ternary(convertErr != nil, path, relpath),
		RealPath: path,
		Content:  string(content),
		Lines: lo.Map(strings.Split(string(content), "\n"), func(line string, i int) FileLine {
			return FileLine{
				Line:    i + 1,
				Content: lo.Ternary(len(line) > 5000, line[:min(5000, len(line))]+"...[too many characters, truncated to 5000]", line),
			}
		}),
	}
	return f, nil
}

type ReadFileArgs struct {
	Path string `mapstructure:"file_path" json:"file_path"`
}

// ReadFile reads a file from disk and update cached opened files
func (e *Editor) ReadFile(args ReadFileArgs) (*File, error) {
	f, err := e.readFile(args.Path)
	if err != nil {
		return nil, err
	}
	e.AddOpenedFile(f)
	return f, nil
}

type ReadFileRangeArgs struct {
	Path      string `mapstructure:"file_path" json:"file_path"`
	StartLine int    `mapstructure:"start_line" json:"start_line"`
	EndLine   int    `mapstructure:"end_line" json:"end_line"`
}

type ReadMarkdownFilesArgs struct {
	Paths []string `mapstructure:"file_paths" json:"file_paths"`
}

type FileLine struct {
	Line    int    `mapstructure:"line" json:"line"`
	Content string `mapstructure:"content" json:"content"`
}

type MarkDownFiles struct {
	Files []MarkDownFile `mapstructure:"files" json:"files"`
}

type MarkDownFile struct {
	FileName string `mapstructure:"file_name" json:"file_name"`
	Content  string `mapstructure:"content" json:"content"`
}

type FileRange struct {
	Start int        `mapstructure:"start_line" json:"start_line"`
	End   int        `mapstructure:"end_line" json:"end_line"`
	Total int        `mapstructure:"total_lines" json:"total_lines"`
	Lines []FileLine `mapstructure:"lines" json:"lines"`
}

func (e *Editor) ReadMarkdownFiles(args ReadMarkdownFilesArgs) (*MarkDownFiles, error) {
	paths := args.Paths
	var files []MarkDownFile
	maxTokenSize := 80000
	tokenBuffer := ""
	for _, path := range paths {
		path = e.normalizePath(path)
		relpath, _ := filepath.Rel(e.WorkingDirectory, path)
		if !strings.HasSuffix(relpath, ".md") {
			continue
		}
		file, err := e.ReadFile(ReadFileArgs{path})
		if err != nil {
			files = append(files, MarkDownFile{
				FileName: path,
				Content:  fmt.Sprintf("[ERROR!] %v", err), // 如果读取失败，返回错误信息
			})
			continue
		}
		tokenBuffer += fmt.Sprintf("Title: %v\nContent:%v\n", file.Path, file.Content)
		if tokenizer.CountTokensByTokenizer(tokenBuffer) > maxTokenSize && len(files) != 0 {
			break
		}
		if tokenizer.CountTokensByTokenizer(tokenBuffer) > maxTokenSize && len(files) == 0 {
			endIndex := len(file.Content)
			if endIndex > maxTokenSize-len(file.Path) {
				endIndex = maxTokenSize - len(file.Path)
			}
			files = append(files, MarkDownFile{
				FileName: file.Path,
				Content:  file.Content[:endIndex],
			})
			break
		}
		files = append(files, MarkDownFile{
			FileName: file.Path,
			Content:  file.Content,
		})

	}
	return &MarkDownFiles{
		Files: files,
	}, nil
}

func (e *Editor) ReadFileRangeOptimzeForLLM(args ReadFileRangeArgs) (f *FileRange, err error) {
	path := e.normalizePath(args.Path)
	relpath, _ := filepath.Rel(e.WorkingDirectory, args.Path)

	for _, ignorePath := range e.IgnorePaths {
		if strings.HasPrefix(path, e.normalizePath(ignorePath)) {
			return nil, errors.Errorf("failed to read file %s: no such file or directory", relpath)
		}
	}

	file, err := e.ReadFile(ReadFileArgs{Path: args.Path})
	if err != nil {
		return nil, err
	}

	// 优化1: 小文件直接全部返回，无论用户指定的start_line和end_line是什么
	fileSize := len(file.Content)
	totalLines := len(file.Lines)
	if fileSize < 10000 || (totalLines < 750 && fileSize < 30000) {
		return &FileRange{Start: 1, End: totalLines, Total: totalLines, Lines: file.Lines}, nil
	}

	// 设置默认值
	if args.StartLine == 0 && args.EndLine == 0 {
		args.EndLine = min(totalLines, 100)
	}

	// 优化2: 处理大行文件，避免返回过多内容
	// 计算从起始行到结束行的总字符数
	charCount := 0
	maxCharThreshold := 30000 // 最大字符阈值
	actualEndLine := args.EndLine

	// 从起始行开始计算字符数，直到达到阈值或结束行
	for i := args.StartLine - 1; i < totalLines; i++ {
		if i < 0 || i >= len(file.Lines) {
			continue
		}
		charCount += len(file.Lines[i].Content)
		actualEndLine = file.Lines[i].Line
		if charCount > maxCharThreshold {
			break
		}
	}

	// 优化3: 尽可能少的次数完成读取
	lines := file.Range(args.StartLine, actualEndLine)
	actualStartLine := lo.TernaryF(len(lines) != 0, func() int { return lines[0].Line }, func() int { return 0 })
	actualEndLine = lo.TernaryF(len(lines) != 0, func() int { return lines[len(lines)-1].Line }, func() int { return 0 })

	return &FileRange{Start: actualStartLine, End: actualEndLine, Total: totalLines, Lines: lines}, nil
}

func (e *Editor) ReadFileRange(args ReadFileRangeArgs) (f *FileRange, err error) {
	path := e.normalizePath(args.Path)
	relpath, _ := filepath.Rel(e.WorkingDirectory, args.Path)

	for _, ignorePath := range e.IgnorePaths {
		if strings.HasPrefix(path, e.normalizePath(ignorePath)) {
			return nil, errors.Errorf("failed to read file %s: no such file or directory", relpath)
		}
	}

	file, err := e.ReadFile(ReadFileArgs{Path: args.Path})
	if err != nil {
		return nil, err
	}
	if args.StartLine == 0 && args.EndLine == 0 {
		args.EndLine = min(len(file.Lines), 100)
	}
	lines := file.Range(args.StartLine, args.EndLine)
	actualStartLine := lo.TernaryF(len(lines) != 0, func() int { return lines[0].Line }, func() int { return 0 })
	actualEndLine := lo.TernaryF(len(lines) != 0, func() int { return lines[len(lines)-1].Line }, func() int { return 0 })
	return &FileRange{Start: actualStartLine, End: actualEndLine, Total: len(file.Lines), Lines: lines}, nil
}

type WriteFileArgs struct {
	FilePath string `json:"file_path" mapstructure:"file_path"`
	Content  string `json:"content" mapstructure:"content"`
	Append   bool   `json:"append" mapstructure:"append"`
}

func (e *Editor) WriteFile(args WriteFileArgs) error {
	path := e.normalizePath(args.FilePath)
	content := args.Content
	flag := os.O_CREATE | os.O_WRONLY
	if args.Append {
		flag |= os.O_APPEND
	}
	f, err := os.OpenFile(path, flag, 0644)
	if err != nil {
		return fmt.Errorf("failed to write file: %v", err)
	}
	defer f.Close()
	_, err = f.WriteString(content)
	if err != nil {
		return fmt.Errorf("failed to write file: %v", err)
	}
	return nil
}

type FindIdentifierArgs struct {
	Path       string `mapstructure:"file_path" json:"file_path"`
	Identifier string `mapstructure:"identifier" json:"identifier"`
}

type FindIdentifierOutput struct {
	Identifiers []FileLine `mapstructure:"identifiers" json:"identifiers"`
}

func (e *Editor) FindIdentifier(args FindIdentifierArgs) (*FindIdentifierOutput, error) {
	var (
		out FindIdentifierOutput
		err error
	)
	file, ok := e.OpenedFiles[args.Path]
	if !ok {
		file, err = e.ReadFile(ReadFileArgs{Path: args.Path})
		if err != nil {
			return nil, err
		}
	}
	for _, line := range file.Lines {
		if strings.Contains(line.Content, args.Identifier) {
			out.Identifiers = append(out.Identifiers, line)
		}
	}
	return &out, nil
}

type CreateFileArgs struct {
	Path      string `mapstructure:"file_path" json:"file_path"`
	Content   string `mapstructure:"content" json:"content"`
	Overwrite bool   `mapstructure:"overwrite" json:"overwrite"`
}

type CreateFileOutput struct {
	Overwritten bool `mapstructure:"overwritten" json:"overwritten"`
}

type CreateFileOption struct {
	RemoveOuterBackquotes bool
}

func (e *Editor) CreateFile(run *iris.AgentRunContext, args CreateFileArgs, opt CreateFileOption) (*CreateFileOutput, error) {
	path := e.normalizePath(args.Path)
	var content = args.Content
	if opt.RemoveOuterBackquotes {
		content = e.removeOuterBackquotes(args.Content)
	}

	output := &CreateFileOutput{
		Overwritten: false,
	}

	// if the file already exists, return an error
	if _, err := os.Stat(path); err == nil {
		if !args.Overwrite {
			return nil, errors.Errorf("file already exists: %s, please edit the file if necessary", path)
		}
		output.Overwritten = true
	}

	err := os.MkdirAll(filepath.Dir(path), os.ModePerm)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create directory")
	}
	err = os.WriteFile(path, []byte(content), os.ModePerm)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to write file")
	}
	e.AddOpenedFile(&File{
		Path:     args.Path,
		RealPath: path,
		Content:  content,
		Lines:    lo.Map(strings.Split(content, "\n"), func(line string, i int) FileLine { return FileLine{Line: i + 1, Content: line} }),
	})
	checkErrs := e.checkFile(run, args.Path)
	if len(checkErrs) > 0 {
		return nil, fmt.Errorf(
			`the file "%s" was saved successfully. However, the following syntax errors were detected in the file:
%v
fix ALL of the above syntax errors in the file and review the file to ensure it is correct. Edit the file again if necessary.`,
			args.Path,
			checkErrs,
		)
	}
	return output, nil
}

func (e *Editor) removeOuterBackquotes(originalContent string) string {
	backquotes := "```"

	lines := strings.Split(originalContent, "\n")
	if len(lines) < 2 {
		return originalContent
	}

	if strings.HasPrefix(lines[0], backquotes) {
		lines = lines[1:]
	}
	if strings.HasPrefix(lines[len(lines)-1], backquotes) {
		lines = lines[:len(lines)-1]
	}

	return strings.Join(lines, "\n")
}

type ListDirectoryArgs struct {
	Path      string `mapstructure:"path" json:"path"`
	Recursive bool   `mapstructure:"recursive" json:"recursive"`
}

type FileEntry struct {
	Path             string `mapstructure:"path" json:"path"` // full path, e.g. a/b.txt
	Name             string `mapstructure:"name" json:"name"` // file name, e.g. b.txt
	RealPath         string `mapstructure:"real_path" json:"real_path"`
	Type             string `mapstructure:"type" json:"type"`
	IsDir            bool   `mapstructure:"is_dir" json:"is_dir"`
	ModificationTime string `mapstructure:"modification_time" json:"modification_time"`
}

type ListDirectoryResult struct {
	Recursive bool        `mapstructure:"recursive" json:"recursive"`
	Files     []FileEntry `mapstructure:"files" json:"files"`
}

func (e *Editor) ListDirectory(args ListDirectoryArgs) (*ListDirectoryResult, error) {
	result, err := e.ReadDirectory(ReadDirectory{
		Path:      args.Path,
		Recursive: args.Recursive,
		MaxDepth:  3,
	})
	if err != nil {
		return nil, err
	}
	log.V1.Info("result: %v", result)
	if len(result.Files) > 200 {
		return &ListDirectoryResult{Files: result.Files[:200], Recursive: false}, nil
	} else if len(result.AllFiles) > 200 {
		return &ListDirectoryResult{Files: result.AllFiles[:200], Recursive: false}, nil
	}
	return &ListDirectoryResult{Files: result.AllFiles, Recursive: args.Recursive}, nil
}

type ReadDirectory struct {
	Path      string `mapstructure:"path" json:"path"`
	Recursive bool   `mapstructure:"recursive" json:"recursive"`
	MaxDepth  int    `mapstructure:"max_depth" json:"max_depth"`
}

type ReadDirectoryResult struct {
	AllFiles []FileEntry `mapstructure:"all_files" json:"all_files"`
	Files    []FileEntry `mapstructure:"files" json:"files"`
}

// ReadDirectory reads a directory
// Not for LLM use, use ListDirectory instead
func (e *Editor) ReadDirectory(args ReadDirectory) (*ReadDirectoryResult, error) {
	path := e.normalizePath(args.Path)
	relpath, _ := filepath.Rel(e.WorkingDirectory, path)
	files := make([]FileEntry, 0)
	topLevelFiles := make([]FileEntry, 0)
	entry := func(base string, d fs.DirEntry) FileEntry {
		path, _ := filepath.Rel(e.WorkingDirectory, filepath.Join(base, d.Name()))
		realpath, _ := os.Readlink(path)
		return FileEntry{
			Path:     path,
			Name:     d.Name(),
			RealPath: lo.Ternary(realpath != path, realpath, ""),
			Type:     lo.Ternary(d.Type().IsDir(), "directory", "file"),
			IsDir:    d.IsDir(),
			ModificationTime: lo.TernaryF(d.IsDir(), func() string { return "" }, func() string {
				info, err := d.Info()
				if err != nil {
					return ""
				}
				return info.ModTime().String()
			}),
		}
	}

	if !args.Recursive {
		items, err := os.ReadDir(path)
		if err != nil {
			if os.IsNotExist(err) {
				return nil, errors.Errorf("directory `%s` does not exist, or is not a directory", relpath)
			}
			return nil, errors.Errorf("read directory `%s` failed: %v", relpath, err)
		}
		files = lo.FilterMap(items, func(item fs.DirEntry, _ int) (FileEntry, bool) {
			itemPath := filepath.Join(path, item.Name())
			for _, ignorePath := range e.IgnorePaths {
				if strings.HasPrefix(itemPath, e.normalizePath(ignorePath)) {
					return FileEntry{}, false
				}
			}
			return entry(path, item), true
		})
		return &ReadDirectoryResult{AllFiles: files, Files: files}, nil
	}

	err := filepath.WalkDir(path, func(cur string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		for _, ignorePath := range e.IgnorePaths {
			if strings.HasPrefix(cur, e.normalizePath(ignorePath)) {
				if d.IsDir() {
					return filepath.SkipDir
				}
				return nil
			}
		}

		curEntry := entry(filepath.Dir(cur), d)
		files = append(files, curEntry)
		if args.MaxDepth > 0 && e.fileDepth(cur, path) <= args.MaxDepth {
			topLevelFiles = append(topLevelFiles, curEntry)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &ReadDirectoryResult{AllFiles: files, Files: topLevelFiles}, nil
}

type SearchFileNameArgs struct {
	Directory string `mapstructure:"directory" json:"directory"`
	FileName  string `mapstructure:"file_name" json:"file_name"`
}

type SearchFileNameOutput struct {
	Files []FileEntry `mapstructure:"files" json:"files"`
}

func (e *Editor) SearchFileName(args SearchFileNameArgs) (*SearchFileNameOutput, error) {
	result, err := e.ReadDirectory(ReadDirectory{
		Path:      args.Directory,
		Recursive: true,
	})
	if err != nil {
		return nil, err
	}
	// first return the exactly matched files
	files := lo.Filter(result.AllFiles, func(file FileEntry, _ int) bool {
		return file.Name == args.FileName
	})
	if len(files) > 0 {
		return &SearchFileNameOutput{Files: files}, nil
	}
	// then return the files that contains the file name
	files = lo.Filter(result.AllFiles, func(file FileEntry, _ int) bool {
		return strings.Contains(file.Name, args.FileName)
	})
	return &SearchFileNameOutput{Files: files}, nil
}

func (result *ListDirectoryResult) String() string {
	if result == nil || len(result.Files) == 0 {
		return ""
	}
	sb := strings.Builder{}
	for _, file := range result.Files {
		depth := strings.Count(file.Path, string(os.PathSeparator))
		sb.WriteString(strings.Repeat("  ", depth))
		sb.WriteString(file.Path)
		if file.IsDir {
			sb.WriteByte('/')
		}
		if file.RealPath != "" {
			sb.WriteString(" -> ")
			sb.WriteString(file.RealPath)
		}
		sb.WriteByte('\n')
	}
	return sb.String()
}

func (e *Editor) normalizePath(path string) string {
	if filepath.IsAbs(path) {
		return filepath.Clean(path)
	}
	return filepath.Join(e.WorkingDirectory, path)
}

func (e *Editor) fileDepth(path string, base string) int {
	return max(0, strings.Count(path, string(os.PathSeparator))-strings.Count(base, string(os.PathSeparator)))
}

type GlobSearchArgs struct {
	Pattern string `json:"pattern" mapstructure:"pattern"`
	Path    string `json:"path" mapstructure:"path"`
}

type GlobSearchOutput struct {
	TotalFiles  int         `mapstructure:"total_files" json:"total_files"`
	HiddenFiles int         `mapstructure:"hidden_files" json:"hidden_files"`
	Files       []FileEntry `mapstructure:"files" json:"files"`
}

func (e *Editor) ExecGlobSearch(args GlobSearchArgs) (*GlobSearchOutput, error) {
	result, err := e.ReadDirectory(ReadDirectory{
		Path:      args.Path,
		Recursive: true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %v", args.Path, err)
	}

	// Validate pattern by testing it
	if _, err := doublestar.Match(args.Pattern, "test"); err != nil {
		return nil, fmt.Errorf("glob pattern `%s` is invalid: %v", args.Pattern, err)
	}

	// Get the normalized search path to calculate relative paths for matching
	searchPath := e.normalizePath(args.Path)

	matchedFiles := lo.FilterMap(result.AllFiles, func(file FileEntry, _ int) (FileEntry, bool) {
		// Calculate path relative to the search directory for pattern matching
		fullPath := e.normalizePath(file.Path)
		relPath, err := filepath.Rel(searchPath, fullPath)
		if err != nil {
			return FileEntry{}, false
		}
		matched, err := doublestar.Match(args.Pattern, relPath)
		if err != nil || !matched {
			return FileEntry{}, false
		}

		// Update the file entry to use the relative path for consistency
		file.Path = relPath
		return file, true
	})
	// sort by modification time
	sort.Slice(matchedFiles, func(i, j int) bool {
		t1, err1 := time.Parse("2006-01-02 15:04:05.999999999 -0700 MST", matchedFiles[i].ModificationTime)
		t2, err2 := time.Parse("2006-01-02 15:04:05.999999999 -0700 MST", matchedFiles[j].ModificationTime)
		if err1 != nil || err2 != nil {
			return false
		}
		return t1.After(t2)
	})

	return &GlobSearchOutput{
		TotalFiles:  len(matchedFiles),
		HiddenFiles: max(0, len(matchedFiles)-300),
		Files:       lo.Slice(matchedFiles, 0, 300),
	}, nil
}
