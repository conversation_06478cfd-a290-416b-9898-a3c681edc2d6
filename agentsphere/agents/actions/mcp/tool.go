package mcptool

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/google/uuid"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/client/transport"
	gomcp "github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc/panics"
	"github.com/spf13/cast"

	"code.byted.org/bcc/conf_engine/jsoniter"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/rds"
	bytedmcpclient "code.byted.org/inf/bytedmcp/go/client"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/lang/gg/stdwrap/syncwrap"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/argos"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/bitsanalysis"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/devmind"
	imagessearch "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/image_search"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/meego"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/oncall"
	llmtool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/llm"
	agents "code.byted.org/devgpt/kiwis/agentsphere/agents/core"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/mcp/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	rtentity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
)

type MCPSource int

const (
	MCPSourceAIME       MCPSource = 1 // AIME平台
	MCPSourceUserDefine MCPSource = 2 // 用户自定义
	MCPSourceCloud      MCPSource = 3 // 字节云平台
)

// invisibleProviderID 不想展示给前端看到的mcp
var invisibleProviderID []string

// MCPType MCP连接类型枚举
type MCPType int

const (
	MCPTypeKiwis          MCPType = -1 // Kiwis内置代码实现 (有Tools字段)
	MCPTypeDeprecated     MCPType = 0  // 已废弃的类型 因为线上的数据都是0，所以需要兼容，走老逻辑，判断cmd和baseurl走不同的连接
	MCPTypeSTDIO          MCPType = 1  // STDIO连接
	MCPTypeSSE            MCPType = 2  // SSE连接
	MCPTypeStreamableHTTP MCPType = 3  // StreamableHTTP连接 (二期新增)
	MCPTypeCloudSDK       MCPType = 4  // CloudSDK连接 (二期新增)
)

// MCP 相关超时常量定义
const (
	// MCPStartTimeout 是 MCP 启动的超时时间
	MCPStartTimeout = 20 * time.Second

	// MCPPingTimeout 是 MCP ping 操作的超时时间
	MCPPingTimeout = 10 * time.Second

	// MCPInitTimeout 是 MCP 初始化的超时时间
	MCPInitTimeout = 20 * time.Second

	// MCPCallToolTimeout 是 MCP 工具执行的超时时间
	MCPCallToolTimeout = 30 * time.Minute

	// MCPListToolsTimeout 是获取 MCP 工具列表的超时时间
	MCPListToolsTimeout = 30 * time.Second

	// MCPSessionErrorMaxRetries 是 session 错误的最大重试次数
	MCPSessionErrorMaxRetries = 10

	MCPConnectMaxRetries = 5

	// MaxResponseTokens MCP工具响应的最大token数限制
	MaxResponseTokens = 32768
)

// AIME服务账号(aime_mcp)的密钥，用于传递
const (
	aimeMCPServerAccountSecret = "8e33c30a23fc858bde7c5f0bb34fd1a6"
)

var (
	ErrMCPInitializationTimeoutWithSuggestion = errors.New("MCP SSE 初始化超时，可能是授权信息过期或缺乏访问权限，请检查链接配置或检查 MCP 权限配置。//MCP SSE initialization timed out. The authorization may have expired or access permission is missing. Please check the MCP link configuration or permission settings.")
)

type MCPProvider struct {
	ID          string
	Name        string // for planner to select
	Description string // for planner to select
	Byted       bool
	Type        MCPSource // for init
	MCPType     MCPType   // MCP连接类型：使用MCPType枚举 (二期新增)
	BaseURL     string    // for http/sse transport
	Cmd         string    // for stdio transport
	Args        []string
	Env         []string
	PSM         string // PSM参数，用于StreamableHTTP和CloudSDK (二期新增)
	// CloudSDKClientOptions 是 CloudSDK 连接时需要的额外参数，例如 WithMCPGatewayRegion
	CloudSDKClientOptions []any `json:"-"`
	// BytedMCPClientOptions 是字节云SDK连接时需要的额外参数，例如 WithRequestTimeout
	CloudSDKBytedMCPClientOptions []bytedmcpclient.BytedMCPClientOption `json:"-"`
	Tools                         []iris.Action                         // for internal mcp tools
	// ToolsBlocklist 强制指定不需要加载的工具，例如部分mcp tools与内部tools冲突，此时需要屏蔽
	ToolsBlocklist []string
	// ToolsWhitelist 该mcp只允许加载的工具列表
	ToolsWhitelist []string

	// 为 MCPProvider 提供运行时上下文参数能力，例如运行时token
	BeforeConnectHooks []func(run *iris.AgentRunContext, provider *MCPProvider) `json:"-"`
	// WithPeriodicReconnect 是否启用定时重连, 用于定期需重启的 mcp server，例如 token 时效性问题
	WithPeriodicReconnect time.Duration
	Header                map[string]string
	SkipCloudJWTAuth      bool // 是否跳过字节云Token传递
}

var (
	Cosy = &MCPProvider{
		ID:   "cosy",
		Name: "Cosy",
		Description: `Cosy is a mcp server which provides codebase search tools, including:
- code_semantic_search: Find snippets of code from the codebase most relevant to the search query. This is a semantic search tool, so the query should ask for something semantically matching what is needed. Returns precise code snippets with filepath, line range and other context that you can directly reference.
- code_relation_search: Use the language server to discover relations of specific code identifier. The code identifier is located by file_path, line_number and code_identifier. Returns relations include references and call_hierarchy, which you can specify in the relation_types parameter. Relation search supported languages: go, python, javascript, javascriptreact, typescript, typescriptreact.
Note: used when you need to do efficient codebase search, or discover code relations like references and call_hierarchy in local repositories`,
		Type:    MCPSourceAIME,
		MCPType: MCPTypeSTDIO, // STDIO连接类型
		Cmd:     "/usr/local/cosy/dgit-cosy-mcp",
		Args:    []string{"-default-recall-entity-results", "20"},
		Env: []string{
			"AUTH_KEY=mock-auth-key",
		},
	}
	AMap = &MCPProvider{
		ID:          "a_map",
		Name:        "AMap",
		Description: `AMap maps service in mainland China, Hong Kong, Macau and Taiwan, provides map-related functions such as location search, coordinate query, route planning, weather query, and so on.`,
		MCPType:     MCPTypeSTDIO, // STDIO连接类型
		Cmd:         "npx",
		Args:        []string{"-y", "@amap/amap-maps-mcp-server"},
		Env: []string{
			"AMAP_MAPS_API_KEY=72cbd4e721ca47af30f366ab572d8829",
		},
	}
	GoogleMaps = &MCPProvider{
		ID:          "google_maps",
		Name:        "GoogleMaps",
		Description: "Google Maps service in regions outside of mainland China",
		MCPType:     MCPTypeSTDIO, // STDIO连接类型
		Cmd:         "npx",
		Args:        []string{"-y", "@modelcontextprotocol/server-google-maps"},
		Env: []string{
			"GOOGLE_MAPS_API_KEY=AIzaSyDJuZYLj7zx8HlwQ7miGhpjShBTAffxX5k",
		},
	}
	Figma = &MCPProvider{
		ID:          "figma",
		Name:        "Figma",
		Description: "Figma design platform",
		MCPType:     MCPTypeSTDIO, // STDIO连接类型
		Cmd:         "node",
		Args:        []string{"/opt/mcp_servers/figma-mcp-chunked/build/index.js"},
		Env: []string{
			"FIGMA_ACCESS_TOKEN=*********************************************",
		},
	}
	Arxiv = &MCPProvider{
		ID:   "arxiv",
		Name: "Arxiv",
		Description: `The Arxiv service provides academic paper search, download, and reading capabilities, supporting both Arxiv and Google Scholar sources.
Main features:
- Search Arxiv papers: supports keyword search, category filtering, date range filtering, and sorting options
- Search Google Scholar papers: supports keyword search, year range filtering, and sorting options
- Download papers: supports downloading in PDF format and automatically converts to Markdown format for easy reading
- Read papers: supports reading the full content of downloaded papers`,
		MCPType: MCPTypeSTDIO, // STDIO连接类型
		Cmd:     "uv",
		Args: []string{
			"--directory",
			"/opt/mcp_servers/arxiv-mcp-server",
			"run",
			"arxiv-mcp-server",
			"--storage-path", fmt.Sprintf("/%s/papers", os.Getenv(rtentity.RuntimeEnvironWorkspacePath)),
		},
		Env: []string{
			"http_proxy=http://sys-proxy-rd-relay.byted.org:8118",
			"https_proxy=http://sys-proxy-rd-relay.byted.org:8118",
			"use_proxy=yes",
		},
	}
	Unsplash = &MCPProvider{
		ID:          "unsplash",
		Name:        "Unsplash",
		Description: "Unsplash image service is primarily used for searching stock images that are free of copyright risks. These images are mainly used for creating posters or other promotional materials. Note: Only English queries are supported. **tips: if you need high quality and none copyright risk pictures, use this tool first**",
		MCPType:     MCPTypeKiwis, // 暂时禁用此 MCP，设置工具为空；已有模版下发 Unsplash 工具不进行初始化
		Tools:       []iris.Action{},
		Cmd:         "uv",
		Args: []string{
			"--directory",
			"/opt/mcp_servers/unsplash-mcp-server",
			"run",
			"--with",
			"fastmcp",
			"fastmcp",
			"run",
			"/opt/mcp_servers/unsplash-mcp-server/server.py",
		},
		Env: []string{
			"UNSPLASH_ACCESS_KEY=JfMu2HOp-jhufz2_7wRjrdf7c29ULRK-7jb6QJtXskY",
		},
	}
	GoogleImageSearch = &MCPProvider{
		ID:          "google_image_search",
		Name:        "GoogleImageSearch",
		Description: "Google Image Search service provides a vast collection of high-quality, royalty-free images from the whole internet. You can find almost everything and every scene in the world. If user's requirement is about anything related to image or creating html with images, you can use this tool to get them.",
		MCPType:     MCPTypeSTDIO, // STDIO连接类型
		Cmd:         imagessearch.GoogleMCPCmd,
		Args:        imagessearch.GoogleMCPArgs,
		Env:         imagessearch.GoogleMCPEnv,
	}
	DouBaoImageSearch = &MCPProvider{
		ID:          imagessearch.ToolImageSearch,
		Name:        imagessearch.ToolImageSearch,
		Description: imagessearch.ToolImageSearchDescription,
		MCPType:     MCPTypeKiwis,
		Tools: []iris.Action{
			imagessearch.NewImagesSearchTool(),
		},
	}
	YFinance = &MCPProvider{
		ID:          "yfinance",
		Name:        "YFinance",
		Description: "Yahoo Finance data, provides a set of tools to fetch stock data, news, and other financial information.",
		MCPType:     MCPTypeSTDIO, // STDIO连接类型
		Cmd:         "npx",
		Args: []string{
			"-y",
			"@apify/actors-mcp-server",
			"--actors",
			"architjn/yahoo-finance",
		},
		Env: []string{
			"APIFY_TOKEN=**********************************************",
		},
	}

	Hummer = &MCPProvider{
		ID:          "hummer",
		Name:        "Hummer",
		Description: "支持查询安卓应用的构建/Sync相关信息，包含大盘数据，单点数据，趋势数据，列表明细，以及构建错误数据.\n主要特点\n- 多维度查询：支持按日期，增量/全量，debug/release，构建用户多维度查询相关数据\n- 细粒度构建数据查询：提供核心Task耗时数据查询，可用于进行耗时趋势变化归因",
		MCPType:     MCPTypeSTDIO,
		Cmd:         "npx",
		Args: []string{
			"--registry",
			"https://bnpm.byted.org",
			"-y",
			"@byted/build-mcp@latest",
			"mcp",
		},
	}

	Lark = &MCPProvider{
		ID:   "lark",
		Name: "lark",
		Description: `Lark can do the following things:
- Lark document/sheet downloader
- Generate a Lark/Feishu document from a html file or markdown file
- Update a Lark/Feishu document URL from a modified markdown file
- Convert csv、xlsx、xls files to Lark/Feishu sheets/table or Lark/Feishu Base. (将csv、xlsx、xls文件转换为飞书表格或多维表格)`,
		MCPType: MCPTypeKiwis, // Kiwis内置代码实现
		Byted:   false,        // Lark is always available to support generate lark docs as final output
		Tools: []iris.Action{
			lark.NewLarkDownloadTool(),
			lark.NewCreateLarkDoc(),
			lark.NewCreateLarkTable(),
			lark.NewUpdateLarkDoc(),
		},
	}
	Meego = &MCPProvider{
		ID:   "meego",
		Name: "meego",
		Description: `Meego, the lark(feishu) project manage platform, directly access Meego api service provides the following features:
- List and search meego spaces
- Get meego space details by simple name
- List all work items within space and time range
- List all work item of current user within all meego spaces and time range
- List work items by view url
- List work items by relation of a work item
- Get single work item detail by work item URL
- Get metadata for creating work items
- Create work items (from scratch or from file)
- Update work items
Key Concepts:
- Meego Space: 空间，是团队或产品的一个协作工作区
- Work Item: 工作项，是工作的一种形式（需求、缺陷、任务等）
- Sprint: 迭代，是交付工作的一种时间盒
- Version: 版本，是发布的一组功能
- Business Line: 业务线，是按业务领域或职能组织工作的一种方式
- View 视图，是工作项的一种展示方式，支持多种视图类型，如：全景视图、Story视图、Issue视图、自定义视图等`,
		MCPType: MCPTypeKiwis, // Kiwis内置代码实现
		Byted:   true,
		Tools: []iris.Action{
			meego.NewListProjects(),
			meego.NewSearchProjects(),
			meego.NewGetProjectDetailBySimpleName(),
			meego.NewIdentifyMeeGoURLType(),
			meego.NewListWorkItems(),
			meego.NewGetWorkItemDetailByURL(),
			meego.NewGetCreateWorkItemMetaData(),
			meego.NewCreateWorkItem(),
			meego.NewCreateWorkItemFromFile(),
			meego.NewUpdateWorkItem(),
			meego.NewWorkItemByView(),
			meego.NewWorkItemByRelation(),
			// user
			// meego.NewGetUserInfo(),
		},
	}
	BitsAnalysis = &MCPProvider{
		ID:   "bits_analysis",
		Name: "BitsAnalysis",
		Description: `Bits Analysis Platform is static code analysis platform (like SonarQube, etc). provides the following features:
- Analyze the URL resources of the bits analysis platform, obtain relevant information, and use it for the next operation.
- List issues in specific repository on the bits analysis platform based on given conditions
Key Concepts:
- Issue: 问题，是在代码库中发现的问题或潜在问题，例如代码质量问题、安全漏洞、语法错误等
- Rule: 规则,是一组预定义的标准，用于静态代码分析过程中检测代码库中的潜在问题。
- Scene: 应用场景，指的是代码分析的不同应用场景，如质量分析(quality_analysis)、合并检查(merge_check)、部署检查(deploy_check)、治理专项检查(resolution_check)`,
		MCPType: MCPTypeKiwis, // Kiwis内置代码实现
		Tools: []iris.Action{
			bitsanalysis.NewListIssues(),
			bitsanalysis.NewParseURL(),
		},
		Byted: true,
	}
	PagePass = &MCPProvider{
		ID:          "pagepass",
		Name:        "PagePass",
		Description: "PagePass is an automated testing tool specifically designed for Web end-to-end (E2E) scenarios. It supports automating interactive operations such as web browsing, clicking, form filling, and data scraping, and allows exporting workflows into reusable PagePass scripts.",
		MCPType:     MCPTypeSTDIO,
		Type:        MCPSourceAIME,
		Cmd:         "npx",
		Args: []string{
			"--registry",
			"https://bnpm.byted.org",
			"-y",
			"@pagepass/mcp@latest",
			"--headless",
			"true",
		},
		Env: []string{},
		BeforeConnectHooks: []func(run *iris.AgentRunContext, provider *MCPProvider){
			func(run *iris.AgentRunContext, provider *MCPProvider) {
				provider.Env = append(provider.Env, fmt.Sprintf(`X_JWT_TOKEN=%s`, run.GetEnv(rtentity.RuntimeEnvironUserCloudJWT)))
			},
		},
		Byted: true,
	}
	Codebase = &MCPProvider{
		ID:   "codebase",
		Name: "Codebase",
		Description: `The server provides various tools related to the Codebase repository, including:
- Repository: Get/list repositories, branches, and commits; create branches; get or update file content.
- Merge Requests (MR): Get/list/create/merge/update/comment on MRs; view MR diffs and comments; check mergeability.
- User: Retrieve current authenticated user info.
Terminology:
- **Codebase** refers to ByteDance's private git hosting platform (like github, gitlab, etc). The original "codebase" term is usually called repository in ByteDance. Platform URL: https://code.byted.org/.
MR stands for Merge Request, it's a feature provided by ByteDance's git hosting platform.
- **MR(Merge request)** is a request to merge changes from one non-default branch into another specified branch, which is commonly the master or main branch.`,
		MCPType: MCPTypeSTDIO,
		Type:    MCPSourceAIME,
		Cmd:     "npx",
		Args: []string{
			"--registry",
			"https://bnpm.byted.org",
			"-y",
			"@byted/mcp-proxy@latest",
		},
		Env: []string{
			"MCP_SERVER_PSM=bytedance.mcp.codebase",
			"MCP_GATEWAY_REGION=CN",
		},
		// 禁用提交 MR 工具，避免与内部 Action 实现冲突
		ToolsBlocklist: []string{
			"CreateBranch",       // Disable CreateBranch to avoid conflicts with Git operations
			"CreateMergeRequest", // Disable CreateMergeRequest to avoid conflicts with inner Subsubmit_merge_request
			"GetFile",
			"CreateOrUpdateFiles",
		},
		BeforeConnectHooks: []func(run *iris.AgentRunContext, provider *MCPProvider){
			func(run *iris.AgentRunContext, provider *MCPProvider) {
				provider.Env = append(provider.Env, fmt.Sprintf(`MCP_SERVER_CALL_TOOL_HEADERS=X-Code-User-JWT=%s`, run.GetEnv(rtentity.RuntimeNextCodeUserJWT)))
				provider.Env = append(provider.Env, fmt.Sprintf(`MCP_SERVER_CALL_TOOL_PARAMS=X-Code-User-JWT=%s`, run.GetEnv(rtentity.RuntimeNextCodeUserJWT)))
			},
		},
		// 当前 token 过期时间为 24h，这里 2h reconnect mcp server，避免因为 token 过期导致codebase mcp tools call 失败
		WithPeriodicReconnect: time.Hour * 2,
		Byted:                 true,
	}
	Argos = &MCPProvider{
		ID:          "argos",
		Name:        "Argos",
		Description: `Argos is an internal APM (Application Performance Monitoring) platform that provides log query services. It allows you to search and retrieve logs using log IDs.`,
		MCPType:     MCPTypeKiwis, // Kiwis内置代码实现
		Tools: []iris.Action{
			argos.NewLogQueryByIdTool(),
			argos.NewLogQueryByIDAndPsmTool(),
			argos.NewLogQueryByKeywordsTool(),
		},
		Byted: true,
	}
	// 先hardcode的方式接入，先只开放log.locate.code这个tool
	ArgosNew = &MCPProvider{
		ID:   "排障工具包",
		Name: "排障工具包",
		Description: `目前具有的工具包括：
- argos日志代码关联
- argos关键词搜索日志`,
		MCPType: MCPTypeStreamableHTTP,
		BaseURL: "https://aiops-argos.byted.org/agent_center/mcp/messages",
		ToolsWhitelist: []string{
			"log.locate.code",
			// 暂时只开放log.locate.code这个tool
			// "log.key_word",
			// "log.error_log",
		},
	}
	OnCall = &MCPProvider{
		ID:          "oncall",
		Name:        "oncall",
		Description: `The OnCall service provides tools to access OnCall platform data, including searching single or multiple OnCall issues by tenant ID, tenant name, time range, or directly from OnCall platform URLs, and retrieving details of a specific OnCall group chat. **IMPORTANT**: When users provide OnCall platform URLs (https://oncall.bytedance.net/admin/review/all), always prioritize using search_oncall_from_url tool to maintain the original search conditions and filters.`,
		MCPType:     MCPTypeKiwis, // Kiwis内置代码实现
		Byted:       true,
		Tools: []iris.Action{
			oncall.NewListOnCallTool(),
			oncall.NewGetOnCallChatMessage(),
			oncall.NewListOnCallFromURLTool(),
		},
	}
	DevMind = &MCPProvider{
		ID:          "devmind",
		Name:        "DevMind",
		Description: `DevMind is an internal platform that offers tools for querying insight report and R&D efficiency metrics, retrieving business and reporting nodes, and managing favorites. These metrics include, but are not limited to, code commit volume, defect rates, and requirement throughput.`,
		MCPType:     MCPTypeKiwis, // Kiwis内置代码实现
		Byted:       true,
		Tools: []iris.Action{
			devmind.NewQueryStoryMetricsTool(),
			devmind.NewQueryBusinessNodesTool(),
			devmind.NewQueryReportNodesTool(),
			devmind.NewQueryMetricDataTool(),
			devmind.NewManageFavoriteTool(),
			devmind.NewInsightReportsTool(),
			devmind.NewInsightReportTaskTool(),
			devmind.NewWriteTaskResultTool(),
			devmind.NewNextStepInstructionTool(),
			devmind.NewQueryDataModelTool(),
			devmind.NewQueryAnalysisMetricTool(),
			devmind.NewQueryAnalysisDimensionTool(),
			devmind.NewQueryChartUrlTool(),
			devmind.NewQueryChartInfoTool(),
		},
	}
	ToolHelper = &MCPProvider{
		ID:   ToolHelperID,
		Name: "ToolHelper",
		Description: `Tool helper for advanced operations:
- Check Loop Tool: Verify if a specific tool is available for loop execution before attempting to use loop_mcp_tool.
- Loop MCP Tool: Execute the same tool multiple times with different parameters and collect all results.`,
		Byted: false,
		Tools: []iris.Action{
			NewCheckLoopTool(),
			NewLoopTool(),
		},
	}
	// PPT = &MCPProvider{
	// 	ID:          "ppt_generator",
	// 	Name:        "PPT Generator",
	// 	Description: `PowerPoint presentation generation service that creates professional PPTX files using predefined templates and JSON configuration.`,
	// 	MCPType:     MCPTypeSTDIO, // STDIO连接类型
	// 	Cmd:         "uv",
	// 	Args: []string{
	// 		"--directory",
	// 		"/opt/mcp_servers/pptx_mcp_server",
	// 		"run",
	// 		"pptx-mcp-server",
	// 		"--storage-path", fmt.Sprintf("/%s/presentations", os.Getenv(rtentity.RuntimeEnvironWorkspacePath)),
	// 	},
	// }
	VisActorChartAssistant = &MCPProvider{
		ID:          "visactor_chart_assistant",
		Name:        "VisActor Chart Assistant",
		Description: `The server provides various tools related to the VisActor Chart Assistant`,
		MCPType:     MCPTypeCloudSDK,
		Type:        MCPSourceCloud,
		PSM:         "bytedance.mcp.visactor_chart_assistant",
		CloudSDKClientOptions: []any{
			bytedmcpclient.WithHTTPClientCallToolMetaHeadersFromGenerator(func(ctx context.Context, request any) map[string]string {
				return mcpGoRequestFunc(nil, false)(ctx)
			}),
			bytedmcpclient.WithHTTPClientCallToolMetaParamsFromGenerator(func(ctx context.Context, request any) map[string]any {
				m := map[string]any{}
				if value := cast.ToString(ctx.Value(VisactorChartAssistantMCPToken)); len(value) > 0 {
					m["api-key"] = value
				}
				return m
			}),
		},
		Byted: true,
	}
	BitsWorkFlow = &MCPProvider{
		ID:          "bits_workflow",
		Name:        "Bits Workflow",
		Description: `The server provides various tools related to the Bits Workflow`,
		MCPType:     MCPTypeCloudSDK,
		Type:        MCPSourceCloud,
		PSM:         "bytedance.mcp.bits_workflow",
		Byted:       true,
	}
	RDS = &MCPProvider{
		ID:          "rds",
		Name:        "RDS Tools",
		Description: "RDS tools providing a set of tools to access the Database platform, including query table info and slow logs etc.",
		MCPType:     MCPTypeKiwis, // Kiwis内置代码实现
		Byted:       true,
		Tools: []iris.Action{
			rds.NewListRDSTableTool(),
			rds.NewGetUserTableSchemaTool(),
			rds.NewGetSlogLogSummaryTool(),
		},
	}
)

// 定义调用工具的最简方法
type CallToolFunc func(run *iris.AgentRunContext, input map[string]any) (output map[string]any, err error)

type MCPProviderRegistry struct {
	Providers []*MCPProvider
	// mcp客户端 map
	clients *syncwrap.Map[string, client.MCPClient]
	// periodicReconnectClients 定时重连客户端,用于定时重连, value 为下次重连时间
	periodicReconnectClients *syncwrap.Map[string, time.Time]
	// 已获取的工具集map
	actionsMap *syncwrap.Map[string, CallToolFunc]
	mu         sync.Mutex
	mcpCallId  *atomic.Int32
}

var _ iris.Disposable = &MCPProviderRegistry{}

func init() {
	ProviderDict = map[string]*MCPProvider{
		AMap.ID:              AMap,
		GoogleMaps.ID:        GoogleMaps,
		Figma.ID:             Figma,
		Arxiv.ID:             Arxiv,
		Unsplash.ID:          Unsplash,
		GoogleImageSearch.ID: GoogleImageSearch,
		YFinance.ID:          YFinance,
		Meego.ID:             Meego,
		Lark.ID:              Lark,
		BitsAnalysis.ID:      BitsAnalysis,
		Codebase.ID:          Codebase,
		Argos.ID:             Argos,
		OnCall.ID:            OnCall,
		DevMind.ID:           DevMind,
		Hummer.ID:            Hummer,
		ArgosNew.ID:          ArgosNew,
		ToolHelper.ID:        ToolHelper,
		PagePass.ID:          PagePass,
		// PPT.ID:               PPT,
		VisActorChartAssistant.ID: VisActorChartAssistant,
		BitsWorkFlow.ID:           BitsWorkFlow,
		DouBaoImageSearch.ID:      DouBaoImageSearch,
		RDS.ID:                    RDS,
	}
	// cosy mcp currently only support amd64
	if runtime.GOARCH == "amd64" {
		ProviderDict[Cosy.ID] = Cosy
	}
	ProviderRegistry = MCPProviderRegistry{
		clients:                  &syncwrap.Map[string, client.MCPClient]{},
		periodicReconnectClients: &syncwrap.Map[string, time.Time]{},
		actionsMap:               &syncwrap.Map[string, CallToolFunc]{},
		mcpCallId:                &atomic.Int32{},
	}

	// Initialize DevOpsAgentProviderRegistry for devops agent tool MCP providers
	DevOpsAgentProviderRegistry = MCPProviderRegistry{
		Providers: []*MCPProvider{
			// DevOps Agent Tool MCP Providers - private to devops agent tool only
			{
				ID:          "scm_build",
				Name:        "SCM Build",
				Description: "SCM build service provides tools for building applications from source code repositories. Supports various build configurations, dependency management, and artifact generation.",
				MCPType:     MCPTypeCloudSDK,
				Type:        MCPSourceCloud,
				PSM:         "bytedance.mcp.scm_build",
				Byted:       true,
			},
			{
				ID:          "tce_deploy",
				Name:        "TCE Deploy",
				Description: "TCE deployment service provides tools for deploying applications to various environments. Supports container orchestration, service management, and deployment monitoring.",
				MCPType:     MCPTypeCloudSDK,
				Type:        MCPSourceCloud,
				PSM:         "bytedance.mcp.tce_deploy",
				Byted:       true,
			},
			{
				ID:          "api_test",
				Name:        "API Test",
				Description: "API test service provides comprehensive API testing capabilities including function testing, HTTP endpoint testing, and automated test execution.",
				MCPType:     MCPTypeCloudSDK,
				Type:        MCPSourceCloud,
				PSM:         "bytedance.mcp.api_test_mcp",
				Byted:       true,
			},
			{
				ID:          "api_doc",
				Name:        "API Doc",
				Description: "API documentation service provides access to API specifications, documentation, and metadata for API testing and integration.",
				MCPType:     MCPTypeCloudSDK,
				Type:        MCPSourceCloud,
				PSM:         "bytedance.bam.api_doc_mcp",
				Byted:       true,
			},
		},
		clients:                  &syncwrap.Map[string, client.MCPClient]{},
		periodicReconnectClients: &syncwrap.Map[string, time.Time]{},
		actionsMap:               &syncwrap.Map[string, CallToolFunc]{},
		mcpCallId:                &atomic.Int32{},
	}

	// loop工具init
	// 内部工具也实现可循环遍历
	for pid, p := range ProviderDict {
		// 系统工具不参与遍历
		if pid == ToolHelper.ID {
			continue
		}
		for _, tool := range p.Tools {
			ProviderRegistry.registerAction(McpToolName(p.ID, tool.Name()), func(run *iris.AgentRunContext, input map[string]any) (output map[string]any, err error) {
				step := &iris.AgentRunStep{
					Inputs: input,
				}
				err = tool.Execute(run, step)
				return step.Outputs, err
			})
		}
	}
	// loop特殊支持非mcp工具
	var extraLoopAction []iris.Action
	extraLoopAction = append(extraLoopAction, gresult.Of(llmtool.NewVisionToolset(nil, nil)).ValueOrZero()...)
	extraLoopAction = append(extraLoopAction, lark.NewLarkDownloadTool())
	for _, tool := range extraLoopAction {
		ProviderRegistry.registerAction(tool.Name(), func(run *iris.AgentRunContext, input map[string]any) (output map[string]any, err error) {
			step := &iris.AgentRunStep{
				Inputs: input,
			}
			err = tool.Execute(run, step)
			return step.Outputs, err
		})
	}
	// invisibleProviderID = append(invisibleProviderID, PPT.ID)
}

var ProviderDict map[string]*MCPProvider

var ProviderRegistry MCPProviderRegistry

// DevOpsAgentProviderRegistry is a dedicated registry for devops agent tool MCP providers
// This registry is separate from the global ProviderRegistry and only contains MCP providers
// that are private to the devops agent tool
var DevOpsAgentProviderRegistry MCPProviderRegistry

func IsBuiltInProvider(providerID string) bool {
	_, ok := ProviderDict[providerID]
	return ok
}

func CheckAndGetBuildInProvider(providerID string) (*MCPProvider, bool) {
	p, ok := ProviderDict[providerID]
	return p, ok
}

// IsLocalFuncs 是否为本地工具集。因为早期Aime的“mcp”实际上都是Local Funcs，而不是标准的MCP。
func IsLocalFuncs(providerID string) bool {
	provider := ProviderDict[providerID]
	if provider == nil {
		return false
	}
	return len(provider.Tools) > 0
}

func (r *MCPProviderRegistry) GetProvider(providerID string) (provider *MCPProvider) {
	provider, _ = lo.Find(r.Providers, func(provider *MCPProvider) bool {
		return provider.ID == providerID
	})
	return
}

func (r *MCPProviderRegistry) Dispose() {
	for _, provider := range r.Providers {
		if cli, ok := r.clients.Load(provider.ID); ok {
			cli.Close()
		}
	}
}

// apify adds some extra tools for each mcp server, we need to filter them out
var apifyToolNames = []string{
	"search-actors",
	"get-actor-details",
	"help-tool",
	"apify-actor-help-tool",
	"search-apify-docs",
	"fetch-apify-docs",
	"add-actor",
}

func (r *MCPProviderRegistry) ListTools(run *iris.AgentRunContext, providerID string) ([]iris.Action, error) {
	provider := r.GetProvider(providerID)
	if provider == nil {
		return nil, fmt.Errorf("provider %s not found", providerID)
	}

	if provider.Tools != nil {
		return lo.Map(provider.Tools, func(tool iris.Action, _ int) iris.Action {
			return actions.NewTool(actions.NewToolOption{
				Name:        McpToolName(providerID, tool.Name()),
				Description: tool.Description(),
				Input:       tool.InputSpec(),
				Output:      tool.OutputSpec(),
				Impl: func(c *iris.AgentRunContext, step *iris.AgentRunStep) (map[string]any, error) {
					err := tool.Execute(c, step)
					return step.Outputs, iris.NewRecoverable(err)
				},
			})
		}), nil
	}

	cli, err := r.Connect(run, provider)
	if err != nil {
		run.GetLogger().Debug("connect server failed: cli: %s, err: %v", provider.ID, err)
		return nil, err
	}

	// 创建一个带超时的上下文
	listCtx, cancel := context.WithTimeout(NewCtxForInjectTokenBeforeRequest(run), MCPListToolsTimeout)
	defer cancel()

	// 重试机制，当错误包含 session 时重试
	var res *gomcp.ListToolsResult
	for attempt := 0; attempt <= MCPSessionErrorMaxRetries; attempt++ {
		res, err = cli.ListTools(listCtx, gomcp.ListToolsRequest{})
		if err == nil {
			break
		}

		// 检查错误是否包含 session，如果包含且还有重试次数则重试
		if strings.Contains(strings.ToLower(err.Error()), "session") && attempt < MCPSessionErrorMaxRetries {
			run.GetLogger().Warnf("list tools failed with session error (attempt %d/%d): %v, retrying...", attempt+1, MCPSessionErrorMaxRetries+1, err)
			continue
		}

		// 如果不是 session 错误或者已经达到最大重试次数，跳出循环
		break
	}

	if err != nil {
		run.GetLogger().Debug("list tools failed: %v", err)
		return nil, err
	}

	return gslice.FilterMap(res.Tools, func(tool gomcp.Tool) (iris.Action, bool) {
		// 过滤统一黑名单
		if gslice.Contains(apifyToolNames, tool.Name) {
			return nil, false
		}
		// 过滤白名单
		if len(provider.ToolsWhitelist) > 0 && !gslice.Contains(provider.ToolsWhitelist, tool.Name) {
			return nil, false
		}
		// 过滤黑名单
		if gslice.Contains(provider.ToolsBlocklist, tool.Name) {
			return nil, false
		}
		// 返回结果
		action, err := r.ConvertMCPTool(run, providerID, tool)
		if err != nil {
			run.GetLogger().Errorf("failed to convert mcp tool %s: %v", tool.Name, err)
			return nil, false
		}
		return action, true
	}), nil
}

func (r *MCPProviderRegistry) Connect(run *iris.AgentRunContext, provider *MCPProvider) (cli client.MCPClient, err error) {
	ctx := NewCtxForInjectTokenBeforeRequest(run)

	//  mcp client 重连检查，重连需释放资源，即 client.Close()
	isPeriodicReconnect := lo.IsNotEmpty(provider.WithPeriodicReconnect)
	if isPeriodicReconnect {
		reconnectTime, ok := r.periodicReconnectClients.Load(provider.ID)
		if ok && time.Now().After(reconnectTime) {
			r.periodicReconnectClients.Delete(provider.ID)
			r.mu.Lock()
			// 释放资源
			if expiredCli, ok := r.clients.Load(provider.ID); ok {
				run.GetLogger().Infof("[connect] periodic reconnect for provider [%s] release resource", provider.ID)
				r.clients.Delete(provider.ID)
				expiredCli.Close()
			}
			r.mu.Unlock()
			run.GetLogger().Infof("[connect] periodic reconnect for provider [%s]", provider.ID)
		}
	}
	// 快速检查是否已有可用的客户端
	if cli, ok := r.clients.Load(provider.ID); ok {
		// 检查客户端是否仍然有效
		// 返回前应该 先ping一波，确认cli是否有效（其实应该建连后就持续ping的，但是太浪费网络资源了，用） https://modelcontextprotocol.io/specification/2025-03-26/basic/utilities/ping#ping
		pingCtx, cancel := context.WithTimeout(ctx, MCPPingTimeout)
		defer cancel()
		err = cli.Ping(pingCtx)
		if err == nil {
			return cli, nil
		}
		run.GetLogger().Warnf("[connect] ping server [%s] failed: %v", provider.ID, err)
		// todo 这里其实应该要有优雅的关闭cli代码

		// 如果ping失败，删除无效的客户端
		r.clients.Delete(provider.ID)
	}

	// 获取锁进行初始化
	r.mu.Lock()
	defer r.mu.Unlock()

	run.GetLogger().Infof("[connect] start trying to connect server [%s]", provider.ID)

	// 双重检查，避免在获取锁期间其他goroutine已经初始化了客户端
	if cli, ok := r.clients.Load(provider.ID); ok {
		run.GetLogger().Infof("[connect] try to ping server [%s]", provider.ID)
		pingCtx, cancel := context.WithTimeout(ctx, MCPPingTimeout)
		defer cancel()
		if err := cli.Ping(pingCtx); err == nil {
			run.GetLogger().Infof("[connect] ping server [%s] success", provider.ID)
			return cli, nil
		}
		r.clients.Delete(provider.ID)
	}

	run.GetLogger().Infof("[connect] try to connect server [%s]", provider.ID)

	for _, beforeConnectHook := range provider.BeforeConnectHooks {
		beforeConnectHook(run, provider)
	}

	// 创建新的客户端连接
	for attempt := 0; attempt <= MCPConnectMaxRetries; attempt++ {
		cli, err = ConnectMCP(ctx, provider)
		if err == nil {
			break
		}
		run.GetLogger().Errorf("[connect] connect server [%s] failed: %v, retrying...(attempt %d/%d)", provider.ID, err, attempt+1, MCPConnectMaxRetries+1)
	}

	if err != nil {
		run.GetLogger().Errorf("[connect] connect server [%s] failed: %v", provider.ID, err)
		return nil, err
	}
	run.GetLogger().Infof("[connect] connect server [%s] success", provider.ID)

	// 存储新的客户端
	r.clients.Store(provider.ID, cli)
	// 记录下次重连时间
	if isPeriodicReconnect {
		r.periodicReconnectClients.Store(provider.ID, time.Now().Add(provider.WithPeriodicReconnect))
	}
	return cli, nil
}

func ConnectMCP(ctx context.Context, provider *MCPProvider) (cli client.MCPClient, err error) {
	// 二期扩展：根据MCPType选择连接方式
	switch provider.MCPType {
	case MCPTypeKiwis:
		// Kiwis内置代码实现，有Tools字段，不需要外部连接
		return nil, fmt.Errorf("MCPTypeKiwis does not need external connection, tools are implemented in Kiwis code")

	case MCPTypeSTDIO:
		// 字节云来源的自动注入AIME的服务账号秘钥
		if provider.Type == MCPSourceCloud || (provider.Type == MCPSourceAIME && strings.HasPrefix(provider.Name, "SlardarApp")) {
			// 先hardcode传递个人jwt token给slardar，快速落地
			if strings.HasPrefix(provider.Name, "SlardarApp") {
				provider.Env = append(provider.Env, fmt.Sprintf("USER_JWT_TOKEN=%s", cast.ToString(ctx.Value(CommonHeaderXJwtToken))))
				provider.Env = append(provider.Env, fmt.Sprintf("MCP_REQUEST_TIMEOUT_MS=%d", MCPCallToolTimeout.Milliseconds()))
			} else {
				provider.Env = append(provider.Env, fmt.Sprintf("SERVICE_ACCOUNT_SECRET_KEY=%s", aimeMCPServerAccountSecret))
			}
		}
		cli, err = client.NewStdioMCPClient(provider.Cmd, provider.Env, provider.Args...)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}

	case MCPTypeSSE:
		scli, err := client.NewSSEMCPClient(provider.BaseURL, client.WithHeaderFunc(mcpGoRequestFunc(provider.Header, provider.SkipCloudJWTAuth)))
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}

		// 使用独立的超时机制启动SSE连接，避免上下文取消影响长连接
		err = startSSEWithTimeout(ctx, scli, MCPStartTimeout)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		cli = scli

	case MCPTypeStreamableHTTP:
		// 二期新增：StreamableHTTP连接
		hcli, err := client.NewStreamableHttpClient(provider.BaseURL,
			transport.WithHTTPHeaderFunc(mcpGoRequestFunc(provider.Header, provider.SkipCloudJWTAuth)),
			transport.WithHTTPTimeout(0),
		)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		// 虽然其实StreamableHTTP不需要调用Start，但是为了兼容性，还是调用一下，因为有Notification机制
		err = hcli.Start(ctx)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		cli = hcli
	case MCPTypeCloudSDK:
		// 二期新增：CloudSDK连接
		if provider.PSM == "" {
			return nil, fmt.Errorf("PSM is required for CloudSDK MCP type")
		}

		sdkClient, err := newCloudSDKClient(provider.PSM, provider.CloudSDKClientOptions, provider.CloudSDKBytedMCPClientOptions)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		return sdkClient, err
	case MCPTypeDeprecated:
		fallthrough
	default:
		cli, err = initMCP(ctx, provider)
		if err != nil {
			return nil, err
		}
	}

	// 为 Initialize 操作添加超时控制
	initCtx, initCancel := context.WithTimeout(ctx, MCPInitTimeout)
	defer initCancel()
	request := gomcp.InitializeRequest{}
	request.Params.ProtocolVersion = gomcp.LATEST_PROTOCOL_VERSION
	request.Params.ClientInfo = gomcp.Implementation{
		Name:    fmt.Sprintf("%s-%s-cli", provider.ID, uuid.New().String()),
		Version: "1.0.0",
	}
	_, err = cli.Initialize(initCtx, request)
	if err != nil {
		// 针对SSE init超时做处理，很可能是链接授权信息过期了，建议用户修改链接，或者申请权限
		if provider.MCPType == MCPTypeSSE && errors.Is(err, context.DeadlineExceeded) {
			return nil, iris.NewRecoverable(errors.Wrapf(ErrMCPInitializationTimeoutWithSuggestion, "failed to initialize mcp client: %s, url: %s", provider.ID, provider.BaseURL))
		}
		return nil, iris.NewRecoverable(errors.Wrapf(err, "failed to initialize mcp client: %s, url: %s", provider.ID, provider.BaseURL))
	}
	return cli, err
}

const ExtraOutputDescKey = "aime_mcp_extra_desc"

func (r *MCPProviderRegistry) ConvertMCPTool(run *iris.AgentRunContext, providerID string, tool gomcp.Tool) (iris.Action, error) {
	callFunc := func(run *iris.AgentRunContext, input map[string]any) (output map[string]any, err error) {
		startTime := time.Now()
		defer func() {
			run.GetLogger().Info("%s Server mcp tool %s call total cost: %s", providerID, tool.Name, time.Since(startTime))
		}()
		cli, err := r.Connect(run, r.GetProvider(providerID))
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		callCtx, cancelFunc := context.WithTimeout(NewCtxForInjectTokenBeforeRequest(run), MCPCallToolTimeout)
		defer cancelFunc()

		// 重试机制，当错误包含 session 时重试
		var res *gomcp.CallToolResult
		for attempt := 0; attempt <= MCPSessionErrorMaxRetries; attempt++ {
			callRequest := gomcp.CallToolRequest{}
			callRequest.Params.Name = tool.Name
			callRequest.Params.Arguments = input
			res, err = cli.CallTool(callCtx, callRequest)
			if err == nil {
				break
			}

			// 检查错误是否包含 session，如果包含且还有重试次数则重试
			if strings.Contains(strings.ToLower(err.Error()), "session") && attempt < MCPSessionErrorMaxRetries {
				run.GetLogger().Warnf("%s Server mcp tool %s call failed with session error (attempt %d/%d): %v, retrying...", providerID, tool.Name, attempt+1, MCPSessionErrorMaxRetries+1, err)
				continue
			}

			// 如果不是 session 错误或者已经达到最大重试次数，跳出循环
			break
		}
		if err != nil {
			if errors.Is(err, context.DeadlineExceeded) {
				run.GetLogger().Warnf("%s Server mcp tool %s call timeout: %v, cost: %s", providerID, tool.Name, err, time.Since(startTime))
				return nil, iris.NewRecoverable(errors.Errorf("[%s]call tool timeout", providerID))
			}
			run.GetLogger().Errorf("%s Server mcp tool %s call error: %v, cost: %s", providerID, tool.Name, err, time.Since(startTime))
			return nil, iris.NewRecoverable(err)
		}
		run.GetLogger().Info("%s Server mcp tool %s call request cost: %s", providerID, tool.Name, time.Since(startTime))

		// 处理图片内容
		images := ExtractImageContent(res.Content)
		imagePaths := make([]string, 0)
		for _, image := range images {
			buf := make([]byte, len(image.Data))
			decoded, err := base64.StdEncoding.Decode(buf, []byte(image.Data))
			if err != nil {
				return nil, iris.NewRecoverable(err)
			}
			mime := mimetype.Lookup(image.MIMEType)
			imageFileName := fmt.Sprintf("%s.%s", tool.Name, mime.Extension())
			err = os.WriteFile(imageFileName, buf[:decoded], os.ModePerm)
			if err != nil {
				run.GetLogger().Errorf("failed to write image to file: %v", err)
				return nil, iris.NewRecoverable(err)
			}
			imagePaths = append(imagePaths, imageFileName)
		}

		textContents := ExtractTextContent(res.Content)
		totalLength := lo.Reduce(textContents, func(agg int, item string, _ int) int {
			return agg + prompt.CountToken(item)
		}, 0)

		// 默认将输出也写入一份到文件中
		var outputDesc string
		// 如果输出过长，将内容保存到本地文件
		outputDir := "mcp_outputs"
		if err := os.MkdirAll(outputDir, os.ModePerm); err != nil {
			return nil, iris.NewRecoverable(fmt.Errorf("创建输出目录失败: %v", err))
		}

		// 为每个文本内容创建单独的文件
		filePaths := make([]string, 0)
		timestamp := fmt.Sprintf("%d", r.mcpCallId.Add(1))
		textFilePaths, err := saveTextContexts(providerID, tool.Name, textContents, outputDir, timestamp)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		filePaths = textFilePaths

		// 移动图片文件到输出目录
		for i, imagePath := range imagePaths {
			oldPath := imagePath
			newFileName := fmt.Sprintf("%s_%s_%s_%d%s", providerID, tool.Name, timestamp, i, filepath.Ext(oldPath))
			newPath := filepath.Join(outputDir, sanitizeFileName(newFileName))
			if err := os.Rename(oldPath, newPath); err != nil {
				run.GetLogger().Errorf("failed to move image file: %v", err)
				return nil, iris.NewRecoverable(err)
			}
			imagePaths[i] = newPath
		}

		if totalLength > MaxResponseTokens {
			// 如果输出过长，不直接把输出展示放到TextContents中，避免 token 超限。
			outputDesc = fmt.Sprintf("Output content is too long (%d KB), saved to the following files:\n%s",
				totalLength/1024,
				strings.Join(filePaths, "\n"))
			if len(imagePaths) > 0 {
				outputDesc += fmt.Sprintf("\n\nImage files:\n%s", strings.Join(imagePaths, "\n"))
			}
			err = mapstructure.Decode(agententity.MCPCallOutput{
				TextContents:  []string{},
				ImageContents: imagePaths,
			}, &output)
		} else {
			outputDesc = fmt.Sprintf("Output is also saved to the following files:\n%s",
				strings.Join(filePaths, "\n"))
			if len(imagePaths) > 0 {
				outputDesc += fmt.Sprintf("\n\nImage files:\n%s", strings.Join(imagePaths, "\n"))
			}
			err = mapstructure.Decode(agententity.MCPCallOutput{
				TextContents:  textContents,
				ImageContents: imagePaths,
			}, &output)
		}

		if output != nil {
			output[ExtraOutputDescKey] = outputDesc + "\n"
		}

		return output, iris.NewRecoverable(err)
	}
	// 所有在这里注册过的工具将直接存在actionsMap，之后LoopTool方法将会使用
	// 重复load没关系
	r.registerAction(McpToolName(providerID, tool.Name), callFunc)
	inputSchema, err := FromMCPSchema(tool.InputSchema)
	if err != nil {
		run.GetLogger().Errorf("failed to convert mcp tool schema %s: %v", tool.Name, err)
		return nil, err
	}

	return actions.NewTool(actions.NewToolOption{
		Name:        McpToolName(providerID, tool.Name),
		Description: tool.Description,
		Input:       inputSchema,
		Impl: func(run *iris.AgentRunContext, step *iris.AgentRunStep) (output map[string]any, err error) {
			return callFunc(run, step.Inputs)
		},
	}), nil
}

func (r *MCPProviderRegistry) loadAction(mcpToolName string) (CallToolFunc, bool) {
	if fc, ok := r.actionsMap.Load(mcpToolName); ok {
		return fc, ok
	}
	if fc, ok := r.actionsMap.Load(strings.ReplaceAll(mcpToolName, "-", "_")); ok {
		return fc, ok
	}
	return nil, false
}

func (r *MCPProviderRegistry) registerAction(mcpToolName string, callFunc CallToolFunc) {
	// 注册原MCP工具名称
	r.actionsMap.Store(mcpToolName, callFunc)
	// 注册转义后的MCP工具名称
	fcName := agents.SanitizeToolName(mcpToolName)
	r.actionsMap.Store(fcName, callFunc)
	// 由于模型容易把中划线处理成下划线，存一份数据，是把所有-替换成_的
	if strings.Contains(mcpToolName, "-") {
		r.actionsMap.Store(strings.ReplaceAll(mcpToolName, "-", "_"), callFunc)
	}
	if strings.Contains(fcName, "-") {
		r.actionsMap.Store(strings.ReplaceAll(fcName, "-", "_"), callFunc)
	}
}

func McpToolName(providerID string, toolName string) string {
	if gslice.Contains(invisibleProviderID, providerID) {
		return fmt.Sprintf("%s_%s", providerID, toolName)
	}
	return fmt.Sprintf("mcp:%s_%s", providerID, toolName)
}

func ExtractTextContent(contents []gomcp.Content) []string {
	result := make([]string, 0)
	for _, content := range contents {
		val := reflect.ValueOf(content)
		if val.FieldByName("Type").String() == string(mcp.ContentTypeText) {
			result = append(result, val.FieldByName("Text").String())
		}
	}
	return result
}

// startSSEWithTimeout 使用独立的超时机制启动 SSE 连接
// 避免使用 context.WithTimeout 导致的连接取消问题
func startSSEWithTimeout(ctx context.Context, cli *client.Client, timeout time.Duration) error {
	// 创建结果通道
	resultChan := make(chan error, 1)

	// 在 goroutine 中启动连接
	go panics.Try(func() {
		err := cli.Start(ctx)
		resultChan <- err
	})

	// 等待结果或超时
	select {
	case err := <-resultChan:
		// 连接成功或失败
		return err
	case <-ctx.Done():
		return fmt.Errorf("SSE connection timeout after %v", timeout)
	case <-time.After(timeout):
		// 超时
		return fmt.Errorf("SSE connection timeout after %v", timeout)
	}
}

// initMCP 兼容旧的逻辑（基于provider字段判断）因为有部分runtime里没有MCPType字段
// Deprecated:
func initMCP(ctx context.Context, provider *MCPProvider) (cli client.MCPClient, err error) {
	if provider.Cmd != "" {
		// 字节云来源的自动注入AIME的服务账号秘钥
		if provider.Type == MCPSourceCloud {
			// 先hardcode传递个人jwt token给slardar，快速落地
			if strings.HasPrefix(provider.Name, "SlardarApp") {
				provider.Env = append(provider.Env, fmt.Sprintf("USER_JWT_TOKEN=%s", cast.ToString(ctx.Value(CommonHeaderXJwtToken))))
				provider.Env = append(provider.Env, fmt.Sprintf("MCP_REQUEST_TIMEOUT_MS=%d", MCPCallToolTimeout.Milliseconds()))
			} else {
				provider.Env = append(provider.Env, fmt.Sprintf("SERVICE_ACCOUNT_SECRET_KEY=%s", aimeMCPServerAccountSecret))
			}
		}
		cli, err = client.NewStdioMCPClient(provider.Cmd, provider.Env, provider.Args...)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
	} else if provider.BaseURL != "" {
		// TODO: 后面如果jwt支持刷新的话，这里需要有刷新的机制
		scli, err := client.NewSSEMCPClient(provider.BaseURL, client.WithHeaders(
			map[string]string{
				"x-jwt-token":    cast.ToString(ctx.Value(CommonHeaderXJwtToken)),
				"x-user-open-id": cast.ToString(ctx.Value(CommonHeaderUserOpenLarkID)),
			}))
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}

		// 使用独立的超时机制启动SSE连接，避免上下文取消影响长连接
		err = startSSEWithTimeout(ctx, scli, MCPStartTimeout)
		if err != nil {
			return nil, iris.NewRecoverable(err)
		}
		cli = scli
	} else {
		return nil, fmt.Errorf("unknown mcp provider type: %s", provider.ID) // should only happen during development
	}
	return cli, nil
}

func sanitizeFileName(name string) string {
	name = strings.ReplaceAll(name, "/", "_")
	return name
}

func saveTextContexts(providerID, toolName string, textContents []string, outputDir, timestamp string) ([]string, error) {
	filePaths := make([]string, 0, len(textContents))
	for i, content := range textContents {
		fileName := fmt.Sprintf("%s_%s_%s_%d.txt", providerID, toolName, timestamp, i)
		// 如果是JSON输出，格式化再存储文件
		if jsoniter.Valid([]byte(content)) {
			fileName = fmt.Sprintf("%s_%s_%s_%d.json", providerID, toolName, timestamp, i)
			var out bytes.Buffer
			if err := json.Indent(&out, []byte(content), "", "  "); err == nil {
				content = out.String()
			}
		}
		filePath := filepath.Join(outputDir, sanitizeFileName(fileName))
		if err := os.WriteFile(filePath, []byte(content), os.ModePerm); err != nil {
			return nil, iris.NewRecoverable(fmt.Errorf("写入文件失败: %v", err))
		}
		filePaths = append(filePaths, filePath)
	}
	return filePaths, nil
}
