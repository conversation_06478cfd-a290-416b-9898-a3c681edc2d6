package browser

import (
	_ "embed"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/fs"
	"net"
	"os"
	"os/exec"
	"os/user"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	copilotentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"github.com/cenkalti/backoff/v4"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/browser_use"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	mcpclient "github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type MCPService struct {
	run                     *iris.AgentRunContext
	cmd                     *exec.Cmd
	virtualGraphicProcesses []*exec.Cmd
}

type ScreenshotArtifact struct {
	ID        string `json:"id"`
	MimeType  string `json:"mime_type"`
	Path      string `json:"path"`
	LocalPath string `json:"local_path,omitempty"`
}

var _ iris.Disposable = (*MCPService)(nil)

type MCPClient struct {
	Client *mcpclient.Client
}

var _ iris.Disposable = (*MCPClient)(nil)

const (
	browserUseMCPPort             = 9227
	browserUseMCPPortForWebSearch = 9226
	browserUseDir                 = "/opt/browser-use"
)

var (
	started = make(chan struct{})
)

func checkPort(port int) error {
	conn, err := net.Dial("tcp", fmt.Sprintf("localhost:%d", port))
	if err != nil {
		return err
	}
	conn.Close()
	return nil
}

// StartMCPService starts browser use mcp service
// Implemented using python utilizing browser-use library
// MCP server: agentsphere/agents/actions/external/browser_use
func StartMCPService(run *iris.AgentRunContext) (*MCPService, error) {
	// release bundled browser-use server
	_ = os.MkdirAll(browserUseDir, 0755)

	err := fs.WalkDir(browser_use.ServerFS, ".", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录（文件写入时会自动创建）
		if d.IsDir() {
			return nil
		}

		// 读取嵌入的文件内容
		data, err := fs.ReadFile(browser_use.ServerFS, path)
		if err != nil {
			return err
		}

		// 构造目标路径（如 /opt/browser-use/server/server.py）
		targetPath := filepath.Join(browserUseDir, path)

		// 创建父目录
		if err := os.MkdirAll(filepath.Dir(targetPath), 0755); err != nil {
			return err
		}

		// 写入文件
		if err := os.WriteFile(targetPath, data, 0644); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("写入 server 文件失败: %v", err)
	}

	service, err := startMcpService(run, browserUseMCPPort, "sse")
	if err != nil {
		return nil, err
	}
	_, err = startMcpService(run, browserUseMCPPortForWebSearch, "http")
	if err != nil {
		return nil, err
	}
	close(started)
	return service, nil
}

func WaitForMCPService(run *iris.AgentRunContext) {
	if run == nil {
		return
	}
	select {
	case <-started:
		return
	case <-run.Done():
		run.GetLogger().Errorf("browser mcp service not started")
		return
	}
}

func prepareChangeUserForBrowserToTiger(cmd *exec.Cmd) (err error) {
	tigerUser, err := user.Lookup("tiger")
	if err != nil {
		return err
	}
	uid, _ := strconv.Atoi(tigerUser.Uid)
	gid, _ := strconv.Atoi(tigerUser.Gid)
	// 绝对路径，tiger没有root的Path
	cmd.Path = "/root/.local/bin/uv"
	cmd.SysProcAttr = &syscall.SysProcAttr{}
	cmd.SysProcAttr.Credential = &syscall.Credential{Uid: uint32(uid), Gid: uint32(gid)}
	cmd.Env = append(cmd.Env,
		"XDG_CONFIG_HOME=/tmp/.chromium",
		"XDG_CACHE_HOME=/tmp/.chromium",
	)
	return nil
}

func startMcpService(run *iris.AgentRunContext, port int, transport string) (*MCPService, error) {
	cmd := exec.Command("uv", "run", "server", "--transport", transport, "--port", strconv.Itoa(port))
	cmd.Env = os.Environ()
	if port == browserUseMCPPortForWebSearch {
		if err := prepareChangeUserForBrowserToTiger(cmd); err != nil {
			run.GetLogger().Errorf("failed to change user to tiger: %s", err)
		}
	}
	cmd.Dir = browserUseDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Env = append(cmd.Env, fmt.Sprintf("BROWSER_USE_SSE_PORT=%d", port))
	llmBaseURL := lo.Ternary(run.GetEnv(entity.RuntimeEnvironLLMBaseURL) == "", run.GetEnv(entity.RuntimeEnvironAPIBaseURL), run.GetEnv(entity.RuntimeEnvironLLMBaseURL))
	llmAPIBaseURL := fmt.Sprintf("%s/llmproxy/", llmBaseURL)
	browserHTTPProxy := run.GetEnv(entity.RuntimeEnvironBrowserHTTPProxy)
	browserHTTPSProxy := run.GetEnv(entity.RuntimeEnvironBrowserHTTPSProxy)
	browserNoProxy := run.GetEnv(entity.RuntimeEnvironBrowserNoProxy)
	headless := run.GetEnv(entity.RuntimeEnvironBrowserHeadless)
	sessionID := run.GetEnv(entity.RuntimeEnvironSessionID)
	jwt := run.GetEnv(entity.RuntimeEnvironUserCloudJWT)

	summarizeModel := run.GetConfig().GetModelByScene("browser_summarize")
	vlmModel := run.GetConfig().GetModelByScene("browser_vlm")

	service := &MCPService{}
	service.virtualGraphicProcesses = []*exec.Cmd{}

	cmd.Env = append(cmd.Env,
		"OPENAI_API_KEY=test",
		"OPENAI_API_BASE_URL="+llmAPIBaseURL,
		"BROWSER_USE_SUMMARIZE_MODEL="+summarizeModel.Model,
		"BROWSER_USE_VLM_MODEL="+vlmModel.Model,
		"HTTP_PROXY="+browserHTTPProxy,
		"HTTPS_PROXY="+browserHTTPSProxy,
		"NO_PROXY="+browserNoProxy,
		"HEADLESS="+headless,
		"PW_TEST_SCREENSHOT_NO_FONTS_READY=1",
		"SESSION_ID="+sessionID,
		"JWT_TOKEN="+jwt,
	)

	file, err := os.OpenFile(path.Join("/workspace/log", fmt.Sprintf("browse-use.log-%d", port)), os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}
	cmd.Stdout = file
	cmd.Stderr = file

	if err := cmd.Start(); err != nil {
		return nil, err
	}
	// wait 1 minute for the service to start
	err = backoff.Retry(func() error {
		err := checkPort(port)
		if err != nil {
			return errors.New("browser start timeout")
		}
		return nil
	}, backoff.WithMaxRetries(backoff.NewConstantBackOff(500*time.Millisecond), 120))
	if err != nil {
		return nil, err
	}
	service.run = run
	service.cmd = cmd

	run.RegisterBackgroundService(service)
	return service, nil
}

func NewBrowserMCPClient(run *iris.AgentRunContext) (*MCPClient, error) {
	WaitForMCPService(run)
	client, err := mcpclient.NewSSEMCPClient(fmt.Sprintf("http://127.0.0.1:%d/sse", browserUseMCPPort))
	if err != nil {
		return nil, err
	}
	err = client.Start(run)
	if err != nil {
		return nil, err
	}
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.Capabilities = mcp.ClientCapabilities{}
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "planact_agent",
		Version: "1.0.0",
	}
	_, err = client.Initialize(run, initRequest)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to initialize mcp client")
	}
	run.GetLogger().Infof("browser mcp client initialized")
	c := &MCPClient{Client: client}
	run.GetBackgroundServiceManager().AddDisposable(c)
	return c, nil
}

func (s *MCPService) Dispose() {
	s.cmd.Process.Kill()
	if s.virtualGraphicProcesses != nil {
		for _, p := range s.virtualGraphicProcesses {
			p.Process.Kill()
		}
	}
}

func (c *MCPClient) Dispose() {
	c.Client.Close()
}

func ContainedBrowserStep(traceSteps []*iris.AgentRunStep) (*iris.AgentRunStep, bool) {
	browserSteps := lo.Filter(traceSteps, func(step *iris.AgentRunStep, inx int) bool {
		return step != nil && step.Action != nil && strings.HasPrefix(step.Action.Name(), "browser_") &&
			step.Action.Name() != "browser_use" && step.Action.Name() != "browser_goto_and_extraction"
	})

	if len(browserSteps) != 0 {
		return browserSteps[len(browserSteps)-1], true
	} else {
		return nil, false
	}
}

func GetBrowserState(run *iris.AgentRunContext, outputs map[string]any) []*framework.ChatMessage {
	logger := run.GetLogger()

	if outputs["screenshot"] != nil {

		messages := []*framework.ChatMessage{}

		browserAssistantMessage := &framework.ChatMessage{
			Role: copilotentity.RoleAssistant,
			ContentParts: []*framework.LLMChatMessageContentPart{
				{
					Text: lo.ToPtr("The Screenshot and Dom Elements of Website will help you to advance"),
				},
			},
		}
		messages = append(messages, browserAssistantMessage)

		browserUserMessage := &framework.ChatMessage{
			Role: copilotentity.RoleUser,
		}

		screenshot, ok := outputs["screenshot"].(*ScreenshotArtifact)
		if !ok {
			logger.Errorf("no screenshot to attach")
			return nil
		}
		artifactLocalPath := screenshot.LocalPath
		if artifactLocalPath != "" {
			content, err := os.ReadFile(artifactLocalPath)
			if err != nil {
				logger.Errorf("failed to read screenshot: %s", err.Error())
				return nil
			}
			base64Content := fmt.Sprintf("data:image/%s;base64,%s", screenshot.MimeType, base64.StdEncoding.EncodeToString(content))
			browserUserMessage.ContentParts = append(browserUserMessage.ContentParts, &framework.LLMChatMessageContentPart{ImageURL: lo.ToPtr(base64Content)})
		}

		if outputs["browser_state"] != nil {
			currentTime := time.Now().Format("2006-01-02 15:04:05")

			stateMap := map[string]any{
				"browser_state": outputs["browser_state"],
				"current_time":  currentTime,
			}
			state, err := json.Marshal(stateMap)
			if err != nil {
				logger.Errorf("failed to get browser state")
				return nil
			}

			browserUserMessage.ContentParts = append(browserUserMessage.ContentParts, &framework.LLMChatMessageContentPart{Text: lo.ToPtr(string(state))})
		}
		messages = append(messages, browserUserMessage)
		return messages
	}

	return nil
}
