package genexp

import (
	"context"
	_ "embed"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"text/template"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/lib/conv"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/go-enry/go-enry/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

type ActorExecutionLogicalStep struct {
	Title        string
	Objective    string
	Rationale    string
	Actions      string
	Deliverables string
}

func GetLogicalStepsText(steps []*ActorExecutionLogicalStep) string {
	tmpl := template.Must(template.New("logical_steps_description").Parse(promptLogicalStepsDescription))

	var buf strings.Builder
	err := tmpl.Execute(&buf, map[string]interface{}{
		"Steps": steps,
	})
	if err != nil {
		// return fmt.Sprintf("Error rendering template: %v", err)
		return conv.JSONFormatString(steps)
	}

	return buf.String()
}

func ParseActorExecutionLogicalStep(content string) ([]*ActorExecutionLogicalStep, error) {
	tags, err := prompt.ParseTopTagsV2(content)
	if err != nil {
		return nil, err
	}

	var steps []*ActorExecutionLogicalStep

	for _, tag := range tags {
		if tag.XMLName.Local != "logical_step" {
			continue
		}

		step := &ActorExecutionLogicalStep{}

		// Extract title from attributes
		for _, attr := range tag.Attr {
			if attr.Name.Local == "title" {
				step.Title = attr.Value
				break
			}
		}

		// Parse inner tags for objective, rationale, actions, deliverables
		innerTags, err := prompt.ParseTopTagsV2(tag.Content)
		if err != nil {
			continue
		}

		for _, innerTag := range innerTags {
			switch innerTag.XMLName.Local {
			case "objective":
				step.Objective = strings.TrimSpace(innerTag.Content)
			case "rationale":
				step.Rationale = strings.TrimSpace(innerTag.Content)
			case "actions":
				step.Actions = strings.TrimSpace(innerTag.Content)
			case "deliverables":
				step.Deliverables = strings.TrimSpace(innerTag.Content)
			}
		}

		steps = append(steps, step)
	}

	return steps, nil
}

func TmplSummarizeActorExecution(ctx context.Context, opt GenerateTemplateExpSOPOption, item TaskTrajectoryNode, idx int) (string, error) {
	msgs := composeActorTrajectorySummarizingPrompt(item)
	if len(msgs) == 0 {
		return "", nil
	}

	tag := "gen_exp_actor_trajectory_summarize"
	llmOpt := framework.LLMCompletionOption{
		Model:       opt.Model.Model,
		Temperature: opt.Model.Temperature,
		MaxTokens:   opt.Model.MaxTokens,
		Tag:         tag,
	}

	var (
		err error
	)

	genTmplExpSaveIntermediateResult(opt, fmt.Sprintf("actor_trajectory_summarize_prompt_%d.xml", idx), conv.JSONFormatString(msgs))

	res, err := opt.LLM.ChatCompletion(ctx, msgs, llmOpt)

	if err != nil {
		return "", errors.WithMessage(err, "failed to call llm to summarize actor execution")
	}

	return res.Content, nil
}

func genTmplExpSaveIntermediateResult(opt GenerateTemplateExpSOPOption, name, content string) {
	if opt.SaveIntermediate {
		_ = os.WriteFile(filepath.Join(opt.ResultDir, name), []byte(content), 0644)
	}
}

func genTmplExpSOP(ctx context.Context, opt GenerateTemplateExpSOPOption, items []TaskTrajectoryNode, id int) (*entity.ExpSOP, error) {
	wsSum, _ := GetWorkspaceStructureSummary(".", DefaultOption())
	genTmplExpSaveIntermediateResult(opt, "workspace_summary.txt", wsSum)

	// Generate exp sop by LLM.
	msgs := composeSOPGenPrompt(items, opt.UserQueryTemplate, wsSum)
	genTmplExpSaveIntermediateResult(opt, fmt.Sprintf("exp_sop_prompt_%d.xml", id), prompt.GetMessagesPromptString(msgs))

	res, err := opt.LLM.ChatCompletion(context.Background(), msgs, framework.LLMCompletionOption{
		Model:       opt.Model.Model,
		Temperature: opt.Model.Temperature,
		MaxTokens:   opt.Model.MaxTokens,
		Tag:         "gen_exp_sop",
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call llm to summarize exp sop")
	}

	genTmplExpSaveIntermediateResult(opt, fmt.Sprintf("exp_sop_llm_result_%d.xml", id), res.Content)

	return ParseTemplateGenExpSOP(ctx, opt, res.Content)
}

type GenerateTemplateExpSOPOption struct {
	Trajectory       []TaskTrajectoryNode
	ResultDir        string
	Model            iris.SceneModelConfig
	SummarizeActor   bool
	SaveIntermediate bool

	UserQueryTemplate *entity.ExpUserQueryTemplate

	LLM framework.LLM
}

func GenerateTemplateExpSOP(ctx context.Context, opt GenerateTemplateExpSOPOption) (*entity.ExpSOP, error) {
	// Summarize each round detail execution trajectory.
	if opt.SummarizeActor {
		log.V1.Info("summarizing actor execution...")
		group, _ := errgroup.WithContext(ctx)
		group.SetLimit(4)
		for idx, item := range opt.Trajectory {
			log.V1.CtxInfo(ctx, "summarizing actor execution for round %d", idx)
			if item.Round == nil || item.Round.Plan.Actor != "mewtwo" {
				continue
			}
			group.Go(func() error {
				sum, err := TmplSummarizeActorExecution(ctx, opt, item, idx)
				if err != nil {
					return errors.WithMessagef(err, "failed to summarize actor execution for round %d", idx)
				}
				genTmplExpSaveIntermediateResult(opt, fmt.Sprintf("actor_result_%d.md", idx), sum)
				opt.Trajectory[idx].Round.Execution.Detail.Summarized = sum
				steps, err := ParseActorExecutionLogicalStep(sum)
				if err != nil {
					return errors.WithMessagef(err, "failed to parse actor execution logical step for round %d", idx)
				}
				opt.Trajectory[idx].Round.Execution.Detail.Summarized = GetLogicalStepsText(steps)
				return nil
			})
		}
		if err := group.Wait(); err != nil {
			log.V1.CtxWarn(ctx, "failed to summarize actor execution: %v", err)
		}
	}
	genTmplExpSaveIntermediateResult(opt, "trajectory.json", conv.JSONFormatString(opt.Trajectory))

	sop, err := genTmplExpSOP(ctx, opt, opt.Trajectory, 0)
	if err != nil {
		return nil, err
	}

	// Collect reference files from the env.
	collectReferenceFilesFromEnv(sop)

	genTmplExpSaveIntermediateResult(opt, "exp_sop.json", conv.JSONFormatString(sop))

	return sop, nil
}

func collectReferenceFilesFromEnv(sop *entity.ExpSOP) {
	// Collect reference files from the env.
	for _, step := range sop.PlanSteps {
		pwd, _ := os.Getwd()
		for _, phase := range step.Phases {
			for idx, ref := range phase.References {
				if ref.Filepath == "" {
					phase.References[idx].Error = "filepath is empty"
					continue
				}
				// FIXME(cyx): forbid path traversal.
				if filepath.IsAbs(ref.Filepath) {
					relPath, pathErr := filepath.Rel(pwd, ref.Filepath)
					if pathErr != nil {
						phase.References[idx].Error = "failed make path relative: " + pathErr.Error()
					} else {
						phase.References[idx].Filepath = relPath
					}
				}
				fileContent, err := os.ReadFile(ref.Filepath)
				if err != nil {
					phase.References[idx].Error = err.Error()
				} else {
					phase.References[idx].Content = string(fileContent)
					phase.References[idx].Binary = enry.IsBinary(fileContent)
				}
			}
		}
	}
}

func GenerateTemplateExpSOPWithScores(ctx context.Context, opt GenerateTemplateExpSOPOption, n int) (*entity.ExpSOP, error) {
	// Summarize each round detail execution trajectory.
	if opt.SummarizeActor {
		log.V1.Info("summarizing actor execution...")
		group, _ := errgroup.WithContext(ctx)
		group.SetLimit(4)
		for idx, item := range opt.Trajectory {
			log.V1.CtxInfo(ctx, "summarizing actor execution for round %d", idx)
			if item.Round == nil || item.Round.Plan.Actor != "mewtwo" {
				continue
			}
			group.Go(func() error {
				sum, err := TmplSummarizeActorExecution(ctx, opt, item, idx)
				if err != nil {
					return errors.WithMessagef(err, "failed to summarize actor execution for round %d", idx)
				}
				genTmplExpSaveIntermediateResult(opt, fmt.Sprintf("actor_result_%d.xml", idx), sum)
				opt.Trajectory[idx].Round.Execution.Detail.Summarized = sum
				steps, err := ParseActorExecutionLogicalStep(sum)
				if err != nil {
					return errors.WithMessagef(err, "failed to parse actor execution logical step for round %d", idx)
				}
				opt.Trajectory[idx].Round.Execution.Detail.Summarized = GetLogicalStepsText(steps)
				return nil
			})
		}
		if err := group.Wait(); err != nil {
			log.V1.CtxWarn(ctx, "failed to summarize actor execution: %v", err)
		}
	}
	g := errgroup.Group{}
	g.SetLimit(3)
	candidates := make([]*entity.ExpSOP, 0, n)
	scores := make([]*entity.ExpEvaluateResult, 0, n)
	for i := 0; i < n; i++ {
		g.Go(func() error {
			sop, err := genTmplExpSOP(ctx, opt, opt.Trajectory, i)
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to generate exp sop: %v", err)
				return err
			}
			candidates = append(candidates, sop)
			genTmplExpSaveIntermediateResult(opt, fmt.Sprintf("exp_sop_%d.json", i), conv.JSONFormatString(sop))
			score, err := EvaluateTemplateExpSOP(ctx, opt, sop, i)
			log.V1.CtxInfo(ctx, "evaluated exp sop %d, score: %+v", i, score)
			if err != nil {
				log.V1.CtxWarn(ctx, "failed to evaluate exp sop: %v", err)
				// 打分失败也给个零分，如果都失败了，有一个可以兜底
				scores = append(scores, &entity.ExpEvaluateResult{
					Score: 0,
				})
				return nil
			}
			sop.EvaluateResult = score
			genTmplExpSaveIntermediateResult(opt, fmt.Sprintf("exp_sop_%d_score.json", i), conv.JSONFormatString(score))
			scores = append(scores, score)
			return nil
		})
	}
	g.Wait()

	if len(scores) == 0 {
		return nil, errors.New("no candidates found")
	}

	bestScore, bestIdx := lo.MaxIndexBy(scores, func(a, b *entity.ExpEvaluateResult) bool {
		return a.Score > b.Score
	})

	log.V1.CtxInfo(ctx, "got %d candidates, best exp sop score: %s", len(scores), conv.JSONFormatString(bestScore))

	genTmplExpSaveIntermediateResult(opt, "exp_sop.json", conv.JSONFormatString(candidates[bestIdx]))

	collectReferenceFilesFromEnv(candidates[bestIdx])

	return candidates[bestIdx], nil
}

func ParseTemplateGenExpSOP(ctx context.Context, opt GenerateTemplateExpSOPOption, content string) (*entity.ExpSOP, error) {
	exp := &entity.ExpSOP{}
	tags, err := prompt.ParseTopTagsV2(content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse top tags")
	}
	if len(tags) == 0 {
		return nil, errors.New("no tags found")
	}
	sopTag := tags[0]
	for _, attr := range sopTag.Attr {
		if attr.Name.Local == "name" {
			exp.Name = attr.Value
		}
	}

	tags, err = prompt.ParseTopTagsV2(sopTag.Content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse inside tags")
	}

	for _, tag := range tags {
		switch tag.XMLName.Local {
		case "user_query_template":
			exp.UserQuery = strings.TrimSpace(tag.Content)
			tags, err := prompt.ParseTopTagsV2(tag.Content)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to parse user query template top tags")
			}
			for _, tag := range tags {
				switch tag.XMLName.Local {
				case "template":
					exp.UserQuery = strings.TrimSpace(tag.Content)
				case "placeholder":
					ph := entity.Placeholder{}
					for _, attr := range tag.Attr {
						switch attr.Name.Local {
						case "name":
							ph.Name = attr.Value
						case "default":
							ph.Default = attr.Value
						case "description":
							ph.Description = attr.Value
						case "ref_attachments":
							ph.RefAattachments = strings.ToLower(attr.Value) == "true"
						}
					}
					if len(ph.Default) == 0 {
						ph.Default = ph.Name
					}
					exp.UserQueryPlaceholders = append(exp.UserQueryPlaceholders, ph)
				}
			}
		case "used_when":
			exp.UsedWhen = strings.TrimSpace(tag.Content)
		case "progress_plan":
			exp.ProgressPlan = strings.TrimSpace(tag.Content)
		case "plan_steps":
			exp.PlanSteps, err = parseTemplateExpSOPGenPlanSteps(tag.Content)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to parse plan steps")
			}
		}
	}

	return exp, nil
}

func filterEmpty(s string, _ int) bool {
	return len(strings.TrimSpace(s)) > 0
}

func parseTemplateExpSOPGenPlanSteps(content string) ([]*entity.ExpSOPPlanStep, error) {
	tags, err := prompt.ParseTopTagsV2(content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse plan steps top tags")
	}
	var steps []*entity.ExpSOPPlanStep
	for _, tag := range tags {
		if tag.XMLName.Local != "step" {
			continue
		}
		step := entity.ExpSOPPlanStep{
			Name:       lo.FirstOrEmpty(tag.Attr).Value,
			AssignedTo: "",
			Persona:    "",
			Toolsets:   []string{},
			Phases:     []*entity.ExpSOPPlanStepPhase{},
			Parameters: map[string]string{},
		}
		tags, err := prompt.ParseTopTagsV2(tag.Content)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to parse plan step top tags")
		}
		for _, tag := range tags {
			switch tag.XMLName.Local {
			case "objective":
				step.Objective = strings.TrimSpace(tag.Content)
			case "assigned_to":
				step.AssignedTo = strings.TrimSpace(tag.Content)
			case "toolsets":
				step.Toolsets = lo.Filter(
					lo.Map(
						strings.Split(strings.TrimSpace(tag.Content), ","),
						func(s string, _ int) string {
							return strings.TrimSpace(s)
						},
					),
					filterEmpty,
				)
			case "persona":
				step.Persona = strings.TrimSpace(tag.Content)
			case "parameters":
				paramTags, err := prompt.ParseTopTagsV2(tag.Content)
				if err != nil {
					log.V1.Warn("failed to parse parameters top tags: %v", err)
					continue
				}
				for _, tag := range paramTags {
					step.Parameters[strings.TrimSpace(tag.XMLName.Local)] = strings.TrimSpace(tag.Content)
				}
			case "phases":
				phaseTags, err := prompt.ParseTopTagsV2(tag.Content)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to parse phases top tags")
				}
				for _, tag := range phaseTags {
					if tag.XMLName.Local != "phase" {
						continue
					}
					phase := entity.ExpSOPPlanStepPhase{
						Name:        lo.FirstOrEmpty(tag.Attr).Value,
						Objective:   "",
						Actions:     "",
						References:  []entity.ReferenceFile{},
						Deliverable: []string{},
					}
					phaseTags, err := prompt.ParseTopTagsV2(tag.Content)
					if err != nil {
						return nil, errors.WithMessage(err, "failed to parse phase top tags")
					}
					for _, tag := range phaseTags {
						switch tag.XMLName.Local {
						case "objective":
							phase.Objective = strings.TrimSpace(tag.Content)
						case "actions":
							phase.Actions = strings.TrimSpace(tag.Content)
						case "reusable_materials":
							phase.References = lo.Map(
								lo.Filter(
									strings.Split(strings.TrimSpace(tag.Content), "\n"),
									filterEmpty,
								),
								func(s string, _ int) entity.ReferenceFile {
									filePath := strings.Trim(s, "\t\n\r `'\"-*[]")
									return entity.ReferenceFile{
										Filepath: filePath,
										Content:  "",
									}
								},
							)
						case "deliverables":
							phase.Deliverable = lo.Filter(
								strings.Split(strings.TrimSpace(tag.Content), "\n"),
								filterEmpty,
							)
						}
					}
					step.Phases = append(step.Phases, &phase)
				}
			}
		}
		steps = append(steps, &step)
	}
	return steps, nil
}

func EvaluateTemplateExpSOP(ctx context.Context, genOpt GenerateTemplateExpSOPOption, sop *entity.ExpSOP, id int) (*entity.ExpEvaluateResult, error) {
	msgs := composeSOPEvalPrompt(sop, genOpt.Trajectory)
	if msgs == nil {
		return nil, errors.New("failed to compose sop eval prompt")
	}

	genTmplExpSaveIntermediateResult(genOpt, fmt.Sprintf("sop_eval_prompt_%d.xml", id), prompt.GetMessagesPromptString(msgs))
	res, err := genOpt.LLM.ChatCompletion(context.Background(), msgs, framework.LLMCompletionOption{
		Model:       genOpt.Model.Model,
		Temperature: genOpt.Model.Temperature,
		MaxTokens:   genOpt.Model.MaxTokens,
		Tag:         "eval_exp_sop",
		Thinking:    genOpt.Model.Thinking,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call llm to summarize exp sop")
	}

	genTmplExpSaveIntermediateResult(
		genOpt,
		fmt.Sprintf("exp_sop_eval_llm_result_%d.xml", id),
		"<reasoning>\n"+res.ReasoningContent+"\n</reasoning>\n"+res.Content,
	)

	return parseTemplateEvalExpSOPResult(res.Content)
}

func parseTemplateEvalExpSOPResult(content string) (*entity.ExpEvaluateResult, error) {
	evalRes := &entity.ExpEvaluateResult{}

	tags, err := prompt.ParseTopTagsV2(content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse eval sop result top tags")
	}

	if len(tags) == 0 {
		return nil, errors.New("no aspect found in eval sop result")
	}

	tags, err = prompt.ParseTopTagsV2(tags[0].Content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse eval sop result internal tags")
	}

	for _, tag := range tags {
		if tag.XMLName.Local != "aspect" {
			continue
		}
		aspect := entity.ExpEvaluateAspect{
			Aspect: lo.FirstOrEmpty(tag.Attr).Value,
		}
		ts, err := prompt.ParseTopTagsV2(tag.Content)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to parse eval sop result aspect tags")
		}
		for _, t := range ts {
			switch t.XMLName.Local {
			case "score":
				aspect.Score, err = strconv.Atoi(t.Content)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to parse eval sop result aspect score")
				}
			case "justification":
				aspect.Justification = t.Content
			}
		}
		evalRes.Aspects = append(evalRes.Aspects, aspect)
		evalRes.Score += aspect.Score
	}

	return evalRes, nil
}

// IsTemplateReferenceFileReusable indicates if the reference file is reusable, currently we only allow code file being reused,
// because agent often misuses the documents reference file for new tasks.
func IsTemplateReferenceFileReusable(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return lo.Contains(
		[]string{
			".py",
			".js",
			".css",
			".ts",
			".tsx",
			".jsx",
			".html",
			".sh",
		},
		ext,
	)
}
