package memory

import (
	"bytes"
	"embed"
	"text/template"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/background/browser"
	action_prompts "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/prompts"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/memory/condenser"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	copilotentity "code.byted.org/devgpt/kiwis/copilotstack/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/search/dal/llm"
)

var (
	//go:embed prompts
	prompts   embed.FS
	promptset = prompt.MustNewPromptSet(prompts, "prompts")
)

type MemoryComposeOptions struct {
	ThoughtPrompt         *template.Template
	ObservationPrompt     *template.Template
	Variant               string
	TaskContext           string
	ObservationFormatFunc func(run *iris.AgentRunContext, step *iris.AgentRunStep) framework.ChatMessage
	Condenser             condenser.Summarizer
	StoreKey              string
}

// WithPreviousContext 获取之前的记忆上下文（跨actor调用，每个actor run只需调用一次）
func (m *AgentMemory) WithPreviousContext(
	run *iris.AgentRunContext,
	options *MemoryComposeOptions,
) ([]*framework.ChatMessage, error) {
	if len(m.ActionMemory) > 0 {
		return m.searchRelatedMessages(run, options), nil
	}
	return make([]*framework.ChatMessage, 0), nil
}

// WithCurrentContext 处理当前上下文（每次compose时调用）
func (m *AgentMemory) WithCurrentContext(
	run *iris.AgentRunContext,
	options *MemoryComposeOptions,
) func() ([]*framework.ChatMessage, error) {
	return func() ([]*framework.ChatMessage, error) {
		previousMemorySteps := lo.Filter(m.ActionMemory, func(step *ActionMemoryItem, _ int) bool {
			return step.ExecutorAgent == run.State.CurrentStep.ExecutorAgent
		})

		messages := make([]*framework.ChatMessage, 0)

		memoryMessages := iris.RetrieveStoreByKey[[]*framework.ChatMessage](run, options.StoreKey)
		if len(memoryMessages) > 0 {
			messages = append(messages, memoryMessages...)
		}

		// 生成窗口内的保留动作
		preservedMessages := m.generateObservationsWithBrowserState(run, previousMemorySteps, options)
		messages = append(messages, preservedMessages...)

		// token超限的兜底方案
		if options.Condenser != nil {
			condensedMessage, err := options.Condenser.Summarize(run, messages, false)
			if err != nil {
				run.GetLogger().Errorf("failed to summarize messages: %v", err)
				return messages, nil
			}
			messages = condensedMessage
		}

		return messages, nil
	}
}

func (m *AgentMemory) searchRelatedMessages(run *iris.AgentRunContext, options *MemoryComposeOptions) []*framework.ChatMessage {
	actionsToSearch := lo.Slice(m.ActionMemory, 0, len(m.ActionMemory))

	// 搜索代理记忆
	searchedAgentMemory, err := m.searchRelatedMemory(run, actionsToSearch, options)
	if err != nil {
		run.GetLogger().Errorf("failed to search work memory: %v", err)
		return make([]*framework.ChatMessage, 0)
	}
	assistantThought, err := promptset.ExecutePrompt("assistant_md", options.Variant, map[string]any{
		"query": options.TaskContext,
	})
	if err != nil {
		run.GetLogger().Errorf("failed to execute prompt: %v", err)
		return make([]*framework.ChatMessage, 0)
	}

	summaryAgentMemory, err := promptset.ExecutePrompt("memory_apply", options.Variant, map[string]any{
		"ActionMemory": searchedAgentMemory.ActionMemory,
		"StatusMemory": searchedAgentMemory.StatusMemory,
	})
	if err != nil {
		run.GetLogger().Errorf("failed to execute prompt: %v", err)
		return make([]*framework.ChatMessage, 0)
	}
	summarizedMessages := []*framework.ChatMessage{
		{
			Role:    copilotentity.RoleAssistant,
			Content: assistantThought,
		},
		{
			Role:    copilotentity.RoleUser,
			Content: summaryAgentMemory,
		},
	}
	return summarizedMessages
}

func (m *AgentMemory) searchRelatedMemory(run *iris.AgentRunContext, actions []*ActionMemoryItem, options *MemoryComposeOptions) (*SearchedAgentMemory, error) {
	llm_api := run.GetLLM()
	modelConfig := run.GetConfig().GetModelByScene("memory_search")

	memorySearchSystem, err := promptset.ExecutePrompt("system_memory_search", options.Variant, map[string]any{})
	if err != nil {
		run.GetLogger().Errorf("failed to execute prompt: %v", err)
		return &SearchedAgentMemory{
			ActionMemory: make([]*SearchedActionMemory, 0),
			StatusMemory: make(map[string]*StatusMemoryItem),
		}, err
	}

	searchedActionMemoryList := make([]*SearchedActionMemory, 0)
	for _, action := range actions {
		if action == nil || action.Thought == nil || action.Status == iris.AgentRunStepStatusRunning {
			continue
		}

		if action.Thought.Tool == "progress_think" || action.Thought.Tool == "conclude" {
			continue
		}

		tempAction := &SearchedActionMemory{
			MemoryID:      action.StepID,
			MemoryContent: generateMemoryContent(action.Thought.Tool, action.Inputs),
			Rationale:     action.Thought.Content,
		}
		searchedActionMemoryList = append(searchedActionMemoryList, tempAction)
	}
	if len(searchedActionMemoryList) == 0 {
		return &SearchedAgentMemory{
			ActionMemory: make([]*SearchedActionMemory, 0),
			StatusMemory: make(map[string]*StatusMemoryItem),
		}, errors.New("no memory found for memory search")
	}

	memorySearchUser, err := promptset.ExecutePrompt("user_memory_search", options.Variant, map[string]any{
		"TaskContent":  options.TaskContext,
		"ActionMemory": searchedActionMemoryList,
	})

	messages := []*framework.ChatMessage{
		{
			Role:    llm.RoleSystem,
			Content: memorySearchSystem,
		},
		{
			Role:    llm.RoleUser,
			Content: memorySearchUser,
		},
	}
	content, err := llm_api.ChatCompletion(run, messages, framework.LLMCompletionOption{
		Model:       modelConfig.Model,
		MaxTokens:   modelConfig.MaxTokens,
		Temperature: modelConfig.Temperature,
		Tag:         "memory_search",
	})
	if err != nil {
		return &SearchedAgentMemory{
			ActionMemory: make([]*SearchedActionMemory, 0),
			StatusMemory: make(map[string]*StatusMemoryItem),
		}, err
	}
	topTags, err := prompt.ParseTopTagsV2(content.Content)
	if err != nil {
		return &SearchedAgentMemory{
			ActionMemory: make([]*SearchedActionMemory, 0),
			StatusMemory: make(map[string]*StatusMemoryItem),
		}, err
	}
	relatedActionMemories := make([]*SearchedActionMemory, 0)
	relatedStatusMemories := make(map[string]*StatusMemoryItem)
	for _, tag := range topTags {
		if len(tag.Attr) == 0 {
			continue
		}
		// 提取 memory_id
		memoryID := tag.Attr[0].Value

		// 提取 Rationale,Result
		rationale := extractXMLTagContent(tag.Content, "Rationale")
		result := extractXMLTagContent(tag.Content, "Result")

		step := m.GetStep(memoryID)

		// Check if step is nil to avoid nil pointer dereference
		if step == nil {
			run.GetLogger().Warnf("Step not found for memory ID: %s", memoryID)
			continue
		}

		// Check if Action is nil to avoid nil pointer dereference
		if step.Action == nil {
			run.GetLogger().Warnf("Action is nil for memory ID: %s", memoryID)
			continue
		}

		searchedActionMemory := &SearchedActionMemory{
			MemoryID:          memoryID,
			ActionName:        step.Action.Name(),
			Rationale:         rationale,
			Result:            result,
			CompressedInputs:  compressStepInputs(step, step.Inputs),
			CompressedOutputs: compressStepOutputs(step, step.Outputs),
		}

		// 根据 memory_id 查询 StatusMemory
		for statusPath, statusMemory := range m.StatusMemory {
			if statusMemory.ReferenceActionMemoryID == memoryID {
				relatedStatusMemories[statusPath] = statusMemory
			}
		}
		relatedActionMemories = append(relatedActionMemories, searchedActionMemory)
	}
	if len(relatedActionMemories) == 0 {
		return &SearchedAgentMemory{
			ActionMemory: make([]*SearchedActionMemory, 0),
			StatusMemory: make(map[string]*StatusMemoryItem),
		}, errors.New("no related memory searched")
	}

	workMemory := SearchedAgentMemory{
		ActionMemory: relatedActionMemories,
		StatusMemory: relatedStatusMemories,
	}
	return &workMemory, nil
}

// generateObservationsWithBrowserState 生成观察消息并处理浏览器状态
func (m *AgentMemory) generateObservationsWithBrowserState(
	run *iris.AgentRunContext,
	allActions []*ActionMemoryItem,
	options *MemoryComposeOptions,
) []*framework.ChatMessage {
	// 初始化消息数组
	messages := make([]*framework.ChatMessage, 0)

	// 从动作生成观察消息
	observations := lo.FlatMap(allActions, func(step *ActionMemoryItem, idx int) []*framework.ChatMessage {
		var buf bytes.Buffer
		err := options.ThoughtPrompt.Execute(&buf, step.Thought)
		if err != nil {
			run.GetLogger().Errorf("failed to execute thought prompt: %v", err)
			return nil
		}
		assistant := framework.ChatMessage{
			Role:    framework.RoleAssistant,
			Content: buf.String(),
		}
		observation := framework.ChatMessage{
			Role: framework.RoleUser,
		}
		if step.Thought.FunctionCall { // 如果使用了模型原生的 function call, 则使用原生方式回传结果
			assistant.ToolCalls = []framework.LLMToolFunction{
				{
					ID:         step.Thought.ToolCallID, // Gemini 返回的 tool call 没有 ID，只有 Name 来区分不同 tool call
					Name:       step.Action.Name(),
					Parameters: step.Inputs,
				},
			}
			observation.Role = framework.RoleTool
			observation.ToolCallID = step.Thought.ToolCallID
		}
		if step.Thought.ReasoningContent != "" {
			assistant.ReasoningContent = step.Thought.ReasoningContent
		}
		var message string
		if options.ObservationFormatFunc != nil {
			observation = options.ObservationFormatFunc(run, (*iris.AgentRunStep)(step))
			message = observation.Content
		} else if step.Action != nil {
			message = action_prompts.ToolObservation(run, step.StepID, step.Action.Name(), step.Inputs, step.Outputs, step.Error)
		} else if step.Error != nil {
			message = step.Error.Error()
		}
		if len(message) == 0 {
			message = "No action is executed."
		}
		observation.Content = message

		return []*framework.ChatMessage{
			&assistant,
			&observation,
		}
	})
	messages = append(messages, observations...)

	// record last action tool call result throughput
	if len(allActions) > 0 && allActions[len(allActions)-1].Action != nil && len(observations) > 0 {
		lastAction := allActions[len(allActions)-1].Action
		lastObservation := observations[len(observations)-1]
		_ = metrics.AR.ToolCallResultThroughput.WithTags(&metrics.ToolCallTag{
			Tool: lastAction.Name(),
		}).Add(float64(len(lastObservation.Content)))
	}

	// 处理浏览器状态
	traceSteps := lo.FilterMap(allActions, func(item *ActionMemoryItem, index int) (*iris.AgentRunStep, bool) {
		step := (*iris.AgentRunStep)(item)
		return step, true
	})

	if lastBrowserStep, ok := browser.ContainedBrowserStep(traceSteps); ok {
		// 只取最新的浏览器状态
		if lastBrowserState := browser.GetBrowserState(run, lastBrowserStep.Outputs); lastBrowserState != nil {
			messages = append(messages, lastBrowserState...)
		}
	}

	return messages
}
