package project_memory

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
)

func TestLoadHierarchicalMemory(t *testing.T) {
	tests := []struct {
		name                   string
		setupFiles             map[string]string // filePath -> content
		fileName               string
		maxDirs                int
		enableImportProcessing bool
		wantMemoryResult       *ProjectMemory
		wantError              bool
	}{
		{
			name: "success with multiple files",
			setupFiles: map[string]string{
				"aime.md":       "# Root Memory\nThis is root content.",
				"dir1/aime.md":  "# Dir1 Memory\nThis is dir1 content.",
				"dir2/aime.md":  "# Dir2 Memory\nThis is dir2 content.",
				"dir1/other.md": "# Other file\nShould be ignored.",
			},
			fileName: "aime.md",
			maxDirs:  200,
			wantMemoryResult: &ProjectMemory{
				FilePaths: []string{"aime.md", "dir1/aime.md", "dir2/aime.md"},
				FileContents: []FileContent{
					{Content: "# Root Memory\nThis is root content."},
					{Content: "# Dir1 Memory\nThis is dir1 content."},
					{Content: "# Dir2 Memory\nThis is dir2 content."},
				},
			},
			wantError: false,
		},
		{
			name:       "no files found",
			setupFiles: map[string]string{},
			fileName:   "aime.md",
			maxDirs:    200,
			wantMemoryResult: &ProjectMemory{
				FilePaths:    []string{},
				FileContents: []FileContent{},
			},
			wantError: false,
		},
		{
			name: "different file name",
			setupFiles: map[string]string{
				"CLAUDE.md":      "# Claude Memory\nClaude content.",
				"dir1/CLAUDE.md": "# Dir1 Claude\nDir1 content.",
				"aime.md":        "# Should be ignored",
			},
			fileName: "CLAUDE.md",
			maxDirs:  200,
			wantMemoryResult: &ProjectMemory{
				FilePaths: []string{"CLAUDE.md", "dir1/CLAUDE.md"},
				FileContents: []FileContent{
					{Content: "# Claude Memory\nClaude content."},
					{Content: "# Dir1 Claude\nDir1 content."},
				},
			},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup temporary directory
			tempDir, err := os.MkdirTemp("", "memory_test_"+tt.name)
			require.NoError(t, err)
			defer os.RemoveAll(tempDir)

			// Create test files
			for filePath, content := range tt.setupFiles {
				fullPath := filepath.Join(tempDir, filePath)
				dir := filepath.Dir(fullPath)
				if dir != tempDir {
					require.NoError(t, os.MkdirAll(dir, 0755))
				}
				require.NoError(t, os.WriteFile(fullPath, []byte(content), 0644))
			}

			// Create config
			config := MemoryConfig{
				FileName:               tt.fileName,
				MaxDirs:                tt.maxDirs,
				Debug:                  false, // Keep logs quiet during tests
				RepoPath:               tempDir,
				EnableImportProcessing: tt.enableImportProcessing,
				Logger:                 iris.DefaultLogger,
			}

			// Test the function
			discovery := NewMemoryDiscovery(config)
			result, err := discovery.LoadHierarchicalMemory()

			// Validate error expectation
			if tt.wantError {
				assert.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.NotNil(t, result)

			// Compare with expected result
			assert.Equal(t, len(tt.wantMemoryResult.FilePaths), len(result.FilePaths))
			assert.Equal(t, len(tt.wantMemoryResult.FileContents), len(result.FileContents))

			// Check file paths match (remove tempDir prefix for comparison)
			actualRelativePaths := make([]string, len(result.FilePaths))
			for i, path := range result.FilePaths {
				actualRelativePaths[i] = strings.TrimPrefix(strings.TrimPrefix(path, tempDir), "/")
			}
			assert.Equal(t, tt.wantMemoryResult.FilePaths, actualRelativePaths)

			// Check file contents match
			for i, expectedFC := range tt.wantMemoryResult.FileContents {
				if i < len(result.FileContents) {
					assert.Equal(t, expectedFC.Content, result.FileContents[i].Content)
					assert.NoError(t, result.FileContents[i].Error)
				}
			}
		})
	}
}

func TestLoadAndMergeMultipleMemories(t *testing.T) {
	tests := []struct {
		name           string
		setupFiles     map[string]string
		fileNames      []string
		expectedResult *ProjectMemory
		expectError    bool
	}{
		{
			name: "multiple files import same file - deduplication",
			setupFiles: map[string]string{
				"aime.md":    "# AIME Memory\<EMAIL>\nAIME content.",
				"claude.md":  "# Claude Memory\<EMAIL>\nClaude content.",
				"shared.md":  "# Shared Content\nThis is shared across files.",
				"unique1.md": "# Unique 1\nOnly in AIME.",
				"unique2.md": "# Unique 2\nOnly in Claude.",
			},
			fileNames: []string{"aime.md", "claude.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"aime.md", "claude.md", "shared.md"},
				FileContents: []FileContent{
					{FilePath: "aime.md", Content: "# AIME Memory\<EMAIL>\nAIME content.", Error: nil},
					{FilePath: "claude.md", Content: "# Claude Memory\<EMAIL>\nClaude content.", Error: nil},
					{FilePath: "shared.md", Content: "# Shared Content\nThis is shared across files.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "nested imports with deduplication",
			setupFiles: map[string]string{
				"main1.md":   "# Main 1\<EMAIL>\<EMAIL>\nMain 1 content.",
				"main2.md":   "# Main 2\<EMAIL>\<EMAIL>\nMain 2 content.",
				"common.md":  "# Common\<EMAIL>\nCommon content.",
				"nested1.md": "# Nested 1\<EMAIL>\nNested 1 content.",
				"nested2.md": "# Nested 2\<EMAIL>\nNested 2 content.",
				"base.md":    "# Base\nBase content.",
			},
			fileNames: []string{"main1.md", "main2.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"base.md", "common.md", "main1.md", "main2.md", "nested1.md", "nested2.md"},
				FileContents: []FileContent{
					{FilePath: "base.md", Content: "# Base\nBase content.", Error: nil},
					{FilePath: "common.md", Content: "# Common\<EMAIL>\nCommon content.", Error: nil},
					{FilePath: "main1.md", Content: "# Main 1\<EMAIL>\<EMAIL>\nMain 1 content.", Error: nil},
					{FilePath: "main2.md", Content: "# Main 2\<EMAIL>\<EMAIL>\nMain 2 content.", Error: nil},
					{FilePath: "nested1.md", Content: "# Nested 1\<EMAIL>\nNested 1 content.", Error: nil},
					{FilePath: "nested2.md", Content: "# Nested 2\<EMAIL>\nNested 2 content.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "no duplicate files when no imports",
			setupFiles: map[string]string{
				"file1.md": "# File 1\nContent 1.",
				"file2.md": "# File 2\nContent 2.",
				"file3.md": "# File 3\nContent 3.",
			},
			fileNames: []string{"file1.md", "file2.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"file1.md", "file2.md"},
				FileContents: []FileContent{
					{FilePath: "file1.md", Content: "# File 1\nContent 1.", Error: nil},
					{FilePath: "file2.md", Content: "# File 2\nContent 2.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "empty file names list",
			setupFiles: map[string]string{
				"file1.md": "# File 1\nContent 1.",
			},
			fileNames: []string{},
			expectedResult: &ProjectMemory{
				FilePaths:    []string{},
				FileContents: []FileContent{},
			},
			expectError: false,
		},
		{
			name: "single file with imports",
			setupFiles: map[string]string{
				"main.md":    "# Main\<EMAIL>\<EMAIL>\nMain content.",
				"import1.md": "# Import 1\nImport 1 content.",
				"import2.md": "# Import 2\nImport 2 content.",
			},
			fileNames: []string{"main.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"import1.md", "import2.md", "main.md"},
				FileContents: []FileContent{
					{FilePath: "import1.md", Content: "# Import 1\nImport 1 content.", Error: nil},
					{FilePath: "import2.md", Content: "# Import 2\nImport 2 content.", Error: nil},
					{FilePath: "main.md", Content: "# Main\<EMAIL>\<EMAIL>\nMain content.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "same file imported multiple times in single file",
			setupFiles: map[string]string{
				"main.md":   "# Main\<EMAIL>\<EMAIL>\nMain content.",
				"shared.md": "# Shared\nShared content.",
			},
			fileNames: []string{"main.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"main.md", "shared.md"},
				FileContents: []FileContent{
					{FilePath: "main.md", Content: "# Main\<EMAIL>\<EMAIL>\nMain content.", Error: nil},
					{FilePath: "shared.md", Content: "# Shared\nShared content.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "relative path imports with deduplication",
			setupFiles: map[string]string{
				"main1.md":       "# Main 1\n@./docs/shared.md\<EMAIL>\nMain 1 content.",
				"main2.md":       "# Main 2\n@./docs/shared.md\<EMAIL>\nMain 2 content.",
				"docs/shared.md": "# Shared in docs\nShared content in docs.",
				"common.md":      "# Common root\nCommon content in root.",
			},
			fileNames: []string{"main1.md", "main2.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"common.md", "docs/shared.md", "main1.md", "main2.md"},
				FileContents: []FileContent{
					{FilePath: "common.md", Content: "# Common root\nCommon content in root.", Error: nil},
					{FilePath: "docs/shared.md", Content: "# Shared in docs\nShared content in docs.", Error: nil},
					{FilePath: "main1.md", Content: "# Main 1\n@./docs/shared.md\<EMAIL>\nMain 1 content.", Error: nil},
					{FilePath: "main2.md", Content: "# Main 2\n@./docs/shared.md\<EMAIL>\nMain 2 content.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "deeply nested relative imports with deduplication",
			setupFiles: map[string]string{
				"file1.md":                "# File 1\n@./level1/level2/shared.md\<EMAIL>\nFile 1 content.",
				"file2.md":                "# File 2\n@./level1/level2/shared.md\n@./level1/sibling.md\nFile 2 content.",
				"level1/level2/shared.md": "# Shared deep\n@../../base.md\nShared deep content.",
				"level1/sibling.md":       "# Sibling\n@../base.md\nSibling content.",
				"base.md":                 "# Base\nBase content.",
			},
			fileNames: []string{"file1.md", "file2.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"base.md", "file1.md", "file2.md", "level1/level2/shared.md", "level1/sibling.md"},
				FileContents: []FileContent{
					{FilePath: "base.md", Content: "# Base\nBase content.", Error: nil},
					{FilePath: "file1.md", Content: "# File 1\n@./level1/level2/shared.md\<EMAIL>\nFile 1 content.", Error: nil},
					{FilePath: "file2.md", Content: "# File 2\n@./level1/level2/shared.md\n@./level1/sibling.md\nFile 2 content.", Error: nil},
					{FilePath: "level1/level2/shared.md", Content: "# Shared deep\n@../../base.md\nShared deep content.", Error: nil},
					{FilePath: "level1/sibling.md", Content: "# Sibling\n@../base.md\nSibling content.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "mixed absolute and relative imports with deduplication",
			setupFiles: map[string]string{
				"auth.md":           "# Auth\n@./modules/config.md\<EMAIL>\nAuth content.",
				"routes.md":         "# Routes\n@./api/handlers.md\<EMAIL>\nRoutes content.",
				"modules/config.md": "# Config\n@../shared.md\nConfig content.",
				"api/handlers.md":   "# Handlers\n@../shared.md\nHandlers content.",
				"shared.md":         "# Shared\nShared content.",
			},
			fileNames: []string{"auth.md", "routes.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"api/handlers.md", "auth.md", "modules/config.md", "routes.md", "shared.md"},
				FileContents: []FileContent{
					{FilePath: "api/handlers.md", Content: "# Handlers\n@../shared.md\nHandlers content.", Error: nil},
					{FilePath: "auth.md", Content: "# Auth\n@./modules/config.md\<EMAIL>\nAuth content.", Error: nil},
					{FilePath: "modules/config.md", Content: "# Config\n@../shared.md\nConfig content.", Error: nil},
					{FilePath: "routes.md", Content: "# Routes\n@./api/handlers.md\<EMAIL>\nRoutes content.", Error: nil},
					{FilePath: "shared.md", Content: "# Shared\nShared content.", Error: nil},
				},
			},
			expectError: false,
		},
		{
			name: "relative imports with multiple levels and same target",
			setupFiles: map[string]string{
				"button.md":                     "# Button\<EMAIL>\n@./frontend/components/shared.md\nButton component.",
				"home.md":                       "# Home\<EMAIL>\n@./frontend/components/shared.md\nHome page.",
				"api.md":                        "# API\<EMAIL>\nAPI module.",
				"frontend/components/shared.md": "# Shared Component\n@../../utils.md\nShared component.",
				"utils.md":                      "# Utils\nUtility functions.",
			},
			fileNames: []string{"button.md", "home.md", "api.md"},
			expectedResult: &ProjectMemory{
				FilePaths: []string{"api.md", "button.md", "frontend/components/shared.md", "home.md", "utils.md"},
				FileContents: []FileContent{
					{FilePath: "api.md", Content: "# API\<EMAIL>\nAPI module.", Error: nil},
					{FilePath: "button.md", Content: "# Button\<EMAIL>\n@./frontend/components/shared.md\nButton component.", Error: nil},
					{FilePath: "frontend/components/shared.md", Content: "# Shared Component\n@../../utils.md\nShared component.", Error: nil},
					{FilePath: "home.md", Content: "# Home\<EMAIL>\n@./frontend/components/shared.md\nHome page.", Error: nil},
					{FilePath: "utils.md", Content: "# Utils\nUtility functions.", Error: nil},
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tempDir, err := os.MkdirTemp("", "merge_test_"+tt.name)
			require.NoError(t, err)
			defer os.RemoveAll(tempDir)

			for filePath, content := range tt.setupFiles {
				fullPath := filepath.Join(tempDir, filePath)
				dir := filepath.Dir(fullPath)
				if dir != tempDir {
					require.NoError(t, os.MkdirAll(dir, 0755))
				}
				require.NoError(t, os.WriteFile(fullPath, []byte(content), 0644))
			}

			result, err := LoadAndMergeMultipleMemories(nil, tt.fileNames, "", tempDir, iris.DefaultLogger)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)

			// Check that file paths match expected (sorted)
			assert.Equal(t, len(tt.expectedResult.FilePaths), len(result.FilePaths))

			// Convert expected relative paths to absolute paths for comparison
			expectedAbsPaths := make([]string, len(tt.expectedResult.FilePaths))
			for i, relPath := range tt.expectedResult.FilePaths {
				expectedAbsPaths[i] = filepath.Join(tempDir, relPath)
			}
			assert.Equal(t, expectedAbsPaths, result.FilePaths)

			// Check file contents
			assert.Equal(t, len(tt.expectedResult.FileContents), len(result.FileContents))

			// Create a map for easier comparison since order might vary
			resultMap := make(map[string]FileContent)
			for _, fc := range result.FileContents {
				relPath, err := filepath.Rel(tempDir, fc.FilePath)
				require.NoError(t, err)
				resultMap[relPath] = fc
			}

			for _, expected := range tt.expectedResult.FileContents {
				actual, exists := resultMap[expected.FilePath]
				assert.True(t, exists, "Expected file not found: %s", expected.FilePath)
				if exists {
					assert.Equal(t, expected.Content, actual.Content)
					assert.NoError(t, actual.Error)
				}
			}

			// Most important check: verify deduplication
			// Count unique file paths
			uniquePaths := make(map[string]bool)
			for _, fc := range result.FileContents {
				if uniquePaths[fc.FilePath] {
					t.Errorf("Duplicate file path found: %s", fc.FilePath)
				}
				uniquePaths[fc.FilePath] = true
			}
		})
	}
}

// TestMemoryResult_String tests the String() method of MemoryResult
func TestMemoryResult_String(t *testing.T) {
	tests := []struct {
		name         string
		memoryResult *ProjectMemory
		expected     string
	}{
		{
			name:         "nil result",
			memoryResult: nil,
			expected:     "",
		},
		{
			name: "empty result",
			memoryResult: &ProjectMemory{
				FilePaths:    []string{},
				FileContents: []FileContent{},
			},
			expected: "",
		},
		{
			name: "single file success",
			memoryResult: &ProjectMemory{
				FilePaths: []string{"/path/to/aime.md"},
				FileContents: []FileContent{
					{
						FilePath: "/path/to/aime.md",
						Content:  "# Test Memory\nThis is test content.",
						Error:    nil,
					},
				},
			},
			expected: `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.

Contents of /path/to/aime.md (project instructions, checked into the codebase): 

# Test Memory
This is test content.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`,
		},
		{
			name: "multiple files success",
			memoryResult: &ProjectMemory{
				FilePaths: []string{"/repo/root/aime.md", "/repo/root/subdir/claude.md"},
				FileContents: []FileContent{
					{
						FilePath: "/repo/root/aime.md",
						Content:  "# AIME Memory\nAIME content here.",
						Error:    nil,
					},
					{
						FilePath: "/repo/root/subdir/claude.md",
						Content:  "# Claude Memory\nClaude content here.",
						Error:    nil,
					},
				},
			},
			expected: `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.

Contents of /repo/root/aime.md (project instructions, checked into the codebase): 

# AIME Memory
AIME content here.

Contents of /repo/root/subdir/claude.md (project instructions, checked into the codebase): 

# Claude Memory
Claude content here.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`,
		},
		{
			name: "file with read error (should be skipped)",
			memoryResult: &ProjectMemory{
				FilePaths: []string{"/path/to/broken.md", "/path/to/good.md"},
				FileContents: []FileContent{
					{
						FilePath: "/path/to/broken.md",
						Content:  "",
						Error:    os.ErrPermission,
					},
					{
						FilePath: "/path/to/good.md",
						Content:  "# Good File\nThis works.",
						Error:    nil,
					},
				},
			},
			expected: `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.

Contents of /path/to/good.md (project instructions, checked into the codebase): 

# Good File
This works.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`,
		},
		{
			name: "empty file content (should be skipped)",
			memoryResult: &ProjectMemory{
				FilePaths: []string{"/path/to/empty.md", "/path/to/nonempty.md"},
				FileContents: []FileContent{
					{
						FilePath: "/path/to/empty.md",
						Content:  "   \n\t\n   ", // whitespace only
						Error:    nil,
					},
					{
						FilePath: "/path/to/nonempty.md",
						Content:  "# Non-empty\nHas content.",
						Error:    nil,
					},
				},
			},
			expected: `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.

Contents of /path/to/nonempty.md (project instructions, checked into the codebase): 

# Non-empty
Has content.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`,
		},
		{
			name: "long path no truncation",
			memoryResult: &ProjectMemory{
				FilePaths: []string{"/very/long/path/with/many/levels/deep/nested/file.md"},
				FileContents: []FileContent{
					{
						FilePath: "/very/long/path/with/many/levels/deep/nested/file.md",
						Content:  "# Deep File\nContent from deep file.",
						Error:    nil,
					},
				},
			},
			expected: `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.

Contents of /very/long/path/with/many/levels/deep/nested/file.md (project instructions, checked into the codebase): 

# Deep File
Content from deep file.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`,
		},
		{
			name: "mixed content with whitespace trimming",
			memoryResult: &ProjectMemory{
				FilePaths: []string{"/path/to/whitespace.md"},
				FileContents: []FileContent{
					{
						FilePath: "/path/to/whitespace.md",
						Content:  "\n\n  # Whitespace Test  \n  Content with leading/trailing whitespace.  \n\n",
						Error:    nil,
					},
				},
			},
			expected: `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.

Contents of /path/to/whitespace.md (project instructions, checked into the codebase): 

# Whitespace Test  
  Content with leading/trailing whitespace.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`,
		},
		{
			name: "all files have errors or empty content",
			memoryResult: &ProjectMemory{
				FilePaths: []string{"/path/to/error1.md", "/path/to/error2.md", "/path/to/empty.md"},
				FileContents: []FileContent{
					{
						FilePath: "/path/to/error1.md",
						Content:  "",
						Error:    os.ErrNotExist,
					},
					{
						FilePath: "/path/to/error2.md",
						Content:  "",
						Error:    os.ErrPermission,
					},
					{
						FilePath: "/path/to/empty.md",
						Content:  "   \n\t  \n   ", // whitespace only
						Error:    nil,
					},
				},
			},
			expected: `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.

IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.memoryResult.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}
