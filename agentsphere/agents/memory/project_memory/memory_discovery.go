package project_memory

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris/telemetry"
)

// FileContent represents a file and its content
type FileContent struct {
	FilePath string
	Content  string
	Error    error
}

// MemoryConfig contains configuration for memory discovery
type MemoryConfig struct {
	// Filename is the name of the memory file to search for
	// e.g. "aime.md" "CLAUDE.md" "GENMINI.md"
	FileName string
	// MaxDirs is the maximum number of directories to search for the memory file
	// default is 200
	MaxDirs int
	Debug   bool
	// RepoPath is the path to the repo
	// e.g. "/Users/<USER>/your-repo"
	RepoPath string
	// EnableImportProcessing is a boolean flag to enable or disable import processing
	// default is true
	EnableImportProcessing bool
	// Logger is the logger instance to use for debug output
	Logger iris.Logger
}

// ProjectMemory contains the result of memory discovery
type ProjectMemory struct {
	// FilePaths list of memory file paths
	FilePaths []string
	// FileContents list of file contents which may contain imported files from memory files
	FileContents []FileContent
}

// MemoryDiscovery handles hierarchical memory file discovery and processing
type MemoryDiscovery struct {
	config          MemoryConfig
	importProcessor *ImportProcessor
	logger          iris.Logger
}

// NewMemoryDiscovery creates a new memory discovery instance
func NewMemoryDiscovery(config MemoryConfig) *MemoryDiscovery {
	// Set defaults
	if config.MaxDirs == 0 {
		config.MaxDirs = 2000
	}

	var importProcessor *ImportProcessor
	if config.EnableImportProcessing {
		importProcessor = NewImportProcessor(config.Debug, config.Logger)
	}

	return &MemoryDiscovery{
		config:          config,
		importProcessor: importProcessor,
		logger:          config.Logger,
	}
}

// LoadHierarchicalMemory loads and processes hierarchical memory files
func (md *MemoryDiscovery) LoadHierarchicalMemory() (*ProjectMemory, error) {
	if md.config.Debug && md.logger != nil {
		md.logger.Debugf("Loading hierarchical memory for repo: %s", md.config.RepoPath)
	}

	// Get all file paths in hierarchical order
	filePaths, err := md.getMemoryFilePaths()
	if err != nil {
		return nil, fmt.Errorf("failed to get memory file paths: %w", err)
	}

	if len(filePaths) == 0 {
		if md.config.Debug && md.logger != nil {
			md.logger.Debugf("No %s files found in hierarchy", md.config.FileName)
		}
		return &ProjectMemory{
			FilePaths:    []string{},
			FileContents: []FileContent{},
		}, nil
	}

	// Read and process all files
	fileContents, err := md.readMemoryFiles(filePaths)
	if err != nil {
		return nil, fmt.Errorf("failed to read memory files: %w", err)
	}

	return &ProjectMemory{
		FilePaths:    filePaths,
		FileContents: fileContents,
	}, nil
}

// getMemoryFilePaths returns all memory file paths by searching down from repo root
func (md *MemoryDiscovery) getMemoryFilePaths() ([]string, error) {
	// Resolve repo path
	resolvedRepoPath, err := filepath.Abs(md.config.RepoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve repo path: %w", err)
	}

	if md.config.Debug && md.logger != nil {
		md.logger.Debugf("Searching for %s in repo: %s", md.config.FileName, resolvedRepoPath)
	}

	// Search downward from repo root
	memoryPaths, err := md.searchDownward(resolvedRepoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to search repo: %w", err)
	}

	// Sort for consistent ordering
	sort.Strings(memoryPaths)

	if md.config.Debug && md.logger != nil {
		md.logger.Debugf("Found %s files: %v", md.config.FileName, memoryPaths)
	}

	return memoryPaths, nil
}

// searchDownward searches for memory files recursively in the directory tree
func (md *MemoryDiscovery) searchDownward(searchPath string) ([]string, error) {
	var memoryPaths []string
	visitedDirs := 0

	err := filepath.WalkDir(searchPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			if md.config.Debug && md.logger != nil {
				md.logger.Debugf("Error walking directory %s: %v", path, err)
			}
			return nil // Continue walking
		}

		if d.IsDir() {
			visitedDirs++
			if visitedDirs > md.config.MaxDirs {
				if md.config.Debug && md.logger != nil {
					md.logger.Debugf("Reached maximum directory limit (%d), stopping search", md.config.MaxDirs)
				}
				return filepath.SkipDir
			}
			return nil
		}

		if d.Name() == md.config.FileName && md.isFileReadable(path) {
			memoryPaths = append(memoryPaths, path)
			if md.config.Debug && md.logger != nil {
				md.logger.Debugf("Found %s: %s", md.config.FileName, path)
			}
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("error during search: %w", err)
	}

	return memoryPaths, nil
}

// readMemoryFiles reads all memory files and processes imports if enabled
func (md *MemoryDiscovery) readMemoryFiles(filePaths []string) ([]FileContent, error) {
	results := make([]FileContent, 0, len(filePaths))

	for _, filePath := range filePaths {
		content, err := os.ReadFile(filePath)
		if err != nil {
			if md.config.Debug && md.logger != nil {
				md.logger.Warnf("Could not read %s file at %s. Error: %v", md.config.FileName, filePath, err)
			}
			results = append(results, FileContent{
				FilePath: filePath,
				Content:  "",
				Error:    err,
			})
			continue
		}

		contentStr := string(content)

		// Add the original file to results first
		results = append(results, FileContent{
			FilePath: filePath,
			Content:  contentStr,
			Error:    nil,
		})

		// Process imports if enabled
		if md.config.EnableImportProcessing && md.importProcessor != nil {
			importedFiles, err := md.importProcessor.ProcessImports(contentStr, filepath.Dir(filePath), md.config.RepoPath)
			if err != nil {
				if md.config.Debug && md.logger != nil {
					md.logger.Warnf("Failed to process imports in %s: %v", filePath, err)
				}
				// Continue without importing files if import processing fails
			} else {
				// Append imported files to results
				results = append(results, importedFiles...)
				if md.config.Debug && md.logger != nil {
					md.logger.Debugf("Successfully processed imports for %s. Imported files: %d", filePath, len(importedFiles))
				}
			}
		} else if md.config.Debug && md.logger != nil {
			md.logger.Debugf("Successfully read: %s (Length: %d)", filePath, len(contentStr))
		}
	}

	return results, nil
}

// isFileReadable checks if a file exists and is readable
func (md *MemoryDiscovery) isFileReadable(filePath string) bool {
	info, err := os.Stat(filePath)
	if err != nil {
		return false
	}

	// Check if it's a regular file (not a directory)
	if !info.Mode().IsRegular() {
		return false
	}

	// Try to open the file to check readability
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	return true
}

// GetDefaultConfig returns a default configuration for memory discovery
func GetDefaultConfig(fileName, repoPath string, logger iris.Logger) MemoryConfig {
	return MemoryConfig{
		FileName:               fileName,
		MaxDirs:                2000,
		Debug:                  true,
		RepoPath:               repoPath,
		EnableImportProcessing: true,
		Logger:                 logger,
	}
}

// LoadAndMergeMultipleMemories loads memory files for multiple file names and merges the results
// It removes duplicate files based on file paths
// If multiple files import the same file, the file will be included only once
func LoadAndMergeMultipleMemories(run *iris.AgentRunContext, fileNames []string, repoName string, repoPath string, logger iris.Logger) (*ProjectMemory, error) {
	if len(fileNames) == 0 {
		return &ProjectMemory{
			FilePaths:    []string{},
			FileContents: []FileContent{},
		}, nil
	}

	// Map to track unique file paths and avoid duplicates
	filePathMap := make(map[string]FileContent)
	var allFilePaths []string

	// Load memory for each file name
	for _, fileName := range fileNames {
		config := GetDefaultConfig(fileName, repoPath, logger)
		discovery := NewMemoryDiscovery(config)

		result, err := discovery.LoadHierarchicalMemory()
		if err != nil {
			if logger != nil {
				logger.Warnf("Failed to load memory for file %s: %v", fileName, err)
			}
			continue
		}

		// Add file contents to map to avoid duplicates
		for _, fileContent := range result.FileContents {
			if _, exists := filePathMap[fileContent.FilePath]; !exists {
				filePathMap[fileContent.FilePath] = fileContent
				allFilePaths = append(allFilePaths, fileContent.FilePath)
			}
		}
	}

	// Convert map back to slice
	mergedFileContents := make([]FileContent, 0, len(filePathMap))
	for _, filePath := range allFilePaths {
		mergedFileContents = append(mergedFileContents, filePathMap[filePath])
	}

	// Sort file paths for consistent ordering
	sort.Strings(allFilePaths)

	if logger != nil {
		logger.Debugf("Merged memory from %d file types, total files: %d", len(fileNames), len(mergedFileContents))
	}

	if run != nil {
		totalContent := lo.Reduce(mergedFileContents, func(acc string, fileContent FileContent, _ int) string {
			return acc + fileContent.Content
		}, "")
		telemetry.EmitAgentsMdUsage(run, repoName, len(totalContent))
	}

	return &ProjectMemory{
		FilePaths:    allFilePaths,
		FileContents: mergedFileContents,
	}, nil
}

const (
	// MemoryResultPrefix is the prefix for memory result string representation
	MemoryResultPrefix = `<system-reminder>As you answer the user's questions, you can use the following context:
Codebase instructions are shown below. Be sure to adhere to these instructions only when its related to the codebase and task.`
	// MemoryResultPostfix is the prefix for memory prompt string representation
	MemoryResultPostfix = `IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>`

	// MaxRepoMemoryLength is the maximum length of the repo memory string
	MaxRepoMemoryLength = 15000
)

// String implements the Stringer interface for ProjectMemory
func (mr *ProjectMemory) String() string {
	if mr == nil {
		return ""
	}

	if len(mr.FilePaths) == 0 {
		return ""
	}

	var result strings.Builder
	result.WriteString(MemoryResultPrefix + "\n\n")

	for _, fileContent := range mr.FileContents {
		if fileContent.Error != nil {
			continue // skip error files
		}

		trimmedContent := strings.TrimSpace(fileContent.Content)
		if len(trimmedContent) == 0 {
			continue // Skip empty files
		}

		displayPath := fileContent.FilePath

		fullContent := fmt.Sprintf("Contents of %s (project instructions, checked into the codebase): \n\n",
			displayPath) + trimmedContent + "\n\n"

		// Check if adding this content would exceed the limit
		if result.Len()+len(fullContent) > MaxRepoMemoryLength {
			// Calculate how much space is left
			remainingSpace := MaxRepoMemoryLength - result.Len()

			// Reserve space for the header and truncation notice
			headerAndNotice := fmt.Sprintf("Contents of %s (project instructions, checked into the codebase): \n\n", displayPath) +
				"\n\n[Content truncated due to length limit]"

			if remainingSpace > len(headerAndNotice) {
				// Calculate how much of the content we can include
				availableContentSpace := remainingSpace - len(headerAndNotice)
				truncatedContent := trimmedContent
				if len(trimmedContent) > availableContentSpace {
					truncatedContent = trimmedContent[:availableContentSpace]
				}

				result.WriteString(fmt.Sprintf("Contents of %s (project instructions, checked into the codebase): \n\n",
					displayPath) + truncatedContent + "\n\n[Content truncated due to length limit]")
			}
			// If there's not even enough space for header and notice, skip this file
		} else {
			result.WriteString(fullContent)
		}
	}

	result.WriteString(MemoryResultPostfix)

	return result.String()
}
