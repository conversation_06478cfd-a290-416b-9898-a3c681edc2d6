package projectartifact

import (
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/util"
	"github.com/samber/lo"
)

var (
	mutex                          sync.Mutex
	projectArtifactManagerStoreKey = "project_artifact_manager"
)

func GetProjectArtifactManager(run *iris.AgentRunContext) *ProjectArtifactManager {
	projectArtifactManager := run.State.Store[projectArtifactManagerStoreKey]
	if projectArtifactManager != nil {
		return projectArtifactManager.(*ProjectArtifactManager)
	}
	mutex.Lock()
	defer mutex.Unlock()
	if projectArtifactManager = run.State.Store[projectArtifactManagerStoreKey]; projectArtifactManager != nil {
		return projectArtifactManager.(*ProjectArtifactManager)
	}
	projectArtifactManager = NewProjectArtifactManager()
	run.State.Store[projectArtifactManagerStoreKey] = projectArtifactManager
	return projectArtifactManager.(*ProjectArtifactManager)
}

type ProjectArtifactManager struct {
	mutex           sync.Mutex
	codebaseRepoMap map[string]CodebaseRepo
	lastVersionMap  map[string]ProjectArtifactVersion
	versionsMap     map[string][]ProjectArtifactVersion
	// 临时缓存，用于缓存 git clone 以及其他工程手段识别到的项目产物
	stashArea map[string]ProjectArtifactVersion
}

type CodebaseRepo struct {
	Name     string // eg: kiwis
	RepoName string // eg: codebase/kiwis
	Platform string // eg: gitlab、gerrit
	URL      string // eg: fmt.Sprintf("<EMAIL>:%s", repoName)
}

type ProjectArtifactVersion struct {
	iris.ProjectReferenceItem
	Version   int                  `json:"version" mapstructure:"version"`
	CommitSha string               `json:"commit_sha" mapstructure:"commit_sha"`
	Artifact  *iris.ResultArtifact `json:"artifact" mapstructure:"artifact"`
}

func NewProjectArtifactManager() *ProjectArtifactManager {
	return &ProjectArtifactManager{
		codebaseRepoMap: make(map[string]CodebaseRepo),
		lastVersionMap:  make(map[string]ProjectArtifactVersion),
		versionsMap:     make(map[string][]ProjectArtifactVersion),
		stashArea:       make(map[string]ProjectArtifactVersion),
	}
}

type ProcessProjectArtifactsOption struct {
	// 临时缓存，用于缓存 git clone 以及其他工程手段识别到的项目产物，用于当 upload attachments 时一并 pop 追加到 attachments，作为 LLM 产物识别的补充手段，提升 LLM 总结的准确性
	stashProjectArtifacts    bool
	stashPopProjectArtifacts bool
	// 是否针对发生变更的历史项目进行补偿追加
	compensateChangedProjectArtifacts bool
}

var (
	UploadAttachmentsOptions = []ProcessProjectArtifactsOptionFunc{
		WithStashPopProjectArtifacts(),
		WithCompensateChangedProjectArtifacts(),
	}
	StashProjectArtifactsOptions = []ProcessProjectArtifactsOptionFunc{
		WithStashProjectArtifacts(),
	}
)

func WithStashProjectArtifacts() ProcessProjectArtifactsOptionFunc {
	return func(option *ProcessProjectArtifactsOption) {
		option.stashProjectArtifacts = true
	}
}

func WithStashPopProjectArtifacts() ProcessProjectArtifactsOptionFunc {
	return func(option *ProcessProjectArtifactsOption) {
		option.stashPopProjectArtifacts = true
	}
}

func WithCompensateChangedProjectArtifacts() ProcessProjectArtifactsOptionFunc {
	return func(option *ProcessProjectArtifactsOption) {
		option.compensateChangedProjectArtifacts = true
	}
}

type ProcessProjectArtifactsOptionFunc func(option *ProcessProjectArtifactsOption)

func (m *ProjectArtifactManager) MarkCodebaseProjectArtifact(run *iris.AgentRunContext, codebaseRepos ...CodebaseRepo) {
	run.GetLogger().Infof("marking codebase project artifact, codebase repos: %s", util.MarshalStruct(codebaseRepos))
	m.mutex.Lock()
	defer m.mutex.Unlock()
	for _, repo := range codebaseRepos {
		m.codebaseRepoMap[repo.Name] = repo
	}
	run.GetLogger().Infof("marked codebase project artifact, codebase repos: %s", util.MarshalStruct(m.codebaseRepoMap))
	return
}

// ProcessProjectArtifacts 处理项目产物，整体包含：
// 1. LLM 识别到的项目产物
// 2. 由工程链路中识别到的项目产物（stash area），例如 git clone 识别到的项目产物
// 3. 发生变更的历史项目产物
func (m *ProjectArtifactManager) ProcessProjectArtifacts(run *iris.AgentRunContext, projectReference iris.ProjectReference, optionFunc ...ProcessProjectArtifactsOptionFunc) (projectArtifactVersions []ProjectArtifactVersion, err error) {
	artifacts := run.GetArtifactService()
	if artifacts.IsNil() {
		run.GetLogger().Errorf("failed to connect to artifact service.")
		return nil, fmt.Errorf("failed to connect to artifact service. session: %s", run.State.SessionID)
	}
	m.mutex.Lock()
	defer m.mutex.Unlock()

	option := &ProcessProjectArtifactsOption{}
	for _, f := range optionFunc {
		f(option)
	}

	// 针对已有的项目也要进行叠加处理，避免模型总结丢失/漏掉, 但工作区已经发生变更的情况
	projectReferenceByLLM := lo.SliceToMap(projectReference, func(item iris.ProjectReferenceItem) (string, struct{}) {
		return item.Name, struct{}{}
	})
	projectReferenceByStash := make(map[string]struct{})
	if option.stashPopProjectArtifacts {
		for _, projectArtifactVersion := range m.stashArea {
			if _, ok := projectReferenceByLLM[projectArtifactVersion.Name]; ok {
				continue
			}
			projectReferenceByStash[projectArtifactVersion.Name] = struct{}{}
			projectReference = append(projectReference, projectArtifactVersion.ProjectReferenceItem)
		}
		// clear stash
		m.stashArea = map[string]ProjectArtifactVersion{}
	}

	historyProjectReference := map[string]ProjectArtifactVersion{}
	if option.compensateChangedProjectArtifacts {
		for name, projectArtifactVersion := range m.lastVersionMap {
			if _, ok := projectReferenceByLLM[name]; ok {
				continue
			}
			if _, ok := projectReferenceByStash[name]; ok {
				continue
			}
			historyProjectReference[name] = projectArtifactVersion
			projectReference = append(projectReference, projectArtifactVersion.ProjectReferenceItem)
		}
	}

	for _, projectReferenceItem := range projectReference {
		projectArtifactVersion, tempErr := m.ProcessProjectArtifact(run, projectReferenceItem)
		if tempErr != nil {
			err = errors.Join(err, tempErr)
			continue
		}
		// 如果是非 LLM 总结的（即 historyProjectReference 中存在），且版本没变化的，直接跳过
		if h, ok := historyProjectReference[projectArtifactVersion.Name]; ok {
			if projectArtifactVersion.Version == h.Version {
				continue
			}
		}
		projectArtifactVersions = append(projectArtifactVersions, projectArtifactVersion)
	}

	if option.stashProjectArtifacts {
		for _, projectArtifactVersion := range projectArtifactVersions {
			m.stashArea[projectArtifactVersion.Name] = projectArtifactVersion
		}
	}

	run.GetLogger().Infof("ProcessProjectArtifacts done, versionsMap: %s,projectArtifactVersions: %s", util.MarshalStruct(m.versionsMap), util.MarshalStruct(projectArtifactVersions))
	return projectArtifactVersions, err
}

func (m *ProjectArtifactManager) ProcessProjectArtifact(run *iris.AgentRunContext, item iris.ProjectReferenceItem) (ProjectArtifactVersion, error) {
	artifacts := run.GetArtifactService()
	if artifacts.IsNil() {
		return ProjectArtifactVersion{}, fmt.Errorf("failed to connect to artifact service. session: %s", run.State.SessionID)
	}

	// 1. check project path exists
	if err := m.checkProjectPathExists(run, item); err != nil {
		run.GetLogger().Errorf("Project artifact name: [%s],path: [%s] does not exist", item.Name, item.Path)
		return ProjectArtifactVersion{}, err
	}

	// 2. ensure project path is a git repo
	if err := m.ensureGitRepo(item.Path); err != nil {
		run.GetLogger().Errorf("Project artifact name: [%s],path: [%s] ensure git repo failed", item.Name, item.Path)
		return ProjectArtifactVersion{}, err
	}
	// 3.  check change
	var (
		hasChanges  bool
		err         error
		artifactKey = fmt.Sprintf("%s-%s-%s", nextentity.ArtifactTypeProject, run.State.SessionID, Sha256Hex(item.Name))
	)
	if hasChanges, err = m.hasChanges(item.Path); err != nil {
		run.GetLogger().Errorf("Project artifact name: [%s],path: [%s] has changes", item.Name, item.Path)
		return ProjectArtifactVersion{}, err
	}
	lastVersion, hasVersion := m.lastVersionMap[item.Name]
	version := 1
	if hasVersion {
		version = lastVersion.Version + 1
	}
	var newCommit string
	if !hasChanges {
		// TODO: 如果是空仓库可能会有问题，但是空仓库到这里也不合理吧
		commit, err := m.GetHeadGitCommit(item.Path)
		if err != nil {
			run.GetLogger().Errorf("Project artifact name: [%s],path: [%s] get head git commit", item.Name, item.Path)
			return ProjectArtifactVersion{}, err
		}
		// no change & no first version & commit not changed, skip, no need to record
		if hasVersion && commit == lastVersion.CommitSha {
			return lastVersion, nil
		}
		// first version or commit changed, record commit sha
		newCommit = commit
	} else {
		// 4. git add & commit
		newCommit, err = m.gitAddCommit(item.Path, fmt.Sprintf("version: %d", version))
		if err != nil {
			run.GetLogger().Errorf("Project artifact name: [%s],path: [%s] git add & commit", item.Name, item.Path)
			return ProjectArtifactVersion{}, err
		}
	}

	newVersion := ProjectArtifactVersion{
		ProjectReferenceItem: item,
		Version:              version,
		CommitSha:            newCommit,
	}
	metadata := nextentity.ProjectArtifactTypeMetadata{
		Name:   item.Name,
		Path:   item.Path,
		Commit: newCommit,
	}
	if codebaseRepo, ok := m.codebaseRepoMap[item.Name]; ok {
		metadata.CodebaseRepoMeta = &nextentity.CodebaseRepoMeta{
			Name:     codebaseRepo.Name,
			RepoName: codebaseRepo.RepoName,
			Platform: codebaseRepo.Platform,
			URL:      codebaseRepo.URL,
		}
	}
	fileMetas := make([]nextagent.FileMeta, 0, 1)
	fileMetas = append(fileMetas, nextagent.FileMeta{
		Name:    item.Name,
		Size:    GetFolderSize(item.Path),
		Content: "",
		Type:    nextagent.ArtifactTypeProject,
		SubType: "repo",
	})
	artifact, err := artifacts.NewArtifactTypeProjectWithKey(run, artifactKey, metadata, fileMetas...)
	if err != nil {
		run.GetLogger().Errorf("Project artifact name: [%s],path: [%s] new artifact", item.Name, item.Path)
		return ProjectArtifactVersion{}, err
	}
	artifact.Metadata = metadata.ToMap()
	if err = artifacts.CommitArtifact(run, artifact); err != nil {
		run.GetLogger().Errorf("Project artifact name: [%s],path: [%s] commit artifact", item.Name, item.Path)
		return ProjectArtifactVersion{}, err
	}
	newVersion.Artifact = artifact
	m.versionsMap[item.Name] = append(m.versionsMap[item.Name], newVersion)
	m.lastVersionMap[item.Name] = newVersion
	return newVersion, nil
}

func (m *ProjectArtifactManager) hasChanges(projectPath string) (bool, error) {
	cmd := exec.Command("git", "status", "--porcelain")
	cmd.Dir = projectPath
	out, err := cmd.Output()
	if err != nil {
		return false, err
	}
	return len(strings.TrimSpace(string(out))) > 0, nil
}

func (m *ProjectArtifactManager) gitAddCommit(projectPath, msg string) (string, error) {
	cmd := exec.Command("git", "add", ".")
	cmd.Dir = projectPath
	if err := cmd.Run(); err != nil {
		return "", err
	}
	cmd = exec.Command("git", "commit", "-m", msg, "--allow-empty")
	cmd.Dir = projectPath
	if err := cmd.Run(); err != nil {
		return "", err
	}
	cmd = exec.Command("git", "rev-parse", "HEAD")
	cmd.Dir = projectPath
	out, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(out)), nil
}

func (m *ProjectArtifactManager) ensureGitRepo(projectPath string) error {
	gitDir := filepath.Join(projectPath, ".git")
	if _, err := os.Stat(gitDir); os.IsNotExist(err) {
		cmd := exec.Command("git", "init")
		cmd.Dir = projectPath
		return cmd.Run()
	}
	return nil
}

func (m *ProjectArtifactManager) checkProjectPathExists(run *iris.AgentRunContext, item iris.ProjectReferenceItem) error {
	// if not dir or not exist
	info, err := os.Stat(item.Path)
	if err != nil {
		return err
	}
	if !info.IsDir() {
		return errors.New("project path is not a directory")
	}
	return nil
}

// GetHeadGitCommit 获取指定仓库当前 HEAD 的 commit hash
func (m *ProjectArtifactManager) GetHeadGitCommit(projectPath string) (string, error) {
	cmd := exec.Command("git", "rev-parse", "HEAD")
	cmd.Dir = projectPath
	out, err := cmd.Output()
	if err != nil {
		return "", err
	}
	commitSha := strings.TrimSpace(string(out))
	// check commit sha
	if commitSha == "" {
		return "", errors.New("empty commit sha")
	}
	return commitSha, nil
}

func Sha256Hex(s string) string {
	hash := sha256.Sum256([]byte(s))
	return hex.EncodeToString(hash[:])
}

func GetFolderSize(path string) int64 {
	var size int64 = 0
	filepath.WalkDir(path, func(_ string, d fs.DirEntry, err error) error {
		if err != nil {
			// 如果遇到错误，继续遍历
			return nil
		}
		// ignore .git
		if d.IsDir() && d.Name() == ".git" {
			return filepath.SkipDir
		}
		if !d.IsDir() {
			info, err := d.Info()
			if err == nil {
				size += info.Size()
			}
		}
		return nil
	})
	return size
}
