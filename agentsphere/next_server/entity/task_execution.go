package entity

import (
	"time"

	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"github.com/samber/lo"
)

type TaskExecution struct {
	ID                int64                          `json:"id"`
	CreatedAt         time.Time                      `json:"created_at"`
	UpdatedAt         time.Time                      `json:"updated_at"`
	TaskUID           string                         `json:"task_uid"`
	TaskName          string                         `json:"task_name"`
	Username          string                         `json:"username"`
	ExecuteTime       time.Time                      `json:"execute_time"`
	Status            SessionStatus                  `json:"status"`
	ErrorMessage      *string                        `json:"error_message,omitempty"`
	SessionID         string                         `json:"session_id"`
	TriggerType       TriggerType                    `json:"trigger_type"`
	MQEventID         *string                        `json:"mq_event_id,omitempty"`
	Config            TaskConfig                     `json:"config"`
	LarkMessageID     *string                        `json:"lark_message_id,omitempty"`
	LarkMessageStatus TaskExecutionLarkMessageStatus `json:"lark_message_status"`
}

func (c *TaskExecution) ToIDL() *nextidl.TaskExecution {
	if c == nil {
		return nil
	}

	return &nextidl.TaskExecution{
		ID:           lo.ToPtr(c.ID),
		TaskUID:      c.TaskUID,
		Username:     lo.ToPtr(c.Username),
		ExecuteTime:  c.ExecuteTime.Format(time.RFC3339),
		ErrorMessage: c.ErrorMessage,
		SessionID:    c.SessionID,
		TriggerType:  c.TriggerTypeToIDL(),
		TaskName:     c.TaskName,
		RunStatus:    c.Status.ToIDL(),
	}
}

type TriggerType int

const (
	TriggerTypeUnknown TriggerType = iota
	TriggerTypeManual
	TriggerTypeCronJob
	TriggerTypeDBCompensation
	TriggerTypeCodebase
	TriggerTypeSlardar
)

func (c *TaskExecution) TriggerTypeToIDL() nextidl.TriggerType {
	if c == nil {
		return nextidl.TriggerType_Unknown
	}
	switch c.TriggerType {
	case TriggerTypeManual:
		return nextidl.TriggerType_Manual
	case TriggerTypeCronJob:
		return nextidl.TriggerType_CronJob
	case TriggerTypeDBCompensation:
		return nextidl.TriggerType_DBCompensation
	default:
		return nextidl.TriggerType_Unknown
	}
}

type TaskConfig struct {
	LarkGroupID          *string `json:"lark_group_id,omitempty"`          // 发送通知到群组，配置了则需要发送
	SendUserNotification *bool   `json:"send_user_notification,omitempty"` // 是否发送通知给个人
}

type TaskExecutionLarkMessageStatus int

const (
	TaskExecutionLarkMessageStatusUnknown TaskExecutionLarkMessageStatus = iota
	TaskExecutionLarkMessageStatusIdle                                   // 首次发送了卡片后，处于IDLE状态，后续可更新卡片
	TaskExecutionLarkMessageStatusDone                                   // 更新卡片时，不论是否有问题，状态均处于终态，不可再发送定时任务卡片
)

func ParseTaskIDLFromEntity(config *nextidl.TaskConfig) *TaskConfig {
	if config == nil {
		return nil
	}
	return &TaskConfig{
		LarkGroupID:          config.LarkGroupID,
		SendUserNotification: config.SendUserNotifaction,
	}
}

type TaskExecutionStatus int

const (
	TaskExecutionStatusUnknown TaskExecutionStatus = 0
	TaskExecutionStatusSuccess TaskExecutionStatus = 1
	TaskExecutionStatusFailure TaskExecutionStatus = 2
	TaskExecutionStatusRunning TaskExecutionStatus = 3
)
