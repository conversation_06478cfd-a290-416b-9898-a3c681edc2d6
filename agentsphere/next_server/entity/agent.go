package entity

import (
	"encoding/json"
	"time"

	"github.com/AlekSi/pointer"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

type Agent struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Creator     string    `json:"creator"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func (a *Agent) ToIDL() *nextidl.Agent {
	return &nextidl.Agent{
		ID:          a.ID,
		Name:        a.Name,
		Description: a.Description,
		Creator:     a.Creator,
		CreatedAt:   a.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   a.UpdatedAt.Format(time.RFC3339),
	}
}

type AgentConfigType string

const (
	AgentConfigTypeUnknown AgentConfigType = "unknown"
	AgentConfigTypeBase    AgentConfigType = "base"
	AgentConfigTypeFeature AgentConfigType = "feature"
	AgentConfigTypeUser    AgentConfigType = "user"
	AgentConfigTypeAbTest  AgentConfigType = "abtest"
)

func (a AgentConfigType) ToIDL() nextidl.AgentConfigType {
	switch a {
	case AgentConfigTypeBase:
		return nextidl.AgentConfigTypeBase
	case AgentConfigTypeFeature:
		return nextidl.AgentConfigTypeFeature
	case AgentConfigTypeUser:
		return nextidl.AgentConfigTypeUser
	case AgentConfigTypeAbTest:
		return nextidl.AgentConfigTypeAbTest
	default:
		return nextidl.AgentConfigTypeUnknown
	}
}

func ParseAgentType(s string) AgentConfigType {
	switch s {
	case "base":
		return AgentConfigTypeBase
	case "feature":
		return AgentConfigTypeFeature
	case "user":
		return AgentConfigTypeUser
	case "abtest":
		return AgentConfigTypeAbTest
	default:
		return AgentConfigTypeUnknown
	}
}

type AgentConfig struct {
	ID          string          `json:"id"`
	AgentID     string          `json:"agent_id"`
	Type        AgentConfigType `json:"Type"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Creator     string          `json:"creator"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

func (a *AgentConfig) ToIDL() *nextidl.AgentConfig {
	return &nextidl.AgentConfig{
		ID:          a.ID,
		AgentID:     a.AgentID,
		Type:        a.Type.ToIDL(),
		Name:        a.Name,
		Description: a.Description,
		Creator:     a.Creator,
		CreatedAt:   a.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   a.UpdatedAt.Format(time.RFC3339),
	}
}

type AgentConfigStatus string

const (
	AgentConfigStatusUnknown AgentConfigStatus = "unknown"
	AgentConfigStatusCreated AgentConfigStatus = "created"
	AgentConfigStatusOnline  AgentConfigStatus = "online"
	AgentConfigStatusCanary  AgentConfigStatus = "canary"
)

func (a AgentConfigStatus) ToIDL() nextidl.AgentConfigStatus {
	switch a {
	case AgentConfigStatusCreated:
		return nextidl.AgentConfigStatusCreated
	case AgentConfigStatusOnline:
		return nextidl.AgentConfigStatusOnline
	case AgentConfigStatusCanary:
		return nextidl.AgentConfigStatusCanary
	default:
		return nextidl.AgentConfigStatusUnknown
	}
}

func ParseAgentConfigStatus(s string) AgentConfigStatus {
	switch s {
	case "created":
		return AgentConfigStatusCreated
	case "online":
		return AgentConfigStatusOnline
	case "canary":
		return AgentConfigStatusCanary
	default:
		return AgentConfigStatusUnknown
	}
}

type CustomConfig *json.RawMessage

type AgentConfigVersion struct {
	ID                 string             `json:"id"`
	AgentConfigID      string             `json:"agent_config_id"`
	Description        string             `json:"description"`
	Creator            string             `json:"creator"`
	Version            int                `json:"version"`
	Enabled            bool               `json:"enabled"`
	Status             AgentConfigStatus  `json:"status"`
	RuntimeConfig      RuntimeConfig      `json:"runtime_config"`
	CustomConfig       CustomConfig       `json:"custom_config"`
	PromptConfig       PromptConfig       `json:"prompt_config"`
	KnowledgesetConfig KnowledgesetConfig `json:"knowledge_config"`
	CreatedAt          time.Time          `json:"created_at"`
	UpdatedAt          time.Time          `json:"updated_at"`

	Knowledgesets        []*Knowledgeset        `json:"knowledgesets"`
	KnowledgesetVersions []*KnowledgesetVersion `json:"knowledge_set_versions"`
	DeployID             string                 `json:"deploy_id"`
}

func (a *AgentConfigVersion) ToIDL() *nextidl.AgentConfigVersion {
	return &nextidl.AgentConfigVersion{
		ID:                 a.ID,
		AgentConfigID:      a.AgentConfigID,
		Description:        a.Description,
		Creator:            a.Creator,
		Version:            int32(a.Version),
		Enabled:            a.Enabled,
		Status:             a.Status.ToIDL(),
		RuntimeConfig:      a.RuntimeConfig.ToIDL(),
		CustomConfig:       common.JsonVariables(pointer.Get(a.CustomConfig)),
		PromptConfig:       a.PromptConfig.ToIDL(),
		CreatedAt:          a.CreatedAt.Format(time.RFC3339),
		UpdatedAt:          a.UpdatedAt.Format(time.RFC3339),
		KnowledgesetConfig: a.KnowledgesetConfig.ToIDL(),
		Knowledgesets: lo.Map(a.Knowledgesets, func(item *Knowledgeset, index int) *nextidl.Knowledgeset {
			return item.ToIDL()
		}),
		KnowledgesetVersions: lo.Map(a.KnowledgesetVersions, func(item *KnowledgesetVersion, index int) *nextidl.KnowledgesetVersion {
			return item.ToIDL()
		}),
		DeployID: &a.DeployID,
	}
}

type RuntimeConfigType string

const (
	RuntimeConfigTypeUnknown    RuntimeConfigType = "unknown"
	RuntimeConfigTypeDocker     RuntimeConfigType = "docker"
	RuntimeConfigTypeStratoCube RuntimeConfigType = "stratocube"
)

func (a RuntimeConfigType) ToIDL() nextidl.RuntimeConfigType {
	switch a {
	case RuntimeConfigTypeDocker:
		return nextidl.RuntimeConfigTypeDocker
	case RuntimeConfigTypeStratoCube:
		return nextidl.RuntimeConfigTypeStratoCube
	default:
		return nextidl.RuntimeConfigTypeUnknown
	}
}

func ParseRuntimeConfigType(s string) RuntimeConfigType {
	switch s {
	case "docker":
		return RuntimeConfigTypeDocker
	case "stratocube":
		return RuntimeConfigTypeStratoCube
	default:
		return RuntimeConfigTypeUnknown
	}
}

type ResourceQuantity struct {
	Requests string  `json:"requests"`
	Limits   *string `json:"limits"`
}

func (r *ResourceQuantity) ToIDL() *nextidl.ResourceQuantity {
	return &nextidl.ResourceQuantity{
		Requests: r.Requests,
		Limits:   r.Limits,
	}
}

func ParseResourceQuantityFromIDL(quantity *nextidl.ResourceQuantity) *ResourceQuantity {
	if quantity == nil {
		return nil
	}
	return &ResourceQuantity{
		Requests: quantity.Requests,
		Limits:   quantity.Limits,
	}
}

type RuntimeResourceQuota struct {
	CPU              *ResourceQuantity `json:"cpu"`
	MEM              *ResourceQuantity `json:"mem"`
	PersistWorkspace *ResourceQuantity `json:"persist_workspace"`
	StorageClass     *string           `json:"storage_class"`
}

func (r *RuntimeResourceQuota) ToIDL() *nextidl.RuntimeResourceQuota {
	return &nextidl.RuntimeResourceQuota{
		CPU: lo.TernaryF(r.CPU != nil,
			func() *nextidl.ResourceQuantity { return r.CPU.ToIDL() },
			func() *nextidl.ResourceQuantity { return nil },
		),
		MEM: lo.TernaryF(r.CPU != nil,
			func() *nextidl.ResourceQuantity { return r.MEM.ToIDL() },
			func() *nextidl.ResourceQuantity { return nil },
		),
		PersistWorkspace: lo.TernaryF(r.CPU != nil,
			func() *nextidl.ResourceQuantity { return r.PersistWorkspace.ToIDL() },
			func() *nextidl.ResourceQuantity { return nil },
		),
		StorageClass: r.StorageClass,
	}
}

func ParseRuntimeResourceQuotaFromIDL(quota *nextidl.RuntimeResourceQuota) *RuntimeResourceQuota {
	if quota == nil {
		return nil
	}
	return &RuntimeResourceQuota{
		CPU:              ParseResourceQuantityFromIDL(quota.CPU),
		MEM:              ParseResourceQuantityFromIDL(quota.MEM),
		PersistWorkspace: ParseResourceQuantityFromIDL(quota.PersistWorkspace),
		StorageClass:     quota.StorageClass,
	}
}

type OrchestrationConfig struct {
	MaxConcurrency       int    `json:"max_concurrency"`
	PoolSize             int    `json:"pool_size"`
	RuntimeStopTimeout   string `json:"runtime_stop_timeout"`
	RuntimeDeleteTimeout string `json:"runtime_delete_timeout"`
}

func (o *OrchestrationConfig) ToIDL() *nextidl.OrchestrationConfig {
	return &nextidl.OrchestrationConfig{
		MaxConcurrency:       int32(o.MaxConcurrency),
		PoolSize:             int32(o.PoolSize),
		RuntimeStopTimeout:   o.RuntimeStopTimeout,
		RuntimeDeleteTimeout: o.RuntimeDeleteTimeout,
	}
}

func ParseOrchestrationConfigFromIDL(config *nextidl.OrchestrationConfig) *OrchestrationConfig {
	if config == nil {
		return nil
	}
	return &OrchestrationConfig{
		MaxConcurrency:       int(config.MaxConcurrency),
		PoolSize:             int(config.PoolSize),
		RuntimeStopTimeout:   config.RuntimeStopTimeout,
		RuntimeDeleteTimeout: config.RuntimeDeleteTimeout,
	}
}

type RuntimeConfig struct {
	ID                   string                `json:"id"` // 对应 Agent 侧的唯一标识
	PSM                  string                `json:"psm"`
	Type                 RuntimeConfigType     `json:"type"`
	InjectOpenAIToken    bool                  `json:"inject_openai_token"`
	Image                *string               `json:"image"`
	BashImage            *string               `json:"bash_image"`
	Envs                 map[string]string     `json:"envs"`
	Port                 *int                  `json:"port"`
	RuntimeResourceQuota *RuntimeResourceQuota `json:"runtime_resource_quota"`
	BinarySource         *string               `json:"binary_source"`
	OrchestrationConfig  *OrchestrationConfig  `json:"orchestration_config"`
}

func (r *RuntimeConfig) ToIDL() *nextidl.RuntimeConfig {
	return &nextidl.RuntimeConfig{
		ID:                r.ID,
		PSM:               r.PSM,
		Type:              r.Type.ToIDL(),
		InjectOpenAIToken: r.InjectOpenAIToken,
		Image:             r.Image,
		BashImage:         lo.ToPtr(pointer.Get(r.BashImage)), // 前端默认值需要为""，而不是null
		Envs:              r.Envs,
		Port: lo.TernaryF(r.Port != nil,
			func() *int32 { return lo.ToPtr(int32(*r.Port)) },
			func() *int32 { return nil }),
		Quota: lo.TernaryF(r.RuntimeResourceQuota != nil,
			func() *nextidl.RuntimeResourceQuota { return r.RuntimeResourceQuota.ToIDL() },
			func() *nextidl.RuntimeResourceQuota { return nil }),
		BinarySource: r.BinarySource,
		OrchestrationConfig: lo.TernaryF(r.OrchestrationConfig != nil,
			func() *nextidl.OrchestrationConfig { return r.OrchestrationConfig.ToIDL() },
			func() *nextidl.OrchestrationConfig { return nil },
		),
	}
}

func ParseRuntimeConfigFromIDL(runtimeConfig *nextidl.RuntimeConfig) *RuntimeConfig {
	if runtimeConfig == nil {
		return nil
	}
	return &RuntimeConfig{
		ID:                runtimeConfig.ID,
		PSM:               runtimeConfig.PSM,
		Type:              ParseRuntimeConfigType(runtimeConfig.Type),
		InjectOpenAIToken: runtimeConfig.InjectOpenAIToken,
		Image:             runtimeConfig.Image,
		BashImage:         runtimeConfig.BashImage,
		Envs:              runtimeConfig.Envs,
		Port: lo.TernaryF(runtimeConfig.Port != nil,
			func() *int { return lo.ToPtr(int(*runtimeConfig.Port)) },
			func() *int { return nil },
		),
		RuntimeResourceQuota: ParseRuntimeResourceQuotaFromIDL(runtimeConfig.Quota),
		BinarySource:         runtimeConfig.BinarySource,
		OrchestrationConfig:  ParseOrchestrationConfigFromIDL(runtimeConfig.OrchestrationConfig),
	}
}

type PromptConfig struct {
	Prompts []*PromptConfigMetadata `json:"prompts"`
}

type PromptConfigMetadata struct {
	Key     string `json:"key"` // 业务自定义信息
	ID      string `json:"id"`
	Version int    `json:"version"`
}

func (p *PromptConfigMetadata) ToIDL() *nextidl.PromptConfigMetadata {
	return &nextidl.PromptConfigMetadata{
		Key:     p.Key,
		ID:      p.ID,
		Version: int32(p.Version),
	}
}

func (p *PromptConfig) ToIDL() *nextidl.PromptConfig {
	return &nextidl.PromptConfig{
		Prompts: lo.Map(p.Prompts, func(item *PromptConfigMetadata, index int) *nextidl.PromptConfigMetadata {
			return item.ToIDL()
		}),
	}
}

func ParsePromptConfigFromIDL(promptConfig *nextidl.PromptConfig) *PromptConfig {
	if promptConfig == nil {
		return nil
	}
	return &PromptConfig{
		Prompts: lo.Map(promptConfig.Prompts, func(item *nextidl.PromptConfigMetadata, index int) *PromptConfigMetadata {
			return &PromptConfigMetadata{
				Key:     item.Key,
				ID:      item.ID,
				Version: int(item.Version),
			}
		}),
	}
}

type AgentDeployStatus string

const (
	AgentDeployStatusCreated      AgentDeployStatus = "created"
	AgentDeployStatusCanary       AgentDeployStatus = "canary"
	AgentDeployStatusFinish       AgentDeployStatus = "finish"
	AgentDeployStatusClose        AgentDeployStatus = "close"
	AgentDeployStatusCancelCanary AgentDeployStatus = "cancel_canary"
	AgentDeployStatusAuditReject  AgentDeployStatus = "audit_reject"
	AgentDeployStatusAuthFail     AgentDeployStatus = "auth_fail"
	AgentDeployStatusUnknown      AgentDeployStatus = "unknown"
)

func ParseAgentDeployStatus(status string) AgentDeployStatus {
	switch status {
	case "created":
		return AgentDeployStatusCreated
	case "canary":
		return AgentDeployStatusCanary
	case "finish":
		return AgentDeployStatusFinish
	case "close":
		return AgentDeployStatusClose
	case "cancel_canary":
		return AgentDeployStatusCancelCanary
	case "audit_reject":
		return AgentDeployStatusAuditReject
	case "auth_fail":
		return AgentDeployStatusAuthFail
	default:
		return AgentDeployStatusUnknown
	}
}

type AgentDeploy struct {
	ID                         string            `json:"id"`
	AgentConfigID              string            `json:"agent_config_id"`
	Actor                      string            `json:"actor"`
	AgentConfigVersionID       string            `json:"agent_config_version_id"`
	Version                    int               `json:"version"`
	Description                string            `json:"description"`
	SourceStatus               AgentConfigStatus `json:"source_status"`
	TargetStatus               AgentConfigStatus `json:"target_status"`
	CreatedAt                  time.Time         `json:"created_at"`
	UpdatedAt                  time.Time         `json:"updated_at"`
	WorkflowID                 int64             `json:"workflow_id"`
	Status                     AgentDeployStatus `json:"status"`
	ExtraInfo                  *DeployExtraInfo  `json:"extra_info"`
	AgentConfigVersionOnlineID string            `json:"agent_config_version_online_id"`
	OnlineVersion              int               `json:"online_version"`
	OnlineDescription          string            `json:"online_description"`
}

func (a *AgentDeploy) ToIDL() *nextidl.AgentDeploy {
	return &nextidl.AgentDeploy{
		ID:            a.ID,
		AgentConfigID: a.AgentConfigID,
		AgentConfigVersionInfo: &nextidl.AgentConfigVersionInfo{
			ID:          a.AgentConfigVersionID,
			Version:     int32(a.Version),
			Description: a.Description,
		},
		CreatedAt:  a.CreatedAt.Format(time.RFC3339),
		WorkflowID: a.WorkflowID,
		ExtraInfo:  a.ExtraInfo.ToIDL(),
		Status:     string(a.Status),
		Actor:      a.Actor,
		AgentConfigVersionOnlineInfo: lo.Ternary(a.AgentConfigVersionOnlineID != "", &nextidl.AgentConfigVersionInfo{
			ID:          a.AgentConfigVersionOnlineID,
			Version:     int32(a.OnlineVersion),
			Description: a.OnlineDescription,
		}, nil),
	}
}

type KnowledgesetConfig struct {
	Knowledgesets []*KnowledgesetMetadata `json:"knowledge_sets"`
}

func (k *KnowledgesetConfig) ToIDL() *nextidl.KnowledgesetConfig {
	return &nextidl.KnowledgesetConfig{
		Knowledgesets: lo.Map(k.Knowledgesets, func(item *KnowledgesetMetadata, index int) *nextidl.KnowledgesetMetadata {
			return item.ToIDL()
		}),
	}
}

type KnowledgesetMetadata struct {
	KnowledgesetID        string `json:"knowledge_set_id"`
	KnowledgesetVersionID string `json:"knowledge_set_version_id"`
	KnowledgesetType      string `json:"knowledge_set_type"`
}

func (k *KnowledgesetMetadata) ToIDL() *nextidl.KnowledgesetMetadata {
	return &nextidl.KnowledgesetMetadata{
		KnowledgesetID:        k.KnowledgesetID,
		KnowledgesetVersionID: k.KnowledgesetVersionID,
		KnowledgesetType:      k.KnowledgesetType,
	}
}

func ParseKnowledgesetConfigFromIDL(knowledgesetConfig *nextidl.KnowledgesetConfig) *KnowledgesetConfig {
	if knowledgesetConfig == nil {
		return nil
	}
	return &KnowledgesetConfig{
		Knowledgesets: lo.Map(knowledgesetConfig.Knowledgesets, func(item *nextidl.KnowledgesetMetadata, index int) *KnowledgesetMetadata {
			return &KnowledgesetMetadata{
				KnowledgesetID:        item.KnowledgesetID,
				KnowledgesetVersionID: item.KnowledgesetVersionID,
				KnowledgesetType:      item.KnowledgesetType,
			}
		}),
	}
}
