package entity

import (
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
)

const (
	LarkDocTypeDocx  = "docx"
	LarkDocTypeSheet = "sheet"
)

type LarkUser struct {
	Username            string
	Email               string
	OpenID              string
	AccessToken         string
	RefreshToken        string
	JSApiTicket         string
	AccessExpireAt      time.Time
	RefreshExpireAt     time.Time
	JSApiTicketExpireAt time.Time
	Scope               string
	AuthorizationDenied bool
}

type GroupItem struct {
	Avatar      string `json:"avatar"`
	ChatId      string `json:"chat_id"`
	ChatStatus  string `json:"chat_status"`
	Description string `json:"description"`
	External    bool   `json:"external"`
	Name        string `json:"name"`
	TenantKey   string `json:"tenant_key"`
}

type SearchGroupsResult struct {
	HasMore   bool        `json:"has_more"`
	Items     []GroupItem `json:"items"`
	PageToken string      `json:"page_token"`
}
type ArtifactLarkDocComment struct {
	ArtifactID string                         `json:"artifact_id"`
	Name       string                         `json:"name"`
	LarkDocUrl string                         `json:"lark_doc_url"`
	Comments   []*agententity.CommentMetadata `json:"comments"`
	Version    int32                          `json:"version"`
}
