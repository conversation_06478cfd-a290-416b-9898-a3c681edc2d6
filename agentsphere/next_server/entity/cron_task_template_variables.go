package entity

import (
	"fmt"
	"time"

	"github.com/samber/lo"
)

type CronTaskTemplateVariable struct {
	ID          string            `json:"id"`
	Key         string            `json:"key"`
	Value       TemplateFormValue `json:"value"`
	TemplateID  string            `json:"template_id"`
	Creator     string            `json:"creator"`
	SpaceID     string            `json:"space_id"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	CronTaskUID string            `json:"cron_task_uid"`
}

func (v TemplateFormValue) GetCronTaskTemplateFormValueUniqueKey(uid string) string {
	var key string
	for k, v := range v.Variables {
		key += fmt.Sprintf("%s_%s", k, v.GetCronTaskTemplateVariableValueUniqueKey(uid))
	}
	key += fmt.Sprintf("%s_%s", uid, key)
	return GetSHA256HashByContent(key)
}

func (v *TemplateVariableValue) GetCronTaskTemplateVariableValueUniqueKey(uid string) string {
	if v == nil {
		return ""
	}
	var key = lo.FromPtr(v.Content)
	for _, attachment := range v.Attachments {
		if attachment == nil {
			continue
		}
		key += fmt.Sprintf("%s_%s", attachment.ID, attachment.FileName)
	}
	key += fmt.Sprintf("%s_%s", uid, key)
	return GetSHA256HashByContent(key)
}
