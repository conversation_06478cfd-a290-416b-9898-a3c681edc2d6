package entity

import (
	"time"

	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"github.com/samber/lo"
)

// CronTaskEvent 定时任务的event信息
type CronTaskEvent struct {
	CronTaskUniqueID string `json:"cron_task_unique_id"`
	//UserDepartment   string `json:"user_department"`
}

type GroupInfo struct {
	GroupName   *string `json:"group_name"`
	GroupAvatar *string `json:"group_avatar"`
	GroupID     *string `json:"group_id"`
}

// CronTask 定时任务实体
type CronTask struct {
	ID              int64                             `json:"id"`
	CreatedAt       time.Time                         `json:"created_at"`
	UpdatedAt       time.Time                         `json:"updated_at"`
	UserName        string                            `json:"username"`
	UID             string                            `json:"uid"`
	Name            string                            `json:"name"`
	Description     string                            `json:"description,omitempty"`
	TaskType        TaskType                          `json:"task_type"`
	TemplateID      string                            `json:"template_id"`
	Schedule        string                            `json:"schedule"`
	ScheduleType    ScheduleType                      `json:"schedule_type"`
	Timezone        string                            `json:"timezone"`
	NextExecuteTime time.Time                         `json:"next_execute_time"`
	NextMQEventID   string                            `json:"next_mq_event_id,omitempty"`
	LastExecuteTime time.Time                         `json:"last_execute_time,omitempty"`
	LastRunStatus   LastRunStatus                     `json:"last_run_status"`
	LastRunErrMsg   string                            `json:"last_run_err_msg,omitempty"`
	TaskStatus      CronTaskStatus                    `json:"task_status"`
	GroupInfo       GroupInfo                         `json:"group_info"`
	Role            SessionRole                       `json:"role"`
	TriggerType     TriggerType                       `json:"trigger_type"`
	SpaceID         string                            `json:"space_id"`
	Content         string                            `json:"content"`
	Option          string                            `json:"option"`
	WeekDay         WeekDay                           `json:"week_day"`
	FormValue       map[string]*TemplateVariableValue `json:"form_value"`
	EnableEdit      bool                              `json:"enable_edit"`
}

func (c *CronTask) ToIDL() *nextidl.CronTask {
	if c == nil {
		return nil
	}

	variables := make(map[string]*nextidl.TemplateVariableValue)
	for k, v := range c.FormValue {
		variables[k] = &nextidl.TemplateVariableValue{
			Content: v.Content,
			Attachments: lo.Map(v.Attachments, func(item *Attachment, _ int) *nextidl.AttachmentRequired {
				if item == nil {
					return nil
				}
				return &nextidl.AttachmentRequired{
					ID:       item.ID,
					FileName: item.FileName,
				}
			}),
		}
	}

	return &nextidl.CronTask{
		ID:            lo.ToPtr(c.ID),
		CreatedAt:     c.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     c.UpdatedAt.Format(time.RFC3339),
		Username:      lo.ToPtr(c.UserName),
		UID:           lo.ToPtr(c.UID),
		Name:          c.Name,
		Description:   lo.ToPtr(c.Description),
		TaskType:      c.TaskTypeToIDL(),
		TemplateID:    c.TemplateID,
		Schedule:      lo.ToPtr(c.Schedule),
		Timezone:      c.Timezone,
		LastRunStatus: lo.ToPtr(c.LastRunStatusToIDL()),
		TaskStatus:    c.CronTaskStatusToIDL(),
		GroupInfo:     lo.ToPtr(convertGroupInfoToIDL(c.GroupInfo)),
		Role:          c.SessionRoleToIDL(),
		ScheduleType:  c.ScheduleTypeToIDL(),
		WeekDay:       lo.ToPtr(c.WeekDayToIDL()),
		SpaceID:       lo.ToPtr(c.SpaceID),
		FormValue:     &nextidl.TemplateFormValue{Variables: variables},
		LastRunErrMsg: lo.ToPtr(c.LastRunErrMsg),
		EnableEdit:    lo.ToPtr(c.EnableEdit),
	}
}

func convertGroupInfoToIDL(g GroupInfo) nextidl.GroupInfo {
	return nextidl.GroupInfo{
		GroupName:   g.GroupName,
		GroupAvatar: g.GroupAvatar,
		GroupID:     g.GroupID,
	}
}

type TaskType int

const (
	TaskTypeUnknown TaskType = iota
	TaskTypeAimeBot
	TaskTypeNewGroup
	TaskTypeBindGroup
)

func (c *CronTask) TaskTypeToIDL() nextidl.TaskType {
	if c == nil {
		return nextidl.TaskType_Unknown
	}
	switch c.TaskType {
	case TaskTypeAimeBot:
		return nextidl.TaskType_AimeBot
	case TaskTypeNewGroup:
		return nextidl.TaskType_NewGroup
	case TaskTypeBindGroup:
		return nextidl.TaskType_BindGroup
	default:
		return nextidl.TaskType_Unknown
	}
}

func TaskTypeFromIDL(taskType nextidl.TaskType) TaskType {
	if taskType == 0 {
		return TaskTypeUnknown
	}
	switch taskType {
	case nextidl.TaskType_AimeBot:
		return TaskTypeAimeBot
	case nextidl.TaskType_NewGroup:
		return TaskTypeNewGroup
	case nextidl.TaskType_BindGroup:
		return TaskTypeBindGroup
	default:
		return TaskTypeUnknown
	}
}

type LastRunStatus int

const (
	LastRunStatusUnknown LastRunStatus = iota
	LastRunStatusSuccess
	LastRunStatusFailed
	LastRunStatusException
)

func (c *CronTask) LastRunStatusToIDL() nextidl.LastRunStatus {
	if c == nil {
		return nextidl.LastRunStatus_Unknown
	}
	switch c.LastRunStatus {
	case LastRunStatusSuccess:
		return nextidl.LastRunStatus_Success
	case LastRunStatusFailed:
		return nextidl.LastRunStatus_Failed
	case LastRunStatusException:
		return nextidl.LastRunStatus_Exception
	default:
		return nextidl.LastRunStatus_Unknown
	}
}

type CronTaskStatus int

const (
	CronTaskStatusUnknown CronTaskStatus = iota
	CronTaskStatusRunning
	CronTaskStatusStopped
)

func (c *CronTask) CronTaskStatusToIDL() nextidl.TaskStatus {
	if c == nil {
		return nextidl.TaskStatus_Unknown
	}
	switch c.TaskStatus {
	case CronTaskStatusRunning:
		return nextidl.TaskStatus_Running
	case CronTaskStatusStopped:
		return nextidl.TaskStatus_Stopped
	default:
		return nextidl.TaskStatus_Unknown
	}
}

func CronTaskStatusFromIDL(taskStatus nextidl.TaskStatus) CronTaskStatus {
	if taskStatus == 0 {
		return CronTaskStatusUnknown
	}
	switch taskStatus {
	case nextidl.TaskStatus_Running:
		return CronTaskStatusRunning
	case nextidl.TaskStatus_Stopped:
		return CronTaskStatusStopped
	default:
		return CronTaskStatusUnknown
	}
}

func (c *CronTask) SessionRoleToIDL() nextidl.SessionRole {
	if c == nil {
		return nextidl.SessionRole_Unknown
	}
	switch c.Role {
	case SessionRoleYoungTalent:
		return nextidl.SessionRole_YoungTalent
	case SessionRoleLateralHire:
		return nextidl.SessionRole_LateralHire
	case SessionRoleIndustryVeteran:
		return nextidl.SessionRole_IndustryVeteran
	default:
		return nextidl.SessionRole_Unknown
	}
}

func (c *CronTask) TriggerTypeToIDL() nextidl.TriggerType {
	if c == nil {
		return nextidl.TriggerType_Unknown
	}
	switch c.TriggerType {
	case TriggerTypeManual:
		return nextidl.TriggerType_Manual
	case TriggerTypeCronJob:
		return nextidl.TriggerType_CronJob
	case TriggerTypeDBCompensation:
		return nextidl.TriggerType_DBCompensation
	default:
		return nextidl.TriggerType_Unknown
	}
}

type ScheduleType int

const (
	ScheduleTypeUnknown ScheduleType = iota
	ScheduleTypeByDay
	ScheduleTypeByWeek
)

func (c *CronTask) ScheduleTypeToIDL() nextidl.ScheduleType {
	if c == nil {
		return nextidl.ScheduleType_Unknown
	}
	switch c.ScheduleType {
	case ScheduleTypeByDay:
		return nextidl.ScheduleType_ScheduleTypeByDay
	case ScheduleTypeByWeek:
		return nextidl.ScheduleType_ScheduleTypeByWeek
	default:
		return nextidl.ScheduleType_Unknown
	}
}

func ScheduleTypeFromIDL(scheduleType nextidl.ScheduleType) ScheduleType {
	if scheduleType == 0 {
		return ScheduleTypeUnknown
	}
	switch scheduleType {
	case nextidl.ScheduleType_ScheduleTypeByDay:
		return ScheduleTypeByDay
	case nextidl.ScheduleType_ScheduleTypeByWeek:
		return ScheduleTypeByWeek
	default:
		return ScheduleTypeUnknown
	}
}

type WeekDay int

const (
	WeekDayUnknown WeekDay = iota
	WeekDayMonday
	WeekDayTuesday
	WeekDayWednesday
	WeekDayThursday
	WeekDayFriday
	WeekDaySaturday
	WeekDaySunday
)

func (c *CronTask) WeekDayToIDL() nextidl.WeekDay {
	if c == nil {
		return nextidl.WeekDay_Unknown
	}
	switch c.WeekDay {
	case WeekDayMonday:
		return nextidl.WeekDay_Monday
	case WeekDayTuesday:
		return nextidl.WeekDay_Tuesday
	case WeekDayWednesday:
		return nextidl.WeekDay_Wednesday
	case WeekDayThursday:
		return nextidl.WeekDay_Thursday
	case WeekDayFriday:
		return nextidl.WeekDay_Friday
	case WeekDaySaturday:
		return nextidl.WeekDay_Saturday
	case WeekDaySunday:
		return nextidl.WeekDay_Sunday
	default:
		return nextidl.WeekDay_Unknown
	}
}

func WeekDayFromIDL(weekDay nextidl.WeekDay) WeekDay {
	if weekDay == 0 {
		return WeekDayUnknown
	}
	switch weekDay {
	case nextidl.WeekDay_Monday:
		return WeekDayMonday
	case nextidl.WeekDay_Tuesday:
		return WeekDayTuesday
	case nextidl.WeekDay_Wednesday:
		return WeekDayWednesday
	case nextidl.WeekDay_Thursday:
		return WeekDayThursday
	case nextidl.WeekDay_Friday:
		return WeekDayFriday
	case nextidl.WeekDay_Saturday:
		return WeekDaySaturday
	case nextidl.WeekDay_Sunday:
		return WeekDaySunday
	default:
		return WeekDayUnknown
	}
}
