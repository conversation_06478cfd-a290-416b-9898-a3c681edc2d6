package entity

import (
	"path/filepath"
	"strings"
	"time"

	"github.com/samber/lo"

	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

type Event struct {
	ID          string
	SessionID   string
	TaskID      string
	EventName   string
	EventData   EventData
	EventOffset int64
	EventKey    string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

// ShouldFilter 判断在传给前端时是否需要过滤掉该事件
func (item *Event) ShouldFilter(isLastEvent, isDeveloper bool) bool {
	// 过滤掉中间状态的 error 事件和 session complete 事件
	if !isLastEvent && (item.EventName == nextidl.EventNameError || item.EventName == nextidl.EventNameSessionCompleted) {
		return true
	}
	// 过滤掉含有 lark md 文件的事件，但对内部研发放开权限
	if item.EventName == nextidl.EventNameUseTool && !isDeveloper {
		s := conv.DefaultAny[map[string]any](item.EventData.Data)
		e, _ := conv.MapToStructByJSONTag[*nextidl.UseToolEvent](s)
		if e == nil {
			return false
		}
		if e.TextEditor != nil && e.TextEditor.CreateFile != nil && IsLarkMDFile(e.TextEditor.CreateFile.FilePath) {
			return true
		}
		if e.TextEditor != nil && e.TextEditor.PatchFile != nil && IsLarkMDFile(e.TextEditor.PatchFile.FilePath) {
			return true
		}
		if e.TextEditor != nil && e.TextEditor.AppendFile != nil && IsLarkMDFile(e.TextEditor.AppendFile.FilePath) {
			return true
		}
	}
	return false
}

type PartialEvent struct {
	EventName   string
	EventOffset int64
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type EventData struct {
	Event string `json:"event"`
	Data  any    `json:"data"`
}

type Attachment struct {
	ID            string   `thrift:"ID,1,required" json:"id"`
	FileName      string   `thrift:"FileName,1,required" json:"file_name"`
	Path          string   `thrift:"Path,2,required" json:"path"`
	Type          string   `thrift:"Type,3,required" json:"type"`
	URL           string   `thrift:"URL,4,required" json:"url"`
	ContentType   string   `thrift:"ContentType,5,required" json:"content_type"`
	SubType       string   `thrift:"SubType,6,required" json:"sub_type"` // 子类型，如果是 Code 对应不同的语言类型，其他则是文件名后缀；如果是 Link，则对应的是 link 类型（lark_doc/deployment）
	ContentLength int64    `thrift:"ContentLength,5,required" json:"content_length"`
	ParentStepIDs []string `thrift:"ParentStepIDs,7,optional" json:"parent_step_ids,omitempty"`
	LarkToken     string   `json:"lark_token,omitempty"`
	Version       int      `json:"version,omitempty"`
}

func (a *Attachment) CanPreview() bool {
	return IsMarkdownFile(a.Path) || a.ContentType == string(ArtifactTypeLink) ||
		lo.Contains([]string{
			string(ArtifactTypeLink),
			string(LinkArtifactKeySourceDeployment),
			string(LinkArtifactKeySourceLarkDoc),
			string(LinkArtifactKeySourceLarkSheet),
		}, a.SubType) ||
		a.Type == string(ArtifactTypeProject)
}

type PreviewPriority int

const (
	PriorityDeployment PreviewPriority = iota + 1
	PriorityLarkDoc
	PriorityLarkSheet
	PriorityProject
	PriorityOthers
)

// previewPriorityMap 定义不同类型附件的优先级
var previewPriorityMap = map[string]PreviewPriority{
	LinkArtifactKeySourceDeployment.String(): PriorityDeployment,
	LinkArtifactKeySourceLarkDoc.String():    PriorityLarkDoc,
	LinkArtifactKeySourceLarkSheet.String():  PriorityLarkSheet,
	ProjectArtifactKeySourceProject.String(): PriorityProject,
}

// GetPreviewAttachmentID 判断是否有可以预览的附件，如果有则返回优先级最高的附件ID，优先级：deployment > lark_doc -> lark_sheet > project > others
func GetPreviewAttachmentID(attachments []*Attachment) string {
	var (
		highestID       string
		highestPriority PreviewPriority
	)

	// 依次遍历附件，找到优先级最高的附件
	for _, a := range attachments {
		if !a.CanPreview() {
			continue
		}

		priority := PriorityOthers
		if p, exists := previewPriorityMap[a.SubType]; exists {
			priority = p
		}

		// 如果是第一个附件或优先级更高，则更新
		if highestID == "" || priority < highestPriority {
			highestID, highestPriority = a.ID, priority
		}
	}

	return highestID
}

// IsMarkdownFile 根据后缀判断是否是markdown类型文件
func IsMarkdownFile(path string) bool {
	ext := strings.TrimPrefix(filepath.Ext(path), ".")
	return strings.ToLower(ext) == "md"
}

func IsLarkMDFile(path string) bool {
	return strings.HasSuffix(strings.ToLower(path), ".lark.md")
}

// ShouldFilterFile 判断是否是需要过滤的文件，后续产物和附件的过滤逻辑可以在这里添加
func ShouldFilterFile(path string) bool {
	return strings.HasSuffix(strings.ToLower(path), ".lark.md")
}

func (d EventData) ToEvent() *Event {
	switch e := d.Data.(type) {
	case *nextidl.MessageCreateEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.Message.SessionID,
			TaskID:    e.Message.TaskID,
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.ProgressNoticeEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.StepUpdateEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.PlanUpdateEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.UseToolEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.SessionCompletedEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.ToolCallRequiredEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.ErrorEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.ReferenceEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	case *nextidl.ToolCallConfirmedEvent:
		return &Event{
			ID:        e.EventID,
			SessionID: e.SessionID,
			TaskID:    "", // TODO: task id
			EventName: d.Event,
			EventData: EventData{
				Event: d.Event,
				Data:  e,
			},
			EventOffset: e.EventOffset,
			EventKey:    e.EventKey,
		}
	}
	return nil
}
