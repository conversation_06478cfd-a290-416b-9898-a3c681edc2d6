package entity

import (
	"time"

	"github.com/AlekSi/pointer"
	"github.com/samber/lo"

	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

type Session struct {
	ID                  string
	Title               string
	Status              SessionStatus
	Creator             string
	Context             SessionContext
	RuntimeMetaData     SessionRuntimeMetadata
	Role                *SessionRole
	TemplateID          string
	SourceSpaceID       string
	StartedAt           time.Time // session 开始时间
	LastMessageAt       time.Time // session 最后一条用户输入消息时间
	CreatedAt           time.Time
	UpdatedAt           time.Time
	CanResume           bool
	CanNotResumeReason  SessionCanNotResumeReason
	LatestAgentResumeAt *time.Time
	Scope               SessionScope
	DeletedAt           *time.Time
	// 以下字段需要单独计算
	PermissionActions []PermissionAction
	Starred           bool
	Source            SessionSource
}

type SessionScope int

const (
	SessionScopeUnknown       SessionScope = iota
	SessionScopePrivate                    // 个人
	SessionScopePublic                     // 公司内公开
	SessionScopeProjectPublic              // 项目内公开
)

func (s SessionScope) ToIDL() *nextidl.SessionScope {
	switch s {
	case SessionScopePrivate:
		return lo.ToPtr(nextidl.SessionScope_Private)
	case SessionScopePublic:
		return lo.ToPtr(nextidl.SessionScope_Public)
	case SessionScopeProjectPublic:
		return lo.ToPtr(nextidl.SessionScope_ProjectPublic)
	default:
		return lo.ToPtr(nextidl.SessionScope_Unknown)
	}
}

type SessionCanNotResumeReason int

const (
	SessionCanNotResumeReasonNotAllowed SessionCanNotResumeReason = 0
	SessionCanNotResumeReasonExpired    SessionCanNotResumeReason = 1
	SessionCanNotResumeReasonDeleted    SessionCanNotResumeReason = 2
)

func (s SessionCanNotResumeReason) ToIDL() nextidl.CanNotResumeReason {
	switch s {
	case SessionCanNotResumeReasonNotAllowed:
		return nextidl.CanNotResumeReasonNotAllowed
	case SessionCanNotResumeReasonExpired:
		return nextidl.CanNotResumeReasonExpired
	case SessionCanNotResumeReasonDeleted:
		return nextidl.CanNotResumeReasonDeleted
	default:
		return nextidl.CanNotResumeReasonUnknown
	}
}

func (s SessionCanNotResumeReason) String() string {
	switch s {
	case SessionCanNotResumeReasonNotAllowed:
		return "not_allowed"
	case SessionCanNotResumeReasonExpired:
		return "expired"
	case SessionCanNotResumeReasonDeleted:
		return "deleted"
	default:
		return "unknown"
	}
}

type SessionPartial struct {
	ID     string
	Title  string
	Status SessionStatus
}

type SessionStatus string

// 消息卡片展示使用
//func (s SessionStatus) String() string {
//	switch s {
//	case SessionStatusCreated:
//		return "执行中"
//	case SessionStatusRunning:
//		return "执行中"
//	case SessionStatusWaiting:
//		return "会话等待中，需要用户交互"
//	case SessionStatusCanceled:
//		return "已取消"
//	case SessionStatusIdle:
//		return "执行完成"
//	case SessionStatusStopped:
//		return "执行完成"
//	case SessionStatusAbnormal:
//		return "会话异常"
//	case SessionStatusClosed:
//		return "会话已结束"
//	case SessionStatusError:
//		return "会话异常"
//	case SessionStatusPending:
//		return "执行中"
//	case SessionStatusBound:
//		return "执行中"
//	case SessionStatusPrepared:
//		return "执行中"
//	default:
//		return "执行中"
//	}
//}

const (
	// 会话中间状态
	SessionStatusCreated  SessionStatus = "created"  // 会话已创建
	SessionStatusRunning  SessionStatus = "running"  // 会话运行中
	SessionStatusWaiting  SessionStatus = "waiting"  // 会话等待中，需要用户交互
	SessionStatusCanceled SessionStatus = "canceled" // 会话已取消，用户主动取消
	SessionStatusIdle     SessionStatus = "idle"     // 会话空闲，当前轮任务完成
	SessionStatusStopped  SessionStatus = "stopped"  // 会话已休眠
	SessionStatusAbnormal SessionStatus = "abnormal" // 会话异常，可重试

	// 会话终态，不允许再发送消息或唤醒
	SessionStatusClosed SessionStatus = "closed" // 会话已关闭，终态
	SessionStatusError  SessionStatus = "error"  // 会话异常， 终态

	// 容器预热状态
	SessionStatusPending  SessionStatus = "pending"  // 创建的资源池，容器未初始化
	SessionStatusBound    SessionStatus = "bound"    // 容器完成绑定
	SessionStatusPrepared SessionStatus = "prepared" // 容器完成初始化
)

func (s SessionStatus) IsStopped() bool {
	return s == SessionStatusStopped
}

func (s SessionStatus) IsError() bool {
	return s == SessionStatusError
}

// IsTaskRunning 判断任务是否正在运行中，这里先用 session 的状态来判断，后面可以用 task 的状态来判断
func (s SessionStatus) IsTaskRunning() bool {
	return s == SessionStatusCreated || s == SessionStatusRunning
}

// IsFinal 判断会话是否是终态
func (s SessionStatus) IsFinal() bool {
	return s == SessionStatusClosed || s == SessionStatusError
}

// IsSessionRunning
func (s SessionStatus) IsSessionRunning() bool {
	return lo.Contains([]SessionStatus{SessionStatusCreated, SessionStatusRunning, SessionStatusWaiting, SessionStatusCanceled, SessionStatusIdle}, s)
}

func (s SessionStatus) ToIDL() nextidl.SessionStatus {
	switch s {
	case SessionStatusCreated:
		return nextidl.SessionStatusCreated
	case SessionStatusRunning:
		return nextidl.SessionStatusRunning
	case SessionStatusCanceled:
		return nextidl.SessionStatusCanceled
	case SessionStatusStopped:
		return nextidl.SessionStatusStopped
	case SessionStatusIdle:
		return nextidl.SessionStatusIdle
	case SessionStatusWaiting:
		return nextidl.SessionStatusWaiting
	case SessionStatusError:
		return nextidl.SessionStatusError
	default:
		return nextidl.SessionStatusError
	}
}

func SessionStatusFromIDL(status *nextidl.SessionStatus) *SessionStatus {
	if status == nil {
		return nil
	}
	switch *status {
	case nextidl.SessionStatusCreated:
		return lo.ToPtr(SessionStatusCreated)
	case nextidl.SessionStatusRunning:
		return lo.ToPtr(SessionStatusRunning)
	case nextidl.SessionStatusCanceled:
		return lo.ToPtr(SessionStatusCanceled)
	case nextidl.SessionStatusStopped:
		return lo.ToPtr(SessionStatusStopped)
	case nextidl.SessionStatusIdle:
		return lo.ToPtr(SessionStatusIdle)
	case nextidl.SessionStatusWaiting:
		return lo.ToPtr(SessionStatusWaiting)
	case nextidl.SessionStatusError:
		return lo.ToPtr(SessionStatusError)
	default:
		return nil
	}
}

type SessionRole int

const (
	SessionRoleUnknown SessionRole = iota
	SessionRoleYoungTalent
	SessionRoleLateralHire
	SessionRoleIndustryVeteran
)

func ParseSessionRole(s string) SessionRole {
	switch s {
	case "invited_expert":
		return SessionRoleIndustryVeteran
	case "newbie":
		return SessionRoleLateralHire
	case "intern":
		return SessionRoleYoungTalent
	default:
		return SessionRoleUnknown
	}
}

func (s *SessionRole) ToIDL() *nextidl.SessionRole {
	if s == nil {
		return nil
	}
	switch *s {
	case SessionRoleYoungTalent:
		return pointer.To(nextidl.SessionRole_YoungTalent)
	case SessionRoleLateralHire:
		return pointer.To(nextidl.SessionRole_LateralHire)
	case SessionRoleIndustryVeteran:
		return pointer.To(nextidl.SessionRole_IndustryVeteran)
	default:
		return pointer.To(nextidl.SessionRole_Unknown)
	}
}

// 消息卡片展示使用
func (s *SessionRole) String() string {
	if s == nil {
		return ""
	}
	switch *s {
	case SessionRoleYoungTalent:
		return "小帅"
	case SessionRoleLateralHire:
		return "小美"
	case SessionRoleIndustryVeteran:
		return "大卫"
	default:
		return ""
	}
}

func SessionRoleFromIDL(role *nextidl.SessionRole) *SessionRole {
	if role == nil {
		return nil
	}
	switch *role {
	case nextidl.SessionRole_YoungTalent:
		return pointer.To(SessionRoleYoungTalent)
	case nextidl.SessionRole_LateralHire:
		return pointer.To(SessionRoleLateralHire)
	case nextidl.SessionRole_IndustryVeteran:
		return pointer.To(SessionRoleIndustryVeteran)
	default:
		return pointer.To(SessionRoleUnknown)
	}
}

type ListSessionFilterType int

const (
	SessionListFilterTypeSpacePublic ListSessionFilterType = iota
	SessionListFilterTypeSelfCreated
)

func SessionListFilterFromIDL(filter *nextidl.ListFilterType) *ListSessionFilterType {
	if filter == nil {
		return nil
	}
	switch *filter {
	case nextidl.ListFilterType_SpacePublic:
		return pointer.To(SessionListFilterTypeSpacePublic)
	case nextidl.ListFilterType_SelfCreated:
		return pointer.To(SessionListFilterTypeSelfCreated)
	default:
		return nil
	}
}

type SessionContext struct {
	UseInternalTool *bool  `json:"use_internal_tool,omitempty"`
	MCPs            []*MCP `json:"mcps,omitempty"`
}

type SessionRuntimeMetadata struct {
	Error                string         `json:"error"`
	AgentConfigID        string         `json:"agent_config_id"`
	AgentConfigName      string         `json:"agent_config_name"`
	AgentConfigVersion   int            `json:"agent_config_version"`
	AgentConfigVersionID string         `json:"agent_config_version_id"`
	LogID                string         `json:"log_id"`
	ABParams             map[string]any `json:"ab_params"`
}

type NotAllowedType int

const (
	NotAllowedTypeReachedMaximum NotAllowedType = iota + 1
	NotAllowedTypeUseInternalTool
)

type CreateSessionAllowed struct {
	Role           SessionRole
	Allowed        bool
	Type           NotAllowedType
	RemainingTimes *int32
}

type CanCreateSessionResult struct {
	Allowed bool
	Results []CreateSessionAllowed
}

// TemplateFormValue 存储一个模板表单的提交结果
type TemplateFormValue struct {
	Variables map[string]*TemplateVariableValue `json:"variables,omitempty"`
}

type TemplateVariableValue struct {
	Content     *string       `json:"content,omitempty"`
	Attachments []*Attachment `json:"attachments,omitempty"`
}

type AgentRuntimeMeta struct {
	RuntimeProvider string `json:"runtime_provider"`
	ContainerID     string `json:"container_id"`
	ContainerHost   string `json:"container_host"`
	WildcardDomain  string `json:"wildcard_domain"`
}

type SessionStar struct {
	ID        string    `json:"id"`
	SessionID string    `json:"session_id"`
	Username  string    `json:"username"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
type SessionTab string

const (
	SessionTabAll       SessionTab = "all"
	SessionTabStarred   SessionTab = "starred"
	SessionTabUnStarred SessionTab = "unstarred"
)

type SessionSource int

const (
	SessionSourceUnknown SessionSource = iota
	SessionSourcePersonal
	SessionSourceCronJob
	SessionSourceCodebase
	SessionSourceSlardar
)

func ParseSessionSourceFromIDL(source *nextidl.SessionSource) SessionSource {
	if source == nil {
		return SessionSourceUnknown
	}
	switch *source {
	case nextidl.SessionSourcePersonal:
		return SessionSourcePersonal
	case nextidl.SessionSourceCronjob:
		return SessionSourceCronJob
	case nextidl.SessionSourceCodebase:
		return SessionSourceCodebase
	case nextidl.SessionSourceSlardar:
		return SessionSourceSlardar
	default:
		return SessionSourceUnknown
	}
}

func (s *SessionSource) ToIDL() nextidl.SessionSource {
	if s == nil {
		return nextidl.SessionSourcePersonal
	}
	switch *s {
	case SessionSourceUnknown:
		return nextidl.SessionSourceUnknown
	case SessionSourcePersonal:
		return nextidl.SessionSourcePersonal
	case SessionSourceCronJob:
		return nextidl.SessionSourceCronjob
	case SessionSourceCodebase:
		return nextidl.SessionSourceCodebase
	default:
		return nextidl.SessionSourceUnknown
	}
}
