package entity

import (
	"encoding/json"
	"time"
)

type WebhookRecord struct {
	ID                   string
	TaskID               string
	SessionID            string
	URL                  string
	RequestHeaders       string
	RequestData          string
	ResponseHeaders      string
	ResponseBody         string
	ResponseStatus       string
	ExecutionDuration    float64
	InternalErrorMessage string
	CreatedAt            time.Time
	UpdatedAt            time.Time
}

type WebhookEvent struct {
	EventType string          `json:"event_type"`
	Session   Session         `json:"session"`
	Data      json.RawMessage `json:"data"`
}

type WebhookAttachment struct {
	ID      string       `json:"id"`
	Length  int64        `json:"length"`
	Type    ArtifactType `json:"type"`
	Path    string       `json:"path"`
	SubType string       `json:"sub_type"`
	URL     string       `json:"url"`
	Version int          `json:"version"`
}

type FirstRoundCompleteEventData struct {
	Content     string              `json:"content"`
	Attachments []WebhookAttachment `json:"attachment"`
	CreatedAt   time.Time           `json:"created_at"`
}

type FirstRoundFailedEventData struct {
	Content string `json:"content"`
}
