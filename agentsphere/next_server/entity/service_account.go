package entity

import (
	"time"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

type ServiceAccount struct {
	ID            string
	Name          string
	Description   string
	WebHookConfig WebhookConfig
	AllowPSMs     AllowPSMs
	AllowSudo     bool
	Owner         string
	Enabled       bool
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

func (s *ServiceAccount) GetSessionSource() nextagent.SessionSource {
	if s == nil {
		return nextagent.SessionSourceUnknown
	}
	switch s.Name {
	case "aime_codebase":
		return nextagent.SessionSourceCodebase
	case "slardar":
		return nextagent.SessionSourceSlardar
	default:
		return nextagent.SessionSourcePersonal
	}
}

func (s *ServiceAccount) GetSessionSourceEntity() SessionSource {
	if s == nil {
		return SessionSourceUnknown
	}
	switch s.Name {
	case "aime_codebase":
		return SessionSourceCodebase
	case "slardar":
		return SessionSourceSlardar
	default:
		return SessionSourcePersonal
	}
}

func (s *ServiceAccount) GetTriggerTypeEntity() TriggerType {
	if s == nil {
		return TriggerTypeUnknown
	}
	switch s.Name {
	case "aime_codebase":
		return TriggerTypeCodebase
	case "slardar":
		return TriggerTypeSlardar
	default:
		return TriggerTypeManual
	}
}

type AllowPSMs struct {
	PSMs []string `json:"psms,omitempty"`
}

type EventType string

const (
	EventTypeSessionFirstRoundComplete EventType = "session.first_round.complete"
	EventTypeSessionFirstRoundFailed   EventType = "session.first_round.failed"
)

type WebhookConfig struct {
	URL        string      `json:"url,omitempty"`
	Secret     string      `json:"secret,omitempty"`      // 默认添加到 x-aime-token 的 header 里面
	EventTypes []EventType `json:"event_types,omitempty"` // 监听的事件类型，不填则监听所有的事件
}
