package entity

import (
	"context"
	"encoding/json"
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/gopkg/logs/v2/log"
)

type Message struct {
	ID          string
	SessionID   string
	TaskID      string
	Role        MessageRole
	Content     MessageContent
	Creator     string
	Attachments []*Attachment
	Options     MessageOptions
	Status      MessageStatus
	Mentions    []*agententity.Mention
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type MessageContent struct {
	Content   string      `json:"content"`
	ToolCalls []*ToolCall `json:"tool_calls,omitempty"`
}

type MessageRole string

type MessageOptions struct {
	Locale string `json:"locale"`
}

func ParseIDLToMessageOptionsString(ctx context.Context, optional *nextagent.MessageOptional) string {
	if optional == nil {
		return `{"locale":"zh"}`
	}
	messageOptions := &MessageOptions{
		Locale: optional.Locale,
	}
	result, err := json.Marshal(messageOptions)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed marshal message options")
		return `{"locale":"zh"}`
	}
	return string(result)
}

const (
	MessageRoleUser      MessageRole = "user"
	MessageRoleAssistant MessageRole = "assistant"
)

type MessageStatus int

const (
	MessageStatusSent MessageStatus = 0
	MessageStatusWait MessageStatus = 1
)
