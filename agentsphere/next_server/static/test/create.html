<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试环境 - 测试平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 60px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .card h2 {
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 1.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .card h2::before {
            content: '';
            width: 4px;
            height: 28px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .card p {
            color: #718096;
            margin-bottom: 40px;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .btn {
            padding: 16px 32px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .error-message {
            background: #fed7d7;
            color: #742a2a;
            padding: 16px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #e53e3e;
            text-align: left;
        }

        .success-message {
            background: #c6f6d5;
            color: #22543d;
            padding: 16px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #38a169;
            text-align: left;
        }

        .info-box {
            background: #ebf8ff;
            border: 1px solid #bee3f8;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
            text-align: left;
        }

        .info-box h3 {
            color: #2a4365;
            margin-bottom: 12px;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .info-box p {
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .info-box .highlight {
            background: #e6fffa;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            color: #234e52;
            border: 1px solid #b2f5ea;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .card {
                padding: 40px 30px;
            }

            .btn {
                padding: 14px 28px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>🚀 测试平台</h1>
        <p>创建新的测试环境</p>
    </div>

    <div class="main-content">
        <div class="card">
            <h2>创建测试环境</h2>
            <p>点击下方按钮创建一个全新的测试环境，系统将为您分配独立的容器和会话ID。</p>
            <p>测试环境保留 12 小时。</p>
            
            <button class="btn" onclick="createTestEnvironment()" id="create-btn">
                <span>🔧 创建测试环境</span>
            </button>

            <div id="message-container"></div>

            <div class="info-box">
                <h3>📋 环境信息</h3>
                <p>创建成功后，您将获得：</p>
                <p>• <strong>会话ID</strong>：用于标识您的测试会话</p>
                <p>• <strong>容器ID</strong>：独立的测试容器环境</p>
                <p>• <strong>专属空间</strong>：完全隔离的测试环境</p>
            </div>
        </div>
    </div>
</div>

<script>
    // API配置
    const API_BASE = '/api/agents/v2/testing';

    // 显示消息
    function showMessage(message, type = 'info') {
        const container = document.getElementById('message-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
        messageDiv.textContent = message;

        container.appendChild(messageDiv);

        // 清除之前的消息
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }

    // 检查环境是否就绪
    async function checkEnvironmentReady(sessionId, maxAttempts = 20, interval = 500) {
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                const response = await fetch(`${API_BASE}/cases?session_id=${encodeURIComponent(sessionId)}`);
                if (response.ok) {
                    const data = await response.json();
                    // 如果接口返回成功且有数据，说明环境已就绪
                    if (data.cases !== undefined) {
                        return true;
                    }
                }
            } catch (error) {
                console.log(`环境检查尝试 ${attempt}/${maxAttempts} 失败:`, error.message);
            }
            
            // 如果不是最后一次尝试，等待后继续
            if (attempt < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, interval));
            }
        }
        return false;
    }

    // 创建测试环境
    async function createTestEnvironment() {
        const createBtn = document.getElementById('create-btn');
        const originalText = createBtn.innerHTML;
        
        // 显示创建状态
        createBtn.innerHTML = '<span class="loading"></span> 创建中...';
        createBtn.disabled = true;

        try {
            // 第一步：创建环境
            const response = await fetch(API_BASE, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.session_id || !data.container_id) {
                throw new Error('返回数据格式不正确');
            }

            // 第二步：检查环境是否就绪
            createBtn.innerHTML = '<span class="loading"></span> 环境启动中...';
            showMessage(`测试环境创建成功！会话ID: ${data.session_id}，正在检查环境就绪状态...`, 'success');

            const isReady = await checkEnvironmentReady(data.session_id);
            
            if (isReady) {
                createBtn.innerHTML = '<span class="loading"></span> 跳转中...';
                showMessage('环境已就绪，正在跳转...', 'success');
                
                // 延迟跳转，让用户看到成功消息
                setTimeout(() => {
                    window.location.href = `/api/agents/v2/test/${data.session_id}`;
                }, 1000);
            } else {
                throw new Error('环境启动超时，请稍后重试或联系管理员');
            }

        } catch (error) {
            console.error('创建测试环境失败:', error);
            showMessage('创建测试环境失败: ' + error.message, 'error');
            
            // 恢复按钮状态
            createBtn.innerHTML = originalText;
            createBtn.disabled = false;
        }
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function () {
        console.log('测试环境创建页面已加载');
    });
</script>
</body>
</html> 