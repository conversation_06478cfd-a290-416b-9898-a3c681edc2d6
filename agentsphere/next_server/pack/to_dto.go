package pack

import (
	"path/filepath"
	"time"

	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"github.com/samber/lo"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
)

// ConvertTemplateEntityToDTO 将模板实体转换为DTO
func ConvertTemplateEntityToDTO(template *entity.TemplateVersion, packDetail bool) *nextagent.Template {
	if template == nil {
		return nil
	}
	var scope nextagent.TemplateScope
	switch template.Scope {
	case entity.TemplateScopePrivate:
		scope = nextagent.TemplateScope_Private
	case entity.TemplateScopePublic:
		scope = nextagent.TemplateScope_Public
	case entity.TemplateScopeShared:
		scope = nextagent.TemplateScope_Shared
	case entity.TemplateScopeOfficial:
		scope = nextagent.TemplateScope_Official
	case entity.TemplateScopeProjectPublic:
		scope = nextagent.TemplateScope_ProjectPublic
	default:
		scope = nextagent.TemplateScope_Private
	}
	t := &nextagent.Template{
		ID:        template.TemplateID,
		Category:  template.Category,
		Name:      template.Name,
		Showcase:  nil,
		Scope:     scope,
		Creator:   template.Creator,
		StarCount: lo.ToPtr(int64(template.StarCount)),
		Status:    convertTemplateStatusToDTO(template.Status),
		Version:   template.Version,
		Label:     template.Label,
		Starred:   template.Starred,
		ShareID:   template.ShareID,
	}
	if packDetail {
		t.Prompt = template.PromptContent
		t.Variables = convertPromptVariablesToDTO(template.PromptVariables)
		t.EstimatedMinutes = &nextagent.EstimatedMinutes{
			// todo 目前定时任务的消息卡片同样写死了执行时间是40min，修改此处时需同步修改消息卡片的预估时长
			Minimum: 10,
			Maximum: 20,
		}
		t.SupportRoles = []nextagent.SessionRole{nextagent.SessionRole_YoungTalent, nextagent.SessionRole_LateralHire, nextagent.SessionRole_IndustryVeteran}
		t.SupportMCPs = convertMCPsToDTO(template.MCPs)
		t.Steps = lo.Ternary(template.Expired, nil, template.PlanSteps)
		t.PlanStepStatus = convertTemplatePlanStepStatusToDTO(template.PlanStepStatus())
	}
	return t
}

func ConvertTemplatesEntityToDTO(templates []*entity.TemplateVersion) []*nextagent.Template {
	var dtoTemplates []*nextagent.Template
	for _, t := range templates {
		dtoTemplates = append(dtoTemplates, ConvertTemplateEntityToDTO(t, false))
	}
	return dtoTemplates
}

func convertPromptVariablesToDTO(variables []*entity.TemplateVariableSchema) []*nextagent.TemplateVariable {
	var dtoVariables []*nextagent.TemplateVariable
	for _, v := range variables {
		if v == nil {
			continue
		}
		dtoVariables = append(dtoVariables, &nextagent.TemplateVariable{
			ID:            v.ID,
			Name:          v.Name,
			Required:      nil, // deprecated
			Description:   &v.Description,
			Type:          lo.Ternary[string](v.AllowedUploadFile, nextagent.TemplateVariableTypeUploadWithText, nextagent.TemplateVariableTypeAutoComplete),
			Placeholder:   &v.Placeholder,
			DefaultValue:  &v.Default,
			SelectContent: &v.SelectContent,
		})
	}
	return dtoVariables
}

func convertMCPsToDTO(mcps []*entity.MCP) []*nextagent.MCP {
	if len(mcps) == 0 {
		return nil
	}
	var dtoMCPs []*nextagent.MCP
	for i := range mcps {
		m := mcps[i]
		if m == nil {
			continue
		}
		dtoMCPs = append(dtoMCPs, &nextagent.MCP{
			ID:            m.MCPID,
			Name:          m.Name,
			Description:   m.Description,
			IconURL:       m.IconURL,
			Config:        nil,
			Creator:       m.Creator,
			Source:        convertMCPSourceToDTO(m.Source),
			CreatedAt:     m.CreatedAt.String(),
			UpdatedAt:     m.UpdatedAt.String(),
			IsActive:      m.IsActive,
			Type:          convertMCPTypeToDTO(m.Type),
			ForceActive:   m.ForceActive,
			ENName:        &m.EnName,
			EnDescription: &m.EnDescription,
			SessionRoles:  m.SessionRoles,
		})
	}
	return dtoMCPs
}

func convertMCPSourceToDTO(source entity.MCPSource) nextagent.MCPSource {
	switch source {
	case entity.MCPSourceAIME:
		return nextagent.MCPSource_AIME
	case entity.MCPSourceUserDefine:
		return nextagent.MCPSource_UserDefine
	case entity.MCPSourceCloud:
		return nextagent.MCPSource_Cloud
	default:
		return nextagent.MCPSource_AIME
	}
}

func convertMCPTypeToDTO(mcpType entity.MCPType) nextagent.MCPType {
	switch mcpType {
	case entity.MCPTypeSTDIO:
		return nextagent.MCPType_STDIO
	case entity.MCPTypeSSE:
		return nextagent.MCPType_SSE
	}
	return nextagent.MCPType_STDIO
}

func convertTemplateStatusToDTO(status string) nextagent.TemplateStatus {
	switch status {
	case entity.TemplateStatusDraft:
		return nextagent.TemplateStatus_Draft
	case entity.TemplateStatusGenerating:
		return nextagent.TemplateStatus_Generating
	case entity.TemplateStatusAvailable:
		return nextagent.TemplateStatus_Available
	default:
		return nextagent.TemplateStatus_Draft
	}
}

func ConvertTemplateFileToDTO(file *entity.TemplateFile) *nextagent.TemplateFile {
	if file == nil {
		return nil
	}
	return &nextagent.TemplateFile{
		FileID:     file.ID,
		TemplateID: file.TemplateID,
		Name:       file.Name,
		Type:       file.Type,
	}
}

func convertTemplatePlanStepStatusToDTO(status string) nextagent.TemplatePlanStepStatus {
	switch status {
	case entity.TemplatePlanStepStatusNone:
		return nextagent.TemplatePlanStepStatus_None
	case entity.TemplatePlanStepStatusGenerating:
		return nextagent.TemplatePlanStepStatus_Generating
	case entity.TemplatePlanStepStatusGenerated:
		return nextagent.TemplatePlanStepStatus_Generated
	default:
		return nextagent.TemplatePlanStepStatus_Default
	}
}

// GetTemplatePermissionsDTO 获取模板权限, username为当前用户, creator为模板创建者
// Deprecated: 后续计划使用 ConvertTemplatePermissionsToDTO，这里保留暂时为了兼容分享场景的权限
func GetTemplatePermissionsDTO(username string, template *entity.TemplateVersion) []nextagent.PermissionAction {
	if template == nil {
		return nil
	}
	var permissions []nextagent.PermissionAction
	if template.CanRead(username) {
		permissions = append(permissions, nextagent.PermissionActionTemplateRead)
	}
	if template.CanEdit(username) {
		permissions = append(permissions, nextagent.PermissionActionTemplateUpdate)
	}
	if template.CanDelete(username) {
		permissions = append(permissions, nextagent.PermissionActionTemplateDelete)
	}
	return permissions
}

// ConvertTemplatePermissionsToDTO 获取模板权限, username为当前用户, creator为模板创建者
func ConvertTemplatePermissionsToDTO(permissions []*entity.Permission) []nextagent.PermissionAction {
	if len(permissions) == 0 {
		return nil
	}
	var dtos []nextagent.PermissionAction
	for _, permission := range permissions {
		if permission == nil {
			continue
		}
		for _, action := range permission.PermissionActions {
			dtos = append(dtos, convertPermissionToDTO(action))
		}
	}
	return dtos
}

func convertPermissionToDTO(action entity.PermissionAction) nextagent.PermissionAction {
	switch action {
	case entity.PermissionActionTemplateRead:
		return nextagent.PermissionActionTemplateRead
	case entity.PermissionActionTemplateUpdate:
		return nextagent.PermissionActionTemplateUpdate
	case entity.PermissionActionTemplateDelete:
		return nextagent.PermissionActionTemplateDelete
	case entity.PermissionActionTemplateCreate:
		return nextagent.PermissionActionTemplateCreate
	default:
		return ""
	}
}

func ConvertToolCallActionToDTO(action agententity.ToolCallAction) nextagent.ToolCallAction {
	var dto nextagent.ToolCallAction
	switch action {
	case agententity.ToolCallActionReject:
		dto = nextagent.ToolCallAction_Reject
	case agententity.ToolCallActionConfirm:
		dto = nextagent.ToolCallAction_Confirm
	case agententity.ToolCallActionTimeout:
		dto = nextagent.ToolCallAction_Timeout
	}
	return dto
}

func ConvertTemplateFormValueDetailToDTO(formValue entity.TemplateFormValue) *nextagent.TemplateFormValueDetail {
	variables := make(map[string]*nextagent.TemplateVariableValueDetail)
	if formValue.Variables != nil {
		for k, v := range formValue.Variables {
			if v == nil || (lo.FromPtr(v.Content) == "" && len(v.Attachments) == 0) { // 过滤空变量
				continue
			}
			variables[k] = &nextagent.TemplateVariableValueDetail{
				Content: v.Content,
				Attachments: lo.Map(v.Attachments, func(a *entity.Attachment, _ int) *nextagent.Attachment {
					return &nextagent.Attachment{
						ID:            a.ID,
						FileName:      a.FileName,
						Path:          a.Path,
						Type:          a.Type,
						URL:           a.URL,
						ContentType:   a.ContentType,
						ContentLength: a.ContentLength,
					}
				}),
			}
		}
	}
	return &nextagent.TemplateFormValueDetail{Variables: variables}
}

func ConvertUserSettingsToDTO(settings *entity.UserSettings) *nextagent.UserSettings {
	if settings == nil {
		return nil
	}
	var keepLogin bool
	if len(settings.LoginInfo) > 0 {
		keepLogin = settings.LoginInfo[0].KeepLogin
	}
	return &nextagent.UserSettings{
		Username:         settings.Username,
		Locale:           settings.Locale,
		KeepLogin:        keepLogin,
		KeyboardShortcut: ConvertKeyboardShortcutToDTO(&settings.KeyboardShortcut),
		CreatedAt:        settings.CreatedAt.String(),
		UpdatedAt:        settings.UpdatedAt.String(),
	}
}

func ConvertKeyboardShortcutToDTO(t *entity.KeyboardShortcut) *nextagent.KeyboardShortcut {
	if t == nil {
		return nil
	}
	return &nextagent.KeyboardShortcut{
		Mac:     convertShortcutToDTO(t.Mac),
		Windows: convertShortcutToDTO(t.Windows),
	}
}

func convertShortcutToDTO(m map[string]*entity.Shortcut) map[string]*nextagent.Shortcut {
	if m == nil {
		return nil
	}
	result := make(map[string]*nextagent.Shortcut)
	for k, v := range m {
		if v == nil {
			continue
		}
		result[k] = &nextagent.Shortcut{
			Keys: v.Keys,
		}
	}
	return result
}

// ConvertMCPToDTOForUser 将 entity.MCP 转换为 nextagent.MCP, 根据权限隐藏关键数据
func ConvertMCPToDTOForUser(mcp *entity.MCP, username string) *nextagent.MCP {
	if mcp == nil {
		return nil
	}
	permissions := lo.Map(mcp.Permissions, func(p entity.PermissionAction, _ int) nextagent.PermissionAction {
		return p.ToIDL()
	})
	// 兜底处理下用户自己创建的 MCP，防止漏掉权限
	if len(permissions) == 0 && username == mcp.Creator {
		permissions = []nextagent.PermissionAction{nextagent.PermissionActionMCPRead, nextagent.PermissionActionMCPUpdate, nextagent.PermissionActionMCPDelete}
	}
	return &nextagent.MCP{
		ID:            mcp.MCPID,
		Name:          mcp.Name,
		ENName:        &mcp.EnName,
		Description:   mcp.Description,
		EnDescription: &mcp.EnDescription,
		IconURL:       mcp.IconURL,
		Config:        convertMCPConfigToConfigDTO(&mcp.Config, mcp.Permissions),
		Creator:       mcp.Creator,
		Source:        nextagent.MCPSource(mcp.Source),
		CreatedAt:     mcp.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     mcp.UpdatedAt.Format("2006-01-02 15:04:05"),
		IsActive:      mcp.IsActive,
		Type:          nextagent.MCPType(mcp.Type),
		ForceActive:   mcp.ForceActive,
		SessionRoles:  mcp.SessionRoles,
		Scope:         convertMCPScope(mcp.Scope),
		Permissions:   permissions,
		UserConfig:    convertMCPConfigToUserConfig(mcp.UserConfig),
		DefaultActive: gptr.Of(mcp.DefaultActive),
	}
}

// ConvertMCPToDTOForAdmin 将 entity.MCP 转换为 nextagent.MCP, 返回全量数据，给后台配置用
func ConvertMCPToDTOForAdmin(mcp *entity.MCP) *nextagent.MCP {
	if mcp == nil {
		return nil
	}

	return &nextagent.MCP{
		ID:            mcp.MCPID,
		Name:          mcp.Name,
		ENName:        &mcp.EnName,
		Description:   mcp.Description,
		EnDescription: &mcp.EnDescription,
		IconURL:       mcp.IconURL,
		Config: convertMCPConfigToConfigDTO(&mcp.Config,
			gslice.Uniq(append(mcp.Permissions, entity.PermissionActionMCPUpdate))),
		Creator:      mcp.Creator,
		Source:       nextagent.MCPSource(mcp.Source),
		CreatedAt:    mcp.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:    mcp.UpdatedAt.Format("2006-01-02 15:04:05"),
		IsActive:     mcp.IsActive,
		Type:         nextagent.MCPType(mcp.Type),
		ForceActive:  mcp.ForceActive,
		SessionRoles: mcp.SessionRoles,
		Scope:        convertMCPScope(mcp.Scope),
		Permissions: lo.Map(mcp.Permissions, func(p entity.PermissionAction, _ int) nextagent.PermissionAction {
			return p.ToIDL()
		}),
		UserConfig:    convertMCPConfigToUserConfig(mcp.UserConfig),
		DefaultActive: gptr.Of(mcp.DefaultActive),
	}
}

func convertMCPScope(scope entity.MCPScope) *nextagent.MCPScope {
	switch scope {
	case entity.MCPScopePublic:
		return lo.ToPtr(nextagent.MCPScope_Public)
	case entity.MCPScopePrivate:
		return lo.ToPtr(nextagent.MCPScope_Private)
	case entity.MCPScopeProjectPublic:
		return lo.ToPtr(nextagent.MCPScope_ProjectPublic)
	default:
		return lo.ToPtr(nextagent.MCPScope_Private)
	}
}

func ConvertMentionsToDTO(mentions []*agententity.Mention) []*nextagent.Mention {
	if len(mentions) == 0 {
		return nil
	}
	var dtos []*nextagent.Mention
	for _, mention := range mentions {
		if mention == nil {
			continue
		}
		dtos = append(dtos, &nextagent.Mention{
			ID:                mention.ID,
			Type:              convertMentionTypeToDTO(mention),
			CodebaseMention:   convertCodebaseMentionToDTO(mention.CodebaseMention),
			KnowledgeMention:  convertKnowledgeMentionToDTO(mention.LarkDocMention),
			AttachmentMention: convertAttachmentMentionToDTO(mention.AttachmentMention),
			SnippetMention:    convertSnippetMentionToDTO(mention.SnippetMention),
		})
	}
	return dtos
}

func convertSnippetMentionToDTO(m *agententity.SnippetMention) *nextagent.SnippetMention {
	if m == nil {
		return nil
	}
	return &nextagent.SnippetMention{
		Path: m.Path,
		Range: &nextagent.Range{
			StartLine:   m.SnippetRange.StartLine,
			EndLine:     m.SnippetRange.EndLine,
			StartColumn: m.SnippetRange.StartColumn,
			EndColumn:   m.SnippetRange.EndColumn,
		},
		Content:           m.Content,
		BelongsToArtifact: convertBelongsToArtifactToDTO(m.BelongsToArtifact),
	}
}

func convertBelongsToArtifactToDTO(m *agententity.BelongsToArtifact) *nextagent.BelongsToArtifact {
	if m == nil {
		return nil
	}
	return &nextagent.BelongsToArtifact{
		ID:      m.ID,
		Type:    m.Type,
		Key:     m.Key,
		Version: m.Version,
	}
}

func convertCodebaseMentionToDTO(m *agententity.CodebaseMention) *nextagent.CodebaseMention {
	if m == nil {
		return nil
	}
	return &nextagent.CodebaseMention{
		RepoName:  m.RepoName,
		Branch:    lo.ToPtr(m.Branch),
		Tag:       lo.ToPtr(m.Tag),
		FilePath:  lo.Ternary(isDirectory(m.Path), nil, &m.Path),
		Directory: lo.Ternary(isDirectory(m.Path), &m.Path, nil),
	}
}

func isDirectory(path string) bool {
	return filepath.Ext(path) == "" // TODO(dingbo): 暂时认为有后缀的是文件，没有后缀的是目录，长期需要优化
}

func convertKnowledgeMentionToDTO(m *agententity.LarkDocMention) *nextagent.KnowledgeMention {
	if m == nil {
		return nil
	}
	return &nextagent.KnowledgeMention{
		DocumentID:      m.ID,
		Title:           lo.ToPtr(m.Title),
		URL:             lo.ToPtr(m.URL),
		KnowledgeBaseID: m.KnowledgeBaseID,
	}
}

func convertAttachmentMentionToDTO(m *agententity.AttachmentMention) *nextagent.AttachmentMention {
	if m == nil {
		return nil
	}
	return &nextagent.AttachmentMention{
		ArtifactID:  m.ArtifactID,
		Path:        m.Path,
		IsFromQuery: m.IsFromQuery,
		Summary:     lo.ToPtr(m.Summary),
		Version:     &m.Version,
		Type:        convertAttachmentMentionTypeToDTO(m.Type),
		FileMeta: &nextagent.FileMeta{
			Name:                m.Path,
			LarkDocVersionToken: &m.LarkDocVersionToken,
			Content:             m.Content,
			SubType:             m.SubType,
			Type:                convertArtifactTypeToDTO(m.ArtifactType),
		},
		Comments: lo.Map(m.Comments, func(comment agententity.CommentMetadata, _ int) *nextagent.CommentMetadata {
			return ConvertCommentMetadataToDTO(&comment)
		}),
	}
}

func convertAttachmentMentionTypeToDTO(t agententity.AttachmentMentionType) *nextagent.AttachmentMentionType {
	switch t {
	case agententity.AttachmentMentionFileType:
		return lo.ToPtr(nextagent.AttachmentMentionFileType)
	case agententity.AttachmentMentionLarkDocCommentType:
		return lo.ToPtr(nextagent.AttachmentMentionLarkDocCommentType)
	default:
		return lo.ToPtr(nextagent.AttachmentMentionFileType)
	}
}

func convertMentionTypeToDTO(mention *agententity.Mention) nextagent.MentionType {
	if mention == nil {
		return nextagent.MentionTypeUnknown
	}
	switch mention.Type {
	case agententity.MentionTypeLarkDoc:
		return nextagent.MentionTypeKnowledgeBase
	case agententity.MentionTypeCodebase:
		if mention.CodebaseMention == nil {
			return nextagent.MentionTypeUnknown
		}
		if mention.CodebaseMention.Path != "" {
			if isDirectory(mention.CodebaseMention.Path) {
				return nextagent.MentionTypeRepositoryDir
			}
			return nextagent.MentionTypeRepositoryFile
		}
		if mention.CodebaseMention.Branch != "" {
			return nextagent.MentionTypeRepositoryBranch
		}
		return nextagent.MentionTypeRepository
	case agententity.MentionTypeAttachment:
		return nextagent.MentionTypeAttachment
	case agententity.MentionTypeSnippet:
		return nextagent.MentionTypeSnippet
	default:
		return nextagent.MentionTypeUnknown
	}
}

func convertMCPConfigToConfigDTO(c *entity.MCPConfig, permissions []entity.PermissionAction) *nextagent.MCPConfig {
	if c == nil {
		return nil
	}
	baseConfig := &nextagent.MCPConfig{
		SkipCloudJWTAuth: c.SkipCloudJWTAuth,
		RequiredUserConfigs: gslice.Map(c.RequiredUserConfigs, func(f int) nextagent.UserConfigType {
			return nextagent.UserConfigType(f)
		}),
	}
	// 没有更新权限，那就只能看到基础配置
	if !gslice.Contains(permissions, entity.PermissionActionMCPUpdate) {
		return baseConfig
	}
	baseConfig.Command = c.Command
	baseConfig.Args = c.Args
	baseConfig.BaseURL = c.BaseURL
	baseConfig.Env = c.Env
	baseConfig.PSM = c.PSM
	baseConfig.Header = c.Header
	return baseConfig
}

func convertMCPConfigToUserConfig(c *entity.MCPConfig) *nextagent.MCPConfig {
	if c == nil {
		return nil
	}
	return &nextagent.MCPConfig{
		Env:    c.Env,
		Header: c.Header,
	}
}

func convertArtifactTypeToDTO(t agententity.ArtifactType) nextagent.ArtifactType {
	switch t {
	case agententity.ArtifactTypeCode:
		return nextagent.ArtifactTypeCode
	case agententity.ArtifactTypeLink:
		return nextagent.ArtifactTypeLink
	case agententity.ArtifactTypeImage:
		return nextagent.ArtifactTypeImage
	case agententity.ArtifactTypeLogs:
		return nextagent.ArtifactTypeLogs
	case agententity.ArtifactTypeResult:
		return nextagent.ArtifactTypeResult
	case nextagent.ArtifactTypeProject:
		return nextagent.ArtifactTypeProject
	default:
		return nextagent.ArtifactTypeFile
	}
}

func ConvertCommentMetadataToDTO(m *agententity.CommentMetadata) *nextagent.CommentMetadata {
	if m == nil {
		return nil
	}
	return &nextagent.CommentMetadata{
		CommentID: m.CommentID,
		BlockID:   m.BlockID,
		Quote:     m.Quote,
		CreatedAt: lo.Ternary(!m.CreateAt.IsZero(), m.CreateAt.Format(time.RFC3339), ""),
		UpdatedAt: lo.Ternary(!m.UpdateAt.IsZero(), m.UpdateAt.Format(time.RFC3339), ""),
		UserID:    m.UserID,
		Username:  m.Username,
		Replies:   convertRepliesToDTO(m.Replies),
	}
}

// convertRepliesToDTO 转换回复列表
func convertRepliesToDTO(replies []*agententity.Reply) []*nextagent.Reply {
	if replies == nil {
		return nil
	}
	return lo.Map(replies, func(reply *agententity.Reply, _ int) *nextagent.Reply {
		if reply == nil {
			return nil
		}
		return &nextagent.Reply{
			ReplyContent: convertReplyContentToDTO(reply.ReplyContent),
			ReplyID:      reply.ReplyID,
			UserID:       reply.UserID,
			Username:     reply.Username,
			CreatedAt:    lo.Ternary(!reply.CreateAt.IsZero(), reply.CreateAt.Format(time.RFC3339), ""),
			UpdatedAt:    lo.Ternary(!reply.UpdateAt.IsZero(), reply.UpdateAt.Format(time.RFC3339), ""),
			Extra:        convertReplyExtraToDTO(reply.Extra),
		}
	})
}

// convertReplyContentToDTO 转换回复内容
func convertReplyContentToDTO(content *agententity.ReplyContent) *nextagent.ReplyContent {
	if content == nil {
		return nil
	}
	return &nextagent.ReplyContent{
		Elements: lo.Map(content.Elements, func(element *agententity.ReplyElement, _ int) *nextagent.ReplyElement {
			if element == nil {
				return nil
			}
			return &nextagent.ReplyElement{
				Type:     element.Type,
				TextRun:  convertTextRunToDTO(element.TextRun),
				DocsLink: convertDocsLinkToDTO(element.DocsLink),
				Person:   convertPersonToDTO(element.Person),
			}
		}),
	}
}

// convertTextRunToDTO 转换文本内容
func convertTextRunToDTO(textRun *agententity.TextRun) *nextagent.TextRun {
	if textRun == nil {
		return nil
	}
	return &nextagent.TextRun{
		Text:          textRun.Text,
		IsAimeComment: textRun.IsAimeComment,
	}
}

// convertDocsLinkToDTO 转换文档链接
func convertDocsLinkToDTO(docsLink *agententity.DocsLink) *nextagent.DocsLink {
	if docsLink == nil {
		return nil
	}
	return &nextagent.DocsLink{
		Url: docsLink.Link,
	}
}

// convertPersonToDTO 转换人员信息
func convertPersonToDTO(person *agententity.Person) *nextagent.Person {
	if person == nil {
		return nil
	}
	return &nextagent.Person{
		UserID:   person.UserID,
		Username: person.Username,
	}
}

// convertReplyExtraToDTO 转换回复额外信息
func convertReplyExtraToDTO(extra *agententity.ReplyExtra) *nextagent.Extra {
	if extra == nil {
		return nil
	}
	return &nextagent.Extra{
		ImageList: extra.ImageList,
	}
}
