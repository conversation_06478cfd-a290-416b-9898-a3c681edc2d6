// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/agentsphere/next_server/dal (interfaces: DeployCanaryDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/deploy_canary_dao.go -package dal code.byted.org/devgpt/kiwis/agentsphere/next_server/dal DeployCanaryDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"
	time "time"

	dal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	entity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockDeployCanaryDAO is a mock of DeployCanaryDAO interface.
type MockDeployCanaryDAO struct {
	ctrl     *gomock.Controller
	recorder *MockDeployCanaryDAOMockRecorder
	isgomock struct{}
}

// MockDeployCanaryDAOMockRecorder is the mock recorder for MockDeployCanaryDAO.
type MockDeployCanaryDAOMockRecorder struct {
	mock *MockDeployCanaryDAO
}

// NewMockDeployCanaryDAO creates a new mock instance.
func NewMockDeployCanaryDAO(ctrl *gomock.Controller) *MockDeployCanaryDAO {
	mock := &MockDeployCanaryDAO{ctrl: ctrl}
	mock.recorder = &MockDeployCanaryDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeployCanaryDAO) EXPECT() *MockDeployCanaryDAOMockRecorder {
	return m.recorder
}

// CreateDeployCanaryConfig mocks base method.
func (m *MockDeployCanaryDAO) CreateDeployCanaryConfig(ctx context.Context, opt dal.CreateDeployCanaryConfigOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDeployCanaryConfig", ctx, opt)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateDeployCanaryConfig indicates an expected call of CreateDeployCanaryConfig.
func (mr *MockDeployCanaryDAOMockRecorder) CreateDeployCanaryConfig(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDeployCanaryConfig", reflect.TypeOf((*MockDeployCanaryDAO)(nil).CreateDeployCanaryConfig), ctx, opt)
}

// DelDeployCanaryConfig mocks base method.
func (m *MockDeployCanaryDAO) DelDeployCanaryConfig(ctx context.Context, agentID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelDeployCanaryConfig", ctx, agentID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelDeployCanaryConfig indicates an expected call of DelDeployCanaryConfig.
func (mr *MockDeployCanaryDAOMockRecorder) DelDeployCanaryConfig(ctx, agentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDeployCanaryConfig", reflect.TypeOf((*MockDeployCanaryDAO)(nil).DelDeployCanaryConfig), ctx, agentID)
}

// GetCanaryTaskStatus mocks base method.
func (m *MockDeployCanaryDAO) GetCanaryTaskStatus(ctx context.Context, agentConfigVersionID string) (int64, int64, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCanaryTaskStatus", ctx, agentConfigVersionID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(int64)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetCanaryTaskStatus indicates an expected call of GetCanaryTaskStatus.
func (mr *MockDeployCanaryDAOMockRecorder) GetCanaryTaskStatus(ctx, agentConfigVersionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCanaryTaskStatus", reflect.TypeOf((*MockDeployCanaryDAO)(nil).GetCanaryTaskStatus), ctx, agentConfigVersionID)
}

// GetDeployCanaryConfig mocks base method.
func (m *MockDeployCanaryDAO) GetDeployCanaryConfig(ctx context.Context, agentID string) (string, float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeployCanaryConfig", ctx, agentID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(float64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDeployCanaryConfig indicates an expected call of GetDeployCanaryConfig.
func (mr *MockDeployCanaryDAOMockRecorder) GetDeployCanaryConfig(ctx, agentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeployCanaryConfig", reflect.TypeOf((*MockDeployCanaryDAO)(nil).GetDeployCanaryConfig), ctx, agentID)
}

// Lock mocks base method.
func (m *MockDeployCanaryDAO) Lock(ctx context.Context, agentConfigID string, expire time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", ctx, agentConfigID, expire)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Lock indicates an expected call of Lock.
func (mr *MockDeployCanaryDAOMockRecorder) Lock(ctx, agentConfigID, expire any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockDeployCanaryDAO)(nil).Lock), ctx, agentConfigID, expire)
}

// StartCanaryTask mocks base method.
func (m *MockDeployCanaryDAO) StartCanaryTask(ctx context.Context, agentConfigVersionID, sessionID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartCanaryTask", ctx, agentConfigVersionID, sessionID)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartCanaryTask indicates an expected call of StartCanaryTask.
func (mr *MockDeployCanaryDAOMockRecorder) StartCanaryTask(ctx, agentConfigVersionID, sessionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCanaryTask", reflect.TypeOf((*MockDeployCanaryDAO)(nil).StartCanaryTask), ctx, agentConfigVersionID, sessionID)
}

// Unlock mocks base method.
func (m *MockDeployCanaryDAO) Unlock(ctx context.Context, agentConfigID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", ctx, agentConfigID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockDeployCanaryDAOMockRecorder) Unlock(ctx, agentConfigID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockDeployCanaryDAO)(nil).Unlock), ctx, agentConfigID)
}

// UpdateCanaryTaskStatus mocks base method.
func (m *MockDeployCanaryDAO) UpdateCanaryTaskStatus(ctx context.Context, agentConfigVersionID, sessionID string, status entity.SessionStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCanaryTaskStatus", ctx, agentConfigVersionID, sessionID, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCanaryTaskStatus indicates an expected call of UpdateCanaryTaskStatus.
func (mr *MockDeployCanaryDAOMockRecorder) UpdateCanaryTaskStatus(ctx, agentConfigVersionID, sessionID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCanaryTaskStatus", reflect.TypeOf((*MockDeployCanaryDAO)(nil).UpdateCanaryTaskStatus), ctx, agentConfigVersionID, sessionID, status)
}
