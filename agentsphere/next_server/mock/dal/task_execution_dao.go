// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/agentsphere/next_server/dal (interfaces: TaskExecutionDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/task_execution_dao.go -package dal code.byted.org/devgpt/kiwis/agentsphere/next_server/dal TaskExecutionDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	dal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	entity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockTaskExecutionDAO is a mock of TaskExecutionDAO interface.
type MockTaskExecutionDAO struct {
	ctrl     *gomock.Controller
	recorder *MockTaskExecutionDAOMockRecorder
	isgomock struct{}
}

// MockTaskExecutionDAOMockRecorder is the mock recorder for MockTaskExecutionDAO.
type MockTaskExecutionDAOMockRecorder struct {
	mock *MockTaskExecutionDAO
}

// NewMockTaskExecutionDAO creates a new mock instance.
func NewMockTaskExecutionDAO(ctrl *gomock.Controller) *MockTaskExecutionDAO {
	mock := &MockTaskExecutionDAO{ctrl: ctrl}
	mock.recorder = &MockTaskExecutionDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskExecutionDAO) EXPECT() *MockTaskExecutionDAOMockRecorder {
	return m.recorder
}

// CreateTaskExecution mocks base method.
func (m *MockTaskExecutionDAO) CreateTaskExecution(ctx context.Context, opt dal.CreateTaskExecutionOption) (*entity.TaskExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTaskExecution", ctx, opt)
	ret0, _ := ret[0].(*entity.TaskExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTaskExecution indicates an expected call of CreateTaskExecution.
func (mr *MockTaskExecutionDAOMockRecorder) CreateTaskExecution(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTaskExecution", reflect.TypeOf((*MockTaskExecutionDAO)(nil).CreateTaskExecution), ctx, opt)
}

// GetLastTaskExecutionByTaskUID mocks base method.
func (m *MockTaskExecutionDAO) GetLastTaskExecutionByTaskUID(ctx context.Context, opt dal.GetLastTaskExecutionByTaskUIDOption) (*entity.TaskExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastTaskExecutionByTaskUID", ctx, opt)
	ret0, _ := ret[0].(*entity.TaskExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastTaskExecutionByTaskUID indicates an expected call of GetLastTaskExecutionByTaskUID.
func (mr *MockTaskExecutionDAOMockRecorder) GetLastTaskExecutionByTaskUID(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastTaskExecutionByTaskUID", reflect.TypeOf((*MockTaskExecutionDAO)(nil).GetLastTaskExecutionByTaskUID), ctx, opt)
}

// GetTaskExecution mocks base method.
func (m *MockTaskExecutionDAO) GetTaskExecution(ctx context.Context, opt dal.GetTaskExecutionOption) (*entity.TaskExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskExecution", ctx, opt)
	ret0, _ := ret[0].(*entity.TaskExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskExecution indicates an expected call of GetTaskExecution.
func (mr *MockTaskExecutionDAOMockRecorder) GetTaskExecution(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskExecution", reflect.TypeOf((*MockTaskExecutionDAO)(nil).GetTaskExecution), ctx, opt)
}

// ListTaskExecutions mocks base method.
func (m *MockTaskExecutionDAO) ListTaskExecutions(ctx context.Context, opt dal.ListTaskExecutionsOption) (int64, []*entity.TaskExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskExecutions", ctx, opt)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*entity.TaskExecution)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTaskExecutions indicates an expected call of ListTaskExecutions.
func (mr *MockTaskExecutionDAOMockRecorder) ListTaskExecutions(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskExecutions", reflect.TypeOf((*MockTaskExecutionDAO)(nil).ListTaskExecutions), ctx, opt)
}

// UpdateTaskExecution mocks base method.
func (m *MockTaskExecutionDAO) UpdateTaskExecution(ctx context.Context, opt dal.UpdateTaskExecutionOption) (*entity.TaskExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskExecution", ctx, opt)
	ret0, _ := ret[0].(*entity.TaskExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskExecution indicates an expected call of UpdateTaskExecution.
func (mr *MockTaskExecutionDAOMockRecorder) UpdateTaskExecution(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskExecution", reflect.TypeOf((*MockTaskExecutionDAO)(nil).UpdateTaskExecution), ctx, opt)
}
