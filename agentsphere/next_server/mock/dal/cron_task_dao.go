// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devgpt/kiwis/agentsphere/next_server/dal (interfaces: CronTaskDAO)
//
// Generated by this command:
//
//	mockgen -destination ../mock/dal/cron_task_dao.go -package dal code.byted.org/devgpt/kiwis/agentsphere/next_server/dal CronTaskDAO
//

// Package dal is a generated GoMock package.
package dal

import (
	context "context"
	reflect "reflect"

	dal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	entity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockCronTaskDAO is a mock of CronTaskDAO interface.
type MockCronTaskDAO struct {
	ctrl     *gomock.Controller
	recorder *MockCronTaskDAOMockRecorder
	isgomock struct{}
}

// MockCronTaskDAOMockRecorder is the mock recorder for MockCronTaskDAO.
type MockCronTaskDAOMockRecorder struct {
	mock *MockCronTaskDAO
}

// NewMockCronTaskDAO creates a new mock instance.
func NewMockCronTaskDAO(ctrl *gomock.Controller) *MockCronTaskDAO {
	mock := &MockCronTaskDAO{ctrl: ctrl}
	mock.recorder = &MockCronTaskDAOMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCronTaskDAO) EXPECT() *MockCronTaskDAOMockRecorder {
	return m.recorder
}

// CreateCronTask mocks base method.
func (m *MockCronTaskDAO) CreateCronTask(ctx context.Context, opt dal.CreateCronTaskOption) (*entity.CronTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCronTask", ctx, opt)
	ret0, _ := ret[0].(*entity.CronTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCronTask indicates an expected call of CreateCronTask.
func (mr *MockCronTaskDAOMockRecorder) CreateCronTask(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCronTask", reflect.TypeOf((*MockCronTaskDAO)(nil).CreateCronTask), ctx, opt)
}

// DeleteCronTask mocks base method.
func (m *MockCronTaskDAO) DeleteCronTask(ctx context.Context, uid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCronTask", ctx, uid)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCronTask indicates an expected call of DeleteCronTask.
func (mr *MockCronTaskDAOMockRecorder) DeleteCronTask(ctx, uid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCronTask", reflect.TypeOf((*MockCronTaskDAO)(nil).DeleteCronTask), ctx, uid)
}

// GetCronTask mocks base method.
func (m *MockCronTaskDAO) GetCronTask(ctx context.Context, opt dal.GetCronTaskOption) (*entity.CronTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCronTask", ctx, opt)
	ret0, _ := ret[0].(*entity.CronTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCronTask indicates an expected call of GetCronTask.
func (mr *MockCronTaskDAOMockRecorder) GetCronTask(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCronTask", reflect.TypeOf((*MockCronTaskDAO)(nil).GetCronTask), ctx, opt)
}

// GetPendingTasks mocks base method.
func (m *MockCronTaskDAO) GetPendingTasks(ctx context.Context, limit int) ([]*entity.CronTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingTasks", ctx, limit)
	ret0, _ := ret[0].([]*entity.CronTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingTasks indicates an expected call of GetPendingTasks.
func (mr *MockCronTaskDAOMockRecorder) GetPendingTasks(ctx, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingTasks", reflect.TypeOf((*MockCronTaskDAO)(nil).GetPendingTasks), ctx, limit)
}

// ListCronTasks mocks base method.
func (m *MockCronTaskDAO) ListCronTasks(ctx context.Context, opt dal.ListCronTasksOption) (int64, []*entity.CronTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCronTasks", ctx, opt)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*entity.CronTask)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListCronTasks indicates an expected call of ListCronTasks.
func (mr *MockCronTaskDAOMockRecorder) ListCronTasks(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCronTasks", reflect.TypeOf((*MockCronTaskDAO)(nil).ListCronTasks), ctx, opt)
}

// UpdateCronTask mocks base method.
func (m *MockCronTaskDAO) UpdateCronTask(ctx context.Context, opt dal.UpdateCronTaskOption) (*entity.CronTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCronTask", ctx, opt)
	ret0, _ := ret[0].(*entity.CronTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCronTask indicates an expected call of UpdateCronTask.
func (mr *MockCronTaskDAOMockRecorder) UpdateCronTask(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCronTask", reflect.TypeOf((*MockCronTaskDAO)(nil).UpdateCronTask), ctx, opt)
}

// UpdateExecutionInfo mocks base method.
func (m *MockCronTaskDAO) UpdateExecutionInfo(ctx context.Context, opt dal.UpdateExecutionInfoOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateExecutionInfo", ctx, opt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateExecutionInfo indicates an expected call of UpdateExecutionInfo.
func (mr *MockCronTaskDAOMockRecorder) UpdateExecutionInfo(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExecutionInfo", reflect.TypeOf((*MockCronTaskDAO)(nil).UpdateExecutionInfo), ctx, opt)
}

// UpdateTaskStatus mocks base method.
func (m *MockCronTaskDAO) UpdateTaskStatus(ctx context.Context, uid string, status int8) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskStatus", ctx, uid, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskStatus indicates an expected call of UpdateTaskStatus.
func (mr *MockCronTaskDAOMockRecorder) UpdateTaskStatus(ctx, uid, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatus", reflect.TypeOf((*MockCronTaskDAO)(nil).UpdateTaskStatus), ctx, uid, status)
}
