package impl

import (
	"context"
	"github.com/AlekSi/pointer"
	"gorm.io/datatypes"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/port/db"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

var _ dal.CronTaskDAO = &CronTaskDAOImpl{}

type CronTaskDAOImpl struct {
	dbClient db.Client
}

func NewCronTaskDAOImpl(dbClient db.Client) *CronTaskDAOImpl {
	return &CronTaskDAOImpl{
		dbClient: dbClient,
	}
}

// GetCronTask 获取定时任务
func (d *CronTaskDAOImpl) GetCronTask(ctx context.Context, opt dal.GetCronTaskOption) (*entity.CronTask, error) {
	req := d.dbClient.NewRequest(ctx)

	if opt.UID != "" {
		req = req.Where("uid = ?", opt.UID)
	}
	if opt.ID != 0 {
		req = req.Where("id = ?", opt.ID)
	}
	if opt.UserID != nil {
		req = req.Where("user_id = ?", *opt.UserID)
	}

	req = req.Where("deleted_at = 0")

	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextCronTasksPO)
	if err := req.Take(p).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return getCronTaskFromPO(p), nil
}

// CreateCronTask 创建定时任务
func (d *CronTaskDAOImpl) CreateCronTask(ctx context.Context, opt dal.CreateCronTaskOption) (*entity.CronTask, error) {
	p := &po.NextCronTasksPO{
		Username:        opt.UserName,
		Uid:             opt.UID,
		Name:            opt.Name,
		TaskType:        int(opt.TaskType),
		TemplateID:      opt.TemplateID,
		Schedule:        opt.Schedule,
		Timezone:        opt.Timezone,
		NextExecuteTime: opt.NextExecuteTime,
		TaskStatus:      int(opt.TaskStatus),
		GroupInfo:       datatypes.NewJSONType(opt.GroupInfo),
		Role:            pointer.To(int(opt.Role)),
		NextMqEventID:   opt.NextMQEventID,
		SpaceID:         opt.SpaceID,
	}

	if opt.Description != nil {
		p.Description = lo.ToPtr(*opt.Description)
	}

	if opt.NextMQEventID != nil {
		p.NextMqEventID = lo.ToPtr(*opt.NextMQEventID)
	}

	if opt.Content != nil {
		p.Content = opt.Content
	}

	if opt.Option != nil {
		p.Option = opt.Option
	}

	if err := d.dbClient.NewRequest(ctx).Create(p).Error; err != nil {
		return nil, err
	}
	return getCronTaskFromPO(p), nil
}

// UpdateCronTask 更新定时任务
func (d *CronTaskDAOImpl) UpdateCronTask(ctx context.Context, opt dal.UpdateCronTaskOption) (*entity.CronTask, error) {
	if opt.UID == "" {
		return nil, errors.New("UID is required")
	}

	updateMap := map[string]any{}
	if opt.Name != nil {
		updateMap["name"] = *opt.Name
	}
	if opt.Description != nil {
		updateMap["description"] = *opt.Description
	}
	if opt.TaskType != nil {
		updateMap["task_type"] = *opt.TaskType
	}
	if opt.TemplateID != nil {
		updateMap["template_id"] = *opt.TemplateID
	}
	if opt.Schedule != nil {
		updateMap["schedule"] = *opt.Schedule
	}
	if opt.Timezone != nil {
		updateMap["timezone"] = *opt.Timezone
	}
	if opt.NextExecuteTime != nil {
		updateMap["next_execute_time"] = *opt.NextExecuteTime
	}
	if opt.NextMQEventID != nil {
		updateMap["next_mq_event_id"] = *opt.NextMQEventID
	}
	if opt.TaskStatus != nil {
		updateMap["task_status"] = *opt.TaskStatus
	}
	if opt.GroupInfo != nil {
		updateMap["group_info"] = datatypes.NewJSONType(opt.GroupInfo)
	}
	if opt.Role != nil {
		updateMap["role"] = *opt.Role
	}
	if opt.Content != "" {
		updateMap["content"] = opt.Content
	}
	if opt.Option != nil {
		updateMap["option"] = *opt.Option
	}
	if opt.LastRunStatus != nil {
		updateMap["last_run_status"] = *opt.LastRunStatus
	}

	updateMap["updated_at"] = time.Now()

	res := d.dbClient.NewRequest(ctx).Model(&po.NextCronTasksPO{}).
		Where("uid = ? AND deleted_at = 0", opt.UID).
		Updates(updateMap)
	if res.Error != nil {
		return nil, res.Error
	}
	if res.RowsAffected < 1 {
		return nil, dal.ErrRowsAffectedZero
	}

	return d.GetCronTask(ctx, dal.GetCronTaskOption{UID: opt.UID, Sync: true})
}

// DeleteCronTask 删除定时任务
func (d *CronTaskDAOImpl) DeleteCronTask(ctx context.Context, uid string) error {
	return d.dbClient.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {

		// 执行记录暂时不删除
		// 删除cron task variables
		if err := tx.Delete(&po.NextCronTaskTemplateVariablePO{}, "cron_task_uid = ?", uid).Error; err != nil {
			return errors.WithMessage(err, "failed to delete cron task variables")
		}
		// 删除cron task
		if err := tx.Delete(&po.NextCronTasksPO{}, "uid = ?", uid).Error; err != nil {
			return errors.WithMessage(err, "failed to delete cron task")
		}

		return nil
	})
}

// ListCronTasks 查询定时任务列表
func (d *CronTaskDAOImpl) ListCronTasks(ctx context.Context, opt dal.ListCronTasksOption) (int64, []*entity.CronTask, error) {
	req := d.dbClient.NewRequest(ctx).Model(po.NextCronTasksPO{}).Where("deleted_at = 0")
	// list时必须带上space id的校验
	req.Where("space_id = ?", opt.SpaceID)

	if opt.Query != nil {
		// todo 考虑增加索引
		req = req.Where("name LIKE ?", *opt.Query+"%")
	}
	if opt.UserName != nil {
		req = req.Where("username = ?", *opt.UserName)
	}
	if opt.TaskStatus != nil {
		req = req.Where("task_status = ?", *opt.TaskStatus)
	}
	if opt.GroupID != nil {
		req = req.Where("group_id = ?", *opt.GroupID)
	}

	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	if opt.Limit > 0 {
		req = req.Limit(opt.Limit)
	}
	if opt.Offset > 0 {
		req = req.Offset(opt.Offset)
	}

	var pos []*po.NextCronTasksPO
	if err := req.Order("id DESC").Find(&pos).Error; err != nil {
		return 0, nil, err
	}

	return total, lo.Map(pos, func(p *po.NextCronTasksPO, _ int) *entity.CronTask {
		return getCronTaskFromPO(p)
	}), nil
}

// GetPendingTasks 获取待执行的任务（用于调度器）
func (d *CronTaskDAOImpl) GetPendingTasks(ctx context.Context, limit int) ([]*entity.CronTask, error) {
	req := d.dbClient.NewRequest(ctx).
		Where("deleted_at = 0").
		Where("task_status = 1"). // 只查询启用状态的任务
		Where("next_execute_time <= ?", time.Now()).
		Order("next_execute_time ASC")

	if limit > 0 {
		req = req.Limit(limit)
	}

	var pos []*po.NextCronTasksPO
	if err := req.Find(&pos).Error; err != nil {
		return nil, err
	}

	return lo.Map(pos, func(p *po.NextCronTasksPO, _ int) *entity.CronTask {
		return getCronTaskFromPO(p)
	}), nil
}

// UpdateTaskStatus 更新任务状态
func (d *CronTaskDAOImpl) UpdateTaskStatus(ctx context.Context, uid string, status int8) error {
	updateMap := map[string]any{
		"task_status": status,
		"updated_at":  time.Now(),
	}

	res := d.dbClient.NewRequest(ctx).Model(&po.NextCronTasksPO{}).
		Where("uid = ? AND deleted_at = 0", uid).
		Updates(updateMap)

	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected < 1 {
		return dal.ErrRowsAffectedZero
	}
	return nil
}

// UpdateExecutionInfo 更新执行信息
func (d *CronTaskDAOImpl) UpdateExecutionInfo(ctx context.Context, opt dal.UpdateExecutionInfoOption) error {
	if opt.UID == "" {
		return errors.New("UID is required")
	}

	updateMap := map[string]any{
		"last_run_status": opt.LastRunStatus,
		"updated_at":      time.Now(),
	}

	if opt.LastExecuteTime != nil {
		updateMap["last_execute_time"] = *opt.LastExecuteTime
	}
	if opt.NextExecuteTime != nil {
		updateMap["next_execute_time"] = *opt.NextExecuteTime
	}
	if opt.NextMQEventID != nil {
		updateMap["next_mq_event_id"] = *opt.NextMQEventID
	}

	res := d.dbClient.NewRequest(ctx).Model(&po.NextCronTasksPO{}).
		Where("uid = ? AND deleted_at = 0", opt.UID).
		Updates(updateMap)

	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected < 1 {
		return dal.ErrRowsAffectedZero
	}
	return nil
}

// getCronTaskFromPO 将 PO 转换为 entity
func getCronTaskFromPO(p *po.NextCronTasksPO) *entity.CronTask {
	if p == nil {
		return nil
	}
	var role entity.SessionRole
	if p.Role != nil {
		role = entity.SessionRole(lo.FromPtr(p.Role))
	}
	var taskType entity.TaskType
	if p.TaskType != 0 {
		taskType = entity.TaskType(p.TaskType)
	}

	var lastRunStatus entity.LastRunStatus
	if p.LastRunStatus != 0 {
		lastRunStatus = entity.LastRunStatus(p.LastRunStatus)
	}

	var taskStatus entity.CronTaskStatus
	if p.TaskStatus != 0 {
		taskStatus = entity.CronTaskStatus(p.TaskStatus)
	}

	task := &entity.CronTask{
		ID:              p.ID,
		CreatedAt:       p.CreatedAt,
		UpdatedAt:       p.UpdatedAt,
		UserName:        p.Username,
		UID:             p.Uid,
		Name:            p.Name,
		TaskType:        taskType,
		TemplateID:      p.TemplateID,
		Schedule:        p.Schedule,
		Timezone:        p.Timezone,
		NextExecuteTime: p.NextExecuteTime,
		LastRunStatus:   lastRunStatus,
		TaskStatus:      taskStatus,
		GroupInfo:       p.GroupInfo.Data(),
		Role:            role,
		Content:         lo.FromPtr(p.Content),
		Option:          lo.FromPtr(p.Option),
		SpaceID:         p.SpaceID,
	}

	if p.Description != nil {
		task.Description = *p.Description
	}
	if p.NextMqEventID != nil {
		task.NextMQEventID = *p.NextMqEventID
	}
	if p.LastExecuteTime != nil {
		task.LastExecuteTime = *p.LastExecuteTime
	}

	return task
}
