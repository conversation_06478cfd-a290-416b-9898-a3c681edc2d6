package impl

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/port/db"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

var _ dal.TaskExecutionDAO = &TaskExecutionDAOImpl{}

type TaskExecutionDAOImpl struct {
	dbClient db.Client
}

func NewTaskExecutionDAOImpl(dbClient db.Client) *TaskExecutionDAOImpl {
	return &TaskExecutionDAOImpl{
		dbClient: dbClient,
	}
}

// GetTaskExecution 获取任务执行记录
func (d *TaskExecutionDAOImpl) GetTaskExecution(ctx context.Context, opt dal.GetTaskExecutionOption) (*entity.TaskExecution, error) {
	req := d.dbClient.NewRequest(ctx).Where("session_id = ? AND deleted_at = 0", opt.SessionID)

	if opt.LarkMessageStatus != nil {
		req = req.Where("lark_message_status = ?", *opt.LarkMessageStatus)
	}

	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextTaskExecutionsHistoryPO)
	if err := req.Take(p).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return getTaskExecutionFromPO(p), nil
}

// GetLastTaskExecutionByTaskUID 通过任务UID获取最近一次执行记录
func (d *TaskExecutionDAOImpl) GetLastTaskExecutionByTaskUID(ctx context.Context, opt dal.GetLastTaskExecutionByTaskUIDOption) (*entity.TaskExecution, error) {
	req := d.dbClient.NewRequest(ctx).Where("external_uid = ? AND deleted_at = 0", opt.UID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextTaskExecutionsHistoryPO)
	if err := req.Last(p).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return getTaskExecutionFromPO(p), nil
}

// CreateTaskExecution 创建任务执行记录
func (d *TaskExecutionDAOImpl) CreateTaskExecution(ctx context.Context, opt dal.CreateTaskExecutionOption) (*entity.TaskExecution, error) {
	p := &po.NextTaskExecutionsHistoryPO{
		ExternalUid:       opt.TaskUID,
		Username:          opt.Username,
		ExecuteTime:       opt.ExecuteTime,
		Status:            int(opt.Status),
		SessionID:         opt.SessionID,
		ExternalType:      int(opt.TriggerType),
		LarkMessageStatus: int8(opt.LarkMessageStatus),
	}

	if opt.ErrorMessage != nil {
		p.ErrorMessage = opt.ErrorMessage
	}
	if opt.MQEventID != nil {
		p.MqEventID = opt.MQEventID
	}
	if opt.LarkMessageID != nil {
		p.LarkMessageID = opt.LarkMessageID
	}
	if opt.Config != nil {
		p.Config = lo.ToPtr(datatypes.NewJSONType(*opt.Config))
	}

	if err := d.dbClient.NewRequest(ctx).Create(p).Error; err != nil {
		return nil, err
	}
	return getTaskExecutionFromPO(p), nil
}

// UpdateTaskExecution 更新任务执行记录
func (d *TaskExecutionDAOImpl) UpdateTaskExecution(ctx context.Context, opt dal.UpdateTaskExecutionOption) (*entity.TaskExecution, error) {
	if opt.SessionID == "" {
		return nil, errors.New("ID is required")
	}

	updateMap := map[string]any{}
	if opt.Status != nil {
		updateMap["status"] = *opt.Status
	}
	if opt.ErrorMessage != nil {
		updateMap["error_message"] = *opt.ErrorMessage
	}
	if opt.ExecutionDuration != nil {
		updateMap["execution_duration"] = *opt.ExecutionDuration
	}
	if opt.LarkMessageStatus != nil {
		updateMap["lark_message_status"] = *opt.LarkMessageStatus
	}
	updateMap["updated_at"] = time.Now()

	res := d.dbClient.NewRequest(ctx).Model(&po.NextTaskExecutionsHistoryPO{}).
		Where("session_id = ? AND deleted_at = 0", opt.SessionID).
		Updates(updateMap)
	if res.Error != nil {
		return nil, res.Error
	}
	if res.RowsAffected < 1 {
		return nil, dal.ErrRowsAffectedZero
	}

	return d.GetTaskExecution(ctx, dal.GetTaskExecutionOption{SessionID: opt.SessionID, Sync: true})
}

// ListTaskExecutions 查询任务执行记录列表
func (d *TaskExecutionDAOImpl) ListTaskExecutions(ctx context.Context, opt dal.ListTaskExecutionsOption) (int64, []*entity.TaskExecution, error) {
	req := d.dbClient.NewRequest(ctx).Model(po.NextTaskExecutionsHistoryPO{}).Where("deleted_at = 0")

	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	if opt.TaskUID != nil {
		req = req.Where("external_uid = ?", *opt.TaskUID)
	}
	if opt.UserID != nil {
		req = req.Where("user_id = ?", *opt.UserID)
	}
	if opt.Status != nil {
		req = req.Where("status = ?", *opt.Status)
	}
	if opt.StartTime != nil {
		req = req.Where("execute_time >= ?", *opt.StartTime)
	}
	if opt.EndTime != nil {
		req = req.Where("execute_time <= ?", *opt.EndTime)
	}

	req = req.Order("execute_time DESC")

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	if opt.Limit > 0 {
		req = req.Limit(opt.Limit)
	}
	if opt.Offset > 0 {
		req = req.Offset(opt.Offset)
	}

	var pos []*po.NextTaskExecutionsHistoryPO
	if err := req.Order("id DESC").Find(&pos).Error; err != nil {
		return 0, nil, err
	}

	result := make([]*entity.TaskExecution, 0, len(pos))
	for _, p := range pos {
		result = append(result, getTaskExecutionFromPO(p))
	}
	return total, result, nil
}

// getTaskExecutionFromPO 将PO转换为Entity
func getTaskExecutionFromPO(p *po.NextTaskExecutionsHistoryPO) *entity.TaskExecution {
	if p == nil {
		return nil
	}

	var triggerType entity.TriggerType
	if p.ExternalType != 0 {
		triggerType = entity.TriggerType(p.ExternalType)
	}
	var config entity.TaskConfig
	if p.Config != nil {
		config = p.Config.Data()
	}
	var larkMessageStatus entity.TaskExecutionLarkMessageStatus
	if p.LarkMessageStatus != 0 {
		larkMessageStatus = entity.TaskExecutionLarkMessageStatus(p.LarkMessageStatus)
	}

	return &entity.TaskExecution{
		ID:                p.ID,
		CreatedAt:         p.CreatedAt,
		UpdatedAt:         p.UpdatedAt,
		TaskUID:           p.ExternalUid,
		Username:          p.Username,
		ExecuteTime:       p.ExecuteTime,
		ErrorMessage:      p.ErrorMessage,
		SessionID:         p.SessionID,
		TriggerType:       triggerType,
		MQEventID:         p.MqEventID,
		Config:            config,
		LarkMessageID:     p.LarkMessageID,
		LarkMessageStatus: larkMessageStatus,
	}
}
