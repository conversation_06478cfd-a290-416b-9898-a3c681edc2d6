package impl

import (
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/lib/di"
	"go.uber.org/fx"
)

var Module = fx.Options(
	fx.Provide(NewAgentDeployRelationDAOImpl),
	fx.Provide(di.Bind(new(AgentDeployRelationDAOImpl), new(dal.AgentDeployRelationDAO))),

	fx.Provide(NewKnowledgesetDAOImpl),
	fx.Provide(di.Bind(new(KnowledgesetDAOImpl), new(dal.KnowledgesetDAO))),

	fx.Provide(NewKnowledgesetVersionDAOImpl),
	fx.Provide(di.Bind(new(KnowledgesetVersionDAOImpl), new(dal.KnowledgesetVersionDAO))),

	fx.Provide(NewKnowledgeDAOImpl),
	fx.Provide(di.Bind(new(KnowledgeDAOImpl), new(dal.KnowledgeDAO))),

	fx.Provide(NewDeployCanaryDAOImpl),
	fx.Provide(di.Bind(new(DeployCanaryDAOImpl), new(dal.DeployCanaryDAO))),

	fx.Provide(NewCronTaskDAOImpl),
	fx.Provide(di.Bind(new(CronTaskDAOImpl), new(dal.CronTaskDAO))),

	fx.Provide(NewTaskExecutionDAOImpl),
	fx.Provide(di.Bind(new(TaskExecutionDAOImpl), new(dal.TaskExecutionDAO))),
)
