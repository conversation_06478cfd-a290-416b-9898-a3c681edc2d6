package impl

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
)

const (
	CanaryPrefix = "canary:"

	// each task belongs to a hset: succeed, failed or running
	// status change can only start from running to a completed status to record the first completion of task
	RunningSuffix = ":running" // when session is created

	SucceedSuffix = ":succeed" // when session enters idle or stopped
	FailedSuffix  = ":failed"  // when session enters error
	InvalidSuffix = ":invalid" // when session is canceled
	LockKeyPrefix = "deploy:lock"
)

type DeployCanaryDAOImpl struct {
	cache redis.Client
}

var _ dal.DeployCanaryDAO = (*DeployCanaryDAOImpl)(nil)

func NewDeployCanaryDAOImpl(cache redis.Client) *DeployCanaryDAOImpl {
	return &DeployCanaryDAOImpl{
		cache: cache,
	}
}

func (d *DeployCanaryDAOImpl) CreateDeployCanaryConfig(ctx context.Context, opt dal.CreateDeployCanaryConfigOption) error {
	return d.cache.HMSet(ctx, fmt.Sprintf("%s%s", CanaryPrefix, opt.AgentID), map[string]interface{}{
		"version": opt.AgentConfigVersionID,
		"ratio":   opt.RatioLimit,
	})
}

func (d *DeployCanaryDAOImpl) GetDeployCanaryConfig(ctx context.Context, agentID string) (string, float64, error) {
	resp, err := d.cache.HMGet(ctx, fmt.Sprintf("%s%s", CanaryPrefix, agentID), "version", "ratio")
	if err != nil {
		return "", 0, err
	}
	if len(resp) == 0 || resp[0] == nil {
		return "", 0, nil
	}
	version := ""
	if versionStr, ok := resp[0].(string); ok {
		version = versionStr
	}
	ratio := 0.0
	if resp[1] != nil {
		if ratioStr, ok := resp[1].(string); ok {
			ratioNum, err := strconv.ParseFloat(ratioStr, 64)
			if err == nil {
				ratio = ratioNum
			}
		}
	}

	return version, ratio, nil
}

func (d *DeployCanaryDAOImpl) DelDeployCanaryConfig(ctx context.Context, agentID string) error {
	version, err := d.cache.HGet(ctx, fmt.Sprintf("%s%s", CanaryPrefix, agentID), "version")
	if err != nil && !redis.IsNil(err) {
		return err
	}

	if version == nil {
		return nil
	}
	versionStr, ok := version.(string)
	if !ok {
		return errors.New("version is not string")
	}
	if versionStr == "" {
		return nil
	}
	err = d.cache.Del(ctx, fmt.Sprintf("%s%s", CanaryPrefix, agentID))
	if err != nil {
		return err
	}
	return d.cache.Del(ctx, canaryTaskKey(versionStr, SucceedSuffix), canaryTaskKey(versionStr, FailedSuffix), canaryTaskKey(versionStr, RunningSuffix))
}

func (d *DeployCanaryDAOImpl) StartCanaryTask(ctx context.Context, agentConfigVersionID string, sessionID string) error {
	_, err := d.cache.SAdd(ctx, canaryTaskKey(agentConfigVersionID, RunningSuffix), sessionID)
	return err
}

// if session is already in running, it will be moved to the new status
// do nothing if session is in a completed status
func (d *DeployCanaryDAOImpl) UpdateCanaryTaskStatus(ctx context.Context, agentConfigVersionID string, sessionID string, status entity.SessionStatus) error {
	key := statusKey(status)
	if len(key) == 0 {
		return nil
	}
	_, err := d.cache.SMove(ctx, canaryTaskKey(agentConfigVersionID, RunningSuffix), canaryTaskKey(agentConfigVersionID, key), sessionID)
	if err != nil {
		log.V1.CtxWarn(ctx, "update canary task %s status to %s, err: %v", sessionID, status, err)
	}
	// maybe its status is already changed, but it's ok
	return err
}

func (d *DeployCanaryDAOImpl) GetCanaryTaskStatus(ctx context.Context, agentConfigVersionID string) (int64, int64, int64, error) {
	succeed, err := d.cache.SCard(ctx, canaryTaskKey(agentConfigVersionID, SucceedSuffix))
	if err != nil {
		return 0, 0, 0, err
	}
	failed, err := d.cache.SCard(ctx, canaryTaskKey(agentConfigVersionID, FailedSuffix))
	if err != nil {
		return 0, 0, 0, err
	}
	running, err := d.cache.SCard(ctx, canaryTaskKey(agentConfigVersionID, RunningSuffix))
	if err != nil {
		return 0, 0, 0, err
	}
	return succeed, failed, running, nil
}

func canaryTaskKey(agentConfigVersionID string, status string) string {
	return fmt.Sprintf("{%s}%s%s%s", agentConfigVersionID, CanaryPrefix, agentConfigVersionID, status)
}

func statusKey(status entity.SessionStatus) string {
	switch status {
	case entity.SessionStatusStopped, entity.SessionStatusIdle:
		return SucceedSuffix
	case entity.SessionStatusError:
		return FailedSuffix
	case entity.SessionStatusCanceled:
		return InvalidSuffix
	default:
		return ""
	}
}

func (d *DeployCanaryDAOImpl) Lock(ctx context.Context, agentConfigID string, expire time.Duration) (bool, error) {
	return d.cache.SetNX(ctx, fmt.Sprintf("%s:%s", LockKeyPrefix, agentConfigID), "1", expire)
}

func (d *DeployCanaryDAOImpl) Unlock(ctx context.Context, agentConfigID string) error {
	return d.cache.Del(ctx, fmt.Sprintf("%s:%s", LockKeyPrefix, agentConfigID))
}
