package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func (d *DAO) BatchCreateSegment(ctx context.Context, segments []*entity.Segment) error {
	if len(segments) == 0 {
		return nil
	}
	segmentPOs := lo.Map(segments, func(segment *entity.Segment, index int) *po.SegmentPO {
		return getSegmentPOFromEntity(segment)
	})
	return d.Conn.NewRequest(ctx).Create(segmentPOs).Error
}

func (d *DAO) MGetSegments(ctx context.Context, ids []int64) ([]*entity.Segment, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	segmentPOs := make([]*po.SegmentPO, 0, len(ids))
	if err := d.Conn.NewRequest(ctx).Where("segment_id in (?)", ids).Find(&segmentPOs).Error; err != nil {
		return nil, errors.WithMessage(err, "failed to get segments by ids")
	}
	return lo.Map(segmentPOs, func(segment *po.SegmentPO, index int) *entity.Segment {
		return getSegmentEntityFromPO(segment)
	}), nil
}

func (d *DAO) GetDocumentSegmentCount(ctx context.Context, datasetID, documentID string) (int64, error) {
	var count int64
	// 显式指定表名 po.SegmentPO
	if err := d.Conn.NewRequest(ctx).Model(&po.SegmentPO{}).Where("dataset_id =? and document_id =?", datasetID, documentID).Count(&count).Error; err != nil {
		return 0, errors.WithMessage(err, "failed to count document segment")
	}
	return count, nil
}

func (d *DAO) UpdateSegmentsByDocumentID(ctx context.Context, documentID string, segments []*entity.Segment) error {
	db := d.Conn.NewRequest(ctx)
	// 先删除所有与documentID相关的segment,再批量插入新的segment
	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Where("document_id = ?", documentID).Delete(&po.SegmentPO{}).Error
		if err != nil {
			return errors.WithMessage(err, "failed to delete segments")
		}
		if len(segments) == 0 {
			return nil
		}
		segmentPOs := lo.Map(segments, func(segment *entity.Segment, index int) *po.SegmentPO {
			return getSegmentPOFromEntity(segment)
		})
		return tx.Create(segmentPOs).Error
	})
	return err
}

func (d *DAO) GetSegmentIDsByDocumentID(ctx context.Context, documentID string) ([]int64, error) {
	var segmentIDs []int64
	err := d.Conn.NewRequest(ctx).Model(&po.SegmentPO{}).Where("document_id =?", documentID).Pluck("segment_id", &segmentIDs).Error
	if err != nil {
		return nil, err
	}
	return segmentIDs, nil
}

func (d *DAO) DeleteSegmentByDocumentID(ctx context.Context, documentID string) error {
	if err := d.Conn.NewRequest(ctx).Where("document_id =?", documentID).Delete(&po.SegmentPO{}).Error; err != nil {
		return err
	}
	return nil
}

func getSegmentPOFromEntity(entity *entity.Segment) *po.SegmentPO {
	ret := &po.SegmentPO{
		SegmentID:  entity.ID,
		DatasetID:  entity.DatasetID,
		DocumentID: entity.DocumentID,
	}
	if entity.Content != nil {
		ret.Content = lo.ToPtr(datatypes.NewJSONType(lo.FromPtr(entity.Content)))
	}
	return ret
}

func getSegmentEntityFromPO(po *po.SegmentPO) *entity.Segment {
	ret := &entity.Segment{
		ID:         po.SegmentID,
		DatasetID:  po.DatasetID,
		DocumentID: po.DocumentID,
	}
	if po.Content != nil {
		ret.Content = lo.ToPtr(po.Content.Data())
	}
	return ret
}
