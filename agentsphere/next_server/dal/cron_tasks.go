package dal

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

// CronTaskDAO 定时任务数据访问接口
//
//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/cron_task_dao.go -package dal code.byted.org/devgpt/kiwis/agentsphere/next_server/dal CronTaskDAO
type CronTaskDAO interface {
	// GetCronTask 获取定时任务
	GetCronTask(ctx context.Context, opt GetCronTaskOption) (*entity.CronTask, error)
	// CreateCronTask 创建定时任务
	CreateCronTask(ctx context.Context, opt CreateCronTaskOption) (*entity.CronTask, error)
	// UpdateCronTask 更新定时任务
	UpdateCronTask(ctx context.Context, opt UpdateCronTaskOption) (*entity.CronTask, error)
	// DeleteCronTask 删除定时任务
	DeleteCronTask(ctx context.Context, uid string) error
	// ListCronTasks 查询定时任务列表
	ListCronTasks(ctx context.Context, opt ListCronTasksOption) (int64, []*entity.CronTask, error)
	// GetPendingTasks 获取待执行的任务（用于调度器）
	GetPendingTasks(ctx context.Context, limit int) ([]*entity.CronTask, error)
	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(ctx context.Context, uid string, status int8) error
	// UpdateExecutionInfo 更新执行信息
	UpdateExecutionInfo(ctx context.Context, opt UpdateExecutionInfoOption) error
}

// GetCronTaskOption 获取定时任务选项
type GetCronTaskOption struct {
	UID    string
	ID     int64
	Sync   bool
	UserID *int64
}

// CreateCronTaskOption 创建定时任务选项
type CreateCronTaskOption struct {
	UserName        string
	UID             string
	Name            string
	Description     *string
	TaskType        entity.TaskType
	TemplateID      string
	Schedule        string
	Timezone        string
	NextExecuteTime time.Time
	NextMQEventID   *string
	TaskStatus      entity.CronTaskStatus
	GroupInfo       entity.GroupInfo
	Role            entity.SessionRole
	Content         *string
	Option          *string
	SpaceID         string
}

// UpdateCronTaskOption 更新定时任务选项
type UpdateCronTaskOption struct {
	UID             string
	Name            *string
	Description     *string
	TaskType        *entity.TaskType
	TemplateID      *string
	Schedule        *string
	Timezone        *string
	NextExecuteTime *time.Time
	NextMQEventID   *string
	TaskStatus      *entity.CronTaskStatus
	GroupInfo       *entity.GroupInfo
	Role            *entity.SessionRole
	Content         string
	Option          *string
	LastRunStatus   *entity.LastRunStatus
}

// ListCronTasksOption 查询定时任务列表选项
type ListCronTasksOption struct {
	UserName   *string
	Query      *string
	TaskStatus *int8
	GroupID    *string
	Offset     int
	Limit      int
	Sync       bool
	SpaceID    string
}

// UpdateExecutionInfoOption 更新执行信息选项
type UpdateExecutionInfoOption struct {
	UID             string
	LastExecuteTime *time.Time
	LastRunStatus   int8
	NextExecuteTime *time.Time
	NextMQEventID   *string
}
