package dal

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/port/db"
)

func (d *DAO) CreateOrUpdateTemplateVariable(ctx context.Context, newVariable *entity.TemplateVariable) (*entity.TemplateVariable, error) {
	oldVariables, err := d.ListTemplateVariables(ctx, ListTemplateVariablesOptions{
		TemplateID: newVariable.TemplateID,
		Creator:    newVariable.Creator,
	})
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to list template variables")
	}
	if len(oldVariables) > 0 {
		// 按照value内容hash去重
		oldUniqueKeys := make([]string, 0, len(oldVariables))
		for _, v := range oldVariables {
			oldUniqueKeys = append(oldUniqueKeys, v.Key)
		}
		newKey := newVariable.Value.GetTemplateFormValueUniqueKey()
		if lo.Contains(oldUniqueKeys, newKey) { // 已经存在，更新后返回
			d.Conn.NewRequest(ctx).Model(&po.NextTemplateVariablePO{}).
				Where("template_id = ? and creator = ? and form_key = ? AND space_id = ?",
					newVariable.TemplateID, newVariable.Creator, newKey, newVariable.SpaceID).
				Updates(map[string]any{"updated_at": time.Now()})
			return nil, nil
		}
	}
	p := getTemplateVariablePOFromEntity(newVariable)
	res := d.Conn.NewRequest(ctx).Create(p)
	if res.Error != nil {
		if db.IsDuplicateKeyError(res.Error) {
			return nil, nil
		}
		return nil, res.Error
	}
	return getTemplateVariableFromPO(p), nil
}

//func (d *DAO) CreateOrUpdateCronTaskTemplateVariable(ctx context.Context, newVariable *entity.TemplateVariable) (*entity.TemplateVariable, error) {
//	oldVariables, err := d.ListTemplateVariables(ctx, ListTemplateVariablesOptions{
//		TemplateID:  newVariable.TemplateID,
//		Creator:     newVariable.Creator,
//		CronTaskUID: newVariable.CronTaskUID,
//	})
//	if err != nil && !db.IsRecordNotFoundError(err) {
//		return nil, errors.WithMessage(err, "failed to list template variables")
//	}
//	if len(oldVariables) > 0 {
//		// 按照value内容hash去重
//		oldUniqueKeys := make([]string, 0, len(oldVariables))
//		for _, v := range oldVariables {
//			oldUniqueKeys = append(oldUniqueKeys, v.Key)
//		}
//		newKey := newVariable.Value.GetTemplateFormValueUniqueKey()
//		if lo.Contains(oldUniqueKeys, newKey) { // 已经存在，更新后返回
//			d.Conn.NewRequest(ctx).Model(&po.NextTemplateVariablePO{}).
//				Where("template_id = ? and creator = ? and form_key = ? AND space_id = ?",
//					newVariable.TemplateID, newVariable.Creator, newKey, newVariable.SpaceID).
//				Updates(map[string]any{"updated_at": time.Now()})
//			return nil, nil
//		}
//	}
//	p := getTemplateVariablePOFromEntity(newVariable)
//	res := d.Conn.NewRequest(ctx).Create(p)
//	if res.Error != nil {
//		if db.IsDuplicateKeyError(res.Error) {
//			return nil, res.Error
//		}
//		return nil, res.Error
//	}
//	return getTemplateVariableFromPO(p), nil
//}

func getTemplateVariableFromPO(po *po.NextTemplateVariablePO) *entity.TemplateVariable {
	return &entity.TemplateVariable{
		ID:         po.Uid,
		Key:        po.FormKey,
		Value:      po.FormValue.Data(),
		TemplateID: po.TemplateID,
		Creator:    po.Creator,
		SpaceID:    po.SpaceID,
		CreatedAt:  po.CreatedAt,
		UpdatedAt:  po.UpdatedAt,
	}
}

func getTemplateVariablePOFromEntity(e *entity.TemplateVariable) *po.NextTemplateVariablePO {
	return &po.NextTemplateVariablePO{
		Uid:        e.ID,
		FormKey:    e.Key,
		FormValue:  datatypes.NewJSONType(e.Value),
		TemplateID: e.TemplateID,
		Creator:    e.Creator,
		SpaceID:    e.SpaceID,
	}
}

type ListTemplateVariablesOptions struct {
	TemplateID string
	Creator    string
	SpaceID    string
	Sync       bool
}

func (d *DAO) ListTemplateVariables(ctx context.Context, opt ListTemplateVariablesOptions) ([]*entity.TemplateVariable, error) {
	req := d.Conn.NewRequest(ctx).Model(&po.NextTemplateVariablePO{})

	if opt.TemplateID != "" {
		req = req.Where("template_id = ?", opt.TemplateID)
	}
	if opt.Creator != "" {
		req = req.Where("creator = ?", opt.Creator)
	}
	if opt.SpaceID != "" {
		req = req.Where("space_id = ?", opt.SpaceID)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	var res []*po.NextTemplateVariablePO
	if err := req.Order("updated_at desc").Find(&res).Error; err != nil {
		return nil, err
	}
	return lo.Map(res, func(item *po.NextTemplateVariablePO, _ int) *entity.TemplateVariable {
		return getTemplateVariableFromPO(item)
	}), nil
}

func (d *DAO) GetLastTemplateVariable(ctx context.Context, opt ListTemplateVariablesOptions) (*entity.TemplateVariable, error) {
	req := d.Conn.NewRequest(ctx).Model(&po.NextTemplateVariablePO{})
	if opt.TemplateID != "" {
		req = req.Where("template_id = ?", opt.TemplateID)
	}
	if opt.Creator != "" {
		req = req.Where("creator = ?", opt.Creator)
	}
	if opt.SpaceID != "" {
		req = req.Where("space_id = ?", opt.SpaceID)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	var res *po.NextTemplateVariablePO
	if err := req.Order("updated_at desc").Find(&res).Error; err != nil {
		return nil, err
	}
	return getTemplateVariableFromPO(res), nil
}
