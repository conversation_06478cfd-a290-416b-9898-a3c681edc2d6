package dal

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/plugin/dbresolver"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

type CreateMessageOption struct {
	ID          string
	SessionID   string
	TaskID      string
	Role        entity.MessageRole
	Content     entity.MessageContent
	Creator     string
	Attachments []*entity.Attachment
	Options     entity.MessageOptions
	Status      entity.MessageStatus
	Mentions    []*agententity.Mention
}

func (d *DAO) CreateMessage(ctx context.Context, opt CreateMessageOption) (*entity.Message, error) {
	p := &po.NextMessagePO{
		Uid:         opt.ID,
		SessionID:   opt.SessionID,
		TaskID:      opt.TaskID,
		Role:        string(opt.Role),
		Content:     datatypes.NewJSONType(opt.Content),
		Creator:     opt.Creator,
		Attachments: datatypes.NewJSONType(opt.Attachments),
		Options:     lo.ToPtr(datatypes.NewJSONType(opt.Options)),
		Status:      int(opt.Status),
		Mentions:    lo.ToPtr(datatypes.NewJSONType(opt.Mentions)),
	}
	res := d.Conn.NewRequest(ctx).Create(p)

	if res.Error != nil {
		return nil, res.Error
	}
	return getMessageFromPO(p), nil
}

type UpdateMessageOption struct {
	ID     string
	Status *entity.MessageStatus
}

func (d *DAO) UpdateMessage(ctx context.Context, opt UpdateMessageOption) (*entity.Message, error) {
	if opt.ID == "" {
		return nil, errors.New("message id is empty")
	}

	query := d.Conn.NewRequest(ctx).Model(&po.NextMessagePO{}).Where("uid = ?", opt.ID)
	query = query.Unscoped()

	updater := map[string]any{}
	if opt.Status != nil {
		updater["status"] = int(*opt.Status)
	}

	res := query.Updates(updater)
	if res.Error != nil {
		return nil, res.Error
	}
	if res.RowsAffected == 0 {
		optJSON, _ := sonic.Marshal(opt)
		logs.V1.CtxWarn(ctx, "no rows affected when update session: %s", optJSON)
	}

	return d.GetMessage(ctx, GetMessageOption{
		ID:   opt.ID,
		Sync: true,
	})
}

func getMessageFromPO(p *po.NextMessagePO) *entity.Message {
	message := &entity.Message{
		ID:          p.Uid,
		SessionID:   p.SessionID,
		TaskID:      p.TaskID,
		Role:        entity.MessageRole(p.Role),
		Content:     p.Content.Data(),
		Attachments: p.Attachments.Data(),
		Status:      entity.MessageStatus(p.Status),
		Creator:     p.Creator,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}
	if p.Options != nil {
		message.Options = p.Options.Data()
	}
	if p.Mentions != nil {
		message.Mentions = p.Mentions.Data()
	}
	return message
}

type ListMessageOption struct {
	SessionID string
	Sync      bool
}

func (d *DAO) ListMessages(ctx context.Context, opt ListMessageOption) ([]*entity.Message, error) {
	p := make([]*po.NextMessagePO, 0)
	req := d.Conn.NewRequest(ctx)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.Where("session_id = ?", opt.SessionID).Find(&p)
	if res.Error != nil {
		return nil, res.Error
	}

	messages := make([]*entity.Message, 0, len(p))
	for _, item := range p {
		messages = append(messages, getMessageFromPO(item))
	}
	return messages, nil
}

type GetMessageOption struct {
	ID   string
	Sync bool
}

func (d *DAO) GetMessage(ctx context.Context, opt GetMessageOption) (*entity.Message, error) {
	p := &po.NextMessagePO{}
	req := d.Conn.NewRequest(ctx).Where("uid = ?", opt.ID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.First(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getMessageFromPO(p), nil
}

func (d *DAO) GetLatestMessageByRole(ctx context.Context, sessionID string, role entity.MessageRole, sync bool) (*entity.Message, error) {
	p := &po.NextMessagePO{}
	req := d.Conn.NewRequest(ctx)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.Where("session_id = ? AND role = ?", sessionID, role).Order("id DESC").Limit(1).Find(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getMessageFromPO(p), nil
}

func (d *DAO) GetFirstAgentReply(ctx context.Context, sessionID string) (*entity.Message, error) {
	p := &po.NextMessagePO{}
	res := d.Conn.NewRequest(ctx).Where("session_id = ? AND role = ?", sessionID, entity.MessageRoleAssistant).Order("id ASC").Limit(1).Find(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getMessageFromPO(p), nil
}

func (d *DAO) GetLastAgentReply(ctx context.Context, sessionID string) (*entity.Message, error) {
	p := &po.NextMessagePO{}
	res := d.Conn.NewRequest(ctx).Where("session_id = ? AND role = ?", sessionID, entity.MessageRoleAssistant).Order("id DESC").Limit(1).Find(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getMessageFromPO(p), nil
}

type BatchGetMessagesOption struct {
	SessionIDs []string
	Role       entity.MessageRole
	Sync       bool
}

func (d *DAO) BatchGetMessages(ctx context.Context, opt BatchGetMessagesOption) ([]*entity.Message, error) {
	if len(opt.SessionIDs) == 0 {
		return []*entity.Message{}, nil
	}
	p := make([]*po.NextMessagePO, 0)
	req := d.Conn.NewRequest(ctx).Where("session_id in ?", opt.SessionIDs)
	if opt.Role != "" {
		req = req.Where("role = ?", opt.Role)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	res := req.Find(&p)
	if res.Error != nil {
		return nil, res.Error
	}
	messages := make([]*entity.Message, 0, len(p))
	for _, item := range p {
		messages = append(messages, getMessageFromPO(item))
	}
	return messages, nil
}

type CountMessagesOption struct {
	SessionID string
	Sync      bool
	Role      *entity.MessageRole
}

func (d *DAO) CountMessages(ctx context.Context, opt CountMessagesOption) (int64, error) {
	p := &po.NextMessagePO{}
	req := d.Conn.NewRequest(ctx).Model(p).Where("session_id = ?", opt.SessionID)
	if opt.Role != nil {
		req = req.Where("role = ?", *opt.Role)
	}
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	var total int64
	if err := req.Count(&total).Error; err != nil {
		return 0, err
	}

	return total, nil
}
