package dal

import (
	"context"
	"encoding/json"
	"reflect"

	"code.byted.org/gopkg/logs/v2/log"
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

func (d *DAO) GetArtifactID(ctx context.Context, opt GetArtifactOption) (int64, error) {
	req := d.Conn.NewRequest(ctx).Where("uid = ?", opt.ID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextArtifactPO)
	if err := req.First(p).Error; err != nil {
		return -1, errors.Wrapf(err, "failed to get artifact id %s", opt.ID)
	}
	return p.ID, nil
}

type GetArtifactOption struct {
	ID   string
	Sync bool
}

func (d *DAO) GetArtifact(ctx context.Context, opt GetArtifactOption) (*entity.Artifact, error) {
	req := d.Conn.NewRequest(ctx).Where("uid = ?", opt.ID)
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextArtifactPO)
	if err := req.First(p).Error; err != nil {
		return nil, errors.Wrapf(err, "failed to get artifact %s", opt.ID)
	}
	return getArtifactFromPO(p), nil
}

type GetLatestArtifactOption struct {
	SessionID string
	Type      []entity.ArtifactType
}

func (d *DAO) GetLatestArtifactByType(ctx context.Context, opt GetLatestArtifactOption) (*entity.Artifact, error) {
	req := d.Conn.NewRequest(ctx).Model(&po.NextArtifactPO{}).Where("session_id = ?", opt.SessionID)
	if len(opt.Type) > 0 {
		req = req.Where("type IN ?", opt.Type)
	}

	var artifactPO *po.NextArtifactPO
	if err := req.Order("created_at DESC").Limit(1).Find(&artifactPO).Error; err != nil {
		return nil, errors.Wrapf(err, "failed to get latest artifact %s, type: %v", opt.SessionID, opt.Type)
	}
	return getArtifactFromPO(artifactPO), nil
}

func (d *DAO) GetArtifactByArtifactKey(ctx context.Context, sessionID string, artifactKey string, version int32, sync bool) (*entity.Artifact, error) {
	req := d.Conn.NewRequest(ctx).Where("session_id = ?", sessionID).Where("artifact_key = ?", artifactKey).Where("version = ?", version)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	p := new(po.NextArtifactPO)
	if err := req.First(p).Error; err != nil {
		return nil, errors.Wrapf(err, "failed to get artifact %s,%s", sessionID, artifactKey)
	}
	return getArtifactFromPO(p), nil
}

type CreateArtifactOption struct {
	SessionID string
	ID        string
	Type      entity.ArtifactType
	Status    entity.ArtifactStatus
	Source    entity.ArtifactSource
	Key       string
	Version   int32
	Files     entity.FileMetas
	Metadata  entity.ArtifactMetadata
	Display   bool
}

func (d *DAO) CreateArtifact(ctx context.Context, opt CreateArtifactOption) (*entity.Artifact, error) {
	metadata, err := json.Marshal(opt.Metadata)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to marshal metadata")
	}
	if opt.Files == nil {
		opt.Files = []entity.FileMeta{}
	}
	p := &po.NextArtifactPO{
		SessionID:   opt.SessionID,
		Uid:         opt.ID,
		Type:        string(opt.Type),
		Status:      string(opt.Status),
		Source:      string(opt.Source),
		ArtifactKey: opt.Key,
		Version:     int(opt.Version),
		FileMetas:   lo.ToPtr(datatypes.NewJSONType(opt.Files)),
		Metadata:    lo.ToPtr(datatypes.NewJSONType(lo.ToPtr(json.RawMessage(metadata)))),
		Display:     lo.Ternary(opt.Display, 1, 0),
	}
	res := d.Conn.NewRequest(ctx).Create(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getArtifactFromPO(p), nil
}

type UpdateArtifactOption struct {
	ID        string
	Status    *entity.ArtifactStatus
	Files     *datatypes.JSONType[entity.FileMetas] // this overrides all files. to update files list atomically, use UpdateArtifactFiles instead
	Metadata  any
	Display   *bool
	SessionID *string
}

func (d *DAO) UpdateArtifact(ctx context.Context, opt UpdateArtifactOption) (*entity.Artifact, error) {
	if opt.ID == "" {
		return nil, errors.New("ID is required")
	}

	updateMap := map[string]any{}
	if opt.Status != nil {
		updateMap["status"] = *opt.Status
	}
	if opt.Files != nil {
		updateMap["file_metas"] = *opt.Files
	}
	if opt.Display != nil {
		updateMap["display"] = lo.Ternary(*opt.Display, 1, 0)
	}
	if opt.Metadata != nil {
		log.V1.CtxInfo(ctx, "update artifact metadata: %+v", opt.Metadata)
		updateMap["metadata"] = datatypes.NewJSONType(opt.Metadata)
	}
	if pointer.Get(opt.SessionID) != "" {
		updateMap["session_id"] = pointer.Get(opt.SessionID)
	}

	res := d.Conn.NewRequest(ctx).Debug().Model(&po.NextArtifactPO{}).Where("uid = ?", opt.ID).Updates(updateMap)
	if res.Error != nil {
		return nil, res.Error
	}

	return d.GetArtifact(ctx, GetArtifactOption{ID: opt.ID, Sync: true})
}

type UpdateArtifactFnOption struct {
	ID string
	// update artifact atomically, only files, status and metadata are mutable
	UpdateArtifact func(*entity.Artifact)
}

func (d *DAO) UpdateArtifactFiles(ctx context.Context, opt UpdateArtifactFnOption) (res *entity.Artifact, err error) {
	if opt.ID == "" {
		return nil, errors.New("ID is required")
	}

	err = d.Conn.NewRequest(ctx).Transaction(func(tx *gorm.DB) error {
		var artifactPO *po.NextArtifactPO
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("uid = ?", opt.ID).
			First(&artifactPO).Error; err != nil {
			return err
		}

		artifact := getArtifactFromPO(artifactPO)
		if opt.UpdateArtifact != nil {
			opt.UpdateArtifact(artifact)
		}
		artifactPO.FileMetas = lo.ToPtr(datatypes.NewJSONType(artifact.FileMetas))
		artifactPO.Status = string(artifact.Status)
		metadata, err := json.Marshal(artifact.Metadata)
		if err != nil {
			return err
		}
		artifactPO.Metadata = lo.ToPtr(datatypes.NewJSONType(lo.ToPtr(json.RawMessage(metadata))))
		log.V1.CtxInfo(ctx, "update artifact files: %+v", artifactPO)
		if err := tx.Save(artifactPO).Error; err != nil {
			return err
		}
		res = getArtifactFromPO(artifactPO)
		return nil
	})

	return
}

type ListArtifactsOption struct {
	SessionID   string
	Key         *string
	ArtifactIDs []string
	Status      []entity.ArtifactStatus
	Type        []entity.ArtifactType
	Display     *bool
}

func (d *DAO) ListArtifacts(ctx context.Context, opt ListArtifactsOption) ([]*entity.Artifact, error) {
	if opt.SessionID == "" {
		return nil, errors.New("assignment_id or session_id is required")
	}

	var artifacts []*po.NextArtifactPO
	req := d.Conn.NewRequest(ctx).Model(&po.NextArtifactPO{})
	req = req.Where("session_id = ?", opt.SessionID)

	if opt.Key != nil {
		req = req.Where("artifact_key = ?", *opt.Key)
	}

	if len(opt.ArtifactIDs) > 0 {
		req = req.Where("uid IN ?", opt.ArtifactIDs)
	}
	if len(opt.Type) > 0 {
		req = req.Where("type in ?", opt.Type)
	}
	if len(opt.Status) > 0 {
		req = req.Where("status in ?", opt.Status)
	}
	if opt.Display != nil {
		req = req.Where("display = ?", lo.Ternary(*opt.Display, 1, 0))
	}

	if err := req.Find(&artifacts).Error; err != nil {
		return nil, err
	}

	return lo.Map(artifacts, func(p *po.NextArtifactPO, _ int) *entity.Artifact {
		return getArtifactFromPO(p)
	}), nil
}

func getArtifactFromPO(p *po.NextArtifactPO) *entity.Artifact {
	artifact := &entity.Artifact{
		SessionID: p.SessionID,
		ID:        p.Uid,
		Type:      entity.ArtifactType(p.Type),
		Status:    entity.ArtifactStatus(p.Status),
		Source:    entity.ArtifactSource(p.Source),
		Key:       p.ArtifactKey,
		Version:   int32(p.Version),
		FileMetas: nil,
		CreatedAt: p.CreatedAt,
		Metadata:  nil,
		Display:   lo.Ternary(p.Display == 1, true, false),
	}
	if p.FileMetas != nil {
		artifact.FileMetas = p.FileMetas.Data()
	}
	if p.Metadata == nil || reflect.ValueOf(p.Metadata.Data()).IsZero() {
		return artifact
	}

	metadata, err := entity.UnmarshalMetadata(artifact.Type, *p.Metadata.Data())
	if err != nil {
		log.V1.Error("failed to unmarshal artifact metadata: %v", err)
	}
	artifact.Metadata = metadata
	return artifact
}

type GetSessionArtifactIDsOption struct {
	Types   []entity.ArtifactType
	Display *bool
	Sync    bool
}

func (d *DAO) GetSessionArtifactIDs(ctx context.Context, sessionID string, opt GetSessionArtifactIDsOption) ([]string, error) {
	var artifacts []*po.NextArtifactPO
	req := d.Conn.NewRequest(ctx).Model(&po.NextArtifactPO{})
	req = req.Where("session_id = ?", sessionID).Select("uid")

	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}
	if len(opt.Types) > 0 {
		req = req.Where("type in ?", opt.Types)
	}
	if opt.Display != nil {
		req = req.Where("display = ?", lo.Ternary(*opt.Display, 1, 0))
	}
	if err := req.Find(&artifacts).Error; err != nil {
		return nil, err
	}
	return lo.Map(artifacts, func(p *po.NextArtifactPO, _ int) string {
		return p.Uid
	}), nil
}

func (d *DAO) GetLatestVersionArtifactByKey(ctx context.Context, sessionID string, artifactKey string, sync bool) (*entity.Artifact, error) {
	if sessionID == "" {
		return nil, errors.New("sessionID cannot be empty")
	}
	if artifactKey == "" {
		return nil, errors.New("artifactKey cannot be empty")
	}
	req := d.Conn.NewRequest(ctx).
		Where("session_id = ?", sessionID).
		Where("artifact_key = ?", artifactKey)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}

	p := new(po.NextArtifactPO)
	err := req.
		Order("id desc").
		First(&p).Error
	if err != nil {
		return nil, err
	}
	return getArtifactFromPO(p), nil
}
