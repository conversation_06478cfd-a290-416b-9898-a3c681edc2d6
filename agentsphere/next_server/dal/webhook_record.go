package dal

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/AlekSi/pointer"
)

type CreateWebhookRecordOption struct {
	ID                   string
	TaskID               string
	SessionID            string
	URL                  string
	RequestHeaders       string
	RequestData          string
	ResponseHeaders      string
	ResponseBody         string
	ResponseStatus       string
	ExecutionDuration    time.Duration
	InternalErrorMessage string
}

func (d *DAO) CreateWebhookRecord(ctx context.Context, opt CreateWebhookRecordOption) (*entity.WebhookRecord, error) {
	p := &po.NextWebhookRecordPO{
		Uid:                  opt.ID,
		TaskID:               opt.TaskID,
		SessionID:            opt.SessionID,
		URL:                  opt.URL,
		RequestHeaders:       &opt.RequestHeaders,
		RequestData:          &opt.RequestData,
		ResponseHeaders:      &opt.ResponseHeaders,
		ResponseBody:         &opt.ResponseBody,
		ResponseStatus:       &opt.ResponseStatus,
		ExecutionDuration:    pointer.To(float64(opt.ExecutionDuration)),
		InternalErrorMessage: &opt.InternalErrorMessage,
	}
	res := d.Conn.NewRequest(ctx).Create(p)

	if res.Error != nil {
		return nil, res.Error
	}
	return getWebhookRecordFromPO(p), nil
}

func getWebhookRecordFromPO(po *po.NextWebhookRecordPO) *entity.WebhookRecord {
	return &entity.WebhookRecord{
		ID:                   po.Uid,
		TaskID:               po.TaskID,
		SessionID:            po.SessionID,
		URL:                  po.URL,
		RequestHeaders:       pointer.Get(po.RequestHeaders),
		RequestData:          pointer.Get(po.RequestData),
		ResponseHeaders:      pointer.Get(po.ResponseHeaders),
		ResponseBody:         pointer.Get(po.ResponseBody),
		ResponseStatus:       pointer.Get(po.ResponseStatus),
		ExecutionDuration:    pointer.Get(po.ExecutionDuration),
		InternalErrorMessage: pointer.Get(po.InternalErrorMessage),
		CreatedAt:            po.CreatedAt,
		UpdatedAt:            po.UpdatedAt,
	}
}
