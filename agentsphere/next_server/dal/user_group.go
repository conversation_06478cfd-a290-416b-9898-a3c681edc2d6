package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/samber/lo"
	"gorm.io/plugin/dbresolver"
)

type AddUserGroupOption struct {
	ID        string
	Username  string
	GroupName string
}

func (d *DAO) AddUserGroup(ctx context.Context, opt AddUserGroupOption) (*entity.UserGroup, error) {
	p := &po.NextUserGroupPO{
		Uid:       opt.ID,
		Username:  opt.Username,
		GroupName: opt.GroupName,
	}

	if err := d.Conn.NewRequest(ctx).Create(p).Error; err != nil {
		return nil, err
	}
	return getUserGroupEntityFromPO(p), nil
}

type ListUserGroupOption struct {
	Username string
	Sync     bool
}

func (d *DAO) ListUserGroup(ctx context.Context, opt ListUserGroupOption) ([]*entity.UserGroup, error) {
	var userGroups []*po.NextUserGroupPO

	req := d.Conn.NewRequest(ctx).Model(&po.NextUserGroupPO{})
	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	req = req.Where("username = ?", opt.Username)

	if err := req.Find(&userGroups).Error; err != nil {
		return nil, err
	}

	return lo.Map(userGroups, func(item *po.NextUserGroupPO, index int) *entity.UserGroup {
		return getUserGroupEntityFromPO(item)
	}), nil
}

func getUserGroupEntityFromPO(item *po.NextUserGroupPO) *entity.UserGroup {
	return &entity.UserGroup{
		ID:        item.Uid,
		Username:  item.Username,
		GroupName: item.GroupName,
	}
}
