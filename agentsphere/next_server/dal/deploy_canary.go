package dal

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

type CreateDeployCanaryConfigOption struct {
	AgentID              string
	AgentConfigVersionID string
	RatioLimit           float64
}

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/deploy_canary_dao.go -package dal code.byted.org/devgpt/kiwis/agentsphere/next_server/dal DeployCanaryDAO
type DeployCanaryDAO interface {
	Lock(ctx context.Context, agentConfigID string, expire time.Duration) (bool, error)
	Unlock(ctx context.Context, agentConfigID string) error
	CreateDeployCanaryConfig(ctx context.Context, opt CreateDeployCanaryConfigOption) error
	GetDeployCanaryConfig(ctx context.Context, agentID string) (string, float64, error)
	DelDeployCanaryConfig(ctx context.Context, agentID string) error

	StartCanaryTask(ctx context.Context, agentConfigVersionID string, sessionID string) error
	UpdateCanaryTaskStatus(ctx context.Context, agentConfigVersionID string, sessionID string, status entity.SessionStatus) error
	GetCanaryTaskStatus(ctx context.Context, agentConfigVersionID string) (int64, int64, int64, error)
}
