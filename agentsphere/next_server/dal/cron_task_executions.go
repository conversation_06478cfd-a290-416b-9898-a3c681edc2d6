package dal

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

// TaskExecutionDAO 任务执行记录数据访问接口
//
//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/dal/task_execution_dao.go -package dal code.byted.org/devgpt/kiwis/agentsphere/next_server/dal TaskExecutionDAO
type TaskExecutionDAO interface {
	// GetTaskExecution 获取任务执行记录
	GetTaskExecution(ctx context.Context, opt GetTaskExecutionOption) (*entity.TaskExecution, error)
	// GetLastTaskExecutionByTaskUID 通过任务UID获取最近一次执行记录
	GetLastTaskExecutionByTaskUID(ctx context.Context, opt GetLastTaskExecutionByTaskUIDOption) (*entity.TaskExecution, error)
	// CreateTaskExecution 创建任务执行记录
	CreateTaskExecution(ctx context.Context, opt CreateTaskExecutionOption) (*entity.TaskExecution, error)
	// UpdateTaskExecution 更新任务执行记录
	UpdateTaskExecution(ctx context.Context, opt UpdateTaskExecutionOption) (*entity.TaskExecution, error)
	// ListTaskExecutions 查询任务执行记录列表
	ListTaskExecutions(ctx context.Context, opt ListTaskExecutionsOption) (int64, []*entity.TaskExecution, error)
}

// GetTaskExecutionOption 获取任务执行记录选项
type GetTaskExecutionOption struct {
	SessionID         string
	LarkMessageStatus *entity.TaskExecutionLarkMessageStatus
	Sync              bool
}

// GetLastTaskExecutionByTaskUIDOption 通过任务UID获取最近一次执行记录选项
type GetLastTaskExecutionByTaskUIDOption struct {
	UID  string
	Sync bool
}

// CreateTaskExecutionOption 创建任务执行记录选项
type CreateTaskExecutionOption struct {
	TaskUID           string
	Username          string
	ExecuteTime       time.Time
	Status            entity.TaskExecutionStatus
	ErrorMessage      *string
	SessionID         string
	TriggerType       entity.TriggerType
	MQEventID         *string
	LarkMessageID     *string
	LarkMessageStatus entity.TaskExecutionLarkMessageStatus
	Config            *entity.TaskConfig
}

// UpdateTaskExecutionOption 更新任务执行记录选项
type UpdateTaskExecutionOption struct {
	SessionID         string
	Status            *int8
	ErrorMessage      *string
	ExecutionDuration *int32
	LarkMessageStatus *entity.TaskExecutionLarkMessageStatus
}

// ListTaskExecutionsOption 查询任务执行记录列表选项
type ListTaskExecutionsOption struct {
	TaskUID   *string
	UserID    *int64
	Status    *int8
	StartTime *time.Time
	EndTime   *time.Time
	Limit     int
	Offset    int
	Sync      bool
	SpaceID   string
}
