package dal

import (
	"context"
	"time"

	"gorm.io/plugin/dbresolver"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

func (d *DAO) GetLarkUser(ctx context.Context, username string, sync bool) (*entity.LarkUser, error) {
	larkUserPO := &po.LarkUserPO{}
	req := d.Conn.NewRequest(ctx).Where("username = ?", username)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	err := req.First(&larkUserPO).Error
	if err != nil {
		return nil, err
	}
	return getLarkUserEntityFromPO(larkUserPO), nil
}

func (d *DAO) GetLarkUserByID(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
	larkUserPO := &po.LarkUserPO{}
	req := d.Conn.NewRequest(ctx).Where("open_id = ?", userID)
	if sync {
		req = req.Clauses(dbresolver.Write)
	}
	err := req.First(&larkUserPO).Error
	if err != nil {
		return nil, err
	}
	return getLarkUserEntityFromPO(larkUserPO), nil
}

func (d *DAO) UpsertLarkUserToken(ctx context.Context, user *entity.LarkUser) error {
	if user == nil {
		return nil
	}
	existLarkUserPO := &po.LarkUserPO{}
	err := d.Conn.NewRequest(ctx).Where("username =?", user.Username).First(existLarkUserPO).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.WithMessage(err, "get lark user failed")
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		larkUserPO := getLarkUserPOFromEntity(user)
		err = d.Conn.NewRequest(ctx).Create(larkUserPO).Error
		if err != nil {
			return errors.WithMessage(err, "create lark user failed")
		}
	} else {
		updateMap := map[string]any{}
		updateMap["access_token"] = user.AccessToken
		updateMap["refresh_token"] = user.RefreshToken
		updateMap["access_expire_at"] = user.AccessExpireAt
		updateMap["refresh_expire_at"] = user.RefreshExpireAt
		updateMap["scope"] = user.Scope
		updateMap["authorization_denied"] = user.AuthorizationDenied
		err = d.Conn.NewRequest(ctx).Model(&po.LarkUserPO{}).Where("id = ? and username =?", existLarkUserPO.ID, user.Username).
			Updates(updateMap).Error
		if err != nil {
			return errors.WithMessage(err, "update lark user failed")
		}
	}
	return nil
}

type UpdateLarkUserTicketOption struct {
	Username string
	Ticket   string
	ExpireAt time.Time
}

func (d *DAO) UpdateUserJSTicket(ctx context.Context, opt UpdateLarkUserTicketOption) error {
	if opt.Username == "" {
		return errors.New("username is required")
	}

	updateMap := map[string]any{}
	updateMap["jsapi_ticket"] = opt.Ticket
	updateMap["jsticket_expire_at"] = opt.ExpireAt
	result := d.Conn.NewRequest(ctx).Model(&po.LarkUserPO{}).Where("username =?", opt.Username).
		Updates(updateMap)
	if result.Error != nil {
		return errors.New("update lark user ticket failed")
	}
	return nil
}

type UpdateLarkUserOpenIDOption struct {
	Username string
	OpenID   string
}

func (d *DAO) UpdateUserOpenID(ctx context.Context, opt UpdateLarkUserOpenIDOption) error {
	if opt.Username == "" {
		return errors.New("username is required")
	}

	updateMap := map[string]any{}
	updateMap["open_id"] = opt.OpenID
	result := d.Conn.NewRequest(ctx).Model(&po.LarkUserPO{}).Where("username =?", opt.Username).
		Updates(updateMap)
	if result.Error != nil {
		return errors.New("update lark user open_id failed")
	}
	return nil
}

func (d *DAO) UpdateLarkUserEmail(ctx context.Context, username string, email string) error {
	if username == "" || email == "" {
		return errors.New("username and email is required")
	}

	updateMap := map[string]any{}
	updateMap["email"] = email
	result := d.Conn.NewRequest(ctx).Model(&po.LarkUserPO{}).Where("username =?", username).
		Updates(updateMap)
	if result.Error != nil {
		return errors.New("update lark user email failed")
	}
	return nil
}

func getLarkUserPOFromEntity(user *entity.LarkUser) *po.LarkUserPO {
	if user == nil {
		return nil
	}
	return &po.LarkUserPO{
		Username:            user.Username,
		Email:               user.Email,
		OpenID:              user.OpenID,
		AccessToken:         lo.ToPtr(user.AccessToken),
		RefreshToken:        lo.ToPtr(user.RefreshToken),
		JsapiTicket:         lo.ToPtr(user.JSApiTicket),
		AccessExpireAt:      user.AccessExpireAt,
		RefreshExpireAt:     user.RefreshExpireAt,
		JsticketExpireAt:    user.JSApiTicketExpireAt,
		Scope:               lo.ToPtr(user.Scope),
		AuthorizationDenied: user.AuthorizationDenied,
	}
}

func getLarkUserEntityFromPO(user *po.LarkUserPO) *entity.LarkUser {
	if user == nil {
		return nil
	}
	return &entity.LarkUser{
		Username:            user.Username,
		Email:               user.Email,
		OpenID:              user.OpenID,
		AccessToken:         lo.FromPtr(user.AccessToken),
		RefreshToken:        lo.FromPtr(user.RefreshToken),
		JSApiTicket:         lo.FromPtr(user.JsapiTicket),
		AccessExpireAt:      user.AccessExpireAt,
		RefreshExpireAt:     user.RefreshExpireAt,
		JSApiTicketExpireAt: user.JsticketExpireAt,
		Scope:               lo.FromPtr(user.Scope),
		AuthorizationDenied: user.AuthorizationDenied,
	}
}
