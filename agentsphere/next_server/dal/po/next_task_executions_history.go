// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_task_executions_history`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextTaskExecutionsHistoryPO is 任务执行记录表.
type NextTaskExecutionsHistoryPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// CreatedAt is 创建时间.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 上一次更新时间.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is 软删除时间戳.
	//
	// index: idx_external_uid, priority: 2.
	//
	// index: idx_username, priority: 2.
	//
	// index: idx_status, priority: 2.
	//
	// index: idx_session_id, priority: 2.
	//
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
	// ExternalType is 触发类型：1-定时触发，2-手动重试，3-事件触发, 4-Cdoebase, 5-<PERSON>lardar.
	ExternalType int `gorm:"column:external_type"`
	// ExternalUid is 关联类型 id，比如可能是定时任务 id，也可以为空.
	//
	// index: idx_external_uid, priority: 1.
	//
	ExternalUid string `gorm:"column:external_uid;size:100"`
	// ExecuteTime is 实际执行时间.
	//
	// index: idx_execute_time, priority: 1.
	//
	ExecuteTime time.Time `gorm:"column:execute_time"`
	// Config is 任务统一的配置，包括通知群的配置，通知人的配置等等.
	Config *datatypes.JSONType[NextTaskExecutionsHistoryConfig] `gorm:"column:config"`
	// Status is 运行任务的结果：1 成功 2 失败.
	//
	// index: idx_status, priority: 1.
	//
	Status int `gorm:"column:status"`
	// ErrorMessage is 错误信息.
	ErrorMessage *string `gorm:"column:error_message"`
	// SessionID is 对话ID.
	//
	// index: idx_session_id, priority: 1.
	//
	SessionID string `gorm:"column:session_id;size:100"`
	// Username is 用户名称.
	//
	// index: idx_username, priority: 1.
	//
	Username string `gorm:"column:username;size:50"`
	// MqEventID is MQ的消息ID.
	//
	// index: idx_mq_event_id, priority: 1.
	//
	MqEventID *string `gorm:"column:mq_event_id;size:64"`
	// LarkMessageID is lark的消息ID.
	LarkMessageID *string `gorm:"column:lark_message_id;size:64"`
	// LarkMessageStatus is 1 发送失败 2 发送成功.
	LarkMessageStatus int8 `gorm:"column:lark_message_status"`
}

// TableName returns PO's corresponding DB table name: `next_task_executions_history`.
func (*NextTaskExecutionsHistoryPO) TableName() string {
	return "next_task_executions_history"
}
