// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_cron_task_template_variable`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextCronTaskTemplateVariablePO is next_cron_task_template_variable.
type NextCronTaskTemplateVariablePO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: idx_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// FormKey is key.
	FormKey string `gorm:"column:form_key;size:64"`
	// FormValue is value.
	FormValue datatypes.JSONType[NextCronTaskTemplateVariableFormValue] `gorm:"column:form_value"`
	// TemplateID is template ID, usually UUID.
	//
	// index: idx_template_id, priority: 1.
	//
	TemplateID string `gorm:"column:template_id;size:40"`
	// CronTaskUid is 定时任务unique ID.
	//
	// index: idx_cron_task_uid, priority: 1.
	//
	CronTaskUid string `gorm:"column:cron_task_uid;size:100"`
	// Creator is creator.
	Creator string `gorm:"column:creator;size:64"`
	// SpaceID is space id.
	SpaceID string `gorm:"column:space_id;size:40"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_cron_task_template_variable`.
func (*NextCronTaskTemplateVariablePO) TableName() string {
	return "next_cron_task_template_variable"
}
