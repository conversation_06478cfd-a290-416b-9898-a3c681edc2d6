// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_cron_tasks`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextCronTasksPO is 定时任务配置表.
type NextCronTasksPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// CreatedAt is 创建时间.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is 上一次更新时间.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is 软删除时间戳.
	//
	// index: idx_next_execute_time, priority: 3.
	//
	// index: idx_task_status, priority: 2.
	//
	// index: idx_space_id, priority: 2.
	//
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
	// Username is 用户名称.
	//
	// index: idx_username, priority: 1.
	//
	Username string `gorm:"column:username;size:50"`
	// Uid is 定时任务unique ID.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:100"`
	// Name is 任务名称.
	Name string `gorm:"column:name;size:255"`
	// Description is 任务描述.
	Description *string `gorm:"column:description"`
	// TaskType is 任务类型：1-AimeBot，2-自动拉群，3-绑定现有群.
	TaskType int `gorm:"column:task_type"`
	// TemplateID is AI模板ID.
	TemplateID string `gorm:"column:template_id;size:100"`
	// Schedule is Cron表达式.
	Schedule string `gorm:"column:schedule;size:100"`
	// Timezone is 时区.
	Timezone string `gorm:"column:timezone;size:50"`
	// NextExecuteTime is 下次执行时间.
	//
	// index: idx_next_execute_time, priority: 1.
	//
	NextExecuteTime time.Time `gorm:"column:next_execute_time"`
	// NextMqEventID is 下次执行的MQ的消息ID，用于区分是否为废弃消息.
	//
	// index: idx_next_mq_event_id, priority: 1.
	//
	NextMqEventID *string `gorm:"column:next_mq_event_id;size:64"`
	// LastExecuteTime is 最后执行时间.
	LastExecuteTime *time.Time `gorm:"column:last_execute_time"`
	// LastRunStatus is 最后执行状态：0-未执行，1-成功，2-失败，3-运行中.
	LastRunStatus int `gorm:"column:last_run_status"`
	// TaskStatus is 任务状态：0-停用，1-启用，2-暂停.
	//
	// index: idx_next_execute_time, priority: 2.
	//
	// index: idx_task_status, priority: 1.
	//
	TaskStatus int `gorm:"column:task_status"`
	// GroupInfo is 群组信息.
	GroupInfo datatypes.JSONType[NextCronTasksGroupInfo] `gorm:"column:group_info"`
	// Role is session role.
	Role *int `gorm:"column:role"`
	// SpaceID is 空间ID.
	//
	// index: idx_space_id, priority: 1.
	//
	SpaceID string `gorm:"column:space_id;size:100"`
	// Content is 模板中的Content.
	Content *string `gorm:"column:content"`
	// Option is 模板中的option.
	Option *string `gorm:"column:option"`
}

// TableName returns PO's corresponding DB table name: `next_cron_tasks`.
func (*NextCronTasksPO) TableName() string {
	return "next_cron_tasks"
}
