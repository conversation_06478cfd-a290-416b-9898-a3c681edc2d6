// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_webhook_record`.
package po

import (
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextWebhookRecordPO is next webhook record.
type NextWebhookRecordPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// TaskID is task id.
	//
	// index: idx_task_id, priority: 1.
	//
	TaskID string `gorm:"column:task_id;size:40"`
	// SessionID is session id.
	//
	// index: idx_session_id, priority: 1.
	//
	SessionID string `gorm:"column:session_id;size:40"`
	// URL is url.
	URL string `gorm:"column:url;size:255"`
	// RequestHeaders is request headers.
	RequestHeaders *string `gorm:"column:request_headers"`
	// RequestData is request data.
	RequestData *string `gorm:"column:request_data"`
	// ResponseHeaders is response headers.
	ResponseHeaders *string `gorm:"column:response_headers"`
	// ResponseBody is response body.
	ResponseBody *string `gorm:"column:response_body"`
	// ResponseStatus is response status.
	ResponseStatus *string `gorm:"column:response_status;size:255"`
	// ExecutionDuration is execution duration.
	ExecutionDuration *float64 `gorm:"column:execution_duration"`
	// InternalErrorMessage is internal error message.
	InternalErrorMessage *string `gorm:"column:internal_error_message;size:255"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_webhook_record`.
func (*NextWebhookRecordPO) TableName() string {
	return "next_webhook_record"
}
