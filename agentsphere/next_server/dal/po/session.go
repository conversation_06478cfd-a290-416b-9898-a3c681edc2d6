// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_session`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextSessionPO is session.
type NextSessionPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: idx_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// Title is session title.
	Title string `gorm:"column:title;size:2048"`
	// Creator is session creator.
	//
	// index: idx_creator_created_at_role, priority: 1.
	//
	Creator string `gorm:"column:creator;size:64"`
	// Status is session status`.
	//
	// index: idx_status, priority: 1.
	//
	Status string `gorm:"column:status;size:64"`
	// Context is input request context, a json map.
	Context datatypes.JSONType[NextSessionContext] `gorm:"column:context"`
	// RuntimeMetadata is runtime metadata.
	RuntimeMetadata datatypes.JSONType[NextSessionRuntimeMetadata] `gorm:"column:runtime_metadata"`
	// Role is session role.
	//
	// index: idx_creator_created_at_role, priority: 3.
	//
	Role *int `gorm:"column:role"`
	// TemplateID is template ID, usually UUID.
	TemplateID string `gorm:"column:template_id;size:40"`
	// StartedAt is started at, the unit is ms.
	StartedAt int64 `gorm:"column:started_at"`
	// SourceSpaceID is source space ID, usually UUID.
	SourceSpaceID string `gorm:"column:source_space_id;size:40"`
	// LastMessageAt is last message timestamp.
	//
	// index: idx_last_message_at, priority: 1.
	//
	LastMessageAt time.Time `gorm:"column:last_message_at"`
	// CanResume is can resume.
	CanResume int `gorm:"column:can_resume"`
	// CanNotResumeReason is can not resume reason 0 not_allowed 1 expired 2 deleted.
	CanNotResumeReason int `gorm:"column:can_not_resume_reason"`
	// LatestAgentResumeAt is latest agent resume at.
	//
	// index: idx_latest_agent_resume_at, priority: 1.
	//
	LatestAgentResumeAt *time.Time `gorm:"column:latest_agent_resume_at"`
	// Source is 0: unknown 1: personal 2: cron job 3: codebase 4: slardar.
	Source int `gorm:"column:source"`
	// CreatedAt is create timestamp.
	//
	// index: idx_creator_created_at_role, priority: 2.
	//
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_session`.
func (*NextSessionPO) TableName() string {
	return "next_session"
}
