// Code generated by SQL PO generator, DO NOT EDIT.
// This src file contains PO(persistent object) struct definition for database table `next_service_account`.
package po

import (
	datatypes "gorm.io/datatypes"
	softdelete "gorm.io/plugin/soft_delete"
	"time"
)

// NextServiceAccountPO is next service account.
type NextServiceAccountPO struct {
	// ID is ID.
	ID int64 `gorm:"column:id;primaryKey"`
	// Uid is unique string ID, usually UUID.
	//
	// index: uk_uid, priority: 1.
	//
	Uid string `gorm:"column:uid;size:40"`
	// Name is service name.
	//
	// index: uk_name, priority: 1.
	//
	Name string `gorm:"column:name;size:128"`
	// Description is description.
	Description *string `gorm:"column:description;size:512"`
	// WebhookConfig is webhook config.
	WebhookConfig *datatypes.JSONType[NextServiceAccountWebhookConfig] `gorm:"column:webhook_config"`
	// AllowPsms is allow psms.
	AllowPsms *datatypes.JSONType[NextServiceAccountAllowPsms] `gorm:"column:allow_psms"`
	// AllowSudo is allow sudo.
	AllowSudo int8 `gorm:"column:allow_sudo"`
	// Owner is owner.
	Owner string `gorm:"column:owner;size:40"`
	// Enabled is 是否开启.
	Enabled int8 `gorm:"column:enabled"`
	// CreatedAt is create timestamp.
	CreatedAt time.Time `gorm:"column:created_at"`
	// UpdatedAt is last update timestamp.
	UpdatedAt time.Time `gorm:"column:updated_at"`
	// DeletedAt is deleted at.
	DeletedAt softdelete.DeletedAt `gorm:"column:deleted_at;softDelete:nano"`
}

// TableName returns PO's corresponding DB table name: `next_service_account`.
func (*NextServiceAccountPO) TableName() string {
	return "next_service_account"
}
