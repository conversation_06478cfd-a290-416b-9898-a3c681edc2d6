package dal

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/po"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/AlekSi/pointer"
	"github.com/samber/lo"
	"gorm.io/plugin/dbresolver"
)

type GetServiceAccount struct {
	ID   *string
	Name *string
	Sync bool
}

func (d *DAO) GetServiceAccount(ctx context.Context, opt GetServiceAccount) (*entity.ServiceAccount, error) {
	p := &po.NextServiceAccountPO{}
	req := d.Conn.NewRequest(ctx).Model(p)
	if opt.ID != nil {
		req.Where("uid = ?", *opt.ID)
	}

	if opt.Name != nil {
		req.Where("name = ?", *opt.Name)
	}

	if opt.Sync {
		req = req.Clauses(dbresolver.Write)
	}

	res := req.First(p)
	if res.Error != nil {
		return nil, res.Error
	}
	return getServiceAccountFromPO(p), nil
}

func getServiceAccountFromPO(po *po.NextServiceAccountPO) *entity.ServiceAccount {
	var webhookConfig entity.WebhookConfig
	var allowPSMs entity.AllowPSMs

	if po.WebhookConfig != nil {
		webhookConfig = po.WebhookConfig.Data()
	}

	if po.AllowPsms != nil {
		allowPSMs = po.AllowPsms.Data()
	}

	return &entity.ServiceAccount{
		ID:            po.Uid,
		Name:          po.Name,
		Description:   pointer.Get(po.Description),
		WebHookConfig: webhookConfig,
		AllowPSMs:     allowPSMs,
		AllowSudo:     lo.Ternary(po.AllowSudo == 0, false, true),
		Owner:         po.Owner,
		Enabled:       lo.Ternary(po.Enabled == 0, false, true),
		CreatedAt:     po.CreatedAt,
		UpdatedAt:     po.UpdatedAt,
	}
}
