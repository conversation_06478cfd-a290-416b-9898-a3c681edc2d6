package serverhandler

import (
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"github.com/samber/lo"
	"github.com/stretchr/testify/require"
)

func TestRewriteContent(t *testing.T) {
	h := Handler{}

	template := entity.TemplateVersion{
		PromptContent: "## 表格数据分析报告\n\n请帮我分析飞书表格 `{表格链接}` 中的数据，计算其中{分析目标}。\n\n要求：\n1. {分析要求}。\n2. 生成详细的分析报告，包含数据概览、分组聚合结果、可视化图表（如总成本对比、趋势图、分布图等）。\n3. 最终将报告部署成网页，报告名称为 `{报告名称}`。",
		PromptVariables: entity.TemplateVariableSchemaList{
			{
				ID:                "df40f5f2-76cc-45d2-b63f-79dc9c8a0187",
				Name:              "表格链接",
				Default:           "",
				Description:       "",
				Placeholder:       "",
				SelectContent:     "https://bytedance.larkoffice.com/sheets/KVRHsVXnNh9PbattnkHcWvP8nvf",
				AllowedUploadFile: true,
			},
			{
				ID:                "0672efc1-4555-437a-bd55-1f0973ad01bf",
				Name:              "分析目标",
				Default:           "",
				Description:       "",
				Placeholder:       "",
				SelectContent:     "每个 `model_name` 的 `cost` 总和",
				AllowedUploadFile: false,
			},
			{
				ID:                "3c09a6ff-7007-49b2-8e37-64a6fd49701c",
				Name:              "分析要求",
				Default:           "",
				Description:       "",
				Placeholder:       "",
				SelectContent:     " `created_time,group` 字段分组，其中 `created_time` 要转换成 `%Y-%m-%d` 的格式",
				AllowedUploadFile: false,
			},
			{
				ID:                "d6087f18-cf55-4848-9b73-47ffc507217a",
				Name:              "报告名称",
				Default:           "",
				Description:       "",
				Placeholder:       "",
				SelectContent:     "数据分析报告",
				AllowedUploadFile: false,
			},
		},
	}

	values := nextagent.TemplateFormValue{
		Variables: map[string]*nextagent.TemplateVariableValue{
			"分析目标": {
				Content: lo.ToPtr("无"),
			},
			"分析要求": {
				Content: lo.ToPtr("无"),
			},
			"报告名称": {
				Content: lo.ToPtr("无"),
			},
			"表格链接": {
				Attachments: []*nextagent.AttachmentRequired{
					{
						ID:       "1e2c81e5-d09b-4496-b11b-9f3ee42e33db",
						FileName: "sync.xlsx",
					},
				},
				Content: lo.ToPtr("测试一下"),
			},
		},
	}

	output := h.rewriteContent(&values, template)

	exceptedContent := "## 表格数据分析报告\n\n请帮我分析飞书表格 `测试一下 sync.xlsx` 中的数据，计算其中无。\n\n要求：\n1. 无。\n2. 生成详细的分析报告，包含数据概览、分组聚合结果、可视化图表（如总成本对比、趋势图、分布图等）。\n3. 最终将报告部署成网页，报告名称为 `无`。"
	require.Equal(t, exceptedContent, output)
}
