package serverhandler

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/hertz-contrib/sse"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/db"
)

func (h *Handler) GetSessionStreamEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetSessionStreamEventsRequest](ctx, c)
	if req == nil {
		return
	}
	ctx = logs.CtxAddKVs(ctx, "_session_id", req.GetSessionID())
	log.V1.CtxInfo(ctx, "get session stream events request: %+v", util.MarshalStruct(req))

	s := sse.NewStream(c)
	pub := func(e string, data any) error {
		return s.Publish(&sse.Event{
			Event: e,
			Data:  conv.JSONBytes(data),
		})
	}

	var (
		err       error
		idGen     = uuid.GetDefaultGenerator(nil)
		doneEvent *nextagent.DoneEvent
	)
	defer func() {
		_ = metrics.NSM.EventDoneRate.WithTags(&metrics.NextServerDoneEventTag{}).Add(1)
		if doneEvent != nil && doneEvent.Status != nextagent.DoneStatus_Success {
			doneEventBytes, _ := json.Marshal(doneEvent)
			log.V1.CtxError(ctx, "done event not success: %+v", string(doneEventBytes))

			_ = metrics.NSM.EventDoneError.WithTags(&metrics.NextServerDoneEventErrorTag{
				ErrorCode: int64(doneEvent.GetErrorCode()),
			}).Add(1)
		}

		// 区分一下内部错误是不是可以重试的
		if doneEvent != nil && lo.FromPtr(doneEvent.ErrorCode) == common.ErrorCode_ErrInternal {
			session, _ := h.SessionService.GetSessionWithDeleted(ctx, req.SessionID, true)
			if session != nil && session.Status == entity.SessionStatusError {
				// 内部致命错误，不需要重试
				doneEvent.ErrorCode = lo.ToPtr(common.ErrorCode_ErrInternalFatal)
			}
		}
		pub(nextagent.EventNameDone, lo.Ternary(doneEvent != nil, doneEvent, &nextagent.DoneEvent{
			EventID:   idGen.NewID(),
			Status:    nextagent.DoneStatus_Success,
			Message:   "",
			Timestamp: time.Now().Unix(),
			ErrorCode: nil,
		}))
	}()

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		doneEvent = &nextagent.DoneEvent{
			EventID:   idGen.NewID(),
			Status:    nextagent.DoneStatus_Failed,
			Message:   "not found account",
			Timestamp: time.Now().Unix(),
			ErrorCode: lo.ToPtr(common.ErrorCode_ErrNoAuth),
		}
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		doneEvent = &nextagent.DoneEvent{
			EventID:   idGen.NewID(),
			Status:    nextagent.DoneStatus_Failed,
			Message:   "permission denied",
			Timestamp: time.Now().Unix(),
			ErrorCode: lo.ToPtr(common.ErrorCode_ErrAccessReject),
		}
		return
	}

	// 校验权限
	if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(req.SessionID),
		Action:    entity.PermissionActionSessionRead,
	}); !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: req.SessionID,
		Sync:      true,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			doneEvent = &nextagent.DoneEvent{
				EventID:   idGen.NewID(),
				Status:    nextagent.DoneStatus_Failed,
				Message:   "session not found",
				Timestamp: time.Now().Unix(),
				ErrorCode: lo.ToPtr(common.ErrorCode_ErrRecordNotFound),
			}
			return
		}
		doneEvent = &nextagent.DoneEvent{
			EventID:   idGen.NewID(),
			Status:    nextagent.DoneStatus_Failed,
			Message:   "failed to get session",
			Timestamp: time.Now().Unix(),
			ErrorCode: lo.ToPtr(common.ErrorCode_ErrInternal),
		}
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		return
	}

	if (user.Username != session.Creator) && !h.UserService.IsGroupOperator(user) {
		doneEvent = &nextagent.DoneEvent{
			EventID:   idGen.NewID(),
			Status:    nextagent.DoneStatus_Failed,
			Message:   "permission denied",
			Timestamp: time.Now().Unix(),
			ErrorCode: lo.ToPtr(common.ErrorCode_ErrAccessReject),
		}
		return
	}

	done := make(chan struct{}, 1)
	defer close(done)

	go func() {
		pub("ping", map[string]string{"message": "ping"})
		ticker := time.NewTicker(time.Second * 10)
		defer ticker.Stop()
		for {
			select {
			case <-done:
				return
			case <-ticker.C:
				if err := pub("ping", map[string]string{"message": "ping"}); err != nil {
					return
				}
			}
		}
	}()

	ch, err := h.SessionService.GetSessionStreamEvents(ctx, req.SessionID, sessionservice.GetSessionStreamOption{
		EventOffset: lo.FromPtr(req.EventOffset),
		Username:    user.Username,
		StartTime:   session.StartedAt,
	})
	if err != nil {
		if ch != nil {
			for event := range ch.DataChannel { // 处理剩余事件
				pub(event.Event, event.Data)
			}
		}
		if errors.Is(err, runtimeservice.ErrAgentStopped) {
			doneEvent = &nextagent.DoneEvent{
				EventID:   idGen.NewID(),
				Status:    nextagent.DoneStatus_Failed,
				Message:   "agent stopped",
				Timestamp: time.Now().Unix(),
				ErrorCode: lo.ToPtr(common.ErrorCode_ErrNextSessionStopped),
			}
			return
		}
		doneEvent = &nextagent.DoneEvent{
			EventID:   idGen.NewID(),
			Status:    nextagent.DoneStatus_Failed,
			Message:   "failed to fetch session events",
			Timestamp: time.Now().Unix(),
			ErrorCode: lo.ToPtr(common.ErrorCode_ErrInternal),
		}
		log.V1.CtxError(ctx, "failed to fetch session events: %v", err)
		return
	}
loop:
	for {
		select {
		case err = <-ch.ErrorChannel:
			if err != nil {
				doneEvent = &nextagent.DoneEvent{
					EventID:   idGen.NewID(),
					Status:    nextagent.DoneStatus_Failed,
					Message:   err.Error(),
					Timestamp: time.Now().Unix(),
					ErrorCode: lo.ToPtr(common.ErrorCode_ErrInternal),
				}
				ch.Close()
				break loop
			}
		case event, ok := <-ch.DataChannel:
			if !ok {
				break loop
			}
			if event.Event != "" && event.Data != nil {
				pubErr := pub(event.Event, event.Data)
				if pubErr != nil {
					// Usually, this is caused by user interrupted AI generating by click `stop generating` button.
					log.V1.CtxError(ctx, "failed to publish agent run stream events to sse: %v", pubErr)
					ch.Close()
					break loop
				}
			}
		}
	}
}

type GetOldSessionEventsResponse struct {
	Events  []*entity.EventData    `json:"events"`
	Role    *nextagent.SessionRole `json:"role,omitempty"`
	Session *nextagent.Session     `json:"session,omitempty"`
}

func (h *Handler) GetSessionOldEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetOldSessionEventsRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get session old events request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 校验权限
	perActions, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
		Account:   user,
		SessionID: lo.ToPtr(req.SessionID),
		Action:    entity.PermissionActionSessionRead,
	})
	if !ok {
		return
	}

	session, err := h.SessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: req.SessionID,
		Sync:      true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session: %v", err)
		if errors.Is(err, serverservice.ErrSessionNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "session not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session")
		return
	}
	session.PermissionActions = perActions
	star, err := h.SessionService.GetSessionStarByKey(ctx, session.ID, user.Username)
	if err != nil && !db.IsRecordNotFoundError(err) {
		log.V1.CtxError(ctx, "failed to get session star: %v", err)
	}
	if star != nil {
		session.Starred = true
	}

	events, err := h.SessionService.GetSessionEvents(ctx, sessionservice.GetSessionEventsOption{
		SessionID: req.SessionID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session events: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get session events")
		return
	}
	events = lo.Filter(events, func(item *entity.Event, i int) bool {
		return !item.ShouldFilter(i == len(events)-1, h.UserService.IsDeveloper(user))
	})
	if session.CreatedAt.Unix() < 1743520496 { // 2025-04-01 23:14:56
		events = replaceEvents(events, h.Conf) // TODO: remove this
	}

	c.JSON(http.StatusOK, GetOldSessionEventsResponse{
		Role: session.Role.ToIDL(),
		Events: lo.Map(events, func(item *entity.Event, _ int) *entity.EventData {
			return &entity.EventData{
				Event: item.EventName,
				Data:  item.EventData.Data,
			}
		}),
		Session: getSessionFromEntity(session, nil),
	})
}

// SaveEventKey 计算并保存历史 event 的 event key
func (h *Handler) SaveEventKey(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SaveEventKeyRequest](ctx, c)
	if req == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "request is empty")
		return
	}
	var (
		events []*entity.Event
		err    error
	)
	if len(req.GetEventIDs()) > 0 {
		events, err = h.SessionService.GetEventsByIDs(ctx, req.GetEventIDs())
	} else {
		events, err = h.SessionService.GetEventsByOffsetLimit(ctx, int(req.Offset), int(req.Limit))
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to get events: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get events")
		return
	}
	failedEventIDs := make([]string, 0)
	for _, event := range events {
		if event.EventKey != "" {
			continue
		}
		eventKey := getEventKey(event)
		if eventKey == "" {
			continue
		}
		err = h.SessionService.UpdateEventKey(ctx, event.ID, eventKey)
		if err != nil {
			log.V1.CtxError(ctx, "failed to update event key: %v", err)
			failedEventIDs = append(failedEventIDs, event.ID)
			continue
		}
	}
	c.JSON(http.StatusOK,
		nextagent.SaveEventKeyResponse{
			FailedEventIDs: failedEventIDs,
		},
	)
}
