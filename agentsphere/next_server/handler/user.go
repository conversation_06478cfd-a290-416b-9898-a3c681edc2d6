package serverhandler

import (
	"context"
	"net/http"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	activityservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/activity"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
)

func (h *Handler) GrantUserAccess(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GrantAccessRequest](ctx, c)
	if req == nil {
		return
	}
	// log.V1.CtxInfo(ctx, "GrantAccess request: %+v", req)
	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "no auth")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if userFeature.Invited {
		log.V1.CtxInfo(ctx, "user %s already has access", user.Username)
		c.JSON(http.StatusOK, &nextagent.GrantAccessResponse{
			Status: nextagent.GrantStatusSuccess,
		})
		return
	}

	grantConf := h.NextAgentGrantAccessConfig.GetValue()
	if grantConf.ActivityID == "" || grantConf.CodeOwner == "" {
		log.V1.CtxError(ctx, "grant access config is not set")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "grant_access config is not set")
		return
	}

	if !grantConf.Enabled {
		log.V1.CtxInfo(ctx, "grant access is disabled")
		c.JSON(http.StatusOK, &nextagent.GrantAccessResponse{
			Status: nextagent.GrantStatusFinish,
		})
		return
	}

	// TODO: 获取雇员类型
	// ids, _ := h.LarkClient.MGetUserIDByEmail(ctx, []string{user.Email}, lark.UserIDTypeUserID)
	// users, _ := h.LarkClient.ListLarkUsers(ctx, []string{ids[user.Email]}, string(lark.UserIDTypeUserID))

	log.V1.CtxInfo(ctx, "grant_access: username:%s, department:%s, sequence:%s", user.Username, user.Department, user.Sequence)
	blackList := grantConf.BlackList
	for _, item := range blackList {
		if strings.Contains(user.Department, item.Department) &&
			strings.Contains(user.Email, item.EmailSuffix) &&
			strings.Contains(user.Sequence, item.Sequence) {
			log.V1.CtxInfo(ctx, "user %s is in black list", user.Username)
			c.JSON(http.StatusOK, &nextagent.GrantAccessResponse{
				Status: nextagent.GrantStatusFinish,
			})
			return
		}
	}

	activityID, codeOwner := grantConf.ActivityID, grantConf.CodeOwner

	count, err := h.ActivityService.CountRemainInvitationCode(ctx, activityID)
	if err != nil {
		log.V1.CtxError(ctx, "count remain invitation code err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "grant_access failed")
		return
	}
	if count <= 0 {
		log.V1.CtxInfo(ctx, "no invitation code left for activity %s", activityID)
		c.JSON(http.StatusOK, &nextagent.GrantAccessResponse{
			Status: nextagent.GrantStatusFinish,
		})
		return
	}

	codes, err := h.ActivityService.DistributeInvitationCode(ctx, activityservice.DistributeInvitationCodeOption{
		ActivityID: activityID,
		Username:   codeOwner,
		Count:      1,
	})
	if err != nil {
		log.V1.CtxError(ctx, "distribute invitation code err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to grant access")
		return
	}

	if len(codes) == 0 {
		log.V1.CtxError(ctx, "no invitation code distributed")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to grant access")
		return
	}

	_, err = h.ActivityService.BindInvitationCode(ctx, activityservice.BindInvitationCodeOption{
		BindUser:       user.Username,
		InvitationCode: codes[0].InvitationCode,
		Sync:           true, // Ensure the binding is synchronous
	})
	if err != nil {
		log.V1.CtxError(ctx, "bind invitation code err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to grant access")
		return
	}

	c.JSON(http.StatusOK, &nextagent.GrantAccessResponse{
		Status: nextagent.GrantStatusSuccess,
	})
}

func (h *Handler) GetUserSettings(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserSettingsRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	settings, err := h.UserService.GetUserSettings(ctx, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get user settings: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get user settings")
		return
	}
	c.JSON(http.StatusOK, nextagent.GetUserSettingsResponse{
		Settings: pack.ConvertUserSettingsToDTO(settings),
	})
}

func (h *Handler) UpdateUserSettings(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateUserSettingsRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	settings, err := h.UserService.GetUserSettings(ctx, user.Username)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get user settings: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get user settings")
		return
	}
	settings, err = h.UserService.UpdateUserSettings(ctx, settings.ID, userservice.UpdateUserSettingOption{
		User:             user,
		Locale:           req.Locale,
		KeepLogin:        req.KeepLogin,
		KeyboardShortcut: pack.ConvertKeyboardShortcutToEntity(req.KeyboardShortcut),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update user settings: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update user settings")
		return
	}
	c.JSON(http.StatusOK, nextagent.UpdateUserSettingsResponse{Settings: pack.ConvertUserSettingsToDTO(settings)})
}
