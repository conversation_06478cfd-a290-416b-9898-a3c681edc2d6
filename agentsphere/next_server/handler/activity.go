package serverhandler

import (
	"context"
	"net/http"

	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	activityservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/activity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

// GetUserActivityProgress implements the GetUserActivityProgress API
func (h *Handler) GetUserActivityProgress(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetUserActivityProgressRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get user activity progress request: %+v", req)

	// 检查活动状态
	activity, err := h.ActivityService.GetCurrentActivity(ctx)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get activity error")
		return
	}
	//无当前活动或者活动已结束
	if activity == nil || !activity.IsActivated() {
		// hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "activity is not activated")
		c.JSON(http.StatusOK, &nextagent.GetUserActivityProgressResponse{})
		return
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	progress, err := h.ActivityService.GetUserActivityStatus(ctx, user.Username, activity.ID)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get user activity status error")
		return
	}

	inviteCodes, err := h.ActivityService.ListInvitationCode(ctx, activityservice.ListInvitationCodeOption{
		Owner:      user.Username,
		ActivityID: &activity.ID,
	})

	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get user invitation code error")
		return
	}

	resp := &nextagent.GetUserActivityProgressResponse{
		Progress: progress,
		InvitationCodes: lo.Map(inviteCodes, func(code *nextentity.InvitationCode, _ int) *nextagent.InvitationCode {
			return &nextagent.InvitationCode{
				ActivityID: code.ActivityID,
				Code:       code.InvitationCode,
				Activited:  lo.Ternary(code.BindUser != nil, true, false),
				BindUser:   code.BindUser,
				Owner:      code.Owner,
			}
		}),
	}

	c.JSON(http.StatusOK, resp)
}

// VerifyActivityAward 回放查看结束后调用，检查是否中活动逻辑
func (h *Handler) VerifyActivityAward(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.VerifyActivityAwardRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "verify activity award request: %+v", req)

	// 检查活动状态
	activity, err := h.ActivityService.GetCurrentActivity(ctx)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "get activity error")
		return
	}
	//无当前活动或者活动已结束
	if activity == nil || !activity.IsActivated() {
		c.JSON(http.StatusOK, &nextagent.VerifyActivityAwardResponse{Hit: false})
		return
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	// 如果已经是内测用户，则不计入活动(不摇彩蛋，也不计入replay浏览量)
	// if userFeature.Invited {
	// c.JSON(http.StatusOK, &nextagent.VerifyActivityAwardResponse{Hit: false})
	// return
	// }

	hit, err := h.ActivityService.VerifyActivityAward(ctx, req.ReplayID, activity, user.Name, userFeature.Invited)
	if err != nil {
		log.V1.CtxError(ctx, "verify activity award err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "verify activity error")
		return
	}

	resp := &nextagent.VerifyActivityAwardResponse{
		Hit: hit,
	}

	c.JSON(http.StatusOK, resp)
}

// BindInvitationCode implements the BindInvitationCode API
func (h *Handler) BindInvitationCode(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BindInvitationCodeRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "bind invitation code request: %+v", req)

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	userFeature := h.UserService.GetUserFeatures(ctx, user, true)
	// 如果已经是内测用户，则不允许绑定 code
	if userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "user already invited")
		return
	}

	_, err := h.ActivityService.BindInvitationCode(ctx, activityservice.BindInvitationCodeOption{InvitationCode: req.InvitationCode, BindUser: user.Username})
	if err != nil {
		if errors.Is(err, serverservice.ErrInvalidInvitationCode) {
			c.JSON(http.StatusOK, &nextagent.BindInvitationCodeResponse{
				Status: nextagent.BindStatusInvalid,
			})
			return
		}
		if errors.Is(err, serverservice.ErrInvitationCodeAlreadyBind) {
			c.JSON(http.StatusOK, &nextagent.BindInvitationCodeResponse{
				Status: nextagent.BindStatusAlreadyBind,
			})
			return
		}
		if errors.Is(err, serverservice.ErrUserAlreadyBindInvitationCode) {
			c.JSON(http.StatusOK, &nextagent.BindInvitationCodeResponse{
				Status: nextagent.BindStatusAlreadyBind,
			})
			return
		}
		log.V1.CtxInfo(ctx, "bind invitation code err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "bind code err")
		return
	}

	c.JSON(http.StatusOK, &nextagent.BindInvitationCodeResponse{
		Status: nextagent.BindStatusSuccess,
	})
}

// CreateInvitationCode for developer
func (h *Handler) CreateInvitationCode(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateInvitationCodeRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create invitation code request: %+v", req)

	h.ActivityService.BatchCreateInvitationCode(ctx, activityservice.BatchCreateInvitationCodeOption{
		ActivityID: req.ActivityID,
		Count:      int(req.Count),
	})
	c.AbortWithStatus(http.StatusNoContent)
}

// DistributeInvitationCode for developer
func (h *Handler) DistributeInvitationCode(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DistributeInvitationCodeRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "distribute invitation code request: %+v", req)

	if req.Count == 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "count not be zero")
		return
	}

	codes, err := h.ActivityService.DistributeInvitationCode(ctx, activityservice.DistributeInvitationCodeOption{
		ActivityID: req.ActivityID,
		Username:   req.Username,
		Count:      int(req.Count),
	})
	if err != nil {
		log.V1.CtxError(ctx, "distribute invitation code err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "distribute invitation code err")
		return
	}
	c.JSON(http.StatusOK, &nextagent.DistributeInvitationCodeResponse{
		InvitationCodes: lo.Map(codes, func(item *nextentity.InvitationCode, index int) *nextagent.InvitationCode {
			return item.ToIDL()
		}),
	})
}
