package serverhandler

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	traceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/trace"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz/pkg/common/utils"
	"github.com/hertz-contrib/sse"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"
)

func (h *Handler) checkPermission(roles []string, isAny bool) app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		account, _ := h.AuthM.GetAccount(ctx, c)
		if account == nil {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrCheckAuth), "no auth info")
			c.Abort()
			return
		}
		if isAny {
			if !h.UserService.VerifyUserRolesAny(roles, account) {
				log.V1.CtxInfo(ctx, "permission denied to user %s, roles: %v", account.Username, roles)
				hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "no permission")
				c.Abort()
				return
			}
		} else if !h.UserService.VerifyUserRoles(roles, account) {
			log.V1.CtxInfo(ctx, "permission denied to user %s, roles: %v", account.Username, roles)
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "no permission")
			c.Abort()
			return
		}
		c.Next(ctx)
	}
}

func (h *Handler) RequireAimoMCPPartner() app.HandlerFunc {
	return h.checkPermission([]string{nextagent.UserRoleAimoMCPPlayground}, false)
}

func (h *Handler) RequireAimoTrace() app.HandlerFunc {
	return h.checkPermission([]string{nextagent.UserRoleAimoTraceSelf, nextagent.UserRoleAimoTraceAll}, true)
}

func (h *Handler) RequireAimoAgentDeveloper() app.HandlerFunc {
	return h.checkPermission([]string{nextagent.UserRoleAimoAgentDeveloper}, false)
}

func (h *Handler) RequireAimoMCPDeveloper() app.HandlerFunc {
	return h.checkPermission([]string{nextagent.UserRoleAimoMCPDeveloper}, false)
}

func (h *Handler) RequireAimoInternalDev() app.HandlerFunc {
	return h.checkPermission([]string{nextagent.UserRoleAimoInternalDev}, false)
}

func (h *Handler) RequireAimoDiagnose() app.HandlerFunc {
	return h.checkPermission([]string{nextagent.UserRoleAimoTraceDiagnose}, false)
}

func (h *Handler) TraceSession(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetTraceSessionRequest](ctx, c)
	if req == nil {
		return
	}
	if req.SessionID == nil && req.ReplayID == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "session_id or replay_id is required")
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrCheckAuth), "no auth info")
		return
	}

	session, err := h.TraceService.GetTraceSession(ctx, traceservice.GetTraceSessionOption{
		SessionID: lo.FromPtr(req.SessionID),
		ReplayID:  lo.FromPtr(req.ReplayID),
		Account:   user,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.JSON(http.StatusOK, nextagent.GetTraceSessionResponse{
		Session: session,
	})
}

type LogEvent struct {
	Event     string `json:"event"`
	Data      any    `json:"data"`
	Offset    int64  `json:"offset"`
	Timestamp string `json:"timestamp"`
}

// 添加过滤函数
func filterLLMCall(data any) any {
	switch v := data.(type) {
	case map[string]interface{}:
		filteredData := make(map[string]interface{})
		for k, val := range v {
			if k == "llm_call" {
				filteredData[k] = nil
			} else {
				// 递归处理嵌套数据
				filteredData[k] = filterLLMCall(val)
			}
		}
		return filteredData
	case []interface{}:
		filteredSlice := make([]interface{}, len(v))
		for i, val := range v {
			filteredSlice[i] = filterLLMCall(val)
		}
		return filteredSlice
	case []map[string]interface{}:
		filteredSlice := make([]map[string]interface{}, len(v))
		for i, val := range v {
			if filtered := filterLLMCall(val); filtered != nil {
				if filteredMap, ok := filtered.(map[string]interface{}); ok {
					filteredSlice[i] = filteredMap
				}
			}
		}
		return filteredSlice
	default:
		// 其他类型直接返回
		return data
	}
}

func (h *Handler) TraceEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetTraceEventsRequest](ctx, c)
	if req == nil {
		return
	}
	if req.RunID == nil && req.SessionID == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "run_id or session_id is required")
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	recv, err := h.TraceService.GetTraceEvents(ctx, traceservice.GetTraceEventsOption{
		RunID:       lo.FromPtr(req.RunID),
		SessionID:   lo.FromPtr(req.SessionID),
		ContainerID: lo.FromPtr(req.ContainerID),
		URI:         lo.FromPtr(req.URI),
		Provider:    lo.FromPtr(req.Provider),
		Account:     account,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		log.V1.CtxError(ctx, "failed to get log: %+v", err)
		return
	}

	s := sse.NewStream(c)
	pub := func(data any) error {
		return s.Publish(&sse.Event{
			Event: "json-data",
			Data:  conv.JSONBytes(data),
		})
	}
	defer pub(LogEvent{
		Event: agentsphere.DSEventDone,
		Data:  agentsphere.DSDone{},
	})
	done := make(chan struct{}, 1)
	defer close(done)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "failed to send ping: %+v", r)
			}
		}()
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				pub(LogEvent{
					Event: "ping",
					Data:  "ping",
				})
			case <-done:
				return
			}
		}
	}()

	for data := range recv.DataChannel {
		// Too many think_delta, skip it.
		if data.Event == "think_delta" {
			continue
		}

		pubErr := pub(LogEvent{
			Event:     string(data.Event),
			Data:      data.Data,
			Offset:    data.Offset,
			Timestamp: data.Timestamp.Format(time.RFC3339),
		})
		if pubErr != nil {
			log.V1.CtxError(ctx, "failed to publish agent run stream events to sse: %v", pubErr)
			recv.Close()
			break
		}
	}
}

func (h *Handler) ResumeRuntime(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ResumeRuntimeRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	err := h.TraceService.ResumeRuntime(ctx, req.RunID, account)
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.JSON(http.StatusOK, nextagent.ResumeRuntimeResponse{
		Message: "success",
	})
}

func (h *Handler) SuspendRuntime(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.SuspendRuntimeRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	err := h.TraceService.SuspendRuntime(ctx, req.RunID, account)
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
}

func (h *Handler) DeleteRuntime(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteRuntimeRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	err := h.TraceService.DeleteRuntime(ctx, req.RunID, account)
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.JSON(http.StatusOK, nextagent.DeleteRuntimeResponse{
		Message: "success",
	})
}

func (h *Handler) TraceSessionChat(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetTraceSessionChatRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrCheckAuth), "no auth info")
		return
	}
	total, chatCompletions, err := h.TraceService.GetTraceSessionChat(ctx, traceservice.GetTraceSessionChatOption{
		SessionID: req.SessionID,
		PageNum:   req.PageNum,
		PageSize:  req.PageSize,
		Status:    lo.FromPtr(req.Status),
		Type:      lo.FromPtr(req.Type),
		TraceID:   lo.FromPtr(req.TraceID),
		Account:   account,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.JSON(http.StatusOK, nextagent.GetTraceSessionChatResponse{
		ChatCompletions: chatCompletions,
		Total:           total,
	})
}

func (h *Handler) ListSessionAgentStep(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListSessionAgentStepRequest](ctx, c)
	if req == nil {
		return
	}
	agentSteps, err := h.TraceService.ListSessionAgentStep(ctx, req.SessionID)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
	}
	agentSteps = lo.UniqBy(agentSteps, func(item *nextagent.AgentStep) string {
		return item.StepID
	})
	c.JSON(http.StatusOK, nextagent.ListSessionAgentStepResponse{
		Steps: agentSteps,
		Total: int64(len(agentSteps)),
	})
}

func (h *Handler) DownloadSessionLog(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DownloadSessionLogRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	reader, err := h.TraceService.GetSessionLogArchive(ctx, traceservice.DownloadSessionLogOption{
		SessionID: req.SessionID,
		Account:   account,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		log.V1.CtxError(ctx, "failed to list session log: %+v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.Response.Header.Set("Content-Type", "application/zip")
	filename := fmt.Sprintf("aime_%s_logs_%s.zip", req.SessionID, time.Now().Format("20060102_150405"))
	c.Response.Header.Set("Content-Disposition", `attachment; filename=`+filename)
	c.SetBodyStream(reader, -1)
}

func (h *Handler) ListSessionDocuments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListSessionDocumentsRequest](ctx, c)
	if req == nil {
		return
	}
	account, _ := h.AuthM.GetAccount(ctx, c)
	imagexConf := h.ImagexConf.GetValue()
	attachments, err := h.TraceService.ListSessionLarkDocuments(ctx, traceservice.ListSessionAttachmentsOption{
		SessionID:  req.SessionID,
		Account:    account,
		ImageXConf: &imagexConf,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.JSON(http.StatusOK, nextagent.ListSessionDocumentsResponse{
		Documents: attachments,
	})
}

func (h *Handler) ConvertSessionDocumentToLark(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ConvertSessionDocumentToLarkRequest](ctx, c)
	if req == nil {
		return
	}
	conf := h.NextAgentUploadLarkFileConfig.GetValue()
	account, _ := h.AuthM.GetAccount(ctx, c)
	imagexConf := h.ImagexConf.GetValue()
	larkURL, err := h.TraceService.ConvertSessionDocumentToLark(ctx, traceservice.ConvertSessionDocumentToLarkOption{
		SessionID:       req.SessionID,
		FilePath:        req.FilePath,
		ArtifactID:      req.ArtifactID,
		Conf:            &conf,
		ForceRegenerate: lo.FromPtr(req.ForceRegenerate),
		Account:         account,
		ImageXConf:      &imagexConf,
	})
	if err != nil {
		if errors.Is(err, serverservice.ErrTracePermissionDenied) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
		log.V1.CtxError(ctx, "failed to convert session document to lark: %+v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.JSON(http.StatusOK, nextagent.ConvertSessionDocumentToLarkResponse{
		LarkURL: larkURL,
	})
}

func (h *Handler) ConvertMarkdownToLark(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ConvertMarkdownToLarkRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "convert markdown to lark session id: %s", lo.FromPtr(req.SessionID))
	conf := h.NextAgentUploadLarkFileConfig.GetValue()
	account, _ := h.AuthM.GetAccount(ctx, c)
	larkURL, err := h.TraceService.ConvertMarkdownToLark(ctx, traceservice.ConvertMarkdownToLarkOption{
		SessionID: lo.FromPtr(req.SessionID),
		Markdown:  req.Content,
		Conf:      &conf,
		Account:   account,
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.JSON(http.StatusOK, nextagent.ConvertMarkdownToLarkResponse{
		LarkURL: larkURL,
	})
}

func (h *Handler) ListModels(ctx context.Context, c *app.RequestContext) {
	models, err := h.TraceService.ListModels(ctx)
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
	}
	c.JSON(http.StatusOK, nextagent.ListModelsResponse{
		Models: models,
	})
}

type ChatRequest struct {
	openai.ChatCompletionRequest
	Thinking *config.ThinkingConfig `json:"thinking,omitempty"`
}

// ErrorResponse is returned if errors occurs requesting OpenAI API.
// Ref: https://platform.openai.com/docs/guides/error-codes/api-errors
type openAIErrorResponseDetail struct {
	Code    any     `json:"code"`
	Message string  `json:"message"`
	Type    string  `json:"type"`
	Param   *string `json:"param"`
}

// 以 openai 兼容的形式返回错误信息，因为客户端用了 openai go sdk（虽然是第三方的）。
func openaiStyleJSONMessage(c *app.RequestContext, status int, message string) {
	c.JSON(status, utils.H{
		"error": openAIErrorResponseDetail{
			Code:    nil,
			Message: message,
			Type:    "invalid_request_error",
			Param:   nil,
		},
	})
}

func toOpenAIChatCompletionRequest(req *nextagent.ChatStreamRequest) *ChatRequest {
	return &ChatRequest{
		ChatCompletionRequest: openai.ChatCompletionRequest{
			Model: req.Model,
			Messages: lo.Map(req.Messages, func(item *nextagent.ChatCompletionMessage, _ int) openai.ChatCompletionMessage {
				return openai.ChatCompletionMessage{
					Role:    item.Role,
					Content: lo.FromPtr(item.Content),
					Refusal: lo.FromPtr(item.Refusal),
					MultiContent: lo.Map(item.MultiContent, func(item *nextagent.ChatMessagePart, _ int) openai.ChatMessagePart {
						return openai.ChatMessagePart{
							Type: openai.ChatMessagePartType(lo.FromPtr(item.Type)),
							Text: lo.FromPtr(item.Text),
							ImageURL: lo.TernaryF(item.ImageURL != nil, func() *openai.ChatMessageImageURL {
								return &openai.ChatMessageImageURL{
									URL:    lo.FromPtr(item.ImageURL.URL),
									Detail: openai.ImageURLDetail(lo.FromPtr(item.ImageURL.Detail)),
								}
							}, func() *openai.ChatMessageImageURL {
								return nil
							}),
						}
					}),
					Name: lo.FromPtr(item.Name),
					FunctionCall: lo.TernaryF(item.FunctionCall != nil, func() *openai.FunctionCall {
						return &openai.FunctionCall{
							Name:      lo.FromPtr(item.FunctionCall.Name),
							Arguments: lo.FromPtr(item.FunctionCall.Arguments),
						}
					}, func() *openai.FunctionCall {
						return nil
					}),
					ToolCalls: lo.Map(item.ToolCalls, func(item *nextagent.ChatToolCall, _ int) openai.ToolCall {
						return openai.ToolCall{
							ID:    lo.FromPtr(item.ID),
							Index: lo.ToPtr(int(lo.FromPtr(item.Index))),
							Type:  openai.ToolType(item.Type),
							Function: lo.TernaryF(item.Function != nil, func() openai.FunctionCall {
								return openai.FunctionCall{
									Name:      lo.FromPtr(item.Function.Name),
									Arguments: lo.FromPtr(item.Function.Arguments),
								}
							}, func() openai.FunctionCall {
								return openai.FunctionCall{}
							}),
						}
					}),
					ToolCallID: lo.FromPtr(item.ToolCallID),
				}
			}),
			MaxTokens:           int(lo.FromPtr(req.MaxTokens)),
			MaxCompletionTokens: int(lo.FromPtr(req.MaxCompletionTokens)),
			Temperature:         float32(lo.FromPtr(req.Temperature)),
			TopP:                float32(lo.FromPtr(req.TopP)),
			N:                   int(lo.FromPtr(req.N)),
			Stream:              lo.FromPtr(req.Stream),
			Stop:                req.Stop,
			PresencePenalty:     float32(lo.FromPtr(req.PresencePenalty)),
			FrequencyPenalty:    float32(lo.FromPtr(req.FrequencyPenalty)),
			LogProbs:            lo.FromPtr(req.LogProbs),
			TopLogProbs:         int(lo.FromPtr(req.TopLogProbs)),
			User:                lo.FromPtr(req.User),
			Tools: lo.Map(req.Tools, func(item *nextagent.Tool, _ int) openai.Tool {
				return openai.Tool{
					Type: openai.ToolType(item.Type),
					Function: lo.TernaryF(item.Function != nil, func() *openai.FunctionDefinition {
						return &openai.FunctionDefinition{
							Name:        item.Function.Name,
							Description: lo.FromPtr(item.Function.Description),
							Parameters:  conv.MapStringAny(item.Function.Parameters),
						}
					}, func() *openai.FunctionDefinition {
						return nil
					}),
				}
			}),
			ToolChoice:      req.ToolChoice,
			Store:           lo.FromPtr(req.Store),
			ReasoningEffort: lo.FromPtr(req.ReasoningEffort),
			Metadata:        req.Metadata,
		},
		Thinking: lo.TernaryF(req.Thinking != nil, func() *config.ThinkingConfig {
			return &config.ThinkingConfig{
				Type:            lo.FromPtr(req.Thinking.Type),
				BudgetTokens:    int(lo.FromPtr(req.Thinking.BudgetTokens)),
				IncludeThoughts: false,
			}
		}, func() *config.ThinkingConfig {
			return nil
		}),
	}
}

// ChatStream is compatible with openai chat/completion.
func (h *Handler) ChatStream(ctx context.Context, c *app.RequestContext) {
	nextReq := hertz.BindValidate[nextagent.ChatStreamRequest](ctx, c)
	if nextReq == nil {
		return
	}

	// 先解析为 ChatStreamRequest, 再转换为 openai.ChatCompletionRequest，原因是直接解析 ChatCompletionRequest 会丢失 multi_content 信息（内部识别为 content 字段）
	req := toOpenAIChatCompletionRequest(nextReq)
	llmServiceReq := llm.GetChatCompletionsRequestFromOpenAI(req.ChatCompletionRequest)
	llmServiceReq.Tag = string(c.GetHeader(framework.LLMTagKey))
	if req.Thinking != nil {
		llmServiceReq.Thinking = &llm.ThinkingOpt{
			Type:            req.Thinking.Type,
			BudgetTokens:    req.Thinking.BudgetTokens,
			IncludeThoughts: req.Thinking.IncludeThoughts,
		}
	}
	llmServiceReq.Record = false

	res, err := h.TraceService.ChatCompletion(ctx, llmServiceReq)
	if err != nil {
		log.V1.CtxError(ctx, "failed to do chat for model %s: %v", req.Model, err)
		openaiStyleJSONMessage(c, http.StatusBadRequest, "failed to init chat: "+err.Error())
		return
	}
	defer res.Close(ctx)

	if !req.Stream {
		aggRes, err := res.Aggregation(ctx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to aggregate response: %v", err)
			openaiStyleJSONMessage(c, http.StatusBadRequest, "failed to aggregate response: "+err.Error())
			return
		}
		c.JSON(http.StatusOK, llm.GetOpenAIChatCompletionsResponse(*aggRes))
		return
	}

	c.SetContentType("text/event-stream")
	s := sse.NewStream(c)
	defer s.Publish(&sse.Event{Data: []byte("[DONE]")})
	for {
		chunk, err := res.NextChunk(ctx)
		if err != nil {
			if errors.Is(err, io.EOF) {
				return
			}
			log.V1.CtxWarn(ctx, "failed to get next chunk: %v", err)
			s.Publish(&sse.Event{Data: conv.JSONBytes(utils.H{"error": openAIErrorResponseDetail{
				Code:    nil,
				Message: "failed to get next chunk: " + err.Error(),
				Type:    "internal_error",
				Param:   nil,
			}})})
			return
		}
		data := conv.JSONBytes(chunk)

		if err := s.Publish(&sse.Event{
			// Add space. openai process stream requires "data: {\"id":...}", while hertz-contrib/sse send "data:{\"id":...}" with no space.
			Data: append([]byte(" "), data...),
		}); err != nil { // Stop if client is disconnected, or it will get OOM.
			log.V1.CtxWarn(ctx, "failed to publish chunk: %v", err)
			return
		}
	}
}

func (h *Handler) TraceMCPEvents(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.TraceMCPEventsRequest](ctx, c)
	if req == nil {
		return
	}
	s := sse.NewStream(c)
	pub := func(data any) error {
		return s.Publish(&sse.Event{
			Event: "json-data",
			Data:  conv.JSONBytes(data),
		})
	}
	defer pub(LogEvent{
		Event: agentsphere.DSEventDone,
		Data:  agentsphere.DSDone{},
	})
	done := make(chan struct{}, 1)
	defer close(done)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "failed to send ping: %+v", r)
			}
		}()
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				pub(LogEvent{
					Event: "ping",
					Data:  "ping",
				})
			case <-done:
				return
			}
		}
	}()

	account, _ := h.AuthM.GetAccount(ctx, c)
	recv, err := h.TraceService.GetTraceMCPEvents(ctx, traceservice.GetTraceMCPEventsOption{
		SessionID: req.SessionID,
		Account:   account,
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	for data := range recv.DataChannel {
		if data.Event != iris.AgentRunEventToolCall && data.Event != iris.AgentRunEventThink {
			continue
		}

		// 过滤 llm_call 属性
		filteredData := filterLLMCall(data.Data)

		pubErr := pub(LogEvent{
			Event:     string(data.Event),
			Data:      filteredData,
			Offset:    data.Offset,
			Timestamp: data.Timestamp.Format(time.RFC3339),
		})
		if pubErr != nil {
			log.V1.CtxError(ctx, "failed to publish agent run stream events to sse: %v", pubErr)
			recv.Close()
			break
		}
	}
}
