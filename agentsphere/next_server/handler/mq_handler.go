package serverhandler

import (
	"context"
	"encoding/json"

	authservice "code.byted.org/devgpt/kiwis/copilotstack/common/auth/service"
	"code.byted.org/devgpt/kiwis/port/lark"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	testingservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/testing"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/proto"
)

func (h *Handler) StartConsumer() error {
	h.SessionMonitorMQ.RegisterHandler(h.HandleServerMonitorEvent)
	return h.SessionMonitorMQ.StartConsumer()
}

func (h *Handler) StopConsumer() error {
	return h.SessionMonitorMQ.Close()
}

func (h *Handler) HandleServerMonitorEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received session monitor event: %s", string(msg))
	m := entity.ServerMonitorEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	var err error
	switch m.EventName {
	case entity.MonitorEventTypeSessionCheck:
		if m.SessionCheckEvent != nil {
			err = h.MonitorService.CheckSession(ctx, m.SessionCheckEvent.SessionID)
		} else {
			log.V1.CtxError(ctx, "session check event is nil")
		}
	case entity.MonitorEventTypeTemplateCheck:
		if m.TemplateCheckEvent != nil {
			err = h.MonitorService.CheckTemplate(ctx, m.TemplateCheckEvent.TemplateID)
		}
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle session monitor event: %v", err)
		return err
	}
	return nil
}

func (h *Handler) KnowledgebaseStartConsumer() error {
	h.KnowledgebaseMQ.RegisterHandlerV2(h.HandleKnowledgebaseEvent)
	return h.KnowledgebaseMQ.StartConsumer()
}

func (h *Handler) KnowledgebaseStopConsumer() error {
	return h.KnowledgebaseMQ.Close()
}

func (h *Handler) KnowledgebaseOfflineStartConsumer() error {
	h.KnowledgebaseOfflineMQ.RegisterHandlerV2(h.HandleKnowledgebaseEvent)
	return h.KnowledgebaseOfflineMQ.StartConsumer()
}

func (h *Handler) KnowledgebaseOfflineStopConsumer() error {
	return h.KnowledgebaseOfflineMQ.Close()
}

func (h *Handler) HandleKnowledgebaseEvent(ctx context.Context, msg []byte, ext *proto.MessageExt) error {
	log.V1.CtxInfo(ctx, "received knowledge base event: %s", string(msg))
	m := entity.KnowledgebaseEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	var err error
	switch {
	case m.DocumentContentEvent != nil:
		err = h.KnowledgebaseService.UpsertContent(ctx, m.DocumentContentEvent, ext.ReconsumeTime)
		if errors.Is(err, lark.ErrLarkAuthFailed) || errors.Is(err, lark.ErrLarkAuthUnauthorized) ||
			errors.Is(err, lark.ErrPermissionDenied) || errors.Is(err, lark.ErrResourceNotFound) { // 不需要重试
			return nil
		}
	case m.ImportWikiEvent != nil:
		err = h.KnowledgebaseService.ImportWiki(ctx, m.ImportWikiEvent)
	default:
		log.V1.CtxError(ctx, "knowledge base event is nil")
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle knowledge base event: %v", err)
		return err
	}
	return nil
}

func (h *Handler) TraceStartConsumer() error {
	h.TraceMQ.RegisterHandler(h.HandleTraceEvent)
	return h.TraceMQ.StartConsumer()
}

func (h *Handler) TraceStopConsumer() error {
	return h.TraceMQ.Close()
}

func (h *Handler) HandleTraceEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received trace event: %s", string(msg))
	m := entity.NextTraceEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	var err error
	switch {
	case m.TraceData != nil:
		err = h.DebugService.UpsertTrace(ctx, m)
	default:
		log.V1.CtxError(ctx, "trace event is nil")
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle trace event: %v", err)
		// skip return error to avoid retry
		return nil
	}
	return nil
}

func (h *Handler) DeployReviewStartConsumer() error {
	h.DeployReviewMQ.RegisterHandler(h.HandleDeployReviewEvent)
	return h.DeployReviewMQ.StartConsumer()
}

func (h *Handler) DeployReviewStopConsumer() error {
	return h.DeployReviewMQ.Close()
}

func (h *Handler) HandleDeployReviewEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received deploy review event: %s", string(msg))
	m := entity.DeployReviewEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	// 查询灰度比例，运行上线流水线
	err := h.DeployReviewService.HandleWorkflowProcess(ctx, &m)
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle deploy review event: %v", err)
		return err
	}

	return nil
}

func (h *Handler) TestingMonitorStartConsumer() error {
	h.TestingMQ.RegisterHandler(h.HandleTestingMonitorEvent)
	return h.TestingMQ.StartConsumer()
}

func (h *Handler) TestingMonitorStopConsumer() error {
	return h.TestingMQ.Close()
}

func (h *Handler) HandleTestingMonitorEvent(ctx context.Context, msg []byte) error {
	log.V1.CtxInfo(ctx, "received testing event: %s", string(msg))
	m := testingservice.RecycleContainerEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	err := h.TestingService.HandleTestingMonitorEvent(ctx, &m)
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle testing monitor event: %v", err)
		return err
	}

	return nil
}

func (h *Handler) CronTaskStartConsumer() error {
	h.CronTaskMQ.RegisterHandlerV2(h.HandleCronTaskEvent)
	return h.CronTaskMQ.StartConsumer()
}

func (h *Handler) CronTaskStopConsumer() error {
	return h.CronTaskMQ.Close()
}

func (h *Handler) HandleCronTaskEvent(ctx context.Context, msg []byte, ext *proto.MessageExt) error {
	log.V1.CtxInfo(ctx, "received cron task event: %s", string(msg))
	if ext == nil {
		log.V1.CtxError(ctx, "cron task event is nil")
		return errors.New("cron task event is nil")
	}
	m := entity.CronTaskEvent{}
	if err := json.Unmarshal(msg, &m); err != nil {
		return err
	}
	if h.CronTaskService == nil {
		log.V1.CtxError(ctx, "cron task service is nil")
		return errors.New("cron task service is nil")
	}
	task, err := h.CronTaskService.GetCronTask(ctx, m.CronTaskUniqueID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get cron task: %v", err)
		return err
	}
	if task == nil {
		log.V1.CtxError(ctx, "cron task is nil")
		return nil
	}

	// 3. 检查事件ID匹配（定时任务场景）
	if ext.Msg == nil || ext.GetMsg() == nil {
		log.V1.CtxError(ctx, "event id is nil")
		return nil
	}
	// use uniqID to judge if the event is the same(https://cloud-boe.bytedance.net/docs/rocketmq/docs/646f4e4111c9bb02200052ba/64a51dbef858f4021c5e13b3?x-resource-account=boe&x-bc-region-id=bytedance)
	if task.NextMQEventID != ext.GetMsg().UniqId {
		logs.V1.CtxError(ctx, "event id not match, expected: %v, got: %v", task.NextMQEventID, ext)
		return nil
	}
	if h.CronTaskService == nil {
		log.V1.CtxError(ctx, "cron task service is nil")
		return errors.New("cron task service is nil")
	}
	if h.CronTaskService.CloudOAuthClient == nil {
		log.V1.CtxError(ctx, "cloud oauth client is nil")
		return errors.New("cloud oauth client is nil")
	}
	token, err := h.CronTaskService.CloudOAuthClient.GetJwtToken(ctx, task.UserName)
	if err != nil {
		logs.V1.CtxError(ctx, "Failed to get jwt token")
		return err
	}
	payload, err := h.CronTaskService.ByteCloudValidator.Validate(ctx, token)
	if err != nil {
		logs.V1.CtxError(ctx, "Failed to validate byte cloud jwt")
		return errors.WithMessage(err, "invalid byte cloud jwt")
	}
	account, err := h.AuthM.AuthService.AuthByteCloudJWT(ctx, token)
	if err != nil {
		logs.V1.CtxError(ctx, "Failed to refresh next code jwt")
		//return err
	}
	if account == nil {
		logs.V1.CtxError(ctx, "Failed to refresh next code jwt")
		return errors.New("failed to refresh next code jwt")
	}

	_, _, _, err = h.CronTaskService.DoTask(ctx, task, entity.TriggerTypeCronJob, ext.MsgId,
		task.UID,
		&authentity.Account{
			ID:              payload.EmployeeID,
			Username:        task.UserName,
			Name:            payload.Username,
			Type:            string(payload.Type),
			Department:      payload.Organization,
			Email:           payload.Email,
			AuthOrigin:      string(authservice.AuthOriginByteCloud),
			CodebaseUserJWT: account.CodebaseUserJWT,
			NextCodeUserJWT: account.NextCodeUserJWT,
			CloudUserJWT:    token,
		})
	if err != nil {
		log.V1.CtxError(ctx, "failed to handle cron task event: %v", err)
		return err
	}
	return nil
}
