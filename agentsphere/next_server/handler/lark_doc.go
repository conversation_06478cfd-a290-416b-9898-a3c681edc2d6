package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/samber/lo"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

// ListLarkDocComments 获取飞书云文档所有评论
func (h *Handler) ListLarkDocComments(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListLarkDocCommentsRequest](ctx, c)
	if req == nil {
		log.V1.CtxInfo(ctx, "[ListLarkDocComments] request is nil")
		return
	}

	account, _ := h.AuthM.GetAccount(ctx, c)
	if account == nil {
		log.V1.CtxInfo(ctx, "[ListLarkDocComments] account is nil")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "account is required")
		return
	}

	if req.SessionID == "" {
		log.V1.CtxError(ctx, "[ListLarkDocComments] session_id is required")
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "session_id is required")
		return
	}

	artifactComments, err := h.LarkService.ListLarkDocComments(ctx, req.SessionID, account.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[ListLarkDocComments] failed to list lark doc comments: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	c.JSON(http.StatusOK, nextagent.ListLarkDocCommentsResponse{
		LarkDocComments: lo.Map(artifactComments, func(item *entity.ArtifactLarkDocComment, _ int) *nextagent.LarkDocCommentItem {
			return &nextagent.LarkDocCommentItem{
				ArtifactID: item.ArtifactID,
				Name:       item.Name,
				LarkDocUrl: item.LarkDocUrl,
				Comments: lo.Map(item.Comments, func(item *agententity.CommentMetadata, _ int) *nextagent.CommentMetadata {
					return pack.ConvertCommentMetadataToDTO(item)
				}),
				Version: item.Version,
			}
		}),
	})
}
