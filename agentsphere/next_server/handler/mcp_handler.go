package serverhandler

import (
	"context"
	"net/http"
	"strconv"

	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/lib/cloud_mcp"
	"code.byted.org/devgpt/kiwis/lib/util"

	"github.com/samber/lo"

	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	mcpservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mcp"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
)

const (
	MCPUnavailableCode = 40001
)

// CreateMCP 创建 MCP 工具
// POST /api/agents/v2/mcp
func (h *Handler) CreateMCP(ctx context.Context, c *app.RequestContext) {
	var req nextagent.CreateMCPRequest

	// 初始化Response
	resp := nextagent.CreateMCPResponse{BaseResp: &common.BaseResp{
		Code: 0,
	}}

	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind create mcp request failed: %v", err)
		resp.BaseResp.Code = 400
		resp.BaseResp.Message = "invalid request"
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		resp.BaseResp.Code = 401
		resp.BaseResp.Message = "unauthorized"
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	// 线上创建和更新MCP 不允许走此接口，鉴权问题
	if entity.MCPSource(req.Source) != entity.MCPSourceUserDefine {
		resp.BaseResp.Code = 403
		resp.BaseResp.Message = "Non-personal MCPS are not allowed to be created/updated"
		c.JSON(http.StatusForbidden, resp)
		return
	}

	larkOpenID := ""
	user, err := h.LarkService.GetLarkUser(ctx, account.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = user.OpenID
	}
	// 构建创建请求
	createOpt := &mcpservice.CreateMCPOption{
		UID:            req.UID,
		Name:           req.Name,
		EnName:         gptr.Indirect(req.ENName),
		Description:    req.Description,
		EnDescription:  gptr.Indirect(req.EnDescription),
		IconURL:        gptr.Indirect(req.IconURL),
		Creator:        account.Username,
		Source:         entity.MCPSource(req.Source),
		Type:           entity.MCPType(req.Type),
		ForceActive:    gptr.Indirect(req.ForceActive),
		DefaultActive:  gptr.Indirect(req.DefaultActive),
		Config:         entity.MCPConfigFromDTO(req.Config),
		Token:          account.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
		SessionRoles:   req.SessionRoles,
		SpaceID:        req.GetSpaceID(),
		Scope:          pack.ConvertMCPScopeToEntity(req.GetScope()),
	}

	// 调用服务层创建 MCP
	mcp, err := h.MCPService.CreateMCP(ctx, createOpt)
	if err != nil {
		log.V1.CtxError(ctx, "create mcp failed: %v", err)
		resp.BaseResp.Code = int64(choose.If(errors.Is(err, mcpservice.ErrMCPUnavailable), MCPUnavailableCode, 500))
		resp.BaseResp.Message = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.MCP = pack.ConvertMCPToDTOForUser(mcp, account.Username)
	// 返回响应
	c.JSON(http.StatusOK, resp)
}

// UpdateMCP 更新 MCP 工具
// PUT /api/agents/v2/mcp/update
func (h *Handler) UpdateMCP(ctx context.Context, c *app.RequestContext) {
	var req nextagent.UpdateMCPRequest

	// 初始化Response
	resp := nextagent.UpdateMCPResponse{BaseResp: &common.BaseResp{
		Code: 0,
	}}

	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind update mcp request failed: %v", err)
		resp.BaseResp.Code = 400
		resp.BaseResp.Message = "invalid request"
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		resp.BaseResp.Code = 401
		resp.BaseResp.Message = "unauthorized"
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	// 线上创建和更新MCP 不允许走此接口，鉴权问题
	if entity.MCPSource(req.Source) != entity.MCPSourceUserDefine {
		resp.BaseResp.Code = 403
		resp.BaseResp.Message = "Non-personal MCPS are not allowed to be created/updated"
		c.JSON(http.StatusForbidden, resp)
		return
	}

	larkOpenID := ""
	user, err := h.LarkService.GetLarkUser(ctx, account.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = user.OpenID
	}

	// 校验权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(account),
		ResourceID:         nil,
		ResourceExternalID: &req.ID,
		ResourceType:       entity.ResourceTypeMCP.Ptr(),
		Action:             entity.PermissionActionMCPUpdate,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		resp.BaseResp.Code = 500
		resp.BaseResp.Message = "internal server error"
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	if !result.Allowed {
		log.V1.CtxWarn(ctx, "unauthorized to update mcp: user=%s", account.Username)
		resp.BaseResp.Code = 403
		resp.BaseResp.Message = "permission denied"
		c.JSON(http.StatusForbidden, resp)
		return
	}

	// 构建更新请求
	updateOpt := &mcpservice.UpdateMCPOption{
		MCPID:          req.ID,
		Source:         entity.MCPSource(req.Source),
		Name:           req.Name,
		EnName:         req.ENName,
		Description:    req.Description,
		EnDescription:  req.EnDescription,
		IconURL:        req.IconURL,
		ForceActive:    req.ForceActive,
		Token:          account.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
		SessionRoles:   req.SessionRoles,
		Scope:          pack.ConvertMCPScopeToEntity(req.GetScope()),
		DefaultActive:  req.DefaultActive,
		Config:         entity.MCPConfigFromDTO(req.Config),
		Username:       account.Username,
	}

	if req.Type != nil {
		mcpType := entity.MCPType(gptr.Indirect(req.Type))
		updateOpt.Type = &mcpType
	}

	// 调用服务层更新 MCP
	mcp, err := h.MCPService.UpdateMCP(ctx, updateOpt)

	if err != nil {
		log.V1.CtxError(ctx, "update mcp failed: %v", err)
		httpCode := http.StatusInternalServerError
		resp.BaseResp.Code = int64(choose.If(errors.Is(err, mcpservice.ErrMCPNotFound), 400,
			choose.If(errors.Is(err, mcpservice.ErrMCPUnavailable), MCPUnavailableCode, 500)))
		resp.BaseResp.Message = err.Error()
		c.JSON(httpCode, resp)
		return
	}

	resp.MCP = pack.ConvertMCPToDTOForUser(mcp, account.Username)
	// 返回响应
	c.JSON(http.StatusOK, resp)
}

// ListMCP 列出 MCP 工具 现在成了后台管理专属接口
// GET /api/agents/v2/mcp
func (h *Handler) ListMCP(ctx context.Context, c *app.RequestContext) {
	var req nextagent.ListMCPRequest
	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind list mcp request failed: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, 400, "invalid request")
		return
	}

	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, 401, "unauthorized")
		return
	}

	listOpt := &mcpservice.ListMCPOption{
		Name:     req.Name,
		IsActive: req.IsActive,
		// 添加SessionRole过滤 (二期新增)
		SessionRole: req.SessionRole,
	}

	// 转换 Sources
	if len(req.Sources) > 0 {
		sources := make([]entity.MCPSource, 0, len(req.Sources))
		for _, s := range req.Sources {
			sources = append(sources, entity.MCPSource(s))
		}
		listOpt.Sources = sources
	}

	// 设置当前用户名，用于筛选个人MCP
	// Service层会确保当搜索包含个人来源时，只返回当前用户创建的MCP
	listOpt.Creator = &account.Username

	// 转换 Types
	if len(req.Types) > 0 {
		types := make([]entity.MCPType, 0, len(req.Types))
		for _, t := range req.Types {
			types = append(types, entity.MCPType(t))
		}
		listOpt.Types = types
	}

	// 调用服务层列出 MCP，服务层会自动处理：
	// 1. 根据条件筛选MCP
	// 2. 设置激活状态
	// 3. 按优先级排序
	mcps, total, err := h.MCPService.ListMCP(ctx, listOpt)
	if err != nil {
		log.V1.CtxError(ctx, "list mcp failed: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, 500, "list mcp failed")
		return
	}

	// 转换响应
	respMCPs := make([]*nextagent.MCP, 0, len(mcps))
	for _, mcp := range mcps {
		respMCPs = append(respMCPs, pack.ConvertMCPToDTOForAdmin(mcp))
	}

	// 返回响应
	c.JSON(http.StatusOK, nextagent.ListMCPResponse{
		MCPs:  respMCPs,
		Total: int32(total), // 保持total为总数，不受本地分页影响
	})
}

// ListSpaceMCP 列出空间下的 MCP 工具
func (h *Handler) ListSpaceMCP(ctx context.Context, c *app.RequestContext) {
	var req nextagent.ListSpaceMCPRequest
	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind list mcp request failed: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, 400, "invalid request")
		return
	}

	// 获取当前用户
	user, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, 401, "unauthorized")
		return
	}

	// 转换 Types
	var types = make([]entity.MCPType, 0, len(req.Types))
	for _, t := range req.Types {
		types = append(types, entity.MCPType(t))
	}
	// 实现空间MCP列表查询逻辑
	startID := util.FromString(req.GetNextID())
	opt := &mcpservice.ListSpaceMCPOption{
		SpaceID:       req.GetSpaceID(),
		Name:          req.Name,
		IsActive:      req.IsActive,
		Types:         types,
		User:          user,
		SessionRole:   req.SessionRole,
		MCPSourceTabs: req.Tabs,
		Limit:         req.Limit,
		StartID:       &startID,
	}
	mcps, nextID, err := h.MCPService.ListSpaceMCP(ctx, opt)
	if err != nil {
		log.V1.CtxError(ctx, "list space mcp failed: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, 500, "list space mcp failed")
		return
	}
	// 转换响应
	respMCPs := make([]*nextagent.MCP, 0, len(mcps))
	for _, mcp := range mcps {
		respMCPs = append(respMCPs, pack.ConvertMCPToDTOForUser(mcp, user.Username))
	}
	mcpCount, err := h.MCPService.CountSpaceMCP(ctx, opt)
	if err != nil {
		log.V1.CtxError(ctx, "failed to count space mcp: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, 500, "failed to count space mcp")
		return
	}
	// 返回响应
	c.JSON(http.StatusOK, nextagent.ListSpaceMCPResponse{
		MCPs:    respMCPs,
		HasMore: nextID > 0,
		NextID:  strconv.FormatInt(nextID, 10),
		Count: &nextagent.ListMCPCount{
			PublicCount:   mcpCount.PublicCount,
			CustomCount:   mcpCount.CustomCount,
			ActivateCount: mcpCount.ActivateCount,
			ActivateLimit: mcpCount.ActivateLimit,
		},
	})
}

// ModifyMCPActivation 修改用户在指定空间中某个MCP工具的激活状态
// POST /api/agents/v2/mcp/activation
func (h *Handler) ModifyMCPActivation(ctx context.Context, c *app.RequestContext) {
	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, 401, "unauthorized")
		return
	}
	// 绑定请求体并获取Status字段
	var req nextagent.ModifyMCPActivationRequest
	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind activation request failed: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, 400, "invalid request")
		return
	}

	// 调用服务层激活/取消激活 MCP
	mcp, err := h.MCPService.UpdateUserMCP(ctx, entity.MCPKey{MCPID: req.ID, Source: entity.MCPSource(req.Source)},
		mcpservice.UserScopedMCPQuery{Username: account.Username, SpaceID: gptr.Indirect(req.SpaceID)},
		gptr.Of(entity.ActiveStatus(req.Status)), nil, nil)
	if err != nil {
		log.V1.CtxError(ctx, "modify mcp activation failed: %v", err)
		if errors.Is(err, mcpservice.ErrMCPNotFound) {
			hertz.JSONMessage(c, http.StatusNotFound, 404, "mcp not found")
			return
		}
		if errors.Is(err, mcpservice.ErrMCPActiveLimit) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrMCPActiveLimitExceeded), "mcp activation limit")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, 500, err.Error())
		return
	}

	// 返回响应
	c.JSON(http.StatusOK, nextagent.ModifyMCPActivationResponse{
		MCP: pack.ConvertMCPToDTOForUser(mcp, account.Username),
	})
}

// UpdateMCPUserConfig 修改用户在指定空间中某个MCP工具的个性化配置（如命令参数、环境变量等）
// POST /api/agents/v2/mcp/user_config
func (h *Handler) UpdateMCPUserConfig(ctx context.Context, c *app.RequestContext) {
	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, 401, "unauthorized")
		return
	}
	larkOpenID := ""
	larkUser, err := h.LarkService.GetLarkUser(ctx, account.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = larkUser.OpenID
	}

	// 绑定请求体并获取Status字段
	var req nextagent.ModifyMCPUserConfigRequest
	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind activation request failed: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, 400, "invalid request")
		return
	}

	if req.UserConfig == nil {
		hertz.JSONMessage(c, http.StatusBadRequest, 400, "user config can not be nil")
		return
	}

	// 调用服务层修改UserConfig
	mcp, err := h.MCPService.UpdateUserMCP(ctx, entity.MCPKey{MCPID: req.ID, Source: entity.MCPSource(req.Source)},
		mcpservice.UserScopedMCPQuery{Username: account.Username, SpaceID: gptr.Indirect(req.SpaceID)},
		nil, entity.MCPConfigFromDTO(req.UserConfig), &mcpservice.MCPAccessContext{
			Token:          account.CloudUserJWT,
			LarkUserOpenID: larkOpenID,
		})
	if err != nil {
		log.V1.CtxError(ctx, "modify mcp activation failed: %v", err)
		if errors.Is(err, mcpservice.ErrMCPNotFound) {
			hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrRecordNotFound), "mcp not found")
			return
		}
		if errors.Is(err, mcpservice.ErrMCPConfigUnavailable) {
			// 返回响应
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrCodeMCPConnectFailed), err.Error())
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, 500, err.Error())
		return
	}

	// 返回响应
	c.JSON(http.StatusOK, nextagent.ModifyMCPUserConfigResponse{
		MCP: pack.ConvertMCPToDTOForUser(mcp, account.Username),
	})
}

// ValidateMCP 验证MCP工具配置
// POST /api/agents/v2/mcp/validate
func (h *Handler) ValidateMCP(ctx context.Context, c *app.RequestContext) {
	var req nextagent.ValidateMCPRequest

	// 初始化Response
	resp := nextagent.ValidateMCPResponse{}

	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind validate mcp request failed: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, 400, "invalid request")
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "unauthorized")
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	larkOpenID := ""
	user, err := h.LarkService.GetLarkUser(ctx, account.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = user.OpenID
	}

	// 构建验证请求
	validateOpt := &mcpservice.MCPConfigAccessContext{
		Source:         entity.MCPSource(req.Source),
		Type:           entity.MCPType(req.Type),
		Config:         entity.MCPConfigFromDTO(req.Config),
		Token:          account.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
	}

	// 调用服务层验证MCP
	result := h.MCPService.ValidateMCPOnlyConfig(ctx, validateOpt)
	if result.Code != 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, int(result.Code), result.Msg)
		return
	}
	resp.Valid = result.Valid
	// 返回响应
	c.JSON(http.StatusOK, resp)
}

// CreateBuildInMCP 创建 MCP 工具
// POST /api/agents/v2/build_in_mcp/create
func (h *Handler) CreateBuildInMCP(ctx context.Context, c *app.RequestContext) {
	var req nextagent.CreateMCPRequest

	// 初始化Response
	resp := nextagent.CreateMCPResponse{BaseResp: &common.BaseResp{
		Code: 0,
	}}

	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind create mcp request failed: %v", err)
		resp.BaseResp.Code = 400
		resp.BaseResp.Message = "invalid request"
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		resp.BaseResp.Code = 401
		resp.BaseResp.Message = "unauthorized"
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	larkOpenID := ""
	user, err := h.LarkService.GetLarkUser(ctx, account.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = user.OpenID
	}

	// 构建创建请求
	createOpt := &mcpservice.CreateMCPOption{
		UID:            req.UID,
		Name:           req.Name,
		EnName:         gptr.Indirect(req.ENName),
		Description:    req.Description,
		EnDescription:  gptr.Indirect(req.EnDescription),
		IconURL:        gptr.Indirect(req.IconURL),
		Creator:        account.Username,
		Source:         entity.MCPSource(req.Source),
		Type:           entity.MCPType(req.Type),
		ForceActive:    gptr.Indirect(req.ForceActive),
		DefaultActive:  gptr.Indirect(req.DefaultActive),
		Config:         entity.MCPConfigFromDTO(req.Config),
		Token:          account.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
		SessionRoles:   req.SessionRoles,
		SpaceID:        req.GetSpaceID(),
		Scope:          pack.ConvertMCPScopeToEntity(req.GetScope()),
	}

	// 调用服务层创建 MCP
	mcp, err := h.MCPService.CreateMCP(ctx, createOpt)
	if err != nil {
		log.V1.CtxError(ctx, "create mcp failed: %v", err)
		resp.BaseResp.Code = int64(choose.If(errors.Is(err, mcpservice.ErrMCPUnavailable), MCPUnavailableCode, 500))
		resp.BaseResp.Message = err.Error()
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp.MCP = pack.ConvertMCPToDTOForAdmin(mcp)
	// 返回响应
	c.JSON(http.StatusOK, resp)
}

// UpdateBuildInMCP 更新 MCP 工具
// PUT /api/agents/v2/build_in_mcp/update
func (h *Handler) UpdateBuildInMCP(ctx context.Context, c *app.RequestContext) {
	var req nextagent.UpdateMCPRequest

	// 初始化Response
	resp := nextagent.UpdateMCPResponse{BaseResp: &common.BaseResp{
		Code: 0,
	}}

	if err := c.BindAndValidate(&req); err != nil {
		log.V1.CtxError(ctx, "bind update mcp request failed: %v", err)
		resp.BaseResp.Code = 400
		resp.BaseResp.Message = "invalid request"
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 获取当前用户
	account, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		resp.BaseResp.Code = 401
		resp.BaseResp.Message = "unauthorized"
		c.JSON(http.StatusUnauthorized, resp)
		return
	}

	larkOpenID := ""
	user, err := h.LarkService.GetLarkUser(ctx, account.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = user.OpenID
	}

	// 构建更新请求
	updateOpt := &mcpservice.UpdateMCPOption{
		MCPID:          req.ID,
		Source:         entity.MCPSource(req.Source),
		Name:           req.Name,
		EnName:         req.ENName,
		Description:    req.Description,
		EnDescription:  req.EnDescription,
		IconURL:        req.IconURL,
		ForceActive:    req.ForceActive,
		Token:          account.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
		SessionRoles:   req.SessionRoles,
		Scope:          pack.ConvertMCPScopeToEntity(req.GetScope()),
		Config:         entity.MCPConfigFromDTO(req.Config),
		Username:       user.Username,
	}

	if req.Type != nil {
		mcpType := entity.MCPType(gptr.Indirect(req.Type))
		updateOpt.Type = &mcpType
	}

	// 调用服务层更新 MCP
	mcp, err := h.MCPService.UpdateMCP(ctx, updateOpt)

	if err != nil {
		log.V1.CtxError(ctx, "update mcp failed: %v", err)
		httpCode := http.StatusInternalServerError
		resp.BaseResp.Code = int64(choose.If(errors.Is(err, mcpservice.ErrMCPNotFound), 400,
			choose.If(errors.Is(err, mcpservice.ErrMCPUnavailable), MCPUnavailableCode, 500)))
		resp.BaseResp.Message = err.Error()
		c.JSON(httpCode, resp)
		return
	}

	resp.MCP = pack.ConvertMCPToDTOForAdmin(mcp)
	// 返回响应
	c.JSON(http.StatusOK, resp)
}

// ValidateActiveMCPs 校验已添加的mcp
func (h *Handler) ValidateActiveMCPs(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ValidateActiveMCPsRequest](ctx, c)
	if req == nil {
		return
	}
	user, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	larkOpenID := ""
	larkUser, err := h.LarkService.GetLarkUser(ctx, user.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = larkUser.OpenID
	}
	// 1 这里是用户发起的会话，查询用户所有已激活的mcp，组装本次会话使用的MCPs
	mcps, _, err := h.MCPService.ListSpaceMCP(ctx, &mcpservice.ListSpaceMCPOption{
		SpaceID:     req.GetSpaceID(),
		IsActive:    gptr.Of(true),
		User:        user,
		SessionRole: req.Role,
		MCPSourceTabs: []nextagent.MCPSourceTab{
			nextagent.MCPSourceTab_Custom,
			nextagent.MCPSourceTab_Builtin,
		},
	})
	if err != nil {
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	// 2 设置权限
	h.MCPService.TrySetMCPPermissions(ctx, user, req.GetSpaceID(), mcps)
	// 3 校验
	results := h.MCPService.BatchValidateMCP(ctx, &mcpservice.BatchMCPAccessContext{
		MCPs:           mcps,
		Token:          user.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
	})
	resp := &nextagent.ValidateActiveMCPsResponse{AllValid: true}
	for i := range results {
		result := results[i]
		resp.AllValid = resp.AllValid && result.Valid
		r := &nextagent.ValidateMCPResult{
			Mcp: pack.ConvertMCPToDTOForUser(mcps[i], user.Username),
		}
		// 兜底异常情况
		if result == nil {
			r.Valid = false
			r.Code = gptr.Of(int64(common.ErrorCode_ErrCodeMCPConnectFailed))
			r.Msg = gptr.Of("system is temporarily abnormal. please try again later")
			resp.Results = append(resp.Results, r)
			continue
		}
		r.Valid = result.Valid
		r.Code = gptr.Of(result.Code)
		r.Msg = gptr.Of(result.Msg)
		resp.Results = append(resp.Results, r)
	}
	c.JSON(http.StatusOK, resp)
}

func (h *Handler) ValidateTemplateMCPs(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ValidateTemplateMCPsRequest](ctx, c)
	if req == nil {
		return
	}
	user, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	larkOpenID := ""
	larkUser, err := h.LarkService.GetLarkUser(ctx, user.Username)
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark user failed: %v", err)
	} else {
		larkOpenID = larkUser.OpenID
	}
	// 校验模板权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceID:         nil,
		ResourceExternalID: &req.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		Action:             entity.PermissionActionTemplateRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to create message with template")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	// 获取模板MCPs
	template, err := h.TemplateService.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}
	// 实时从 DB 获取 MCPs
	mcps, err := h.MCPService.GetMCPsByKeys(ctx, &mcpservice.GetMCPsByKeysOption{MCPKeys: template.SupportMCPs,
		UserScopedMCPQuery: &mcpservice.UserScopedMCPQuery{
			Username: user.Username, SpaceID: gptr.Indirect(req.SpaceID),
		}})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get MCPs: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get MCPs")
		return
	}
	// 过滤限定角色的MCP
	mcps = entity.FilterMCPsBySessionRole(mcps, gptr.Indirect(req.Role))

	//  设置权限
	h.MCPService.TrySetMCPPermissions(ctx, user, req.GetSpaceID(), mcps)

	results := h.MCPService.BatchValidateMCP(ctx, &mcpservice.BatchMCPAccessContext{
		MCPs:           mcps,
		Token:          user.CloudUserJWT,
		LarkUserOpenID: larkOpenID,
	})
	resp := &nextagent.ValidateTemplateMCPsResponse{AllValid: true}
	for i := range results {
		result := results[i]
		resp.AllValid = resp.AllValid && result.Valid
		r := &nextagent.ValidateMCPResult{
			Mcp: pack.ConvertMCPToDTOForUser(mcps[i], user.Username),
		}
		// 兜底异常情况
		if result == nil {
			r.Valid = false
			r.Code = gptr.Of(int64(common.ErrorCode_ErrCodeMCPConnectFailed))
			r.Msg = gptr.Of("system is temporarily abnormal. please try again later")
			resp.Results = append(resp.Results, r)
			continue
		}
		r.Valid = result.Valid
		r.Code = gptr.Of(result.Code)
		r.Msg = gptr.Of(result.Msg)
		resp.Results = append(resp.Results, r)
	}
	c.JSON(http.StatusOK, resp)
}

func (h *Handler) CreateCloudSDKAuthTicket(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateCloudSDKAuthTicketRequest](ctx, c)
	if req == nil {
		return
	}
	user, exists := h.AuthM.GetAccount(ctx, c)
	if !exists {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	var resp = nextagent.CreateCloudSDKAuthTicketResponse{}
	authTicket, err := h.MCPService.CreateCloudMCPAuth(ctx, user.CloudUserJWT, req.PSM)
	if err != nil {
		if errors.Is(err, cloud_mcp.ErrRecordNotFound) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrCodeMCPPSMNotFound), err.Error())
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}
	resp.TicketURL = &authTicket
	c.JSON(http.StatusOK, resp)
	return
}
