package serverhandler

import (
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	conf "code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
)

func replaceEvents(events []*entity.Event, config *conf.AgentSphereConfig) []*entity.Event {
	var deployConfig *conf.AgentDeploymentConfig
	if config != nil {
		deployConfig = &config.AgentDeploymentConfig
	}

	return lo.Map(events, func(d *entity.Event, _ int) *entity.Event {
		switch d.EventName {
		case nextagent.EventNameMessageCreate:
			var (
				s    = conv.DefaultAny[map[string]any](d.EventData.Data)
				e, _ = conv.MapToStructByJSONTag[*nextagent.MessageCreateEvent](s)
			)
			if e == nil {
				return d
			}
			if e.Message != nil {
				e.Message.Content = replaceNeumaAndDevagent(e.Message.Content)
				e.Message.Attachments = lo.Map(e.Message.Attachments, func(attachment *nextagent.Attachment, _ int) *nextagent.Attachment {
					attachment.URL = replaceNeumaAndDevagent(attachment.URL)
					return attachment
				})
			}
			e.EventKey = d.EventKey
			return &entity.Event{
				ID:        e.EventID,
				SessionID: e.Message.SessionID,
				TaskID:    e.Message.TaskID,
				EventName: d.EventName,
				EventData: entity.EventData{
					Event: d.EventName,
					Data:  e,
				},
				EventOffset: e.EventOffset,
				EventKey:    e.EventKey,
			}
		case nextagent.EventNameStepUpdate:
			var (
				s    = conv.DefaultAny[map[string]any](d.EventData.Data)
				e, _ = conv.MapToStructByJSONTag[*nextagent.StepUpdateEvent](s)
			)
			if e == nil {
				return d
			}
			e.Summary = replaceNeumaAndDevagent(e.Summary)
			e.Description = replaceNeumaAndDevagent(e.Description)
			e.EventKey = d.EventKey
			return &entity.Event{
				ID:        e.EventID,
				SessionID: e.SessionID,
				TaskID:    "", // TODO: task id
				EventName: d.EventName,
				EventData: entity.EventData{
					Event: d.EventName,
					Data:  e,
				},
				EventOffset: e.EventOffset,
				EventKey:    e.EventKey,
			}
		case nextagent.EventNamePlanUpdate:
			var (
				s    = conv.DefaultAny[map[string]any](d.EventData.Data)
				e, _ = conv.MapToStructByJSONTag[*nextagent.PlanUpdateEvent](s)
			)
			if e == nil {
				return d
			}
			e.Steps = lo.Map(e.Steps, func(step *nextagent.Step, _ int) *nextagent.Step {
				step.Title = replaceNeumaAndDevagent(step.Title)
				return step
			})
			e.EventKey = d.EventKey
			return &entity.Event{
				ID:        e.EventID,
				SessionID: e.SessionID,
				TaskID:    "", // TODO: task id
				EventName: d.EventName,
				EventData: entity.EventData{
					Event: d.EventName,
					Data:  e,
				},
				EventOffset: e.EventOffset,
				EventKey:    e.EventKey,
			}
		case nextagent.EventNameUseTool:
			var (
				s    = conv.DefaultAny[map[string]any](d.EventData.Data)
				e, _ = conv.MapToStructByJSONTag[*nextagent.UseToolEvent](s)
			)
			if e == nil {
				return d
			}
			e.Summary = replaceNeumaAndDevagent(e.Summary)
			e.Description = replaceNeumaAndDevagent(e.Description)
			if e.Browser != nil {
				e.Browser.URL = replaceNeumaAndDevagent(e.Browser.URL)
				if len(e.Browser.DeployID) > 0 { // 动态拼接部署 URL
					e.Browser.URL = entity.GetDeploymentURLByID(e.Browser.DeployID, "", nil, deployConfig)
				}
			}
			e.EventKey = d.EventKey
			return &entity.Event{
				ID:        e.EventID,
				SessionID: e.SessionID,
				TaskID:    "", // TODO: task id
				EventName: d.EventName,
				EventData: entity.EventData{
					Event: d.EventName,
					Data:  e,
				},
				EventOffset: e.EventOffset,
				EventKey:    e.EventKey,
			}
		case nextagent.EventNameToolCallRequired:
			var (
				s    = conv.DefaultAny[map[string]any](d.EventData.Data)
				e, _ = conv.MapToStructByJSONTag[*nextagent.ToolCallRequiredEvent](s)
			)
			if e == nil {
				return d
			}
			e.Question = replaceNeumaAndDevagent(e.Question)
			e.EventKey = d.EventKey
			return &entity.Event{
				ID:        e.EventID,
				SessionID: e.SessionID,
				TaskID:    "", // TODO: task id
				EventName: d.EventName,
				EventData: entity.EventData{
					Event: d.EventName,
					Data:  e,
				},
				EventOffset: e.EventOffset,
				EventKey:    e.EventKey,
			}
		case nextagent.EventNameProgressNotice:
			var (
				s    = conv.DefaultAny[map[string]any](d.EventData.Data)
				e, _ = conv.MapToStructByJSONTag[*nextagent.ProgressNoticeEvent](s)
			)
			if e == nil {
				return d
			}
			e.EventKey = d.EventKey
			return &entity.Event{
				ID:        e.EventID,
				SessionID: e.SessionID,
				TaskID:    "", // TODO: task id
				EventName: d.EventName,
				EventData: entity.EventData{
					Event: d.EventName,
					Data:  e,
				},
				EventOffset: e.EventOffset,
				EventKey:    e.EventKey,
			}
		case nextagent.EventNameSessionCompleted:
			var (
				s    = conv.DefaultAny[map[string]any](d.EventData.Data)
				e, _ = conv.MapToStructByJSONTag[*nextagent.SessionCompletedEvent](s)
			)
			if e == nil {
				return d
			}
			e.EventKey = d.EventKey
			return &entity.Event{
				ID:        e.EventID,
				SessionID: e.SessionID,
				TaskID:    "", // TODO: task id
				EventName: d.EventName,
				EventData: entity.EventData{
					Event: d.EventName,
					Data:  e,
				},
				EventOffset: e.EventOffset,
				EventKey:    e.EventKey,
			}
		}
		return d
	})
}

func replaceNeumaAndDevagent(s string) string {
	s = strings.ReplaceAll(s, "neuma", "aime")
	s = strings.ReplaceAll(s, "Neuma", "Aime")
	s = strings.ReplaceAll(s, "devagent", "aime")
	s = strings.ReplaceAll(s, "DevAgent", "Aime")
	return s
}

func getEventKey(d *entity.Event) string {
	if d == nil {
		return ""
	}
	switch d.EventName {
	case nextagent.EventNameMessageCreate:
		var (
			s    = conv.DefaultAny[map[string]any](d.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.MessageCreateEvent](s)
		)
		if e == nil {
			return ""
		}
		return entity.GetMessageCreateEventKey(d.SessionID, e.Message.MessageID, e.EventOffset)
	case nextagent.EventNameProgressNotice:
		var (
			s    = conv.DefaultAny[map[string]any](d.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.ProgressNoticeEvent](s)
		)
		if e == nil {
			return ""
		}
		return entity.GetProgressNoticeEventKey(d.SessionID, e.Status, e.EventOffset)
	case nextagent.EventNameStepUpdate:
		var (
			s    = conv.DefaultAny[map[string]any](d.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.StepUpdateEvent](s)
		)
		if e == nil {
			return ""
		}
		return entity.GetStepUpdateEventKey(d.SessionID, e.StepID, e.EventOffset)
	case nextagent.EventNamePlanUpdate:
		var (
			s    = conv.DefaultAny[map[string]any](d.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.PlanUpdateEvent](s)
		)
		if e == nil {
			return ""
		}
		return entity.GetPlanUpdateEventKey(d.SessionID, e.PlanID, e.EventOffset)
	case nextagent.EventNameUseTool:
		var (
			s    = conv.DefaultAny[map[string]any](d.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.UseToolEvent](s)
		)
		if e == nil {
			return ""
		}
		return entity.GetUseToolEventKey(d.SessionID, e.AgentStepID, e.EventOffset)
	case nextagent.EventNameToolCallRequired:
		var (
			s    = conv.DefaultAny[map[string]any](d.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.ToolCallRequiredEvent](s)
		)
		if e == nil {
			return ""
		}
		return entity.GetToolCallRequiredEventKey(d.SessionID, e.ToolCallID, e.EventOffset)
	case nextagent.EventNameSessionCompleted:
		var (
			s    = conv.DefaultAny[map[string]any](d.EventData.Data)
			e, _ = conv.MapToStructByJSONTag[*nextagent.SessionCompletedEvent](s)
		)
		if e == nil {
			return ""
		}
		return entity.GetSessionCompletedEventKey(d.SessionID, e.EventOffset)
	}
	return ""
}

type diffStrings struct {
	a, b []string
}

func (ab *diffStrings) LenA() int                                { return len(ab.a) }
func (ab *diffStrings) LenB() int                                { return len(ab.b) }
func (ab *diffStrings) Equal(ai, bi int) bool                    { return ab.a[ai] == ab.b[bi] }
func (ab *diffStrings) WriteATo(w io.Writer, i int) (int, error) { return io.WriteString(w, ab.a[i]) }
func (ab *diffStrings) WriteBTo(w io.Writer, i int) (int, error) { return io.WriteString(w, ab.b[i]) }

// timeToCron converts a time.Time to a cron expression
func timeToCron(frequency nextagent.ScheduleType, timeStr string) (string, error) {
	if frequency == 0 {
		return "", fmt.Errorf("invalid frequency: %v", frequency)
	}
	// 解析时间
	t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		return "", fmt.Errorf("time parse err: %v", err)
	}

	second := t.Second()
	minute := t.Minute()
	hour := t.Hour()

	switch frequency {
	case nextagent.ScheduleType_ScheduleTypeByDay:
		// 每天执行：秒 分钟 小时 * * *
		return fmt.Sprintf("%d %d %d * * ?", second, minute, hour), nil

	case nextagent.ScheduleType_ScheduleTypeByWeek:
		// 每周执行：秒 分钟 小时 * * 周几
		// 这里的 Weekday() 返回的是 0-6，0 表示周日，1 表示周一，cron表达式是从0开始的(https://en.wikipedia.org/wiki/Cron)
		return fmt.Sprintf("%d %d %d ? * %d", second, minute, hour, t.Weekday()), nil

	default:
		return "", fmt.Errorf("unsupported frequency: %v", frequency)
	}
}
