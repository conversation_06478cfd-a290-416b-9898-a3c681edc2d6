package serverhandler

import (
	"context"
	"encoding/json"
	"net/http"

	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
)

func (h *Handler) GetWebABTestConfig(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetABTestConfigRequest](ctx, c)
	if req == nil {
		return
	}

	// 获取当前用户信息
	user, ok := h.AuthM.GetAccount(ctx, c)
	if !ok {
		log.V1.CtxError(ctx, "failed to get user account")
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	data, err := h.WebABConfigService.GetWebConfig(ctx, user)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get ab config for user %s, err:%v", user.Name, err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get ab config")
		return
	}

	// 返回 AB 测试配置
	dataStr := make(map[string]common.JsonVariables)
	for k, v := range data {
		val, err := json.Marshal(v)
		if err != nil {
			log.V1.CtxError(ctx, "failed to marshal ab config value for key %s, err:%v", k, err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to marshal ab config")
			return
		}
		dataStr[k] = common.JsonVariables(val)
	}
	c.JSON(http.StatusOK, nextagent.GetABTestConfigResponse{
		Data: dataStr,
	})
}
