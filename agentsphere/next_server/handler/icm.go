package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/samber/lo"
)

// GetICMVersionList 获取ICM版本列表
func (h *Handler) GetIcmVersionList(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetIcmVersionRequest](ctx, c)
	if req == nil {
		return
	}

	log.V1.CtxInfo(ctx, "GetICMVersion, req: %+v", req)

	// 调用service层获取ICM版本列表
	opt := &serverservice.GetIcmVersionListOption{}
	if req.Version != nil {
		opt.Version = *req.Version
	}

	if req.Region != nil {
		opt.Region = *req.Region
	}

	if req.SpecificTag != nil {
		opt.SpecificTag = *req.SpecificTag
	}

	if req.ConfigType != nil {
		opt.ConfigType = *req.ConfigType
	}

	if req.ImageType != nil {
		opt.ImageType = *req.ImageType
	}

	versionList, err := h.IcmOpenapiService.GetIcmVersionList(ctx, opt)
	if err != nil {
		log.V1.CtxError(ctx, "[GetICMVersion] failed to get icm version list, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	// 转换为API响应格式
	icmVersions := lo.Map(versionList, func(item *entity.IcmVersion, _ int) *nextagent.IcmVersion {
		return item.ToIDL()
	})

	resp := &nextagent.GetIcmVersionResponse{
		IcmVersions: icmVersions,
	}

	c.JSON(http.StatusOK, resp)
}
