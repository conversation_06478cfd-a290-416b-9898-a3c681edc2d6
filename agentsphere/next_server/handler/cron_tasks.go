package serverhandler

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/pack"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	artifactService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	crontaskservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/cron_tasks"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	templateservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
)

// CreateCronTask implements the CreateCronTask API
func (h *Handler) CreateCronTask(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateCronTaskRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "create cron task request: %+v", req)

	// 检查参数
	if req.Name == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "name is required")
		return
	}
	if req.TaskType == 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "task type is required")
		return
	}
	taskType := entity.TaskTypeFromIDL(req.TaskType)
	if taskType == entity.TaskTypeUnknown {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "task type param invalid")
		return
	}
	if req.TemplateID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template id is required")
		return
	}
	if req.Schedule == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "schedule is required")
		return
	}
	scheduleType := entity.ScheduleTypeFromIDL(req.ScheduleType)
	if scheduleType == entity.ScheduleTypeUnknown {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "schedule type is required")
		return
	}
	if req.Content == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "content is required")
		return
	}
	if req.Role == 0 {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "agent type is required")
		return
	}
	role := lo.FromPtr(entity.SessionRoleFromIDL(lo.ToPtr(req.Role)))
	if role == entity.SessionRoleUnknown {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "role param invalid")
		return
	}

	// 转换Schedule，用户输入为"2006-01-02 15:04:05"这种日期，需要转换为cron表达式
	cronSchedule, err := timeToCron(req.ScheduleType, req.Schedule)
	if err != nil {
		log.V1.CtxError(ctx, "failed to convert time to cron: %v", err)
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed to convert time to cron")
		return
	}

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	if req.SpaceID == nil {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
		if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
			Account: user,
			SpaceID: req.SpaceID,
			Action:  entity.PermissionActionSessionCreate,
		}); !ok {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	// 校验模板权限
	result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(user),
		ResourceID:         nil,
		ResourceExternalID: &req.TemplateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
		Action:             entity.PermissionActionTemplateRead,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to check permission: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
		return
	}
	if !result.Allowed {
		log.V1.CtxInfo(ctx, "permission denied to create message with template")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 获取模板信息
	template, err := h.TemplateService.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}
	// 提前创建uid，存模板需要用到
	uid := uuid.GetDefaultGenerator(nil).NewID()
	// 将用户的输入存入到next_template_variable表中
	if req.FormValue != nil {
		// 此处不需要session id，无法走到需要session id的逻辑
		formValue := getCronTaskTemplateFormValueFromDto(ctx, req.FormValue, h.ArtifactService, "")
		// 将templateVariables存储到TemplateVariable表中，方便后续执行时取用
		// 创建时，无需检查模板和variable是否一一对应
		_, err := h.TemplateService.CreateOrUpdateCronTaskTemplateVariable(ctx, templateservice.CreateCronTaskTemplateVariableOptions{
			TemplateID:  req.TemplateID,
			Creator:     user.Username,
			Value:       formValue,
			SpaceID:     req.GetSpaceID(),
			CronTaskUID: uid,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to create template variable: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create template variable")
			return
		}
	}

	// 拉群or创建群
	groupInfo := entity.GroupInfo{}
	switch req.TaskType {
	case nextagent.TaskType_NewGroup:
		if req.GroupInfo == nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group info is required")
			return
		}
		if req.GroupInfo.GetGroupName() == "" {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group name is required")
			return
		}
		chatID, avatar, err := h.LarkService.CreateGroup(ctx, user.Username, user.Email, req.GroupInfo.GetGroupName())
		if err != nil {
			log.V1.CtxError(ctx, "[CreateCronTask] failed to create group: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return
		}
		groupInfo.GroupID = lo.ToPtr(chatID)
		groupInfo.GroupAvatar = lo.ToPtr(avatar)
		groupInfo.GroupName = lo.ToPtr(req.GroupInfo.GetGroupName())
	case nextagent.TaskType_BindGroup:
		if req.GroupInfo == nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group info is required")
			return
		}
		if req.GroupInfo.GetGroupName() == "" {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group name is required")
			return
		}
		if req.GroupInfo.GetGroupID() == "" {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group id is required")
			return
		}
		groupInfo.GroupID = lo.ToPtr(req.GroupInfo.GetGroupID())
		groupInfo.GroupName = lo.ToPtr(req.GroupInfo.GetGroupName())
		groupInfo.GroupAvatar = lo.ToPtr(req.GroupInfo.GetGroupAvatar())
	default:
	}

	// 如果是绑定群组，则需要查看bot是否在群组中
	// 额外判断下group info的空指针问题，否则lint过不了
	if req.GetTaskType() == nextagent.TaskType_BindGroup {
		if req.GroupInfo == nil || req.GroupInfo.GetGroupID() == "" {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group id is required")
			return
		}
		isBotIn, err := h.LarkService.JudgeBotInGroup(ctx, req.GroupInfo.GetGroupID())
		if err != nil {
			if errors.Is(err, lark.ErrCanNotOperateOuterGroup) {
				log.V1.CtxWarn(ctx, "[CreateCronTask] The operator or invited bots does NOT have the authority to manage external chats without the scope.: %v", err)
				hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrCanNotOperateOuterGroup), err.Error())
				return
			}
			log.V1.CtxError(ctx, "[CreateCronTask] failed to judge bot in group: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return
		}
		if !isBotIn {
			log.V1.CtxWarn(ctx, "[CreateCronTask] bot not in group, group id: %s", req.GroupInfo.GetGroupID())
			hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrBotNotInGroup), "bot is not in group")
			return
		}
		isUserIn, err := h.LarkService.JudgeUserInGroup(ctx, req.GroupInfo.GetGroupID(), user.Username)
		if err != nil {
			if errors.Is(err, lark.ErrCanNotOperateOuterGroup) {
				log.V1.CtxWarn(ctx, "[CreateCronTask] The operator or invited bots does NOT have the authority to manage external chats without the scope.: %v", err)
				hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrCanNotOperateOuterGroup), err.Error())
				return
			}
			log.V1.CtxError(ctx, "[CreateCronTask] failed to judge user in group: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return
		}
		if !isUserIn {
			log.V1.CtxWarn(ctx, "[CreateCronTask] bot user in group, group id: %s", req.GroupInfo.GetGroupID())
			hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrUserNotInGroup), "user is not in group")
			return
		}
	}

	// 创建任务
	err = h.CronTaskService.CreateCronTask(ctx, crontaskservice.CreateCronTaskOption{
		UID:          uid,
		Username:     user.Username,
		Email:        user.Email,
		TaskName:     req.GetName(),
		Description:  req.GetDescription(),
		TaskType:     taskType,
		TemplateID:   template.TemplateID,
		TemplateName: template.Name,
		Schedule:     cronSchedule,
		Timezone:     req.GetTimezone(),
		GroupInfo:    groupInfo,
		Role:         role,
		Content:      req.GetContent(),
		Option:       req.GetOptions(),
		SpaceID:      req.GetSpaceID(),
		Params:       convertFormValueToJSONFormat(ctx, req.GetFormValue()),
	})

	if err != nil {
		log.V1.CtxError(ctx, "failed to create cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create cron task")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateCronTaskResponse{IsSuccess: true})
}

// UpdateCronTask implements the UpdateCronTask API
func (h *Handler) UpdateCronTask(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.UpdateCronTaskRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "update cron task request: %+v", req)

	// 检查参数
	if req.UID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "uid is required")
		return
	}
	cronSchedule := ""
	if req.ScheduleType != nil && req.Schedule != nil {
		// 转换Schedule，用户输入为"2006-01-02 15:04:05"这种日期，需要转换为cron表达式
		tempSchedule, err := timeToCron(req.GetScheduleType(), req.GetSchedule())
		if err != nil {
			log.V1.CtxError(ctx, "failed to convert time to cron: %v", err)
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "failed to convert time to cron")
			return
		}
		cronSchedule = tempSchedule
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	// 先判断之前的定时任务是否存在
	originalTask, err := h.CronTaskService.GetCronTask(ctx, req.GetUID())
	groupChanged := false
	if err != nil {
		log.V1.CtxError(ctx, "failed to get cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}
	if originalTask == nil {
		log.V1.CtxError(ctx, "failed to get cron task")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}

	if user.Username != originalTask.UserName {
		log.V1.CtxError(ctx, "user does not match")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	if req.SpaceID == nil {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
		if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
			Account: user,
			SpaceID: req.SpaceID,
			Action:  entity.PermissionActionSessionCreate,
		}); !ok {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	if req.TemplateID != "" {
		// 校验模板权限
		result, err := h.PermissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
			Account:            lo.FromPtr(user),
			ResourceID:         nil,
			ResourceExternalID: &req.TemplateID,
			ResourceType:       entity.ResourceTypeTemplate.Ptr(),
			Action:             entity.PermissionActionTemplateRead,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to check permission: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to check permission")
			return
		}
		if !result.Allowed {
			log.V1.CtxInfo(ctx, "permission denied to create message with template")
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	// 获取模板信息
	template, err := h.TemplateService.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}
	if template == nil {
		log.V1.CtxError(ctx, "failed to get template")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}

	// 记录模板中的变量并去重
	templateVariable := make(map[string]int)
	for _, v := range template.PromptVariables {
		if v != nil {
			templateVariable[v.Name] = 1
		}
	}

	// 获取模板变量信息
	if req.FormValue != nil {
		formValue := getCronTaskTemplateFormValueFromDto(ctx, req.FormValue, h.ArtifactService, "")
		// 需要将模板本身的变量信息和输入的req.FormValue进行校验
		//if templateVariable != nil {
		// 如果输入的变量为空或数量不匹配，直接返回错误（提前退出保证效率）
		if formValue.Variables == nil || len(templateVariable) != len(formValue.Variables) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template variables not match")
			return
		}

		// 验证每个模板声明的变量在输入中都存在
		for k, _ := range templateVariable {
			if _, ok := formValue.Variables[k]; !ok {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template variables not match")
				return
			}
		}
		//}

		// 校验完成后，更新Template Variable
		_, err := h.TemplateService.CreateOrUpdateCronTaskTemplateVariable(ctx, templateservice.CreateCronTaskTemplateVariableOptions{
			TemplateID:  req.TemplateID,
			Creator:     user.Username,
			Value:       formValue,
			SpaceID:     req.GetSpaceID(),
			CronTaskUID: req.GetUID(),
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to create template variable: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create template variable")
			return
		}
	}

	// 当变更时才进行拉群拉群or创建群
	needChangeGroup := false
	if req.TaskType != nil {
		newType := entity.TaskTypeFromIDL(req.GetTaskType())
		oldType := originalTask.TaskType

		// 1. 类型不同
		if newType != oldType {
			needChangeGroup = true
		}

		// 2. 类型相同且都是 BindGroup，但 groupID 不同
		if newType == entity.TaskTypeBindGroup && oldType == entity.TaskTypeBindGroup {
			if req.GroupInfo == nil {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group info is required")
				return
			}
			if req.GroupInfo.GetGroupID() == "" {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group id is required")
				return
			}
			if lo.FromPtr(originalTask.GroupInfo.GroupID) != req.GroupInfo.GetGroupID() {
				needChangeGroup = true
			}
		}
	}

	var groupInfo *entity.GroupInfo
	if needChangeGroup && req.TaskType != nil {
		// new group需要group info和 group name不为空，bind则额外需要group id，之前校验过了
		if req.GroupInfo == nil {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group info is required")
			return
		}
		if req.GroupInfo.GetGroupName() == "" && req.GetTaskType() != nextagent.TaskType_AimeBot {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group name is required")
			return
		}

		switch *req.TaskType {
		case nextagent.TaskType_NewGroup:
			chatID, avatar, err := h.LarkService.CreateGroup(ctx, user.Username, user.Email, req.GroupInfo.GetGroupName())
			if err != nil {
				log.V1.CtxError(ctx, "[UpdateCronTask] failed to create group: %v", err)
				hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
				return
			}
			groupInfo = &entity.GroupInfo{
				GroupID:     lo.ToPtr(chatID),
				GroupAvatar: lo.ToPtr(avatar),
				GroupName:   lo.ToPtr(req.GroupInfo.GetGroupName()),
			}
		case nextagent.TaskType_BindGroup:
			if req.GroupInfo.GetGroupID() == "" {
				hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group id is required")
				return
			}
			groupInfo = &entity.GroupInfo{
				GroupID:     lo.ToPtr(req.GroupInfo.GetGroupID()),
				GroupAvatar: lo.ToPtr(req.GroupInfo.GetGroupAvatar()),
				GroupName:   lo.ToPtr(req.GroupInfo.GetGroupName()),
			}
		}
		groupChanged = true
	}

	// 如果是绑定群组，则需要查看bot是否在群组中，同时更新时需要额外考虑bot一开始在群聊中，后续被移除的可能性，也需要额外判断下
	if req.GetTaskType() == nextagent.TaskType_BindGroup {
		if req.GroupInfo == nil || req.GroupInfo.GetGroupID() == "" {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "group id is required")
			return
		}
		isIn, err := h.LarkService.JudgeBotInGroup(ctx, req.GroupInfo.GetGroupID())
		if err != nil {
			if errors.Is(err, lark.ErrCanNotOperateOuterGroup) {
				log.V1.CtxWarn(ctx, "[CreateCronTask] The operator or invited bots does NOT have the authority to manage external chats without the scope.: %v", err)
				hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrCanNotOperateOuterGroup), err.Error())
				return
			}
			log.V1.CtxError(ctx, "[UpdateCronTask] failed to judge bot in group: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return
		}
		if !isIn {
			log.V1.CtxWarn(ctx, "[UpdateCronTask] bot not in group, group id: %s", req.GroupInfo.GetGroupID())
			hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrBotNotInGroup), "bot is not in group")
			return
		}
		isUserIn, err := h.LarkService.JudgeUserInGroup(ctx, req.GroupInfo.GetGroupID(), user.Username)
		if err != nil {
			if errors.Is(err, lark.ErrCanNotOperateOuterGroup) {
				log.V1.CtxWarn(ctx, "[CreateCronTask] The operator or invited bots does NOT have the authority to manage external chats without the scope.: %v", err)
				hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrCanNotOperateOuterGroup), err.Error())
				return
			}
			log.V1.CtxError(ctx, "[CreateCronTask] failed to judge user in group: %v", err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
			return
		}
		if !isUserIn {
			log.V1.CtxWarn(ctx, "[CreateCronTask] bot user in group, group id: %s", req.GroupInfo.GetGroupID())
			hertz.JSONMessage(c, http.StatusOK, int(common.ErrorCode_ErrUserNotInGroup), "user is not in group")
			return
		}
	}

	task, err := h.CronTaskService.UpdateCronTask(ctx, crontaskservice.UpdateCronTaskOption{
		UID:          req.GetUID(),
		Username:     user.Username,
		Email:        user.Email,
		TaskName:     req.GetName(),
		Description:  req.GetDescription(),
		TaskType:     entity.TaskTypeFromIDL(req.GetTaskType()),
		TemplateID:   req.GetTemplateID(),
		TemplateName: template.Name,
		Schedule:     lo.TernaryF(cronSchedule == "", func() string { return "" }, func() string { return cronSchedule }),
		Timezone:     req.GetTimezone(),
		GroupInfo:    groupInfo,
		Role:         lo.FromPtr(entity.SessionRoleFromIDL(req.Role)),
		Content:      req.GetContent(),
		Option:       req.GetOptions(),
		TaskStatus:   entity.CronTaskStatusFromIDL(req.GetTaskStatus()),
		Params:       convertFormValueToJSONFormat(ctx, req.GetFormValue()),
		OriginalTask: lo.FromPtr(originalTask),
		GroupChanged: groupChanged,
	})

	if err != nil {
		log.V1.CtxError(ctx, "failed to update cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to update cron task")
		return
	}
	if task == nil {
		log.V1.CtxError(ctx, "failed to get task after update cron task")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get task after update cron task")
		return
	}
	timeStr, weekDay, scheduleType, err := cronToNextTime(task.Schedule)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get cron to next time: %v", err)
	} else {
		task.Schedule = timeStr
		task.WeekDay = weekDay
		task.ScheduleType = scheduleType
	}

	value, err := h.TemplateService.GetLastCronTaskTemplateVariable(ctx, task.TemplateID, task.UserName, task.SpaceID, task.UID)
	if err != nil {
		log.V1.CtxError(ctx, "[UpdateCronTask] failed to get template variable, err: %v", err)
	} else {
		task.FormValue = value.Value.Variables
	}
	task.EnableEdit = true

	c.JSON(http.StatusOK, nextagent.UpdateCronTaskResponse{CronTask: task.ToIDL()})
}

// DeleteCronTask implements the DeleteCronTask API
func (h *Handler) DeleteCronTask(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.DeleteCronTaskRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "delete cron task request: %+v", req)

	// 检查参数
	if req.UID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "uid is required")
		return
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	if req.SpaceID == nil {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
		if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
			Account: user,
			SpaceID: req.SpaceID,
			Action:  entity.PermissionActionSessionCreate,
		}); !ok {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	err := h.CronTaskService.DeleteCronTask(ctx, req.GetUID(), user)
	if err != nil {
		log.V1.CtxError(ctx, "failed to delete cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to delete cron task")
		return
	}

	c.JSON(http.StatusOK, nextagent.DeleteCronTaskResponse{IsSuccess: true})
}

// GetCronTask implements the GetCronTask API
func (h *Handler) GetCronTask(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetCronTaskRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "get cron task request: %+v", req)

	// 检查参数
	if req.UID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "uid is required")
		return
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	if req.SpaceID == nil {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
		if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
			Account: user,
			SpaceID: req.SpaceID,
			Action:  entity.PermissionActionSessionCreate,
		}); !ok {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	task, err := h.CronTaskService.GetCronTask(ctx, req.UID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}
	if task == nil {
		log.V1.CtxError(ctx, "failed to get cron task")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}
	timeStr, weekDay, scheduleType, err := cronToNextTime(task.Schedule)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get cron to next time: %v", err)
	} else {
		task.Schedule = timeStr
		task.WeekDay = weekDay
		task.ScheduleType = scheduleType
	}

	value, err := h.TemplateService.GetLastCronTaskTemplateVariable(ctx, task.TemplateID, task.UserName, task.SpaceID, task.UID)
	if err != nil {
		log.V1.CtxError(ctx, "[GetCronTask] failed to get template variable, err: %v", err)
	} else {
		task.FormValue = value.Value.Variables
	}
	if task.UserName == user.Username {
		task.EnableEdit = true
	}

	c.JSON(http.StatusOK, nextagent.GetCronTaskResponse{
		CronTask: task.ToIDL(),
	})
}

// ListCronTasks implements the ListCronTasks API
func (h *Handler) ListCronTasks(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListCronTasksRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list cron tasks request: %+v", req)

	pageNum := int64(1)
	pageSize := int64(10)
	if req.PageNum > 0 {
		pageNum = req.PageNum
	}
	if req.PageSize > 0 && req.PageSize <= 100 {
		pageSize = req.PageSize
	}

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	if req.SpaceID == nil {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
		if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
			Account: user,
			SpaceID: req.SpaceID,
			Action:  entity.PermissionActionSessionCreate,
		}); !ok {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	total, tasks, err := h.CronTaskService.ListCronTasks(ctx, &crontaskservice.ListCronTasksOption{
		Query:    req.Query,
		PageNum:  pageNum,
		PageSize: pageSize,
		SpaceID:  req.GetSpaceID(),
	})
	if err != nil {
		log.V1.CtxError(ctx, "[ListCronTasks] failed to list tasks, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	for k, v := range tasks {
		if v == nil {
			continue
		}
		value, err := h.TemplateService.GetLastCronTaskTemplateVariable(ctx, v.TemplateID, v.UserName, v.SpaceID, v.UID)
		if err != nil {
			log.V1.CtxError(ctx, "[ListCronTasks] failed to get template variable, err: %v", err)
			continue
		}
		tasks[k].FormValue = value.Value.Variables
		timeStr, weekDay, scheduleType, err := cronToNextTime(v.Schedule)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get cron to next time: %v", err)
		} else {
			tasks[k].Schedule = timeStr
			tasks[k].WeekDay = weekDay
			tasks[k].ScheduleType = scheduleType
		}
		if v.UserName == user.Username {
			tasks[k].EnableEdit = true
		}
	}

	resp := &nextagent.ListCronTasksResponse{
		CronTasks: getCronTasksFromEntities(tasks),
		Total:     total,
	}

	c.JSON(http.StatusOK, resp)
}

func getCronTasksFromEntities(tasks []*entity.CronTask) []*nextagent.CronTask {
	result := make([]*nextagent.CronTask, 0, len(tasks))
	for _, v := range tasks {
		result = append(result, v.ToIDL())
	}
	return result
}

// RetryCronTask implements the RetryCronTask API
func (h *Handler) RetryCronTask(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ReTryCronTaskRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "retry cron task request: %+v", req)

	// 检查参数
	if req.UID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "uid is required")
		return
	}

	// 获取用户信息
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	if req.SpaceID == nil {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
		if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
			Account: user,
			SpaceID: req.SpaceID,
			Action:  entity.PermissionActionSessionCreate,
		}); !ok {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	originalTask, err := h.CronTaskService.GetCronTask(ctx, req.GetUID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to get cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}
	if originalTask == nil {
		log.V1.CtxError(ctx, "failed to get cron task")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}
	// 校验用户是否一致，不一致不允许发起任务
	if user.Username != originalTask.UserName {
		log.V1.CtxError(ctx, "user does not match")
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}

	session, message, template, err := h.CronTaskService.DoTask(ctx, nil, entity.TriggerTypeManual, "", req.GetUID(), user)
	if err != nil {
		if errors.Is(err, serverservice.ErrMaximumRunningSessionsReached) {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrNextAgentReachedUpperLimit), "maximum running sessions reached")
			return
		}
		log.V1.CtxError(ctx, "failed to retry cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to retry cron task")
		return
	}

	c.JSON(http.StatusOK, nextagent.ReTryCronTaskResponse{
		Message:  getMessageFromEntity(message),
		Session:  getSessionFromEntity(session, nil),
		Template: pack.ConvertTemplateEntityToDTO(template, true),
	})
}

// ListCronTaskLogs implements the ListCronTaskLogs API
func (h *Handler) ListCronTaskLogs(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.ListCronTaskLogsRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "list cron task logs request: %+v", req)

	// 检查参数
	if req.UID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "uid is required")
		return
	}

	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}
	userFeature := h.UserService.GetUserFeatures(ctx, user, false)
	if !userFeature.Invited {
		hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
		return
	}
	if req.SpaceID == nil {
		req.SpaceID = lo.ToPtr(h.getSessionSpaceID(ctx, c))
		if _, ok := h.checkSessionPermission(ctx, c, SessionPermissionCheckOption{
			Account: user,
			SpaceID: req.SpaceID,
			Action:  entity.PermissionActionSessionCreate,
		}); !ok {
			hertz.JSONMessage(c, http.StatusForbidden, int(common.ErrorCode_ErrAccessReject), "permission denied")
			return
		}
	}

	// 获取任务详情
	originalTask, err := h.CronTaskService.GetCronTask(ctx, req.GetUID())
	if err != nil {
		log.V1.CtxError(ctx, "failed to get cron task: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}
	if originalTask == nil {
		log.V1.CtxError(ctx, "failed to get cron task")
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get cron task")
		return
	}

	if user.Username != originalTask.UserName {
		resp := &nextagent.ListCronTaskLogsResponse{
			EnableView: false,
		}
		c.JSON(http.StatusOK, resp)
		return
	}

	pageNum := int64(1)
	pageSize := int64(10)
	if req.PageNum > 0 {
		pageNum = req.PageNum
	}
	if req.PageSize > 0 && req.PageSize <= 100 {
		pageSize = req.PageSize
	}

	total, taskLogs, err := h.CronTaskService.ListTaskExecutions(ctx, &crontaskservice.ListTaskExecutionsOption{
		UID:      req.GetUID(),
		PageNum:  pageNum,
		PageSize: pageSize,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[ListCronTaskLogs] failed to list tasks logs, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}

	resp := &nextagent.ListCronTaskLogsResponse{
		TaskExecution: getCronTaskExecutionsFromEntities(taskLogs),
		Total:         total,
		EnableView:    user.Username == originalTask.UserName,
	}

	c.JSON(http.StatusOK, resp)
}

func getCronTaskExecutionsFromEntities(executions []*entity.TaskExecution) []*nextagent.TaskExecution {
	result := make([]*nextagent.TaskExecution, 0, len(executions))
	for _, v := range executions {
		tempIDL := v.ToIDL()
		result = append(result, tempIDL)
	}
	return result
}

func getCronTaskTemplateFormValueFromDto(ctx context.Context, formValue *nextagent.TemplateFormValue, artifactService *artifactService.Service, sessionID string) entity.TemplateFormValue {
	if formValue == nil {
		return entity.TemplateFormValue{}
	}
	variables := make(map[string]*entity.TemplateVariableValue)
	for k, v := range formValue.Variables {
		//if v == nil || (lo.FromPtr(v.Content) == "" && len(v.Attachments) == 0) { // 过滤空变量
		if v == nil { // 过滤nil变量，空变量依然保留
			continue
		}
		variables[k] = &entity.TemplateVariableValue{
			Content: v.Content,
			Attachments: lo.Map(v.Attachments, func(a *nextagent.AttachmentRequired, _ int) *entity.Attachment {
				detail, _ := getArtifactDetailEntity(ctx, artifactService, sessionID, a)
				if detail == nil {
					return nil
				}
				return detail
			}),
		}
	}
	return entity.TemplateFormValue{Variables: variables}
}

func convertFormValueToJSONFormat(ctx context.Context, formValue *nextagent.TemplateFormValue) interface{} {
	if formValue == nil || formValue.Variables == nil {
		return ""
	}

	rowVals := make([]map[string]string, 0, len(formValue.Variables))

	for key, val := range formValue.Variables {
		var text string
		if val.Content != nil {
			text = *val.Content
		}

		if len(val.Attachments) > 0 {
			fileNames := make([]string, 0, len(val.Attachments))
			for _, attachment := range val.Attachments {
				if attachment != nil && attachment.FileName != "" {
					fileNames = append(fileNames, attachment.FileName)
				}
			}
			if len(fileNames) > 0 {
				if text != "" {
					text += ","
				}
				text += strings.Join(fileNames, ",")
			}
		}

		rowVals = append(rowVals, map[string]string{
			"key": key,
			"val": text,
		})
	}

	return rowVals
}

func cronToNextTime(cronExpr string) (string, entity.WeekDay, entity.ScheduleType, error) {
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	schedule, err := parser.Parse(cronExpr)
	if err != nil {
		return "", entity.WeekDayUnknown, entity.ScheduleTypeUnknown, fmt.Errorf("parse cron err: %v", err)
	}

	// 计算下一次执行时间
	nextTime := schedule.Next(time.Now())

	// 判断频率
	var freq entity.ScheduleType
	if strings.Contains(cronExpr, "? *") { // 表达式里有 ? * → 每周
		freq = entity.ScheduleTypeByWeek
	} else if strings.Contains(cronExpr, "* * ?") { // 每天
		freq = entity.ScheduleTypeByDay
	} else {
		// fallback：通过判断 DOW / DOM 是否含 ?
		if strings.Contains(cronExpr, "?") {
			freq = entity.ScheduleTypeByWeek
		} else {
			freq = entity.ScheduleTypeByDay
		}
	}

	// 判断weekday
	var w entity.WeekDay
	switch nextTime.Weekday() {
	case time.Monday:
		w = entity.WeekDayMonday
	case time.Tuesday:
		w = entity.WeekDayTuesday
	case time.Wednesday:
		w = entity.WeekDayWednesday
	case time.Thursday:
		w = entity.WeekDayThursday
	case time.Friday:
		w = entity.WeekDayFriday
	case time.Saturday:
		w = entity.WeekDaySaturday
	case time.Sunday:
		w = entity.WeekDaySunday
	default:
		w = entity.WeekDayUnknown
	}

	return nextTime.Format("2006-01-02 15:04:05"), w, freq, nil
}
