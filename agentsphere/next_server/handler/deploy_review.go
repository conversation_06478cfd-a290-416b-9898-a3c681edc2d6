package serverhandler

import (
	"context"
	"net/http"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	deployreview "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/deploy_review"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

func (h *Handler) CreateDeploy(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.CreateDeployRequest](ctx, c)
	if req == nil {
		return
	}
	user, exist := h.AuthM.GetAccount(ctx, c)
	if !exist {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), "not found account")
		return
	}

	log.V1.CtxInfo(ctx, "CreateDeploy req: %v", req)
	deployID, workflowID, err := h.DeployReviewService.CreateDeploy(ctx, req.AgentConfigVersionID, user.Username, &entity.DeployExtraInfo{
		Reviewer:  req.Reviewer,
		EnableAB:  req.EnableAB,
		ABComment: req.ABComment,
	})
	if err != nil {
		log.V1.CtxError(ctx, "CreateDeploy err: %v", err)
		if errors.Is(err, deployreview.ErrAgentConfigVersionInDeployStatus) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), err.Error())
			return
		}

		if h.checkAgentBusinessError(c, err) {
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create deploy")
		return
	}

	c.JSON(http.StatusOK, nextagent.CreateDeployResponse{
		DeployID:   deployID,
		WorkflowID: workflowID,
	})
}

func (h *Handler) GetDeployProcessInfo(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetDeployProcessInfoRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "GetDeployProcessInfo req: %v", req)
	processInfo, err := h.DeployReviewService.GetDeployProcessInfo(ctx, req.DeployID)
	if err != nil {
		log.V1.CtxError(ctx, "GetDeployProcessInfo err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get deploy process info")
		return
	}

	c.JSON(http.StatusOK, &nextagent.GetDeployProcessInfoResponse{
		// 保留两位小数
		CanaryRatio:  processInfo.CanaryRatio,
		SuccessCount: processInfo.SuccessCount,
		FailCount:    processInfo.FailCount,
		RunningCount: processInfo.RunningCount,
		DeployStatus: string(processInfo.Status),
	})
}

// BPMAuthCallback 处理 BPM 授权回调请求
func (h *Handler) BPMAuthCallback(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BPMAuthCallbackRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "BPMAuthCallback req: %v", req)

	c.JSON(http.StatusOK, nextagent.BPMAuthCallbackResponse{
		Code: 0,
		Data: &nextagent.BPMAuthCallbackResponseData{
			AuthResult: true,
		},
	})
}

// BPMCanaryCallback 处理 BPM 灰度回调请求
func (h *Handler) BPMCanaryCallback(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BPMCanaryCallbackRequest](ctx, c)
	if req == nil {
		return
	}

	if req.ID == 0 || req.DeployID == "" || req.AgentConfigID == "" || req.AgentConfigVersionID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid param")
		return
	}
	log.V1.CtxInfo(ctx, "BPMCanaryCallback req: %v", req)

	err := h.DeployReviewService.StartCanary(ctx, &serverservice.DeployCanaryOption{
		WorkflowID:           req.ID,
		DeployID:             req.DeployID,
		AgentConfigID:        req.AgentConfigID,
		AgentConfigVersionID: req.AgentConfigVersionID,
		Status:               entity.ParseWorkflowNodeKey(req.Status),
	})

	if err != nil {
		log.V1.CtxError(ctx, "StartCanary err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to start canary")
		return
	}

	c.JSON(http.StatusOK, nextagent.BPMCanaryCallbackResponse{
		Code: 0,
	})
}

// BPMCloseCallback 处理 BPM 关闭回调请求
func (h *Handler) BPMCloseCallback(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BPMCloseCallbackRequest](ctx, c)
	if req == nil {
		return
	}
	if req.ID == 0 || req.DeployID == "" || req.AgentConfigID == "" || req.AgentConfigVersionID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid param")
		return
	}
	log.V1.CtxInfo(ctx, "BPMCloseCallback req: %v", req)
	closeReason := ""
	if req.Comment != nil {
		closeReason = *req.Comment
	}
	auditRejectComment := ""
	if req.AuditRejectComment != nil {
		auditRejectComment = *req.AuditRejectComment
	}
	err := h.DeployReviewService.CloseWorkflowRecord(ctx, &serverservice.DeployCanaryOption{
		WorkflowID:           req.ID,
		DeployID:             req.DeployID,
		AgentConfigID:        req.AgentConfigID,
		AgentConfigVersionID: req.AgentConfigVersionID,
		CloseReason:          closeReason,
		AuditRejectComment:   auditRejectComment,
	})

	if err != nil {
		log.V1.CtxError(ctx, "CloseDeploy err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to close deploy")
		return
	}

	c.JSON(http.StatusOK, nextagent.BPMCloseCallbackResponse{
		Code: 0,
	})
}

// BPMCancelCanaryCallback 处理 BPM 取消灰度回调请求
func (h *Handler) BPMCancelCanaryCallback(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BPMCancelCanaryCallbackRequest](ctx, c)
	if req == nil {
		return
	}
	if req.ID == 0 || req.DeployID == "" || req.AgentConfigID == "" || req.AgentConfigVersionID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid param")
		return
	}
	log.V1.CtxInfo(ctx, "BPMCancelCanaryCallback req: %v", req)

	err := h.DeployReviewService.CancelCanary(ctx, &serverservice.DeployCanaryOption{
		WorkflowID:           req.ID,
		DeployID:             req.DeployID,
		AgentConfigID:        req.AgentConfigID,
		AgentConfigVersionID: req.AgentConfigVersionID,
	})

	if err != nil {
		log.V1.CtxError(ctx, "CancelCanary err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to cancel canary")
		return
	}

	c.JSON(http.StatusOK, nextagent.BPMCancelCanaryCallbackResponse{
		Code: 0,
	})
}

// BPMOnlineCallback 处理 BPM 上线回调请求
func (h *Handler) BPMOnlineCallback(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BPMOnlineCallbackRequest](ctx, c)
	if req == nil {
		return
	}
	if req.ID == 0 || req.DeployID == "" || req.AgentConfigID == "" || req.AgentConfigVersionID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid param")
		return
	}
	log.V1.CtxInfo(ctx, "BPMOnlineCallback req: %v", req)
	var skipCanaryComment string
	if req.SkipCanaryComment != nil {
		skipCanaryComment = *req.SkipCanaryComment
	}
	err := h.DeployReviewService.Online(ctx, &serverservice.DeployCanaryOption{
		WorkflowID:           req.ID,
		DeployID:             req.DeployID,
		AgentConfigID:        req.AgentConfigID,
		AgentConfigVersionID: req.AgentConfigVersionID,
		SkipCanaryComment:    skipCanaryComment,
	})

	if err != nil {
		log.V1.CtxError(ctx, "Online err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to online")
		return
	}

	c.JSON(http.StatusOK, nextagent.BPMOnlineCallbackResponse{
		Code: 0,
		Data: &nextagent.BPMOnlineCallbackResponseData{
			OnlineResult: true,
		},
	})
}

// GetDeploy 获取 deploy
func (h *Handler) GetDeploy(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetDeployRequest](ctx, c)
	if req == nil {
		return
	}
	deploy, err := h.DeployReviewService.GetDeploy(ctx, req.DeployID)
	if err != nil {
		log.V1.CtxError(ctx, "GetDeploy err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get deploy")
		return
	}
	c.JSON(http.StatusOK, &nextagent.GetDeployResponse{
		Deploy: deploy.ToIDL(),
	})
}

// GetDeployReviewUser 获取审核人名单
func (h *Handler) GetDeployReviewUser(ctx context.Context, c *app.RequestContext) {
	users, err := h.DeployReviewService.GetDeployReviewUser(ctx)
	if err != nil {
		log.V1.CtxError(ctx, "GetDeployReviewUser err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get deploy review user")
		return
	}
	c.JSON(http.StatusOK, &nextagent.GetDeployReviewUserResponse{
		Users: users,
	})
}

// GetAgentDeployList 获取 agent deploy 列表
func (h *Handler) GetAgentDeployList(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.GetAgentDeployListRequest](ctx, c)
	if req == nil {
		return
	}

	deploys, total, err := h.DeployReviewService.ListAgentDeploy(ctx, &serverservice.GetAgentDeployListOption{
		AgentConfigID: req.AgentConfigID,
		PageNum:       req.PageNum,
		PageSize:      req.PageSize,
		Status:        req.Status,
	})
	if err != nil {
		log.V1.CtxError(ctx, "ListAgentDeploy err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to list agent deploy")
		return
	}
	c.JSON(http.StatusOK, &nextagent.GetAgentDeployListResponse{
		AgentDeploys: lo.Map(deploys, func(item *entity.AgentDeploy, index int) *nextagent.AgentDeploy {
			return item.ToIDL()
		}),
		Total: total,
	})

}

func (h *Handler) BitsUpsertAgentConfigVersion(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.BitsUpsertAgentVersionRequest](ctx, c)
	if req == nil {
		return
	}
	log.V1.CtxInfo(ctx, "DeployCopyVersion, req: %+v", req)
	if req.AgentConfigID == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid agent_config_id or agent_config_version_id param")
		return
	}

	if req.ScmVersion == "" && req.IcmVersion == "" {
		hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "invalid scm_version or icm_version param")
		return
	}

	opts := &agent.CopyAgentConfigOnlineVersionOrUpdateOption{
		AgentConfigID:  req.AgentConfigID,
		Creator:        req.User,
		ScmVersion:     req.ScmVersion,
		Description:    req.Description,
		BitsBuildID:    req.BitsBuildID,
		IcmVersion:     req.IcmVersion,
		SourceID:       req.GetSourceID(),
		BashIcmVersion: req.GetBashIcmVersion(),
	}
	var agentConfigVersion *entity.AgentConfigVersion
	var err error

	agentConfigVersion, err = h.AgentService.BitsUpsertAgentConfigVersion(ctx, opts)
	if err != nil {
		log.V1.CtxError(ctx, "BitsAtomicCreateFeatureVersion, err: %v", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), err.Error())
		return
	}
	c.JSON(http.StatusOK, nextagent.BitsUpsertAgentVersionResponse{
		AgentConfigVersionID: agentConfigVersion.ID,
	})
}
