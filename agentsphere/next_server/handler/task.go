package serverhandler

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	crontaskservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/cron_tasks"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	"code.byted.org/devgpt/kiwis/lib/hertz"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

func (h *Handler) OpenAPICreateTask(ctx context.Context, c *app.RequestContext) {
	req := hertz.BindValidate[nextagent.OpenAPICreateTaskRequet](ctx, c)
	if req == nil {
		return
	}

	user, _ := h.AuthM.GetAccount(ctx, c)
	if user == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), common.ErrorCode_ErrNoAuth.String())
		c.Abort()
		return
	}

	// 检查用户是否登陆过，没有登陆过，自动激活
	var sync bool
	features := h.UserService.GetUserFeatures(ctx, user, false)
	if !features.Invited {
		err := h.UserService.AddUserGroup(ctx, userservice.AddUserGroupOption{
			Username: user.Username,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to add user group to user", user.Username, err)
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), common.ErrorCode_ErrInternal.String())
			c.Abort()
			return
		}
		sync = true
	}

	serviceAccount, _ := h.PermissionService.GetServiceAccount(c)
	if serviceAccount == nil {
		hertz.JSONMessage(c, http.StatusUnauthorized, int(common.ErrorCode_ErrNoAuth), common.ErrorCode_ErrNoAuth.String())
		c.Abort()
		return
	}
	_ = metrics.NSM.SessionWithOpenAPITask.WithTags(&metrics.NextServerSessionTag{
		Method:      "OpenAPICreateTask",
		ErrorReason: "",
		Username:    user.Username,
		FromApp:     serviceAccount.Name, // 调用方
	}).Add(1)

	template, err := h.TemplateService.GetTemplate(ctx, req.TemplateID)
	if err != nil || template == nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		if errors.Is(err, serverservice.ErrTemplateNotFound) {
			hertz.JSONMessage(c, http.StatusBadRequest, int(common.ErrorCode_ErrParamInvalid), "template not found")
			return
		}
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get template")
		return
	}

	var space *entity.Space
	if req.SpaceID != nil {
		space, err = h.SpaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: *req.SpaceID})
		if err != nil || space == nil {
			log.V1.CtxError(ctx, "failed to get space: %v", err)
			if errors.Is(err, serverservice.ErrSpaceNotFound) {
				hertz.JSONMessage(c, http.StatusNotFound, int(common.ErrorCode_ErrRecordNotFound), "space not found")
				return
			}
			hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get space")
			return
		}
	} else {
		space, err = h.SpaceService.GetPersonalSpace(ctx, spaceservice.GetPersonalSpaceOption{Owner: user.Username})
		if err != nil || space == nil {
			log.V1.CtxError(ctx, "failed to get space: %v", err)
			if errors.Is(err, serverservice.ErrSpaceNotFound) {
				space, err = h.SpaceService.CreatePersonalSpace(ctx, user.Username)
				if err != nil {
					log.V1.CtxError(ctx, "failed to create personal space: %v", err)
					hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to create personal space")
					return
				}
			} else {
				hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "failed to get space")
				return
			}
		}
	}
	session, _, err := h.createMessageWithTemplate(ctx, c, createMessageWithTemplateOption{
		User:      *user,
		Role:      &req.Role,
		Content:   h.rewriteContent(req.FormValue, *template),
		Source:    lo.ToPtr(serviceAccount.GetSessionSource()),
		FormValue: req.FormValue,
		Template:  *template,
		SpaceID:   &space.ID,
		Options:   lo.ToPtr(entity.ParseIDLToMessageOptionsString(ctx, req.Options)),
		Sync:      sync,
	})

	if err != nil {
		log.V1.CtxError(ctx, "create message with template", err)
		return
	}

	_, err = h.CronTaskService.CreateExecutionRecord(ctx, crontaskservice.CreateExecutionRecordOption{
		Username:    user.Username,
		SessionID:   session.ID,
		TriggerType: serviceAccount.GetTriggerTypeEntity(),
		Config:      entity.ParseTaskIDLFromEntity(req.Config),
	})
	if err != nil {
		log.V1.CtxError(ctx, "create execution task record", err)
		hertz.JSONMessage(c, http.StatusInternalServerError, int(common.ErrorCode_ErrInternal), "create execution task record")
		return
	}

	c.AbortWithStatusJSON(http.StatusOK, nextagent.OpenAPICreateTaskResponse{
		Session: getSessionFromEntity(session, nil),
	})
}

var (
	re = regexp.MustCompile(`\{\s*([^{}]+)\s*\}`)
)

func (h *Handler) rewriteContent(values *nextagent.TemplateFormValue, template entity.TemplateVersion) string {
	result := re.ReplaceAllStringFunc(template.PromptContent, func(match string) string {
		// 提取大括号内的内容
		submatch := re.FindStringSubmatch(match)
		if len(submatch) < 2 {
			return match
		}

		key := strings.TrimSpace(submatch[1])
		// 检查 key 是否在 template.PromptVariables 中存在
		if lo.ContainsBy(template.PromptVariables, func(item *entity.TemplateVariableSchema) bool {
			if item == nil {
				return false
			}
			if strings.TrimSpace(item.Name) == key {
				return true
			}
			return false
		}) {
			cleanKey := strings.ReplaceAll(key, ".", "")
			value, ok := values.Variables[cleanKey]
			if !ok {
				return match
			}
			// 判断类型
			str := ""
			if value.Content != nil {
				str = *value.Content
			}
			for _, attachment := range value.Attachments {
				str += fmt.Sprintf(" %s", attachment.FileName)
			}
			return str
		}
		// 如果变量不存在或值不存在，返回原始匹配
		return match
	})
	return result
}
