package trace

import (
	"archive/zip"
	"bytes"
	"context"
	"crypto/sha256"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	journaldal "code.byted.org/devgpt/kiwis/agentsphere/journal/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/journal"
	journalentity "code.byted.org/devgpt/kiwis/copilotstack/journal"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/stream"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/hulkcloud"
	"code.byted.org/devgpt/kiwis/port/lark"

	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"
	"go.uber.org/fx"
)

type Service struct {
	dao             *dal.DAO
	runtime         *runtimeservice.Service
	sessionService  *sessionservice.Service
	agentService    *agentservice.Service
	artifact        *artifact.Service
	conf            *config.AgentSphereConfig
	journalDao      journaldal.JournalDAO
	llmService      llm.Service
	larkClient      lark.Client
	hulkCloudClient hulkcloud.Client
	stratocubeConf  *libtcc.GenericConfig[config.StratoCubeConfig]
	userService     *userservice.Service
}

type CreateServiceOption struct {
	fx.In
	DAO             *dal.DAO
	Runtime         *runtimeservice.Service
	SessionService  *sessionservice.Service
	AgentService    *agentservice.Service
	Artifact        *artifact.Service
	Conf            *config.AgentSphereConfig
	JournalDao      journaldal.JournalDAO
	LLMService      llm.Service
	LarkClient      lark.Client
	HulkCloudClient hulkcloud.Client
	StratocubeConf  *libtcc.GenericConfig[config.StratoCubeConfig]
	UserService     *userservice.Service
}

func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		dao:             opt.DAO,
		runtime:         opt.Runtime,
		sessionService:  opt.SessionService,
		agentService:    opt.AgentService,
		artifact:        opt.Artifact,
		conf:            opt.Conf,
		journalDao:      opt.JournalDao,
		llmService:      opt.LLMService,
		larkClient:      opt.LarkClient,
		stratocubeConf:  opt.StratocubeConf,
		hulkCloudClient: opt.HulkCloudClient,
		userService:     opt.UserService,
	}, nil
}

type GetTraceSessionOption struct {
	SessionID           string
	ReplayID            string
	Account             *authentity.Account
	SkipPermissionCheck bool
}

func (s *Service) GetTraceSession(ctx context.Context, opt GetTraceSessionOption) (*nextagent.TraceSession, error) {
	var session *nextentity.Session
	var err error
	if opt.SessionID == "" && opt.ReplayID == "" {
		return nil, errors.New("session_id or replay_id is required")
	}
	if opt.SessionID != "" {
		session, err = s.sessionService.GetSessionWithDeleted(ctx, opt.SessionID, true)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get session")
		}
	}
	if opt.ReplayID != "" {
		session, err = s.sessionService.GetSessionByReplay(ctx, opt.ReplayID)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get session by replay")
		}
	}

	if !opt.SkipPermissionCheck && (opt.Account == nil || (opt.Account.Username != session.Creator && !s.userService.IsTraceAll(opt.Account))) {
		return nil, serverservice.ErrTracePermissionDenied
	}

	run, err := s.runtime.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{
		SessionID: session.ID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agent run")
	}
	// try to get runtime status by provider
	debugStatus, err := s.runtime.GetRuntimeStatus(ctx, run.ID)

	conf := s.getStratocubeConf(ctx, GetStratocubeConfOption{
		CubeID:   run.RuntimeMetadata.ContainerID,
		Username: opt.Account.Username,
	})
	fileServerURL := ""
	if debugStatus == runtimeservice.RunDebugStatusRunning {
		fileServerURL, err = s.runtime.GetRuntimeFileServerURI(ctx, run.ID)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get runtime file server uri")
		}
	}
	var agentMetadata nextagent.AgentMetadata
	if session.RuntimeMetaData.Error == "" {
		agentMetadata.AgentConfigID = session.RuntimeMetaData.AgentConfigID
		agentMetadata.AgentConfigVersion = int32(session.RuntimeMetaData.AgentConfigVersion)
		agentMetadata.AgentConfigVersionID = session.RuntimeMetaData.AgentConfigVersionID

		agentConfig, _, err := s.agentService.GetAgentConfig(ctx, agentservice.GetAgentConfigOption{
			ID:      session.RuntimeMetaData.AgentConfigID,
			Version: lo.ToPtr(int32(session.RuntimeMetaData.AgentConfigVersion)),
		})
		if err == nil {
			agent, err := s.agentService.GetAgent(ctx, agentservice.GetAgentOption{
				ID: agentConfig.AgentID,
			})
			if err == nil {
				agentMetadata.AgentName = agent.Name
			}
		}
	}

	traceSession := nextagent.TraceSession{
		ID:             session.ID,
		Creator:        session.Creator,
		CreatedAt:      session.CreatedAt.Format(time.RFC3339),
		UpdatedAt:      session.UpdatedAt.Format(time.RFC3339),
		Title:          session.Title,
		Status:         nextagent.SessionStatus(session.Status),
		RunID:          run.ID,
		RunProvider:    nextagent.RuntimeProvider(run.RuntimeMetadata.RuntimeProvider),
		RunStatus:      string(run.Status),
		RunDebugStatus: nextagent.RunDebugStatus(debugStatus),
		WebshellURL:    conf.WebshellURL,
		FileServerURL:  fileServerURL,
		BrowserURL:     fmt.Sprintf(conf.BrowserURL, run.RuntimeMetadata.ContainerID, extractCubeWildcardDomain(run.RuntimeMetadata.ContainerHost)),
		EventsURL:      fmt.Sprintf("%s%s/runtime/log/session/%s", s.conf.RuntimeAPIConfig.APIBaseURL, s.conf.RuntimeAPIConfig.APIPrefix, session.ID),
		AgentMetadata:  &agentMetadata,
		LogID:          session.RuntimeMetaData.LogID,
	}

	return &traceSession, nil
}

func extractCubeWildcardDomain(host string) string {
	if host == "" {
		return ""
	}
	parts := strings.Split(host, ".")
	// 如果分割后的部分少于3个，返回空字符串
	if len(parts) < 3 {
		return ""
	}
	return parts[len(parts)-3]
}

type StratocubeConf struct {
	FileServerURL string
	WebshellURL   string
	BrowserURL    string
}

type GetStratocubeConfOption struct {
	CubeID   string
	Username string
}

func (s *Service) getStratocubeConf(ctx context.Context, opt GetStratocubeConfOption) *StratocubeConf {
	if env.IsProduct() {
		if env.GetCurrentVRegion() == env.R_SingaporeCentral {
			var sdpWebShellURL string
			serviceToken, err := s.hulkCloudClient.GetJWTToken(ctx)
			if err != nil {
				log.V1.CtxError(ctx, "failed get jwt token from hulkcloud: %v", err)
			} else {
				sdpWebShellURL = s.runtime.GetSDPWebShellURL(ctx, runtimeservice.GetSDPWebShellURLOption{
					Username:     opt.Username,
					ServiceToken: serviceToken,
					CubeID:       opt.CubeID,
				})
			}

			return &StratocubeConf{
				FileServerURL: "http://%s.cube-kubestrato-my.byted.org:17081/file/",
				WebshellURL:   sdpWebShellURL,
				BrowserURL:    "https://strato-https-proxy.bytedance.net/%s.%s/novnc/vnc.html?autoconnect=true&reconnect=true&reconnect_delay=1000&password=password&resize=scale&view_only=true",
			}
		}

		return &StratocubeConf{
			// https://bytedance.larkoffice.com/wiki/Pg0MwkVu0ihfzikOkQecA9QLnLc
			FileServerURL: "http://%s.cube-kubestrato-online.byted.org:17081/file/",
			WebshellURL:   fmt.Sprintf("https://stratocube-online.byted.org/webshell/index.html?id=%s&container=runtime", opt.CubeID),
			BrowserURL:    "https://strato-https-proxy.bytedance.net/%s.%s/novnc/vnc.html?autoconnect=true&reconnect=true&reconnect_delay=1000&password=password&resize=scale&view_only=true",
		}
	}
	return &StratocubeConf{
		FileServerURL: "http://%s.strato-cloudbuild-ppe.byted.org:17081/file/",
		WebshellURL:   fmt.Sprintf("https://stratocube-test.byted.org/webshell/index.html?id=%s&container=runtime", opt.CubeID),
		BrowserURL:    "https://strato-https-proxy.bytedance.net/%s.%s/novnc/vnc.html?autoconnect=true&reconnect=true&reconnect_delay=1000&password=password&resize=scale&view_only=true",
	}
}

type GetTraceEventsOption struct {
	RunID               string
	SessionID           string
	ContainerID         string
	URI                 string
	Provider            string
	Account             *authentity.Account
	SkipPermissionCheck bool
}

func (s *Service) GetTraceEvents(ctx context.Context, opt GetTraceEventsOption) (*stream.RecvChannel[*iris.AgentRunEvent[any]], error) {
	run, err := s.runtime.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{
		RunID:     opt.RunID,
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agent run")
	}
	if !opt.SkipPermissionCheck && (opt.Account == nil || opt.Account.Username != run.CreatorID && !s.userService.IsTraceAll(opt.Account)) {
		return nil, serverservice.ErrTracePermissionDenied
	}

	if opt.SkipPermissionCheck {
		return s.runtime.GetLogWithOptions(ctx, &entity.RuntimeMetadata{
			ContainerID:     lo.Ternary(opt.ContainerID != "", opt.ContainerID, run.RuntimeMetadata.ContainerID),
			URI:             lo.Ternary(opt.URI != "", opt.URI, run.RuntimeMetadata.URI),
			RuntimeProvider: lo.Ternary(opt.Provider != "", entity.RuntimeProviderType(opt.Provider), run.RuntimeMetadata.RuntimeProvider),
		}, run.ID, opt.Account, true)
	}
	return s.runtime.GetLog(ctx, &entity.RuntimeMetadata{
		ContainerID:     lo.Ternary(opt.ContainerID != "", opt.ContainerID, run.RuntimeMetadata.ContainerID),
		URI:             lo.Ternary(opt.URI != "", opt.URI, run.RuntimeMetadata.URI),
		RuntimeProvider: lo.Ternary(opt.Provider != "", entity.RuntimeProviderType(opt.Provider), run.RuntimeMetadata.RuntimeProvider),
	}, run.ID, opt.Account)
}

func (s *Service) ResumeRuntime(ctx context.Context, id string, account *authentity.Account) error {
	return s.runtime.ResumeRuntimeForDebug(ctx, runtimeservice.ResumeRuntimeForDebugOption{
		RunID:   id,
		Account: account,
	})
}

func (s *Service) SuspendRuntime(ctx context.Context, id string, account *authentity.Account) error {
	return s.runtime.SuspendRuntimeForDebug(ctx, runtimeservice.SuspendRuntimeForDebugOption{
		RunID:   id,
		Account: account,
	})
}

func (s *Service) DeleteRuntime(ctx context.Context, id string, account *authentity.Account) error {
	run, err := s.runtime.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{
		RunID: id,
	})
	if err != nil {
		return errors.Wrap(err, "failed to get agent run")
	}
	if account == nil || (account.Username != run.CreatorID && !s.userService.IsTraceAll(account)) {
		return serverservice.ErrTracePermissionDenied
	}

	session, err := s.sessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: run.SessionID,
	})
	if err != nil {
		return errors.Wrap(err, "failed to get session")
	}

	err = s.runtime.SendNextRuntimeImmediatelyDeleteWorkspace(ctx, runtimeservice.ScheduleOption{
		SessionID: session.ID,
	})
	if err != nil {
		return errors.Wrap(err, "failed to send next runtime immediately delete")
	}
	_, err = s.dao.UpdateSession(ctx, dal.UpdateSessionOption{
		ID:                 session.ID,
		CanResume:          lo.ToPtr(false),
		CanNotResumeReason: lo.ToPtr(nextentity.SessionCanNotResumeReasonDeleted),
	})
	if err != nil {
		return errors.Wrap(err, "failed to update session")
	}
	return nil
}

type GetTraceSessionChatOption struct {
	SessionID string
	PageNum   int64
	PageSize  int64
	Status    string
	Type      string
	TraceID   string
	Account   *authentity.Account
}

func (s *Service) GetTraceSessionChat(ctx context.Context, opt GetTraceSessionChatOption) (int64, []*nextagent.ChatCompletion, error) {
	session, err := s.sessionService.GetSessionWithDeleted(ctx, opt.SessionID, true)
	if err != nil {
		return 0, nil, errors.Wrap(err, "failed to get session")
	}

	if opt.Account == nil || (opt.Account.Username != session.Creator && !s.userService.IsTraceAll(opt.Account)) {
		return 0, nil, serverservice.ErrTracePermissionDenied
	}

	total, items, err := s.journalDao.ListChatCompletionLog(ctx, journaldal.ListChatCompletionLogOption{
		SessionID: &opt.SessionID,
		Limit:     opt.PageSize,
		Offset:    (opt.PageNum - 1) * opt.PageSize,
		Status:    lo.Ternary(opt.Status != "", lo.ToPtr(journalentity.PromptCompletionStatus(opt.Status)), nil),
		Type:      lo.Ternary(opt.Type != "", lo.ToPtr(journalentity.PromptCompletionType(opt.Type)), nil),
		AppID:     lo.Ternary(opt.TraceID != "", lo.ToPtr(opt.TraceID), nil),
	})

	if err != nil {
		return 0, nil, errors.Wrap(err, "failed to list chat completion log")
	}

	return total, lo.Map(items, func(item *journal.PromptCompletion, _ int) *nextagent.ChatCompletion {
		return &nextagent.ChatCompletion{
			ID:        strconv.FormatInt(item.ID, 10),
			CreatedAt: item.CreatedAt.Format(time.RFC3339),
			UpdatedAt: item.UpdatedAt.Format(time.RFC3339),
			Prompt:    lo.FromPtr(item.Prompt),
			Response:  lo.FromPtr(item.ContentRaw),
			Status:    string(item.Status),
			Type:      string(item.Type),
			Metadata:  conv.JSONString(lo.FromPtr(item.RequestMetadata)),
			ModelName: item.ModelName,
		}
	}), nil
}

func (s *Service) ListSessionAgentStep(ctx context.Context, sessionID string) ([]*nextagent.AgentStep, error) {
	steps, err := s.runtime.GetStepGraph(ctx, sessionID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to list agent steps")
	}
	return lo.Map(steps, func(s *entity.AgentRunStep, _ int) *nextagent.AgentStep {
		mcpTool := ""
		if s.Action.Name == "mcp_call" {
			mcpTool, _ = s.Inputs.Inputs["name"].(string)
		}
		return &nextagent.AgentStep{
			Action:    s.Action.Name,
			Executor:  s.Metadata.Executor,
			MCPTool:   mcpTool,
			StartTime: s.CreatedAt.Unix(),
			EndTime:   s.UpdatedAt.Unix(),
			Status:    nextagent.AgentStepStatus(s.Status),
			StepID:    s.ID,
		}
	}), nil
}

type DownloadSessionLogOption struct {
	SessionID string
	Account   *authentity.Account
}

func (s *Service) GetSessionLogArchive(ctx context.Context, opt DownloadSessionLogOption) (io.ReadCloser, error) {
	run, err := s.runtime.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agent run")
	}
	if opt.Account == nil || (opt.Account.Username != run.CreatorID && !s.userService.IsTraceAll(opt.Account)) {
		return nil, serverservice.ErrTracePermissionDenied
	}

	artifacts, err := s.artifact.ListSessionArtifacts(ctx, artifact.ListSessionArtifactsOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to list session artifacts")
	}
	logArtifact, _ := lo.Find(artifacts, func(a *nextentity.Artifact) bool {
		return a.Type == nextentity.ArtifactTypeLogs
	})
	if logArtifact == nil {
		return nil, errors.New("no log artifact found")
	}
	metas := logArtifact.FileMetas

	pr, pw := io.Pipe()

	wg := conc.NewWaitGroup()
	wg.Go(func() {
		defer pw.Close()

		zipWriter := zip.NewWriter(pw)
		defer zipWriter.Close()

		var zipMutex sync.Mutex
		innerWg := conc.NewWaitGroup()
		timeoutDuration := 30 * time.Second
		childCtx, cancel := context.WithTimeout(ctx, timeoutDuration)
		defer cancel()

		for _, meta := range metas {
			meta := meta
			innerWg.Go(func() {
				artifact, _, err := s.artifact.GetArtifactFile(ctx, artifact.GetArtifactFileOption{
					Artifact: *logArtifact,
					Path:     meta.Name,
					Account:  opt.Account,
					TosCtx:   &childCtx,
				})
				if err != nil {
					log.V1.CtxError(ctx, "failed to get artifact file: %s, err: %+v", meta.Name, err)
					return
				}
				defer artifact.Close()

				zipMutex.Lock()
				writer, err := zipWriter.Create(meta.Name)
				if err != nil {
					zipMutex.Unlock()
					log.V1.CtxError(ctx, "failed to create zip entry: %s, err: %+v", meta.Name, err)
					return
				}

				buf := make([]byte, 4*1024*1024) // 4MB缓冲区
				_, err = io.CopyBuffer(writer, artifact, buf)
				zipMutex.Unlock()

				if err != nil {
					log.V1.CtxError(ctx, "failed to copy data to zip: %s, err: %+v", meta.Name, err)
				}
			})
		}
		innerWg.Wait()
	})

	return pr, nil
}

type ListSessionAttachmentsOption struct {
	SessionID           string
	Account             *authentity.Account
	ImageXConf          *config.ImageXTCCConfig
	FilePath            string
	SkipPermissionCheck bool
}

func (s *Service) ListSessionLarkDocuments(ctx context.Context, opt ListSessionAttachmentsOption) ([]*nextagent.Document, error) {
	// check session permission
	if !opt.SkipPermissionCheck {
		if err := s.checkSessionPermission(ctx, opt.SessionID, opt.Account); err != nil {
			return nil, err
		}
	}

	// 1. get citation references
	referenceMap, err := s.artifact.GetReferenceMap(ctx, opt.SessionID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact reference: %v", err)
	}

	// 2. get artifacts
	artifacts, err := s.artifact.ListSessionArtifacts(ctx, artifact.ListSessionArtifactsOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to list session artifacts")
	}

	larkLinks := make([]string, 0) // lark doc links
	imageMap := make(map[string]string)
	documents := make([]*nextagent.Document, 0)

	for _, a := range artifacts {
		if a.Type != nextentity.ArtifactTypeFile && a.Type != nextentity.ArtifactTypeLink {
			continue
		}
		// find lark.md from artifacts
		if strings.HasPrefix(a.Key, nextentity.ArtifactKeyFileLarkMD) {
			for _, meta := range a.FileMetas {
				if meta.Type != nextentity.ArtifactTypeFile || meta.SubType != "md" {
					continue
				}
				if opt.FilePath != "" && meta.Name != opt.FilePath {
					continue
				}
				content, _, err := s.artifact.GetArtifactFileContent(ctx, artifact.GetArtifactFileOption{
					Artifact: *a,
					Path:     meta.Name,
					Account:  opt.Account,
				})
				if err != nil {
					log.V1.CtxError(ctx, "failed to get artifact file content: %v", err)
					return nil, errors.Wrap(err, "failed to get artifact file content")
				}
				docName := filepath.Base(meta.Name)
				if metadata, ok := a.Metadata.(*nextentity.FileArtifactTypeMetadata); ok {
					if metadata.Title != "" {
						docName = metadata.Title
					}
				}

				documents = append(documents, &nextagent.Document{
					Name:       docName,
					FilePath:   meta.Name,
					Type:       "markdown",
					Content:    string(content),
					Size:       int64(len(content)),
					ArtifactID: &a.ID,
					RelatedFiles: lo.FilterMap(a.FileMetas, func(r nextentity.FileMeta, _ int) (string, bool) {
						if r.Name == meta.Name {
							return "", false
						}
						return r.Name, true
					}),
					CreatedAt: a.CreatedAt.Format(time.RFC3339),
				})
			}
			continue
		}
		for _, meta := range a.FileMetas {
			// filter lark doc links
			if meta.Type == nextentity.ArtifactTypeLink && meta.SubType == "lark_doc" {
				larkLinks = append(larkLinks, meta.Name)
			}
			// filter image
			if meta.Type == nextentity.ArtifactTypeImage {
				imageMap[meta.Name] = meta.ImageURL(opt.ImageXConf.DefaultDomain, opt.ImageXConf.DefaultTemplateID)
			}
		}
	}

	if len(larkLinks) > 0 && len(documents) == 0 {
		// no lark.md found from artifacts, find lark.md from tool events
		toolEvents, err := s.sessionService.GetSessionEvents(ctx, sessionservice.GetSessionEventsOption{
			SessionID: opt.SessionID,
			EventName: nextagent.EventNameUseTool,
		})
		if err != nil {
			return nil, errors.Wrap(err, "failed to get session events")
		}

		ToolDocuments := lo.FilterMap(toolEvents, func(e *nextentity.Event, _ int) (*nextagent.Document, bool) {
			if d, ok := e.EventData.Data.(map[string]any); ok {
				if useToolEvent, err := conv.MapToStructByJSONTag[*nextagent.UseToolEvent](d); err == nil && useToolEvent != nil {
					if useToolEvent.TextEditor != nil && useToolEvent.TextEditor.CreateFile != nil {
						filePath := useToolEvent.TextEditor.CreateFile.FilePath
						if opt.FilePath != "" && filePath != opt.FilePath {
							return nil, false
						}
						if nextentity.IsLarkMDFile(filePath) && !lo.ContainsBy(documents, func(d *nextagent.Document) bool {
							return d.Name == filePath
						}) {
							content := useToolEvent.TextEditor.CreateFile.Content
							// 替换引用标签
							if len(referenceMap) > 0 {
								contentReader := bytes.NewReader([]byte(content))
								processedReader, size, err := s.artifact.ReplaceCiteTagsByLine(contentReader, &nextentity.FileMeta{Size: int64(len(content))}, referenceMap)
								if err == nil {
									processedBody := make([]byte, size)
									_, err = processedReader.Read(processedBody)
									if err == nil {
										content = string(processedBody)
									}
								}
							}
							// 替换图片引用
							docDir := filepath.Dir(filePath)
							for imgName, imgURL := range imageMap {
								content = strings.Replace(content, fmt.Sprintf("](%s)", imgName), fmt.Sprintf("](%s)", imgURL), -1)
								content = strings.Replace(content, fmt.Sprintf("](./%s)", imgName), fmt.Sprintf("](%s)", imgURL), -1)

								// 处理相对于文档目录的相对引用
								if docDir != "." && strings.HasPrefix(imgName, docDir+"/") {
									relativeImgName := strings.TrimPrefix(imgName, docDir+"/")
									content = strings.Replace(content, fmt.Sprintf("](%s)", relativeImgName), fmt.Sprintf("](%s)", imgURL), -1)
								}
							}

							// 为 tool events 生成模拟的 artifact ID，避免同名文件冲突
							mockArtifactID := generateMockArtifactID(opt.SessionID, filePath, content)

							return &nextagent.Document{
								Name:       filePath,
								Type:       "markdown",
								Content:    content,
								Size:       int64(len(content)),
								ArtifactID: &mockArtifactID, // 使用生成的模拟 artifact ID
							}, true
						}
					}
				}
			}
			return nil, false
		})

		documents = append(documents, ToolDocuments...)
	}

	return documents, nil
}

func generateMockArtifactID(sessionID, filePath, content string) string {
	hasher := sha256.New()
	hasher.Write([]byte(sessionID))
	hasher.Write([]byte(filePath))
	hasher.Write([]byte(content))
	hash := fmt.Sprintf("%x", hasher.Sum(nil))

	return fmt.Sprintf("mock_%s", hash[:16])
}

type ConvertSessionDocumentToLarkOption struct {
	SessionID       string
	FilePath        string
	ArtifactID      *string
	Conf            *config.NextAgentUploadLarkFileConfig
	ForceRegenerate bool // 是否强制重新生成，忽略已有的转换记录
	Account         *authentity.Account
	ImageXConf      *config.ImageXTCCConfig
}

func (s *Service) ConvertSessionDocumentToLark(ctx context.Context, opt ConvertSessionDocumentToLarkOption) (larkURL string, err error) {
	// check session permission
	if err := s.checkSessionPermission(ctx, opt.SessionID, opt.Account); err != nil {
		return "", err
	}

	if !opt.ForceRegenerate {
		conversion, getErr := s.dao.GetDebugLarkmdConversion(ctx, dal.GetDebugLarkmdConversionOption{
			SessionID:  opt.SessionID,
			FilePath:   opt.FilePath,
			ArtifactID: opt.ArtifactID,
		})
		if getErr == nil {
			if conversion.Status == "success" {
				// add permission
				token := strings.TrimPrefix(conversion.LarkURL, "https://bytedance.larkoffice.com/docx/")
				if err := s.larkClient.AddLarkFilePermission(ctx, token, opt.Account.Email, "view", "docx"); err != nil {
					log.V1.CtxError(ctx, "failed to add lark file permission: %v", err)
				} else {
					return conversion.LarkURL, nil
				}
			}
			if conversion.Status == "failed" {
				return "", errors.New(*conversion.ErrorMessage)
			}
		}
	}

	_, createErr := s.dao.CreateDebugLarkmdConversion(ctx, dal.CreateDebugLarkmdConversionOption{
		SessionID:  opt.SessionID,
		FilePath:   opt.FilePath,
		Status:     "pending",
		LarkURL:    "",
		ArtifactID: opt.ArtifactID,
	})
	if createErr != nil {
		return "", errors.Wrap(createErr, "failed to create debug larkmd conversion")
	}

	defer func() {
		if err != nil {
			errMsg := err.Error()
			s.dao.UpdateDebugLarkmdConversionStatus(ctx, opt.SessionID, opt.FilePath, "failed", "", &errMsg)
		} else {
			s.dao.UpdateDebugLarkmdConversionStatus(ctx, opt.SessionID, opt.FilePath, "success", larkURL, nil)
		}
	}()

	documents, err := s.ListSessionLarkDocuments(ctx, ListSessionAttachmentsOption{
		SessionID:           opt.SessionID,
		FilePath:            opt.FilePath,
		ImageXConf:          opt.ImageXConf,
		SkipPermissionCheck: true,
	})
	if err != nil {
		return "", errors.Wrap(err, "failed to list session attachments")
	}
	if len(documents) == 0 {
		return "", errors.New("no documents found")
	}
	doc := documents[0]

	relatedFilesContent := map[string][]byte{}

	if doc.ArtifactID != nil && !strings.HasPrefix(*doc.ArtifactID, "mock_") && len(doc.RelatedFiles) > 0 {
		artifacts, err := s.artifact.ListSessionArtifacts(ctx, artifact.ListSessionArtifactsOption{
			SessionID:   opt.SessionID,
			ArtifactIDs: []string{*doc.ArtifactID},
		})
		if err == nil && len(artifacts) > 0 {
			for _, relatedFile := range doc.RelatedFiles {
				fileContent, _, err := s.artifact.GetArtifactFileContent(ctx, artifact.GetArtifactFileOption{
					Artifact: *artifacts[0],
					Path:     relatedFile,
					Account:  opt.Account,
				})
				if err == nil {
					relatedFilesContent[relatedFile] = fileContent
				}
			}
		}
	}

	documentID, err := s.mockAgentConvertLarkMarkdownFile(ctx, ProcessLarkMarkdownFileArgs{
		Title:        doc.Name,
		Content:      doc.Content,
		RelatedFiles: relatedFilesContent,
		FolderToken:  opt.Conf.TraceFolderToken,
	})
	if err != nil {
		return "", errors.Wrap(err, "failed to mock agent convert lark markdown file")
	}
	err = s.larkClient.AddLarkFilePermission(ctx, documentID, opt.Account.Email, "view", "docx")
	if err != nil {
		return "", errors.Wrap(err, "failed to add lark file permission")
	}

	return fmt.Sprintf("https://bytedance.larkoffice.com/docx/%s", documentID), nil
}

type ConvertMarkdownToLarkOption struct {
	SessionID string
	Markdown  string
	Conf      *config.NextAgentUploadLarkFileConfig
	Account   *authentity.Account
}

func (s *Service) ConvertMarkdownToLark(ctx context.Context, opt ConvertMarkdownToLarkOption) (larkURL string, err error) {
	title := fmt.Sprintf("[Aime RL Debug]/%s/%s", opt.SessionID, time.Now().Format("20060102_150405"))
	relatedFiles := make(map[string][]byte)
	if opt.SessionID != "" {
		relatedFiles, _ = s.getRelatedFiles(ctx, opt.SessionID, opt.Account)
	}
	documentID, err := s.mockAgentConvertLarkMarkdownFile(ctx, ProcessLarkMarkdownFileArgs{
		Title:        title,
		Content:      opt.Markdown,
		RelatedFiles: relatedFiles,
		FolderToken:  opt.Conf.RLFolderToken,
	})
	if err != nil {
		return "", errors.Wrap(err, "failed to convert markdown to lark file")
	}
	err = s.larkClient.AddLarkFilePermission(ctx, documentID, opt.Account.Email, "view", "docx")
	if err != nil {
		return "", errors.Wrap(err, "failed to add lark file permission")
	}

	return fmt.Sprintf("https://bytedance.larkoffice.com/docx/%s", documentID), nil
}

func (s *Service) getRelatedFiles(ctx context.Context, sessionID string, account *authentity.Account) (map[string][]byte, error) {
	artifacts, err := s.artifact.ListSessionArtifacts(ctx, artifact.ListSessionArtifactsOption{
		SessionID: sessionID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to list session artifacts")
	}
	relatedFiles := make(map[string][]byte)
	for _, a := range artifacts {
		if a.Type == nextentity.ArtifactTypeFile && strings.HasPrefix(a.Key, nextentity.ArtifactKeyFileLarkMD) {
			for _, meta := range a.FileMetas {
				if meta.Type == nextentity.ArtifactTypeFile && meta.SubType == "md" {
					continue
				}
				fileContent, _, err := s.artifact.GetArtifactFileContent(ctx, artifact.GetArtifactFileOption{
					Artifact: *a,
					Path:     meta.Name,
					Account:  account,
				})
				if err == nil {
					relatedFiles[meta.Name] = fileContent
				}
			}
		}
	}
	return relatedFiles, nil
}

func (s *Service) convertMarkdownToLark(ctx context.Context, doc *nextagent.Document, conf *config.NextAgentUploadLarkFileConfig) (larkURL string, err error) {
	// https://open.larkoffice.com/document/server-docs/docs/drive-v1/import_task/import-user-guide
	token, err := s.larkClient.UploadLarkFile(ctx, "[Aime Debug] "+doc.Name, conf.TraceFolderToken, int(doc.Size), bytes.NewReader([]byte(doc.Content)))
	if err != nil {
		return "", errors.Wrap(err, "failed to upload lark file")
	}
	baseName := strings.Split(doc.Name, ".")[0]
	importTaskReq := larkdrive.NewCreateImportTaskReqBuilder().
		ImportTask(larkdrive.NewImportTaskBuilder().
			FileExtension(strings.TrimPrefix(filepath.Ext(doc.Name), ".")).
			FileName("[Aime Debug] " + baseName).
			FileToken(*token).
			Type("docx").
			Point(larkdrive.NewImportTaskMountPointBuilder().
				MountType(1).
				MountKey(conf.TraceFolderToken).
				Build()).
			Build()).
		Build()
	ticket, err := s.larkClient.CreateImportTask(ctx, importTaskReq, "")
	if err != nil {
		return "", errors.Wrap(err, "failed to create import task")
	}

	data, err := s.larkClient.GetImportTask(ctx, *ticket, "")
	if err != nil {
		return "", errors.Wrap(err, "failed to get import task")
	}

	ctx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()

	maxRetries := 10
	for i := 0; i < maxRetries; i++ {
		select {
		case <-ctx.Done():
			return "", errors.New("import task timeout")
		default:
			if data.Result.JobStatus != nil {
				switch *data.Result.JobStatus {
				case 0:
					log.V1.CtxInfo(ctx, "import task completed successfully after %d retries", i)
					return *data.Result.Url, nil
				case 1, 2:
					log.V1.CtxInfo(ctx, "import task in progress, status: %d, retry: %d", *data.Result.JobStatus, i)
				default:
					errMsg := "unknown error"
					if data.Result.JobErrorMsg != nil {
						errMsg = *data.Result.JobErrorMsg
					}
					return "", fmt.Errorf("import task failed with status %d: %s", *data.Result.JobStatus, errMsg)
				}
			}

			time.Sleep(time.Second)

			data, err = s.larkClient.GetImportTask(ctx, *ticket, "")
			if err != nil {
				return "", errors.Wrap(err, "failed to get import task status")
			}
		}
	}

	return "", errors.New("import task timeout after 10 retries")
}

func (s *Service) ListModels(ctx context.Context) ([]*nextagent.Model, error) {
	models, err := s.runtime.ListModels(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to list models")
	}
	return models, nil
}

func (s *Service) ChatCompletion(ctx context.Context, req llm.ChatCompletionRequest) (llm.ChatCompletionStreamResult, error) {
	return s.llmService.ChatCompletion(ctx, req)
}

type GetTraceMCPEventsOption struct {
	SessionID string
	Account   *authentity.Account
}

func (s *Service) GetTraceMCPEvents(ctx context.Context, opt GetTraceMCPEventsOption) (*stream.RecvChannel[*iris.AgentRunEvent[any]], error) {
	run, err := s.runtime.GetAgentRun(ctx, runtimeservice.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get agent run")
	}
	return s.GetTraceEvents(ctx, GetTraceEventsOption{
		RunID:               run.ID,
		Account:             opt.Account,
		SkipPermissionCheck: true,
	})
}

type DownloadFileFromFileserverOption struct {
	FileServerURL string
	FilePath      string
}

func (s *Service) DownloadFileFromFileserver(ctx context.Context, opt DownloadFileFromFileserverOption) (int64, io.ReadCloser, error) {
	fileReq, err := http.NewRequestWithContext(ctx, http.MethodGet, fmt.Sprintf("%s/%s", opt.FileServerURL, opt.FilePath), nil)
	if err != nil {
		return 0, nil, err
	}
	scConf := lo.FirstOrEmpty(s.stratocubeConf.GetValue().TenantConfig)
	fileReq.SetBasicAuth("admin", scConf.FileServerToken)
	fileResp, err := http.DefaultClient.Do(fileReq)
	if err != nil {
		return 0, nil, err
	}
	if fileResp.StatusCode != http.StatusOK {
		return 0, nil, errors.Errorf("unexpected status code from file server: %d", fileResp.StatusCode)
	}
	return fileResp.ContentLength, fileResp.Body, nil
}

type DownloadFileFromLogArtifactsOption struct {
	SessionID string
	Account   *authentity.Account
	Filename  string
}

func (s *Service) DownloadFileFromLogArtifacts(ctx context.Context, opt DownloadFileFromLogArtifactsOption) (int64, io.ReadCloser, error) {
	artifacts, err := s.artifact.ListSessionArtifacts(ctx, artifact.ListSessionArtifactsOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return 0, nil, errors.Wrap(err, "failed to list session artifacts")
	}
	logArtifact, _ := lo.Find(artifacts, func(a *nextentity.Artifact) bool {
		return a.Type == nextentity.ArtifactTypeLogs
	})
	if logArtifact == nil {
		return 0, nil, errors.New("no log artifact found")
	}
	metas := logArtifact.FileMetas

	resMeta, ok := lo.Find(metas, func(m nextentity.FileMeta) bool {
		return m.Name == opt.Filename
	})
	if !ok {
		return 0, nil, errors.New("target file is not found")
	}
	artifact, _, err := s.artifact.GetArtifactFile(ctx, artifact.GetArtifactFileOption{
		Artifact: *logArtifact,
		Path:     resMeta.Name,
		Account:  opt.Account,
		TosCtx:   &ctx,
	})
	if err != nil {
		return 0, nil, errors.Wrap(err, "failed to get artifact file")
	}
	return resMeta.Size, artifact, nil
}

func (s *Service) checkSessionPermission(ctx context.Context, sessionID string, account *authentity.Account) error {
	if account == nil {
		return serverservice.ErrTracePermissionDenied
	}
	session, err := s.sessionService.GetSession(ctx, sessionservice.GetSessionOption{
		SessionID: sessionID,
	})
	if err != nil {
		return errors.Wrap(err, "failed to get session")
	}
	if account.Username != session.Creator && !s.userService.IsTraceAll(account) {
		return serverservice.ErrTracePermissionDenied
	}
	return nil
}
