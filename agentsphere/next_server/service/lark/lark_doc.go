package lark

import (
	"context"
	"html"
	"regexp"
	"strings"
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/gjson"
	"github.com/cenkalti/backoff/v4"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"
	"gorm.io/gorm"
)

// ListLarkDockComments 获取飞书云文档的所有评论信息
func (s *Service) ListLarkDocComments(ctx context.Context, sessionID string, userName string) ([]*entity.ArtifactLarkDocComment, error) {
	// 1. 获取 session 中的 lock doc 产物
	artifacts, err := s.getSessionLarkDocArtifacts(ctx, sessionID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session artifacts")
	}

	p := poolsdk.New().WithMaxGoroutines(3).WithErrors()

	resultChan := make(chan *entity.ArtifactLarkDocComment, 3)
	artifactComments := make([]*entity.ArtifactLarkDocComment, 0)
	done := make(chan struct{})

	go func() {
		defer close(done)
		for result := range resultChan {
			artifactComments = append(artifactComments, result)
		}
	}()

	for _, artifact := range artifacts {
		for _, fileMeta := range artifact.FileMetas {
			if fileMeta.SubType != entity.LinkArtifactKeySourceLarkDoc.String() || fileMeta.LarkDocVersionToken != "" {
				continue
			}
			artifactID := artifact.ID
			fileName := fileMeta.Name
			artifactVersion := artifact.Version
			fileUrl := gjson.Get(fileMeta.Content, "url").String()

			p.Go(func() error {
				larkDocComments, err := s.GetLarkFileComments(ctx, fileUrl, entity.LarkDocTypeDocx)
				if err != nil {
					return err
				}

				resultChan <- &entity.ArtifactLarkDocComment{
					ArtifactID: artifactID,
					Name:       fileName,
					LarkDocUrl: fileUrl,
					Comments:   larkDocComments,
					Version:    artifactVersion,
				}
				return nil
			})
		}
	}

	if err := p.Wait(); err != nil {
		close(resultChan)
		<-done
		return nil, err
	}
	close(resultChan)
	<-done

	return artifactComments, nil
}

func (s *Service) GetLarkFileComments(ctx context.Context, fileUrl string, fileType string) ([]*agententity.CommentMetadata, error) {
	// 使用正则表达式提取文档 token
	fileToken := extractLarkDocToken(fileUrl)
	if fileToken == "" {
		log.V1.CtxWarn(ctx, "extract lark doc token failed, lark url: %s", fileUrl)
		return nil, nil
	}
	var comments []*larkdrive.FileComment

	operation := func() error {
		var err error
		comments, err = s.larkClient.GetLarkDocComments(ctx, false, fileToken, fileType, "")
		return err
	}

	// 使用指数退避机制
	larkCommentsTccConf := s.tccConf.NextLarkCommentsConfig.GetPointer()
	exponentialBackOff := backoff.NewExponentialBackOff()
	exponentialBackOff.InitialInterval = lo.Ternary(larkCommentsTccConf.Interval > 0, time.Duration(larkCommentsTccConf.Interval), 200) * time.Millisecond
	exponentialBackOff.MaxInterval = lo.Ternary(larkCommentsTccConf.MaxInterval > 0, time.Duration(larkCommentsTccConf.MaxInterval), 500) * time.Millisecond
	exponentialBackOff.MaxElapsedTime = 0 // 不限制总时间，只限制重试次数
	err := backoff.Retry(operation, backoff.WithMaxRetries(exponentialBackOff, uint64(lo.Ternary(larkCommentsTccConf.MaxRetry > 0, larkCommentsTccConf.MaxRetry, 3))))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lark doc comments after retries")
	}

	comments = s.filterComments(ctx, fileToken, comments)
	return s.processComments(ctx, comments)
}

func (s *Service) filterComments(ctx context.Context, documentID string, comments []*larkdrive.FileComment) []*larkdrive.FileComment {
	blocks, err := s.larkClient.GetLarkDocxBlocks(ctx, documentID, "")
	if err != nil {
		log.V1.CtxWarn(ctx, "get lark docx blocks failed, err: %v", err)
		return comments
	}
	commentIDMap := make(map[string]struct{})
	for _, block := range blocks {
		for _, commentID := range block.CommentIds {
			commentIDMap[commentID] = struct{}{}
		}
	}

	var filteredComments []*larkdrive.FileComment
	for _, comment := range comments {
		if _, ok := commentIDMap[lo.FromPtrOr(comment.CommentId, "")]; ok {
			filteredComments = append(filteredComments, comment)
		}
	}
	return filteredComments
}

// processDocumentComments 处理单个文档的评论
func (s *Service) processComments(ctx context.Context, comments []*larkdrive.FileComment) ([]*agententity.CommentMetadata, error) {
	larkDocComments := make([]*agententity.CommentMetadata, 0, len(comments))
	for _, comment := range comments {
		var username string
		var err error
		if comment.UserId != nil {
			username, err = s.getUsernameByID(ctx, *comment.UserId)
			if err != nil {
				log.V1.CtxWarn(ctx, "get lark username by id failed, err: %v", err)
			}
		}

		// 处理评论回复
		replies, err := s.processCommentReplies(ctx, comment)
		if err != nil {
			return nil, err
		}

		quote := ""
		if comment.Quote != nil {
			quote = *comment.Quote
		}
		larkDocComments = append(larkDocComments, &agententity.CommentMetadata{
			CommentID: lo.FromPtrOr(comment.CommentId, ""),
			Quote:     quote,
			CreateAt:  time.Unix(int64(lo.FromPtrOr(comment.CreateTime, 0)), 0),
			UpdateAt:  time.Unix(int64(lo.FromPtrOr(comment.UpdateTime, 0)), 0),
			UserID:    lo.FromPtrOr(comment.UserId, ""),
			Username:  username,
			Replies:   replies,
		})
	}

	return larkDocComments, nil
}

// processCommentReplies 处理评论的回复
func (s *Service) processCommentReplies(ctx context.Context, comment *larkdrive.FileComment) ([]*agententity.Reply, error) {
	replies := make([]*agententity.Reply, 0)
	if comment.ReplyList == nil || len(comment.ReplyList.Replies) == 0 {
		return replies, nil
	}

	for _, reply := range comment.ReplyList.Replies {
		var replyUsername string
		var err error
		if reply.UserId != nil {
			replyUsername, err = s.getUsernameByID(ctx, *reply.UserId)
			if err != nil {
				log.V1.CtxWarn(ctx, "get lark username by id failed, err: %v", err)
			}
		}

		// 转换回复内容
		replyContent := s.convertReplyContent(ctx, reply.Content, reply.Extra)
		replyExtra := s.convertReplyExtra(reply.Extra)

		replies = append(replies, &agententity.Reply{
			ReplyID:      lo.FromPtrOr(reply.ReplyId, ""),
			CreateAt:     time.Unix(int64(lo.FromPtrOr(reply.CreateTime, 0)), 0),
			UpdateAt:     time.Unix(int64(lo.FromPtrOr(reply.UpdateTime, 0)), 0),
			UserID:       lo.FromPtrOr(reply.UserId, ""),
			Username:     replyUsername,
			ReplyContent: replyContent,
			Extra:        replyExtra,
		})
	}

	return replies, nil
}

// convertReplyContent 转换回复内容
func (s *Service) convertReplyContent(ctx context.Context, content *larkdrive.ReplyContent, extra *larkdrive.ReplyExtra) *agententity.ReplyContent {
	if content == nil {
		return nil
	}

	elements := make([]*agententity.ReplyElement, 0, len(content.Elements))
	for _, item := range content.Elements {
		element := s.convertReplyElement(ctx, item)
		if element != nil {
			elements = append(elements, element)
		}
	}

	// 特殊处理图片
	if extra != nil && len(extra.ImageList) > 0 {
		elements = append(elements, &agententity.ReplyElement{
			Type: "text_run",
			TextRun: &agententity.TextRun{
				Text: "[图片]",
			},
		})
	}

	return &agententity.ReplyContent{Elements: elements}
}

// convertReplyElement 转换回复元素
func (s *Service) convertReplyElement(ctx context.Context, item *larkdrive.ReplyElement) *agententity.ReplyElement {
	if item == nil || item.Type == nil {
		return nil
	}

	// 处理人员引用
	var personInfo *agententity.Person
	if item.Person != nil && item.Person.UserId != nil {
		username, err := s.getUsernameByID(ctx, *item.Person.UserId)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get lark user by id: %v", err)
			return nil
		}
		personInfo = &agententity.Person{
			UserID:   *item.Person.UserId,
			Username: username,
		}
	}

	return &agententity.ReplyElement{
		Type:     *item.Type,
		TextRun:  s.convertTextRun(item.TextRun),
		DocsLink: s.convertDocsLink(item.DocsLink),
		Person:   personInfo,
	}
}

// convertTextRun 转换文本内容
func (s *Service) convertTextRun(textRun *larkdrive.TextRun) *agententity.TextRun {
	if textRun == nil || textRun.Text == nil {
		return nil
	}

	larkCommentsTccConf := s.tccConf.NextLarkCommentsConfig.GetPointer()
	return &agententity.TextRun{Text: html.UnescapeString(*textRun.Text), IsAimeComment: strings.HasPrefix(*textRun.Text, larkCommentsTccConf.AimeCommentPrefix)}
}

// convertDocsLink 转换文档链接
func (s *Service) convertDocsLink(docsLink *larkdrive.DocsLink) *agententity.DocsLink {
	if docsLink == nil || docsLink.Url == nil {
		return nil
	}
	return &agententity.DocsLink{Link: *docsLink.Url}
}

// convertReplyExtra 转换回复额外信息
func (s *Service) convertReplyExtra(extra *larkdrive.ReplyExtra) *agententity.ReplyExtra {
	if extra == nil {
		return nil
	}
	return &agententity.ReplyExtra{ImageList: extra.ImageList}
}

func (s *Service) getSessionLarkDocArtifacts(ctx context.Context, sessionID string) ([]*entity.Artifact, error) {
	artifacts, err := s.dao.ListArtifacts(ctx, dal.ListArtifactsOption{
		SessionID: sessionID,
		Display:   lo.ToPtr(true),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list artifacts")
	}

	artifactMap := make(map[string]*entity.Artifact)
	for _, artifact := range artifacts {
		// 检查是否为飞书文档类型
		if len(artifact.FileMetas) == 0 {
			continue
		}
		isLarkDoc := false
		for _, fileMeta := range artifact.FileMetas {
			if fileMeta.SubType == entity.LinkArtifactKeySourceLarkDoc.String() {
				isLarkDoc = true
				break
			}
		}
		if !isLarkDoc {
			continue
		}

		// 按 ArtifactKey 分组，保留版本最大的
		key := artifact.Key
		if existing, exists := artifactMap[key]; !exists || artifact.Version > existing.Version {
			artifactMap[key] = artifact
		}
	}

	// 将 map 转换为切片返回
	result := make([]*entity.Artifact, 0, len(artifactMap))
	for _, artifact := range artifactMap {
		result = append(result, artifact)
	}
	return result, nil
}

func (s *Service) getUsernameByID(ctx context.Context, userID string) (string, error) {
	// 1. 从数据库中查询 Lark 用户
	larkUser, err := s.dao.GetLarkUserByID(ctx, userID, false)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", errors.WithMessage(err, "failed to get lark user by id")
	}
	if larkUser != nil {
		return larkUser.Username, nil
	}
	// 2. fallback 到 lark api
	info, err := s.larkClient.GetUserInfoById(ctx, userID, "")
	if err != nil {
		return "", errors.WithMessage(err, "failed to get lark user info")
	}
	if info.User == nil || info.User.Email == nil || *info.User.Email == "" {
		return "", errors.New("failed to get lark user info")
	}

	return strings.Split(*info.User.Email, "@")[0], nil
}

func extractLarkDocToken(larkUrl string) string {
	// 定义正则表达式
	//https://bytedance.larkoffice.com/docx/WomSdrxbLoxWgTxav8mcej2XnNh
	re := regexp.MustCompile(`/([^/\\#]+)[\\#]*$`)

	matches := re.FindStringSubmatch(larkUrl)

	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

func (s *Service) CreateFileVersion(ctx context.Context, fileUrl, versionName, fileType string, userName string) (string, error) {
	fileToken := extractLarkDocToken(fileUrl)
	if fileToken == "" {
		return "", errors.New("failed to extract lark doc token")
	}
	versionData, err := s.larkClient.CreateFileVersion(ctx, &lark.CreateFileVersionParams{
		FileToken:   fileToken,
		VersionName: versionName,
		ObjType:     fileType,
	})
	if err != nil {
		return "", errors.WithMessage(err, "failed to create lark doc version")
	}

	return *versionData.Version, nil
}
