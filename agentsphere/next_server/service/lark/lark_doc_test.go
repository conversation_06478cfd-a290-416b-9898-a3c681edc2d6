package lark

import (
	"context"
	"testing"
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	larkMock "code.byted.org/devgpt/kiwis/port/lark/mock"
	"github.com/bytedance/mockey"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"github.com/pkg/errors"
	. "github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

func TestListLarkDocComments(t *testing.T) {
	mockey.PatchConvey("TestListLarkDocComments", t, func() {
		mockey.PatchConvey("测试 ListLarkDocComments 方法", func() {
			ctx := context.Background()
			sessionID := "test-session-id"
			userName := "test-user"

			// 创建 Service 实例
			s := &Service{}

			mockey.PatchConvey("成功获取飞书文档评论", func() {
				// Mock 数据
				mockArtifacts := []*entity.Artifact{
					{
						ID: "artifact-1",
						FileMetas: []entity.FileMeta{
							{
								Name:    "测试文档",
								SubType: entity.LinkArtifactKeySourceLarkDoc.String(),
								Content: `{"url":"https://bytedance.larkoffice.com/docx/WomSdrxbLoxWgTxav8mcej2XnNh"}`},
						},
					},
				}

				mockLarkDocComments := []*agententity.CommentMetadata{
					{
						CommentID: "comment-1",
						Quote:     "引用内容",
						CreateAt:  time.Unix(1640995200, 0),
						UpdateAt:  time.Unix(1640995200, 0),
						UserID:    "user-1",
						Username:  "test-user-1",
						Replies:   []*agententity.Reply{},
					},
				}

				// Mock 方法调用
				mockey.Mock((*Service).getSessionLarkDocArtifacts).To(func(ctx context.Context, sessionID string) ([]*entity.Artifact, error) {
					return mockArtifacts, nil
				}).Build()

				mockey.Mock((*Service).GetLarkFileComments).To(func(ctx context.Context, fileurl string, fileType string) ([]*agententity.CommentMetadata, error) {
					return mockLarkDocComments, nil
				}).Build()

				// 执行测试
				result, err := s.ListLarkDocComments(ctx, sessionID, userName)

				// 验证结果
				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				So(len(result), ShouldEqual, 1)
				So(result[0].ArtifactID, ShouldEqual, "artifact-1")
				So(result[0].Name, ShouldEqual, "测试文档")
				So(result[0].LarkDocUrl, ShouldEqual, "https://bytedance.larkoffice.com/docx/WomSdrxbLoxWgTxav8mcej2XnNh")
				So(len(result[0].Comments), ShouldEqual, 1)
				So(result[0].Comments[0].CommentID, ShouldEqual, "comment-1")
			})

			mockey.PatchConvey("获取 session artifacts 失败", func() {
				mockey.Mock((*Service).getSessionLarkDocArtifacts).To(func(ctx context.Context, sessionID string) ([]*entity.Artifact, error) {
					return nil, errors.New("failed to get artifacts")
				}).Build()

				result, err := s.ListLarkDocComments(ctx, sessionID, userName)

				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "failed to get session artifacts")
				So(result, ShouldBeNil)
			})

			mockey.PatchConvey("处理文档评论失败", func() {
				mockArtifacts := []*entity.Artifact{
					{
						ID: "artifact-1",
						FileMetas: []entity.FileMeta{
							{
								Name:    "测试文档",
								SubType: entity.LinkArtifactKeySourceLarkDoc.String(),
								Content: `{"url":"https://bytedance.larkoffice.com/docx/WomSdrxbLoxWgTxav8mcej2XnNh"}`},
						},
					},
				}

				mockey.Mock((*Service).getSessionLarkDocArtifacts).To(func(ctx context.Context, sessionID string) ([]*entity.Artifact, error) {
					return mockArtifacts, nil
				}).Build()

				mockey.Mock((*Service).GetLarkFileComments).To(func(ctx context.Context, fileurl string, fileType string) ([]*agententity.CommentMetadata, error) {
					return nil, errors.New("failed to process comments")
				}).Build()

				result, err := s.ListLarkDocComments(ctx, sessionID, userName)

				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "failed to process comments")
				So(result, ShouldBeNil)
			})

			mockey.PatchConvey("跳过非飞书文档类型的文件", func() {
				mockArtifacts := []*entity.Artifact{
					{
						ID: "artifact-1",
						FileMetas: []entity.FileMeta{
							{
								Name:    "非飞书文档",
								SubType: "other_type", // 非飞书文档类型
								Content: `{"url":"https://example.com/doc"}`},
						},
					},
				}

				mockey.Mock((*Service).getSessionLarkDocArtifacts).To(func(ctx context.Context, sessionID string) ([]*entity.Artifact, error) {
					return mockArtifacts, nil
				}).Build()

				result, err := s.ListLarkDocComments(ctx, sessionID, userName)

				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				So(len(result), ShouldEqual, 0) // 应该跳过非飞书文档
			})

			mockey.PatchConvey("跳过无效的飞书文档URL", func() {
				mockArtifacts := []*entity.Artifact{
					{
						ID: "artifact-1",
						FileMetas: []entity.FileMeta{
							{
								Name:    "无效URL文档",
								SubType: entity.LinkArtifactKeySourceLarkDoc.String(),
								Content: `{"url":"invalid-url"}`}, // 无效的URL
						},
					},
				}

				mockey.Mock((*Service).getSessionLarkDocArtifacts).To(func(ctx context.Context, sessionID string) ([]*entity.Artifact, error) {
					return mockArtifacts, nil
				}).Build()

				result, err := s.ListLarkDocComments(ctx, sessionID, userName)

				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				So(len(result), ShouldEqual, 1)
				So(len(result[0].Comments), ShouldEqual, 0)
			})

			mockey.PatchConvey("处理多个飞书文档", func() {
				mockArtifacts := []*entity.Artifact{
					{
						ID: "artifact-1",
						FileMetas: []entity.FileMeta{
							{
								Name:    "文档1",
								SubType: entity.LinkArtifactKeySourceLarkDoc.String(),
								Content: `{"url":"https://bytedance.larkoffice.com/docx/doc1"}`},
							{
								Name:    "文档2",
								SubType: entity.LinkArtifactKeySourceLarkDoc.String(),
								Content: `{"url":"https://bytedance.larkoffice.com/docx/doc2"}`},
						},
					},
				}

				mockLarkDocComments := []*agententity.CommentMetadata{
					{
						CommentID: "comment-1",
						Username:  "user1",
					},
				}

				mockey.Mock((*Service).getSessionLarkDocArtifacts).To(func(ctx context.Context, sessionID string) ([]*entity.Artifact, error) {
					return mockArtifacts, nil
				}).Build()

				mockey.Mock((*Service).GetLarkFileComments).To(func(ctx context.Context, fileUrl string, fileType string) ([]*agententity.CommentMetadata, error) {
					return mockLarkDocComments, nil
				}).Build()

				result, err := s.ListLarkDocComments(ctx, sessionID, userName)

				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				So(len(result), ShouldEqual, 2) // 应该处理两个文档
				So(result[0].Name, ShouldEqual, "文档1")
				So(result[1].Name, ShouldEqual, "文档2")
			})
		})
	})
}

func TestCreateFileVersion(t *testing.T) {
	mockey.PatchConvey("TestCreateFileVersion", t, func() {
		mockey.PatchConvey("测试 CreateFileVersion 方法", func() {
			ctx := context.Background()
			versionName := "v1.0.0"
			fileType := "docx"
			userName := "test-user"

			// 创建 Service 实例
			s := &Service{}

			mockey.PatchConvey("提取文档token失败", func() {
				// 直接测试 extractLarkDocToken 函数的行为
				token := extractLarkDocToken("invalid-url")
				So(token, ShouldEqual, "")

				result, err := s.CreateFileVersion(ctx, "invalid-url", versionName, fileType, userName)

				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "failed to extract lark doc token")
				So(result, ShouldEqual, "")
			})
		})
	})
}

func TestGetUsernameByID(t *testing.T) {
	Convey("TestGetUsernameByID", t, func() {
		mockey.PatchConvey("测试 getUsernameByID 方法", func() {
			ctx := context.Background()
			userID := "test-user-id"
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 创建 mock lark client
			mockLarkClient := larkMock.NewMockClient(ctrl)

			// 创建 Service 实例
			s := &Service{
				dao:        &dal.DAO{},
				larkClient: mockLarkClient,
			}

			Convey("数据库查询成功场景", func() {
				mockLarkUser := &entity.LarkUser{
					Username: "test-username",
					Email:    "<EMAIL>",
					OpenID:   userID,
				}

				// Mock DAO 方法
				mockey.Mock((*dal.DAO).GetLarkUserByID).To(func(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
					return mockLarkUser, nil
				}).Build()

				result, err := s.getUsernameByID(ctx, userID)

				So(err, ShouldBeNil)
				So(result, ShouldEqual, "test-username")
			})

			Convey("数据库查询失败但 lark API 成功场景", func() {
				// Mock DAO 返回 RecordNotFound 错误
				mockey.Mock((*dal.DAO).GetLarkUserByID).To(func(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
					return nil, gorm.ErrRecordNotFound
				}).Build()

				// Mock lark client 成功返回用户信息
				mockUserRespData := &larkcontact.GetUserRespData{
					User: &larkcontact.User{
						Email: stringPtr("<EMAIL>"),
					},
				}
				mockLarkClient.EXPECT().GetUserInfoById(ctx, userID, "").Return(mockUserRespData, nil)

				result, err := s.getUsernameByID(ctx, userID)

				So(err, ShouldBeNil)
				So(result, ShouldEqual, "test-user")
			})

			Convey("数据库查询其他错误场景", func() {
				// Mock DAO 返回其他错误
				mockey.Mock((*dal.DAO).GetLarkUserByID).To(func(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
					return nil, errors.New("database connection error")
				}).Build()

				result, err := s.getUsernameByID(ctx, userID)

				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "failed to get lark user by id")
				So(result, ShouldEqual, "")
			})

			Convey("数据库查询失败且 lark API 也失败场景", func() {
				// Mock DAO 返回 RecordNotFound 错误
				mockey.Mock((*dal.DAO).GetLarkUserByID).To(func(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
					return nil, gorm.ErrRecordNotFound
				}).Build()

				// Mock lark client 返回错误
				mockLarkClient.EXPECT().GetUserInfoById(ctx, userID, "").Return(nil, errors.New("lark api error"))

				result, err := s.getUsernameByID(ctx, userID)

				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "failed to get lark user info")
				So(result, ShouldEqual, "")
			})

			Convey("lark API 返回空用户信息场景", func() {
				// Mock DAO 返回 RecordNotFound 错误
				mockey.Mock((*dal.DAO).GetLarkUserByID).To(func(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
					return nil, gorm.ErrRecordNotFound
				}).Build()

				// Mock lark client 返回空用户信息
				mockUserRespData := &larkcontact.GetUserRespData{
					User: nil,
				}
				mockLarkClient.EXPECT().GetUserInfoById(ctx, userID, "").Return(mockUserRespData, nil)

				result, err := s.getUsernameByID(ctx, userID)

				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldEqual, "failed to get lark user info")
				So(result, ShouldEqual, "")
			})

			Convey("lark API 返回空邮箱场景", func() {
				// Mock DAO 返回 RecordNotFound 错误
				mockey.Mock((*dal.DAO).GetLarkUserByID).To(func(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
					return nil, gorm.ErrRecordNotFound
				}).Build()

				// Mock lark client 返回空邮箱
				mockUserRespData := &larkcontact.GetUserRespData{
					User: &larkcontact.User{
						Email: nil,
					},
				}
				mockLarkClient.EXPECT().GetUserInfoById(ctx, userID, "").Return(mockUserRespData, nil)

				result, err := s.getUsernameByID(ctx, userID)

				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldEqual, "failed to get lark user info")
				So(result, ShouldEqual, "")
			})

			Convey("lark API 返回复杂邮箱格式场景", func() {
				// Mock DAO 返回 RecordNotFound 错误
				mockey.Mock((*dal.DAO).GetLarkUserByID).To(func(ctx context.Context, userID string, sync bool) (*entity.LarkUser, error) {
					return nil, gorm.ErrRecordNotFound
				}).Build()

				// Mock lark client 返回复杂邮箱格式
				mockUserRespData := &larkcontact.GetUserRespData{
					User: &larkcontact.User{
						Email: stringPtr("<EMAIL>"),
					},
				}
				mockLarkClient.EXPECT().GetUserInfoById(ctx, userID, "").Return(mockUserRespData, nil)

				result, err := s.getUsernameByID(ctx, userID)

				So(err, ShouldBeNil)
				So(result, ShouldEqual, "complex.user.name")
			})
		})
	})
}

// stringPtr 辅助函数，用于创建字符串指针
func stringPtr(s string) *string {
	return &s
}

func TestExtractLastSegmentFromURL(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		expected string
	}{
		{
			name:     "正常URL",
			url:      "https://bytedance.larkoffice.com/docx/WomSdrxbLoxWgTxav8mcej2XnNh",
			expected: "WomSdrxbLoxWgTxav8mcej2XnNh",
		},
		{
			name:     "带反斜杠结尾的URL",
			url:      "https://bytedance.larkoffice.com/docx/WomSdrxbLoxWgTxav8mcej2XnNh\\",
			expected: "WomSdrxbLoxWgTxav8mcej2XnNh",
		},
		{
			name:     "带井号结尾的URL",
			url:      "https://bytedance.larkoffice.com/docx/WomSdrxbLoxWgTxav8mcej2XnNh#",
			expected: "WomSdrxbLoxWgTxav8mcej2XnNh",
		},
		{
			name:     "不含斜杠的URL",
			url:      "example",
			expected: "",
		},
		{
			name:     "空URL",
			url:      "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := extractLarkDocToken(tt.url)
			if got != tt.expected {
				t.Errorf("extractLastSegmentFromURL() = %v, want %v", got, tt.expected)
			}
		})
	}
}
