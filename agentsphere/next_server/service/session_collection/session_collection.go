package session_collection

import (
	"context"
	"fmt"
	"strings"
	"time"

	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
	poolsdk "github.com/sourcegraph/conc/pool"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type Service struct {
	idGen            uuid.Generator
	dao              *dal.DAO
	tccConf          *config.AgentSphereTCCConfig
	larkClient       lark.Client
	artifact         *artifact.Service
	larkService      *larkservice.Service
	sessionService   *sessionservice.Service
	runtimeAPIConfig config.AgentSphereRuntimeAPIConfig
}

type CreateServiceOption struct {
	fx.In
	DAO              *dal.DAO
	TccConf          *config.AgentSphereTCCConfig
	LarkClient       lark.Client
	Artifact         *artifact.Service
	LarkService      *larkservice.Service
	SessionService   *sessionservice.Service
	RuntimeAPIConfig config.AgentSphereRuntimeAPIConfig
}

func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		idGen:            uuid.GetDefaultGenerator(nil),
		dao:              opt.DAO,
		tccConf:          opt.TccConf,
		artifact:         opt.Artifact,
		larkService:      opt.LarkService,
		larkClient:       opt.LarkClient,
		sessionService:   opt.SessionService,
		runtimeAPIConfig: opt.RuntimeAPIConfig,
	}, nil
}

type CreateSessionCollectionOption struct {
	User             *authentity.Account
	Content          string
	Attachments      []*entity.Attachment
	AgreeHomeDisplay bool
}

func (s *Service) CreateSessionCollection(ctx context.Context, opt CreateSessionCollectionOption) (err error) {
	reportMetricsFunc := metrics.NSM.ReportSessionMetrics()
	defer func() {
		reportMetricsFunc("CreateSessionCollection", err != nil, serverservice.ErrorToErrorReason(err), opt.User.Username)
	}()

	user := opt.User
	creator := user.Username
	creatorEmail := user.Email
	// add db record
	_, err = s.dao.CreateSessionCollection(ctx, dal.CreateSessionCollectionOption{
		ID:               s.idGen.NewID(),
		Creator:          creator,
		CreatorEmail:     creatorEmail,
		Content:          opt.Content,
		AgreeHomeDisplay: opt.AgreeHomeDisplay,
		Attachments:      opt.Attachments,
		Status:           entity.SessionCollectionStatusCreated,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to create session collection")
	}
	return nil
}

type ListSessionCollectionOption struct {
	Creator          *string
	Status           *entity.SessionCollectionStatus
	Content          *string
	Limit            int
	Offset           int
	IDs              []string
	OrderByCreatedAt *bool
	OrderByUpdatedAt *bool
	Order            *entity.Order
}

func (s *Service) ListSessionCollection(ctx context.Context, opt ListSessionCollectionOption) (int64, []*entity.SessionCollection, error) {
	total, sessionCollections, err := s.dao.ListSessionCollections(ctx, dal.ListSessionCollectionsOption{
		Offset: opt.Offset,
		Limit:  opt.Limit,
		ListSessionCollectionsNoLimitOption: dal.ListSessionCollectionsNoLimitOption{
			Creator:          opt.Creator,
			Status:           opt.Status,
			Content:          opt.Content,
			IDs:              opt.IDs,
			OrderByCreatedAt: opt.OrderByCreatedAt,
			OrderByUpdatedAt: opt.OrderByUpdatedAt,
			Order:            opt.Order,
		},
	})
	if err != nil {
		return 0, nil, errors.WithMessage(err, "failed to list sessions")
	}

	// assemble runs
	sessionCollectionIDs := lo.Map(sessionCollections, func(sessionCollectionItem *entity.SessionCollection, index int) string {
		return sessionCollectionItem.ID
	})
	sessionCollectionRunsMap, err := s.dao.MGetSessionCollectionRunByCollectionIDs(ctx, sessionCollectionIDs)
	if err != nil {
		return 0, nil, errors.WithMessage(err, "failed to get mget session collection runs")
	}
	for _, sessionCollection := range sessionCollections {
		if sessionCollection == nil {
			continue
		}
		runList, exist := sessionCollectionRunsMap[sessionCollection.ID]
		if exist {
			sessionCollection.RunList = runList
		}
	}
	return total, sessionCollections, nil
}

func (s *Service) RunSessionCollectionOnce(ctx context.Context, account *authentity.Account, sessionCollection *entity.SessionCollection, role entity.SessionRole) error {
	if account == nil {
		return errors.New("account is nil")
	}
	if sessionCollection == nil {
		return errors.New("session collection is nil")
	}
	// step1: check collection status
	//if sessionCollection.Status == entity.SessionCollectionStatusRunning {
	//	logs.V1.CtxWarn(ctx, "session collection is running, not need to run")
	//	return nil
	//}
	if sessionCollection.Status == entity.SessionCollectionStatusNoticed {
		logs.V1.CtxWarn(ctx, "session collection is noticed, not need to run")
		return nil
	}

	// step2: update collection to running
	err := s.dao.UpdateSessionCollection(ctx, sessionCollection.ID, dal.UpdateSessionCollectionOption{
		Status: lo.ToPtr(entity.SessionCollectionStatusRunning),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update session collection")
	}

	// step3: create new session
	session, err := s.sessionService.CreateSession(ctx, sessionservice.CreateSessionOption{
		User: account,
		Context: entity.SessionContext{
			UseInternalTool: lo.ToPtr(true), // 采集任务开启内场工具
		},
		SkipMaxRunningAgentsLimit: true,
		Role:                      lo.ToPtr(role),
		Source:                    entity.SessionSourcePersonal,
	})
	if err != nil {
		return errors.WithMessage(err, "create session failed")
	}

	// step4: create session collection run
	_, err = s.dao.CreateSessionCollectionRun(ctx, dal.CreateSessionCollectionRunOption{
		ID:                  s.idGen.NewID(),
		SessionID:           session.ID,
		SessionCollectionID: sessionCollection.ID,
		Creator:             account.Username,
		SessionRole:         lo.ToPtr(role),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to create session collection run")
	}

	// step5: copy attachment to new artifact
	copyAttachments := make([]*entity.Attachment, 0)
	for _, oldAttachment := range sessionCollection.Attachments {
		if oldAttachment == nil {
			continue
		}
		newAttachment, err := s.artifact.CopyToNewArtifact(ctx, artifact.CopyToNewArtifactOption{
			OldArtifactID: oldAttachment.ID,
			NewSessionID:  session.ID,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to copy to new artifact")
		}
		copyAttachments = append(copyAttachments, &entity.Attachment{
			ID:       newAttachment.ID,
			FileName: oldAttachment.FileName,
		})
	}

	// step6: create message run
	_, err = s.sessionService.CreateMessage(ctx, sessionservice.CreateMessageOption{
		SessionID: session.ID,
		Role:      entity.MessageRoleUser,
		Content: entity.MessageContent{
			Content: sessionCollection.Content,
		},
		User:        account,
		Attachments: copyAttachments},
	)
	if err != nil {
		return errors.WithMessage(err, "failed to create message")
	}
	return nil
}

type SessionCollectionRunResult struct {
	SessionCollectionID string
	Success             bool
}

func (s *Service) RunSessionCollections(ctx context.Context, account *authentity.Account, sessionCollectionIDs []string, role entity.SessionRole) ([]*SessionCollectionRunResult, error) {
	sessionCollections, err := s.dao.GetSessionCollections(ctx, sessionCollectionIDs)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to run session collections")
	}

	nextAgentSessionCollectionConfig := s.tccConf.NextAgentSessionCollectionConfig.GetValue()
	if nextAgentSessionCollectionConfig.MaxRunning > 0 {
		// check max running
		total, err := s.dao.CountSessionCollections(ctx, dal.CountSessionCollectionsOption{
			Status: lo.ToPtr(entity.SessionCollectionStatusRunning),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to count session collections")
		}
		logs.V1.CtxInfo(ctx, "total running session collections: %d, max running session: %d", total, nextAgentSessionCollectionConfig.MaxRunning)
		if int(total) >= nextAgentSessionCollectionConfig.MaxRunning {
			return nil, serverservice.ErrMaximumRunningSessionCollectionReached
		}
	}

	// run session collections with parallel
	parallelPool := poolsdk.NewWithResults[*SessionCollectionRunResult]().WithMaxGoroutines(10)
	for _, sessionCollection := range sessionCollections {
		if sessionCollection == nil {
			continue
		}
		if sessionCollection.Status == entity.SessionCollectionStatusClosed {
			logs.V1.CtxInfo(ctx, "session collection is closed, id: %s", sessionCollection.ID)
			continue
		}
		sessionCollection := sessionCollection

		parallelPool.Go(func() *SessionCollectionRunResult {
			sessionCollectionRunResult := &SessionCollectionRunResult{
				SessionCollectionID: sessionCollection.ID,
				Success:             true,
			}
			runErr := s.RunSessionCollectionOnce(ctx, account, sessionCollection, role)
			if runErr != nil {
				log.V1.CtxError(ctx, "failed to run session collection for %s: %+v", sessionCollection.ID, runErr)
				sessionCollectionRunResult.Success = false
			}
			return sessionCollectionRunResult
		})
	}
	runResults := parallelPool.Wait()
	return runResults, nil
}

type SessionCollectionRunNotification struct {
	SessionCollectionRunID string
	NoticeWay              entity.SessionCollectionRunNoticeWay
}

func (s *Service) SendSessionCollectionRunNotification(
	ctx context.Context, notifications []*SessionCollectionRunNotification,
) error {
	// step1: get session collection run list
	sessionCollectionRunIDs := lo.Map(notifications, func(notification *SessionCollectionRunNotification, _ int) string {
		return notification.SessionCollectionRunID
	})
	sessionCollectionRuns, err := s.dao.MGetSessionCollectionRun(ctx, sessionCollectionRunIDs)
	if err != nil {
		return errors.WithMessage(err, "failed to get session collection run list")
	}

	// step2: get session collection list
	sessionCollectionIDs := make([]string, 0)
	for _, sessionCollectionRun := range sessionCollectionRuns {
		if sessionCollectionRun == nil {
			continue
		}
		sessionCollectionIDs = append(sessionCollectionIDs, sessionCollectionRun.SessionCollectionID)
	}
	sessionCollections, err := s.dao.MGetSessionCollection(ctx, sessionCollectionIDs)
	if err != nil {
		return errors.WithMessage(err, "failed to multi get session collections")
	}

	// step3: get session collection user email and lark user id
	emails := lo.MapToSlice(sessionCollections, func(sessionCollectionID string, sessionCollection *entity.SessionCollection) string {
		return sessionCollection.GetCreatorEmail()
	})
	userIDMap, err := s.larkClient.MGetUserIDByEmail(ctx, emails, lark.UserIDTypeUserID, true)
	if err != nil {
		return errors.WithMessage(err, "failed to multi get user id by email")
	}

	// step4: send lark message
	domain := s.tccConf.NextAgentSessionCollectionConfig.GetValue().Domain
	for _, notification := range notifications {
		if notification == nil {
			continue
		}
		sessionCollectionRun := sessionCollectionRuns[notification.SessionCollectionRunID]
		if sessionCollectionRun == nil {
			continue
		}

		// If session collection run is noticed, not need to notice
		if sessionCollectionRun.Noticed {
			logs.V1.CtxWarn(ctx, "session collection run:%s is noticed, not need to notice", sessionCollectionRun.ID)
			continue
		}
		if sessionCollectionRun.ReplayID == "" {
			logs.V1.CtxWarn(ctx, "session collection run:%s replay not generated, not need to notice", sessionCollectionRun.ID)
			continue
		}

		// If session collection not exist, not need to notice
		sessionCollection, exist := sessionCollections[sessionCollectionRun.SessionCollectionID]
		if !exist {
			logs.V1.CtxWarn(ctx, "session collection:%s not found, not need to notice", sessionCollection.ID)
			continue
		}

		// Send lark message
		creatorEmail := sessionCollection.GetCreatorEmail()
		userID, ok := userIDMap[creatorEmail]
		if !ok {
			logs.V1.CtxError(ctx, "user not found, email:%s", creatorEmail)
			continue
		}
		replayLink := fmt.Sprintf("https://%s/share/%s", domain, sessionCollectionRun.ReplayID)

		var toLarkMessageType string
		switch notification.NoticeWay {
		case entity.SessionCollectionRunNoticeWayUser:
			toLarkMessageType = larkservice.LarkMessageToTypeUser
		case entity.SessionCollectionRunNoticeWayGroup:
			toLarkMessageType = larkservice.LarkMessageToTypeGroup
		default:
			logs.V1.CtxError(ctx, "unknown notice way, notification:%+v", notification)
			continue
		}
		err = s.larkService.InnerSendLarkReplayLinkMessage(ctx, userID, creatorEmail, strings.TrimSpace(sessionCollection.Content), replayLink, toLarkMessageType)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to send lark replay link message. err: %s", err)
			continue
		}

		// update session_run notice way
		err = s.dao.UpdateSessionCollectionRun(ctx, sessionCollectionRun.ID, dal.UpdateSessionCollectionRunOption{
			Noticed:   lo.ToPtr(true),
			NoticeWay: lo.ToPtr(notification.NoticeWay),
		})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to update session collection run:%s. err:%s", sessionCollectionRun.ID, err)
			continue
		}

		// update session collection noticed
		err = s.dao.UpdateSessionCollection(ctx, sessionCollectionRun.SessionCollectionID, dal.UpdateSessionCollectionOption{
			Status: lo.ToPtr(entity.SessionCollectionStatusNoticed),
		})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to update session collection:%s. err: %s", sessionCollectionRun.SessionCollectionID, err)
			continue
		}
		logs.V1.CtxInfo(ctx, "success to send lark replay link message. session_collection_run_id:%s, email:%s, msg_type:%s", sessionCollectionRun.ID, creatorEmail, toLarkMessageType)
	}
	return nil
}

type DownloadSessionCollectionsOption struct {
	Creator          *string
	Status           *entity.SessionCollectionStatus
	Content          *string
	IDs              []string
	OrderByCreatedAt *bool
	OrderByUpdatedAt *bool
	Order            *entity.Order
}

func (s *Service) DownloadSessionCollections(ctx context.Context, opt DownloadSessionCollectionsOption, account *authentity.Account) (string, error) {
	var (
		sessionCollections    []*entity.SessionCollection
		sessionCollectionsErr error
		createSheetResp       *larksheets.CreateSpreadsheetRespData
		sheetErr              error
		sheetID               string
	)
	nextAgentSessionCollectionConfig := s.tccConf.NextAgentSessionCollectionConfig.GetValue()
	downloadPool := poolsdk.New().WithMaxGoroutines(2)
	downloadPool.Go(func() {
		sessionCollections, sessionCollectionsErr = s.dao.ListSessionCollectionsNoLimit(ctx, dal.ListSessionCollectionsNoLimitOption{
			Creator:          opt.Creator,
			Status:           opt.Status,
			Content:          opt.Content,
			IDs:              opt.IDs,
			OrderByCreatedAt: opt.OrderByCreatedAt,
			OrderByUpdatedAt: opt.OrderByUpdatedAt,
			Order:            opt.Order,
		})
		if sessionCollectionsErr != nil || len(sessionCollections) == 0 {
			return
		}
		if nextAgentSessionCollectionConfig.DownloadSheetMaxRows != 0 && len(sessionCollections) > nextAgentSessionCollectionConfig.DownloadSheetMaxRows {
			sessionCollectionsErr = serverservice.ErrorSessionCollectionExceededLineLimit
			return
		}
		sessionCollectionIDs := lo.Map(sessionCollections, func(sessionCollectionItem *entity.SessionCollection, index int) string {
			return sessionCollectionItem.ID
		})
		var sessionCollectionRunsMap map[string][]*entity.SessionCollectionRun
		sessionCollectionRunsMap, sessionCollectionsErr = s.dao.MGetSessionCollectionRunByCollectionIDs(ctx, sessionCollectionIDs)
		if sessionCollectionsErr != nil {
			return
		}
		for _, sessionCollection := range sessionCollections {
			if sessionCollection == nil {
				continue
			}
			runList, exist := sessionCollectionRunsMap[sessionCollection.ID]
			if exist {
				sessionCollection.RunList = runList
			}
		}
	})
	downloadPool.Go(func() {
		createSheetResp, sheetErr = s.larkClient.CreateSheet(ctx, downloadTitle(opt), "")
		if sheetErr != nil {
			return
		}
		if createSheetResp.Spreadsheet == nil {
			sheetErr = errors.New("create sheet resp date is nil")
			return
		}
		subIDResp, err := s.larkClient.GetSheetsSubIDs(ctx, lo.FromPtr(createSheetResp.Spreadsheet.SpreadsheetToken), "")
		if err != nil {
			sheetErr = err
			return
		}
		if subIDResp.Data == nil || subIDResp.Data.Sheets == nil || len(subIDResp.Data.Sheets) == 0 {
			sheetErr = errors.New("sub id resp sheets is nil")
			return
		}
		sheetID = lo.FromPtr(subIDResp.Data.Sheets[0].SheetId)
	})
	downloadPool.Wait()
	if sessionCollectionsErr != nil {
		return "", errors.WithMessage(sessionCollectionsErr, "failed to list sessions")
	}
	if sheetErr != nil {
		return "", sheetErr
	}
	if len(sessionCollections) == 0 {
		return "", serverservice.ErrorSessionCollectionNotFound
	}
	valueRange := fmt.Sprintf("%v", sheetID)
	rowsValues := [][]interface{}{
		{"Query ID", "邮箱前缀", "任务内容", "创建时间", "完成时间", "运行耗时(分)", "任务状态", "会话链接", "会话回放链接"},
	}
	for _, item := range sessionCollections {
		content := item.Content
		rs := []rune(content)
		if len(rs) > 40000 { // 飞书单次写入上限50000, 推荐截断40000
			content = string(rs[:40000]) + "..."
		}
		tmp := []interface{}{
			item.ID, item.Creator, content, item.CreatedAt.Format(time.DateTime), "", "", item.Status.DisplayString(), "", "",
		}
		runLen := len(item.RunList)
		if runLen > 0 {
			if item.RunList[runLen-1].FinishedAt != nil {
				tmp[4] = item.RunList[runLen-1].FinishedAt.Format(time.DateTime)
				tmp[5] = fmt.Sprintf("%.2f", item.RunList[runLen-1].FinishedAt.Sub(item.RunList[runLen-1].CreatedAt).Minutes())
			}
			tmp[7] = fmt.Sprintf("%s/chat/%s", s.runtimeAPIConfig.APIBaseURL, item.RunList[runLen-1].SessionID)
			if item.RunList[runLen-1].ReplayID != "" {
				tmp[8] = fmt.Sprintf("%s/share/%s", s.runtimeAPIConfig.APIBaseURL, item.RunList[runLen-1].ReplayID)
			}
		}

		rowsValues = append(rowsValues, tmp)
	}
	for begin := 0; begin < len(rowsValues); {
		end := len(rowsValues)
		if begin+5000 < len(rowsValues) { // 飞书单次写入上限5000
			end = begin + 5000
		}
		_, err := s.larkClient.AddRowForLarkSheet(ctx, lo.FromPtr(createSheetResp.Spreadsheet.SpreadsheetToken), valueRange, rowsValues[begin:end])
		if err != nil {
			logs.V1.CtxError(ctx, "failed to add row for lark sheet. err:%s", err.Error())
			return "", errors.WithMessage(err, "failed to add row for lark sheet")
		}
		begin = end
	}
	req := larkdrive.NewTransferOwnerPermissionMemberReqBuilder().
		Token(lo.FromPtr(createSheetResp.Spreadsheet.SpreadsheetToken)).
		Type("sheet").
		NeedNotification(false).
		RemoveOldOwner(false).
		StayPut(true).
		Owner(larkdrive.NewOwnerBuilder().
			MemberType("email").
			MemberId(account.Username + "@bytedance.com").
			Build()).
		Build()
	_, err := s.larkClient.TransferOwnerPermissionMember(ctx, req, "")
	if err != nil {
		log.V1.CtxError(ctx, "failed to transfer owner permission: %v", err)
		return "", err
	}
	log.V1.CtxInfo(ctx, "download success, url:%s", lo.FromPtr(createSheetResp.Spreadsheet.Url))
	return lo.FromPtr(createSheetResp.Spreadsheet.Url), nil
}

func downloadTitle(opt DownloadSessionCollectionsOption) string {
	now := time.Now().Format(time.DateTime)
	var titleBuild strings.Builder
	titleBuild.WriteString("内测任务集")
	if opt.Creator != nil {
		titleBuild.WriteString(fmt.Sprintf("-%s", *opt.Creator))
	}
	if opt.Status != nil {
		titleBuild.WriteString(fmt.Sprintf("-%s", *opt.Status))
	}
	if opt.Content != nil {
		titleBuild.WriteString(fmt.Sprintf("-%s", *opt.Content))
	}
	titleBuild.WriteString(fmt.Sprintf("-%s", now))
	return titleBuild.String()
}

func (s *Service) CloseSessionCollections(ctx context.Context, ids []string) error {

	err := s.dao.BatchUpdateSessionCollections(ctx, ids, dal.BatchUpdateSessionCollectionsOption{
		Status: lo.ToPtr(entity.SessionCollectionStatusClosed),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to close session collections")
	}
	return nil
}

func (s *Service) GetNotificationTemplates() ([]*config.LarkNotificationTemplate, error) {
	nextAgentSessionCollectionConfig := s.tccConf.NextAgentSessionCollectionConfig.GetValue()
	if nextAgentSessionCollectionConfig.LarkNotificationTemplates != nil {
		return nextAgentSessionCollectionConfig.LarkNotificationTemplates, nil
	}
	return nil, errors.New("failed to find next agent session collection config")
}

type LarkNotificationTemplate struct {
	Title   string
	Content string
}

func (s *Service) SendSessionCollectionNotification(
	ctx context.Context, ids []string, template LarkNotificationTemplate,
) error {
	sessionCollections, err := s.dao.MGetSessionCollection(ctx, ids)
	if err != nil {
		return errors.WithMessage(err, "failed to multi get session collections")
	}

	for _, sessionCollection := range sessionCollections {
		if sessionCollection.Status == entity.SessionCollectionStatusNoticed {
			continue
		}

		creatorEmail := sessionCollection.GetCreatorEmail()
		err = s.larkService.SendNoAutoSendNotificationCard(ctx, creatorEmail, strings.TrimSpace(sessionCollection.Content), "", template.Title, template.Content)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to send lark replay link message. err: %s", err)
			continue
		}

		// update session collection noticed
		err = s.dao.UpdateSessionCollection(ctx, sessionCollection.ID, dal.UpdateSessionCollectionOption{
			Status: lo.ToPtr(entity.SessionCollectionStatusNoticed),
		})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to update session collection:%s. err: %s", sessionCollection.ID, err)
			continue
		}
		logs.V1.CtxInfo(ctx, "success to send lark. session_collection_id:%s, email:%s", sessionCollection.ID, creatorEmail)
	}
	return nil
}
