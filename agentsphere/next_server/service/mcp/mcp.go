package mcp

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"sync"

	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
	"github.com/sourcegraph/conc/panics"
	"github.com/spf13/cast"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	runtimeserver "code.byted.org/devgpt/kiwis/agentsphere/runtime_server/client"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime_server/model"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextruntime"
	"code.byted.org/devgpt/kiwis/api/idl/kitex_gen/common"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/cloud_mcp"

	mcptool "code.byted.org/devgpt/kiwis/agentsphere/agents/actions/mcp"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	"code.byted.org/devgpt/kiwis/lib/uuid"
)

// Service 是 MCP 服务接口
type Service interface {
	// CreateMCP 创建 MCP 工具
	CreateMCP(ctx context.Context, opt *CreateMCPOption) (*entity.MCP, error)
	// UpdateMCP 更新 MCP 工具
	UpdateMCP(ctx context.Context, opt *UpdateMCPOption) (*entity.MCP, error)
	// ListMCP 列出 MCP 工具
	ListMCP(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int64, error)
	// ListSpaceMCP 列出空间下的 MCP 工具
	ListSpaceMCP(ctx context.Context, opt *ListSpaceMCPOption) ([]*entity.MCP, int64, error)
	// CountSpaceMCP 统计空间下的 MCP 工具
	CountSpaceMCP(ctx context.Context, opt *ListSpaceMCPOption) (*entity.MCPCount, error)
	// GetMCPsByKeys 根据MCPKeys获取MCP工具列表
	GetMCPsByKeys(ctx context.Context, opt *GetMCPsByKeysOption) ([]*entity.MCP, error)
	// UpdateUserMCP 修改用户在指定空间中某个MCP工具的激活状态和个性化配置（如命令参数、环境变量等）
	UpdateUserMCP(ctx context.Context, mcpID entity.MCPKey, userConfigQuery UserScopedMCPQuery, status *entity.ActiveStatus, userConfig *entity.MCPConfig, accessContext *MCPAccessContext) (*entity.MCP, error)
	// GetMCPByID 根据ID获取MCP工具
	GetMCPByID(ctx context.Context, id string, source entity.MCPSource) (*entity.MCP, error)
	// ValidateMCP 验证MCP工具配置是否有效，仅校验个人来源
	ValidateMCP(ctx context.Context, opt *MCPAccessContext) *ValidateMCPResult
	BatchValidateMCP(ctx context.Context, opt *BatchMCPAccessContext) []*ValidateMCPResult
	// ValidateMCPOnlyConfig 同ValidateMCP，仅传输校验配置
	ValidateMCPOnlyConfig(ctx context.Context, opt *MCPConfigAccessContext) *ValidateMCPResult
	// 获取 MCP 工具列表
	ListMCPTools(ctx context.Context, opt *MCPAccessContext) *ListMCPToolsResult
	// 批量获取 多个MCP 工具列表
	BatchListMCPTools(ctx context.Context, opt *BatchMCPAccessContext) []*ListMCPToolsResult
	// CreateCloudMCPAuth 创建字节云MCP的PSM访问方式授权工单
	CreateCloudMCPAuth(ctx context.Context, userToken, psm string) (string, error)
	TrySetMCPPermissions(ctx context.Context, user *authentity.Account, spaceID string, mcps []*entity.MCP)
}

var (
	// ErrMCPNotFound 表示 MCP 服务不存在
	ErrMCPNotFound = errors.New("mcp not found")
	// ErrMCPUnavailable 表示 MCP 服务不可用
	ErrMCPUnavailable = errors.New("mcp server is unavailable")
	// ErrMCPActiveLimit 表示命中mcp激活上限
	ErrMCPActiveLimit = errors.New("the number of activated mcp services exceeds the limit")
	// ErrMCPConfigUnavailable 表示当前的用户配置有误，MCP无法校验通过
	ErrMCPConfigUnavailable = errors.New("the user configuration is unavailable or invalid for the specified MCP")
)

var (
	aimePSMs = []string{"flow.agentsphere.nextserver", "flow.agentsphere.runtime"}
)

const (
	figmaMCPID = "figma_mcp"
)

// ServiceImpl 是 MCP 服务的实现
type ServiceImpl struct {
	dao               *dal.DAO
	idGen             uuid.Generator
	spaceService      *spaceservice.Service
	permissionService *permissionservice.Service
	runtimeCli        *runtimeserver.RuntimeServerClient
	cloudMCPAuthCli   *cloud_mcp.CloudMCPClient
}

// NewService 创建一个新的 MCP 服务实例
func NewService(dao *dal.DAO, spaceService *spaceservice.Service, permissionService *permissionservice.Service,
	cli *runtimeserver.RuntimeServerClient, cloudMCPAuthCli *cloud_mcp.CloudMCPClient) Service {
	return &ServiceImpl{
		dao:               dao,
		idGen:             uuid.GetDefaultGenerator(nil),
		spaceService:      spaceService,
		permissionService: permissionService,
		runtimeCli:        cli,
		cloudMCPAuthCli:   cloudMCPAuthCli,
	}
}

// CreateMCP 创建 MCP 工具
func (s *ServiceImpl) CreateMCP(ctx context.Context, opt *CreateMCPOption) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] CreateMCP request: %#v", opt)
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.Creator)
	if err != nil {
		return nil, err
	}
	// 将请求转换为 MCP 实体
	mcp := &entity.MCP{
		MCPKey: entity.MCPKey{
			MCPID:  gptr.Indirect(opt.UID),
			Source: opt.Source,
		},
		Name:          opt.Name,
		EnName:        opt.EnName,
		Description:   opt.Description,
		EnDescription: opt.EnDescription,
		IconURL:       opt.IconURL,
		Config:        *opt.Config,
		Creator:       opt.Creator,
		Type:          opt.Type,
		ForceActive:   opt.ForceActive,
		SessionRoles:  opt.SessionRoles,
		SourceSpaceID: spaceID,
		Scope:         opt.Scope,
		DefaultActive: opt.DefaultActive,
	}
	// 如果uid为空
	if len(mcp.MCPID) == 0 {
		mcp.MCPID = s.idGen.NewID()
	}

	// 仅校验个人来源
	if opt.Source == entity.MCPSourceUserDefine {
		// 校验mcp是否可用
		valid := s.ValidateMCP(ctx, &MCPAccessContext{
			MCP:            mcp,
			Token:          opt.Token,
			LarkUserOpenID: opt.LarkUserOpenID,
		})
		if valid == nil || !valid.Valid {
			return nil, ErrMCPUnavailable
		}
	}

	// 校验可见范围
	var (
		status        entity.ResourceStatus
		isSpacePublic *bool
	)
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{
		SpaceID: spaceID,
		Sync:    true,
	})
	if err != nil {
		return nil, err
	}
	if space.Type.IsPersonal() {
		mcp.Scope = lo.Ternary(opt.Scope == entity.MCPScopePublic, entity.MCPScopePublic, entity.MCPScopePrivate)
		status = lo.Ternary(opt.Scope == entity.MCPScopePublic, entity.ResourceStatusPublic, entity.ResourceStatusPrivate)
	} else { // 项目空间内单独处理可见范围
		mcp.Scope = lo.Ternary(opt.Scope == entity.MCPScopeProjectPublic, entity.MCPScopeProjectPublic, entity.MCPScopePrivate)
		status = entity.ResourceStatusPrivate
		if opt.Scope == entity.MCPScopeProjectPublic {
			isSpacePublic = lo.ToPtr(true)
		}
	}
	// 调用 DAL 层创建 MCP
	createdMCP, err := s.dao.CreateMCP(ctx, mcp)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] CreateMCP error: %v", err)
		return nil, errors.Wrap(err, "failed to create mcp")
	}
	_, err = s.permissionService.CreateResource(ctx, permissionservice.CreateResourceOption{
		Owner:         opt.Creator,
		Type:          entity.ResourceTypeMCP,
		ExternalID:    createdMCP.MCPID,
		Status:        &status,
		GroupID:       &spaceID,
		IsSpacePublic: isSpacePublic,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create resource for mcp")
	}
	createdMCP.Permissions = []entity.PermissionAction{entity.PermissionActionMCPRead,
		entity.PermissionActionMCPUpdate, entity.PermissionActionMCPDelete}
	log.V1.CtxInfo(ctx, "[MCP] CreateMCP success: %s", createdMCP.MCPID)
	return createdMCP, nil
}

// UpdateMCP 更新 MCP 工具
func (s *ServiceImpl) UpdateMCP(ctx context.Context, opt *UpdateMCPOption) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] UpdateMCP request: %#v", opt)

	// 检查 MCP 是否存在
	existingMCP, err := s.dao.GetMCPByID(ctx, opt.MCPID, opt.Source, nil)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetMCPByID error: %v", err)
		return nil, errors.Wrap(err, "failed to get mcp")
	}

	if existingMCP == nil {
		log.V1.CtxError(ctx, "[MCP] MCP not found: %s, source: %d", opt.MCPID, opt.Source)
		return nil, ErrMCPNotFound
	}

	// 仅校验个人来源
	if opt.Source == entity.MCPSourceUserDefine {
		tempValidateMCP := opt.FromMCP(existingMCP)
		// 校验mcp是否可用
		valid := s.ValidateMCP(ctx, &MCPAccessContext{
			MCP:            tempValidateMCP,
			Token:          opt.Token,
			LarkUserOpenID: opt.LarkUserOpenID,
		})
		if valid == nil || !valid.Valid {
			return nil, ErrMCPUnavailable
		}
	}

	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, existingMCP.SourceSpaceID, opt.Username)
	if err != nil {
		return nil, err
	}
	// 构造DAL层更新选项
	dalOpt := &dal.UpdateMCPOption{
		MCPID:         opt.MCPID,
		Source:        opt.Source,
		Name:          opt.Name,
		EnName:        opt.EnName,
		Description:   opt.Description,
		EnDescription: opt.EnDescription,
		IconURL:       opt.IconURL,
		Type:          opt.Type,
		ForceActive:   opt.ForceActive,
		Config:        opt.Config,
		SessionRoles:  opt.SessionRoles,
		Scope:         opt.Scope,
		DefaultActive: opt.DefaultActive,
	}
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID})
	if err != nil {
		return nil, err
	}
	// 项目空间内不允许设置为全局公开
	if existingMCP.Scope != opt.Scope && opt.Scope == entity.MCPScopePublic && space.Type.IsProject() {
		return nil, serverservice.ErrNotAllowedSetToPublic
	}

	// 调用 DAL 层更新 MCP
	err = s.dao.UpdateMCP(ctx, dalOpt)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] UpdateMCP error: %v", err)
		if err.Error() == "mcp not found" {
			return nil, ErrMCPNotFound
		}
		return nil, errors.Wrap(err, "failed to update mcp")
	}

	// 使用FromMCP创建更新后的MCP实体并返回，无需再次查询数据库
	updatedMCP := opt.FromMCP(existingMCP)
	// 更新MCP资源和权限
	if existingMCP.Scope != opt.Scope {
		var o = permissionservice.UpdateResourceOptions{
			ResourceID:         nil,
			ResourceExternalID: &existingMCP.MCPID,
			ResourceType:       entity.ResourceTypeMCP.Ptr(),
			Owner:              nil,
			Status:             nil,
			IsSpacePublic:      nil,
		}
		if space.Type.IsProject() {
			o.IsSpacePublic = lo.ToPtr(lo.Ternary(opt.Scope == entity.MCPScopeProjectPublic, true, false))
		} else {
			o.Status = lo.ToPtr(lo.Ternary(opt.Scope == entity.MCPScopePublic, entity.ResourceStatusPublic, entity.ResourceStatusPrivate))
		}
		// 更新资源及权限
		_, err = s.permissionService.UpdateResource(ctx, o)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update template resource")
		}
	}
	updatedMCP.Permissions = []entity.PermissionAction{entity.PermissionActionMCPRead,
		entity.PermissionActionMCPUpdate, entity.PermissionActionMCPDelete}
	log.V1.CtxInfo(ctx, "[MCP] UpdateMCP success: %s", opt.MCPID)
	return updatedMCP, nil
}

// ListMCP 列出 MCP 工具
func (s *ServiceImpl) ListMCP(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int64, error) {
	log.V1.CtxInfo(ctx, "[MCP] ListMCP request: %#v", opt)

	// 1. 处理筛选条件，分别查询公共和个人MCP并合并结果
	mcps, total, err := s.queryAllMCPs(ctx, opt)
	if err != nil {
		return nil, 0, err
	}

	// 2. 设置激活状态
	if opt.Creator != nil {
		mcps, err = s.setUserMCPConfig(ctx, mcps, UserScopedMCPQuery{
			Username: *opt.Creator,
			SpaceID:  "",
		})
		if err != nil {
			return nil, 0, err
		}
	}

	// 3. 根据IsActive过滤结果
	if opt.IsActive != nil {
		filteredMCPs := make([]*entity.MCP, 0, len(mcps))
		filterValue := *opt.IsActive
		for _, mcp := range mcps {
			// 只保留激活状态与过滤条件匹配的MCP
			if mcp.IsActive == filterValue {
				filteredMCPs = append(filteredMCPs, mcp)
			}
		}
		// 更新结果集合和总数
		mcps = filteredMCPs
		total = int64(len(filteredMCPs))
		log.V1.CtxInfo(ctx, "[MCP] Filtered by IsActive=%v: %d items remaining", filterValue, total)
	}

	// 4. 根据SessionRole过滤结果 (二期新增) - Service层过滤
	if opt.SessionRole != nil {
		filteredMCPs := entity.FilterMCPsBySessionRole(mcps, *opt.SessionRole)
		mcps = filteredMCPs
		total = int64(len(filteredMCPs))
		log.V1.CtxInfo(ctx, "[MCP] Filtered by SessionRole=%v: %d items remaining", *opt.SessionRole, total)
	}

	// 5. 在Service层进行排序
	s.sortMCPsByPriority(mcps, -1, 0)

	log.V1.CtxInfo(ctx, "[MCP] ListMCP success: total=%d", total)
	return mcps, total, nil
}

// queryAllMCPs 查询所有满足条件的MCP，确保个人MCP只返回当前用户创建的
func (s *ServiceImpl) queryAllMCPs(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int64, error) {
	// 查询公共MCP
	publicMCPs, publicTotal, err := s.queryPublicMCPs(ctx, opt)
	if err != nil {
		return nil, 0, err
	}

	// 查询个人MCP（如果有创建者信息）
	var personalMCPs []*entity.MCP
	var personalTotal int64
	if opt.Creator != nil {
		personalMCPs, personalTotal, err = s.queryPersonalMCPs(ctx, opt, *opt.Creator)
		if err != nil {
			return nil, 0, err
		}
	}

	// 合并结果
	allMCPs := append(publicMCPs, personalMCPs...)
	totalCount := publicTotal + personalTotal

	log.V1.CtxInfo(ctx, "[MCP] Query result: total=%d (public=%d, personal=%d)",
		totalCount, publicTotal, personalTotal)
	return allMCPs, totalCount, nil
}

// queryPublicMCPs 查询公共MCP（非个人创建）
func (s *ServiceImpl) queryPublicMCPs(ctx context.Context, opt *ListMCPOption) ([]*entity.MCP, int64, error) {
	// 确定要查询的公共来源
	var publicSources []entity.MCPSource

	if len(opt.Sources) == 0 {
		// 如果未指定来源，默认查询所有公共来源（除个人外）
		publicSources = []entity.MCPSource{
			entity.MCPSourceAIME,
			entity.MCPSourceCloud,
			// 可添加其他非个人来源
		}
	} else {
		// 从指定来源中筛选出公共来源
		for _, source := range opt.Sources {
			if source != entity.MCPSourceUserDefine {
				publicSources = append(publicSources, source)
			}
		}
	}

	// 如果没有公共来源要查询，直接返回空结果
	if len(publicSources) == 0 {
		return []*entity.MCP{}, 0, nil
	}

	// 构建查询选项
	listOpt := &dal.ListMCPsOption{
		Name:    opt.Name,
		Sources: publicSources,
		Types:   opt.Types,
		// 公共MCP不需要指定Creator
		// SessionRole过滤移到Service层处理
	}

	// 执行查询
	mcps, total, err := s.dao.ListMCPs(ctx, listOpt)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ListMCPs public sources error: %v", err)
		return nil, 0, errors.Wrap(err, "failed to list public mcps")
	}

	log.V1.CtxInfo(ctx, "[MCP] Query result (public sources): total=%d", total)
	return mcps, total, nil
}

// queryPersonalMCPs 查询个人MCP（仅当前用户创建的）
func (s *ServiceImpl) queryPersonalMCPs(ctx context.Context, opt *ListMCPOption, username string) ([]*entity.MCP, int64, error) {
	// 检查是否需要查询个人来源
	var needQueryPersonal bool

	if len(opt.Sources) == 0 {
		// 如果未指定来源，默认也查询个人来源
		needQueryPersonal = true
	} else {
		// 检查指定的来源中是否包含个人来源
		for _, source := range opt.Sources {
			if source == entity.MCPSourceUserDefine {
				needQueryPersonal = true
				break
			}
		}
	}

	// 如果不需要查询个人来源，直接返回空结果
	if !needQueryPersonal {
		return []*entity.MCP{}, 0, nil
	}

	// 构建查询选项
	listOpt := &dal.ListMCPsOption{
		Name:    opt.Name,
		Sources: []entity.MCPSource{entity.MCPSourceUserDefine},
		Types:   opt.Types,
		Creator: &username, // 必须指定创建者，确保只返回当前用户的个人MCP
		// SessionRole过滤移到Service层处理
	}

	// 执行查询
	mcps, total, err := s.dao.ListMCPs(ctx, listOpt)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ListMCPs personal error: %v", err)
		return nil, 0, errors.Wrap(err, "failed to list personal mcps")
	}

	log.V1.CtxInfo(ctx, "[MCP] Query result (personal source): total=%d", total)
	return mcps, total, nil
}

func (s *ServiceImpl) setUserMCPConfig(ctx context.Context, mcps []*entity.MCP, query UserScopedMCPQuery) ([]*entity.MCP, error) {
	// 查询用户所有的UserMCP，一般也不多，直接查
	userMCPs, err := s.dao.GetUserMCPs(ctx, dal.UserScopedMCPQuery{
		Username: query.Username,
		SpaceID:  query.SpaceID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[setUserMCPConfig] GetUserMCPs error: %v", err)
		return nil, errors.Wrap(err, "failed to get user mcps")
	}
	userMCPMap := make(map[entity.MCPKey]*entity.MCP)
	for i := range userMCPs {
		userMCPMap[userMCPs[i].MCPKey] = userMCPs[i]
	}
	// 组装信息
	for i := range mcps {
		mcp := mcps[i]
		userMCP := userMCPMap[mcp.MCPKey]
		// 设置添加态/个人配置等
		mcp.MergeUserConfig(userMCP)
	}
	log.V1.CtxInfo(ctx, "[setUserMCPConfig] Set user mcp config success: %d mcps", len(mcps))
	return mcps, nil
}

// sortMCPsByPriority 按优先级排序MCP列表并返回, limit 小于 0 代表不限制数量
func (s *ServiceImpl) sortMCPsByPriority(mcps []*entity.MCP, limit int, startID int64) []*entity.MCP {
	if len(mcps) == 0 {
		return nil
	}
	// 在内存中进行排序 - 按照以下规则:
	// 1. 已添加 > 未添加
	// 2. AIME > UserDefine > Cloud
	// 3. 同来源下按创建时间升序
	sort.Slice(mcps, func(i, j int) bool {
		// 优先级1: 已添加的排在前面
		if mcps[i].IsActive != mcps[j].IsActive {
			return mcps[i].IsActive
		}

		// 优先级2: 按来源排序 AIME(1) > UserDefine(2) > Cloud(3)
		if mcps[i].Source != mcps[j].Source {
			return mcps[i].Source < mcps[j].Source
		}

		// 优先级3: 相同来源按创建时间升序
		return mcps[i].CreatedAt.Before(mcps[j].CreatedAt)
	})

	log.V1.CtxDebug(context.Background(), "[MCP] Sorted %d MCPs by priority", len(mcps))
	// 过滤出排列在 start id 之后的MCP
	part := make([]*entity.MCP, 0, len(mcps))
	if startID > 0 {
		start := false
		for _, v := range mcps {
			if v.ID == startID {
				start = true
				continue
			}
			if start {
				part = append(part, v)
			}
		}
	} else {
		part = mcps
	}
	if len(part) == 0 {
		return nil
	}
	var end int
	if limit <= 0 {
		end = len(part)
	} else {
		end = lo.Ternary(limit < len(part), limit, len(part))
	}
	return part[:end]
}

// GetMCPsByKeys 根据MCPKeys获取MCP工具列表
func (s *ServiceImpl) GetMCPsByKeys(ctx context.Context, opt *GetMCPsByKeysOption) ([]*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] GetMCPsByKeys request: %d keys", len(opt.MCPKeys))
	if len(opt.MCPKeys) == 0 {
		return []*entity.MCP{}, nil
	}

	// 1. 按来源分组MCPKeys以加速查询
	keysBySource := make(map[entity.MCPSource][]string)
	for _, key := range opt.MCPKeys {
		if key == nil {
			continue
		}
		keysBySource[key.Source] = append(keysBySource[key.Source], key.MCPID)
	}

	// 2. 按来源分别查询MCP
	var allMCPs []*entity.MCP
	for source, mcpIDs := range keysBySource {
		mcps, err := s.dao.GetMCPsByIDsAndSource(ctx, mcpIDs, source)
		if err != nil {
			log.V1.CtxError(ctx, "[MCP] GetMCPsByIDsAndSource error for source %d: %v", source, err)
			return nil, errors.Wrap(err, "failed to get mcps by ids and source")
		}
		allMCPs = append(allMCPs, mcps...)
	}

	// 3. 组装成map，以MCPKey为键
	mcpMap := make(map[entity.MCPKey]*entity.MCP)
	for _, mcp := range allMCPs {
		key := entity.MCPKey{
			MCPID:  mcp.MCPID,
			Source: mcp.Source,
		}
		mcpMap[key] = mcp
	}

	// 4. 按照opt.MCPKeys的顺序取出结果
	result := make([]*entity.MCP, 0, len(opt.MCPKeys))
	for _, key := range opt.MCPKeys {
		if key == nil {
			continue
		}
		if mcp, exists := mcpMap[*key]; exists {
			result = append(result, mcp)
		}
	}
	if opt.UserScopedMCPQuery != nil {
		mcps, err := s.setUserMCPConfig(ctx, result, *opt.UserScopedMCPQuery)
		if err != nil {
			log.V1.CtxError(ctx, "[GetMCPsByKeys] SetUserMCPConfig error: %v", err)
			return result, errors.Wrap(err, "failed to set user mcp config")
		}
		result = mcps
	}

	log.V1.CtxInfo(ctx, "[MCP] GetMCPsByKeys success: found %d MCPs", len(result))
	return result, nil
}

// UpdateUserMCP 修改MCP工具的激活状态（激活或取消激活）
func (s *ServiceImpl) UpdateUserMCP(ctx context.Context, mcpID entity.MCPKey, userConfigQuery UserScopedMCPQuery,
	statusPtr *entity.ActiveStatus, userConfig *entity.MCPConfig, accessContext *MCPAccessContext) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[UpdateUserMCP] UpdateUserMCP request: username=%s, mcpID=%v", userConfigQuery.Username, mcpID)

	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, userConfigQuery.SpaceID, userConfigQuery.Username)
	if err != nil {
		log.V1.CtxError(ctx, "[UpdateUserMCP] MustGetSpaceIDWithDefault error: %v", err)
		return nil, err
	}
	// 先获取MCP信息
	mcp, err := s.dao.GetMCPByID(ctx, mcpID.MCPID, mcpID.Source, &dal.UserScopedMCPQuery{
		Username: userConfigQuery.Username,
		SpaceID:  spaceID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[UpdateUserMCP] GetMCPByID error: %v", err)
		return nil, errors.Wrap(err, "failed to get mcp")
	}
	if mcp == nil {
		log.V1.CtxError(ctx, "[UpdateUserMCP] MCP not found: %d", mcpID)
		return nil, ErrMCPNotFound
	}

	// 如果有状态变更，那么做状态变更的业务校验
	if statusPtr != nil {
		status := *statusPtr
		// 强制激活的不让取消
		if mcp.Source == entity.MCPSourceAIME && mcp.ForceActive && status == entity.ActiveStatusDeactivate {
			return nil, errors.New("this mcp prohibit this operation")
		}
		// 校验激活上限
		if status == entity.ActiveStatusActivate {
			mcp.IsActive = *statusPtr == entity.ActiveStatusActivate
			offset := int64(choose.If(mcp.IsActive, 0, 1))
			if userConfigQuery.SpaceID != "" {
				count, err := s.CountSpaceMCP(ctx, &ListSpaceMCPOption{
					SpaceID: userConfigQuery.SpaceID,
					User:    &authentity.Account{Username: userConfigQuery.Username},
				})
				if err != nil {
					log.V1.CtxError(ctx, "[UpdateUserMCP] CountSpaceMCP error: %v", err)
					return nil, errors.Wrap(err, "failed to count space mcps")
				}
				if count.ActivateCount+offset > entity.MCPActivateLimit {
					return nil, ErrMCPActiveLimit
				}
			} else {
				_, total, err := s.ListMCP(ctx, &ListMCPOption{
					IsActive: gptr.Of(true),
					Creator:  gptr.Of(userConfigQuery.Username),
				})
				if err != nil {
					log.V1.CtxError(ctx, "[MCP] ListMCPs error: %v", err)
					return nil, errors.Wrap(err, "failed to list active mcps")
				}
				if total+offset > entity.MCPActivateLimit {
					return nil, ErrMCPActiveLimit
				}
			}
		}
	}

	// 如果涉及修改user config，则校验是否能正常通过
	if userConfig != nil {
		mcp.UserConfig = userConfig
		result := s.ValidateMCP(ctx, &MCPAccessContext{
			MCP:            mcp,
			Token:          gptr.Indirect(accessContext).Token,
			LarkUserOpenID: gptr.Indirect(accessContext).LarkUserOpenID,
		})
		if !result.Valid {
			return nil, errors.Wrapf(ErrMCPConfigUnavailable, " err: %s", result.Msg)
		}
	}

	err = s.dao.UpdateUserMCP(ctx, mcpID, dal.UserScopedMCPQuery{Username: userConfigQuery.Username, SpaceID: spaceID},
		statusPtr, userConfig)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] UpdateUserMCP error: %v", err)
		return nil, errors.Wrap(err, "failed to update user mcp")
	}

	log.V1.CtxInfo(ctx, "[MCP] UpdateUserMCP success: %s", mcp.MCPID)
	return mcp, nil
}

// GetMCPByID 根据ID获取MCP工具
func (s *ServiceImpl) GetMCPByID(ctx context.Context, id string, source entity.MCPSource) (*entity.MCP, error) {
	log.V1.CtxInfo(ctx, "[MCP] GetMCPByID request: id=%d, source=%d", id, source)

	// 调用 DAL 层获取MCP
	mcp, err := s.dao.GetMCPByID(ctx, id, source, nil)
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] GetMCPByID error: %v", err)
		return nil, errors.Wrap(err, "failed to get mcp")
	}

	if mcp == nil {
		log.V1.CtxError(ctx, "[MCP] MCP not found: %d", id)
		return nil, ErrMCPNotFound
	}

	log.V1.CtxInfo(ctx, "[MCP] GetMCPByID success: %s", mcp.MCPID)
	return mcp, nil
}

func ConvertToMCPProvider(mcp *entity.MCP, index int) *mcptool.MCPProvider {
	if mcp == nil {
		return nil
	}
	name := replaceSpecialChars(mcp.Name)
	provider := mcptool.MCPProvider{
		// 非AIME的话，传递的id为名称_序列id(仅做去重，因此直接用这里的排序id)
		ID: choose.If(mcp.Source == entity.MCPSourceAIME, mcp.MCPID, fmt.Sprintf("%s_%d", name, index)),
		// 如果来源不是AIME，则ID和name需要拼接成"name_id"格式以防止重名
		Name:        choose.If(mcp.Source == entity.MCPSourceAIME, name, fmt.Sprintf("%s_%d", name, index)),
		Description: mcp.Description,
		Type:        mcptool.MCPSource(mcp.Source),
		MCPType:     mcptool.MCPType(mcp.Type), // 二期新增：设置MCP类型（使用枚举转换）
	}
	// 合并用户配置，优先级：个人 > MCP
	if mcp.UserConfig != nil {
		mcp.Config.Env = gmap.Union(mcp.Config.Env, mcp.UserConfig.Env)
		mcp.Config.Header = gmap.Union(mcp.Config.Header, mcp.UserConfig.Header)
	}

	// 根据MCP类型设置不同的字段
	switch mcp.Type {
	case entity.MCPTypeSTDIO:
		provider.Cmd = gptr.Indirect(mcp.Config.Command)
		provider.Args = mcp.Config.Args

		// 将map[string]string转换为[]string格式的环境变量
		provider.Env = make([]string, 0, len(mcp.Config.Env))
		for key, value := range mcp.Config.Env {
			provider.Env = append(provider.Env, key+"="+value)
		}
	case entity.MCPTypeSSE:
		if mcp.Config.BaseURL != nil {
			provider.BaseURL = *mcp.Config.BaseURL
		}
	case entity.MCPTypeStreamableHTTP:
		// 二期新增：StreamableHTTP类型
		if mcp.Config.BaseURL != nil {
			provider.BaseURL = *mcp.Config.BaseURL
		}
	case entity.MCPTypeCloudSDK:
		// 二期新增：CloudSDK类型
		if mcp.Config.PSM != nil {
			provider.PSM = *mcp.Config.PSM
		}
	}
	provider.Header = mcp.Config.Header
	provider.SkipCloudJWTAuth = cast.ToBool(mcp.Config.SkipCloudJWTAuth)
	return &provider
}

// 匹配所有非中文、非英文、非数字、非下划线、非空格的字符
var specialRegex = regexp.MustCompile(`[^a-zA-Z0-9_\s\p{Han}]`)

// 将字符串中的特殊字符替换为下划线
func replaceSpecialChars(s string) string {
	return specialRegex.ReplaceAllString(s, "_")
}

func (s *ServiceImpl) ValidateMCPOnlyConfig(ctx context.Context, opt *MCPConfigAccessContext) *ValidateMCPResult {
	log.V1.CtxInfo(ctx, "[MCP] ValidateMCP request: %#v", opt)
	// 仅校验个人来源
	if opt.Source != entity.MCPSourceUserDefine {
		return &ValidateMCPResult{Valid: true}
	}

	// Config不能为空
	if opt.Config == nil {
		return &ValidateMCPResult{Valid: false}
	}
	// 临时创建一个MCP对象，用于验证
	tempMCP := &entity.MCP{
		MCPKey: entity.MCPKey{
			MCPID:  s.idGen.NewID(), // 生成一个临时ID
			Source: opt.Source,
		},
		Name:          "temp_validation_mcp",
		Description:   "Temporary MCP for validation",
		Config:        gptr.Indirect(opt.Config),
		Type:          opt.Type,
		DefaultActive: true, // 临时MCP默认激活
	}
	return s.ValidateMCP(ctx, &MCPAccessContext{
		MCP:            tempMCP,
		Token:          opt.Token,
		LarkUserOpenID: opt.LarkUserOpenID,
	})
}

// ValidateMCP 验证MCP工具配置是否有效，仅校验个人来源
func (s *ServiceImpl) ValidateMCP(ctx context.Context, opt *MCPAccessContext) *ValidateMCPResult {
	provider := ConvertToMCPProvider(opt.MCP, opt.Index)
	if provider == nil {
		return &ValidateMCPResult{
			Code: int64(common.ErrorCode_ErrInternalFatal),
			Msg:  "mcp provider is nil",
		}
	}
	// 特殊处理Figma
	if provider.ID == figmaMCPID {
		// 校验FIGMA_API_KEY是否不为空
		return &ValidateMCPResult{Valid: gslice.Any(provider.Env, func(s string) bool {
			return strings.HasPrefix(s, "FIGMA_API_KEY=") && len(s) > len("FIGMA_API_KEY=")
		})}
	}
	resp, err := s.runtimeCli.ValidateMCP(ctx, &nextruntime.ValidateMCPRequest{
		Config:         model.ConvertConfigFromMCPProvider(provider),
		Token:          &opt.Token,
		LarkUserOpenID: &opt.LarkUserOpenID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ValidateMCP ConnectMCP error: %v", err)
		return &ValidateMCPResult{
			Code: int64(common.ErrorCode_ErrInternalFatal),
			Msg:  err.Error(),
		}
	}
	if resp.Result == nil {
		return &ValidateMCPResult{}
	}

	log.V1.CtxInfo(ctx, "[MCP] ValidateMCP success")
	return TransToValidateMCPResult(resp.Result)
}

func (s *ServiceImpl) BatchValidateMCP(ctx context.Context, opt *BatchMCPAccessContext) []*ValidateMCPResult {
	res := make([]*ValidateMCPResult, len(opt.MCPs))
	// build result
	wg := sync.WaitGroup{}
	for i := range opt.MCPs {
		index := i
		mcp := opt.MCPs[i]
		wg.Add(1)
		go panics.Try(func() {
			defer wg.Done()
			if mcp.MCPKey.MCPID != figmaMCPID && s.shouldSkipMCPAccess(mcp) {
				res[index] = &ValidateMCPResult{Valid: true}
				return
			}
			res[index] = s.ValidateMCP(ctx, &MCPAccessContext{
				MCP:            mcp,
				Token:          opt.Token,
				LarkUserOpenID: opt.LarkUserOpenID,
			})
		})
	}
	wg.Wait()
	return res
}

func (s *ServiceImpl) BatchListMCPTools(ctx context.Context, opt *BatchMCPAccessContext) []*ListMCPToolsResult {
	res := make([]*ListMCPToolsResult, len(opt.MCPs))
	// build result
	wg := sync.WaitGroup{}
	for i := range opt.MCPs {
		index := i
		mcp := opt.MCPs[i]
		wg.Add(1)
		go panics.Try(func() {
			defer wg.Done()
			res[index] = s.ListMCPTools(ctx, &MCPAccessContext{
				MCP:            mcp,
				Token:          opt.Token,
				LarkUserOpenID: opt.LarkUserOpenID,
				Index:          index,
			})
			if res[index].Err != nil {
				log.V1.CtxError(ctx, "MCP [%s] failed to list tools: %v", mcp.Name, res[index].Err)
			}
		})
	}
	wg.Wait()
	return res
}

func (s *ServiceImpl) ListMCPTools(ctx context.Context, opt *MCPAccessContext) (res *ListMCPToolsResult) {
	res = &ListMCPToolsResult{
		MCP: opt.MCP,
	}
	// Config不能为空
	if opt.MCP == nil {
		res.Err = errors.New("config is nil")
		return
	}
	provider := ConvertToMCPProvider(opt.MCP, opt.Index)
	// 获取Agent拿到的名称(Agent实际上不消费provider的Name而是ID)
	res.MCP.NameForAgent = provider.ID
	if s.shouldSkipMCPAccess(opt.MCP) {
		res.Skipped = true
		return res
	}

	resp, err := s.runtimeCli.ListMCPTools(ctx, &nextruntime.ListMCPToolsRequest{
		Config:         model.ConvertConfigFromMCPProvider(provider),
		Token:          &opt.Token,
		LarkUserOpenID: &opt.LarkUserOpenID,
	})
	if err != nil {
		log.V1.CtxError(ctx, "[MCP] ListMCPTools error: %v", err)
		res.Err = err
		return
	}
	tools := make([]*entity.MCPTool, len(resp.GetTools()))
	for i := range resp.GetTools() {
		tool := resp.GetTools()[i]
		tools[i] = &entity.MCPTool{
			Name:         tool.Name,
			Description:  tool.Description,
			NameForAgent: mcptool.McpToolName(provider.ID, tool.Name),
		}
	}
	res.Tools = tools
	return res
}

func (s *ServiceImpl) shouldSkipMCPAccess(mcp *entity.MCP) bool {
	return !gslice.Contains([]entity.MCPType{entity.MCPTypeStreamableHTTP, entity.MCPTypeCloudSDK, entity.MCPTypeSSE}, mcp.Type)
}

func (s *ServiceImpl) CreateCloudMCPAuth(ctx context.Context, userToken, psm string) (string, error) {
	serviceID, err := s.cloudMCPAuthCli.GetMCPServiceIDByPSM(ctx, userToken, psm)
	if err != nil {
		log.V1.CtxError(ctx, "[CreateCloudMCPAuth] psm %s GetMCPServiceIDByPSM error: %v", psm, err)
		return "", err
	}
	ticket, err := s.cloudMCPAuthCli.CreateMCPAuthTicket(ctx, userToken, serviceID, aimePSMs, "apply from https://aime.bytedance.net/")
	if err != nil {
		log.V1.CtxError(ctx, "[CreateCloudMCPAuth] psm %s CreateMCPAuthTicket error: %v", psm, err)
		return "", err
	}
	return ticket, nil
}
