package user

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/port/db"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"github.com/samber/lo"
	"go.uber.org/fx"
)

type Service struct {
	dao     *dal.DAO
	tccConf *config.AgentSphereTCCConfig
	idGen   uuid.Generator
}

type CreateServiceOption struct {
	fx.In
	DAO     *dal.DAO
	TccConf *config.AgentSphereTCCConfig
}

func NewService(opt CreateServiceOption) (*Service, error) {
	return &Service{
		dao:     opt.DAO,
		tccConf: opt.TccConf,
		idGen:   uuid.GetDefaultGenerator(nil),
	}, nil
}

func (s *Service) GetOldUserFeatures(account *authentity.Account) *nextentity.UserFeature {
	features := s.tccConf.NextAgentUserFeaturesConfig.GetValue()
	// 超级白名单，不限制数量
	if lo.Contains(features.GrayUsers, account.Username) {
		return &nextentity.UserFeature{
			Invited: true,
		}
	}
	feature := &nextentity.UserFeature{
		Invited:        false,
		SessionLimit:   lo.ToPtr(int64(features.MaxSessionsPerDay)),
		MessageLimit:   lo.ToPtr(int64(features.MaxMessageLimit)),
		MessageWarning: lo.ToPtr(int64(features.MaxMessageWarning)),
	}
	// 部门白名单
	if lo.SomeBy(features.GrayDepartments, func(d string) bool { return strings.HasPrefix(account.Department, d) }) {
		feature.Invited = true
		return feature
	}
	// 正常白名单
	if features.NormalGrayUsers != nil && features.NormalGrayUsers[account.Username] {
		feature.Invited = true
		return feature
	}
	return feature
}

func (s *Service) GetUserFeatures(ctx context.Context, account *authentity.Account, sync bool) *nextentity.UserFeature {
	groupMaps := s.tccConf.NextAgentGroupConfig.GetValue().Groups
	ruleMaps := s.tccConf.NextAgentRuleConfig.GetValue().Rules
	groupRuleMaps := s.tccConf.NextAgentGroupRuleConfig.GetValue().GroupRules

	feature := &nextentity.UserFeature{}

	// 获取用户所在的 Group
	var groups []string
	for key, group := range groupMaps {
		if group.Users[account.Username] {
			feature.Invited = true
			groups = append(groups, key)
			continue
		}
		if lo.SomeBy(group.Departments, func(d string) bool {
			return strings.HasPrefix(account.Department, d)
		}) {
			feature.Invited = true
			groups = append(groups, key)
		}
	}
	groupsFromDB, err := s.ListUserGroups(ctx, ListUserGroupOption{
		Username: account.Username,
		Sync:     sync,
	})
	if err != nil {
		logs.V1.CtxError(ctx, "failed get user: %s groups, err: %v", account.Username, err)
	} else if len(groupsFromDB) > 0 {
		feature.Invited = true
		groups = append(groups, groupsFromDB...)
	}

	// 如果没有找到用户所在的 Group 直接返回
	if !feature.Invited {
		return feature
	}

	// 获取 group 所在的 rule
	var rules []string
	for _, group := range groups {
		rules = append(rules, groupRuleMaps[group]...)
	}

	// 获取对应的规则，并返回并集
	ruleRole := make(map[int]config.NextAgentRule)
	for _, rule := range rules {
		nowRules := ruleMaps[rule]
		for _, nowRule := range nowRules {
			if v, ok := ruleRole[nowRule.Role]; ok {
				// 之前存在，获取并集，同时存在获取更大的权限
				if nowRule.AllowUseInternalTool {
					v.AllowUseInternalTool = true
				}
				v.MaxSessionsPerDay = s.compareAndGetMax(v.MaxSessionsPerDay, nowRule.MaxSessionsPerDay)
				v.MaxMessageWarning = s.compareAndGetMax(v.MaxMessageWarning, nowRule.MaxMessageWarning)
				v.MaxMessageLimit = s.compareAndGetMax(v.MaxMessageLimit, nowRule.MaxMessageLimit)
				ruleRole[nowRule.Role] = v
			} else {
				// 之前不存在，直接赋值
				ruleRole[nowRule.Role] = nowRule
			}
		}
	}
	feature.Roles = lo.Map(lo.MapToSlice(ruleRole, func(key int, value config.NextAgentRule) config.NextAgentRule {
		return value
	}), func(item config.NextAgentRule, index int) nextentity.RoleConfig {
		return nextentity.RoleConfig{
			Role:                 nextentity.SessionRole(item.Role),
			AllowUseInternalTool: item.AllowUseInternalTool,
			SessionLimit:         item.MaxSessionsPerDay,
			MessageLimit:         item.MaxMessageLimit,
			MessageWarning:       item.MaxMessageWarning,
		}
	})
	return feature
}

func (s *Service) compareAndGetMax(before *int64, after *int64) *int64 {
	if before == nil || after == nil {
		return nil
	}
	if *after > *before {
		return after
	}
	return before
}

// 项目组全员，可访问他人的 chat/:session_id 页面，/tasks 后台页
func (s *Service) IsGroupOperator(account *authentity.Account) bool {
	return s.VerifyUserRole(nextagent.UserRoleViewOthers, account)
}

// 开发者，可访问 trace/:session_id 页面，/tasks 后台页，session trace 页面，可下载 logs 类型的 artifact
func (s *Service) IsDeveloper(account *authentity.Account) bool {
	return s.VerifyUserRole(nextagent.UserRoleAimoInternalDev, account)
}

// MCP 合作伙伴，可访问 playground/mcp 页面
func (s *Service) IsMCPPartner(account *authentity.Account) bool {
	return s.VerifyUserRole(nextagent.UserRoleAimoMCPPlayground, account)
}

func (s *Service) IsTraceAll(account *authentity.Account) bool {
	return s.VerifyUserRole(nextagent.UserRoleAimoTraceAll, account)
}

type ListShowcaseOptions struct {
	Locale *string
}

func (s *Service) ListShowcases(ctx context.Context, opt ListShowcaseOptions) ([]*nextagent.Showcase, error) {
	configData := s.tccConf.NextAgentShowcaseLocalizedConfig.GetValue()

	showcases := configData.ShowcasesZH
	if opt.Locale != nil && strings.ToUpper(*opt.Locale) == "EN" && len(configData.ShowcasesEN) > 0 {
		showcases = configData.ShowcasesEN
	}

	if len(showcases) == 0 {
		logs.V1.CtxWarn(ctx, "showcases not found")
		return nil, serverservice.ErrorShowcaseNotFound
	}

	return lo.Map(showcases, func(showcase config.NextAgentShowcase, index int) *nextagent.Showcase {
		category, err := nextagent.ShowcaseCategoryFromString(showcase.Category)
		if err != nil {
			category = nextagent.ShowcaseCategory_Study
		}

		return &nextagent.Showcase{
			ID:          showcase.ReplayID,
			Title:       showcase.Title,
			Description: showcase.Description,
			ImageURL:    showcase.ImageURL,
			TotalSteps:  showcase.TotalSteps,
			Duration:    showcase.Duration,
			ArtifactList: lo.Map(showcase.Artifacts, func(artifact config.ShowcaseArtifacts, index int) *nextagent.ShowcaseArtifact {
				return &nextagent.ShowcaseArtifact{
					Type: artifact.Type,
					Name: artifact.Name,
				}
			}),
			Category: category,
		}
	}), nil
}

func (s *Service) VerifyUserRole(role string, account *authentity.Account) bool {
	if account == nil {
		return false
	}
	roles := s.GetUserRoles(account)
	return lo.Contains(roles, role)
}

func (s *Service) VerifyUserRoles(roles []string, account *authentity.Account) bool {
	if account == nil {
		return false
	}
	userRoles := s.GetUserRoles(account)
	return lo.Every(userRoles, roles)
}

func (s *Service) VerifyUserRolesAny(roles []string, account *authentity.Account) bool {
	if account == nil {
		return false
	}
	userRoles := s.GetUserRoles(account)
	return lo.Some(userRoles, roles)
}

func (s *Service) GetUserRoles(account *authentity.Account) []string {
	conf := s.tccConf.NextAgentUserRoleConfig.GetValue()
	userGroups := lo.FilterMap(conf.UserGroups, func(userGroup config.UserGroupConfig, index int) (string, bool) {
		if lo.Contains(userGroup.Users, account.Username) || lo.SomeBy(userGroup.Departments, func(d string) bool {
			return strings.HasPrefix(account.Department, d)
		}) {
			return userGroup.Name, true
		}
		return "", false
	})
	roles := lo.FilterMap(conf.Roles, func(role config.RoleConfig, index int) (string, bool) {
		if lo.Some(role.UserGroups, userGroups) {
			return role.Name, true
		}

		if lo.Contains(role.Users, account.Username) || lo.SomeBy(role.Departments, func(d string) bool {
			return strings.HasPrefix(account.Department, d)
		}) {
			return role.Name, true
		}
		return "", false
	})
	return roles
}

const (
	// 默认只增加该 group 权限
	normalGroupName = "normal_gray_users_v3"
)

type AddUserGroupOption struct {
	Username string
}

func (s *Service) AddUserGroup(ctx context.Context, opt AddUserGroupOption) (err error) {
	defer func() {
		if err != nil {
			_ = metrics.NSM.PermissionError.WithTags(&metrics.NextServerPermissionEventTag{Username: opt.Username, Method: "AddUserGroup"}).Add(1)
		}
	}()
	_, err = s.dao.AddUserGroup(ctx, dal.AddUserGroupOption{
		ID:        s.idGen.NewID(),
		Username:  opt.Username,
		GroupName: normalGroupName,
	})
	return err
}

type ListUserGroupOption struct {
	Username string
	Sync     bool
}

func (s *Service) ListUserGroups(ctx context.Context, opt ListUserGroupOption) (result []string, err error) {
	defer func() {
		if err != nil {
			_ = metrics.NSM.PermissionError.WithTags(&metrics.NextServerPermissionEventTag{Username: opt.Username, Method: "ListUserGroups"}).Add(1)
		}
	}()
	userGroups, err := s.dao.ListUserGroup(ctx, dal.ListUserGroupOption{
		Username: opt.Username,
		Sync:     opt.Sync,
	})
	if err != nil {
		return nil, err
	}
	return lo.Map(userGroups, func(item *nextentity.UserGroup, index int) string {
		return item.GroupName
	}), nil
}

func (s *Service) GetUserSettings(ctx context.Context, username string) (*nextentity.UserSettings, error) {
	settings, err := s.dao.GetUserSettingsByUsername(ctx, username, false)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to get user settings")
	}
	if settings != nil {
		return settings, nil
	}
	// 如果配置不存在，创建一个默认配置
	settings, err = s.dao.CreateUserSettings(ctx, &nextentity.UserSettings{
		ID:        s.idGen.NewID(),
		Username:  username,
		Locale:    nextentity.UserSettingsLocaleZh,
		LoginInfo: nextentity.UserSettingsLoginInfo{{Website: nextentity.LoginWebsiteTypeBytedance, KeepLogin: false}},
	})
	if err != nil {
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			settings, err = s.dao.GetUserSettingsByUsername(ctx, username, true)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to get user settings")
			}
			return settings, nil
		}
		return nil, errors.WithMessage(err, "failed to create user settings")
	}
	return settings, nil
}

type UpdateUserSettingOption struct {
	User             *authentity.Account
	Locale           *string
	KeepLogin        *bool
	KeyboardShortcut *nextentity.KeyboardShortcut
}

func (s *Service) UpdateUserSettings(ctx context.Context, id string, opt UpdateUserSettingOption) (*nextentity.UserSettings, error) {
	if opt.User == nil {
		return nil, errors.New("user is required")
	}
	updateOptions := dal.UpdateUserSettingsOption{
		Locale:           opt.Locale,
		KeyboardShortcut: opt.KeyboardShortcut,
	}
	if opt.KeepLogin != nil { // 目前只需要维护一个登录站点，后续可能会扩展多个
		updateOptions.LoginInfo = &nextentity.UserSettingsLoginInfo{{Website: nextentity.LoginWebsiteTypeBytedance, KeepLogin: *opt.KeepLogin}}
		if !*opt.KeepLogin {
			// 清除登录cookie
			err := clearUserCookieFromRemote(ctx, opt.User.CloudUserJWT)
			if err != nil {
				return nil, err
			}
		}
	}
	return s.dao.UpdateUserSettings(ctx, id, updateOptions)
}

func clearUserCookieFromRemote(ctx context.Context, userCloudJWT string) error {
	url := "https://strato-keystore.bytedance.net/api/v1/cookie"
	if env.GetCurrentVRegion() == env.VREGION_SINGAPORECENTRAL {
		url = "https://strato-keystore.byteintl.net/api/v1/cookie"
	}
	req, err := http.NewRequest(http.MethodDelete, url, nil)
	if err != nil {
		return errors.WithMessage(err, "failed to create http request")
	}
	req.Header.Add("x-jwt-token", userCloudJWT)
	resp, err := http.DefaultClient.Do(req.WithContext(ctx))
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return errors.Errorf("failed to do http request, status code: %d", resp.StatusCode)
	}
	result := struct {
		Code int    `json:"code"`
		Msg  string `json:"message"`
	}{}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	err = json.Unmarshal(body, &result)
	if result.Code != 0 {
		return errors.Errorf("failed to clear user cookie, code: %d, msg: %s", result.Code, result.Msg)
	}
	return nil
}
