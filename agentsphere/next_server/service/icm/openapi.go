package icm

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/pkg/protocol"
	"github.com/cloudwego/hertz/pkg/app/client"
	"github.com/cloudwego/hertz/pkg/network/standard"
	"github.com/pkg/errors"
)

var _ serverservice.IcmOpenAPI = &IcmOpenapiServiceImpl{}

type IcmOpenapiServiceImpl struct {
	client    *byted.Client
	tccConfig *tcc.GenericConfig[config.NextAimeAccountConfig]
	icmConfig config.AgentSphereIcmConfig
}

func NewIcmOpenapiServiceImpl(conf *config.AgentSphereConfig, tccConfig *tcc.GenericConfig[config.NextAimeAccountConfig]) (*IcmOpenapiServiceImpl, error) {
	icmConfig := conf.ICMConfig
	client, err := byted.NewClient(byted.WithAppClientOptions(
		client.WithDialTimeout(time.Duration(icmConfig.Timeout)*time.Second),
		client.WithDialer(standard.NewDialer())),
	)

	if err != nil {
		return nil, err
	}

	return &IcmOpenapiServiceImpl{
		client:    client,
		tccConfig: tccConfig,
		icmConfig: icmConfig,
	}, nil
}

func (s *IcmOpenapiServiceImpl) GetIcmPsm(imageType string) string {
	switch imageType {
	case serverservice.ImageTypeAgent:
		return s.icmConfig.OnlineImageName
	case serverservice.ImageTypeBash:
		return s.icmConfig.OnlineBashImageName
	default:
		return s.icmConfig.OnlineImageName
	}
}

func (s *IcmOpenapiServiceImpl) GetIcmVersionList(ctx context.Context, opt *serverservice.GetIcmVersionListOption) ([]*entity.IcmVersion, error) {
	req := &protocol.Request{}
	res := &protocol.Response{}

	req.SetRequestURI(fmt.Sprintf("%s/api/v2/images/%s/version_list/", s.icmConfig.Host, s.icmConfig.Namespace))
	req.SetMethod("GET")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", s.genBasicAuth())
	query := "length=20"
	query += "&psm=" + s.GetIcmPsm(opt.ImageType)

	if opt.Region != "" {
		query += "&region=" + opt.Region
	} else if s.icmConfig.Region != "" {
		query += "&region=" + s.icmConfig.Region
	}

	var versionInfo *entity.IcmVersionInfo
	var err error
	if opt.Version != "" {
		if strings.HasPrefix(opt.Version, s.icmConfig.Registry) {
			versionInfo, err = s.GetIcmVersionInfo(ctx, opt.Version)
			if err != nil {
				return nil, err
			}
		} else {
			query += "&image_version=" + opt.Version
			query += "&neighbor=true"
		}
	}

	if opt.SpecificTag != "" {
		query += "&specific_tag=" + opt.SpecificTag
	}

	// TODO: 后面 base 版本加上这个限制
	// if opt.ConfigType != "" && entity.ParseAgentType(opt.ConfigType) == entity.AgentConfigTypeBase {
	// 	query += "&specific_tag=master"
	// }

	req.SetQueryString(query)

	err = s.client.Do(ctx, req, res)
	if err != nil {
		return nil, errors.Wrap(err, "get icm version list failed")
	}

	var versionList []*entity.IcmVersion
	err = json.Unmarshal(res.Body(), &versionList)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshal icm version list failed")
	}

	index := -1
	for i, v := range versionList {
		v.ImageName = fmt.Sprintf("%s/%s", s.icmConfig.Registry, v.ImageName)
		if versionInfo != nil && v.ImageName == versionInfo.DetailInfo.ImageName {
			index = i
		}

	}

	// 处理版本列表，将当前版本放在第一位
	if versionInfo != nil {
		currentVersion := &entity.IcmVersion{
			Describe:          versionInfo.DetailInfo.Describe,
			ImageSize:         versionInfo.DetailInfo.ImageSize,
			Builder:           versionInfo.DetailInfo.Builder,
			ImageName:         versionInfo.DetailInfo.ImageName,
			ImageVersion:      versionInfo.DetailInfo.ImageVersion,
			SpecificTag:       versionInfo.DetailInfo.SpecificTag,
			SpecificImageName: versionInfo.DetailInfo.SpecificImageName,
			CreateTime:        versionInfo.DetailInfo.CreateTime,
		}

		if index != -1 {
			// 版本已在列表中，将其从列表中移除
			versionList = append(versionList[:index], versionList[index+1:]...)
		}
		versionList = append([]*entity.IcmVersion{currentVersion}, versionList...)
	}

	return versionList, nil
}

func (s *IcmOpenapiServiceImpl) GetIcmVersionInfo(ctx context.Context, imageName string) (*entity.IcmVersionInfo, error) {
	req := &protocol.Request{}
	res := &protocol.Response{}

	req.SetRequestURI(fmt.Sprintf("%s/api/v1/versions/version_info/", s.icmConfig.Host))
	req.SetMethod("GET")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", s.genBasicAuth())
	query := "image_name=" + imageName + "&detail=true"

	req.SetQueryString(query)

	err := s.client.Do(ctx, req, res)
	if err != nil {
		return nil, errors.Wrap(err, "get icm version info failed")
	}

	var versionInfo entity.IcmVersionInfo
	err = json.Unmarshal(res.Body(), &versionInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshal icm version info failed")
	}

	return &versionInfo, nil
}

func (s *IcmOpenapiServiceImpl) genBasicAuth() string {
	auth := s.tccConfig.GetPointer().AimeICMAccount + ":" + s.tccConfig.GetPointer().AimeICMToken
	return "Basic " + base64.StdEncoding.EncodeToString([]byte(auth))
}
