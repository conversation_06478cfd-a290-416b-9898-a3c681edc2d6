package icm

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/middleware/hertz/pkg/app/client"
	"code.byted.org/middleware/hertz/pkg/protocol"

	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestIcmOpenapiServiceImpl_GetIcmVersionList(t *testing.T) {
	ctx := context.Background()

	mockey.PatchConvey("Test GetIcmVersionList Setup", t, func() {
		// 创建测试所需的配置
		icmConfig := &config.AgentSphereConfig{
			ICMConfig: config.AgentSphereIcmConfig{
				Host:                "http://example.com",
				Namespace:           "test-namespace",
				OnlineImageName:     "test-image",
				OnlineBashImageName: "test-image",
				Timeout:             1000,
				Registry:            "hub.byted.org",
			},
		}
		tccConfig := &tcc.GenericConfig[config.NextAimeAccountConfig]{}

		// 创建 IcmOpenapiServiceImpl 实例
		icmService, err := NewIcmOpenapiServiceImpl(icmConfig, tccConfig)
		So(err, ShouldBeNil)

		// 创建测试参数
		opts := &serverservice.GetIcmVersionListOption{
			Region:      "test-region",
			Version:     "test-version",
			SpecificTag: "test-tag",
		}

		mockey.PatchConvey("When client.Do returns error", func() {
			expectedErr := errors.New("network error")

			// Mock genBasicAuth 方法
			mockey.Mock((*IcmOpenapiServiceImpl).genBasicAuth).Return("test-auth").Build()
			// Mock client.Do 方法返回错误
			mockey.Mock((*client.Client).Do).Return(expectedErr).Build()

			// 调用被测试的方法
			versionList, err := icmService.GetIcmVersionList(ctx, opts)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(versionList, ShouldBeNil)
		})

		mockey.PatchConvey("When response unmarshal fails", func() {
			// Mock genBasicAuth 方法
			mockey.Mock((*IcmOpenapiServiceImpl).genBasicAuth).Return("test-auth").Build()
			// Mock client.Do 方法返回无效的 JSON
			mockey.Mock((*client.Client).Do).To(func(_ *client.Client, _ context.Context, _ *protocol.Request, res *protocol.Response) error {
				res.SetBody([]byte(`invalid json`))
				return nil
			}).Build()

			// 调用被测试的方法
			versionList, err := icmService.GetIcmVersionList(ctx, opts)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "unmarshal icm version list failed")
			So(versionList, ShouldBeNil)
		})

		mockey.PatchConvey("When get icm version list succeeds", func() {
			expectedVersions := []*entity.IcmVersion{
				{
					ImageName:    "test-namespace/test-image",
					ImageVersion: "v1.0.0",
					Builder:      "test-builder",
					// CommitID:     "test-commit-id",
					Describe: "test-describe",
				},
			}

			// Mock genBasicAuth 方法
			mockey.Mock((*IcmOpenapiServiceImpl).genBasicAuth).Return("test-auth").Build()
			// Mock client.Do 方法返回成功响应
			mockey.Mock((*client.Client).Do).To(func(_ *client.Client, _ context.Context, _ *protocol.Request, res *protocol.Response) error {
				// 注意这里不需要包装在 Response 结构中，因为 GetIcmVersionList 方法直接解析为 []*entity.IcmVersion
				body, _ := json.Marshal(expectedVersions)
				res.SetBody(body)
				return nil
			}).Build()

			// 调用被测试的方法
			versionList, err := icmService.GetIcmVersionList(ctx, opts)

			// 验证结果
			So(err, ShouldBeNil)
			So(len(versionList), ShouldEqual, 1)
			// 注意：由于 GetIcmVersionList 方法会修改 ImageName，所以这里需要调整期望值
			So(versionList[0].ImageName, ShouldEqual, "hub.byted.org/test-namespace/test-image")
			So(versionList[0].ImageVersion, ShouldEqual, expectedVersions[0].ImageVersion)
			So(versionList[0].Builder, ShouldEqual, expectedVersions[0].Builder)
			// So(versionList[0].CommitID, ShouldEqual, expectedVersions[0].CommitID)
			So(versionList[0].Describe, ShouldEqual, expectedVersions[0].Describe)
		})
	})
}

func TestIcmOpenapiServiceImpl_GetIcmVersionInfo(t *testing.T) {
	ctx := context.Background()

	// 创建测试所需的配置
	icmConfig := &config.AgentSphereConfig{
		ICMConfig: config.AgentSphereIcmConfig{
			Host:                "http://example.com",
			Namespace:           "test-namespace",
			OnlineImageName:     "test-image",
			OnlineBashImageName: "test-image",
			Timeout:             1000,
		},
	}
	tccConfig := &tcc.GenericConfig[config.NextAimeAccountConfig]{}

	// 创建测试参数
	version := "test-version"

	mockey.PatchConvey("Test GetIcmVersionInfo", t, func() {
		// 创建 IcmOpenapiServiceImpl 实例
		icmService, err := NewIcmOpenapiServiceImpl(icmConfig, tccConfig)
		So(err, ShouldBeNil)
		mockey.PatchConvey("When client.Do returns error", func() {
			expectedErr := errors.New("network error")

			// Mock genBasicAuth 方法
			mockey.Mock((*IcmOpenapiServiceImpl).genBasicAuth).Return("test-auth").Build()
			// Mock client.Do 方法返回错误
			mockey.Mock((*client.Client).Do).Return(expectedErr).Build()

			// 调用被测试的方法
			versionInfo, err := icmService.GetIcmVersionInfo(ctx, version)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, expectedErr.Error())
			So(versionInfo, ShouldBeNil)
		})

		mockey.PatchConvey("When response unmarshal fails", func() {
			// Mock genBasicAuth 方法
			mockey.Mock((*IcmOpenapiServiceImpl).genBasicAuth).Return("test-auth").Build()
			// Mock client.Do 方法返回无效的 JSON
			mockey.Mock((*client.Client).Do).To(func(_ *client.Client, _ context.Context, _ *protocol.Request, res *protocol.Response) error {
				res.SetBody([]byte(`invalid json`))
				return nil
			}).Build()

			// 调用被测试的方法
			versionInfo, err := icmService.GetIcmVersionInfo(ctx, version)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "unmarshal icm version info failed")
			So(versionInfo, ShouldBeNil)
		})

		mockey.PatchConvey("When get icm version info succeeds", func() {
			resp := entity.IcmVersionInfo{
				VersionName: version,
				Status:      "test-status",
				Describe:    "test-describe",
				DetailInfo: entity.IcmVersionDetailInfo{
					Describe:          "test-describe",
					ImageSize:         "test-image-size",
					Builder:           "test-builder",
					ImageName:         "test-image-name",
					SpecificTag:       "test-specific-tag",
					SpecificImageName: "test-specific-image-name",
					CreateTime:        "test-create-time",
				},
			}

			// Mock genBasicAuth 方法
			mockey.Mock((*IcmOpenapiServiceImpl).genBasicAuth).Return("test-auth").Build()
			// Mock client.Do 方法返回成功响应
			mockey.Mock((*client.Client).Do).To(func(_ *client.Client, _ context.Context, _ *protocol.Request, res *protocol.Response) error {

				body, _ := json.Marshal(resp)
				res.SetBody(body)
				return nil
			}).Build()

			// 调用被测试的方法
			versionInfo, err := icmService.GetIcmVersionInfo(ctx, version)

			// 验证结果
			So(err, ShouldBeNil)
			So(versionInfo.VersionName, ShouldEqual, resp.VersionName)
			So(versionInfo.Status, ShouldEqual, resp.Status)
			So(versionInfo.Describe, ShouldEqual, resp.Describe)
		})
	})
}

func TestIcmOpenapiServiceImpl_genBasicAuth(t *testing.T) {
	// 创建测试所需的配置
	icmConfig := &config.AgentSphereConfig{
		ICMConfig: config.AgentSphereIcmConfig{
			Host:                "http://example.com",
			Namespace:           "test-namespace",
			OnlineImageName:     "test-image",
			OnlineBashImageName: "test-image",
			Timeout:             1000,
		},
	}

	// 创建 tccConfig 并设置 ICM 账号和 Token
	tccConfig := &tcc.GenericConfig[config.NextAimeAccountConfig]{}

	mockey.PatchConvey("Test genBasicAuth", t, func() {
		// 正确模拟 tccConfig.GetPointer 方法
		mockey.MockGeneric((*tcc.GenericConfig[config.NextAimeAccountConfig]).GetPointer).Return(&config.NextAimeAccountConfig{
			AimeICMAccount: "test-account",
			AimeICMToken:   "test-token",
		}).Build()

		// 创建 IcmOpenapiServiceImpl 实例
		icmService, err := NewIcmOpenapiServiceImpl(icmConfig, tccConfig)
		So(err, ShouldBeNil)

		// 调用被测试的方法
		auth := icmService.genBasicAuth()

		// 验证结果
		So(auth, ShouldNotBeEmpty)
		So(auth, ShouldStartWith, "Basic ")
	})
}
