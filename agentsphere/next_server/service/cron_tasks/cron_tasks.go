package cron_tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	bcjwt "code.byted.org/paas/cloud-sdk-go/jwt"
	"github.com/AlekSi/pointer"
	"github.com/bytedance/sonic"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	activityservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/activity"
	artifactservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	templateservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth/service"
	"code.byted.org/devgpt/kiwis/lib/cloud_oauth"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/idgenerator"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/devgpt/kiwis/port/viking"
)

const (
	taskLockKey                       = "next_server:cron_tasks:%v"
	DEFAULT_UNLIMITED_REMAINING_TIMES = 10          // 当用户可以无限次使用角色时，默认赋予10的值
	DEFAULT_USER_TIME_ZONE            = "Etc/GMT-8" // 用户默认时区
	DEFAULT_TASK_RUNNING              = "执行中"
)

var (
	ErrCronTaskNotFound = errors.New("cron task not found")
)

type Service struct {
	CloudOAuthClient      *cloud_oauth.OAuthClient // 字节云OAuth客户端
	ByteCloudValidator    bcjwt.Validator
	cronTaskDAO           dal.CronTaskDAO
	taskExecutionDAO      dal.TaskExecutionDAO
	dao                   *dal.DAO
	taskCronjobMQ         rocketmq.Client
	idGen                 uuid.Generator
	idIntGen              idgenerator.IDGenerator
	larkCli               lark.Client
	larkService           *larkservice.Service
	sessionService        *sessionservice.Service
	spaceService          *spaceservice.Service
	activityService       *activityservice.Service
	templateService       *templateservice.Service
	artifactService       *artifactservice.Service
	authService           service.Service
	redis                 redis.Client
	permissionService     *permission.Service
	config                *config.AgentSphereConfig
	nextTaskCronJobConfig *libtcc.GenericConfig[config.NextTaskCronJob]
}

type CreateServiceOption struct {
	fx.In

	CloudOAuthClient *cloud_oauth.OAuthClient // 字节云OAuth客户端

	CronTaskDAO           dal.CronTaskDAO
	TaskExecutionDAO      dal.TaskExecutionDAO
	Dao                   *dal.DAO
	TaskCronjobMQ         rocketmq.Client `name:"task_cronjob"`
	IdIntGen              idgenerator.IDGenerator
	LarkCli               lark.Client
	LarkService           *larkservice.Service
	SessionService        *sessionservice.Service
	SpaceService          *spaceservice.Service
	ActivityService       *activityservice.Service
	TemplateService       *templateservice.Service
	ArtifactService       *artifactservice.Service
	AuthService           service.Service
	Redis                 redis.Client
	PermissionService     *permission.Service
	VikingClient          *viking.Client
	Conf                  *config.AgentSphereConfig
	NextTaskCronJobConfig *libtcc.GenericConfig[config.NextTaskCronJob]
}

func NewService(opt CreateServiceOption) *Service {
	return &Service{

		CloudOAuthClient:      opt.CloudOAuthClient,
		ByteCloudValidator:    bcjwt.NewValidator([]string{bcjwt.RegionCN, bcjwt.RegionI18N, bcjwt.RegionTX}),
		cronTaskDAO:           opt.CronTaskDAO,
		taskExecutionDAO:      opt.TaskExecutionDAO,
		dao:                   opt.Dao,
		taskCronjobMQ:         opt.TaskCronjobMQ,
		idGen:                 uuid.GetDefaultGenerator(nil),
		idIntGen:              opt.IdIntGen,
		larkCli:               opt.LarkCli,
		larkService:           opt.LarkService,
		sessionService:        opt.SessionService,
		spaceService:          opt.SpaceService,
		activityService:       opt.ActivityService,
		templateService:       opt.TemplateService,
		artifactService:       opt.ArtifactService,
		authService:           opt.AuthService,
		redis:                 opt.Redis,
		permissionService:     opt.PermissionService,
		config:                opt.Conf,
		nextTaskCronJobConfig: opt.NextTaskCronJobConfig,
	}
}

type CreateCronTaskOption struct {
	UID             string
	Username        string
	Email           string
	TaskName        string
	Description     string
	TaskType        nextentity.TaskType
	TemplateID      string
	TemplateName    string
	Schedule        string
	Timezone        string
	NextExecuteTime time.Time
	NextMQEventID   *string
	TaskStatus      nextentity.TaskStatus
	GroupInfo       nextentity.GroupInfo
	Role            nextentity.SessionRole
	Content         string
	Option          string
	SpaceID         string
	Params          interface{}
}

// CreateCronTask 创建定时任务
func (s *Service) CreateCronTask(ctx context.Context, opt CreateCronTaskOption) error {
	// 创建任务时，暂时不对参数做校验，目前无法判断是否存在一个相同的任务

	// 计算下一次执行时间
	nextTime, durationUntilNext, err := s.getNextTime(ctx, opt.Schedule, opt.Timezone)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get next time: %v", err)
		return errors.Wrap(err, "failed to get next time")
	}

	// 发信给消息失败不阻塞创建任务，使用DB Scan兜底
	msgID, err := s.sendMQEvent(ctx, &nextentity.CronTaskEvent{CronTaskUniqueID: opt.UID}, durationUntilNext)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send MQ event: %v", err)
	}

	// 创建任务
	_, err = s.cronTaskDAO.CreateCronTask(ctx, dal.CreateCronTaskOption{
		UserName:        opt.Username,
		UID:             opt.UID,
		Name:            opt.TaskName,
		Description:     lo.ToPtr(opt.Description),
		TaskType:        opt.TaskType,
		TemplateID:      opt.TemplateID,
		Schedule:        opt.Schedule,
		Timezone:        opt.Timezone,
		NextExecuteTime: time.Unix(nextTime, 0),
		NextMQEventID:   lo.ToPtr(msgID),
		GroupInfo:       opt.GroupInfo,
		Role:            opt.Role,
		Content:         lo.ToPtr(opt.Content),
		Option:          lo.ToPtr(opt.Option),
		SpaceID:         opt.SpaceID,
		TaskStatus:      nextentity.CronTaskStatusRunning,
	})
	if err != nil {
		return errors.Wrap(err, "failed to create cron task")
	}

	executionTimeStr, err := cronToDescription(opt.Schedule, opt.Timezone)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get cron to description: %v", err)
	}
	if opt.TaskType != nextentity.TaskTypeAimeBot {
		messageID, err := s.larkService.SendGroupCronTaskConfigUpdateNotification(ctx, larkservice.SendGroupCronTaskConfigUpdateOption{
			Username:          opt.Username,
			TaskName:          opt.TaskName,
			TaskUID:           opt.UID,
			TemplateName:      opt.TemplateName,
			TaskExecutionTime: executionTimeStr,
			AgentName:         opt.Role.String(),
			ChatID:            lo.FromPtr(opt.GroupInfo.GroupID),
			Email:             opt.Email,
			Params:            opt.Params,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to send group cron task config update notification: %v, message id is %v", err, messageID)
		}
		logs.V1.CtxInfo(ctx, "send group cron task config update notification message id is %v", messageID)
	}

	return nil
}

// DeleteCronTask 删除定时任务
func (s *Service) DeleteCronTask(ctx context.Context, uid string, user *authentity.Account) error {

	if user == nil {
		logs.V1.CtxError(ctx, "user is nil")
		return errors.New("user is nil")
	}
	task, err := s.cronTaskDAO.GetCronTask(ctx, dal.GetCronTaskOption{
		UID: uid,
	})
	if err != nil {
		return errors.Wrap(err, "failed to get cron task")
	}
	if task == nil {
		return ErrCronTaskNotFound
	}

	if task.UserName != user.Username {
		log.V1.CtxError(ctx, "user is not match")
		return errors.New("user not match")
	}

	err = s.cronTaskDAO.DeleteCronTask(ctx, uid)
	if err != nil {
		return errors.Wrap(err, "failed to delete cron task")
	}

	return nil
}

type UpdateCronTaskOption struct {
	UID             string
	Username        string
	Email           string
	TaskName        string
	Description     string
	TaskType        nextentity.TaskType
	TemplateID      string
	TemplateName    string
	Schedule        string
	Timezone        string
	NextExecuteTime time.Time
	NextMQEventID   *string
	TaskStatus      nextentity.CronTaskStatus
	GroupInfo       *nextentity.GroupInfo
	Role            nextentity.SessionRole
	Content         string
	Option          string
	Params          interface{}
	OriginalTask    nextentity.CronTask
	GroupChanged    bool
}

func (s *Service) UpdateCronTask(ctx context.Context, opt UpdateCronTaskOption) (*nextentity.CronTask, error) {
	var (
		err error
	)

	// 检查任务修改人是否和之前一致
	if opt.OriginalTask.UserName != opt.Username {
		return nil, errors.New("username not match")
	}

	// 更新
	option := dal.UpdateCronTaskOption{
		UID: opt.UID,
	}

	if strings.TrimSpace(opt.TaskName) != "" {
		option.Name = &opt.TaskName
	}
	if strings.TrimSpace(opt.Description) != "" {
		option.Description = &opt.Description
	}
	if strings.TrimSpace(opt.TemplateID) != "" {
		option.TemplateID = &opt.TemplateID
	}
	if strings.TrimSpace(opt.Content) != "" {
		option.Content = opt.Content
	}
	if strings.TrimSpace(opt.Option) != "" {
		option.Option = &opt.Option
	}
	if opt.Role != 0 {
		option.Role = &opt.Role
	}
	if opt.TaskType != 0 {
		option.TaskType = &opt.TaskType
	}

	// 针对时间进行处理
	var nextTime int64
	var durationUntilNext time.Duration
	timezone := opt.OriginalTask.Timezone
	schedule := opt.OriginalTask.Schedule
	taskStatus := opt.TaskStatus
	if strings.TrimSpace(opt.Timezone) != "" {
		option.Timezone = &opt.Timezone
		timezone = opt.Timezone
	}
	if strings.TrimSpace(opt.Schedule) != "" {
		option.Schedule = &opt.Schedule
		schedule = opt.Schedule
	}
	if opt.TaskStatus != nextentity.CronTaskStatusUnknown {
		option.TaskStatus = &opt.TaskStatus
		taskStatus = opt.TaskStatus
	}
	// 任务状态变为运行中需要重新计算下一次执行时间
	statusChangedToRunning := taskStatus != opt.OriginalTask.TaskStatus && taskStatus == nextentity.CronTaskStatusRunning

	// 在运行状态下，时区或定时任务时间的变更需要重新计算下一次执行时间
	configChangedWhileRunning := taskStatus == nextentity.CronTaskStatusRunning &&
		(timezone != opt.OriginalTask.Timezone || schedule != opt.OriginalTask.Schedule)

	if statusChangedToRunning || configChangedWhileRunning {
		nextTime, durationUntilNext, err = s.getNextTime(ctx, schedule, timezone)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get next time: %v", err)
			return nil, errors.Wrap(err, "failed to get next time")
		}
		option.NextExecuteTime = lo.ToPtr(time.Unix(nextTime, 0))
	}

	if nextTime != 0 {
		// 发信给消息失败不阻塞创建任务，使用DB Scan兜底
		msgID, err := s.sendMQEvent(ctx, &nextentity.CronTaskEvent{CronTaskUniqueID: opt.UID}, durationUntilNext)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to send MQ event: %v", err)
		}
		option.NextMQEventID = lo.ToPtr(msgID)
	}

	if opt.GroupInfo != nil {
		option.GroupInfo = &nextentity.GroupInfo{}
		if opt.GroupInfo.GroupName != nil && strings.TrimSpace(*opt.GroupInfo.GroupName) != "" {
			option.GroupInfo.GroupName = opt.GroupInfo.GroupName
		}
		if opt.GroupInfo.GroupAvatar != nil && strings.TrimSpace(*opt.GroupInfo.GroupAvatar) != "" {
			option.GroupInfo.GroupAvatar = opt.GroupInfo.GroupAvatar
		}
		if opt.GroupInfo.GroupID != nil && strings.TrimSpace(*opt.GroupInfo.GroupID) != "" {
			option.GroupInfo.GroupID = opt.GroupInfo.GroupID
		}
	}
	if opt.GroupChanged && opt.TaskType == nextentity.TaskTypeAimeBot {
		option.GroupInfo = &nextentity.GroupInfo{}
	}

	task, err := s.cronTaskDAO.UpdateCronTask(ctx, option)

	if err != nil {
		return nil, errors.Wrap(err, "failed to update cron task")
	}
	executionTimeStr, err := cronToDescription(opt.Schedule, opt.Timezone)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get cron to description: %v", err)
	}
	if opt.GroupChanged && opt.TaskType != nextentity.TaskTypeAimeBot && opt.TaskType != nextentity.TaskTypeUnknown {
		chatID := ""
		if opt.GroupInfo != nil && opt.GroupInfo.GroupID != nil {
			chatID = lo.FromPtr(opt.GroupInfo.GroupID)
		}
		messageID, err := s.larkService.SendGroupCronTaskConfigUpdateNotification(ctx, larkservice.SendGroupCronTaskConfigUpdateOption{
			Username:          opt.Username,
			TaskName:          opt.TaskName,
			TaskUID:           opt.UID,
			TemplateName:      opt.TemplateName,
			TaskExecutionTime: executionTimeStr,
			AgentName:         opt.Role.String(),
			ChatID:            chatID,
			Email:             opt.Email,
			Params:            opt.Params,
		})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to send group cron task config update notification: %v, message id is %v", err, messageID)
		}
		logs.V1.CtxInfo(ctx, "send group cron task config update notification message id is %v", messageID)
	}

	// 处理0值
	if task.LastRunStatus == nextentity.LastRunStatusUnknown {
		task.LastRunStatus = nextentity.LastRunStatusSuccess
	}
	task.LastRunStatus = nextentity.LastRunStatusSuccess

	// 获取上次的错误原因，并进行替换，不直接对外暴露上次执行错误状态
	execLog, err := s.taskExecutionDAO.GetLastTaskExecutionByTaskUID(ctx, dal.GetLastTaskExecutionByTaskUIDOption{UID: task.UID, Sync: true})
	if err == nil && execLog != nil {
		if lo.FromPtr(execLog.ErrorMessage) != "" {
			task.LastRunStatus = nextentity.LastRunStatusException
			task.LastRunErrMsg = lo.FromPtr(execLog.ErrorMessage)
		}
	}

	return task, nil
}

func (s *Service) GetCronTask(ctx context.Context, uid string) (*nextentity.CronTask, error) {
	// 从Dal中获取CronTask信息
	task, err := s.cronTaskDAO.GetCronTask(ctx, dal.GetCronTaskOption{UID: uid})
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get cron task: %v", err)
		return nil, err
	}
	if task == nil {
		return nil, nil
	}

	// 处理0值
	if task.LastRunStatus == nextentity.LastRunStatusUnknown {
		task.LastRunStatus = nextentity.LastRunStatusSuccess
	}
	// 处理week_day和schedule_type
	//timeStr, weekDay, scheduleType, err := cronToNextTime(task.Schedule)
	//if err != nil {
	//	logs.V1.CtxError(ctx, "failed to get cron to next time: %v", err)
	//} else {
	//	task.Schedule = timeStr
	//	task.WeekDay = weekDay
	//	task.ScheduleType = scheduleType
	//}

	return task, nil
}

type ListCronTasksOption struct {
	Username string
	Query    *string
	PageNum  int64
	PageSize int64
	SpaceID  string
}

func (s *Service) ListCronTasks(ctx context.Context, opt *ListCronTasksOption) (int64, []*nextentity.CronTask, error) {
	// 需要对opt.query做处理，trim space，以及将所有大写转换为小写
	if opt.Query != nil {
		*opt.Query = strings.TrimSpace(*opt.Query)
	}

	option := dal.ListCronTasksOption{
		Query:   opt.Query,
		Offset:  int((opt.PageNum - 1) * opt.PageSize),
		Limit:   int(opt.PageSize),
		SpaceID: opt.SpaceID,
	}

	count, tasks, err := s.cronTaskDAO.ListCronTasks(ctx, option)
	if err != nil {
		return 0, nil, errors.Wrap(err, "failed to list cron tasks")
	}

	// 增加week_day和schedule_type
	// 处理0值的last_run_status
	// 从DB中获取from value
	for k, v := range tasks {
		if v == nil {
			continue
		}
		// 处理0值
		if v.LastRunStatus == nextentity.LastRunStatusUnknown {
			tasks[k].LastRunStatus = nextentity.LastRunStatusSuccess
		}
		tasks[k].LastRunStatus = nextentity.LastRunStatusSuccess
		// 处理week_day和schedule_type
		//timeStr, weekDay, scheduleType, err := cronToNextTime(v.Schedule)
		//if err != nil {
		//	logs.V1.CtxError(ctx, "failed to get cron to next time: %v", err)
		//	continue
		//}
		//tasks[k].Schedule = timeStr
		//tasks[k].WeekDay = weekDay
		//tasks[k].ScheduleType = scheduleType

		// 获取上次的错误原因，并进行替换，不直接对外暴露上次执行错误状态
		execLog, err := s.taskExecutionDAO.GetLastTaskExecutionByTaskUID(ctx, dal.GetLastTaskExecutionByTaskUIDOption{UID: v.UID, Sync: true})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get last task execution: %v", err)
			continue
		}
		if execLog == nil {
			// 没找到默认正常值
			continue
		}
		if lo.FromPtr(execLog.ErrorMessage) != "" {
			tasks[k].LastRunStatus = nextentity.LastRunStatusException
			tasks[k].LastRunErrMsg = lo.FromPtr(execLog.ErrorMessage)
		}
	}

	return count, tasks, nil
}

type ListTaskExecutionsOption struct {
	UID      string
	PageNum  int64
	PageSize int64
}

func (s *Service) ListTaskExecutions(ctx context.Context, opt *ListTaskExecutionsOption) (int64, []*nextentity.TaskExecution, error) {
	option := dal.ListTaskExecutionsOption{
		TaskUID: lo.ToPtr(opt.UID),
		Offset:  int((opt.PageNum - 1) * opt.PageSize),
		Limit:   int(opt.PageSize),
	}

	count, tasks, err := s.taskExecutionDAO.ListTaskExecutions(ctx, option)
	if err != nil {
		return 0, nil, errors.Wrap(err, "failed to list tasks executions")
	}

	// 这里需要查询任务信息，然后给到list中任务的名称，以及任务的状态，如果任务压根没执行，则这里任务状态给个失败
	for k, v := range tasks {
		if v == nil {
			continue
		}
		if v.ErrorMessage != nil && lo.FromPtr(v.ErrorMessage) != "" {
			tasks[k].Status = nextentity.SessionStatusError
			// todo const
			tasks[k].TaskName = "任务发起失败"
			continue
		}
		// 如果没出错，则直接拿之前的信息
		// 增加查询的信息
		session, err := s.dao.GetSession(ctx, dal.GetSessionOption{ID: v.SessionID})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get session: %v", err)
			continue
		}
		if session == nil {
			logs.V1.CtxError(ctx, "session is nil")
			continue
		}
		tasks[k].Status = session.Status
		tasks[k].TaskName = session.Title
	}

	return count, tasks, nil
}

// DoTask 执行任务
func (s *Service) DoTask(ctx context.Context, taskInfo *nextentity.CronTask, triggerType nextentity.TriggerType, eventID string, UID string,
	user *authentity.Account) (*nextentity.Session, *nextentity.Message, *nextentity.TemplateVersion, error) {
	logs.V1.CtxDebug(ctx, "[doTask] start to do task")

	// 执行结果记录
	var (
		sessionID    = ""   // session ID
		errorMessage = ""   // 插入DB的错误信息 todo 暂时使用中文，后续改成i18n的方式
		success      = true // 任务是否执行成功
	)

	logID, _ := ctxvalues.LogID(ctx)

	// ========== 第一阶段：前置检查，失败直接丢弃消息 ==========

	// 1. 获取redis锁
	if err := s.acquireRedisLock(ctx, UID); err != nil {
		logs.V1.CtxError(ctx, "failed to acquire redis lock: %v", err)
		return nil, nil, nil, err // 拿不到锁，丢弃消息
	}

	// 结束后释放锁
	defer s.releaseRedisLock(ctx, UID)

	//// 2. 获取任务信息
	if taskInfo == nil {
		tempTaskInfo, err := s.cronTaskDAO.GetCronTask(ctx, dal.GetCronTaskOption{UID: UID, Sync: true})
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get task info: %v", err)
			return nil, nil, nil, errors.Wrap(err, "failed to get task info")
		}
		if tempTaskInfo == nil {
			logs.V1.CtxError(ctx, "task not found")
			return nil, nil, nil, errors.New("task not found")
		}

		taskInfo = tempTaskInfo
	}

	// 判断任务是否还开着，如果任务关闭，那么来自于非手动触发的流程，均停止执行，并且不发送下一次的MQ消息了
	if triggerType != nextentity.TriggerTypeManual && taskInfo.TaskStatus == nextentity.CronTaskStatusStopped {
		logs.V1.CtxInfo(ctx, "task is stopped, skip")
		return nil, nil, nil, nil
	}

	// ========== 第二阶段：业务逻辑执行，确保最终插入DB ==========

	// 使用defer确保最终一定会插入DB并发送下一次消息
	defer func() {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					logs.V1.CtxError(ctx, "[CodeAssistEntityRetrieval]"+
						" panic in walkthrough goroutine: %+v, stacktrace: %s", r, string(debug.Stack()))
				}
			}()
			if err := s.finalizeTask(ctx, taskInfo, triggerType, eventID, time.Now().Unix(),
				sessionID, errorMessage, user.Username, user.Email, success); err != nil {
				logs.V1.CtxError(ctx, "failed to finalize task: %v", err)
			}
		}()
	}()

	// 4. 检查用户权限和剩余次数
	useRole, canExecute, err := s.checkUserPermissions(ctx, user, taskInfo)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to check user permissions: %v", err)
		success = false
		//errorMessage = errors.Wrap(err, "failed to check user permissions").Error()
		errorMessage = "检查用户权限失败"
		return nil, nil, nil, err // 记录错误但不返回，确保执行defer
	}
	if !canExecute {
		logs.V1.CtxInfo(ctx, "user has no permission or reached limit")
		success = false
		//errorMessage = "reach the limit of role or no permission"
		errorMessage = "达到运行次数上限"
		return nil, nil, nil, serverservice.ErrMaximumRunningSessionsReached // 记录错误但不返回，确保执行defer
	}

	// 5. 执行核心业务逻辑
	session, message, template, err := s.executeTask(ctx, user, taskInfo, useRole, logID)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to execute task: %v", err)
		success = false
		errorMessage = err.Error()
		return nil, nil, nil, err // 记录错误但不返回，确保执行defer
	}
	if session != nil {
		sessionID = session.ID
	}

	logs.V1.CtxInfo(ctx, "task executed successfully, session: %v, message: %v", util.ToJson(session), util.ToJson(message))

	return session, message, template, nil
}

// acquireRedisLock 获取Redis锁
func (s *Service) acquireRedisLock(ctx context.Context, UID string) error {
	ok, err := s.redis.SetNX(ctx, taskLockKey, UID, time.Second*60)
	if err != nil {
		return errors.WithMessage(err, "failed to connect redis")
	}
	if !ok {
		return errors.New("failed to get lock")
	}
	return nil
}

func (s *Service) releaseRedisLock(ctx context.Context, UID string) {
	if _, err := s.redis.Cad(ctx, taskLockKey, UID); err != nil {
		// 记录错误但不返回，因为defer函数通常不处理返回值
		logs.V1.CtxError(ctx, "failed to release redis lock: %v", err)
	}
}

// checkUserPermissions 检查用户权限和剩余次数
func (s *Service) checkUserPermissions(ctx context.Context, user *authentity.Account, taskInfo *nextentity.CronTask) (nextentity.SessionRole, bool, error) {
	// 检查是否可以创建session
	result, err := s.sessionService.CanCreateSession(ctx, sessionservice.CanCreateSessionOption{
		Account: user,
		Roles: []nextentity.SessionRole{
			nextentity.SessionRoleYoungTalent,
			nextentity.SessionRoleLateralHire,
			nextentity.SessionRoleIndustryVeteran,
		},
	})
	if err != nil {
		return nextentity.SessionRoleUnknown, false, err
	}

	logs.V1.CtxInfo(ctx, "check create session result: %v", util.ToJson(result))

	if result == nil || !result.Allowed {
		return nextentity.SessionRoleUnknown, false, nil
	}

	// 统计剩余次数
	remainingTime := make(map[nextentity.SessionRole]int32)
	sum := int32(0)
	for _, v := range result.Results {
		// 如果次数用完了，allow会是false
		if v.Allowed == false {
			continue
		}
		if v.RemainingTimes == nil {
			remainingTime[v.Role] = DEFAULT_UNLIMITED_REMAINING_TIMES
			sum += DEFAULT_UNLIMITED_REMAINING_TIMES
		}
		if v.RemainingTimes != nil && lo.FromPtr(v.RemainingTimes) > 0 {
			remainingTime[v.Role] = lo.FromPtr(v.RemainingTimes)
			sum += lo.FromPtr(v.RemainingTimes)
		}
	}

	if sum == 0 {
		return nextentity.SessionRoleUnknown, false, nil
	}

	// 确定使用的角色
	useRole := taskInfo.Role
	if remainingTime[taskInfo.Role] < 1 {
		// 随机使用另外一个有剩余次数的角色
		for k, v := range remainingTime {
			if v > 0 {
				useRole = k
				break
			}
		}
	}

	return useRole, true, nil
}

// executeTask 执行核心任务逻辑
func (s *Service) executeTask(ctx context.Context, user *authentity.Account, taskInfo *nextentity.CronTask,
	useRole nextentity.SessionRole, logID string) (*nextentity.Session, *nextentity.Message, *nextentity.TemplateVersion, error) {
	// 1. 获取模板变量，检查模板变量信息
	template, err := s.templateService.GetTemplate(ctx, taskInfo.TemplateID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get template: %v", err)
		//return nil, nil, nil, errors.Wrap(err, "failed to get template")
		return nil, nil, nil, errors.New("无法获取模板信息")
	}
	if template == nil {
		log.V1.CtxError(ctx, "template not found")
		//return nil, nil, nil, errors.New("template not found")
		return nil, nil, nil, errors.New("模板不存在")
	}

	// 记录模板中的变量并去重
	templateVariable := make(map[string]int)
	for _, v := range template.PromptVariables {
		if v != nil {
			templateVariable[v.Name] = 1
		}
	}
	// todo 后续如果允许A创建的任务B来执行or重试，修改TemplateVariables和用户名的关联情况
	savedTemplateVariables, attachments, err := s.getTemplateVariables(ctx, taskInfo, user.Username)
	if err != nil {
		//return nil, nil, template, errors.Wrap(err, "failed to get template variables")
		logs.V1.CtxError(ctx, "failed to get template variables: %v", err)
		return nil, nil, template, errors.Wrap(err, "无法获取模板变量")
	}

	if len(templateVariable) != len(savedTemplateVariables) {
		logs.V1.CtxInfo(ctx, "template variable not match, template variable: %v, saved template variable: %v", len(templateVariable), len(savedTemplateVariables))
		return nil, nil, template, errors.New("绑定的模板内容有变更")
	}
	// 查看模板中的变量和存储的变量是否匹配
	for k, _ := range templateVariable {
		if _, ok := savedTemplateVariables[k]; !ok {
			//return nil, nil, template, errors.New("template variable not match")
			logs.V1.CtxError(ctx, "template variable %s not found", k)
			return nil, nil, template, errors.New("绑定的模板内容有变更")
		}
	}

	// 2. 创建session
	session, err := s.createSessionWithRetry(ctx, user, useRole, taskInfo, logID)
	if err != nil {
		//return nil, nil, template, errors.Wrap(err, "failed to create session")
		logs.V1.CtxError(ctx, "failed to create session: %v", err)
		return nil, nil, template, errors.Wrap(err, "创建任务失败")
	}
	if session == nil {
		//return nil, nil, template, errors.New("session is nil")
		return nil, nil, template, errors.New("创建任务失败")
	}

	// 3. 创建session资源
	if err := s.createSessionResource(ctx, user, taskInfo, session.ID); err != nil {
		//return session, nil, template, errors.Wrap(err, "failed to create session resource")
		logs.V1.CtxError(ctx, "failed to create session resource: %v", err)
		return session, nil, template, errors.New("创建任务失败")
	}

	// 4. 处理活动（非关键错误，不影响主流程）
	s.handleActivity(ctx, user.Username)

	// 5. 检查是否可以创建消息
	if err := s.checkCanCreateMessage(ctx, session, user); err != nil {
		logs.V1.CtxError(ctx, "failed to check create message permission: %v", err)
		//return session, nil, template, errors.Wrap(err, "failed to check create message permission")
		return session, nil, template, errors.New("创建任务失败")
	}

	// 6. 更新artifact（异步执行，不影响主流程）
	s.updateArtifactAsync(ctx, session.ID, attachments)

	// 7. 创建消息
	message, err := s.createTaskMessage(ctx, user, session.ID, taskInfo, savedTemplateVariables, attachments)
	if err != nil {
		//return session, nil, nil, errors.Wrap(err, "failed to create message")
		logs.V1.CtxError(ctx, "failed to create task message: %v", err)
		return session, nil, nil, errors.New("创建任务失败")
	}

	return session, message, template, nil
}

// createSessionWithRetry 创建session（带重试）
func (s *Service) createSessionWithRetry(ctx context.Context, user *authentity.Account, useRole nextentity.SessionRole, taskInfo *nextentity.CronTask, logID string) (*nextentity.Session, error) {
	return s.sessionService.CreateSession(ctx, sessionservice.CreateSessionOption{
		User:            user,
		Role:            &useRole,
		TemplateID:      taskInfo.TemplateID,
		SpaceID:         taskInfo.SpaceID,
		LogID:           logID,
		Source:          nextentity.SessionSourceCronJob,
		SkipPrepareCube: true,
	})
}

// createSessionResource 创建session资源
func (s *Service) createSessionResource(ctx context.Context, user *authentity.Account, taskInfo *nextentity.CronTask, sessionID string) error {
	var isSpacePublic *bool
	if taskInfo.SpaceID != "" {
		space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{
			SpaceID:    taskInfo.SpaceID,
			NeedConfig: true,
		})
		if err != nil {
			return err
		}
		if space != nil && space.Config != nil && space.Config.BaseConfig != nil && pointer.Get(space.Config.BaseConfig.SessionVisibility) {
			isSpacePublic = lo.ToPtr(true)
		}
	}

	return backoff.Retry(func() error {
		_, err := s.permissionService.CreateResource(ctx, permission.CreateResourceOption{
			Owner:         user.Username,
			Type:          nextentity.ResourceTypeSession,
			ExternalID:    sessionID,
			GroupID:       lo.ToPtr(taskInfo.SpaceID),
			IsSpacePublic: isSpacePublic,
		})
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 2))
}

// handleActivity 处理活动（非关键逻辑）
func (s *Service) handleActivity(ctx context.Context, username string) {
	activity, _ := s.activityService.GetCurrentActivity(ctx)
	if activity != nil && activity.IsActivated() {
		if err := s.activityService.HandleActivityCreateSession(ctx, username, activity.ID); err != nil {
			logs.V1.CtxError(ctx, "failed to handle activity when create session: %v", err)
		}
	}
}

// getTemplateVariables 获取模板变量
func (s *Service) getTemplateVariables(ctx context.Context, taskInfo *nextentity.CronTask, username string) (map[string]*nextentity.TemplateVariableValue, []*nextagent.AttachmentRequired, error) {
	variable, err := s.templateService.GetLastCronTaskTemplateVariable(ctx, taskInfo.TemplateID, username, taskInfo.SpaceID, taskInfo.UID)
	if err != nil {
		return nil, nil, err
	}

	templateVariables := make(map[string]*nextentity.TemplateVariableValue)
	var attachments []*nextagent.AttachmentRequired

	if variable != nil {
		templateVariables = variable.Value.Variables
		for _, v := range variable.Value.Variables {
			if v == nil || len(v.Attachments) == 0 {
				continue
			}
			attachments = append(attachments, lo.Map(v.Attachments, func(a *nextentity.Attachment, _ int) *nextagent.AttachmentRequired {
				return &nextagent.AttachmentRequired{ID: a.ID, FileName: a.FileName}
			})...)
		}
	}

	return templateVariables, attachments, nil
}

// checkCanCreateMessage 检查是否可以创建消息
func (s *Service) checkCanCreateMessage(ctx context.Context, session *nextentity.Session, user *authentity.Account) error {
	allowed, err := s.sessionService.CanCreateMessage(ctx, sessionservice.CanCreateMessageOption{
		Session: *session,
		Account: *user,
	})
	if err != nil {
		return err
	}
	if !allowed {
		return errors.New("not allowed to create message")
	}
	return nil
}

// updateArtifactAsync 异步更新artifact
func (s *Service) updateArtifactAsync(ctx context.Context, sessionID string, attachments []*nextagent.AttachmentRequired) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logs.V1.CtxError(ctx, "update artifact async panic: %v", r)
			}
		}()

		for _, attachment := range attachments {
			artifact, err := s.artifactService.GetArtifact(ctx, artifactservice.GetArtifactOption{
				ID:   attachment.ID,
				Sync: true,
			})
			if err != nil {
				logs.V1.CtxError(ctx, "failed to get artifact: %v", err)
				continue
			}
			if artifact.SessionID == "unknown" {
				_, err = s.artifactService.UpdateArtifact(ctx, artifactservice.UpdateArtifactOption{
					ID:        attachment.ID,
					SessionID: lo.ToPtr(sessionID),
				})
				if err != nil {
					logs.V1.CtxError(ctx, "failed to update artifact: %v", err)
				}
			}
		}
	}()
}

// createTaskMessage 创建任务消息
func (s *Service) createTaskMessage(ctx context.Context, user *authentity.Account, sessionID string, taskInfo *nextentity.CronTask,
	templateVariables map[string]*nextentity.TemplateVariableValue, attachments []*nextagent.AttachmentRequired) (*nextentity.Message, error) {

	messageOptions := nextentity.MessageOptions{}
	if taskInfo.Option != "" {
		err := sonic.Unmarshal([]byte(taskInfo.Option), &messageOptions)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal message options: %v", err)
			return nil, errors.Wrap(err, fmt.Sprintf("failed to unmarshal message options: %s", taskInfo.Option))
		}
	}

	message, err := s.sessionService.CreateMessage(ctx, sessionservice.CreateMessageOption{
		SessionID: sessionID,
		Role:      nextentity.MessageRoleUser,
		Content: nextentity.MessageContent{
			Content: taskInfo.Content,
		},
		User: user,
		Attachments: lo.Map(attachments, func(a *nextagent.AttachmentRequired, _ int) *nextentity.Attachment {
			return &nextentity.Attachment{
				ID:       a.ID,
				FileName: a.FileName,
			}
		}),
		EventOffset:       0,
		Options:           messageOptions,
		Status:            nextentity.MessageStatusSent,
		TemplateID:        taskInfo.TemplateID,
		TemplateVariables: templateVariables,
		SpaceID:           taskInfo.SpaceID,
	})

	if err != nil {
		return nil, err
	}
	if message == nil {
		return nil, errors.New("message is nil")
	}
	return message, nil
}

// finalizeTask 最终化任务处理（插入DB和发送下一次消息）
func (s *Service) finalizeTask(ctx context.Context, taskInfo *nextentity.CronTask, triggerType nextentity.TriggerType, eventID string,
	unixTime int64, sessionID, errorMessage, username, email string, taskSuccess bool) error {

	var (
		messageID string
	)

	// 发送下一次MQ消息并更新任务表
	if err := s.scheduleNextExecution(ctx, taskInfo); err != nil {
		logs.V1.CtxError(ctx, "failed to schedule next execution: %v", err)
		// 任务表的下次执行时间可能会报错，但暂时不影响本次的消息发送和插入到history表
	}

	if taskSuccess && sessionID != "" {
		// 等待10s后获取信息
		time.Sleep(10 * time.Second)

		// 从 DB 获取最新的 session
		session, err := s.dao.GetSession(ctx, dal.GetSessionOption{ID: sessionID})
		if err != nil || session == nil {
			logs.V1.CtxError(ctx, "failed to get session for notification: %v", err)
		} else {
			if taskInfo.TaskType == nextentity.TaskTypeAimeBot {
				messageID, err = s.larkService.SendPersonalCronTaskStartNotification(ctx,
					larkservice.SendPersonalCronTaskStartNotificationOption{
						Email:      email,
						SessionID:  session.ID,
						TaskName:   session.Title,
						TaskUID:    taskInfo.UID,
						TaskStatus: DEFAULT_TASK_RUNNING,
						ChatID:     lo.FromPtr(taskInfo.GroupInfo.GroupID),
					})

			} else {
				messageID, err = s.larkService.SendGroupCronTaskStartNotification(ctx,
					larkservice.SendGroupCronTaskStartNotificationOption{
						Username:   username,
						Email:      email,
						SessionID:  session.ID,
						TaskName:   session.Title,
						TaskUID:    taskInfo.UID,
						TaskStatus: DEFAULT_TASK_RUNNING,
						ChatID:     lo.FromPtr(taskInfo.GroupInfo.GroupID),
					})
			}

			if err != nil {
				logs.V1.CtxError(ctx, "failed to send notification: %v", err)
			}
			logs.V1.CtxInfo(ctx, "send cron task notification message id is %v", messageID)
		}
	}

	// 插入执行记录到DB
	if err := s.insertExecutionRecord(ctx, taskInfo, triggerType, eventID, unixTime, sessionID, errorMessage, username,
		messageID); err != nil {
		logs.V1.CtxError(ctx, "failed to insert execution record: %v", err)
	}

	return nil
}

type CreateExecutionRecordOption struct {
	Username    string
	SessionID   string
	TriggerType nextentity.TriggerType
	Config      *nextentity.TaskConfig
}

func (s *Service) CreateExecutionRecord(ctx context.Context, opt CreateExecutionRecordOption) (*nextentity.TaskExecution, error) {
	taskExecution, err := s.taskExecutionDAO.CreateTaskExecution(ctx, dal.CreateTaskExecutionOption{
		TaskUID:     s.idGen.NewID(),
		Username:    opt.Username,
		ExecuteTime: time.Now(),
		Status:      nextentity.TaskExecutionStatusRunning,
		SessionID:   opt.SessionID,
		TriggerType: opt.TriggerType,
		Config:      opt.Config,
	})
	if err != nil {
		return nil, err
	}
	return taskExecution, nil
}

type UpdateExecutionRecordOption struct {
}

// TODO(lxj): 回调前的时候更新一下任务状态
func (s *Service) UpdateExecutionRecord(ctx context.Context, opt CreateExecutionRecordOption) (*nextentity.TaskExecution, error) {
	return nil, nil
}

// insertExecutionRecord 插入执行记录
func (s *Service) insertExecutionRecord(ctx context.Context, taskInfo *nextentity.CronTask, triggerType nextentity.TriggerType, eventID string,
	unixTime int64, sessionID, errorMessage, username, larkMsgID string) error {
	var (
		errorPtr      *string
		larkMsgStatus nextentity.TaskExecutionLarkMessageStatus
	)
	if errorMessage != "" {
		errorPtr = lo.ToPtr(errorMessage)
	}

	// 如果发送的卡片消息成功，则在DB中记录idle状态
	if larkMsgID != "" {
		larkMsgStatus = nextentity.TaskExecutionLarkMessageStatusIdle
	}

	_, err := s.taskExecutionDAO.CreateTaskExecution(ctx, dal.CreateTaskExecutionOption{
		TaskUID:           taskInfo.UID,
		Username:          username,
		ExecuteTime:       time.Unix(unixTime, 0),
		Status:            nextentity.TaskExecutionStatusSuccess,
		ErrorMessage:      errorPtr,
		SessionID:         sessionID,
		TriggerType:       triggerType,
		MQEventID:         lo.ToPtr(eventID),
		LarkMessageID:     lo.ToPtr(larkMsgID),
		LarkMessageStatus: larkMsgStatus,
		Config: lo.ToPtr(nextentity.TaskConfig{
			LarkGroupID:          taskInfo.GroupInfo.GroupID,
			SendUserNotification: lo.ToPtr(taskInfo.TaskType == nextentity.TaskTypeAimeBot),
		}),
	})
	return err
}

// updateCronTaskLastRunStatus 更新是否执行成功
func (s *Service) updateCronTaskLastRunStatus(ctx context.Context, uid string, status nextentity.LastRunStatus) error {
	_, err := s.cronTaskDAO.UpdateCronTask(ctx, dal.UpdateCronTaskOption{
		UID:           uid,
		LastRunStatus: lo.ToPtr(status),
	})
	return err
}

// scheduleNextExecution 安排下一次执行
func (s *Service) scheduleNextExecution(ctx context.Context, taskInfo *nextentity.CronTask) error {
	// 计算下一次执行时间
	nextTime, durationUntilNext, err := s.getNextTime(ctx, taskInfo.Schedule, taskInfo.Timezone)
	if err != nil {
		return errors.Wrap(err, "failed to get next time")
	}

	// 发送MQ消息
	msgID, err := s.sendMQEvent(ctx, &nextentity.CronTaskEvent{CronTaskUniqueID: taskInfo.UID}, durationUntilNext)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to send MQ event: %v", err)
		// MQ发送失败不阻塞，依靠DB扫描兜底
	}

	// 更新任务表
	option := dal.UpdateCronTaskOption{
		UID:             taskInfo.UID,
		NextExecuteTime: lo.ToPtr(time.Unix(nextTime, 0)),
		NextMQEventID:   lo.ToPtr(msgID),
	}

	_, err = s.cronTaskDAO.UpdateCronTask(ctx, option)
	if err != nil {
		return errors.Wrap(err, "failed to update cron task")
	}

	return nil
}

// getNextTime 通过cron表达式和用户时区，计算下一次执行时间和预期执行间隔
func (s *Service) getNextTime(ctx context.Context, cronExpr, userSelectTimezone string) (int64, time.Duration, error) {
	if userSelectTimezone == "" {
		userSelectTimezone = DEFAULT_USER_TIME_ZONE
	}
	// 获取系统时区
	systemTimezone, err := getCurrentSystemTimezone()
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get current system timezone", "err", err)
		return 0, 0, errors.Wrap(err, "failed to get current system timezone")
	}

	// 加载系统时区
	systemLoc, err := time.LoadLocation(systemTimezone)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to load system timezone %s: %v", systemTimezone, err)
		return 0, 0, errors.Wrap(err, "failed to load system timezone")
	}

	// 加载用户选择的时区
	userLoc, err := time.LoadLocation(userSelectTimezone)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to load user timezone %s: %v", userSelectTimezone, err)
		return 0, 0, errors.Wrap(err, "failed to load user timezone")
	}

	// 解析cron表达式
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	schedule, err := parser.Parse(cronExpr)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to parse cron expression %s: %v", cronExpr, err)
		return 0, 0, errors.Wrap(err, "failed to parse cron expression")
	}

	// 获取当前系统时间
	systemNow := time.Now().In(systemLoc)

	// 将当前系统时间转换到用户时区
	userNow := systemNow.In(userLoc)

	// 在用户时区中计算下一次执行时间
	userNextTime := schedule.Next(userNow)

	// 将用户时区的下一次执行时间转换回系统时区，用于存储
	systemNextTime := userNextTime.In(systemLoc)

	// 计算预期多久后执行（系统时区的时间差）
	duration := systemNextTime.Sub(systemNow)

	// 调试日志
	logs.V1.CtxInfo(ctx, "timezone conversion debug, systemTimezone %v, userTimezone %v, "+
		"systemNow %v, userNow %v, userNextTime %v, systemNextTime %v, duration %v",
		systemTimezone,
		userSelectTimezone,
		systemNow.Format("2006-01-02 15:04:05 MST"),
		userNow.Format("2006-01-02 15:04:05 MST"),
		userNextTime.Format("2006-01-02 15:04:05 MST"),
		systemNextTime.Format("2006-01-02 15:04:05 MST"),
		duration.String())

	// 返回系统时区的Unix时间戳和执行间隔
	return systemNextTime.Unix(), duration, nil
}

// sendMQEvent 使用RocketMQ发送MQ事件
func (s *Service) sendMQEvent(ctx context.Context, event *nextentity.CronTaskEvent, delay time.Duration) (string, error) {
	message, err := json.Marshal(event)
	if err != nil {
		return "", err
	}
	msgID, err := s.taskCronjobMQ.SendDelayMessageAndGetID(ctx, message, delay, entity.TaskCronJobTag)
	if err != nil {
		log.V1.CtxError(ctx, "failed to send stop workspace event, err: %v", err)
		return "", err
	}

	return msgID, nil
}
