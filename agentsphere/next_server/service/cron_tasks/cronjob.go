package cron_tasks

import (
	"context"
	"math"
	"math/rand"
	"sync"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logid"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/robfig/cron/v3"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	authservice "code.byted.org/devgpt/kiwis/copilotstack/common/auth/service"
)

func (s *Service) StartCron() error {
	// 每10min执行一次
	c := cron.New(cron.WithSeconds(),
		cron.With<PERSON>hain(cron.SkipIfStillRunning(cron.DefaultLogger)))
	_, err := c.AddFunc("0 0/10 * * * ?", func() {
		ctx := ctxvalues.SetLogID(context.Background(), logid.GenLogID())
		logs.V1.CtxDebug(ctx, "Start to handle cron job to scan cron tasks")
		if err := s.ScanTasks(ctx); err != nil {
			logs.V1.CtxError(ctx, "Failed to handle cron job to scan cron tasks", "err", err)
		}
	})
	if err != nil {
		return err
	}
	c.Start()
	return nil
}

// ScanTasks 扫描未执行的定时任务
func (s *Service) ScanTasks(ctx context.Context) error {
	logs.V1.CtxInfo(ctx, "[ScanTasks] start to scan tasks")
	lockKey := "next_server:cron_tasks:cronjob"
	r := rand.New(rand.NewSource(time.Now().UnixNano())) // 使用当前时间作为种子
	lockValue := r.Intn(math.MaxInt)
	ok, err := s.redis.SetNX(ctx, lockKey, lockValue, time.Second*30)
	if err != nil {
		return errors.WithMessage(err, "failed to lock")
	}
	if !ok {
		return errors.New("failed to get lock")
	}
	defer s.redis.Cad(ctx, lockKey, lockValue)
	errLock := sync.Mutex{}
	mulErr := &multierror.Error{}
	g := errgroup.Group{}
	tasks, err := s.cronTaskDAO.GetPendingTasks(ctx, 0)
	if err != nil {
		return errors.WithMessage(err, "failed to get pending tasks")
	}
	for _, task := range tasks {
		tempTask := task
		logs.V1.CtxDebug(ctx, "[ScanTasks] start to scan task %+v", tempTask)
		g.Go(func() error {
			if task == nil {
				log.V1.CtxError(ctx, "cron task is nil")
				return errors.New("cron task is nil")
			}
			if s.CloudOAuthClient == nil {
				log.V1.CtxError(ctx, "cloud oauth client is nil")
				return errors.New("cloud oauth client is nil")
			}
			token, err := s.CloudOAuthClient.GetJwtToken(ctx, tempTask.UserName)
			if err != nil {
				logs.V1.CtxError(ctx, "Failed to get jwt token")
				return err
			}
			payload, err := s.ByteCloudValidator.Validate(ctx, token)
			if err != nil {
				logs.V1.CtxError(ctx, "Failed to validate byte cloud jwt")
				return errors.WithMessage(err, "invalid byte cloud jwt")
			}
			account, err := s.authService.AuthByteCloudJWT(ctx, token)
			if err != nil {
				logs.V1.CtxError(ctx, "Failed to refresh next code jwt")
			}
			if account == nil {
				logs.V1.CtxError(ctx, "Failed to refresh next code jwt")
				return errors.New("failed to refresh next code jwt")
			}
			_, _, _, err = s.DoTask(ctx, task, entity.TriggerTypeDBCompensation, "", tempTask.UID,
				&authentity.Account{
					ID:              payload.EmployeeID,
					Username:        tempTask.UserName,
					Name:            payload.Username,
					Type:            string(payload.Type),
					Department:      payload.Organization,
					Email:           payload.Email,
					AuthOrigin:      string(authservice.AuthOriginByteCloud),
					CodebaseUserJWT: account.CodebaseUserJWT,
					NextCodeUserJWT: account.NextCodeUserJWT,
					CloudUserJWT:    token,
				})
			if err == nil {
				return nil
			}
			logs.V1.CtxError(ctx, "[ScanTasks] failed to doTask, err: %v", err)
			errLock.Lock()
			mulErr = multierror.Append(mulErr, err)
			errLock.Unlock()
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		return errors.WithMessage(err, "failed to do tasks")
	}
	return mulErr.ErrorOrNil()
}
