package cron_tasks

import (
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"fmt"
	"github.com/robfig/cron/v3"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// getCurrentSystemTimezone 通过读取系统时区信息判断当前时区
func getCurrentSystemTimezone() (string, error) {
	// 尝试读取 /etc/timezone
	if content, err := os.ReadFile("/etc/timezone"); err == nil {
		timezone := strings.TrimSpace(string(content))
		if timezone != "" {
			return timezone, nil
		}
	}

	// 尝试读取 /etc/localtime 的软链接
	if linkTarget, err := filepath.EvalSymlinks("/etc/localtime"); err == nil {
		if strings.Contains(linkTarget, "/zoneinfo/") {
			parts := strings.Split(linkTarget, "/zoneinfo/")
			if len(parts) > 1 {
				return parts[1], nil
			}
		}
	}

	// 获取系统默认时区
	now := time.Now()
	zone, _ := now.Zone()
	// 根据时区名称推断
	switch zone {
	case "CST":
		return "Asia/Shanghai", nil
	case "UTC", "GMT":
		return "Etc/UTC", nil
	default:
		// 默认返回UTC
		return "Etc/UTC", nil
	}
}

func cronToNextTime(cronExpr string) (string, nextentity.WeekDay, nextentity.ScheduleType, error) {

	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	schedule, err := parser.Parse(cronExpr)
	if err != nil {
		return "", nextentity.WeekDayUnknown, nextentity.ScheduleTypeUnknown, fmt.Errorf("parse cron err: %v", err)
	}

	// 计算下一次执行时间
	nextTime := schedule.Next(time.Now())

	// 判断频率
	var freq nextentity.ScheduleType
	if strings.Contains(cronExpr, "? *") { // 表达式里有 ? * → 每周
		freq = nextentity.ScheduleTypeByWeek
	} else if strings.Contains(cronExpr, "* * ?") { // 每天
		freq = nextentity.ScheduleTypeByDay
	} else {
		// fallback：通过判断 DOW / DOM 是否含 ?
		if strings.Contains(cronExpr, "?") {
			freq = nextentity.ScheduleTypeByWeek
		} else {
			freq = nextentity.ScheduleTypeByDay
		}
	}

	// 判断weekday
	var w nextentity.WeekDay
	switch nextTime.Weekday() {
	case time.Monday:
		w = nextentity.WeekDayMonday
	case time.Tuesday:
		w = nextentity.WeekDayTuesday
	case time.Wednesday:
		w = nextentity.WeekDayWednesday
	case time.Thursday:
		w = nextentity.WeekDayThursday
	case time.Friday:
		w = nextentity.WeekDayFriday
	case time.Saturday:
		w = nextentity.WeekDaySaturday
	case time.Sunday:
		w = nextentity.WeekDaySunday
	default:
		w = nextentity.WeekDayUnknown
	}

	return nextTime.Format("2006-01-02 15:04:05"), w, freq, nil
}

func cronToDescription(schedule, timezone string) (string, error) {
	// 支持秒的解析器
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)

	// 解析表达式
	_, err := parser.Parse(schedule)
	if err != nil {
		return "", fmt.Errorf("invalid cron expr: %w", err)
	}

	// 拆字段
	fields := strings.Fields(schedule)
	if len(fields) != 6 {
		return "", fmt.Errorf("cron expression must have 6 fields (with seconds)")
	}

	_, minute, hour, dom, _, dow := fields[0], fields[1], fields[2], fields[3], fields[4], fields[5]

	// 加载时区
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return "", fmt.Errorf("invalid timezone: %w", err)
	}
	_, offset := time.Now().In(loc).Zone()
	utcOffset := offset / 3600
	utcStr := fmt.Sprintf("UTC%+d", utcOffset)

	// 格式化 HH:mm
	hhmm := fmt.Sprintf("%s:%s", pad(hour), pad(minute))

	// 判断周期
	if dom == "*" && (dow == "*" || dow == "?") {
		// 每天
		return fmt.Sprintf("每天 %s (%s)", hhmm, utcStr), nil
	} else if dow != "*" {
		// 每周
		weekday := cronWeekday(dow)
		return fmt.Sprintf("每周%s %s (%s)", weekday, hhmm, utcStr), nil
	}

	// 兜底
	return fmt.Sprintf("Cron表达式: %s (%s)", schedule, utcStr), nil
}

func pad(s string) string {
	if len(s) == 1 {
		return "0" + s
	}
	return s
}

func cronWeekday(dow string) string {
	switch strings.ToUpper(dow) {
	case "1", "MON":
		return "一"
	case "2", "TUE":
		return "二"
	case "3", "WED":
		return "三"
	case "4", "THU":
		return "四"
	case "5", "FRI":
		return "五"
	case "6", "SAT":
		return "六"
	case "0", "SUN":
		return "日"
	default:
		return dow
	}
}
