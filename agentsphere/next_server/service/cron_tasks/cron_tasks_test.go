package cron_tasks

import (
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	mockdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/mock/dal"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	deduplicatorlib "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/deduplicator"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/proto"
)

func TestGetNextTime(t *testing.T) {
	testCases := []struct {
		name               string
		cronExpr           string
		userSelectTimezone string
		description        string
		expectError        bool
	}{
		{
			name:               "每30秒执行一次_新加坡",
			cronExpr:           "0/30 * * * * *",
			userSelectTimezone: "Asia/Singapore",
			description:        "每30秒执行一次，新加坡时区",
			expectError:        false,
		},
		{
			name:               "每小时执行一次_圣何塞",
			cronExpr:           "0 0 * * * *",
			userSelectTimezone: "America/Los_Angeles", // 圣何塞在加利福尼亚州，使用洛杉矶时区
			description:        "每小时整点执行，圣何塞时区",
			expectError:        false,
		},
		{
			name:               "每天上午9点_北京",
			cronExpr:           "0 0 9 * * *",
			userSelectTimezone: "Asia/Shanghai", // 北京使用上海时区
			description:        "每天上午9点执行，北京时区",
			expectError:        false,
		},
		{
			name:               "每周一上午10点_纽约",
			cronExpr:           "0 0 10 * * 1",
			userSelectTimezone: "America/New_York",
			description:        "每周一上午10点执行，纽约时区",
			expectError:        false,
		},
		{
			name:               "每月1号凌晨2点_伦敦",
			cronExpr:           "0 0 2 1 * *",
			userSelectTimezone: "Europe/London",
			description:        "每月1号凌晨2点执行，伦敦时区",
			expectError:        false,
		},
		{
			name:               "工作日每15分钟_东京",
			cronExpr:           "0 0/15 9-18 * * 1-5",
			userSelectTimezone: "Asia/Tokyo",
			description:        "工作日(周一到周五)上午9点到下午6点每15分钟执行，东京时区",
			expectError:        false,
		},
		{
			name:               "使用GMT偏移时区",
			cronExpr:           "0 30 14 * * *",
			userSelectTimezone: "Etc/GMT-5", // GMT-5 表示 UTC+5
			description:        "每天下午2:30，使用GMT偏移时区",
			expectError:        false,
		},
		{
			name:               "使用标准GMT偏移_西12区",
			cronExpr:           "0 0 12 * * *",
			userSelectTimezone: "Etc/GMT+12", // GMT+12 表示 UTC-12
			description:        "每天中午12点，西12区时间",
			expectError:        false,
		},
		{
			name:               "空时区应该报错",
			cronExpr:           "0 0 0 * * *",
			userSelectTimezone: "",
			description:        "空时区字符串",
			expectError:        false,
		},
		//{
		//	name:               "无效的cron表达式",
		//	cronExpr:           "invalid cron",
		//	userSelectTimezone: "Asia/Shanghai",
		//	description:        "无效的cron表达式",
		//	expectError:        true,
		//},
		//{
		//	name:               "无效的时区",
		//	cronExpr:           "0 0 12 * * *",
		//	userSelectTimezone: "Invalid/Timezone",
		//	description:        "无效的时区名称",
		//	expectError:        true,
		//},
	}

	s := NewService(CreateServiceOption{})
	t.Logf("Cron表达式时区处理和下次执行时间测试")
	ctx := context.Background()

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Logf("\n=== 测试用例: %s ===", tc.name)
			t.Logf("Cron表达式: %s", tc.cronExpr)
			t.Logf("用户时区: %s", tc.userSelectTimezone)
			t.Logf("描述: %s", tc.description)

			// 记录测试开始时间
			testStartTime := time.Now()
			t.Logf("测试开始时间: %s", testStartTime.Format("2006-01-02 15:04:05 MST"))

			// 调用新的getNextTime方法
			timestamp, duration, err := s.getNextTime(ctx, tc.cronExpr, tc.userSelectTimezone)

			if tc.expectError {
				if err == nil {
					t.Errorf("❌ 预期应该有错误，但没有错误发生")
				} else {
					t.Logf("✅ 预期错误发生: %v", err)
				}
				return
			}

			if err != nil {
				t.Errorf("❌ 意外错误: %v", err)
				return
			}

			// 验证返回值
			if timestamp <= 0 {
				t.Errorf("❌ Unix时间戳应该大于0，实际值: %d", timestamp)
				return
			}

			if duration <= 0 {
				t.Errorf("❌ 执行间隔应该大于0，实际值: %v", duration)
				return
			}

			// 获取系统时区来显示结果
			systemTimezone, _ := getCurrentSystemTimezone()
			systemLoc, _ := time.LoadLocation(systemTimezone)

			// 在系统时区显示下次执行时间
			nextTime := time.Unix(timestamp, 0).In(systemLoc)

			// 如果用户时区有效，也在用户时区显示时间
			var userNextTime time.Time
			if tc.userSelectTimezone != "" {
				if userLoc, err := time.LoadLocation(tc.userSelectTimezone); err == nil {
					userNextTime = time.Unix(timestamp, 0).In(userLoc)
				}
			}

			t.Logf("✅ 测试结果:")
			t.Logf("   系统时区(%s)下次执行时间: %s", systemTimezone, nextTime.Format("2006-01-02 15:04:05 MST"))
			if !userNextTime.IsZero() {
				t.Logf("   用户时区(%s)下次执行时间: %s", tc.userSelectTimezone, userNextTime.Format("2006-01-02 15:04:05 MST"))
			}
			t.Logf("   Unix时间戳: %d", timestamp)
			t.Logf("   预期执行间隔: %s", formatDuration(duration))
			t.Logf("   预期执行间隔(原始): %v", duration)

			// 验证时间逻辑正确性
			expectedNextTime := testStartTime.Add(duration)
			actualNextTime := time.Unix(timestamp, 0)

			// 允许1秒的误差（考虑到测试执行时间）
			timeDiff := actualNextTime.Sub(expectedNextTime)
			if timeDiff < 0 {
				timeDiff = -timeDiff
			}
			if timeDiff > time.Second {
				t.Logf("⚠️  时间计算可能存在问题:")
				t.Logf("   测试开始时间 + 执行间隔 = %s", expectedNextTime.Format("2006-01-02 15:04:05"))
				t.Logf("   实际下次执行时间 = %s", actualNextTime.Format("2006-01-02 15:04:05"))
				t.Logf("   时间差: %v", timeDiff)
			}

			t.Logf("--- 测试用例 %s 完成 ---\n", tc.name)
		})
	}
}

// 辅助函数：格式化时间差显示（从之前的代码复制）
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0f秒", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.1f分钟", d.Minutes())
	} else if d < 24*time.Hour {
		return fmt.Sprintf("%.1f小时", d.Hours())
	} else {
		days := d.Hours() / 24
		hours := d.Hours() - (days * 24)
		return fmt.Sprintf("%.0f天%.1f小时", days, hours)
	}
}

// 基准测试：测试不同时区的性能
func BenchmarkGetNextTime(b *testing.B) {
	s := NewService(CreateServiceOption{})
	ctx := context.Background()

	testCases := []struct {
		name     string
		cronExpr string
		timezone string
	}{
		{"Asia/Shanghai", "0 0 12 * * *", "Asia/Shanghai"},
		{"America/New_York", "0 0 12 * * *", "America/New_York"},
		{"Europe/London", "0 0 12 * * *", "Europe/London"},
		{"Etc/GMT+8", "0 0 12 * * *", "Etc/GMT+8"},
	}

	for _, tc := range testCases {
		b.Run(tc.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				_, _, err := s.getNextTime(ctx, tc.cronExpr, tc.timezone)
				if err != nil {
					b.Fatalf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// 表格驱动测试：验证特殊时区转换
func TestTimezoneConversion(t *testing.T) {
	s := NewService(CreateServiceOption{})
	ctx := context.Background()

	tests := []struct {
		name         string
		cronExpr     string
		userTimezone string
		expectedHour int // 期望在用户时区的小时数
	}{
		{
			name:         "GMT+8转换测试",
			cronExpr:     "0 0 14 * * *", // 14:00
			userTimezone: "Etc/GMT-8",    // UTC+8
			expectedHour: 14,
		},
		{
			name:         "GMT-5转换测试",
			cronExpr:     "0 0 9 * * *", // 09:00
			userTimezone: "Etc/GMT+5",   // UTC-5
			expectedHour: 9,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			timestamp, duration, err := s.getNextTime(ctx, tt.cronExpr, tt.userTimezone)
			if err != nil {
				t.Fatalf("获取下次执行时间失败: %v", err)
			}

			// 验证返回的时间戳在用户时区中的小时是否正确
			userLoc, _ := time.LoadLocation(tt.userTimezone)
			userTime := time.Unix(timestamp, 0).In(userLoc)

			if userTime.Hour() != tt.expectedHour {
				t.Errorf("时区转换错误: 期望小时 %d，实际小时 %d",
					tt.expectedHour, userTime.Hour())
			}

			t.Logf("✅ 时区转换正确:")
			t.Logf("   用户时区时间: %s", userTime.Format("2006-01-02 15:04:05 MST"))
			t.Logf("   执行间隔: %v", duration)
		})
	}
}

func TestCronToDescription(t *testing.T) {
	// 每周一 10:00 (UTC+8)
	ex1, _ := cronToDescription("0 0 10 ? * 2", "Etc/GMT-8")
	t.Logf("%v", ex1)

	ex2, _ := cronToDescription("0 0 15 * * ?", "Etc/GMT-8")
	// 每天 15:00 (UTC+8)
	t.Logf("%v", ex2)
}

// Mock RocketMQ Client
type MockRocketMQClient struct{}

func (m *MockRocketMQClient) SendMessage(ctx context.Context, message []byte, tag string) error {
	return nil
}

func (m *MockRocketMQClient) SendBatchMessage(ctx context.Context, messages [][]byte, tag string) error {
	return nil
}

func (m *MockRocketMQClient) SendBatchDelayedMessage(ctx context.Context, messages [][]byte, delay time.Duration, tag string) error {
	return nil
}

func (m *MockRocketMQClient) SendDelayMessageAndGetID(ctx context.Context, message []byte, delay time.Duration, tag string) (string, error) {
	return "mock-mq-event-id", nil
}

func (m *MockRocketMQClient) SendDelayedMessage(ctx context.Context, message []byte, delay time.Duration, tag string) error {
	return nil
}

func (m *MockRocketMQClient) SendOrderlyMessage(ctx context.Context, message []byte, partitionKey string, tag string) error {
	return nil
}

func (m *MockRocketMQClient) SendBatchOrderlyMessage(ctx context.Context, messages [][]byte, partitionKey string, tag string) error {
	return nil
}

func (m *MockRocketMQClient) RegisterHandler(handler func(ctx context.Context, message []byte) error) {
	// Mock implementation
}

func (m *MockRocketMQClient) RegisterHandlerV2(handler func(ctx context.Context, message []byte, ext *proto.MessageExt) error) {
	// Mock implementation
}

func (m *MockRocketMQClient) RegisterDeduplicator(deduplicator deduplicatorlib.IDeduplicator) {
	// Mock implementation
}

func (m *MockRocketMQClient) StartConsumer() error {
	return nil
}

func (m *MockRocketMQClient) Close() error {
	return nil
}

// 测试工具函数
func createTestService(t *testing.T) (*Service, *mockdal.MockCronTaskDAO, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockDAO := mockdal.NewMockCronTaskDAO(ctrl)

	service := &Service{
		cronTaskDAO:   mockDAO,
		idGen:         uuid.GetDefaultGenerator(nil),
		taskCronjobMQ: &MockRocketMQClient{}, // 添加Mock的RocketMQ客户端
	}

	return service, mockDAO, ctrl
}

// 创建测试用的CronTask实体
func createTestCronTask() *entity.CronTask {
	return &entity.CronTask{
		ID:          1,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		UserName:    "test_user",
		UID:         "test-uid-123",
		Name:        "测试任务",
		Description: "这是一个测试任务",
		TaskType:    entity.TaskTypeAimeBot,
		TemplateID:  "100",
		// Params字段在当前entity中不存在，已移除
		Schedule:        "0 0 9 * * *", // 每天上午9点
		Timezone:        "Asia/Shanghai",
		NextExecuteTime: time.Now().Add(time.Hour),
		LastRunStatus:   entity.LastRunStatusUnknown,
		TaskStatus:      entity.CronTaskStatusRunning,
		// GroupID和AgentType字段在当前entity中不存在，已移除
	}
}

// TestService_GetCronTask 测试获取定时任务
func TestService_GetCronTask(t *testing.T) {
	tests := []struct {
		name        string
		uid         string
		mockSetup   func(*mockdal.MockCronTaskDAO)
		expectedErr error
		expected    *entity.CronTask
	}{
		{
			name: "成功获取任务",
			uid:  "test-uid-123",
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO) {
				task := createTestCronTask()
				mockDAO.EXPECT().GetCronTask(gomock.Any(), dal.GetCronTaskOption{
					UID: "test-uid-123",
				}).Return(task, nil)
			},
			expected: createTestCronTask(),
		},
		{
			name: "任务不存在",
			uid:  "nonexistent-uid",
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO) {
				mockDAO.EXPECT().GetCronTask(gomock.Any(), dal.GetCronTaskOption{
					UID: "nonexistent-uid",
				}).Return(nil, nil)
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, mockDAO, ctrl := createTestService(t)
			defer ctrl.Finish()

			tt.mockSetup(mockDAO)

			ctx := context.Background()
			result, err := service.GetCronTask(ctx, tt.uid)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
				if tt.expected != nil {
					assert.NotNil(t, result) // 检查返回的任务不为空
					assert.Equal(t, tt.expected.UID, result.UID)
					assert.Equal(t, tt.expected.Name, result.Name)
				} else {
					assert.Nil(t, result)
				}
			}
		})
	}
}

// TestService_CreateCronTask 测试创建定时任务
func TestService_CreateCronTask(t *testing.T) {
	tests := []struct {
		name        string
		option      CreateCronTaskOption
		mockSetup   func(*mockdal.MockCronTaskDAO, *Service)
		expectedErr error
	}{
		{
			name: "成功创建任务",
			option: CreateCronTaskOption{
				Username:    "test_user",
				TaskName:    "测试任务",
				Description: "这是一个测试任务",
				TaskType:    entity.TaskTypeAimeBot,
				TemplateID:  "100",
				// Params字段在当前结构中不存在，已移除
				Schedule: "0 0 9 * * *",
				Timezone: "Asia/Shanghai",
				// 注意：在Service层CreateCronTaskOption中，TaskStatus字段实际上应该是CronTaskStatus类型
				// 但由于类型定义问题，这里暂时不设置TaskStatus字段
				// TaskStatus: entity.CronTaskStatusRunning,
				// GroupID和AgentType字段在CreateCronTaskOption中不存在，已移除
			},
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO, service *Service) {
				// Mock创建任务成功
				mockDAO.EXPECT().CreateCronTask(gomock.Any(), gomock.Any()).Return(createTestCronTask(), nil)
			},
		},
		{
			name: "数据库创建失败",
			option: CreateCronTaskOption{
				Username: "test_user",
				TaskName: "测试任务",
				Schedule: "0 0 9 * * *",
				Timezone: "Asia/Shanghai",
				// 注意：在Service层CreateCronTaskOption中，TaskStatus字段实际上应该是CronTaskStatus类型
				// 但由于类型定义问题，这里暂时不设置TaskStatus字段
				// TaskStatus: entity.CronTaskStatusRunning,
			},
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO, service *Service) {
				mockDAO.EXPECT().CreateCronTask(gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))
			},
			expectedErr: errors.New("failed to create cron task"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, mockDAO, ctrl := createTestService(t)
			defer ctrl.Finish()

			tt.mockSetup(mockDAO, service)

			ctx := context.Background()
			err := service.CreateCronTask(ctx, tt.option)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestService_DeleteCronTask 测试删除定时任务
func TestService_DeleteCronTask(t *testing.T) {
	tests := []struct {
		name        string
		uid         string
		user        *authentity.Account
		mockSetup   func(*mockdal.MockCronTaskDAO)
		expectedErr error
	}{
		{
			name: "成功删除任务",
			uid:  "test-uid-123",
			user: &authentity.Account{Username: "test_user"},
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO) {
				task := createTestCronTask()
				mockDAO.EXPECT().GetCronTask(gomock.Any(), dal.GetCronTaskOption{
					UID: "test-uid-123",
				}).Return(task, nil)
				mockDAO.EXPECT().DeleteCronTask(gomock.Any(), "test-uid-123").Return(nil)
			},
		},
		{
			name: "任务不存在",
			uid:  "nonexistent-uid",
			user: &authentity.Account{Username: "test_user"},
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO) {
				mockDAO.EXPECT().GetCronTask(gomock.Any(), dal.GetCronTaskOption{
					UID: "nonexistent-uid",
				}).Return(nil, nil)
			},
			expectedErr: ErrCronTaskNotFound,
		},
		{
			name: "获取任务时数据库错误",
			uid:  "test-uid-123",
			user: &authentity.Account{Username: "test_user"},
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO) {
				mockDAO.EXPECT().GetCronTask(gomock.Any(), dal.GetCronTaskOption{
					UID: "test-uid-123",
				}).Return(nil, errors.New("database error"))
			},
			expectedErr: errors.New("failed to get cron task"),
		},
		{
			name: "删除任务时数据库错误",
			uid:  "test-uid-123",
			user: &authentity.Account{Username: "test_user"},
			mockSetup: func(mockDAO *mockdal.MockCronTaskDAO) {
				task := createTestCronTask()
				mockDAO.EXPECT().GetCronTask(gomock.Any(), dal.GetCronTaskOption{
					UID: "test-uid-123",
				}).Return(task, nil)
				mockDAO.EXPECT().DeleteCronTask(gomock.Any(), "test-uid-123").Return(errors.New("delete error"))
			},
			expectedErr: errors.New("failed to delete cron task"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, mockDAO, ctrl := createTestService(t)
			defer ctrl.Finish()

			tt.mockSetup(mockDAO)

			ctx := context.Background()
			err := service.DeleteCronTask(ctx, tt.uid, tt.user)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				if tt.expectedErr == ErrCronTaskNotFound {
					// 对于ErrCronTaskNotFound，检查错误消息是否包含"cron task not found"
					assert.Contains(t, err.Error(), "cron task not found")
				} else {
					assert.Contains(t, err.Error(), tt.expectedErr.Error())
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestService_getNextTime 测试计算下次执行时间的边界情况
func TestService_getNextTime(t *testing.T) {
	tests := []struct {
		name         string
		cronExpr     string
		userLocation string
		expectError  bool
		errorMsg     string
	}{
		{
			name:         "有效的Cron表达式",
			cronExpr:     "0 0 9 * * *",
			userLocation: "",
			expectError:  false,
		},
		{
			name:         "复杂的Cron表达式",
			cronExpr:     "0 0/15 9-18 * * 1-5", // 工作日每15分钟
			userLocation: "",
			expectError:  false,
		},
		{
			name:         "秒级Cron表达式",
			cronExpr:     "0/30 * * * * *", // 每30秒
			userLocation: "",
			expectError:  false,
		},
	}

	service, _, ctrl := createTestService(t)
	defer ctrl.Finish()

	ctx := context.Background()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			timestamp, duration, err := service.getNextTime(ctx, tt.cronExpr, tt.userLocation)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.Greater(t, timestamp, int64(0))
				// 验证时间戳是未来时间
				assert.Greater(t, timestamp, time.Now().Unix())
				// 验证duration也是正数
				assert.Greater(t, duration, time.Duration(0))
			}
		})
	}
}

// TestService_sendMQEvent 测试发送MQ事件（需要Mock RocketMQ）
func TestService_sendMQEvent(t *testing.T) {
	tests := []struct {
		name        string
		event       *entity.CronTaskEvent
		delay       time.Duration
		mockSetup   func(*Service)
		expectError bool
	}{
		{
			name: "成功发送MQ事件",
			event: &entity.CronTaskEvent{
				CronTaskUniqueID: "test-uid-123", // 修正为string类型
			},
			delay: time.Minute * 5,
			mockSetup: func(service *Service) {
				// Mock RocketMQ客户端已经在createTestService中设置
			},
			expectError: false, // 修改为false，因为Mock会成功
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, _, ctrl := createTestService(t)
			defer ctrl.Finish()

			if tt.mockSetup != nil {
				tt.mockSetup(service)
			}

			ctx := context.Background()
			msgID, err := service.sendMQEvent(ctx, tt.event, tt.delay)

			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, msgID)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, msgID)
			}
		})
	}
}
