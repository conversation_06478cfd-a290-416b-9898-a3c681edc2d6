package agent

import (
	"context"
	"fmt"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	poolsdk "github.com/sourcegraph/conc/pool"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

type AgentBusinessError struct {
	Message string
}

func (e *AgentBusinessError) Error() string {
	return e.Message
}

func newAgentBusinessError(message string) *AgentBusinessError {
	return &AgentBusinessError{Message: message}
}

var (
	ErrAgentNotFound                             = newAgentBusinessError("agent not found")
	ErrAgentConfigNotFound                       = newAgentBusinessError("agent config not found")
	ErrAgentConfigExists                         = newAgentBusinessError("agent config exists")
	ErrAgentConfigBaseTypeExists                 = newAgentBusinessError("agent config base type exists")
	ErrAgentConfigVersionNotFound                = newAgentBusinessError("agent config version not found")
	ErrAgentConfigNotAllowedDeploy               = newAgentBusinessError("agent config not allowed deploy")
	ErrAgentConfigNotAllowedCopy                 = newAgentBusinessError("agent config not allowed copy from other env")
	ErrAgentConfigBaseTypeNotAllowedUpdate       = newAgentBusinessError("agent config base type not allowed update")
	ErrAgentConfigBaseTypeNotAllowedBinarySource = newAgentBusinessError("agent config base type not allowed binary source")
	ErrAgentConfigBaseTypeNotAllowedImage        = newAgentBusinessError("agent config base type not allowed image")
	ErrAgentConfigBaseTypeNotAllowedBashImage    = newAgentBusinessError("agent config base type not allowed bash image")
	ErrAgentConfigNotAllowedUseOtherPrompt       = newAgentBusinessError("agent config not allowed use other prompt")
	ErrAgentConfigNotAllowedUseDisablePrompt     = newAgentBusinessError("agent config not allowed use disable prompt")
	ErrAgentConfigPromptNotExists                = newAgentBusinessError("agent config prompt not exists")
	ErrKnowledgesetVersionNotOnline              = newAgentBusinessError("knowledgeset version status should be online")
	ErrAgentConfigNotAllowedUseSameKnowledgeset  = newAgentBusinessError("agent config not allowed use same version knowledgeset")
	ErrBinarySourceVersionNotExists              = newAgentBusinessError("binary source version not exists")
	ErrBinarySourceVersionNotOnline              = newAgentBusinessError("binary source version not online")
	ErrBinarySourceVersionNotOnlineOrTest        = newAgentBusinessError("binary source version not online or test")
	ErrAgentConfigVersionBinarySourceInvalid     = newAgentBusinessError("binary source invalid")
	ErrImageVersionNotExists                     = newAgentBusinessError("image version not exists")
	ErrAgentConfigVersionUpdated                 = newAgentBusinessError("agent config version has updated by others")
)

type Service struct {
	dao                    *dal.DAO
	idGen                  uuid.Generator
	tccConf                *config.AgentSphereTCCConfig
	larkService            *larkservice.Service
	agentDeployRelationDAO dal.AgentDeployRelationDAO
	knowledgeSetVersionDao dal.KnowledgesetVersionDAO
	knowledgesetDao        dal.KnowledgesetDAO
	permissionService      *permissionservice.Service
	scmService             serverservice.ScmOpenAPI
	icmService             serverservice.IcmOpenAPI
	conf                   *config.AgentSphereConfig
}

type CreateServiceOption struct {
	fx.In
	DAO                    *dal.DAO
	TccConf                *config.AgentSphereTCCConfig
	LarkService            *larkservice.Service
	AgentDeployRelationDAO dal.AgentDeployRelationDAO
	KnowledgeSetVersionDao dal.KnowledgesetVersionDAO
	KnowledgesetDao        dal.KnowledgesetDAO
	PermissionService      *permissionservice.Service
	ScmService             serverservice.ScmOpenAPI
	IcmService             serverservice.IcmOpenAPI
	Conf                   *config.AgentSphereConfig
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		idGen:                  uuid.GetDefaultGenerator(nil),
		dao:                    opt.DAO,
		tccConf:                opt.TccConf,
		larkService:            opt.LarkService,
		agentDeployRelationDAO: opt.AgentDeployRelationDAO,
		knowledgeSetVersionDao: opt.KnowledgeSetVersionDao,
		knowledgesetDao:        opt.KnowledgesetDao,
		permissionService:      opt.PermissionService,
		scmService:             opt.ScmService,
		icmService:             opt.IcmService,
		conf:                   opt.Conf,
	}
	return s, nil
}

type CreateAgentOption struct {
	Creator     string
	Name        string
	Description string
}

func (s *Service) CreateAgent(ctx context.Context, opt CreateAgentOption) (*entity.Agent, error) {

	agent, err := s.dao.CreateAgent(ctx, dal.CreateAgentOption{
		ID:          s.idGen.NewID(),
		Creator:     opt.Creator,
		Name:        opt.Name,
		Description: opt.Description,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed create agent")
	}

	// 创建agent resouce
	_, err = s.permissionService.CreateResource(ctx, permissionservice.CreateResourceOption{
		Owner:      opt.Creator,
		Type:       entity.ResourceTypeAgent,
		ExternalID: agent.ID,
		Status:     pointer.To(entity.ResourceStatusPublic),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed create resource")
	}

	return agent, nil
}

type GetAgentOption struct {
	ID   string
	Sync bool
}

func (s *Service) GetAgent(ctx context.Context, opt GetAgentOption) (*entity.Agent, error) {
	agent, err := s.dao.GetAgent(ctx, dal.GetAgentOption{
		ID:   opt.ID,
		Sync: opt.Sync,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent")
	}
	return agent, nil
}

type UpdateAgentOption struct {
	ID          string
	Name        *string
	Description *string
}

func (s *Service) UpdateAgent(ctx context.Context, opt UpdateAgentOption) (*entity.Agent, error) {
	_, err := s.dao.GetAgent(ctx, dal.GetAgentOption{ID: opt.ID})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent")
	}

	return s.dao.UpdateAgent(ctx, dal.UpdateAgentOption{
		ID:          opt.ID,
		Name:        opt.Name,
		Description: opt.Description,
	})
}

type DeleteAgentOption struct {
	ID string
}

func (s *Service) DeleteAgent(ctx context.Context, opt DeleteAgentOption) error {
	count, err := s.dao.CountAgentConfigs(ctx, dal.CountAgentConfigOption{
		AgentID: &opt.ID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed count agent config")
	}
	if count != 0 {
		return ErrAgentConfigExists
	}

	err = s.dao.DeleteAgent(ctx, opt.ID)
	if err != nil {
		return errors.WithMessagef(err, "failed to delete agent %s", opt.ID)
	}
	return nil
}

type ListAgentsOption struct {
	Creator *string
	Name    *string
	Offset  int
	Limit   int
}

func (s *Service) ListAgents(ctx context.Context, opt ListAgentsOption) (int64, []*entity.Agent, error) {
	total, agents, err := s.dao.ListAgents(ctx, dal.ListAgentOption{
		Creator: opt.Creator,
		Name:    opt.Name,
		Limit:   opt.Limit,
		Offset:  opt.Offset,
	})
	if err != nil {
		return 0, nil, errors.WithMessage(err, "failed list agents")
	}
	return total, agents, nil
}

type CreateAgentConfigOption struct {
	ID          string
	AgentID     string
	Type        entity.AgentConfigType
	Creator     string
	Name        string
	Description string
}

func (s *Service) CreateAgentConfig(ctx context.Context, opt CreateAgentConfigOption) (*entity.AgentConfig, error) {
	_, err := s.dao.GetAgent(ctx, dal.GetAgentOption{ID: opt.AgentID})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent")
	}
	// 检查是否存在 base 配置，需要保证只存在一个 base 配置
	if opt.Type == entity.AgentConfigTypeBase {
		count, err := s.dao.CountAgentConfigs(ctx, dal.CountAgentConfigOption{
			AgentID: &opt.AgentID,
			Type:    lo.ToPtr(entity.AgentConfigTypeBase),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed count agent config")
		}
		if count != 0 {
			return nil, ErrAgentConfigBaseTypeExists
		}
	}

	return s.dao.CreateAgentConfig(ctx, dal.CreateAgentConfigOption{
		ID:          s.idGen.NewID(),
		AgentID:     opt.AgentID,
		Type:        opt.Type,
		Creator:     opt.Creator,
		Name:        opt.Name,
		Description: opt.Description,
	})
}

type UpdateAgentConfigOption struct {
	ID          string
	Name        *string
	Description *string
}

func (s *Service) UpdateAgentConfig(ctx context.Context, opt UpdateAgentConfigOption) (*entity.AgentConfig, error) {
	_, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{ID: &opt.ID})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentConfigNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent config")
	}

	return s.dao.UpdateAgentConfig(ctx, dal.UpdateAgentConfigOption{
		ID:          opt.ID,
		Name:        opt.Name,
		Description: opt.Description,
	})
}

type GetAgentConfigOption struct {
	ID      string
	Sync    bool
	Version *int32
	Latest  *bool
	Online  *bool
}

func (s *Service) GetAgentConfig(ctx context.Context, opt GetAgentConfigOption) (*entity.AgentConfig, *entity.AgentConfigVersion, error) {
	var err error
	var agentConfigVersion *entity.AgentConfigVersion
	if opt.Version != nil {
		agentConfigVersion, err = s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
			AgentConfigID: &opt.ID,
			Sync:          opt.Sync,
			Version:       lo.ToPtr(int(*opt.Version)),
		})
	} else if opt.Latest != nil {
		agentConfigVersion, err = s.dao.GetLatestAgentConfigVersion(ctx, dal.GetLatestAgentConfigVersionOption{
			AgentConfigID: opt.ID,
		})
	} else if opt.Online != nil {
		agentConfigVersion, err = s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
			AgentConfigID: &opt.ID,
			Sync:          opt.Sync,
			Status:        lo.ToPtr(entity.AgentConfigStatusOnline),
		})
	}
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, ErrAgentConfigVersionNotFound
		}
		return nil, nil, errors.WithMessage(err, "failed get agent config version")
	}

	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		ID:   &opt.ID,
		Sync: opt.Sync,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, ErrAgentConfigNotFound
		}
		return nil, nil, errors.WithMessage(err, "failed get agent config")
	}
	return agentConfig, agentConfigVersion, nil
}

type DeleteAgentConfigOption struct {
	ID string
}

func (s *Service) DeleteAgentConfig(ctx context.Context, opt DeleteAgentConfigOption) error {
	// TODO: 检查是不是存在线上版本
	err := s.dao.DeleteAgentConfig(ctx, opt.ID)
	if err != nil {
		return errors.WithMessagef(err, "failed to delete agent config %s", opt.ID)
	}
	return nil
}

type ListAgentConfigsOption struct {
	AgentID *string
	Creator *string
	Name    *string
	Type    []*entity.AgentConfigType
	Offset  int
	Limit   int
}

func (s *Service) ListAgentConfigs(ctx context.Context, opt ListAgentConfigsOption) (int64, []*entity.AgentConfig, error) {
	total, agents, err := s.dao.ListAgentConfigs(ctx, dal.ListAgentConfigOption{
		AgentID: opt.AgentID,
		Creator: opt.Creator,
		Type:    opt.Type,
		Name:    opt.Name,
		Offset:  opt.Offset,
		Limit:   opt.Limit,
	})
	if err != nil {
		return 0, nil, errors.WithMessage(err, "failed list agent configs")
	}

	// 第一页保证 base 配置一定返回
	if opt.Offset == 0 && opt.Type == nil {
		hasBaseConfig := lo.ContainsBy(agents, func(agent *entity.AgentConfig) bool {
			return agent.Type == entity.AgentConfigTypeBase
		})

		if !hasBaseConfig {
			baseConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
				AgentID: opt.AgentID,
				Type:    lo.ToPtr(entity.AgentConfigTypeBase),
			})

			if err != nil {
				return 0, nil, errors.WithMessage(err, "failed get base agent config")
			}
			agents = append([]*entity.AgentConfig{baseConfig}, agents...)
		}
	}

	return total, agents, nil
}

type CreateAgentConfigVersionOption struct {
	AgentConfigID      string
	Description        string
	Creator            string
	Enabled            bool
	Status             entity.AgentConfigStatus
	RuntimeConfig      entity.RuntimeConfig
	CustomConfig       entity.CustomConfig
	PromptConfig       entity.PromptConfig
	KnowledgesetConfig entity.KnowledgesetConfig
}

func (s *Service) CreateAgentConfigVersion(ctx context.Context, opt CreateAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		ID: &opt.AgentConfigID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentConfigNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent config")
	}
	// 不允许不填，或者填 latest 版本
	isOnline := agentConfig.Type == entity.AgentConfigTypeBase && opt.RuntimeConfig.Type == entity.RuntimeConfigTypeStratoCube
	if isOnline {
		if opt.RuntimeConfig.BinarySource == nil || strings.Contains(pointer.GetString(opt.RuntimeConfig.BinarySource), "latest") {
			return nil, ErrAgentConfigBaseTypeNotAllowedBinarySource
		}
		if opt.RuntimeConfig.Image == nil || strings.Contains(pointer.GetString(opt.RuntimeConfig.Image), "latest") {
			return nil, ErrAgentConfigBaseTypeNotAllowedImage
		}
		if opt.RuntimeConfig.BashImage == nil || strings.Contains(pointer.GetString(opt.RuntimeConfig.BashImage), "latest") {
			return nil, ErrAgentConfigBaseTypeNotAllowedBashImage
		}
	}
	// 校验 binarysource version
	if opt.RuntimeConfig.BinarySource != nil && opt.RuntimeConfig.Type == entity.RuntimeConfigTypeStratoCube {
		// 用正则表达式从  binarysource 提取版本，匹配 version= 之后的内容， scm://devgpt/agentsphere/runtime?version=1.0.0.1331
		version := regexp.MustCompile(`version=(.+)`).FindStringSubmatch(*opt.RuntimeConfig.BinarySource)
		if len(version) < 2 {
			return nil, ErrAgentConfigVersionBinarySourceInvalid
		}
		if err := s.checkBinaryVersion(ctx, version[1], agentConfig.Type); err != nil {
			return nil, err
		}
	}
	// 校验 Image version
	if opt.RuntimeConfig.Image != nil {
		if err := s.checkImageVersion(ctx, *opt.RuntimeConfig.Image, agentConfig.Type); err != nil {
			return nil, err
		}
	}

	if len(opt.PromptConfig.Prompts) != 0 {
		if err := s.CheckPromptConfig(ctx, isOnline, agentConfig.AgentID, opt.PromptConfig); err != nil {
			return nil, err
		}
	}
	if len(opt.KnowledgesetConfig.Knowledgesets) > 0 {
		if err = s.checkKnowledgesetConfig(ctx, opt.KnowledgesetConfig); err != nil {
			return nil, err
		}
	}

	latestAgentConfigVersion, err := s.dao.GetLatestAgentConfigVersion(ctx, dal.GetLatestAgentConfigVersionOption{
		AgentConfigID: opt.AgentConfigID,
	})
	version := 0
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.WithMessage(err, "failed get latest agent config version")
	}
	if latestAgentConfigVersion != nil {
		version = latestAgentConfigVersion.Version
	}
	agentConfigVersion, err := s.dao.CreateAgentConfigVersion(ctx, dal.CreateAgentConfigVersionOption{
		ID:                 s.idGen.NewID(),
		AgentConfigID:      opt.AgentConfigID,
		Description:        opt.Description,
		Creator:            opt.Creator,
		Version:            version + 1,
		Enable:             opt.Enabled,
		Status:             opt.Status,
		RuntimeConfig:      opt.RuntimeConfig,
		CustomConfig:       opt.CustomConfig,
		PromptConfig:       opt.PromptConfig,
		KnowledgesetConfig: opt.KnowledgesetConfig,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed create agent config version")
	}

	return agentConfigVersion, nil
}

type GetAgentConfigVersionOption struct {
	AgentConfigVersionID *string
}

func (s *Service) GetAgentConfigVersion(ctx context.Context, opt GetAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
		ID: opt.AgentConfigVersionID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentConfigVersionNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent config version")
	}

	if len(agentConfigVersion.KnowledgesetConfig.Knowledgesets) == 0 {
		return agentConfigVersion, nil
	}
	// 获取知识集以及知识集版本相关信息
	g := poolsdk.New().WithErrors()
	knowledgesetIDs := []string{}
	knowledgesetVersionIDs := []string{}
	for _, k := range agentConfigVersion.KnowledgesetConfig.Knowledgesets {
		knowledgesetIDs = append(knowledgesetIDs, k.KnowledgesetID)
		knowledgesetVersionIDs = append(knowledgesetVersionIDs, k.KnowledgesetVersionID)
	}
	// 获取知识集信息
	g.Go(func() error {
		knowledgesets, err := s.knowledgesetDao.GetKnowledgesets(ctx, knowledgesetIDs)
		if err != nil {
			return err
		}
		agentConfigVersion.Knowledgesets = knowledgesets
		return nil
	})

	// 获取知识集版本信息
	g.Go(func() error {
		knowledgesetVersions, err := s.knowledgeSetVersionDao.GetKnowledgesetVersions(ctx, knowledgesetVersionIDs)
		if err != nil {
			return err
		}
		agentConfigVersion.KnowledgesetVersions = knowledgesetVersions
		return nil
	})
	if err := g.Wait(); err != nil {
		return nil, err
	}

	return agentConfigVersion, nil
}

type UpdateAgentConfigVersionOption struct {
	ID                 string
	Description        *string
	Enabled            *bool
	RuntimeConfig      *entity.RuntimeConfig
	CustomConfig       *entity.CustomConfig
	PromptConfig       *entity.PromptConfig
	KnowledgesetConfig *entity.KnowledgesetConfig
	UpdatedAt          *string
}

func (s *Service) UpdateAgentConfigVersion(ctx context.Context, opt UpdateAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
		ID:   &opt.ID,
		Sync: true,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentConfigVersionNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent config version")
	}
	// 比较时间是否有更新
	var updatedAt *time.Time
	if opt.UpdatedAt != nil {
		update, err := time.Parse(time.RFC3339, *opt.UpdatedAt)
		if err != nil {
			return nil, errors.WithMessage(err, "failed parse updated at")
		}
		if update.Before(agentConfigVersion.UpdatedAt) {
			return nil, ErrAgentConfigVersionUpdated
		}
		updatedAt = &update
	}

	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{ID: &agentConfigVersion.AgentConfigID})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentConfigNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent config")
	}
	isStratoCube := agentConfigVersion.RuntimeConfig.Type == entity.RuntimeConfigTypeStratoCube
	var isOnline bool
	if agentConfig.Type == entity.AgentConfigTypeBase {
		// 如果是 disable 状态允许更新
		if agentConfigVersion.Enabled {
			if opt.Enabled != nil && !*opt.Enabled {
				// 不允许 base 从 enable 更新为 disable
				return nil, ErrAgentConfigBaseTypeNotAllowedUpdate
			}
			if opt.RuntimeConfig != nil || opt.CustomConfig != nil || opt.PromptConfig != nil {
				return nil, ErrAgentConfigBaseTypeNotAllowedUpdate
			}
		} else if opt.RuntimeConfig != nil && opt.RuntimeConfig.Type == entity.RuntimeConfigTypeStratoCube {
			// 更新为 stratocube
			isOnline = true
			if opt.RuntimeConfig.BinarySource == nil || strings.Contains(pointer.GetString(opt.RuntimeConfig.BinarySource), "latest") {
				return nil, ErrAgentConfigBaseTypeNotAllowedBinarySource
			}
			if opt.RuntimeConfig.Image == nil || strings.Contains(pointer.GetString(opt.RuntimeConfig.Image), "latest") {
				return nil, ErrAgentConfigBaseTypeNotAllowedImage
			}
			if opt.RuntimeConfig.BashImage == nil || strings.Contains(pointer.GetString(opt.RuntimeConfig.BashImage), "latest") {
				return nil, ErrAgentConfigBaseTypeNotAllowedBashImage
			}
		}
		// 原本是 stratocube
		if isStratoCube {
			isOnline = true
		}
		// 更新为非 stratocube
		if opt.RuntimeConfig != nil && opt.RuntimeConfig.Type != entity.RuntimeConfigTypeStratoCube {
			isOnline = false
		}
	}

	if opt.RuntimeConfig != nil {
		if opt.RuntimeConfig.BinarySource != nil && opt.RuntimeConfig.Type == entity.RuntimeConfigTypeStratoCube {
			version := regexp.MustCompile(`version=(.+)`).FindStringSubmatch(*opt.RuntimeConfig.BinarySource)
			if len(version) < 2 {
				return nil, ErrAgentConfigVersionBinarySourceInvalid
			}
			if err := s.checkBinaryVersion(ctx, version[1], agentConfig.Type); err != nil {
				return nil, err
			}
		}
		if opt.RuntimeConfig.Image != nil {
			if err := s.checkImageVersion(ctx, *opt.RuntimeConfig.Image, agentConfig.Type); err != nil {
				return nil, err
			}
		}
	}

	if opt.PromptConfig != nil && len(opt.PromptConfig.Prompts) != 0 {
		if err := s.CheckPromptConfig(ctx, isOnline, agentConfig.AgentID, *opt.PromptConfig); err != nil {
			return nil, err
		}
	}

	if opt.KnowledgesetConfig != nil && len(opt.KnowledgesetConfig.Knowledgesets) > 0 {
		if err := s.checkKnowledgesetConfig(ctx, *opt.KnowledgesetConfig); err != nil {
			return nil, err
		}
	}

	v, err := s.dao.UpdateAgentConfigVersion(ctx, dal.UpdateAgentConfigVersionOption{
		ID:                 opt.ID,
		Enabled:            opt.Enabled,
		Description:        opt.Description,
		RuntimeConfig:      opt.RuntimeConfig,
		CustomConfig:       opt.CustomConfig,
		PromptConfig:       opt.PromptConfig,
		KnowledgesetConfig: opt.KnowledgesetConfig,
		UpdatedAt:          updatedAt,
	})
	if errors.Is(err, dal.ErrRowsAffectedZero) {
		return nil, ErrAgentConfigVersionUpdated
	}
	return v, err
}

type ListAgentConfigVersionsOption struct {
	AgentConfigID *string
	Creator       *string
	Enabled       *bool
	Status        []*entity.AgentConfigStatus
	Offset        int
	Limit         int
}

func (s *Service) ListAgentConfigVersions(ctx context.Context, opt ListAgentConfigVersionsOption) (int64, []*entity.AgentConfigVersion, error) {
	var (
		total               int64
		agentConfigVersions []*entity.AgentConfigVersion
		agentDeploy         *entity.AgentDeploy
	)
	pool := poolsdk.New().WithErrors()
	pool.Go(func() error {
		var err error
		total, agentConfigVersions, err = s.dao.ListAgentConfigVersion(ctx, dal.ListAgentConfigVersionOption{
			AgentConfigID: opt.AgentConfigID,
			Creator:       opt.Creator,
			Enabled:       opt.Enabled,
			Status:        opt.Status,
			Offset:        opt.Offset,
			Limit:         opt.Limit,
		})
		if err != nil {
			return errors.WithMessage(err, "failed list agent config versions")
		}
		return nil
	})
	pool.Go(func() error {
		if opt.AgentConfigID != nil {
			var err error
			// 查询是否有正在部署中的工单
			agentDeploy, err = s.dao.GetAgentDeploy(ctx, dal.GetAgentDeployOption{
				AgentConfigID: *opt.AgentConfigID,
				Status:        []string{string(entity.AgentDeployStatusCreated), string(entity.AgentDeployStatusCanary)},
			})
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.WithMessage(err, "failed get agent deploy")
			}
		}
		return nil
	})
	if err := pool.Wait(); err != nil {
		return 0, nil, err
	}

	if agentDeploy != nil {
		for _, agentConfigVersion := range agentConfigVersions {
			if agentConfigVersion.ID == agentDeploy.AgentConfigVersionID {
				agentConfigVersion.DeployID = agentDeploy.ID
			}
		}
	}
	return total, agentConfigVersions, nil
}

type CopyAgentConfigVersionOption struct {
	SourceID       string
	AgentConfigID  string
	Creator        string
	ScmVersion     string
	IcmVersion     string
	Description    string
	CreateSource   *string
	BashIcmVersion string
}

func (s *Service) CopyAgentConfigVersion(ctx context.Context, opt *CopyAgentConfigVersionOption) (*entity.AgentConfigVersion, error) {
	// 获取源版本
	sourceAgentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
		ID: &opt.SourceID,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed get source agent config version")
	}

	// 获取最新版本号
	latestAgentConfigVersion, err := s.dao.GetLatestAgentConfigVersion(ctx, dal.GetLatestAgentConfigVersionOption{
		AgentConfigID: opt.AgentConfigID,
	})
	version := 0
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.WithMessage(err, "failed get latest agent config version")
	}
	if latestAgentConfigVersion != nil {
		version = latestAgentConfigVersion.Version
	}

	// 获取 agent 配置
	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{ID: &opt.AgentConfigID})
	if err != nil {
		return nil, errors.WithMessage(err, "failed get agent config")
	}

	// base 类型不允许从其他类型复制
	deployReviewConfig := s.tccConf.NextDeployReviewConfig.GetValue()
	if agentConfig.Type == entity.AgentConfigTypeBase &&
		sourceAgentConfigVersion.AgentConfigID != agentConfig.ID {
		// base 版本允许从指定的白名单复制
		if !deployReviewConfig.BaseAllowCopyAll && !slices.Contains(deployReviewConfig.BaseAllowCopyAgentConfigIDs, sourceAgentConfigVersion.AgentConfigID) {
			return nil, ErrAgentConfigNotAllowedCopy
		}
	}
	if opt.ScmVersion != "" {
		sourceAgentConfigVersion.RuntimeConfig.BinarySource = &opt.ScmVersion
	}

	if opt.IcmVersion != "" {
		sourceAgentConfigVersion.RuntimeConfig.Image = &opt.IcmVersion
	}
	if opt.BashIcmVersion != "" {
		sourceAgentConfigVersion.RuntimeConfig.BashImage = &opt.BashIcmVersion
	}

	// 根据源版本创建新版本
	agentConfigVersion, err := s.dao.CreateAgentConfigVersion(ctx, dal.CreateAgentConfigVersionOption{
		ID:                 s.idGen.NewID(),
		AgentConfigID:      opt.AgentConfigID,
		Description:        lo.Ternary(opt.Description != "", opt.Description, fmt.Sprintf("Copy of %d", sourceAgentConfigVersion.Version)),
		Creator:            opt.Creator,
		Version:            version + 1,
		Enable:             false,
		Status:             entity.AgentConfigStatusCreated,
		RuntimeConfig:      sourceAgentConfigVersion.RuntimeConfig,
		CustomConfig:       sourceAgentConfigVersion.CustomConfig,
		PromptConfig:       sourceAgentConfigVersion.PromptConfig,
		KnowledgesetConfig: sourceAgentConfigVersion.KnowledgesetConfig,
		CreateSource:       opt.CreateSource,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed create agent config version")
	}
	return agentConfigVersion, nil
}

type DeployAgentOption struct {
	AgentConfigVersionID string
	Actor                string
	DeployID             string
	ExtraInfo            *entity.DeployExtraInfo
}

func (s *Service) DeployAgent(ctx context.Context, opt DeployAgentOption) (*entity.AgentConfigVersion, error) {
	agentConfigVersion, agentConfig, err := s.GetAndCheckAgentConfigVersion(ctx, opt.AgentConfigVersionID)
	if err != nil {
		return nil, err
	}

	if !agentConfigVersion.Enabled {
		_, err = s.dao.UpdateAgentConfigVersion(ctx, dal.UpdateAgentConfigVersionOption{
			ID:      agentConfigVersion.ID,
			Enabled: lo.ToPtr(true),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed update agent config version")
		}
	}

	err = s.UpdateDeployAgentStatus(ctx, agentConfigVersion, agentConfig, opt)
	if err != nil {
		return nil, err
	}
	return s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
		ID:   &opt.AgentConfigVersionID,
		Sync: true,
	})
}

// 抽离发布校验逻辑为了 deploy review 复用
func (s *Service) GetAndCheckAgentConfigVersion(ctx context.Context, agentConfigVersionID string) (*entity.AgentConfigVersion, *entity.AgentConfig, error) {
	agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
		ID: &agentConfigVersionID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, ErrAgentConfigVersionNotFound
		}
		return nil, nil, errors.WithMessage(err, "failed get agent config version")
	}
	// 校验知识集是否全部上线
	if len(agentConfigVersion.KnowledgesetConfig.Knowledgesets) > 0 {
		ids := lo.Map(agentConfigVersion.KnowledgesetConfig.Knowledgesets, func(item *entity.KnowledgesetMetadata, index int) string {
			return item.KnowledgesetVersionID
		})
		if err = s.checkKnowledgesetVersionStatus(ctx, ids); err != nil {
			return nil, nil, err
		}
	}

	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{ID: &agentConfigVersion.AgentConfigID})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, ErrAgentConfigNotFound
		}
		return nil, nil, errors.WithMessage(err, "failed get agent config")
	}
	if agentConfig.Type != entity.AgentConfigTypeBase && agentConfig.Type != entity.AgentConfigTypeAbTest {
		return nil, nil, ErrAgentConfigNotAllowedDeploy
	}

	// 校验 prompt 配置
	if err := s.CheckPromptConfig(ctx, agentConfigVersion.RuntimeConfig.Type == entity.RuntimeConfigTypeStratoCube, agentConfig.AgentID, agentConfigVersion.PromptConfig); err != nil {
		return nil, nil, err
	}

	// 校验二进制源
	if agentConfigVersion.RuntimeConfig.BinarySource != nil && agentConfigVersion.RuntimeConfig.Type == entity.RuntimeConfigTypeStratoCube {
		version := regexp.MustCompile(`version=(.+)`).FindStringSubmatch(*agentConfigVersion.RuntimeConfig.BinarySource)
		if len(version) < 2 {
			return nil, nil, ErrAgentConfigVersionBinarySourceInvalid
		}
		if err := s.checkBinaryVersion(ctx, version[1], agentConfig.Type); err != nil {
			return nil, nil, err
		}
	}

	// 校验 image
	if agentConfigVersion.RuntimeConfig.Image != nil {
		if err := s.checkImageVersion(ctx, *agentConfigVersion.RuntimeConfig.Image, agentConfig.Type); err != nil {
			return nil, nil, err
		}
	}

	return agentConfigVersion, agentConfig, nil
}

// 抽离发布过程的更新状态逻辑为了 deploy review 复用
func (s *Service) UpdateDeployAgentStatus(ctx context.Context, agentConfigVersion *entity.AgentConfigVersion, agentConfig *entity.AgentConfig, opt DeployAgentOption) error {
	creates, deletes, err := s.getUpdateAgentDeployRelation(ctx, agentConfigVersion.KnowledgesetConfig, agentConfigVersion.AgentConfigID, agentConfigVersion.ID, opt.Actor)
	if err != nil {
		return err
	}

	// 记录上线信息, 更新 config version 状态，原子操作
	id := opt.DeployID
	if id == "" {
		id = s.idGen.NewID()
	}
	err = s.dao.CreateAgentDeploy(ctx, dal.CreateAgentDeployOption{
		ID:                    id,
		AgentConfigID:         agentConfigVersion.AgentConfigID,
		AgentConfigVersionID:  agentConfigVersion.ID,
		Actor:                 opt.Actor,
		SourceStatus:          agentConfigVersion.Status,
		TargetStatus:          entity.AgentConfigStatusOnline,
		DeleteRelationIDs:     deletes,
		CreateRelationOptions: creates,
		Status:                entity.AgentDeployStatusFinish,
		ExtraInfo:             opt.ExtraInfo,
	})
	if err != nil {
		return errors.WithMessage(err, "failed create agent config")
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "panic with send notification: %+v", r)
			}
		}()
		agent, err := s.dao.GetAgent(ctx, dal.GetAgentOption{
			ID: agentConfig.AgentID,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed get agent config for send notification: %v", err)
			return
		}
		err = s.larkService.SendDeployNotification(
			ctx,
			agent.Name,
			opt.Actor,
			strconv.Itoa(agentConfigVersion.Version),
			agentConfigVersion.Description,
			GetAgentConfigVersionURI(agentConfigVersion.ID, agentConfig.ID),
			string(agentConfig.Type),
		)
		if err != nil {
			log.V1.CtxError(ctx, "failed send deploy notification: %v", err)
		}
	}()

	return nil
}

func (s *Service) CheckPromptConfig(ctx context.Context, isOnline bool, agentID string, cfg entity.PromptConfig) error {
	var promptIDs []string
	var versions []int
	for _, prompt := range cfg.Prompts {
		promptIDs = append(promptIDs, prompt.ID)
		versions = append(versions, prompt.Version)
	}

	if err := s.checkUseOtherAgentPrompt(ctx, agentID, promptIDs); err != nil {
		return err
	}

	// online 状态检查是否用了未开启的 Prompt
	if isOnline {
		if err := s.checkUseDisablePrompt(ctx, promptIDs, versions); err != nil {
			return err
		}
	}

	return nil
}

func (s *Service) checkUseOtherAgentPrompt(ctx context.Context, agentID string, promptIDs []string) error {
	if len(promptIDs) == 0 {
		return nil
	}
	prompts, err := s.dao.BatchGetPrompts(ctx, dal.BatchGetPromptsOption{
		PromptIDs: promptIDs,
	})
	if err != nil {
		return errors.WithMessage(err, "failed batch get prompts")
	}

	if len(prompts) != len(lo.Uniq(promptIDs)) {
		return ErrAgentConfigPromptNotExists
	}
	for _, prompt := range prompts {
		if prompt.Type == entity.PromptTypeAgent && (prompt.AgentID == nil || *prompt.AgentID != agentID) {
			return ErrAgentConfigNotAllowedUseOtherPrompt
		}
	}
	return nil
}

func (s *Service) checkUseDisablePrompt(ctx context.Context, promptIDs []string, versions []int) error {
	if len(promptIDs) == 0 {
		return nil
	}
	promptVersions, err := s.dao.BatchGetPromptVersions(ctx, dal.BatchGetPromptVersionOptions{
		PromptIDs: promptIDs,
		Versions:  versions,
	})
	if err != nil {
		return errors.WithMessage(err, "failed batch get prompt versions")
	}
	if len(promptVersions) != len(versions) {
		return ErrAgentConfigPromptNotExists
	}
	for _, promptVersion := range promptVersions {
		if !promptVersion.Enabled {
			return ErrAgentConfigNotAllowedUseDisablePrompt
		}
	}
	return nil
}

func (s *Service) checkKnowledgesetConfig(ctx context.Context, config entity.KnowledgesetConfig) error {
	versions := []string{}
	knowledgesetConfigs := []string{}
	for _, k := range config.Knowledgesets {
		if slices.Contains(knowledgesetConfigs, k.KnowledgesetID) {
			return ErrAgentConfigNotAllowedUseSameKnowledgeset
		}
		knowledgesetConfigs = append(knowledgesetConfigs, k.KnowledgesetID)
		versions = append(versions, k.KnowledgesetVersionID)
	}

	// 状态检查是否用了未 online 的 knowledge
	return s.checkKnowledgesetVersionStatus(ctx, versions)
}

func (s *Service) checkKnowledgesetVersionStatus(ctx context.Context, ids []string) error {
	knowledgesetVersions, err := s.knowledgeSetVersionDao.GetKnowledgesetVersions(ctx, ids)
	if err != nil {
		return errors.WithMessage(err, "failed get knowledgeset versions")
	}
	for _, knowledgesetVersion := range knowledgesetVersions {
		if knowledgesetVersion.Status != entity.KnowledgesetVersionOnlineStatus {
			return ErrKnowledgesetVersionNotOnline
		}
	}
	return nil
}

func (s *Service) checkBinaryVersion(ctx context.Context, version string, agentConfigType entity.AgentConfigType) error {
	if env.IsBoe() || env.IsBoeI18N() {
		return nil
	}
	versions, err := s.scmService.GetScmVersionList(ctx, &serverservice.GetScmVersionListOption{
		Version: version,
	})
	if err != nil {
		return errors.Wrap(err, "failed get scm version list")
	}
	if len(versions) == 0 {
		return ErrBinarySourceVersionNotExists
	}
	scmVersion := versions[0]

	if agentConfigType == entity.AgentConfigTypeBase {
		if scmVersion.Type != "online" {
			return ErrBinarySourceVersionNotOnline

		}
	}

	if agentConfigType == entity.AgentConfigTypeAbTest {
		if scmVersion.Type != "online" && scmVersion.Type != "test" {
			return ErrBinarySourceVersionNotOnlineOrTest
		}
	}
	return nil
}

// 检查 image 版本是否存在
func (s *Service) checkImageVersion(ctx context.Context, imageName string, agentConfigType entity.AgentConfigType) error {
	imagePrefix := fmt.Sprintf("%s/%s/%s", s.conf.ICMConfig.Registry, s.conf.ICMConfig.Namespace, s.conf.ICMConfig.OnlineImageName)
	if strings.Split(imageName, ":")[0] != imagePrefix {
		return ErrImageVersionNotExists
	}
	versionInfo, err := s.icmService.GetIcmVersionInfo(ctx, imageName)
	if err != nil {
		return err
	}

	if versionInfo == nil {
		return ErrImageVersionNotExists
	}
	// TODO: 暂时先去掉
	// if agentConfigType == entity.AgentConfigTypeBase && versionInfo.DetailInfo.SpecificTag != "" {
	// 	return ErrImageVersionNotExists
	// }
	return nil
}

func (s *Service) GetRoleByAgentConfigVersion(ctx context.Context, version entity.AgentConfigVersion) (*entity.SessionRole, error) {
	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		ID: &version.AgentConfigID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed get agent config")
	}
	cfg := s.tccConf.RoleAgentConfig.GetValue()
	for k, v := range cfg.Roles {
		if v.AgentID == agentConfig.AgentID {
			return lo.ToPtr(entity.ParseSessionRole(k)), nil
		}
	}
	return nil, nil
}

const (
	agentVersionIntern        = "intern"
	agentVersionNewbie        = "newbie"
	agentVersionInvitedExpert = "invited_expert"
)

func (s *Service) GetAgentConfigByVersion(ctx context.Context, v string) (*entity.AgentConfigVersion, error) {
	agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{ID: &v})
	if err != nil {
		return nil, errors.WithMessage(err, "failed get agent config version")
	}
	return agentConfigVersion, nil
}

func (s *Service) GetAgentConfigVersionByRole(ctx context.Context, role *entity.SessionRole, v string) (*entity.AgentConfigVersion, error) {
	if v != "" { // 如果指定了版本，就用指定的版本
		agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{ID: &v})
		if err != nil {
			return nil, errors.WithMessage(err, "failed get agent config version")
		}
		return agentConfigVersion, nil
	}

	var name, agentID string
	switch lo.FromPtr(role) {
	case entity.SessionRoleYoungTalent:
		name = lo.Ternary(env.InTCE(), agentVersionIntern, agentVersionIntern+"_docker")
	case entity.SessionRoleLateralHire:
		name = lo.Ternary(env.InTCE(), agentVersionNewbie, agentVersionNewbie+"_docker")
	case entity.SessionRoleIndustryVeteran:
		name = lo.Ternary(env.InTCE(), agentVersionInvitedExpert, agentVersionInvitedExpert+"_docker")
	default:
		name = lo.Ternary(env.InTCE(), "stratocube", "docker")
	}
	cfg := s.tccConf.RoleAgentConfig.GetValue()
	if value, ok := cfg.Roles[name]; !ok {
		return nil, errors.New("not found role")
	} else {
		agentID = value.AgentID
	}

	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		AgentID: &agentID,
		Type:    lo.ToPtr(entity.AgentConfigTypeBase),
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentConfigNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent config")
	}
	agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
		AgentConfigID: &agentConfig.ID,
		Status:        lo.ToPtr(entity.AgentConfigStatusOnline),
		Sync:          true,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrAgentConfigVersionNotFound
		}
		return nil, errors.WithMessage(err, "failed get agent config version")
	}
	return agentConfigVersion, nil
}

func (s *Service) GetAgentIDByRole(ctx context.Context, role *entity.SessionRole) (string, error) {
	var name, agentID string
	switch lo.FromPtr(role) {
	case entity.SessionRoleYoungTalent:
		name = lo.Ternary(env.InTCE(), agentVersionIntern, agentVersionIntern+"_docker")
	case entity.SessionRoleLateralHire:
		name = lo.Ternary(env.InTCE(), agentVersionNewbie, agentVersionNewbie+"_docker")
	case entity.SessionRoleIndustryVeteran:
		name = lo.Ternary(env.InTCE(), agentVersionInvitedExpert, agentVersionInvitedExpert+"_docker")
	default:
		name = lo.Ternary(env.InTCE(), "stratocube", "docker")
	}
	cfg := s.tccConf.RoleAgentConfig.GetValue()
	if value, ok := cfg.Roles[name]; !ok {
		return "", errors.New("not found role")
	} else {
		agentID = value.AgentID
	}
	return agentID, nil
}

func (s *Service) GetRoleByAgentConfig(ctx context.Context, agentConfigVersion *entity.AgentConfigVersion) (entity.SessionRole, error) {
	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		ID: &agentConfigVersion.AgentConfigID,
	})
	if err != nil {
		return 0, errors.WithMessagef(err, "failed get agent config by id: %+v", agentConfigVersion.AgentConfigID)
	}
	cfg := s.tccConf.RoleAgentConfig.GetValue()
	for role, roleConfig := range cfg.Roles {
		if roleConfig.AgentID == agentConfig.AgentID {
			switch role {
			case agentVersionIntern, agentVersionIntern + "_docker":
				return entity.SessionRoleYoungTalent, nil
			case agentVersionNewbie, agentVersionNewbie + "_docker":
				return entity.SessionRoleLateralHire, nil
			case agentVersionInvitedExpert, agentVersionInvitedExpert + "_docker":
				return entity.SessionRoleIndustryVeteran, nil
			}
		}
	}
	return 0, errors.Errorf("not found role")
}

func (s *Service) getUpdateAgentDeployRelation(ctx context.Context, knowledgesetConfig entity.KnowledgesetConfig, agentConfigID, agentConfigVersionID, creator string) ([]dal.CreateAgentDeployRelationOption, []string, error) {
	// 1. 先查询数据库的 relation 记录
	existingRelations, err := s.agentDeployRelationDAO.ListAgentDeployRelation(ctx, dal.ListAgentDeployRelationOption{
		AgentConfigVersionID: &agentConfigVersionID,
		RelationType:         lo.ToPtr(entity.AgentDeployRelationTypeKnowledgeset),
	})
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to list agent deploy relations")
	}

	// 2. 和入参进行比对，找出新增和需要删除的记录
	// 构建现有关系的映射，以 RelationID 为键
	existingRelationMap := make(map[string]*entity.AgentDeployRelation)
	for _, relation := range existingRelations {
		existingRelationMap[relation.RelationID] = relation
	}

	// 构建新关系的映射，以 KnowledgesetVersionID 为键
	newRelationMap := make(map[string]*entity.KnowledgesetMetadata)
	for _, knowledgeset := range knowledgesetConfig.Knowledgesets {
		newRelationMap[knowledgeset.KnowledgesetVersionID] = knowledgeset
	}

	// 找出需要新增的记录
	var toCreate []dal.CreateAgentDeployRelationOption
	for versionID := range newRelationMap {
		if _, exists := existingRelationMap[versionID]; !exists {
			// 不存在，需要新增
			toCreate = append(toCreate, dal.CreateAgentDeployRelationOption{
				ID:                   s.idGen.NewID(),
				AgentConfigID:        agentConfigID,
				AgentConfigVersionID: agentConfigVersionID,
				RelationType:         entity.AgentDeployRelationTypeKnowledgeset,
				RelationID:           versionID,
				Creator:              creator,
			})
		}
	}

	// 找出需要删除的记录
	var toDelete []string
	for versionID, relation := range existingRelationMap {
		if _, exists := newRelationMap[versionID]; !exists {
			// 不存在，需要删除
			toDelete = append(toDelete, relation.ID)
		}
	}

	return toCreate, toDelete, nil
}

func GetAgentConfigVersionURI(agentConfigVersionID string, agentConfigID string) string {
	return fmt.Sprintf("/lab/agents/version/detail?id=%s&config_id=%s", agentConfigVersionID, agentConfigID)
}

type CheckAgentPermissionOption struct {
	Account              *authentity.Account
	AgentID              *string
	AgentConfigVersionID *string
	Action               entity.PermissionAction
}

// CheckAgentPermission 检查是否有对Agent执行action的权限
func (s *Service) CheckAgentPermission(ctx context.Context, opt CheckAgentPermissionOption) (bool, error) {
	if opt.AgentID == nil {
		agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
			ID: opt.AgentConfigVersionID,
		})
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return false, ErrAgentConfigVersionNotFound
			}
			return false, errors.WithMessage(err, "failed get agent config version")
		}
		agentConfigID := agentConfigVersion.AgentConfigID
		// 如果没有指定 AgentID，则使用 AgentConfigVersion 中的 AgentID
		agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
			ID: lo.ToPtr(agentConfigID),
		})
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return false, ErrAgentConfigNotFound
			}
			return false, errors.WithMessage(err, "failed get agent config")
		}
		opt.AgentID = lo.ToPtr(agentConfig.AgentID)
	}
	// 检查是否有权限
	permRet, err := s.permissionService.CheckPermission(ctx, permissionservice.CheckPermissionOption{
		Account:            lo.FromPtr(opt.Account),
		ResourceType:       lo.ToPtr(entity.ResourceTypeAgent),
		ResourceExternalID: opt.AgentID,
		Action:             opt.Action,
	})

	if err != nil {
		return false, errors.WithMessage(err, "failed check permission")
	}

	return permRet.Allowed, nil

}

type CopyAgentConfigOnlineVersionOrUpdateOption struct {
	AgentConfigID  string
	Description    string
	Creator        string
	ScmVersion     string
	IcmVersion     string
	BitsBuildID    string
	SourceID       string
	BashIcmVersion string
}

func (s *Service) BitsUpsertAgentConfigVersion(ctx context.Context, opts *CopyAgentConfigOnlineVersionOrUpdateOption) (*entity.AgentConfigVersion, error) {
	// 校验 scm 和 icm
	agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
		ID: &opts.AgentConfigID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed get agent config")
	}
	if opts.ScmVersion != "" {
		version := regexp.MustCompile(`version=(.+)`).FindStringSubmatch(opts.ScmVersion)
		if len(version) < 2 {
			return nil, ErrAgentConfigVersionBinarySourceInvalid
		}
		if err := s.checkBinaryVersion(ctx, version[1], agentConfig.Type); err != nil {
			return nil, err
		}
	}

	if opts.IcmVersion != "" {
		if err := s.checkImageVersion(ctx, opts.IcmVersion, agentConfig.Type); err != nil {
			return nil, err
		}
	}

	if opts.SourceID != "" {
		return s.CopyFromBaseOnlineVersion(ctx, opts)
	}
	agentConfigVersion, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
		CreateSource:  &opts.BitsBuildID,
		AgentConfigID: &opts.AgentConfigID,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.WithMessage(err, "failed get agent config version")
	}
	if agentConfigVersion == nil {
		return s.CopyFromBaseOnlineVersion(ctx, opts)
	} else {
		return s.UpdateAgentConfigVersionRuntime(ctx, opts, agentConfigVersion)
	}
}

// 更新指定 AgentConfigVersion 的运行时配置
func (s *Service) UpdateAgentConfigVersionRuntime(ctx context.Context, opt *CopyAgentConfigOnlineVersionOrUpdateOption, agentConfigVersion *entity.AgentConfigVersion) (*entity.AgentConfigVersion, error) {
	if agentConfigVersion.Enabled {
		return nil, errors.New("agent config version is not allowed update")
	}

	// 更新 RuntimeConfig 中的 BinarySource
	if opt.ScmVersion != "" {
		agentConfigVersion.RuntimeConfig.BinarySource = &opt.ScmVersion
	}

	// 如果提供了 IcmVersion，则更新 Image
	if opt.IcmVersion != "" {
		agentConfigVersion.RuntimeConfig.Image = &opt.IcmVersion
	}

	// 保存更新后的 AgentConfigVersion
	agentConfigVersion, err := s.dao.UpdateAgentConfigVersion(ctx, dal.UpdateAgentConfigVersionOption{
		ID:            agentConfigVersion.ID,
		RuntimeConfig: &agentConfigVersion.RuntimeConfig,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed update agent config version")
	}

	return agentConfigVersion, nil
}

// 从 Agent 的 Base 配置的在线版本复制一个新版本
func (s *Service) CopyFromBaseOnlineVersion(ctx context.Context, opt *CopyAgentConfigOnlineVersionOrUpdateOption) (*entity.AgentConfigVersion, error) {
	// 获取目标 Agent 配置
	sourceID := opt.SourceID
	if sourceID == "" {
		agentConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{ID: &opt.AgentConfigID})
		if err != nil {
			return nil, errors.WithMessage(err, "failed get agent config")
		}

		// 获取对应的 Agent Base 配置
		var agentBaseConfigID string
		if agentConfig.Type == entity.AgentConfigTypeBase {
			agentBaseConfigID = agentConfig.ID
		} else {
			agentBaseConfig, err := s.dao.GetAgentConfig(ctx, dal.GetAgentConfigOption{
				AgentID: &agentConfig.AgentID,
				Type:    lo.ToPtr(entity.AgentConfigTypeBase),
			})
			if err != nil {
				return nil, errors.WithMessage(err, "failed get agent base config")
			}
			agentBaseConfigID = agentBaseConfig.ID
		}

		// 获取 Agent Base 的在线版本
		agentBaseOnlineConfig, err := s.dao.GetAgentConfigVersion(ctx, dal.GetAgentConfigVersionOption{
			AgentConfigID: &agentBaseConfigID,
			Status:        lo.ToPtr(entity.AgentConfigStatusOnline),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed get agent base online config")
		}
		sourceID = agentBaseOnlineConfig.ID
	}

	// 使用 CopyAgentConfigVersion 方法复制版本
	return s.CopyAgentConfigVersion(ctx, &CopyAgentConfigVersionOption{
		SourceID:       sourceID,
		AgentConfigID:  opt.AgentConfigID,
		Creator:        opt.Creator,
		ScmVersion:     opt.ScmVersion,
		Description:    opt.Description,
		IcmVersion:     opt.IcmVersion,
		CreateSource:   &opt.BitsBuildID,
		BashIcmVersion: opt.BashIcmVersion,
	})
}
