package serverservice

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

type GetIcmVersionListOption struct {
	Version     string
	Region      string
	SpecificTag string
	ConfigType  string
	ImageType   string
}

const (
	ImageTypeAgent = "agent"
	ImageTypeBash  = "bash"
)

//go:generate go run go.uber.org/mock/mockgen@latest -destination ../mock/service/icm_openapi.go -package serverservice code.byted.org/devgpt/kiwis/agentsphere/next_server/service IcmOpenAPI
type IcmOpenAPI interface {
	GetIcmVersionList(ctx context.Context, opt *GetIcmVersionListOption) ([]*entity.IcmVersion, error)
	GetIcmVersionInfo(ctx context.Context, imageName string) (*entity.IcmVersionInfo, error)
}
