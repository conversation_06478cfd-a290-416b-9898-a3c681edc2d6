package artifact

import (
	"archive/zip"
	"bytes"
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"path"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"code.byted.org/codebase/git-wrapper/diff/parser"
	lstree "code.byted.org/codebase/git-wrapper/ls-tree"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	tossdk "code.byted.org/gopkg/tos"
	"code.byted.org/middleware/gjson"
	"code.byted.org/videoarch/imagex-sdk-golang/v2/service/imagex"
	"github.com/AlekSi/pointer"
	"github.com/cenkalti/backoff/v4"
	"github.com/gabriel-vasile/mimetype"
	"github.com/go-enry/go-enry/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/iter"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lock"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/service/runtimestarter"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	serverdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	concurrentslice "code.byted.org/devgpt/kiwis/lib/concurrent_slice"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/util"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/tos"
)

type Service struct {
	tccConf                 *config.AgentSphereTCCConfig
	dao                     *dal.DAO
	larkClient              lark.Client
	tos                     tos.Client
	idGen                   uuid.Generator
	imagexClient            *imagex.ImageXClient
	userService             *user.Service
	runtimeProviderRegistry *runtimedal.RuntimeProviderRegistry
	runtimeDao              *runtimedal.DAO
	runtimeLocker           lock.Lock
	codebaseClient          codebase.Client
	redisClient             redis.Client
}

type CreateServiceOption struct {
	fx.In
	TCCConf                 *config.AgentSphereTCCConfig
	DAO                     *dal.DAO
	Tos                     tos.Client
	LarkClient              lark.Client
	ImagexClient            *imagex.ImageXClient
	UserService             *user.Service
	RuntimeProviderRegistry *runtimedal.RuntimeProviderRegistry
	RuntimeDao              *runtimedal.DAO
	RuntimeLocker           lock.Lock
	CodebaseClient          codebase.Client
	RedisClient             redis.Client
}

func NewService(opt CreateServiceOption) (*Service, error) {
	s := &Service{
		tccConf:                 opt.TCCConf,
		dao:                     opt.DAO,
		larkClient:              opt.LarkClient,
		tos:                     opt.Tos,
		idGen:                   uuid.GetDefaultGenerator(nil),
		imagexClient:            opt.ImagexClient,
		userService:             opt.UserService,
		runtimeProviderRegistry: opt.RuntimeProviderRegistry,
		runtimeDao:              opt.RuntimeDao,
		runtimeLocker:           opt.RuntimeLocker,
		codebaseClient:          opt.CodebaseClient,
		redisClient:             opt.RedisClient,
	}
	return s, nil
}

type CreateArtifactOption struct {
	SessionID string
	Type      entity.ArtifactType
	Source    entity.ArtifactSource
	Key       string
	Metadata  entity.ArtifactMetadata
	FileMetas entity.FileMetas
}

func (s *Service) CreateArtifact(ctx context.Context, opt CreateArtifactOption) (result *entity.Artifact, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("CreateArtifact", string(opt.Source), err != nil, serverservice.ErrorToErrorReason(err))
	}()

	sessionID := opt.SessionID
	if sessionID != "unknown" {
		_, err := s.dao.GetSessionWithDeleted(ctx, dal.GetSessionOption{
			ID: sessionID,
		})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get session %s for artifact", sessionID)
		}
	}

	previousVersions, err := s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID: opt.SessionID,
		Key:       lo.ToPtr(opt.Key),
	})
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get previous versions of artifact %s", opt.Key)
	}

	return s.dao.CreateArtifact(ctx, serverdal.CreateArtifactOption{
		SessionID: sessionID,
		ID:        s.idGen.NewID(),
		Status:    entity.ArtifactStatusDraft,
		Source:    opt.Source,
		Type:      opt.Type,
		Key:       opt.Key,
		Version:   int32(len(previousVersions) + 1),
		Metadata:  opt.Metadata,
		Files:     opt.FileMetas,
	})
}

type CreateLinkArtifactOption struct {
	Url               string
	ArtifactKeySource entity.LinkArtifactKeySource // artifact key 生成来源
	ArtifactSource    entity.ArtifactSource        // artifact 来源
	Display           bool
	Title             string

	DeploymentID *string // 部署的link，有此id
}

func (s *Service) CreateLinkArtifact(ctx context.Context, sessionID string, opt CreateLinkArtifactOption) (result *entity.Artifact, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("CreateLinkArtifact", string(opt.ArtifactSource), err != nil, serverservice.ErrorToErrorReason(err))
	}()

	l := entity.LinkContent{
		URL: opt.Url,
	}
	var (
		artifactKey string
		fileMeta    entity.FileMeta
	)
	switch opt.ArtifactKeySource {
	case entity.LinkArtifactKeySourceDeployment:
		if opt.DeploymentID == nil {
			return nil, errors.Wrap(serverservice.ErrorArtifactNotFound, "deployment id is required")
		}
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceDeployment, *opt.DeploymentID, "")
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceDeployment.String()}
	case entity.LinkArtifactKeySourceLarkDoc:
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceLarkDoc, util.MD5([]byte(opt.Url)), "")
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceLarkDoc.String()}
	case entity.LinkArtifactKeySourceLarkSheet:
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceLarkSheet, util.MD5([]byte(opt.Url)), "")
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceLarkSheet.String()}
	case entity.LinkArtifactKeySourceURL:
		artifactKey = s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceURL, util.MD5([]byte(opt.Url)), "")
		fileMeta = entity.FileMeta{Content: conv.JSONString(l), Type: entity.ArtifactTypeLink, Name: opt.Title, SubType: entity.LinkArtifactKeySourceURL.String()}
	default:
		return nil, errors.New("unknown artifact key source")
	}
	version := int32(1)

	// 如果存在返回
	artifact, err := s.dao.GetArtifactByArtifactKey(ctx, sessionID, artifactKey, version, true)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.WithMessage(err, "get artifact failed")
	}
	if artifact != nil {
		return artifact, nil
	}

	// 如果不存在create
	artifact, err = s.dao.CreateArtifact(ctx, dal.CreateArtifactOption{
		SessionID: sessionID,
		ID:        s.idGen.NewID(),
		Type:      entity.ArtifactTypeLink,
		Status:    entity.ArtifactStatusCompleted,
		Source:    opt.ArtifactSource,
		Key:       artifactKey,
		Version:   version, // link 类型暂时版本都是1
		Files:     entity.FileMetas{fileMeta},
		Display:   opt.Display,
	})
	if err != nil { // 如果唯一索引冲突，再查一次返回
		if errors.Is(err, gorm.ErrDuplicatedKey) || db.IsDuplicateKeyError(err) {
			return s.dao.GetArtifactByArtifactKey(ctx, sessionID, artifactKey, version, true)
		}
		return nil, errors.WithMessagef(err, "failed to create link artifact, session id: %v", sessionID)
	}
	return artifact, err
}

type GetArtifactOption struct {
	ID   string
	Sync bool
}

func (s *Service) GetArtifact(ctx context.Context, opt GetArtifactOption) (*entity.Artifact, error) {
	return s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{
		ID:   opt.ID,
		Sync: opt.Sync,
	})
}

type GetArtifactByKeyOption struct {
	SessionID   string
	ArtifactKey string
	Sync        bool
}

func (s *Service) GetArtifactByKey(ctx context.Context, opt GetArtifactByKeyOption) (*entity.Artifact, error) {
	return s.dao.GetArtifactByArtifactKey(ctx, opt.SessionID, opt.ArtifactKey, 1, opt.Sync)
}

type ListSessionArtifactsOption struct {
	SessionID   string
	Status      []entity.ArtifactStatus
	Display     *bool
	ArtifactIDs []string
	ArtifactKey *string
}

func (s *Service) ListSessionArtifacts(ctx context.Context, opt ListSessionArtifactsOption) ([]*entity.Artifact, error) {
	return s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID:   opt.SessionID,
		Status:      opt.Status,
		Display:     opt.Display,
		ArtifactIDs: opt.ArtifactIDs,
		Key:         opt.ArtifactKey,
	})
}

type GetLatestArtifactOption struct {
	SessionID string
	Type      []entity.ArtifactType
}

func (s *Service) GetLatestArtifactByType(ctx context.Context, opt GetLatestArtifactOption) (*entity.Artifact, error) {
	return s.dao.GetLatestArtifactByType(ctx, serverdal.GetLatestArtifactOption{
		SessionID: opt.SessionID,
		Type:      opt.Type,
	})
}

type ListReplayArtifactsOption struct {
	ReplayID string
	Display  *bool
}

func (s *Service) ListReplayArtifacts(ctx context.Context, opt ListReplayArtifactsOption) ([]*entity.Artifact, error) {
	replay, err := s.dao.GetReplay(ctx, serverdal.GetReplayOption{
		ID:    opt.ReplayID,
		Sync:  false,
		Cache: true,
	})
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get replay, replay_id: %s", opt.ReplayID)
	}
	if replay == nil {
		return nil, errors.Errorf("replay is nil, replay_id: %s", opt.ReplayID)
	}

	// 兼容旧replay没有写入snapshot_meta的情况，要根据SessionID获取
	if replay.SnapshotMeta == nil {
		return s.ListSessionArtifacts(ctx, ListSessionArtifactsOption{
			SessionID: replay.SessionID,
			Display:   opt.Display,
		})
	}
	// 这里的 artifactsIDs 是一个 string 数组，表示 artifact 的 ID 列表
	artifactsIDs := replay.SnapshotMeta.ArtifactIDs
	if len(artifactsIDs) == 0 {
		return []*entity.Artifact{}, nil
	}
	return s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID:   replay.SessionID,
		ArtifactIDs: artifactsIDs,
		Display:     opt.Display,
	})
}

type UpdateArtifactOption struct {
	ID        string
	Status    *entity.ArtifactStatus
	Metadata  entity.ArtifactMetadata
	SessionID *string
}

func (s *Service) UpdateArtifact(ctx context.Context, opt UpdateArtifactOption) (*entity.Artifact, error) {
	return s.dao.UpdateArtifact(ctx, serverdal.UpdateArtifactOption{
		ID:        opt.ID,
		Status:    opt.Status,
		Metadata:  opt.Metadata,
		SessionID: opt.SessionID,
	})
}

type UpdateArtifactFileOption struct {
	ID        string
	Name      string
	LarkToken string
}

func (s *Service) UpdateArtifactFile(ctx context.Context, opt UpdateArtifactFileOption) (*entity.Artifact, error) {
	return s.dao.UpdateArtifactFiles(ctx, serverdal.UpdateArtifactFnOption{
		ID: opt.ID,
		UpdateArtifact: func(artifact *entity.Artifact) {
			var files entity.FileMetas
			for _, file := range artifact.FileMetas {
				if file.Name == opt.Name {
					file.LarkToken = opt.LarkToken
				}
				files = append(files, file)
			}
			artifact.FileMetas = files
		},
	})
}

func (s *Service) UploadImageByURL(ctx context.Context, urls []string, sessionID string) ([]*entity.Artifact, error) {
	var (
		results     = concurrentslice.NewConcurrentSlice[*entity.Artifact]()
		failedCount int64
	)
	// 并发上传

	wg := conc.NewWaitGroup()
	for _, u := range urls {
		u := u
		wg.Go(func() {
			imageReader, contentType, contentLength, err := downloadImageByURL(ctx, u)
			if err != nil {
				atomic.AddInt64(&failedCount, 1)
				log.V1.CtxError(ctx, "failed to download image by url `%s`: %v", u, err)
				return
			}
			artifact, err := s.CreateArtifact(ctx, CreateArtifactOption{
				SessionID: sessionID,
				Type:      entity.ArtifactTypeImage,
				Source:    entity.ArtifactSourceUser,
				Key:       s.idGen.NewID(),
				Metadata:  nil,
			})
			if err != nil {
				atomic.AddInt64(&failedCount, 1)
				log.V1.CtxError(ctx, "failed to create artifact: %v", err)
				return
			}
			mime := mimetype.Lookup(contentType)
			filePath := fmt.Sprintf("%s%s", util.RandomString(40), mime.Extension()) // 随机生成文件名
			artifact, err = s.UploadArtifact(ctx, UploadArtifactOption{
				Artifact:    artifact,
				Path:        filePath,
				Content:     imageReader,
				ContentSize: contentLength,
				Type:        lo.ToPtr(entity.ArtifactTypeImage),
			})
			if err != nil {
				atomic.AddInt64(&failedCount, 1)
				log.V1.CtxError(ctx, "failed to upload artifact: %v", err)
				return
			}
			results.Append(artifact)
		})
	}
	wg.Wait()
	if failedCount > 0 {
		log.V1.CtxError(ctx, "failed to upload image with count: %d", failedCount)
		return nil, errors.Errorf("failed to upload image with count: %d", failedCount)
	}
	return results.GetSlice(), nil
}

func downloadImageByURL(ctx context.Context, url string) (io.ReadCloser, string, int64, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, "", 0, errors.WithMessagef(err, "failed to do http get request")
	}
	if resp.StatusCode != http.StatusOK {
		defer resp.Body.Close()
		buf, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, "", 0, errors.WithMessagef(err, "failed to read response body")
		}
		return nil, "", 0, errors.Errorf("unexpected status: %d, resp: %v", resp.StatusCode, string(buf))
	}
	// 检查内容类型是否为图片
	contentType := resp.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		return nil, "", 0, errors.Errorf("unexpected content type: %s", contentType)
	}
	return resp.Body, contentType, resp.ContentLength, nil
}

type UploadArtifactOption struct {
	Artifact    *entity.Artifact
	Path        string
	Content     io.ReadCloser
	ContentSize int64
	Type        *entity.ArtifactType
}

var imageExts = []string{"gif", "jpeg", "tiff", "png", "heic", "heif", "avif", "avis", "bmp", "jpg", "jpe"}

const (
	MaxArtifactContentSize = 4 * 1024 * 1024 * 1024 // 4GB，限制最大文件大小4GB
	MaxLimitedWriterSize   = 32 * 1024              // 32K，对于非图片的文件，超过32K的内容会被截断，主要用于判断类型
	MaxImageLimitedSize    = 100 * 1024 * 1024      // 100MB，图片文件最大限制100M
)

func (s *Service) UploadArtifact(ctx context.Context, opt UploadArtifactOption) (result *entity.Artifact, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("UploadArtifact", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	// for runtime, upload happens immediately after artifact is created and we need to ensure to read latest data
	// artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.ID, Sync: true})
	// if err != nil {
	// 	return nil, errors.WithMessage(err, "artifact not found")
	// }
	artifact := opt.Artifact
	if artifact == nil {
		return nil, errors.New("artifact is nil")
	}

	if opt.ContentSize > MaxArtifactContentSize {
		return nil, errors.WithMessagef(serverservice.ErrorArtifactSizeExceeded, "artifact size exceeds max size, max size: %d, actual size: %d", MaxArtifactContentSize, opt.ContentSize)
	}

	fullpath := path.Join(tosPrefix(artifact), opt.Path)
	log.V1.CtxInfo(ctx, "save to TOS, path: %s", fullpath)
	var (
		content   []byte
		imagexURI string
		buf       bytes.Buffer
		md5Str    string
		md5Hash   = md5.New()
	)
	artifactType := artifact.Type
	if opt.Type != nil {
		artifactType = *opt.Type
	}
	linkSource := entity.LinkArtifactKeySource("")
	if artifact.Type == entity.ArtifactTypeLink {
		linkSource = "link"
	}
	// 这里传空字符串，先根据path或linkSource取一次artifactType
	artifactType, subType := GetArtifactTypeAndSubType(opt.Path, []byte(""), linkSource)

	if artifactType != entity.ArtifactTypeLink {
		// 空文件不上传
		if opt.ContentSize > 0 {
			err = backoff.Retry(func() error {
				// 每次重试前，都要重置md5Hash和buf
				md5Hash.Reset()
				buf.Reset()
				seeker, seekable := opt.Content.(io.Seeker)
				if seekable {
					seeker.Seek(0, io.SeekStart) // 重置到开头,opt.Content也被移动到开头
				}
				writers := []io.Writer{md5Hash}
				if artifactType == entity.ArtifactTypeImage {
					writers = append(writers, NewLimitedWriter(&buf, MaxImageLimitedSize))
				} else {
					writers = append(writers, NewLimitedWriter(&buf, MaxLimitedWriterSize))
				}
				reader := io.TeeReader(opt.Content, io.MultiWriter(writers...))

				tosCtx, cancel := context.WithTimeout(ctx, 5*time.Minute) //timeout  最长5min
				defer cancel()
				err = s.tos.PutObject(tosCtx, fullpath, opt.ContentSize, reader)
				if err != nil {
					if tossdk.IsRetryableError(err) && seekable {
						return err
					}
					return backoff.Permanent(err)
				}
				return nil
			}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
		}
		if err != nil {
			return nil, errors.WithMessage(err, "failed to upload artifact")
		}

		// 需要再reader被tos.PutObject读取后，md5才可以计算出来
		md5Str = hex.EncodeToString(md5Hash.Sum(nil))

		if artifactType == entity.ArtifactTypeImage {
			imagexConf := s.tccConf.ImageXConfig.GetValue()
			params := &imagex.UploadImageParam{
				ServiceId: imagexConf.ServiceID,
			}
			result, err := s.imagexClient.UploadImageByIOreader(ctx, params, &buf)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to upload image to imagex")
			}
			if len(result.Results) < 1 {
				return nil, errors.New("faild get imagex info")
			}
			if result.Results[0].UriStatus != imagex.UploadSuccessCode {
				return nil, errors.Errorf("faild upload to imagex, %d", result.Results[0].UriStatus)
			}
			imagexURI = result.Results[0].Uri
		}

		//根据content更新一次artifactType
		newArtifactType, newSubType := GetArtifactTypeAndSubType(opt.Path, buf.Bytes(), linkSource)
		if newArtifactType != artifactType || newSubType != subType {
			log.V1.CtxInfo(ctx, "artifact type changed, old: %s/%s, new: %s/%s", artifactType, subType, newArtifactType, newSubType)
			artifactType = newArtifactType
			subType = newSubType
		}
		buf.Reset() //释放buf占用的内存
	} else {
		content, err = io.ReadAll(opt.Content)
		if err != nil {
			return nil, errors.WithMessage(err, "failed read content")
		}
	}

	return s.dao.UpdateArtifactFiles(ctx, serverdal.UpdateArtifactFnOption{
		ID: artifact.ID,
		UpdateArtifact: func(artifact *entity.Artifact) {
			filenameMap := lo.SliceToMap(artifact.FileMetas, func(item entity.FileMeta) (string, entity.FileMeta) {
				return item.Name, item
			})
			if _, ok := filenameMap[opt.Path]; !ok {
				artifact.FileMetas = append(artifact.FileMetas, entity.FileMeta{
					Name:      opt.Path,
					Size:      opt.ContentSize,
					Content:   string(content),
					MD5:       md5Str,
					ImageXURI: imagexURI,
					Type:      artifactType,
					SubType:   subType,
				})
			}
			sort.Sort(artifact.FileMetas)
			if artifact.Status == entity.ArtifactStatusDraft && len(artifact.FileMetas) > 0 {
				artifact.Status = entity.ArtifactStatusCreating
			}
		},
	})
}

// 目前仅限制 logs 类型的 artifact 下载，禁止非管理员访问，避免 prompt 和 token 等信息泄露
func (s *Service) checkArtifactPermission(ctx context.Context, artifact entity.Artifact, account *authentity.Account) bool {
	if artifact.Type != entity.ArtifactTypeLogs {
		return true
	}
	if account == nil {
		return false
	}
	if s.userService.IsDeveloper(account) {
		return true
	}
	return false
}

func (s *Service) CheckProjectArtifactPermission(ctx context.Context, artifact *entity.Artifact, account *authentity.Account) (bool, error) {
	if artifact.Type != entity.ArtifactTypeProject {
		return false, errors.New("not a project artifact")
	}
	if account == nil {
		return false, nil
	}
	artifactMetadata, ok := artifact.Metadata.(*entity.ProjectArtifactTypeMetadata)
	if !ok {
		return false, errors.New("invalid project artifact metadata")
	}
	if artifactMetadata.CodebaseRepoMeta == nil {
		return true, nil
	}
	// 构造缓存 key
	repoName := artifactMetadata.CodebaseRepoMeta.RepoName
	cacheKey := fmt.Sprintf("project_artifact_permission:%s:%s", repoName, account.Username)
	cached, err := s.redisClient.Get(ctx, cacheKey)
	if err == nil && cached == "true" {
		return true, nil
	}
	role, err := s.codebaseClient.GetRepoUserRole(ctx, account.Username, repoName)
	if err != nil {
		return false, err
	}
	clonePermissionRoles := []string{"master", "owner", "developer", "reporter"}
	if !lo.Contains(clonePermissionRoles, role) {
		return false, nil
	}
	// 仅缓存为 true 的权限，如果缓存 false 可能会导致用户申请权限后脏缓存导致的解释成本
	_ = s.redisClient.Set(ctx, cacheKey, "true", 24*time.Hour)
	return true, nil
}

type GetArtifactFileMetaOption struct {
	ID   string
	Name string
}

func (s *Service) GetArtifactFileMeta(ctx context.Context, opt GetArtifactFileMetaOption) (*entity.FileMeta, error) {
	artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.ID})
	if err != nil {
		return nil, err
	}

	for _, fileMeta := range artifact.FileMetas {
		if fileMeta.Name == opt.Name {
			return &fileMeta, nil
		}
	}
	return nil, errors.New("artifact file not found")
}

type GetArtifactFileOption struct {
	Artifact entity.Artifact
	Path     string
	Account  *authentity.Account
	TosCtx   *context.Context
	// SkipPermissionCheck 用于特殊场景（如 MCP trace）跳过权限检查
	SkipPermissionCheck bool
}

func (s *Service) GetArtifactFile(ctx context.Context, opt GetArtifactFileOption) (r io.ReadCloser, meta *entity.FileMeta, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("GetArtifactFile", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.Artifact.ID, Sync: true})
	if err != nil {
		return nil, nil, err
	}

	if !opt.SkipPermissionCheck && !s.checkArtifactPermission(ctx, opt.Artifact, opt.Account) {
		return io.NopCloser(bytes.NewReader([]byte(""))), nil, nil
	}

	if artifact.Type == entity.ArtifactTypeProject {
		return s.getProjectArtifactFile(ctx, artifact, opt)
	}

	var file *entity.FileMeta
	for _, fileMeta := range artifact.FileMetas {
		if fileMeta.Name == opt.Path {
			file = &fileMeta
			break
		}
	}
	if file == nil {
		return nil, nil, serverservice.ErrorArtifactNotFound
	}

	if artifact.Type == entity.ArtifactTypeLink {
		content := strings.ReplaceAll(file.Content, "neuma-boe.bytedance.net", "aime-boe-app.bytedance.net")
		content = strings.ReplaceAll(content, "neuma-app.bytedance.net", "aime-app.bytedance.net")
		content = strings.ReplaceAll(content, "devagent-boe-app.bytedance.net", "aime-boe-app.bytedance.net")
		content = strings.ReplaceAll(content, "devagent-app.bytedance.net", "aime-app.bytedance.net")
		return io.NopCloser(strings.NewReader(content)), file, nil
	}

	var tosCtx context.Context = ctx
	if opt.TosCtx != nil {
		tosCtx = *opt.TosCtx
	}

	// 空文件直接返回，不从tos取
	if file.Size == 0 {
		return io.NopCloser(strings.NewReader("")), file, nil
	}
	obj, err := s.tos.GetObject(tosCtx, path.Join(tosPrefix(artifact), opt.Path))
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to retrieve artifact file")
	}
	return obj.R, file, nil
}

func (s *Service) getProjectArtifactFile(ctx context.Context, artifact *entity.Artifact, opt GetArtifactFileOption) (io.ReadCloser, *entity.FileMeta, error) {
	if artifact.Type != entity.ArtifactTypeProject {
		return nil, nil, errors.New("artifact type is not project")
	}
	artifactMetadata := artifact.Metadata.(*entity.ProjectArtifactTypeMetadata)
	agentRun, err := s.runtimeDao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: artifact.SessionID,
	})
	if err != nil {
		return nil, nil, err
	}
	if err = s.makeSureRuntimeReady(ctx, artifact.SessionID, agentRun.RuntimeMetadata, opt.Account); err != nil {
		logs.V1.CtxInfo(ctx, "make sure runtime ready error: %v", err)
		return nil, nil, err
	}

	runtimeProvider := s.runtimeProviderRegistry.GetProvider(agentRun.RuntimeMetadata.RuntimeProvider)
	containerID := agentRun.RuntimeMetadata.ContainerID

	opt.Path = strings.TrimSuffix(opt.Path, "/")
	if strings.HasPrefix(opt.Path, artifactMetadata.Name) {
		opt.Path = strings.TrimPrefix(opt.Path, artifactMetadata.Name)
	}
	logs.V1.CtxInfo(ctx, "download path: %s", opt.Path)
	isDir := false
	if opt.Path == "" {
		isDir = true
	} else {
		cmd := fmt.Sprintf("git ls-tree -z --full-name %s %s", artifactMetadata.Commit, opt.Path)
		output, err := runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, artifactMetadata.Path, runtimedal.WithSyncExecution())
		if err != nil {
			return nil, nil, errors.WithMessage(err, "failed to check if path is dir")
		}
		p := lstree.NewParser(strings.NewReader(output))
		for {
			entry, err := p.NextEntry()
			if err != nil {
				return nil, nil, errors.WithMessage(err, "failed to check if path is dir")
			}
			if entry.Path == opt.Path {
				isDir = entry.Type == lstree.Tree
				break
			}
		}
	}
	if !isDir {
		cmd := fmt.Sprintf("git show %s:%s", artifactMetadata.Commit, opt.Path)
		fileContent, err := runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, artifactMetadata.Path, runtimedal.WithSyncExecution())
		if err != nil {
			log.V1.CtxError(ctx, "failed to get file content: %v", err)
		}
		return io.NopCloser(strings.NewReader(fileContent)), &entity.FileMeta{
			Name:    opt.Path,
			Size:    int64(len(fileContent)),
			Content: fileContent,
		}, nil
	}
	// dir download
	// 目录或整个仓库
	// git archive --format=zip <commit> [<path>]
	var archiveCmd string
	outputName := fmt.Sprintf("myarchive-%d.zip", time.Now().UnixNano())
	if opt.Path == "" {
		archiveCmd = fmt.Sprintf("git archive --format=zip --output=%s %s", outputName, artifactMetadata.Commit)
	} else {
		archiveCmd = fmt.Sprintf("git archive --format=zip --output=%s %s %s", outputName, artifactMetadata.Commit, opt.Path)
	}
	log.V1.CtxInfo(ctx, "archive cmd: %s", archiveCmd)
	_, err = runtimeProvider.ExecuteInContainer(ctx, "", archiveCmd, containerID, artifactMetadata.Path, runtimedal.WithSyncExecution(), runtimedal.WithTimeout(10*time.Minute))
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to get zip content")
	}
	defer func() {
		_, _ = runtimeProvider.ExecuteInContainer(ctx, "", fmt.Sprintf("rm %s", outputName), containerID, artifactMetadata.Path)
	}()
	readCloser, err := runtimeProvider.DownloadContainerFile(ctx, "", containerID, filepath.Join(artifactMetadata.Path, outputName))
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to get zip content")
	}

	return readCloser, &entity.FileMeta{
		// 这里的 Name 特指 zip 的文件名
		Name: lo.TernaryF(isDir, func() string {
			return fmt.Sprintf("%s.zip", artifactMetadata.Name)
		}, func() string {
			return filepath.Base(opt.Path)
		}),
		Size: 0,
	}, nil
}

func (s *Service) GetArtifactFileContent(ctx context.Context, opt GetArtifactFileOption) (b []byte, meta *entity.FileMeta, err error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	defer func() {
		reportMetricsFunc("GetArtifactFileContent", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	reader, meta, err := s.GetArtifactFile(ctx, opt)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to retrieve artifact file")
	}
	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to read artifact file")
	}
	return body, meta, nil
}

func (s *Service) UpdateArtifactToDisplay(ctx context.Context, id string) (*entity.Artifact, error) {
	artifact, err := s.dao.UpdateArtifact(ctx, dal.UpdateArtifactOption{
		ID:      id,
		Display: pointer.To(true),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update artifact")
	}
	return artifact, nil
}

type CopyToNewArtifactOption struct {
	OldArtifactID string
	NewSessionID  string
}

func (s *Service) CopyToNewArtifact(ctx context.Context, opt CopyToNewArtifactOption) (*entity.Artifact, error) {
	artifact, err := s.dao.GetArtifact(ctx, serverdal.GetArtifactOption{ID: opt.OldArtifactID})
	if err != nil {
		return nil, err
	}
	if opt.NewSessionID == "" {
		return nil, errors.New("session id should not be empty")
	}

	newArtifact, err := s.dao.CreateArtifact(ctx, serverdal.CreateArtifactOption{
		SessionID: opt.NewSessionID,
		ID:        s.idGen.NewID(),
		Type:      artifact.Type,
		Status:    artifact.Status,
		Source:    artifact.Source,
		Key:       s.idGen.NewID(),
		Version:   1,
		Files:     artifact.FileMetas,
		Metadata:  artifact.Metadata,
		Display:   artifact.Display,
	})

	files := artifact.FileMetas
	if len(files) == 0 {
		return newArtifact, nil
	}
	for _, file := range files {
		err = s.tos.CopyObject(ctx, path.Join(tosPrefix(artifact), file.Name), path.Join(tosPrefix(newArtifact), file.Name))
		if err != nil {
			return nil, errors.WithMessage(err, "failed to copy object")
		}
	}
	return newArtifact, nil
}

func tosPrefix(artifact *entity.Artifact) string {
	prefix := "next_artifacts"
	switch artifact.Type {
	case entity.ArtifactTypeFile:
		prefix += "/file"
	case entity.ArtifactTypeCode:
		prefix += "/code"
	case entity.ArtifactTypeImage:
		prefix += "/image"
	case entity.ArtifactTypeLink:
		prefix += "/link"
	case entity.ArtifactTypeLogs:
		prefix += "/logs"
	case entity.ArtifactTypeResult:
		prefix += "/result"
	}
	return path.Join(prefix, artifact.ID)
}

type GetAndUpdateAttachmentArtifactOption struct {
	SessionID         string
	Attachment        agententity.Attachment
	UpdateDisplay     bool
	IsAgentAttachment bool
	MessageID         string
}

func (s *Service) GetAndUpdateAttachmentArtifact(ctx context.Context, opt GetAndUpdateAttachmentArtifactOption) (*entity.Attachment, error) {
	sessionID := opt.SessionID
	attachment := opt.Attachment
	updateDisplay := opt.UpdateDisplay
	isAgentAttachment := opt.IsAgentAttachment

	// 根据filename获取匹配的文件
	var (
		finalAttachmentArtifactID string
		finalAttachmentType       string
		fileMeta                  entity.FileMeta
		url                       string
		title                     string
		version                   int
	)

	switch attachment.Type {
	case agententity.AttachmentTypeLarkDoc, agententity.AttachmentTypeLarkSheet, agententity.AttachmentTypeURL:
		if attachment.URL != "" {
			url = attachment.URL
		}
		if isHTTPURL(attachment.Path) { // 兼容旧的逻辑，可能会通过 path 传飞书文档链接
			url = attachment.Path
			title = attachment.Path
		} else {
			title = attachment.Path
		}
		// 做artifact创建
		var artifactKeySource entity.LinkArtifactKeySource
		switch attachment.Type {
		case agententity.AttachmentTypeLarkDoc:
			artifactKeySource = entity.LinkArtifactKeySourceLarkDoc
		case agententity.AttachmentTypeLarkSheet:
			artifactKeySource = entity.LinkArtifactKeySourceLarkSheet
		case agententity.AttachmentTypeURL:
			artifactKeySource = entity.LinkArtifactKeySourceURL
		}

		// 先判断是否存在
		var artifact *entity.Artifact
		var err error
		// 只有 Agent 发送过来带有 Attachment的消息才需要创建新的 Artifact 版本
		if isAgentAttachment {
			artifact, err = s.createLarkDocVersionIfExists(ctx, &CreateLarkDocVersionIfExistsOption{
				SessionID:         sessionID,
				ArtifactKeySource: artifactKeySource,
				URL:               url,
				Attachment:        &attachment,
				MessageID:         opt.MessageID,
			})
			if err != nil {
				return nil, err
			}
		}
		createLinkArtifactOption := CreateLinkArtifactOption{
			Url:               url,
			ArtifactKeySource: artifactKeySource,
			ArtifactSource:    entity.ArtifactSourceAgent,
			Title:             title,
		}
		if artifact == nil {
			artifact, err = s.CreateLinkArtifact(ctx, sessionID, createLinkArtifactOption)
		}
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to create lark doc artifact")
		}
		if artifact == nil {
			return nil, serverservice.ErrorArtifactNotFound
		}

		finalAttachmentArtifactID = artifact.ID
		finalAttachmentType = string(artifact.Type)
		if len(artifact.FileMetas) > 0 {
			fileMeta = artifact.FileMetas[0]
		}
		version = int(artifact.Version)
	case agententity.AttachmentTypeDeployment:
		// 如果是deployment的artifacts，需要做一下trick转换
		// 先通过artifact id查找到 deployment id, 再通过 deployment id 查到所对应的artifacts id 做转换
		artifact, err := s.GetArtifact(ctx, GetArtifactOption{ID: attachment.ArtifactID, Sync: true})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get attachment deployment artifact, artifact id:%s, err: %s", attachment.ArtifactID, err)
		}
		if artifact == nil {
			return nil, serverservice.ErrorArtifactNotFound
		}

		deployment, err := s.dao.GetDeploymentByArtifactID(ctx, serverdal.GetDeploymentByArtifactIDOption{
			ArtifactID: artifact.ID,
			Sync:       true,
			Cache:      true,
		})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get attachment deployment artifact, artifact id:%s", attachment.ArtifactID)
		}

		deploymentArtifactKey := s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceDeployment, deployment.ID, "")
		deploymentArtifact, err := s.dao.GetArtifactByArtifactKey(ctx, deployment.SessionID, deploymentArtifactKey, 1, true) // 部署artifact key版本是固定的1
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get attachment deployment artifact, artifact key:%s", deploymentArtifactKey)
		}

		finalAttachmentArtifactID = deploymentArtifact.ID
		finalAttachmentType = string(deploymentArtifact.Type)
		url = deployment.URL
		if len(deploymentArtifact.FileMetas) > 0 {
			fileMeta = deploymentArtifact.FileMetas[0]
		}
		log.V1.CtxInfo(ctx, "attachment artifact is deployed, convert to deployment artifact. origin artifact: %s, attachment artifact: %s", attachment.ArtifactID, deploymentArtifact.ID)
	case agententity.AttachmentTypeProject:
		artifact, err := s.GetArtifact(ctx, GetArtifactOption{ID: attachment.ArtifactID, Sync: true})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get attachment deployment artifact, artifact id:%s, err: %s", attachment.ArtifactID, err)
		}
		if artifact == nil {
			return nil, serverservice.ErrorArtifactNotFound
		}
		version = int(artifact.Version)
		if len(artifact.FileMetas) > 0 {
			fileMeta = artifact.FileMetas[0]
		} else {
			artifactMetadata, ok := artifact.Metadata.(*entity.ProjectArtifactTypeMetadata)
			if !ok {
				return nil, errors.New("failed to convert artifact metadata")
			}
			fileMeta = entity.FileMeta{
				Name: artifactMetadata.Name,
				Type: entity.ArtifactTypeProject,
			}
		}
		finalAttachmentArtifactID = artifact.ID
		finalAttachmentType = string(artifact.Type)

	default: // 默认文件处理方式
		artifact, err := s.GetArtifact(ctx, GetArtifactOption{ID: attachment.ArtifactID, Sync: true})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to operate artifact, id: %v", attachment.ArtifactID)
		}
		if artifact == nil {
			return nil, serverservice.ErrorArtifactNotFound
		}

		finalAttachmentArtifactID = artifact.ID
		finalAttachmentType = string(artifact.Type)
		for _, file := range artifact.FileMetas {
			if file.Name != attachment.Path {
				continue
			}
			fileMeta = file
			break
		}
		if fileMeta.Name == "" {
			return nil, serverservice.ErrorArtifactNotFound
		}
		// 如果是图片类型，生成 url
		if fileMeta.Type == entity.ArtifactTypeImage {
			url = fileMeta.ImageURL(s.tccConf.ImageXConfig.GetValue().DefaultDomain, s.tccConf.ImageXConfig.GetValue().DefaultTemplateID)
		}
	}

	if finalAttachmentArtifactID == "" || finalAttachmentType == "" {
		return nil, errors.WithMessage(serverservice.ErrorArtifactNotFound, "artifact id or type not found")
	}

	// 更新为结果产物展示
	if updateDisplay {
		_, err := s.UpdateArtifactToDisplay(ctx, finalAttachmentArtifactID)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to update artifact to display, id: %v", finalAttachmentArtifactID)
		}
	}
	return &entity.Attachment{
		ID:            finalAttachmentArtifactID,
		FileName:      fileMeta.Name,
		Path:          fileMeta.Name,
		Type:          finalAttachmentType,
		URL:           url,
		ContentType:   string(fileMeta.Type),
		SubType:       fileMeta.SubType,
		ContentLength: fileMeta.Size,
		LarkToken:     fileMeta.LarkToken,
		Version:       version,
	}, nil
}

type CreateLarkDocVersionIfExistsOption struct {
	SessionID         string
	ArtifactKeySource entity.LinkArtifactKeySource
	URL               string
	Attachment        *agententity.Attachment
	MessageID         string
}

// createLarkDocVersionIfExists 检查是否存在相同URL的飞书文档，如果存在则创建新版本
func (s *Service) createLarkDocVersionIfExists(ctx context.Context, opt *CreateLarkDocVersionIfExistsOption) (*entity.Artifact, error) {
	var artifact *entity.Artifact
	log.V1.CtxInfo(ctx, " create agent attachment new version if exists, session_id: %s, url: %s", opt.SessionID, opt.URL)
	if opt.ArtifactKeySource == entity.LinkArtifactKeySourceLarkDoc {
		messageID := ""
		if s.enableMessageIDAsArtifactKey(ctx) {
			messageID = opt.MessageID
		}
		artifactKey := s.GenerateLinkArtifactKey(entity.LinkArtifactKeySourceLarkDoc, util.MD5([]byte(opt.URL)), messageID)
		latestArtifact, err := s.dao.GetLatestVersionArtifactByKey(ctx, opt.SessionID, artifactKey, true)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.WithMessagef(err, "failed to get lark doc artifact")
		}
		// 如果存在相同 url ，则创建一个新的版本
		if latestArtifact != nil {
			artifact, err = s.dao.CreateArtifact(ctx, serverdal.CreateArtifactOption{
				SessionID: opt.SessionID,
				ID:        s.idGen.NewID(),
				Status:    entity.ArtifactStatusCompleted,
				Source:    latestArtifact.Source,
				Type:      latestArtifact.Type,
				Key:       latestArtifact.Key,
				Version:   latestArtifact.Version + 1,
				Metadata:  latestArtifact.Metadata,
				Files: lo.Map(latestArtifact.FileMetas, func(fileMeta entity.FileMeta, index int) entity.FileMeta {
					if fileMeta.LarkDocVersionToken != "" {
						fileMeta.LarkDocVersionToken = ""
					}

					fileUrl := gjson.Get(fileMeta.Content, "url").String()
					if opt.Attachment != nil && fileUrl == opt.URL {
						fileMeta.Name = opt.Attachment.Path
					}
					return fileMeta
				}),
			})
			if err != nil {
				if errors.Is(err, gorm.ErrDuplicatedKey) || db.IsDuplicateKeyError(err) {
					return s.dao.GetArtifactByArtifactKey(ctx, opt.SessionID, artifactKey, latestArtifact.Version+1, true)
				}
				return nil, err
			}
		}
	}
	return artifact, nil
}

// TODO: 后面删除这个逻辑，仅上线期间需要
func (s *Service) enableMessageIDAsArtifactKey(ctx context.Context) bool {
	exists, err := s.redisClient.Exists(ctx, "aime:larkdoc:messageid:enable")
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to check message id as artifact key, err: %v", err)
		return false
	}
	if exists == 1 {
		return false
	}
	return true
}

type GetSessionArtifactIDsOption struct {
	Types   []entity.ArtifactType
	Display *bool
	Sync    bool
}

func (s *Service) GetSessionArtifactIDs(ctx context.Context, sessionID string, opt GetSessionArtifactIDsOption) ([]string, error) {
	return s.dao.GetSessionArtifactIDs(ctx, sessionID, dal.GetSessionArtifactIDsOption{
		Types:   opt.Types,
		Display: opt.Display,
		Sync:    opt.Sync,
	})
}

type GetArtifactFileContentOption struct {
	Artifact entity.Artifact
	Path     string
	Account  *authentity.Account
}

type BatchDownloadArtifactOption struct {
	SessionID         string
	ArtifactIDToPaths map[string][]string
	Account           *authentity.Account
}

// BatchDownloadArtifacts downloads multiple files from multiple artifacts
func (s *Service) BatchDownloadArtifacts(ctx context.Context, opt BatchDownloadArtifactOption) (map[string]map[string][]byte, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var err error
	defer func() {
		reportMetricsFunc("BatchDownloadArtifacts", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	result := make(map[string]map[string][]byte)

	// 从 ArtifactIDToPaths 中提取所有的 artifact IDs
	artifactIDs := make([]string, 0, len(opt.ArtifactIDToPaths))
	for artifactID := range opt.ArtifactIDToPaths {
		artifactIDs = append(artifactIDs, artifactID)
	}

	// Get reference map for the session
	referenceMap, err := s.GetReferenceMap(ctx, opt.SessionID)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get reference map for session %s: %v", opt.SessionID, err)
		// Continue without reference map
	}

	// Create a mapper to process artifacts in parallel
	mapper := iter.Mapper[string, map[string][]byte]{
		MaxGoroutines: 4,
	}

	// Process each artifact
	artifactResults, err := mapper.MapErr(artifactIDs, func(artifactID *string) (map[string][]byte, error) {
		// Get the artifact
		artifact, err := s.GetArtifact(ctx, GetArtifactOption{ID: *artifactID, Sync: true})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get artifact %s", *artifactID)
		}

		// 以防夹带非这个session的artifact_id
		if artifact.SessionID != opt.SessionID {
			return nil, errors.WithMessagef(serverservice.ErrorArtifactNotFound, "artifact %s not in session %s", *artifactID, opt.SessionID)
		}

		// Check permission
		if !s.checkArtifactPermission(ctx, *artifact, opt.Account) {
			return make(map[string][]byte), nil
		}

		// Create a map to store file contents for this artifact
		artifactFiles := make(map[string][]byte)

		// 获取当前 artifact 对应的路径
		paths, ok := opt.ArtifactIDToPaths[*artifactID]
		if !ok {
			return artifactFiles, nil
		}

		// Process each path for this artifact
		for _, path := range paths {
			// Check if the file exists in the artifact
			fileExists := false
			for _, fileMeta := range artifact.FileMetas {
				if fileMeta.Name == path {
					fileExists = true
					break
				}
			}

			if !fileExists && artifact.Type != entity.ArtifactTypeProject {
				continue
			}

			// Get the file content
			content, meta, err := s.GetArtifactFileContent(ctx, GetArtifactFileOption{
				Artifact: *artifact,
				Path:     path,
				Account:  opt.Account,
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to get file content for artifact %s, path %s: %v", *artifactID, path, err)
				continue
			}

			// Apply ReplaceCiteTagsByLine if the file is a markdown and have a reference map
			if len(referenceMap) > 0 && entity.IsMarkdownFile(path) {
				contentReader := bytes.NewReader(content)
				// Apply ReplaceCiteTagsByLine
				processedReader, size, err := s.ReplaceCiteTagsByLine(contentReader, meta, referenceMap)
				if err != nil {
					log.V1.CtxWarn(ctx, "failed to replace cite tags for artifact %s, path %s: %v", *artifactID, path, err)
					// Continue with original content
				} else {
					processedBody := make([]byte, size)
					_, err = processedReader.Read(processedBody)
					if err != nil {
						log.V1.CtxWarn(ctx, "failed to read processed content for artifact %s, path %s: %v", *artifactID, path, err)
					} else {
						content = processedBody
					}
				}
			}

			// Store the content
			if artifact.Type == entity.ArtifactTypeProject {
				path = meta.Name
			}
			artifactFiles[path] = content
		}

		return artifactFiles, nil
	})

	if err != nil {
		return nil, err
	}

	// Combine results
	for i, artifactID := range artifactIDs {
		result[artifactID] = artifactResults[i]
	}

	return result, nil
}

type BatchDownloadArtifactZipOption struct {
	SessionID         string
	ArtifactIDToPaths map[string][]string
	Account           *authentity.Account
	ZipName           string
}

// BatchDownloadArtifactsAsZip downloads multiple files from multiple artifacts and packages them as a ZIP file
func (s *Service) BatchDownloadArtifactsAsZip(ctx context.Context, opt BatchDownloadArtifactZipOption) (io.ReadCloser, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var err error
	defer func() {
		reportMetricsFunc("BatchDownloadArtifactsAsZip", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()

	// Create a buffer to store the ZIP file
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)

	// Download the artifacts
	results, err := s.BatchDownloadArtifacts(ctx, BatchDownloadArtifactOption{
		SessionID:         opt.SessionID,
		ArtifactIDToPaths: opt.ArtifactIDToPaths,
		Account:           opt.Account,
	})
	if err != nil {
		return nil, err
	}

	// Add files to the ZIP
	for artifactID, files := range results {
		for path, content := range files {
			// Create a file in the ZIP with a flattened path
			// Use the path directly without creating artifact ID directories
			zipPath := path
			writer, err := zipWriter.Create(zipPath)
			if err != nil {
				log.V1.CtxError(ctx, "failed to create file in ZIP for artifact %s, path %s: %v", artifactID, path, err)
				continue
			}

			// Write the content to the ZIP
			_, err = writer.Write(content)
			if err != nil {
				log.V1.CtxError(ctx, "failed to write content to ZIP for artifact %s, path %s: %v", artifactID, path, err)
				continue
			}
		}
	}

	// Close the ZIP writer
	err = zipWriter.Close()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to close ZIP writer")
	}

	// Return a reader for the ZIP buffer
	return io.NopCloser(bytes.NewReader(zipBuffer.Bytes())), nil
}

type ListProjectArtifactFilesOption struct {
	Account     *authentity.Account
	SessionID   string `json:"session_id"`
	ArtifactKey string `json:"artifact_key"`
	Version     string `json:"version"`
	FilePath    string `json:"file_path"`
	Depth       *int32 `json:"depth"`
}

func (s *Service) ListProjectArtifactFiles(ctx context.Context, opt ListProjectArtifactFilesOption) ([]*nextagent.FileNode, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var err error
	defer func() {
		reportMetricsFunc("ListProjectArtifactFiles", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()
	var (
		version  int
		find     bool
		artifact *entity.Artifact
	)
	version, err = strconv.Atoi(opt.Version)
	if err != nil {
		return nil, errors.Wrap(err, "invalid version")
	}
	allVersionArtifacts, err := s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID: opt.SessionID,
		Key:       lo.ToPtr(opt.ArtifactKey),
	})
	if err != nil {
		return nil, err
	}
	if artifact, find = lo.Find(allVersionArtifacts, func(artifact *entity.Artifact) bool {
		return int(artifact.Version) == version
	}); !find {
		return nil, fmt.Errorf("invalid revision, version not found")
	}
	artifactMetadata := artifact.Metadata.(*entity.ProjectArtifactTypeMetadata)

	agentRun, err := s.runtimeDao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}

	if err = s.makeSureRuntimeReady(ctx, opt.SessionID, agentRun.RuntimeMetadata, opt.Account); err != nil {
		logs.V1.CtxInfo(ctx, "make sure runtime ready error: %v", err)
		return nil, err
	}

	runtimeProvider := s.runtimeProviderRegistry.GetProvider(agentRun.RuntimeMetadata.RuntimeProvider)
	containerID := agentRun.RuntimeMetadata.ContainerID

	// 构造命令
	depth := int32(1)
	if opt.Depth != nil {
		depth = *opt.Depth
	}
	if opt.FilePath != "" {
		// 确保带有/结束
		opt.FilePath = fmt.Sprintf("%s/", filepath.Clean(opt.FilePath))
	}
	var entries lstree.Entries
	if depth == 1 {
		// 递归获取所有文件（性能优化见下）
		cmd := fmt.Sprintf("git ls-tree -z --full-name %s %s", artifactMetadata.Commit, opt.FilePath)
		stdout, err := runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, artifactMetadata.Path, runtimedal.WithSyncExecution())
		if err != nil {
			return nil, fmt.Errorf("git ls-tree error: %w", err)
		}
		log.V1.CtxInfo(ctx, "command: %s, stdout: %s", cmd, stdout)
		p := lstree.NewParser(strings.NewReader(stdout))
		for {
			entry, err := p.NextEntry()
			if err != nil {
				break
			}
			entries = append(entries, *entry)
		}
		log.V1.CtxInfo(ctx, "entries length: %v", len(entries))
	} else {
		// depth > 1，递归获取所有文件（性能优化见下）
		// 并发访问 tree 和 blob
		wg := conc.WaitGroup{}
		var blobErr, treeErr error
		var blobOutput, treeOutput string
		var mu sync.Mutex
		wg.Go(func() {
			cmd := fmt.Sprintf("git ls-tree -r -z --full-name %s %s", artifactMetadata.Commit, opt.FilePath)
			blobOutput, blobErr = runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, artifactMetadata.Path, runtimedal.WithSyncExecution())
			if err != nil {
				return
			}
			log.V1.CtxInfo(ctx, "command: %s, stdout: %s", cmd, blobOutput)
			p := lstree.NewParser(strings.NewReader(blobOutput))
			for {
				entry, err := p.NextEntry()
				if err != nil {
					break
				}
				mu.Lock()
				entries = append(entries, *entry)
				mu.Unlock()
			}
			log.V1.CtxInfo(ctx, "entries length: %v", len(entries))
		})
		wg.Go(func() {
			cmd := fmt.Sprintf("git ls-tree -r -d -z --full-name %s %s", artifactMetadata.Commit, opt.FilePath)
			treeOutput, treeErr = runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, artifactMetadata.Path, runtimedal.WithSyncExecution())
			if treeErr != nil {
				return
			}
			log.V1.CtxInfo(ctx, "command: %s, stdout: %s", cmd, treeOutput)
			p := lstree.NewParser(strings.NewReader(treeOutput))
			for {
				entry, err := p.NextEntry()
				if err != nil {
					break
				}
				mu.Lock()
				entries = append(entries, *entry)
				mu.Unlock()
			}
			log.V1.CtxInfo(ctx, "entries length: %v", len(entries))
		})
		wg.Wait()
		if blobErr != nil {
			return nil, fmt.Errorf("git ls-tree error: %w", blobErr)
		}
		if treeErr != nil {
			return nil, fmt.Errorf("git ls-tree -d error: %w", treeErr)
		}
	}
	fileNodes := BuildFileTreeWithDepth(entries, opt.FilePath, int(depth))
	return fileNodes, nil
}

func BuildFileTree(entries lstree.Entries, path string) []*nextagent.FileNode {
	if strings.HasSuffix(path, "/") {
		path = path[:len(path)-1]
	}
	nodeMap := make(map[string]*nextagent.FileNode)

	// 先创建根节点（即 path 本身，空串时为仓库根）
	root := &nextagent.FileNode{
		Path:  path,
		IsDir: true,
	}
	nodeMap[path] = root

	for _, entry := range entries {
		// 只处理以 path 为前缀的 entry
		if path != "" && entry.Path != path && !strings.HasPrefix(entry.Path, path+"/") {
			continue
		}
		// 计算当前 entry 的所有父路径
		relPath := entry.Path
		if path != "" {
			relPath = strings.TrimPrefix(entry.Path, path)
			relPath = strings.TrimPrefix(relPath, "/")
		}
		parts := strings.Split(relPath, "/")

		curPath := path
		parent := root

		// 递归创建所有父目录节点
		for i, part := range parts {
			if part == "" {
				continue
			}
			curPath = joinPath(curPath, part)
			node, exists := nodeMap[curPath]
			isLast := i == len(parts)-1
			if !exists {
				node = &nextagent.FileNode{
					Path:   curPath,
					Name:   part,
					IsDir:  !isLast || entry.Type == lstree.Tree,
					IsLeaf: isLast && entry.Type == lstree.Blob,
				}
				nodeMap[curPath] = node
				parent.Children = append(parent.Children, node)
			}
			parent = node
		}
	}

	return root.Children
}

func joinPath(parent, child string) string {
	if parent == "" {
		return child
	}
	return parent + "/" + child
}

func BuildFileTreeWithDepth(entries lstree.Entries, path string, depth int) []*nextagent.FileNode {
	if depth <= 0 {
		return nil
	}
	if strings.HasSuffix(path, "/") {
		path = path[:len(path)-1]
	}
	var filtered lstree.Entries
	for _, entry := range entries {
		if path == "" {
			// 计算 entry 的层级（根目录下是第 1 层）
			layer := 1
			if entry.Path != "" {
				layer += strings.Count(entry.Path, "/")
			}
			if layer <= depth {
				filtered = append(filtered, entry)
			}
			continue
		}
		if entry.Path == path {
			filtered = append(filtered, entry)
			continue
		}
		if !strings.HasPrefix(entry.Path, path+"/") {
			continue
		}
		rel := strings.TrimPrefix(entry.Path, path+"/")
		layer := 1
		if rel != "" {
			layer += strings.Count(rel, "/")
		}
		if layer <= depth {
			filtered = append(filtered, entry)
		}
	}
	return BuildFileTree(filtered, path)
}

type GetProjectArtifactFilesContentOption struct {
	Account         *authentity.Account
	SessionID       string
	ArtifactKey     string
	Version         string
	FilePathList    []string
	IsPreviewBinary bool
}

func (s *Service) GetProjectArtifactFilesContent(ctx context.Context, opt GetProjectArtifactFilesContentOption) ([]*nextagent.FileContent, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var err error
	defer func() {
		reportMetricsFunc("GetProjectArtifactFilesContent", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()
	var (
		version  int
		find     bool
		artifact *entity.Artifact
	)
	version, err = strconv.Atoi(opt.Version)
	if err != nil {
		return nil, errors.Wrap(err, "invalid version")
	}
	allVersionArtifacts, err := s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID: opt.SessionID,
		Key:       lo.ToPtr(opt.ArtifactKey),
	})
	if err != nil {
		return nil, err
	}
	if artifact, find = lo.Find(allVersionArtifacts, func(artifact *entity.Artifact) bool {
		return int(artifact.Version) == version
	}); !find {
		return nil, fmt.Errorf("invalid version, version not found")
	}
	artifactMetadata := artifact.Metadata.(*entity.ProjectArtifactTypeMetadata)

	agentRun, err := s.runtimeDao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}
	if err = s.makeSureRuntimeReady(ctx, opt.SessionID, agentRun.RuntimeMetadata, opt.Account); err != nil {
		logs.V1.CtxInfo(ctx, "make sure runtime ready error: %v", err)
		return nil, err
	}

	runtimeProvider := s.runtimeProviderRegistry.GetProvider(agentRun.RuntimeMetadata.RuntimeProvider)
	containerID := agentRun.RuntimeMetadata.ContainerID

	var (
		wg    = conc.WaitGroup{}
		mutex = sync.Mutex{}

		fileContentList []*nextagent.FileContent
	)

	for _, filePath := range opt.FilePathList {
		filePath := filePath
		wg.Go(func() {
			cmd := fmt.Sprintf("git show %s:%s | base64", artifactMetadata.Commit, filePath)
			fileContentEncoded, err := runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, artifactMetadata.Path, runtimedal.WithSyncExecution())
			if err != nil {
				log.V1.CtxError(ctx, "failed to get file content: %v", err)
				return
			}
			fileContent, err := base64.StdEncoding.DecodeString(fileContentEncoded)
			if err != nil {
				log.V1.CtxError(ctx, "failed to decode file content: %v", err)
				return
			}
			isBinary := enry.IsBinary(fileContent)
			mutex.Lock()
			defer mutex.Unlock()
			log.V1.CtxInfo(ctx, "command: %s, file path: %s,file content: %s", cmd, filePath, fileContent)
			fileContentList = append(fileContentList, &nextagent.FileContent{
				Path:     filePath,
				Content:  lo.If(!isBinary, string(fileContent)).ElseIf(opt.IsPreviewBinary, fileContentEncoded).Else(""),
				Size:     int64(len(fileContent)),
				IsBinary: isBinary,
			})
			return
		})
	}
	wg.Wait()

	return fileContentList, nil
}

type GetProjectArtifactDiffFilesContentOption struct {
	Account     *authentity.Account
	SessionID   string
	ArtifactKey string
	Revision    string
	FilePath    *string
}

func (s *Service) GetProjectArtifactDiffFilesContent(ctx context.Context, opt GetProjectArtifactDiffFilesContentOption) ([]*nextagent.DiffFileContent, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var err error
	defer func() {
		reportMetricsFunc("GetProjectArtifactDiffFilesContent", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()
	allVersionArtifacts, err := s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID: opt.SessionID,
		Key:       lo.ToPtr(opt.ArtifactKey),
	})
	if err != nil {
		return nil, err
	}
	var (
		beforeArtifact, afterArtifact                 *entity.Artifact
		beforeArtifactMetadata, afterArtifactMetadata *entity.ProjectArtifactTypeMetadata
		find                                          bool
	)

	// 解析 revision
	beforeVersion, afterVersion, err := ParseRevision(opt.Revision)
	if err != nil {
		return nil, fmt.Errorf("invalid revision: %w", err)
	}
	// 如果 revision 中仅包含一个版本号,那么当前版本与上一个版本比较
	if beforeVersion == afterVersion {
		beforeVersion = afterVersion - 1
	}
	if beforeVersion <= 0 {
		return nil, fmt.Errorf("invalid revision, diff api requires revision has previous version: %w", err)
	}
	logs.V1.CtxInfo(ctx, "beforeVersion: %d, afterVersion: %d", beforeVersion, afterVersion)
	if beforeArtifact, find = lo.Find(allVersionArtifacts, func(artifact *entity.Artifact) bool {
		return int(artifact.Version) == beforeVersion
	}); !find {
		return nil, fmt.Errorf("invalid revision, previous version not found")
	}
	if afterArtifact, find = lo.Find(allVersionArtifacts, func(artifact *entity.Artifact) bool {
		return int(artifact.Version) == afterVersion
	}); !find {
		return nil, fmt.Errorf("invalid revision, current version not found")
	}
	beforeArtifactMetadata, afterArtifactMetadata = beforeArtifact.Metadata.(*entity.ProjectArtifactTypeMetadata), afterArtifact.Metadata.(*entity.ProjectArtifactTypeMetadata)
	agentRun, err := s.runtimeDao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}

	if err = s.makeSureRuntimeReady(ctx, opt.SessionID, agentRun.RuntimeMetadata, opt.Account); err != nil {
		logs.V1.CtxInfo(ctx, "make sure runtime ready error: %v", err)
		return nil, err
	}

	runtimeProvider := s.runtimeProviderRegistry.GetProvider(agentRun.RuntimeMetadata.RuntimeProvider)
	containerID := agentRun.RuntimeMetadata.ContainerID

	cmd := "git diff --patch-with-raw --abbrev=40 --no-color " + beforeArtifactMetadata.Commit + " " + afterArtifactMetadata.Commit
	if opt.FilePath != nil {
		cmd += " -- " + *opt.FilePath
	}
	stdout, err := runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, afterArtifactMetadata.Path, runtimedal.WithSyncExecution())
	if err != nil {
		return nil, fmt.Errorf("git diff error: %w", err)
	}
	log.V1.CtxInfo(ctx, "command: %s, stdout: %s", cmd, stdout)
	limits := parser.Limits{
		MaxFiles:      1000,
		MaxLines:      100000,
		MaxBytes:      10 * 1024 * 1024,
		MaxPatchBytes: 512 * 1024,
	}
	p := parser.NewDiffParser(bytes.NewReader([]byte(stdout)), limits)
	var results []*nextagent.DiffFileContent
	for p.Parse() {
		diff := p.Diff()
		changeType := mapStatusToChangeType(diff.Status)
		results = append(results, &nextagent.DiffFileContent{
			Path:       string(diff.ToPath),
			ChangeType: changeType,
			FromPath:   string(diff.FromPath),
			FromCommit: beforeArtifactMetadata.Commit,
			FromMode:   diff.OldMode,
			ToPath:     string(diff.ToPath),
			ToCommit:   afterArtifactMetadata.Commit,
			ToMode:     diff.NewMode,
			IsBinary:   diff.Binary,
			RawPatch:   string(diff.Patch),
			FromId:     diff.FromID,
			ToId:       diff.ToID,
			TooLarge:   diff.TooLarge,
		})
	}
	if err := p.Err(); err != nil {
		return nil, fmt.Errorf("parse diff error: %w", err)
	}
	return results, nil
}

type GetProjectArtifactDiffFilesMetaOption struct {
	Account     *authentity.Account
	SessionID   string
	ArtifactKey string
	Revision    string
}

func (s *Service) GetProjectArtifactDiffFilesMeta(ctx context.Context, opt GetProjectArtifactDiffFilesMetaOption) ([]*nextagent.DiffFileMeta, error) {
	reportMetricsFunc := metrics.NSM.ReportArtifactMetrics()
	var (
		err error
	)
	defer func() {
		reportMetricsFunc("GetProjectArtifactDiffFilesMeta", "all", err != nil, serverservice.ErrorToErrorReason(err))
	}()
	allVersionArtifacts, err := s.dao.ListArtifacts(ctx, serverdal.ListArtifactsOption{
		SessionID: opt.SessionID,
		Key:       lo.ToPtr(opt.ArtifactKey),
	})
	if err != nil {
		return nil, err
	}
	var (
		beforeArtifact, afterArtifact                 *entity.Artifact
		beforeArtifactMetadata, afterArtifactMetadata *entity.ProjectArtifactTypeMetadata
		find                                          bool
	)

	// 解析 revision
	beforeVersion, afterVersion, err := ParseRevision(opt.Revision)
	if err != nil {
		return nil, fmt.Errorf("invalid revision: %w", err)
	}
	logs.V1.CtxInfo(ctx, "beforeVersion: %d, afterVersion: %d", beforeVersion, afterVersion)
	// 如果 revision 中仅包含一个版本号,那么当前版本与上一个版本比较
	if beforeVersion == afterVersion {
		beforeVersion = afterVersion - 1
	}
	if beforeVersion <= 0 {
		return nil, fmt.Errorf("invalid revision, diff api requires revision has previous version: %w", err)
	}
	if beforeArtifact, find = lo.Find(allVersionArtifacts, func(artifact *entity.Artifact) bool {
		return int(artifact.Version) == beforeVersion
	}); !find {
		return nil, fmt.Errorf("invalid revision, previous version not found")
	}
	if afterArtifact, find = lo.Find(allVersionArtifacts, func(artifact *entity.Artifact) bool {
		return int(artifact.Version) == afterVersion
	}); !find {
		return nil, fmt.Errorf("invalid revision, current version not found")
	}
	beforeArtifactMetadata, afterArtifactMetadata = beforeArtifact.Metadata.(*entity.ProjectArtifactTypeMetadata), afterArtifact.Metadata.(*entity.ProjectArtifactTypeMetadata)

	agentRun, err := s.runtimeDao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}

	if err = s.makeSureRuntimeReady(ctx, opt.SessionID, agentRun.RuntimeMetadata, opt.Account); err != nil {
		logs.V1.CtxInfo(ctx, "make sure runtime ready error: %v", err)
		return nil, err
	}

	runtimeProvider := s.runtimeProviderRegistry.GetProvider(agentRun.RuntimeMetadata.RuntimeProvider)
	containerID := agentRun.RuntimeMetadata.ContainerID

	cmd := "git diff --raw --numstat -z --abbrev=40 --full-index --find-renames=30% " + beforeArtifactMetadata.Commit + " " + afterArtifactMetadata.Commit
	stdout, err := runtimeProvider.ExecuteInContainer(ctx, "", cmd, containerID, afterArtifactMetadata.Path, runtimedal.WithSyncExecution())
	if err != nil {
		return nil, fmt.Errorf("numstat error: %w", err)
	}
	logs.V1.CtxInfo(ctx, "command: %s, stdout: %s", cmd, stdout)
	statParser := parser.NewDiffNumStatParser(strings.NewReader(stdout))
	var numStats []*parser.NumStat
	for statParser.Parse() {
		numStats = append(numStats, statParser.Stat())
	}
	if err = statParser.Err(); err != nil {
		return nil, fmt.Errorf("parse numstat error: %w", err)
	}

	return lo.Map(numStats, func(stat *parser.NumStat, _ int) *nextagent.DiffFileMeta {
		return &nextagent.DiffFileMeta{
			Path:          string(stat.Path),
			ChangeType:    mapStatusToChangeType(stat.Status),
			FromPath:      string(stat.OldPath),
			FromCommit:    beforeArtifactMetadata.Commit,
			FromMode:      stat.OldMode,
			ToPath:        string(stat.Path),
			ToCommit:      afterArtifactMetadata.Commit,
			ToMode:        stat.Mode,
			IsBinary:      stat.Binary,
			LinesInserted: int64(stat.Additions),
			LinesDeleted:  int64(stat.Deletions),
		}
	}), nil
}

var (
	ErrContainerStartupTimeout = errors.New("container startup timeout")
	ErrContainerIsDeleted      = errors.New("container is deleted")
)

func (s *Service) makeSureRuntimeReady(ctx context.Context, sessionID string, runtimeMetadata agententity.RuntimeMetadata, user *authentity.Account) error {
	session, err := s.dao.GetSession(ctx, serverdal.GetSessionOption{
		ID:   sessionID,
		Sync: true,
	})
	if err != nil {
		return err
	}
	if !session.Status.IsStopped() {
		return nil
	}
	key := fmt.Sprintf("runtimelocker:%s:%s", runtimeMetadata.RuntimeProvider, sessionID)
	iLocker, err := s.runtimeLocker.Acquire(ctx, key)
	if err != nil {
		return err
	}
	defer iLocker.Release(ctx)
	provider := s.runtimeProviderRegistry.GetProvider(runtimeMetadata.RuntimeProvider)
	if provider == nil {
		return errors.New("unknown provider")
	}
	status, err := provider.GetContainerStatus(ctx, runtimeMetadata.TenantKey, runtimeMetadata.ContainerID)
	if err != nil {
		return fmt.Errorf("failed to get container status: %w", err)
	}
	switch status {
	case runtimedal.ContainerStatusCreated, runtimedal.ContainerStatusRunning:
		return nil
	case runtimedal.ContainerStatusDeleted:
		return ErrContainerIsDeleted
	case runtimedal.ContainerStatusStopped:
		if err = <-runtimestarter.Get().Start(ctx, &runtimestarter.StartEvent{
			SessionID: sessionID,
			User:      user,
			FromType:  runtimestarter.FromTypeServer,
		}); err != nil {
			return fmt.Errorf("failed to start runtime: %w", err)
		}
	}
	// 轮训检查状态，直到启动成功
	count := 0
	for {
		time.Sleep(time.Millisecond * 500)
		status, _ = provider.GetContainerStatus(ctx, runtimeMetadata.TenantKey, runtimeMetadata.ContainerID)
		if status == runtimedal.ContainerStatusRunning {
			break
		}
		count++
		// 一般容器启动时间需要 4s
		if count > 15 {
			return ErrContainerStartupTimeout
		}
	}
	return nil
}

func ParseRevision(rev string) (int, int, error) {
	rev = strings.TrimSpace(rev)
	if strings.Contains(rev, "..") {
		parts := strings.Split(rev, "..")
		if len(parts) != 2 {
			return 0, 0, errors.New("invalid revision format")
		}
		start, err1 := strconv.Atoi(parts[0])
		end, err2 := strconv.Atoi(parts[1])
		if err1 != nil || err2 != nil {
			return 0, 0, errors.New("invalid number in revision")
		}
		return start, end, nil
	} else {
		v, err := strconv.Atoi(rev)
		if err != nil {
			return 0, 0, errors.New("invalid number in revision")
		}
		return v, v, nil
	}
}

func mapStatusToChangeType(status byte) nextagent.GitChangeType {
	var changeType nextagent.GitChangeType
	switch status {
	case 'A':
		changeType = nextagent.ChangeTypeAdded
	case 'D':
		changeType = nextagent.ChangeTypeDeleted
	case 'M':
		changeType = nextagent.ChangeTypeModified
	case 'R':
		changeType = nextagent.ChangeTypeRenamed
	default:
		changeType = ""
	}
	return changeType
}
