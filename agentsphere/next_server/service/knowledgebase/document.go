package knowledgebase

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type GetDocumentFromSourceOption struct {
	SourceType entity.DocumentSourceType
	SourceKey  string
	Operator   string
	DatasetID  string
	UserToken  string
}

var (
	ErrNotFound = errors.New("not found")
)

func (s *Service) DeleteDocumentContent(ctx context.Context, datasetID, documentID string) error {
	segmentIDs, err := s.dao.GetSegmentIDsByDocumentID(ctx, documentID)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil
		}
		return err
	}
	if len(segmentIDs) == 0 {
		return nil
	}
	err = s.dao.DeleteSegmentByDocumentID(ctx, documentID)
	if err != nil {
		return errors.WithMessage(err, "failed to delete segments")
	}
	err = s.deleteContentToTOS(ctx, datasetID, documentID)
	if err != nil && !errors.Is(err, ErrNotFound) {
		return err
	}
	err = s.deleteSegmentsToES(ctx, segmentIDs)
	if err != nil && !errors.Is(err, ErrNotFound) {
		return err
	}
	err = s.deleteSegmentsToViking(ctx, segmentIDs)
	if err != nil {
		return errors.WithMessage(err, "failed to delete document from Viking")
	}
	return nil
}

func (s *Service) UpsertContent(ctx context.Context, event *entity.DocumentContentEvent, reconsumeTime int32) (err error) {
	var (
		tosKey                  string
		updateDocumentOpt       *dal.UpdateDocumentOption
		isNeedUpdated           bool
		knowledgeBaseErrorStage = metrics.KnowledgeBaseErrorStageUnknown
	)
	logs.V1.CtxInfo(ctx, "[UpsertContent] start to upsert content, event: %+v, reconsumeTime:%d", event, reconsumeTime)
	defer func() {
		_ = metrics.NSM.KnowledgeBaseRate.WithTags(&metrics.NextServerKnowledgeBaseTag{
			IsError: err != nil, IsNeedUpdated: isNeedUpdated,
			ErrorStage: knowledgeBaseErrorStage}).Add(1)
		processStatus := lo.ToPtr(entity.DocumentProcessStatusSuccess)
		if err != nil {
			if errors.Is(err, lark.ErrFrequencyLimit) {
				if reconsumeTime >= entity.MaxReconsumeTime {
					logs.CtxInfo(ctx, "[UpsertContent] reconsume time exceeds maximum allowed time limit, eventID:%s", event.ID)
					processStatus = lo.ToPtr(entity.DocumentProcessStatusPending)
				} else {
					logs.CtxInfo(ctx, "[UpsertContent] reconsume time less than maximum allowed time limit, eventID:%s, reconsumeTime:%d", event.ID, reconsumeTime)
					processStatus = lo.ToPtr(entity.DocumentProcessStatusProcessing)
				}
			} else {
				logs.CtxInfo(ctx, "[UpsertContent] failed to upsert content, eventID:%s, err:%v", event.ID, err)
				processStatus = lo.ToPtr(entity.DocumentProcessStatusFailed)
			}
		}
		failReason := lo.Ternary(err != nil, &entity.DocumentFailedReason{
			Error: entity.GetDocumentFailedReasonError(err),
			LogID: ctxvalues.LogIDDefault(ctx),
		}, nil)
		var err error
		esOption := &updateDocumentToESOption{
			DocumentID:    event.ID,
			UpdatedAt:     lo.ToPtr(time.Now()),
			ProcessStatus: lo.ToPtr(string(*processStatus)),
		}
		if isNeedUpdated {
			logs.V1.CtxInfo(ctx, "[UpsertContent] update document updateDocumentOpt: %+v", lo.FromPtr(updateDocumentOpt))
			if updateDocumentOpt != nil {
				err = s.dao.UpdateDocument(ctx, event.ID, &dal.UpdateDocumentOption{
					Title:             updateDocumentOpt.Title,
					Owner:             updateDocumentOpt.Owner,
					ProcessStatus:     processStatus,
					FailedReason:      failReason,
					LastUpdatedAt:     updateDocumentOpt.LastUpdatedAt,
					Content:           lo.ToPtr(tosKey),
					DocumentCreatedAt: updateDocumentOpt.DocumentCreatedAt,
				})
				esOption.Title = updateDocumentOpt.Title
				esOption.Owner = updateDocumentOpt.Owner
				esOption.LastUpdatedAt = updateDocumentOpt.LastUpdatedAt
				if updateDocumentOpt.DocumentCreatedAt != nil {
					esOption.UpdatedAt = updateDocumentOpt.DocumentCreatedAt
				}
			} else {
				err = s.dao.UpdateDocument(ctx, event.ID, &dal.UpdateDocumentOption{
					ProcessStatus: processStatus,
					FailedReason:  failReason,
					Content:       lo.ToPtr(tosKey),
				})
			}
		} else {
			err = s.dao.UpdateDocument(ctx, event.ID, &dal.UpdateDocumentOption{
				ProcessStatus: processStatus,
				FailedReason:  failReason,
			})
		}
		if err != nil {
			logs.V1.CtxError(ctx, "[UpsertContent] failed to update document, err: %v, event:%+v", err, event)
			return
		}
		err = s.updateDocumentToES(ctx, esOption)
		if err != nil {
			logs.V1.CtxError(ctx, "[UpsertContent] failed to update document to es, err: %v, event:%+v", err, event)
			return
		}
	}()

	updateDocumentOpt, isNeedUpdated, content, err := s.processDocumentMetaContent(ctx, event)
	if err != nil {
		knowledgeBaseErrorStage = metrics.KnowledgeBaseErrorStageHandleData
		if errors.Is(err, lark.ErrRefreshTokenRevoked) {
			// Retry is not helpful for this error.
			logs.V1.CtxWarn(ctx, "[UpsertContent] failed to refresh token revoked, err: %v, event:%+v", err, event)
			return nil
		}
		return errors.WithMessage(err, "failed to process document meta content")
	}
	if !isNeedUpdated {
		return nil
	}
	docSplitter, err := NewMarkdownSplitter()
	if err != nil {
		return errors.WithMessage(err, "failed to create document splitter")
	}
	title := event.Title
	if updateDocumentOpt != nil && updateDocumentOpt.Title != nil {
		title = lo.FromPtr(updateDocumentOpt.Title)
	}
	splitResult := docSplitter.Split(ctx, title, content)
	if len(splitResult) == 0 {
		logs.V1.CtxInfo(ctx, "[UpsertContent] got empty split result, raw content: %s, doc: %+v", content, *event)
	}
	segments, err := s.generateSegments(ctx, event, splitResult)
	if err != nil {
		knowledgeBaseErrorStage = metrics.KnowledgeBaseErrorStageHandleData
		return errors.WithMessage(err, "failed to generate segments")
	}
	if err = s.DeleteDocumentContent(ctx, event.DatasetID, event.ID); err != nil {
		logs.V1.CtxError(ctx, "[UpsertContent] failed to delete document, err: %v, event: %+v", err, event)
	}
	if len(segments) == 0 {
		return nil
	}
	err = s.dao.UpdateSegmentsByDocumentID(ctx, event.ID, segments)
	if err != nil {
		knowledgeBaseErrorStage = metrics.KnowledgeBaseErrorStageSaveToDB
		return errors.WithMessage(err, "failed to update segments")
	}
	tosKey, err = s.saveContentToTOS(ctx, event.DatasetID, event.ID, content)
	if err != nil {
		knowledgeBaseErrorStage = metrics.KnowledgeBaseErrorStageSaveToTOS
		return errors.WithMessage(err, "failed to save content to tos")
	}
	err = s.saveSegmentsToViking(ctx, segments)
	if err != nil {
		knowledgeBaseErrorStage = metrics.KnowledgeBaseErrorStageSaveToViking
		return errors.WithMessage(err, "failed to save segments to viking")
	}
	err = s.saveSegmentsToES(ctx, segments)
	if err != nil {
		knowledgeBaseErrorStage = metrics.KnowledgeBaseErrorStageSaveToES
		return errors.WithMessage(err, "failed to save segments to es")
	}
	return nil
}

func (s *Service) SendBatchUpsertContentMessage(ctx context.Context, docs []*entity.Document, forceUpdate bool) error {
	messages := make([][]byte, 0, len(docs))
	for _, doc := range docs {
		tmp := entity.KnowledgebaseEvent{
			DocumentContentEvent: &entity.DocumentContentEvent{
				ID:               doc.ID,
				DatasetID:        doc.DatasetID,
				Creator:          doc.Creator,
				SourceType:       doc.SourceType,
				SourceUid:        doc.SourceUid,
				ContentType:      doc.ContentType,
				DocToken:         doc.DocToken,
				LastUpdatedAt:    doc.LastUpdatedAt,
				Title:            doc.Title,
				OldProcessStatus: doc.ProcessStatus,
				ForceUpdate:      forceUpdate,
			},
		}
		var bs []byte
		bs, _ = json.Marshal(tmp)
		messages = append(messages, bs)
	}
	return s.knowledgebaseMQ.SendBatchMessage(ctx, messages, agententity.KnowledgebaseTag)
}

func (s *Service) OfflineSendBatchUpsertContentMessage(ctx context.Context, docs []*entity.Document, forceUpdate bool) error {
	messages := make([][]byte, 0, len(docs))
	for _, doc := range docs {
		tmp := entity.KnowledgebaseEvent{
			DocumentContentEvent: &entity.DocumentContentEvent{
				ID:               doc.ID,
				DatasetID:        doc.DatasetID,
				Creator:          doc.Creator,
				SourceType:       doc.SourceType,
				SourceUid:        doc.SourceUid,
				ContentType:      doc.ContentType,
				DocToken:         doc.DocToken,
				LastUpdatedAt:    doc.LastUpdatedAt,
				Title:            doc.Title,
				OldProcessStatus: doc.ProcessStatus,
				ForceUpdate:      forceUpdate,
			},
		}
		var bs []byte
		bs, _ = json.Marshal(tmp)
		messages = append(messages, bs)
	}
	return s.knowledgebaseOfflineMQ.SendBatchMessage(ctx, messages, agententity.KnowledgebaseOfflineTag)
}

func (s *Service) processDocumentMetaContent(ctx context.Context, event *entity.DocumentContentEvent) (opt *dal.UpdateDocumentOption, isNeedUpdated bool, content string, err error) {
	if s.nextAgentKnowledgeConfig.GetValue().SkipRefreshDocument {
		segmentCount, err := s.dao.GetDocumentSegmentCount(ctx, event.DatasetID, event.ID)
		if err != nil {
			return nil, false, "", errors.WithMessage(err, "failed to get document")
		}
		// 临时处理，放过老文档
		if segmentCount > 0 {
			logs.V1.Info("skip early created document")
			return nil, false, "", nil
		}
	}

	userToken, err := s.larkService.GetUserAccessToken(ctx, event.Creator)
	if err != nil {
		return nil, false, "", errors.WithMessage(err, "failed to get user token")
	}
	// 加载元数据
	docToken := event.DocToken
	contentType := event.ContentType
	if docToken == nil {
		meta, err := s.loader.LoadMeta(ctx, event.Creator, userToken, MetaOption{
			SourceType: event.SourceType,
			SourceUid:  event.SourceUid,
		})
		if err != nil {
			return nil, false, "", errors.WithMessage(err, "failed to load meta")
		}
		// 检查是否需要更新
		if !event.ForceUpdate && !meta.UpdateTime.After(event.LastUpdatedAt) && event.OldProcessStatus == entity.DocumentProcessStatusSuccess {
			logs.V1.CtxInfo(ctx, "skip update document, meta: %+v, event: %+v", meta, event)
			return nil, false, "", nil
		}
		docToken = &meta.DocToken
		contentType = meta.ContentType
		opt = &dal.UpdateDocumentOption{
			Title:         &meta.Title,
			Owner:         &meta.Owner,
			LastUpdatedAt: &meta.UpdateTime,
		}
		if event.ForceUpdate {
			opt.DocumentCreatedAt = &meta.CreateTime
			opt.ContentType = &meta.ContentType
		}
		lastDoc, _ := s.dao.GetLastDocumentBySourceUID(ctx, event.SourceUid)
		if lastDoc != nil && !meta.UpdateTime.After(lastDoc.LastUpdatedAt) { // 库里存在最新的文档
			logs.V1.CtxInfo(ctx, "exist last document, event: %+v, last document id: %v", event, lastDoc.ID)
			content, err = s.getContentFromTOS(ctx, GetDocumentTosKey(lastDoc.DatasetID, lastDoc.ID))
			if err == nil {
				return opt, true, content, nil
			} else {
				logs.V1.CtxError(ctx, "failed to get content from tos, err: %v, event: %+v", err, event)
			}
		}
	}
	if content == "" {
		content, err = s.loader.Load(ctx, LoadOption{
			DocToken:    lo.FromPtr(docToken),
			Operator:    event.Creator,
			UserToken:   userToken,
			ContentType: contentType,
		})
		if err != nil {
			return nil, false, "", errors.WithMessage(err, fmt.Sprintf("failed to load content, docment id:%v", event.ID))
		}
	}
	return opt, true, content, nil
}

func (s *Service) generateSegments(ctx context.Context, event *entity.DocumentContentEvent, splitResult []string) ([]*entity.Segment, error) {
	segments := make([]*entity.Segment, 0, len(splitResult))
	for _, text := range splitResult {
		id, err := s.idIntGen.NextID(ctx, "")
		if err != nil {
			return nil, errors.WithMessage(err, "failed to generate segment id")
		}
		segments = append(segments, &entity.Segment{
			ID:         id,
			DatasetID:  event.DatasetID,
			DocumentID: event.ID,
			Content:    &entity.SegmentContent{Text: text},
		})
	}
	return segments, nil
}

func (s *Service) saveContentToTOS(ctx context.Context, datasetID, documentID, content string) (string, error) {
	// 存储文档内容到 TOS
	saveContent := content
	reader := strings.NewReader(saveContent)
	key := GetDocumentTosKey(datasetID, documentID)

	logs.V1.CtxInfo(ctx, "save content to tos, key: %s", key)
	ctx, cancel := context.WithTimeout(ctx, time.Second*30) // 设置上传超时时间
	defer cancel()

	return key, s.tosCli.PutObject(ctx, key, int64(len([]byte(saveContent))), reader)
}

func GetDocumentTosKey(datasetID, documentID string) string {
	return fmt.Sprintf("knowledgebase/%s/%s", datasetID, documentID)
}

func (s *Service) deleteContentToTOS(ctx context.Context, datasetID, documentID string) error {
	key := GetDocumentTosKey(datasetID, documentID)
	err := s.tosCli.DelObject(ctx, key)
	if err != nil {
		if err.Error() == "not found" {
			return ErrNotFound
		}
		return errors.WithMessage(err, "failed to delete tos document")
	}
	return nil
}

func (s *Service) getContentFromTOS(ctx context.Context, key string) (string, error) {
	if key == "" {
		return "", errors.New("key is empty")
	}
	ctx, cancel := context.WithTimeout(ctx, time.Second*2) // 设置获取超时时间
	defer cancel()
	obj, err := s.tosCli.GetObject(ctx, key)
	if err != nil {
		return "", errors.WithMessage(err, "failed to get content from tos")
	}
	content, err := io.ReadAll(obj.R)
	if err != nil {
		return "", errors.WithMessage(err, "failed to read content from tos")
	}
	return string(content), nil
}
