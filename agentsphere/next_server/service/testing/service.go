package testing

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus"
	remotebus "code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus/remote"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"
)

type CreateServiceOption struct {
	fx.In
	Redis                   redis.Client
	RuntimeProviderRegistry *runtimedal.RuntimeProviderRegistry
	AgentService            *agentservice.Service
	RuntimeAPIConfig        config.AgentSphereRuntimeAPIConfig
	MQ                      rocketmq.Client `name:"testing_monitor_mq"`
}

func NewService(opt CreateServiceOption) *Service {
	return &Service{
		redis:                   opt.Redis,
		runtimeProviderRegistry: opt.RuntimeProviderRegistry,
		agentService:            opt.AgentService,
		runtimeAPIConfig:        opt.RuntimeAPIConfig,
		mq:                      opt.MQ,
	}
}

type Service struct {
	redis                   redis.Client
	debugURI                string
	runtimeProviderRegistry *runtimedal.RuntimeProviderRegistry
	agentService            *agentservice.Service
	runtimeAPIConfig        config.AgentSphereRuntimeAPIConfig
	mq                      rocketmq.Client
}

// SessionID is the name for local testing session id.
const localSessionID = "00000000-0000-0000-0000-000000000000"

func (s *Service) CreateRuntime(ctx context.Context, debugAgentVersion string, debug bool) (sessionID string, containerID string, err error) {
	environ := map[string]string{}
	if debug {
		environ[agententity.RuntimeEnvironDebug] = "true"
	}
	var agentConfig *entity.AgentConfigVersion
	if len(debugAgentVersion) > 0 {
		agentConfig, err = s.agentService.GetAgentConfigByVersion(ctx, debugAgentVersion)
	} else {
		agentConfig, err = s.agentService.GetAgentConfigVersionByRole(ctx, lo.ToPtr(entity.SessionRoleIndustryVeteran), debugAgentVersion)
	}
	if err != nil {
		return "", "", errors.WithMessagef(err, "get agent config version by role failed")
	}
	role, err := s.agentService.GetRoleByAgentConfig(ctx, agentConfig)
	if err != nil {
		return "", "", errors.WithMessagef(err, "get role by agent config failed")
	}
	environ["TESTING_ROLE"] = role.ToIDL().String()
	sessionID = localSessionID
	if InTCE() {
		sessionID = uuid.GetDefaultGenerator(nil).NewID()
		s.setTestingSessionID(ctx, sessionID)
	}
	environ["TESTING_SESSION"] = sessionID
	runtimeToken := os.Getenv("RUNTIME_DOAS_TOKEN")
	if len(runtimeToken) > 0 {
		environ["SEC_TOKEN_STRING"] = runtimeToken
	}
	environ["X_DEBUG_VERSION"] = debugAgentVersion
	o := runtimedal.CreateContainerOption{
		SessionID:        sessionID,
		Image:            lo.FromPtr(agentConfig.RuntimeConfig.Image),
		BashImage:        lo.FromPtr(agentConfig.RuntimeConfig.BashImage),
		Environ:          environ,
		Port:             rand.Int()%5000 + 10000,
		ResourceQuota:    &config.RuntimeResourceQuota{},
		WorkspacePath:    "/workspace/iris_" + sessionID,
		BinarySource:     lo.FromPtr(agentConfig.RuntimeConfig.BinarySource),
		APIBaseURL:       fmt.Sprintf("%s%s", s.runtimeAPIConfig.APIBaseURL, s.runtimeAPIConfig.APIPrefix),
		SessionType:      agentsphere.ArtifactSessionTypeGeneral,
		PSM:              agentConfig.RuntimeConfig.PSM,
		CodebaseMentions: []*iris.CodebaseMention{},
	}
	providerType := GetProviderType()
	provider := s.runtimeProviderRegistry.GetProvider(providerType)
	container, err := provider.CreateContainer(ctx, agentConfig.RuntimeConfig.ID,
		o)
	if err != nil {
		return "", "", errors.WithMessagef(err, "create container %s failed", agentConfig.RuntimeConfig.ID)
	}
	log.V1.CtxInfo(ctx, "start server: %s", container.ID)
	if err = s.sendRecycleContainerEvent(ctx, sessionID, container.ID); err != nil {
		return "", "", errors.WithMessagef(err, "failed to send event")
	}
	err = s.saveTestingContainerInfo(ctx, sessionID, container.ID, debugAgentVersion)
	if err != nil {
		return "", "", errors.WithMessage(err, "failed to save testing container")
	}
	return sessionID, container.ID, nil
}

func (s *Service) SaveDebugURI(ctx context.Context, sessionID string, uri string) {
	if InTCE() {
		_, err := s.redis.SetNX(ctx, buildURIKey(sessionID), uri, time.Hour*24)
		if err != nil {
			log.V1.CtxError(ctx, "failed to set debug uri, error: %v", err)
		}
		return
	}
	s.debugURI = uri
}

func buildURIKey(sessionID string) string {
	return "testing:debug_uri_" + sessionID
}
func buildSessionKey(sessionID string) string {
	return "testing:session_id_" + sessionID
}

func (s *Service) getTestingURI(sessionID string) string {
	if InTCE() {
		got, err := s.redis.Get(context.Background(), buildURIKey(sessionID))
		if err != nil && !errors.Is(err, redis.Nil) {
			log.V1.CtxError(context.Background(), "failed to get debug uri, error: %v", err)
		}
		return got
	}
	return s.debugURI
}

func (s *Service) setTestingSessionID(ctx context.Context, sessionID string) {
	if InTCE() {
		_, err := s.redis.SetNX(ctx, buildSessionKey(sessionID), "true", time.Hour*24)
		if err != nil {
			log.V1.CtxError(context.Background(), "failed to set testing session id, error: %v", err)
		}
		return
	}
}

var InTCE = env.InTCE

func (s *Service) IsTestingRequest(ctx context.Context, sessionID string) bool {
	if InTCE() {
		got, err := s.redis.Get(ctx, buildSessionKey(sessionID))
		if err != nil && !errors.Is(err, redis.Nil) {
			log.V1.CtxError(context.Background(), "failed to get testing session id, error: %v", err)
			return false
		}
		return len(got) > 0
	}
	return sessionID == localSessionID
}

func GetProviderType() agententity.RuntimeProviderType {
	providerType := agententity.RuntimeProviderTypeDocker
	if InTCE() {
		providerType = agententity.RuntimeProviderTypeStratoCube
	}
	return providerType
}

func (s *Service) GetRuntimeClient(ctx context.Context, sessionID string) (*eventbus.RuntimeServiceClient, error) {
	uri := s.getTestingURI(sessionID)
	containerInfo, err := s.GetTestingContainerInfo(ctx, sessionID)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get testing container info")
	}
	agentConfig, err := s.agentService.GetAgentConfigVersionByRole(ctx, lo.ToPtr(entity.SessionRoleIndustryVeteran), containerInfo.DebugVersion)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get agent config")
	}
	providerType := GetProviderType()
	provider := s.runtimeProviderRegistry.GetProvider(providerType)
	uri, _ = provider.ResolveURI(ctx, agentConfig.RuntimeConfig.ID, "", containerInfo.ContainerID, uri)
	connect, err := remotebus.Connect(uri)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to connect uri")
	}
	client := eventbus.NewRuntimeServiceClient(connect, "")
	return client, nil
}

func (s *Service) DeleteRuntime(ctx context.Context, sessionID string) error {
	mainContainer, err := s.GetTestingContainerInfo(ctx, sessionID)
	if err != nil {
		return errors.WithMessagef(err, "failed to get testing container")
	}
	agentConfig, err := s.agentService.GetAgentConfigVersionByRole(ctx, lo.ToPtr(entity.SessionRoleIndustryVeteran), mainContainer.DebugVersion)
	if err != nil {
		return errors.WithMessagef(err, "failed to get agent config")
	}
	providerType := GetProviderType()
	provider := s.runtimeProviderRegistry.GetProvider(providerType)
	err = provider.DeleteContainer(ctx, agentConfig.RuntimeConfig.ID, mainContainer.ContainerID)
	if err != nil {
		return errors.WithMessagef(err, "failed to delete container")
	}
	return nil
}

func (s *Service) GetAgentRunContextData(ctx context.Context, agentRun *agententity.AgentRun) (map[string][]byte, error) {
	provider := s.runtimeProviderRegistry.GetProvider(agentRun.RuntimeMetadata.RuntimeProvider)
	uri, _ := provider.ResolveURI(ctx, agentRun.RuntimeMetadata.TenantKey, agentRun.RuntimeMetadata.ContainerHost, agentRun.RuntimeMetadata.ContainerID, agentRun.RuntimeMetadata.URI)
	connect, err := remotebus.Connect(uri)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to connect uri")
	}
	client := eventbus.NewRuntimeServiceClient(connect, "")
	response, err := client.GetAgentRunContext(ctx, agententity.GetAgentRunContextRequest{AgentRunID: agentRun.ID})
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to get agent run context data")
	}
	return response.AgentRunContextData, nil
}
