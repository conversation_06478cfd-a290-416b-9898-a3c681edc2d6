package ab

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/overpass/data_abtest_vm_framed/kitex_gen/abtest"
	"code.byted.org/overpass/data_abtest_vm_framed/rpc/data_abtest_vm_framed"
	"github.com/pkg/errors"
)

type ABTestParam struct {
	Token      string `json:"token,omitempty"`
	Uid        int64  `json:"uid,omitempty"`
	Uuid       string `json:"uuid,omitempty"`
	Username   string `json:"username,omitempty"`
	Department string `json:"department,omitempty"`
	AppId      string `json:"app_id,omitempty"`
}

type ABAgentConfig struct {
	AgentVersionID string         `json:"agent_version_id,omitempty"`
	AgentConfigID  string         `json:"agent_config_id,omitempty"`
	ABParams       map[string]any `json:"ab_params,omitempty"`
}

type Feature struct {
	Config map[string]any `json:"config,omitempty"`
}

// ABVersionsResponse 实验配置
type ABVersionsResponse struct {
	// key 是 agent_${agent_id} ，例如 agent_ed890c4a-c322-4aca-b60c-d8787e4b672d
	AgentConfigs map[string]ABAgentConfig `json:"agent_config_center,omitempty"`
	Features     map[string]Feature       `json:"features,omitempty"`
	Web          map[string]any           `json:"web,omitempty"`
}

func GetABVersion(ctx context.Context, token, username, department, appID string) (*ABVersionsResponse, error) {
	params := ABTestParam{
		Token:      token,
		Uuid:       username, // use username as uuid to do abtest
		Username:   username,
		Department: department,
		AppId:      appID,
	}

	props, err := json.Marshal(params)
	if err != nil {
		return nil, errors.WithMessage(err, "[ABTest] Error Marshal ab param to JSON string")
	}

	abInfo, err := data_abtest_vm_framed.GetAbVersions2(ctx, string(props))
	if err != nil || abInfo == nil || abInfo.Info == nil {
		log.V1.CtxError(ctx, "[ABTest]  user:%s GetAbVersions fail, err:%v", username, err)
		return nil, errors.WithMessage(err, "[ABTest] Error GetAbVersions")
	}

	if abInfo.Info.RspType != abtest.VersionRspType_SUCCEED {
		return nil, nil
	}

	var abRes ABVersionsResponse
	err = json.Unmarshal([]byte(abInfo.Info.GetParameters()), &abRes)
	if err != nil {
		return nil, errors.WithMessage(err, "[ABTest] Error Marshal ab param to JSON string")
	}

	return &abRes, nil
}
