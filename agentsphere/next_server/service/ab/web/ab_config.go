package web

import (
	"context"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/ab"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/gopkg/pkg/errors"
)

type WebABConfigService struct {
	tccConf *config.AgentSphereTCCConfig
}

func NewService(tccConf *config.AgentSphereTCCConfig) *WebABConfigService {
	return &WebABConfigService{
		tccConf: tccConf,
	}
}

func (s *WebABConfigService) GetWebConfig(ctx context.Context, user *authentity.Account) (map[string]any, error) {
	abConfig := s.tccConf.NextAimeAbConfig.GetPointer()
	if abConfig == nil {
		return nil, errors.New("ab config not found")
	}
	res, err := ab.GetABVersion(ctx, abConfig.Token, user.Name, user.Department, abConfig.AppID)
	if err != nil {
		return nil, errors.Wrap(err, "get ab config failed")
	}
	if res == nil {
		return nil, nil
	}

	return res.Web, nil
}
