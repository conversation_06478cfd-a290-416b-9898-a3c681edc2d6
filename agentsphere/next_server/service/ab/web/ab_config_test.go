package web

import (
	"context"
	"testing"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/ab"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/gopkg/pkg/errors"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestWebABConfigService_GetWebConfig(t *testing.T) {
	mockey.PatchConvey("Test GetWebConfig", t, func() {
		// 创建测试用的配置
		tccConf := &config.AgentSphereTCCConfig{
			NextAimeAbConfig: tcc.MustNewConfig(
				config.NextAimeAbConfig{
					AppID: "test-app-id",
					Token: "test-token",
				},
				"test-service", "test-space", "test-key", tcc.ConfigFormatJSON,
			),
		}

		// 创建服务实例
		service := NewService(tccConf)

		// 创建测试用户
		user := &authentity.Account{
			Name:       "test-user",
			Department: "test-department",
		}

		ctx := context.Background()

		convey.Convey("当 ab.GetABVersion 成功返回数据时", func() {
			// Mock ab.GetABVersion 返回成功结果
			expectedWebData := map[string]any{
				"feature1": "enabled",
				"feature2": map[string]any{
					"config":  "value",
					"enabled": true,
				},
			}
			expectedResult := &ab.ABVersionsResponse{
				Web: expectedWebData,
			}

			mockey.Mock(ab.GetABVersion).To(func(ctx context.Context, token, username, department, appID string) (*ab.ABVersionsResponse, error) {
				convey.So(token, convey.ShouldEqual, "test-token")
				convey.So(username, convey.ShouldEqual, "test-user")
				convey.So(department, convey.ShouldEqual, "test-department")
				convey.So(appID, convey.ShouldEqual, "test-app-id")
				return expectedResult, nil
			}).Build()

			result, err := service.GetWebConfig(ctx, user)

			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(result, convey.ShouldResemble, expectedWebData)
		})

		convey.Convey("当 ab.GetABVersion 返回 nil 时", func() {
			mockey.Mock(ab.GetABVersion).To(func(ctx context.Context, token, username, department, appID string) (*ab.ABVersionsResponse, error) {
				return nil, nil
			}).Build()

			result, err := service.GetWebConfig(ctx, user)

			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldBeNil)
		})

		convey.Convey("当 ab.GetABVersion 返回错误时", func() {
			expectedError := errors.New("ab service error")

			mockey.Mock(ab.GetABVersion).To(func(ctx context.Context, token, username, department, appID string) (*ab.ABVersionsResponse, error) {
				return nil, expectedError
			}).Build()

			result, err := service.GetWebConfig(ctx, user)

			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "get ab config failed")
			convey.So(err.Error(), convey.ShouldContainSubstring, "ab service error")
		})

		convey.Convey("当 ab.GetABVersion 返回空的 Web 字段时", func() {
			expectedResult := &ab.ABVersionsResponse{
				Web: nil,
			}

			mockey.Mock(ab.GetABVersion).To(func(ctx context.Context, token, username, department, appID string) (*ab.ABVersionsResponse, error) {
				return expectedResult, nil
			}).Build()

			result, err := service.GetWebConfig(ctx, user)

			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldBeNil)
		})

		convey.Convey("当 ab.GetABVersion 返回空的 Web map 时", func() {
			expectedResult := &ab.ABVersionsResponse{
				Web: map[string]any{},
			}

			mockey.Mock(ab.GetABVersion).To(func(ctx context.Context, token, username, department, appID string) (*ab.ABVersionsResponse, error) {
				return expectedResult, nil
			}).Build()

			result, err := service.GetWebConfig(ctx, user)

			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(len(result), convey.ShouldEqual, 0)
		})

		convey.Convey("测试参数传递的正确性", func() {
			// 使用不同的用户信息测试参数传递
			differentUser := &authentity.Account{
				Name:       "different-user",
				Department: "different-department",
			}

			mockey.Mock(ab.GetABVersion).To(func(ctx context.Context, token, username, department, appID string) (*ab.ABVersionsResponse, error) {
				// 验证传递的参数是否正确
				convey.So(token, convey.ShouldEqual, "test-token")
				convey.So(username, convey.ShouldEqual, "different-user")
				convey.So(department, convey.ShouldEqual, "different-department")
				convey.So(appID, convey.ShouldEqual, "test-app-id")
				return &ab.ABVersionsResponse{
					Web: map[string]any{"test": "value"},
				}, nil
			}).Build()

			result, err := service.GetWebConfig(ctx, differentUser)

			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldNotBeNil)
		})
	})
}
