package template

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	nextdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
)

type CreateTemplateVariableOptions struct {
	TemplateID string
	Creator    string
	Value      entity.TemplateFormValue
	SpaceID    string
	Content    string
	Option     string
}

func (s *Service) CreateOrUpdateTemplateVariable(ctx context.Context, opt CreateTemplateVariableOptions) (*entity.TemplateVariable, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.Creator)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}
	variables, err := s.dao.CreateOrUpdateTemplateVariable(ctx, &entity.TemplateVariable{
		ID:         s.idGen.NewID(),
		Key:        opt.Value.GetTemplateFormValueUniqueKey(),
		Value:      opt.Value,
		TemplateID: opt.TemplateID,
		Creator:    opt.Creator,
		SpaceID:    spaceID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create template variable")
	}
	return variables, nil
}

type CreateCronTaskTemplateVariableOptions struct {
	TemplateID  string
	Creator     string
	Value       entity.TemplateFormValue
	SpaceID     string
	Content     string
	Option      string
	CronTaskUID string
}

func (s *Service) CreateOrUpdateCronTaskTemplateVariable(ctx context.Context, opt CreateCronTaskTemplateVariableOptions) (*entity.CronTaskTemplateVariable, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.Creator)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}
	variables, err := s.dao.CreateOrUpdateCronTaskTemplateVariable(ctx, &entity.CronTaskTemplateVariable{
		ID:          s.idGen.NewID(),
		Key:         opt.Value.GetCronTaskTemplateFormValueUniqueKey(opt.CronTaskUID),
		Value:       opt.Value,
		TemplateID:  opt.TemplateID,
		Creator:     opt.Creator,
		SpaceID:     spaceID,
		CronTaskUID: opt.CronTaskUID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create template variable")
	}
	return variables, nil
}

func (s *Service) ListTemplateVariables(ctx context.Context, templateID, creator, spaceID string) ([]*entity.TemplateVariable, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, spaceID, creator)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}
	variables, err := s.dao.ListTemplateVariables(ctx, nextdal.ListTemplateVariablesOptions{
		TemplateID: templateID,
		Creator:    creator,
		SpaceID:    spaceID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list template variables")
	}
	return lo.Filter(variables, func(item *entity.TemplateVariable, _ int) bool {
		return item != nil
	}), nil
}

func (s *Service) GetLastCronTaskTemplateVariable(ctx context.Context, templateID, creator, spaceID, uid string) (*entity.CronTaskTemplateVariable, error) {
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, spaceID, creator)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}
	variable, err := s.dao.GetCronTaskLastTemplateVariable(ctx, nextdal.ListCronTaskTemplateVariablesOptions{
		TemplateID:  templateID,
		Creator:     creator,
		SpaceID:     spaceID,
		CronTaskUID: uid,
		Sync:        true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list template variables")
	}
	return variable, nil
}

func (s *Service) SaveTemplateShareFormData(ctx context.Context, formData entity.TemplateFormValue) (string, error) {
	id := s.idGen.NewID()
	err := s.redis.SetJSON(ctx, getTemplateFormDataKey(id), formData, time.Hour*24)
	if err != nil {
		return "", errors.WithMessage(err, "failed to save template share form data")
	}
	return id, nil
}

func (s *Service) GetTemplateShareFormData(ctx context.Context, id string) (*entity.TemplateFormValue, error) {
	var formData entity.TemplateFormValue
	err := s.redis.GetJSON(ctx, getTemplateFormDataKey(id), &formData)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get template share form data")
	}
	return &formData, nil
}

func getTemplateFormDataKey(id string) string {
	return fmt.Sprintf("template_form_data_%s", id)
}
