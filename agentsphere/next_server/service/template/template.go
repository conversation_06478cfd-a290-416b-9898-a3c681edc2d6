package template

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	tossdk "code.byted.org/gopkg/tos"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	agententity "code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	"code.byted.org/devgpt/kiwis/port/db"
)

// CreateTemplateDraft 创建模板草稿
func (s *Service) CreateTemplateDraft(ctx context.Context, sessionID string, latestEventTimestamp int64, username string,
	mcps []*entity.MCPKey, spaceID string) (*entity.TemplateVersion, error) {
	// 检查是否已经存在相同的模板
	existingTemplate, err := s.dao.GetTemplateDraftVersionBySessionIDAndEventTimestamp(ctx, sessionID, latestEventTimestamp)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to get template version by key")
	}
	if existingTemplate != nil && existingTemplate.PromptContent != "" { // 支持空模板再次生成 Prompt
		return existingTemplate, nil
	}
	spaceID, err = s.spaceService.MustGetSpaceIDWithDefault(ctx, spaceID, username)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}

	var version *entity.TemplateVersion
	if existingTemplate != nil {
		version = existingTemplate
	} else {
		// 创建模板草稿
		templateDraft := &entity.TemplateVersion{
			VersionID:       s.idGen.NewID(),
			TemplateID:      s.idGen.NewID(),
			Name:            "",
			Status:          entity.TemplateStatusDraft,
			Version:         "v1",
			Scope:           "",
			Category:        "",
			Creator:         username,
			SessionID:       sessionID,
			EventTimestamp:  latestEventTimestamp,
			PromptContent:   "",
			PromptVariables: nil,
			PlanSteps:       nil,
			ExpSop:          nil,
			SupportMCPs:     mcps,
			Expired:         false,
			Edited:          false,
			SourceSpaceID:   spaceID,
		}
		// 保存到数据库
		version, err = s.dao.CreateTemplateVersion(ctx, templateDraft)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create template draft")
		}
	}

	// 生成 prompt 模板经验
	expResult, err := s.runtime.GenerateExperienceForTemplate(ctx, sessionID, "", latestEventTimestamp, nil, runtimeservice.ExpTypeUserQueryTemplate)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to generate experience")
	}
	if expResult == nil || expResult.UserQueryTemplate == nil {
		return nil, errors.New("generate experience result is nil")
	}

	variables := s.convertPlaceholdersToTemplateVariables(expResult.UserQueryTemplate.UserQueryPlaceholders)
	// 更新模板经验
	version, err = s.dao.UpdateTemplateVersion(ctx, version.VersionID, dal.UpdateTemplateVersionOptions{
		Status:          nil,
		Category:        nil,
		Scope:           nil,
		Name:            &expResult.UserQueryTemplate.Name,
		Prompt:          &expResult.UserQueryTemplate.UserQuery,
		Expired:         nil,
		Edited:          nil,
		PromptVariables: variables,
		ExpSOP:          expResult.ExpSOP,
		Plan:            expResult.ProgressPlan,
		PlanSteps:       getPlanStepsFromExpSOP(expResult.ExpSOP),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update template draft")
	}
	return version, nil
}

type GetTemplateDraftOption struct {
	TemplateID        string // 模板ID
	TemplateVersionID string // 模板版本ID
}

// GetTemplateDraft 根据模板 ID 或者模板版本 ID 获取模板草稿
func (s *Service) GetTemplateDraft(ctx context.Context, opt GetTemplateDraftOption) (*entity.TemplateVersion, error) {
	// 参数校验
	if opt.TemplateID == "" && opt.TemplateVersionID == "" {
		return nil, errors.New("template id or template version id is required")
	}

	var templateDraft *entity.TemplateVersion
	var err error

	// 根据不同的请求参数获取模板草稿
	if opt.TemplateVersionID != "" {
		// 根据版本ID获取
		templateDraft, err = s.dao.GetTemplateVersion(ctx, opt.TemplateVersionID, false)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get template draft version")
		}
		if templateDraft.Status != entity.TemplateStatusDraft {
			return nil, errors.New("template draft version is not draft")
		}
	} else {
		// 根据模板ID获取最新的草稿版本
		drafts, err := s.dao.ListTemplateVersions(ctx, dal.ListTemplateVersionsOptions{
			TemplateID: opt.TemplateID,
			Statuses:   []string{entity.TemplateStatusDraft},
			Limit:      1,
			OrderBy:    "updated_at",
			OrderDesc:  true,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list template drafts")
		}
		if len(drafts) > 0 {
			templateDraft = drafts[0]
		}
	}

	if templateDraft == nil {
		return nil, serverservice.ErrTemplateNotFound
	}
	return templateDraft, nil
}

type CreateTemplateOption struct {
	// 必填
	Username string
	// 选填（如果填 FromTemplateID 则将基于模板复制一个新模板，如果填 SessionID + DraftTemplateID + ModifyTemplate 则将更新模板版本）
	FromTemplateID  string
	SessionID       string
	DraftTemplateID string // 草稿状态的模板ID
	ModifyTemplate  *entity.ModifyTemplate
	SpaceID         string
}

// CreateTemplate 创建模板
func (s *Service) CreateTemplate(ctx context.Context, opt CreateTemplateOption) (*entity.TemplateVersion, error) {
	// 参数校验
	if opt.FromTemplateID == "" && opt.DraftTemplateID == "" {
		return nil, errors.New("template id or template draft id required")
	}
	spaceID, err := s.spaceService.MustGetSpaceIDWithDefault(ctx, opt.SpaceID, opt.Username)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get space id")
	}
	var templateVersion *entity.TemplateVersion
	if opt.DraftTemplateID != "" {
		draft, err := s.dao.GetTemplateVersionByTemplateID(ctx, opt.DraftTemplateID)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get template draft")
		}
		// 更新模板版本
		var (
			edited            bool
			status            string
			updateOption      = dal.UpdateTemplateVersionOptions{}
			userQueryTemplate = &agententity.ExpUserQueryTemplate{
				Name:                  draft.Name,
				UserQuery:             draft.PromptContent,
				UserQueryPlaceholders: draft.PromptVariables.ConvertToPlaceholder(),
			}
			resourceStatus entity.ResourceStatus
			isSpacePublic  *bool
		)
		space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID, Sync: true})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get space")
		}
		// 只有 Prompt 或模板变量有变化才认为模板版本已被编辑
		if opt.ModifyTemplate != nil {
			if opt.ModifyTemplate.Name != nil && *opt.ModifyTemplate.Name != "" { // 修改模板名称不算编辑，不影响后续经验总结流程
				updateOption.Name = opt.ModifyTemplate.Name
				userQueryTemplate.Name = *opt.ModifyTemplate.Name
			}
			if opt.ModifyTemplate.Prompt != nil {
				updateOption.Prompt = opt.ModifyTemplate.Prompt
				userQueryTemplate.UserQuery = *opt.ModifyTemplate.Prompt
				edited = true
			}
			if opt.ModifyTemplate.Variables != nil {
				updateOption.PromptVariables = opt.ModifyTemplate.Variables
				userQueryTemplate.UserQueryPlaceholders = entity.TemplateVariableSchemaList(opt.ModifyTemplate.Variables).ConvertToPlaceholder()
				edited = true
			}
			updateOption.Category = &opt.ModifyTemplate.Category
			updateOption.Label = &opt.ModifyTemplate.Label
			if space.Type.IsPersonal() { // 个人空间
				updateOption.Scope = lo.ToPtr(lo.Ternary(opt.ModifyTemplate.Scope == entity.TemplateScopePublic, entity.TemplateScopePublic, entity.TemplateScopePrivate))
				resourceStatus = lo.Ternary(opt.ModifyTemplate.Scope == entity.TemplateScopePublic, entity.ResourceStatusPublic, entity.ResourceStatusPrivate)
			} else { // 项目空间，需要单独处理可见范围
				updateOption.Scope = lo.ToPtr(lo.Ternary(opt.ModifyTemplate.Scope == entity.TemplateScopeProjectPublic, entity.TemplateScopeProjectPublic, entity.TemplateScopePrivate))
				resourceStatus = entity.ResourceStatusPrivate
				if opt.ModifyTemplate.Scope == entity.TemplateScopeProjectPublic { // 项目内公开走授权逻辑
					isSpacePublic = lo.ToPtr(true)
				}
			}
		}
		updateOption.Expired = lo.ToPtr(edited && draft.HasOrGeneratingExperience()) // 用户编辑了模板且模板有经验或者正在生成经验，标记模板被更新，此时部分经验不会生效
		updateOption.Edited = &edited
		status = entity.TemplateStatusGenerating
		if !draft.HasOrGeneratingExperience() { // 没有经验且没有正在生成的话再生成经验，避免重复生成
			expTypes := []runtimeservice.ExpType{runtimeservice.ExpProgressPlan} // 无论模板版本是否被编辑，都需要生成 ProgressPlan
			if !edited {                                                         // 模板版本未被编辑，可以生成 ExpSOP 经验
				expTypes = append(expTypes, runtimeservice.ExpTypeSOP)
			}
			if draft.Name == "" || draft.PromptContent == "" { // 模板草稿的名称或 Prompt 为空，这里兜底再次生成 Prompt 和 Name
				expTypes = append(expTypes, runtimeservice.ExpTypeUserQueryTemplate)
			}
			// 异步生成经验
			expResult, err := s.runtime.GenerateExperienceForTemplate(ctx, opt.SessionID, opt.DraftTemplateID, draft.EventTimestamp, userQueryTemplate, expTypes...)
			if err != nil { // 如果调用失败，则直接将模板版本状态设置为可用，暂不生成这部分经验
				log.V1.CtxError(ctx, "[CreateTemplate] failed to generate experience for template `%s`: %v", opt.DraftTemplateID, err)
				status = entity.TemplateStatusAvailable
			} else {
				// 一小时后检查下经验是否生成完成
				err = s.monitorService.SendEventWithDelay(ctx, &entity.ServerMonitorEvent{
					EventName:          entity.MonitorEventTypeTemplateCheck,
					TemplateCheckEvent: &entity.TemplateCheckEvent{TemplateID: opt.DraftTemplateID},
				}, time.Hour)
				if err != nil {
					log.V1.CtxError(ctx, "[CreateTemplate] failed to send delay message for template check: %v", err)
				}
			}
			if lo.Contains(expTypes, runtimeservice.ExpTypeUserQueryTemplate) && expResult != nil && expResult.UserQueryTemplate != nil {
				updateOption.Prompt = &expResult.UserQueryTemplate.UserQuery
				updateOption.Name = &expResult.UserQueryTemplate.Name
				variables := s.convertPlaceholdersToTemplateVariables(expResult.UserQueryTemplate.UserQueryPlaceholders)
				updateOption.PromptVariables = variables
			}
		}

		updateOption.Status = &status
		templateVersion, err = s.dao.UpdateTemplateVersion(ctx, draft.VersionID, updateOption)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update template version")
		}
		// 创建模板资源并分配权限
		_, err = s.permissionService.CreateResource(ctx, permissionservice.CreateResourceOption{
			Owner:         opt.Username,
			Type:          entity.ResourceTypeTemplate,
			ExternalID:    templateVersion.TemplateID,
			Status:        &resourceStatus,
			GroupID:       &spaceID, // 设置为当前的空间ID
			IsSpacePublic: isSpacePublic,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create resource for template")
		}
	} else {
		// 基于模板复制一个新模板
		template, err := s.dao.GetTemplateNormalVersionByTemplateID(ctx, opt.FromTemplateID)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get template")
		}
		if template == nil {
			return nil, errors.New("template not found")
		}
		// 复制模板，部分字段需要重置
		template.VersionID = s.idGen.NewID()
		template.TemplateID = s.idGen.NewID()
		template.Creator = opt.Username
		template.Status = entity.TemplateStatusAvailable
		template.SessionID = ""
		template.EventTimestamp = 0
		template.Scope = entity.TemplateScopePrivate
		template.Version = "v1"
		template.Name += "-副本"
		template.CreatedAt = time.Now()
		template.UpdatedAt = time.Now()
		template.StarCount = 0
		template.SourceSpaceID = spaceID
		template.PromptVariables = lo.FilterMap(template.PromptVariables, func(item *entity.TemplateVariableSchema, _ int) (*entity.TemplateVariableSchema, bool) {
			if item == nil {
				return nil, false
			}
			item.SelectContent = "模版原始prompt内容，不支持非创建者查看"
			return item, true
		})
		templateVersion, err = s.dao.CreateTemplateVersion(ctx, template)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to copy template version")
		}
		// 创建模板资源并分配权限
		_, err = s.permissionService.CreateResource(ctx, permissionservice.CreateResourceOption{
			Owner:         opt.Username,
			Type:          entity.ResourceTypeTemplate,
			ExternalID:    templateVersion.TemplateID,
			Status:        lo.ToPtr(entity.ResourceStatusPrivate),
			GroupID:       &spaceID, // 设置为当前的空间ID
			IsSpacePublic: nil,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create resource for template")
		}
	}

	return templateVersion, nil
}

// GetTemplate 获取模板详情
func (s *Service) GetTemplate(ctx context.Context, templateID string) (*entity.TemplateVersion, error) {
	// 参数校验
	if templateID == "" {
		return nil, errors.New("template id is invalid")
	}

	// 从数据库获取模板
	template, err := s.dao.GetTemplateNormalVersionByTemplateID(ctx, templateID)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, serverservice.ErrTemplateNotFound
		}
		return nil, errors.WithMessage(err, "get template version failed")
	}
	if template == nil {
		return nil, serverservice.ErrTemplateNotFound
	}
	return template, nil
}

// GetTemplateWithShared 获取模板详情，如果是他人分享的模板，则拼接上分享信息
func (s *Service) GetTemplateWithShared(ctx context.Context, templateID, username string) (*entity.TemplateVersion, error) {
	template, err := s.GetTemplate(ctx, templateID)
	if err != nil {
		return nil, err
	}
	if template.Creator == username {
		return template, nil
	}
	target, err := s.dao.GetTemplateShareTargetByTemplateIDAndUsername(ctx, templateID, username)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return nil, errors.WithMessage(err, "failed to get template share target")
	}
	if target != nil {
		template.ShareID = &target.ShareID
		template.Scope = entity.TemplateScopeShared
	}
	return template, nil
}

type ListTemplatesOptions struct {
	PageNum  int64
	PageSize int64
	Category *string
	Search   *string
	Source   entity.TemplateSource
	Username string
	Label    *string
}

const (
	defaultPageSize = 20
	defaultPageNum  = 1
)

type OpsListTemplatesOption struct {
	SearchName *string
	Creator    *string
	SessionID  *string
	PageNum    int
	PageSize   int
}

// 管理接口，不考虑权限。
func (s *Service) OpsListTemplates(ctx context.Context, opt OpsListTemplatesOption) ([]*entity.TemplateVersion, error) {
	// 参数校验
	var (
		pageNum  = defaultPageNum
		pageSize = defaultPageSize
	)
	if opt.PageNum > 0 {
		pageNum = opt.PageNum
	}
	if opt.PageSize > 0 {
		pageSize = opt.PageSize
	}
	offset := (pageNum - 1) * pageSize
	limit := pageSize
	options := dal.ListTemplateVersionsOptions{
		Search:    lo.FromPtr(opt.SearchName),
		Creator:   lo.FromPtr(opt.Creator),
		SessionID: lo.FromPtr(opt.SessionID),
		Limit:     limit,
		Offset:    offset,
		OrderDesc: true,
	}
	// 从数据库获取模板
	templates, err := s.dao.ListTemplateVersions(ctx, options)
	if err != nil {
		return nil, errors.WithMessage(err, "list template versions failed")
	}
	return templates, nil
}

// ListTemplates 获取模板列表
func (s *Service) ListTemplates(ctx context.Context, opt ListTemplatesOptions) ([]*entity.TemplateVersion, int64, error) {
	// 参数校验
	var (
		pageNum  = defaultPageNum
		pageSize = defaultPageSize
	)
	if opt.PageNum > 0 {
		pageNum = int(opt.PageNum)
	}
	if opt.PageSize > 0 {
		pageSize = int(opt.PageSize)
	}
	offset := (pageNum - 1) * pageSize
	limit := pageSize

	// 构建查询条件
	var (
		options                                     = dal.ListTemplateVersionsOptions{}
		starredTemplateIDsMap, sharedTemplateIDsMap map[string]bool
	)
	// 支持按模板source过滤
	switch opt.Source {
	case entity.TemplateSourceMy: // 我创建的模板
		options.Creator = opt.Username
		starredTemplateIDs, err := s.dao.ListStarTemplateIDsByUsername(ctx, opt.Username)
		if err != nil {
			log.V1.CtxError(ctx, "[ListTemplates] failed to list starred template ids: %v", err)
		}
		starredTemplateIDsMap = make(map[string]bool)
		for _, id := range starredTemplateIDs {
			starredTemplateIDsMap[id] = true
		}
	case entity.TemplateSourceStar: // 我收藏的模板
		starredTemplateIDs, sharedTemplateIDs := s.getStarredAndSharedTemplateIDs(ctx, opt.Username)
		if len(starredTemplateIDs) == 0 {
			return nil, 0, nil
		}
		starredTemplateIDsMap = make(map[string]bool)
		for _, id := range starredTemplateIDs {
			starredTemplateIDsMap[id] = true
		}
		if len(sharedTemplateIDs) > 0 {
			sharedTemplateIDsMap = make(map[string]bool)
			for _, id := range sharedTemplateIDs {
				sharedTemplateIDsMap[id] = true
			}
		}
		templateIDsStr := strings.Join(lo.Map(starredTemplateIDs, func(id string, _ int) string {
			return "'" + id + "'"
		}), ",")
		options.Condition = fmt.Sprintf("template_id IN (%s)", templateIDsStr)
	default: // 全部模板，包括公共模板、官方模板、自己创建的模板、分享给自己的模板和自己收藏的模板
		starredTemplateIDs, sharedTemplateIDs := s.getStarredAndSharedTemplateIDs(ctx, opt.Username)
		var starredAndSharedTemplateIDs []string
		if len(starredTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, starredTemplateIDs...)
			starredTemplateIDsMap = make(map[string]bool)
			for _, id := range starredTemplateIDs {
				starredTemplateIDsMap[id] = true
			}
		}
		if len(sharedTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, sharedTemplateIDs...)
			sharedTemplateIDsMap = make(map[string]bool)
			for _, id := range sharedTemplateIDs {
				sharedTemplateIDsMap[id] = true
			}
		}
		if len(starredAndSharedTemplateIDs) > 0 {
			templateIDsStr := strings.Join(lo.Map(starredAndSharedTemplateIDs, func(id string, _ int) string {
				return "'" + id + "'"
			}), ",")
			options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR creator = '%s' OR template_id IN (%s))",
				entity.TemplateScopePublic, entity.TemplateScopeOfficial, opt.Username, templateIDsStr)
		} else {
			options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR creator = '%s')",
				entity.TemplateScopePublic, entity.TemplateScopeOfficial, opt.Username)
		}
	}
	if opt.Category != nil {
		options.Category = *opt.Category
	}
	if opt.Search != nil {
		options.Search = *opt.Search
	}
	if opt.Label != nil {
		options.Label = *opt.Label
	}
	options.Statuses = []string{entity.TemplateStatusGenerating, entity.TemplateStatusAvailable}

	// 查询模板列表部分字段
	partials, err := s.dao.ListPartialTemplateVersions(ctx, options)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "failed to list partial template version")
	}
	// 对模板列表排序和分页
	var templates []*entity.TemplateVersion
	partialTemplateIDs := partials.GetTemplateIDsByOffsetLimit(offset, limit, 0)
	if len(partialTemplateIDs) > 0 {
		// 查询 templates 详情并按照 ids 排序
		var orderBy = fmt.Sprintf("FIELD(template_id, %s)", strings.Join(lo.Map(partialTemplateIDs, func(item string, _ int) string {
			return "'" + item + "'"
		}), ","))
		templates, err = s.dao.ListTemplateVersions(ctx, dal.ListTemplateVersionsOptions{TemplateIDs: partialTemplateIDs, OrderBy: orderBy})
		if err != nil {
			return nil, 0, errors.WithMessage(err, "failed to list template versions")
		}
	}

	// 查询总数
	total, err := s.dao.CountTemplateVersions(ctx, options)
	if err != nil {
		return nil, 0, errors.WithMessage(err, "count template versions failed")
	}

	return lo.Map(templates, func(item *entity.TemplateVersion, _ int) *entity.TemplateVersion {
		if _, ok := starredTemplateIDsMap[item.TemplateID]; ok {
			item.Starred = lo.ToPtr(true)
		}
		if _, ok := sharedTemplateIDsMap[item.TemplateID]; ok && item.Scope == entity.TemplateScopePrivate {
			item.Scope = entity.TemplateScopeShared
		}
		return item
	}), total, nil
}

type CountTemplatesOptions struct {
	Source   entity.TemplateSource
	Username string
}

func (s *Service) CountTemplates(ctx context.Context, opt CountTemplatesOptions) (int64, error) {
	// 构建查询条件
	options := dal.ListTemplateVersionsOptions{}
	// 支持按模板source过滤
	switch opt.Source {
	case entity.TemplateSourceMy: // 我创建的模板
		options.Creator = opt.Username
	case entity.TemplateSourceStar: // 收藏模板
		return s.dao.CountTemplateStarsByUsername(ctx, opt.Username)
	default: // 全部模板，包括公共模板、官方模板、自己创建的模板、分享给自己的模板和自己收藏的模板
		starredTemplateIDs, sharedTemplateIDs := s.getStarredAndSharedTemplateIDs(ctx, opt.Username)
		var starredAndSharedTemplateIDs []string
		if len(starredTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, starredTemplateIDs...)
		}
		if len(sharedTemplateIDs) > 0 {
			starredAndSharedTemplateIDs = append(starredAndSharedTemplateIDs, sharedTemplateIDs...)
		}
		if len(starredAndSharedTemplateIDs) > 0 {
			templateIDsStr := strings.Join(lo.Map(starredAndSharedTemplateIDs, func(id string, _ int) string {
				return "'" + id + "'"
			}), ",")
			options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR creator = '%s' OR template_id IN (%s))",
				entity.TemplateScopePublic, entity.TemplateScopeOfficial, opt.Username, templateIDsStr)
		} else {
			options.Condition = fmt.Sprintf("(scope = '%s' OR scope = '%s' OR creator = '%s')",
				entity.TemplateScopePublic, entity.TemplateScopeOfficial, opt.Username)
		}
	}
	options.Statuses = []string{entity.TemplateStatusGenerating, entity.TemplateStatusAvailable}
	// 查询总数
	total, err := s.dao.CountTemplateVersions(ctx, options)
	if err != nil {
		return 0, errors.WithMessage(err, "count template versions failed")
	}
	return total, nil
}

func (s *Service) getStarredAndSharedTemplateIDs(ctx context.Context, username string) ([]string, []string) {
	// 获取自己收藏的模板ID列表
	starredTemplateIDs, err := s.dao.ListStarTemplateIDsByUsername(ctx, username)
	if err != nil {
		log.V1.CtxError(ctx, "[getStarredAndSharedTemplateIDs] failed to list starred template ids: %v", err)
		// 不返回错误，继续执行，忽略收藏模板
	}
	// 获取分享给用户的模板ID列表
	sharedTemplateIDs, err := s.dao.ListSharedTemplateIDsByTargetUsername(ctx, username)
	if err != nil {
		log.V1.CtxError(ctx, "[getStarredAndSharedTemplateIDs] failed to list shared template ids: %v", err)
		// 不返回错误，继续执行，忽略分享模板
	}
	return starredTemplateIDs, sharedTemplateIDs
}

type UpdateTemplateOptions struct {
	// 必填
	TemplateID string
	Username   string
	// 选填
	TemplateKey    *entity.TemplateKey
	ModifyTemplate *entity.ModifyTemplate
	// 管理员修改模版
	OpsModifyTemplate      *entity.OpsModifyTemplate
	NeedGenerateExperience bool
}

// UpdateTemplate 更新模板
func (s *Service) UpdateTemplate(ctx context.Context, opt UpdateTemplateOptions) (*entity.TemplateVersion, error) {
	// 参数校验
	if opt.TemplateKey == nil && opt.ModifyTemplate == nil && opt.OpsModifyTemplate == nil {
		return nil, errors.New("template key or modify template required")
	}

	// 获取当前模板
	template, err := s.dao.GetTemplateNormalVersionByTemplateID(ctx, opt.TemplateID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get template")
	}
	if template == nil {
		return nil, serverservice.ErrTemplateNotFound
	}
	spaceID := template.SourceSpaceID

	var (
		updateOption       = dal.UpdateTemplateVersionOptions{}
		needPackShare      bool
		needUpdateResource bool // 是否需要更新资源
		scope              string
	)
	// 需要生成经验且当前模板状态不是正在生成的话再生成经验，避免重复生成
	if opt.TemplateKey != nil && opt.NeedGenerateExperience && template.Status != entity.TemplateStatusGenerating {
		// 异步生成经验
		var status = entity.TemplateStatusGenerating
		userQueryTemplate := &agententity.ExpUserQueryTemplate{
			Name:                  template.Name,
			UserQuery:             template.PromptContent,
			UserQueryPlaceholders: template.PromptVariables.ConvertToPlaceholder(),
		}
		_, err = s.runtime.GenerateExperienceForTemplate(ctx, opt.TemplateKey.SessionID, opt.TemplateID, opt.TemplateKey.LatestEventTimestamp, userQueryTemplate, runtimeservice.ExpProgressPlan, runtimeservice.ExpTypeSOP)
		if err != nil { // 如果调用失败，则直接将模板版本状态设置为可用，暂不生成这部分经验
			log.V1.CtxError(ctx, "[UpdateTemplate] failed to generate experience for template `%s`: %v", opt.TemplateID, err)
			status = entity.TemplateStatusAvailable
		} else {
			// 一小时后检查下经验是否生成完成
			err = s.monitorService.SendEventWithDelay(ctx, &entity.ServerMonitorEvent{
				EventName:          entity.MonitorEventTypeTemplateCheck,
				TemplateCheckEvent: &entity.TemplateCheckEvent{TemplateID: opt.TemplateID},
			}, time.Hour)
			if err != nil {
				log.V1.CtxError(ctx, "[UpdateTemplate] failed to send delay message for template check: %v", err)
			}
		}
		updateOption.Status = &status
		// 更新经验时先将旧的经验删掉，并重置 expired & edited 标识
		err = s.dao.ResetTemplateVersionExperience(ctx, template.VersionID)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to reset template version experience")
		}
	}
	space, err := s.spaceService.GetSpace(ctx, spaceservice.GetSpaceOption{SpaceID: spaceID})
	if err != nil {
		return nil, err
	}
	if opt.ModifyTemplate != nil {
		var edited bool
		if opt.ModifyTemplate.Prompt != nil {
			updateOption.Prompt = opt.ModifyTemplate.Prompt
			edited = true
		}
		if opt.ModifyTemplate.Variables != nil {
			updateOption.PromptVariables = opt.ModifyTemplate.Variables
			edited = true
		}
		if opt.ModifyTemplate.Name != nil {
			updateOption.Name = opt.ModifyTemplate.Name
		}
		if opt.ModifyTemplate.Category != "" {
			updateOption.Category = &opt.ModifyTemplate.Category
		}
		// 官方模板暂不支持修改 Scope，且不允许修改模板为 Shared，这个状态是动态拼接的
		if opt.ModifyTemplate.Scope != "" && opt.ModifyTemplate.Scope != entity.TemplateScopeShared && template.Scope != entity.TemplateScopeOfficial {
			updateOption.Scope = &opt.ModifyTemplate.Scope
			if opt.ModifyTemplate.Scope != template.Scope {
				if opt.ModifyTemplate.Scope == entity.TemplateScopePublic && space.Type.IsProject() { // 项目空间内不允许设置为全局公开
					return nil, serverservice.ErrNotAllowedSetToPublic
				}
				needUpdateResource = true
				scope = opt.ModifyTemplate.Scope
			}
		}
		if opt.ModifyTemplate.Label != "" {
			updateOption.Label = &opt.ModifyTemplate.Label
		}
		updateOption.Expired = lo.ToPtr((edited && template.HasOrGeneratingExperience()) || template.Expired) // 用户编辑了模板且模板有经验或者正在生成经验，标记模板被更新，此时部分经验不会生效
		updateOption.Edited = lo.ToPtr(edited || template.Edited)
		needPackShare = true
	} else if opt.OpsModifyTemplate != nil {
		updateOption = dal.UpdateTemplateVersionOptions{
			Scope:           opt.OpsModifyTemplate.Scope,
			Name:            opt.OpsModifyTemplate.Name,
			Prompt:          opt.OpsModifyTemplate.QueryTemplate,
			Expired:         opt.OpsModifyTemplate.Expired,
			Edited:          opt.OpsModifyTemplate.Edited,
			PromptVariables: nil,
			ExpSOP:          nil,
			Plan:            opt.OpsModifyTemplate.ExpProgressPlan,
			PlanSteps:       nil,
		}
		if opt.OpsModifyTemplate.QueryTemplateVariables != nil {
			var variables []*entity.TemplateVariableSchema
			err = json.Unmarshal([]byte(*opt.OpsModifyTemplate.QueryTemplateVariables), &variables)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to unmarshal query template variables")
			}
			updateOption.PromptVariables = variables
		}
		if opt.OpsModifyTemplate.SupportMCPs != nil {
			var mcps []*entity.MCPKey
			err = json.Unmarshal([]byte(*opt.OpsModifyTemplate.SupportMCPs), &mcps)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to unmarshal support mcps")
			}
			updateOption.SupportMCPs = mcps
		}
		if opt.OpsModifyTemplate.ExpSOP != nil {
			expSOP := &agententity.ExpSOP{}
			err = json.Unmarshal([]byte(*opt.OpsModifyTemplate.ExpSOP), expSOP)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to unmarshal exp sop")
			}
			updateOption.ExpSOP = expSOP
			for _, ph := range expSOP.UserQueryPlaceholders {
				updateOption.PromptVariables = append(updateOption.PromptVariables, &entity.TemplateVariableSchema{
					ID:                uuid.NewString(),
					Name:              ph.Name,
					Default:           ph.Default,
					Description:       ph.Description,
					Placeholder:       ph.Default,
					SelectContent:     ph.Default,
					AllowedUploadFile: ph.RefAattachments,
				})
			}
			for _, step := range expSOP.PlanSteps {
				for _, phase := range step.Phases {
					updateOption.PlanSteps = append(updateOption.PlanSteps, phase.Name)
				}
			}
		}
	}

	// 保存到数据库
	newTemplate, err := s.dao.UpdateTemplateVersion(ctx, template.VersionID, updateOption)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update template version")
	}
	if needPackShare {
		target, err := s.dao.GetTemplateShareTargetByTemplateIDAndUsername(ctx, template.TemplateID, opt.Username)
		if err != nil && !db.IsRecordNotFoundError(err) {
			return nil, errors.WithMessage(err, "failed to get template share target")
		}
		if target != nil && newTemplate != nil {
			newTemplate.ShareID = &target.ShareID
			newTemplate.Scope = entity.TemplateScopeShared
		}
	}
	if needUpdateResource {
		var o = permissionservice.UpdateResourceOptions{
			ResourceID:         nil,
			ResourceExternalID: &template.TemplateID,
			ResourceType:       entity.ResourceTypeTemplate.Ptr(),
			Owner:              nil,
			Status:             nil,
			IsSpacePublic:      nil,
		}
		if space.Type.IsProject() {
			o.IsSpacePublic = lo.ToPtr(lo.Ternary(scope == entity.TemplateScopeProjectPublic, true, false))
		} else {
			o.Status = lo.ToPtr(lo.Ternary(scope == entity.TemplateScopePublic, entity.ResourceStatusPublic, entity.ResourceStatusPrivate))
		}
		// 更新资源及权限
		_, err = s.permissionService.UpdateResource(ctx, o)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update template resource")
		}
	}
	return newTemplate, nil
}

// DeleteTemplate 删除模板和对应的模板文件
func (s *Service) DeleteTemplate(ctx context.Context, templateID string) error {
	// 参数校验
	if templateID == "" {
		return errors.New("template id is invalid")
	}
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "[DeleteTemplate] recover from panic")
			}
		}()
		files, err := s.dao.ListTemplateFilesByTemplateID(ctx, templateID)
		if err != nil {
			log.V1.CtxError(ctx, "[DeleteTemplate] failed to list template files")
			return
		}
		// TODO: 批量删除 TOS 文件
		for _, file := range files {
			// delete file from tos.
			err = s.tos.DelObject(ctx, file.FileKey)
			if err != nil && tossdk.IsNotFound(err) {
				log.V1.CtxError(ctx, "[DeleteTemplate] failed to delete file from tos, file key: %s", file.FileKey)
				continue
			}
		}
	}()

	// 使用事务删除模板及其相关关联资源
	err := s.dao.DeleteTemplateWithRelation(ctx, templateID)
	if err != nil {
		return errors.WithMessage(err, "failed to delete template with relation")
	}
	err = s.permissionService.DeleteResource(ctx, permissionservice.DeleteResourceOption{
		ResourceID:         nil,
		ResourceExternalID: &templateID,
		ResourceType:       entity.ResourceTypeTemplate.Ptr(),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to delete template resource")
	}
	return nil
}

type UpdateTemplateExperienceOption struct {
	TemplateID       string
	ProgressPlan     string
	ExpSOP           *agententity.ExpSOP
	ExperienceStatus string
	ExperienceError  string
	Expired          *bool
}

// UpdateTemplateExperienceSOP 更新模板经验SOP
func (s *Service) UpdateTemplateExperienceSOP(ctx context.Context, opt UpdateTemplateExperienceOption) error {
	// 参数校验
	if opt.TemplateID == "" {
		return errors.New("template id is invalid")
	}
	if opt.ExpSOP == nil && opt.ProgressPlan == "" && opt.ExperienceStatus == "" {
		return errors.New("exp sop or progress plan or status is required")
	}

	// 获取当前模板
	template, err := s.dao.GetTemplateNormalVersionByTemplateID(ctx, opt.TemplateID)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return serverservice.ErrTemplateNotFound
		}
		return errors.WithMessage(err, "get template version failed")
	}
	if opt.ExperienceError != "" {
		log.V1.CtxError(ctx, "failed to generate experience, template id: %s, error: %s", opt.TemplateID, opt.ExperienceError)
	}

	// 更新模板
	_, err = s.dao.UpdateTemplateVersion(ctx, template.VersionID, dal.UpdateTemplateVersionOptions{
		Status:    lo.ToPtr(entity.TemplateStatusAvailable),
		ExpSOP:    opt.ExpSOP,
		Plan:      lo.Ternary(opt.ProgressPlan == "", nil, &opt.ProgressPlan),
		PlanSteps: getPlanStepsFromExpSOP(opt.ExpSOP),
		Expired:   opt.Expired,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update template version")
	}
	return nil
}

// UploadAndSaveTemplateFile 上传模板文件流到 TOS 并保存文件信息到数据库
func (s *Service) UploadAndSaveTemplateFile(ctx context.Context, templateID, filePath string, reader io.Reader, size int64) (*entity.TemplateFile, error) {
	// 检查是否已存在同名文件
	// TODO: 这里改成不依赖 path 去重，改成限制每个模板内的文件最大个数
	//existingFile, err := s.dao.GetTemplateFileByTemplateIDAndName(ctx, templateID, filePath)
	//if err != nil && !db.IsRecordNotFoundError(err) {
	//	return nil, errors.WithMessage(err, "failed to check template file")
	//}
	//if existingFile != nil {
	//	return existingFile, serverservice.ErrTemplateFileExists
	//}
	fileID := s.idGen.NewID()
	// 上传文件到 TOS
	key := generateFileKey(templateID, filePath, fileID)
	if size > 0 {
		err := s.tos.PutObject(ctx, key, size, reader)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to upload template file to tos")
		}
		log.V1.CtxInfo(ctx, "UploadTemplateExperienceFileStream success, template id: %d, file name: %s", templateID, key)
	}

	fileType := filepath.Ext(filePath)
	// 创建新文件
	templateFile := &entity.TemplateFile{
		ID:         fileID,
		TemplateID: templateID,
		Name:       filePath,
		Size:       size,
		Type:       fileType,
		FileKey:    key,
		Metadata:   nil,
	}
	// 保存到数据库
	file, err := s.dao.CreateTemplateFile(ctx, templateFile)
	if err != nil {
		if db.IsDuplicateKeyError(err) {
			return nil, serverservice.ErrTemplateFileExists
		}
		return nil, errors.WithMessage(err, "failed to create template file")
	}
	return file, nil
}

// GetAndDownloadTemplateFile 下载模板文件内容并获取文件元信息
func (s *Service) GetAndDownloadTemplateFile(ctx context.Context, fileID string) (io.ReadCloser, *entity.TemplateFile, error) {
	// 参数校验
	if fileID == "" {
		return nil, nil, errors.New("file id is empty")
	}

	// 获取文件信息
	file, err := s.dao.GetTemplateFile(ctx, fileID)
	if err != nil {
		if db.IsRecordNotFoundError(err) {
			return nil, nil, serverservice.ErrTemplateFileNotFound
		}
		return nil, nil, errors.WithMessage(err, "failed to get template file")
	}
	if file == nil {
		return nil, nil, serverservice.ErrTemplateFileNotFound
	}
	// 空文件直接返回
	if file.Size == 0 {
		return io.NopCloser(strings.NewReader("")), file, nil
	}
	obj, err := s.tos.GetObject(ctx, file.FileKey)
	if err != nil {
		return nil, nil, errors.WithMessagef(err, "failed to get template file from tos with key `%s`", file.FileKey)
	}
	return obj.R, file, nil
}
