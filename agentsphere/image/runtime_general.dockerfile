FROM hub.byted.org/base/debian.bookworm.python311

ARG TARGETARCH

ENV PATH="$PATH:/root/.local/bin" \
    HTTP_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    HTTPS_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    CHROME_BIN=/usr/bin/chromium \
    CHROMIUM_FLAGS="--no-sandbox --headless --disable-gpu --disable-software-rasterizer --disable-dev-shm-usage"

# libc:amd64 for M1 mac to test agent in local container
RUN dpkg --add-architecture amd64 && \
  apt update && \
  apt install -y\
    build-essential \
    libc6:amd64 \
    jq \
    sshpass \
    curl \
    wget \
    xz-utils \
    zip \
    unzip \
    tar \
    p7zip-full \
    ripgrep \
    bvc \
    git \
    file \
    x11vnc \
    xvfb \
    fluxbox \
    # browser-use related dependencies
    xfce4 \
    dbus-x11 \
    tigervnc-standalone-server \
    tigervnc-tools \
    chromium \
    chromium-driver \
    # video codec
    ffmpeg \
    # fonts
    fonts-freefont-ttf \
    fonts-ipafont-gothic \
    fonts-wqy-zenhei \
    fonts-thai-tlwg \
    fonts-kacst \
    fonts-symbola \
    fonts-noto-color-emoji \
    fonts-noto-cjk \
    graphviz \
    # Preinstall jvm
    openjdk-17-jdk \
    byteopenjdk-11 \
    pandoc && \
    # install nodejs \
    if [ "${TARGETARCH}" = "amd64" ]; then NODE_ARCH="linux-x64"; \
      elif [ "${TARGETARCH}" = "arm64" ]; then NODE_ARCH="linux-arm64"; \
      else echo "Unsupported arch: ${TARGETARCH}" && exit 1;  \
    fi \
    && NODE_VERSION=22.17.0 \
    && wget -qO- "https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-${NODE_ARCH}.tar.xz" | tar -xJ -C /usr/local \
    && mv /usr/local/node-v${NODE_VERSION}-${NODE_ARCH} /usr/local/node && \
    export PATH=/usr/local/node/bin:$PATH && \
    echo 'export PATH=/usr/local/node/bin:$PATH' >> /etc/profile && \
    # proxy-login-automator
    npm i -g proxy-login-automator && \
    apt clean && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /var/cache/apt/* && \
    # extra fonts for pdf generation, Source Han Sans (adobe) = Noto Sans CJK (google)
    mkdir -p /usr/share/fonts/truetype/source-han-sans && curl -LsSf https://github.com/adobe-fonts/source-han-sans/raw/release/Variable/TTF/SourceHanSansSC-VF.ttf -o /usr/share/fonts/truetype/source-han-sans/SourceHanSansSC-VF.ttf && \
    mkdir -p /usr/share/fonts/truetype/source-han-serif && curl -LsSf https://github.com/adobe-fonts/source-han-serif/raw/release/Variable/TTF/SourceHanSerifSC-VF.ttf -o /usr/share/fonts/truetype/source-han-serif/SourceHanSerifSC-VF.ttf && \
    mkdir -p /usr/share/fonts/truetype && curl -LsSf https://github.com/StellarCN/scp_zh/raw/refs/heads/master/fonts/SimHei.ttf -o /usr/share/fonts/truetype/SimHei.ttf && \
    # Noto CJK SC for matplotlib
    mkdir -p /usr/share/fonts/opentype/noto-cjk-sc && curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Serif/OTF/SimplifiedChinese/NotoSerifCJKsc-Regular.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSerifCJKsc-Regular.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Serif/OTF/SimplifiedChinese/NotoSerifCJKsc-Bold.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSerifCJKsc-Bold.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Regular.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansCJKsc-Regular.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Bold.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansCJKsc-Bold.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/Mono/NotoSansMonoCJKsc-Regular.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansMonoCJKsc-Regular.otf && \
    curl -LsSf https://github.com/notofonts/noto-cjk/raw/refs/heads/main/Sans/Mono/NotoSansMonoCJKsc-Bold.otf -o /usr/share/fonts/opentype/noto-cjk-sc/NotoSansMonoCJKsc-Bold.otf

ENV JAVA_HOME="/usr/lib/jvm/java-17-openjdk-${TARGETARCH}"
ENV PATH="$PATH:/usr/local/node/bin"

# Preinstall go since it's highly likely to fail to let llms install and configure goproxy themselves
# Preinstall go with cross-platform support (amd64 and arm64)
# tango 1.23
RUN if [ "${TARGETARCH}" = "amd64" ]; then \
      GO_ARCH="amd64"; \
    elif [ "${TARGETARCH}" = "arm64" ]; then \
      GO_ARCH="arm64"; \
    else \
      echo "Unsupported architecture: ${TARGETARCH}" && exit 1; \
    fi \
    && curl -LsSf "https://language-version.byted.org/api/bytedance-go/v1?type=raw&os=linux&arch=${GO_ARCH}&branch=tango1.23&version=latest" -o go.tar.gz \
    && tar -C /usr/local -xzf go.tar.gz \
    && rm go.tar.gz \
    && echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile \
    && echo 'export GOPATH=$HOME/go' >> /etc/profile

# go分布式缓存: https://code.byted.org/actions/go-cacher/blob/master/main.sh
RUN HTTP_PROXY="" bvc clone go/tools/cacher /usr/local/go/go-cacher --version ********* && \
    chmod +x /usr/local/go/go-cacher/go-cacher

RUN curl -LsSf https://astral.sh/uv/install.sh | sh && \
  uv venv --python 3.11 && \
  uv pip install --upgrade pip && \
  # data analysis toolkits; install to default python as llms are tend to use default interpreter
  pip install \
    pandas \
    numpy \
    matplotlib==3.10.5 \
    pillow \
    folium \
    requests \
    beautifulsoup4 \
    geopy \
    jinja2 \
    seaborn \
    plotly \
    graphviz \
    pydot \
    statsmodels \
    openpyxl \
    uvicorn==0.22.0 \
    python-pptx \
    docx2txt \
    PyMuPDF \
    # https://github.com/encode/httpx/issues/3221
    httpx==0.23.3 \
    fastapi==0.67.0 && \
    # 修改 matplotlib 类库中所有 .mplstyle 文件，删除所有 font.family 和 font.sans-serif 相关的行
    find /usr/local/lib/python3.11/site-packages/matplotlib/mpl-data/stylelib -type f -name "*.mplstyle" -exec sh -c 'for file; do cp "$file" "$file.bak"; grep -Ev "^\s*(font\.family|font\.sans-serif)\s*:" "$file" > "${file}.tmp" && mv -f "${file}.tmp" "$file"; done' _ {} +

# 修复 matplotlib 字体问题，强制设置 rcParams 中的 font 配置防止被覆盖
COPY agentsphere/lib/matplotlib/__init__.py /usr/local/lib/python3.11/site-packages/matplotlib/__init__.py

COPY agentsphere/agents/actions/external/browser_use /opt/browser-use

RUN cd /opt/browser-use && \
  uv sync && \
  uv run playwright install --with-deps --no-shell chromium

# third party mcp servers
RUN mkdir -p /opt/mcp_servers && \
  cd /opt/mcp_servers && \
# install servers
# arxiv \
  HTTP_PROXY="" bvc clone devinfra/arxivmcpserver/server arxiv-mcp-server && \
  cd arxiv-mcp-server && \
  uv venv --python 3.11 && \
  uv pip install . && \
  uv run arxiv-mcp-server --storage-path . && \
  cd .. && \
# unsplash
  git clone https://github.com/hellokaton/unsplash-mcp-server.git && \
  cd unsplash-mcp-server && \
  uv venv --python 3.11 && \
  uv pip install . && \
  cd .. && \
# ppt mcp
  git clone https://gitlab+deploy-token-9675:<EMAIL>/bits/pptx_mcp_server.git && \
  cd pptx_mcp_server && \
  uv venv --python 3.11 && \
  uv pip install . && \
  cd .. && \
# figma\
  git clone https://github.com/ArchimedesCrypto/figma-mcp-chunked.git && \
  cd figma-mcp-chunked && \
  npm install && \
  npm run build && \
  cd .. && \
# google image search \
  # must -g (install global, then client can run anywhere)
  npm i -g @babel/parser \
    @apify/actors-mcp-server \
    @amap/amap-maps-mcp-server \
    @modelcontextprotocol/server-google-maps \
    @mermaid-js/mermaid-cli \
    @byted/build-mcp --registry=https://bnpm.byted.org \
    @pagepass/mcp --registry=https://bnpm.byted.org \
    @b-mcp/figma --registry https://bnpm.byted.org/

# 更改uv权限，让tiger也访问
RUN chmod 777 /root && \
    chmod 777 /root/.local && \
    chmod -R 777 /root/.local/bin && \
    chmod 777 /root/.cache && \
    chmod -R 777 /root/.cache/uv

# build from repo root
COPY agentsphere/image/install.sh /opt/install.sh

RUN chmod +x /opt/install.sh && /opt/install.sh

COPY agentsphere/image/gitconfig /etc/gitconfig

# set default font for matplotlib
RUN echo 'export MPLCONFIGDIR=/root/.matplotlib' >> /root/.bashrc \
    && mkdir -p /root/.matplotlib \
    && echo "font.family: sans-serif" > /root/.matplotlib/matplotlibrc \
    && echo "font.sans-serif: Noto Sans CJK SC" >> /root/.matplotlib/matplotlibrc

# 安装code-server 用了 https_proxy 后可以连回 cn 的 tos 下载
RUN apt install -y sshpass jq gettext-base supervisor && \
    curl -OL https://tosv.byted.org/obj/dolphin-inner/cusk/aime/code-server/code-server-4.100.3-linux-amd64.tar.gz && \
    tar xvf code-server-4.100.3-linux-amd64.tar.gz && \
    mv code-server-4.100.3-linux-amd64 /usr/lib/code-server && \
    rm -rf code-server-4.100.3-linux-amd64.tar.gz && \
    HTTP_PROXY="" bvc clone stratos/cube/code_server /home/<USER>/.config/ && \
    echo '#!/usr/bin/env sh' > /usr/bin/code-server && \
    echo 'exec /usr/lib/code-server/bin/code-server "$@"' > /usr/bin/code-server && \
    chmod a+x /usr/bin/code-server && \
    echo '{}' > /home/<USER>/.config/code-server/vscode/coder.json && \
    rm -rf /home/<USER>/.config/code-server/vscode/logs/* && \
    rm -rf /home/<USER>/.config/code-server/*.log

# 预装nvm，pnpm，rush
ENV NVM_DIR=/root/.nvm
RUN curl -o- 'https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh' | bash && \
    \. ${NVM_DIR}/nvm.sh && \
    npm config set registry https://bnpm.byted.org && \
    touch ${NVM_DIR}/default-packages && \
    echo "pnpm" >> $NVM_DIR/default-packages && \
    echo "yarn" >> $NVM_DIR/default-packages && \
    echo "@microsoft/rush" >> $NVM_DIR/default-packages && \
    echo "@ies/eden-monorepo" >> $NVM_DIR/default-packages && \
    curl -fsSL https://get.pnpm.io/install.sh | bash - && \
    npm i @microsoft/rush -g && \
    npm i @ies/eden-monorepo -g && \
    npm i vite@6.0.1 -g

ENV HTTPS_PROXY= \
    HTTP_PROXY= \
    PATH="$PATH:/usr/local/go/bin" \
    GOPATH="/root/go" \
    GOBIN="/usr/local/go/bin" \
    GOPROXY="https://goproxy.byted.org|direct" \
    GOPRIVATE="sysrepo.byted.org,*.everphoto.cn,git.smartisan.com" \
    GONOSUMDB="code.byted.org,gitlab.everphoto.cn,git.byted.org,sysrepo.byted.org,git.smartisan.com" \
    GOSUMDB="sum.golang.google.cn" \
    MPLCONFIGDIR="/root/.matplotlib"

# install cosy
COPY agentsphere/image/install_cosy.sh /opt/install_cosy.sh
RUN chmod +x /opt/install_cosy.sh && /opt/install_cosy.sh
COPY agentsphere/image/config.cosy_rpc.local.toml /usr/local/cosy/config.cosy_rpc.local.toml
ENV PATH="$PATH:/usr/local/cosy"

# install aime_project_templates
RUN cd /tmp && git clone https://gitlab+deploy-token-9864:<EMAIL>/smart-infra/react-app-tool-test.git && \
    cd /tmp/react-app-tool-test && \
    ls -la && \
    mkdir -p /opt/bin /opt/deploy/templates && \
    cp -r /tmp/react-app-tool-test/bin/* /opt/bin/ && \
    cp -r /tmp/react-app-tool-test/templates/* /opt/deploy/templates/ && \
    rm -rf /tmp/react-app-tool-test
ENV PATH="$PATH:/opt/bin"

# lldb for CoredumpAi: https://bytedance.larkoffice.com/docx/WIBTdy9o0ogYyLxzgAPcaXTPnSg
ENV LLDB_PYTHON_PATH="/usr/lib/llvm-16/lib/python3.11/site-packages:/usr/lib/llvm-16/lib/python3/dist-packages/lldb"
ENV PYTHONPATH="$PYTHONPATH:$LLDB_PYTHON_PATH"
RUN apt update && apt install -y lldb-16 && \
    ln -s /usr/bin/lldb-16 /usr/bin/lldb && \
    echo 'export PYTHONPATH=$PYTHONPATH:$LLDB_PYTHON_PATH' >> /etc/profile

ARG REGION
RUN if [ "$REGION" != "" ] && [ "$REGION" != "china-north-lf" ]; then apt-get update && DEBIAN_FRONTEND=noninteractive apt-get  install tzdata && rm -rf /etc/localtime && /bin/cp -f /usr/share/zoneinfo/Etc/UTC /etc/localtime && echo "Etc/UTC" > /etc/timezone; fi;

# 设置BASH_ENV使非交互式bash默认加载/root/.bash_profile
ENV BASH_ENV="/root/.bash_profile"
COPY agentsphere/image/bash_profile $BASH_ENV
# Install dlv to debug runtime.
RUN go install github.com/go-delve/delve/cmd/dlv@latest

CMD ["/usr/local/bin/runtime/agentsphere_runtime"]
