# syntax = hub.byted.org/kubestrato/dockerfile-x:1.4.2

FROM ./agentsphere/image/bash_image/bash_base.dockerfile

ARG TARGETARCH
ARG REGION

ENV HTTP_PROXY=http://sys-proxy-rd-relay.byted.org:3128 \
    HTTPS_PROXY=http://sys-proxy-rd-relay.byted.org:3128

# install nodejs，22.17 ~1.8GiB
RUN if [ "${TARGETARCH}" = "amd64" ]; then NODE_ARCH="linux-x64"; \
      elif [ "${TARGETARCH}" = "arm64" ]; then NODE_ARCH="linux-arm64"; \
      else echo "Unsupported arch: ${TARGETARCH}" && exit 1;  \
    fi && \
    for NODE_VERSION in 20.9.0 21.7.3 22.17.0 23.9.0 24.6.0; \
    do \
        wget -qO- "https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-${NODE_ARCH}.tar.xz" | tar -xJ -C /usr/local; \
    done && \
    # 默认22.17.0
    ln -nsf /usr/local/node-v22.17.0-${NODE_ARCH} /usr/local/node && \
    export PATH=/usr/local/node/bin:$PATH && \
    echo 'export PATH=/usr/local/node/bin:$PATH' >> /etc/profile

ENV PATH="/usr/local/node/bin:$PATH"

# install nvm，pnpm，rush
# /root/.npm ~815MiB
ENV NVM_DIR=/root/.nvm
RUN curl -o- 'https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh' | bash && \
    \. ${NVM_DIR}/nvm.sh && \
    npm config set registry https://bnpm.byted.org && \
    touch ${NVM_DIR}/default-packages && \
    echo "pnpm" >> $NVM_DIR/default-packages && \
    echo "yarn" >> $NVM_DIR/default-packages && \
    echo "@microsoft/rush" >> $NVM_DIR/default-packages && \
    echo "@ies/eden-monorepo" >> $NVM_DIR/default-packages && \
    curl -fsSL https://get.pnpm.io/install.sh | bash - && \
    export HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" && \
    npm i @microsoft/rush -g && \
    https_proxy="" npm i @ies/eden-monorepo -g && \
    https_proxy="" npm i vite@6.0.1 -g

# install tango 1.20-1.24，tango 1.23 ~320MiB
RUN for GO_VERSION in 1_20 1_21 1_22 1_23 1_24; \
    do \
        HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" bvc clone bytedance/lang/tango${GO_VERSION}_bookworm /usr/local/go${GO_VERSION}; \
    done && \
    # 默认使用tango1.23
    ln -nsf /usr/local/go1_23 /usr/local/go && \
    echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile && \
    echo 'export GOPATH=$HOME/go' >> /etc/profile

ENV PATH="$PATH:/usr/local/go/bin" \
    GOPATH="/root/go" \
    GOBIN="/usr/local/go/bin" \
    GOPROXY="https://goproxy.byted.org|direct" \
    GOPRIVATE="sysrepo.byted.org,*.everphoto.cn,git.smartisan.com" \
    GONOSUMDB="code.byted.org,gitlab.everphoto.cn,git.byted.org,sysrepo.byted.org,git.smartisan.com" \
    GOSUMDB="sum.golang.google.cn"

# go分布式缓存: https://code.byted.org/actions/go-cacher/blob/master/main.sh
RUN HTTP_PROXY="" HTTPS_PROXY="" http_proxy="" https_proxy="" bvc clone go/tools/cacher /usr/local/go/go-cacher --version 1.0.0.112 && \
    chmod +x /usr/local/go/go-cacher/go-cacher

# Install dlv to debug runtime.
RUN go install github.com/go-delve/delve/cmd/dlv@latest

# install jdk, 17 like devin, jdk11 for douyin
# apt show openjdk-17-jdk byteopenjdk-11 ~270MiB
# 配置maven源：https://bytedance.larkoffice.com/wiki/wikcnNBLIjxmf5LDqMxX5jbHJ9b
# 需要填写用户scm的账号和密码，无法获取
RUN apt install -y \
    byteopenjdk-17 \
    byteopenjdk-11 \
    byteopenjdk-8 && \
    ln -nsf /usr/lib/jvm/java-17-byteopenjdk-${TARGETARCH} /usr/lib/jvm/openjdk
ENV JAVA_HOME="/usr/lib/jvm/openjdk"

ENV HTTPS_PROXY= \
    HTTP_PROXY=
