package main

import (
	"os"

	nextCronTasksService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/cron_tasks"
	nextKnowledgebaseService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"go.uber.org/fx"
)

var CronJobModule = fx.Invoke(func(knowledgebaseService *nextKnowledgebaseService.Service,
	cronTasksService *nextCronTasksService.Service) error {
	if env.Cluster() != "cronjob" {
		return nil
	}
	if os.Getenv("KNOWLEDGEBASSE_ENABLE_PATROL") == "1" {
		logs.Info("knowledgebase patrol is started")
		return knowledgebaseService.StartCron()
	}
	if os.Getenv("CRONTASK_ENABLE") == "1" {
		logs.Info("cron task is started")
		return cronTasksService.StartCron()
	}
	return nil
})
