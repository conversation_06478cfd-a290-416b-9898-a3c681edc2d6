package main

import (
	"context"
	"strings"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextserverhandler "code.byted.org/devgpt/kiwis/agentsphere/next_server/handler"
	runtimenexthandler "code.byted.org/devgpt/kiwis/agentsphere/runtime/nexthandler"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/devgpt/kiwis/port/rocketmq/middleware"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"
	"go.uber.org/fx"
)

func RunConsumers(lc fx.Lifecycle, handler *runtimenexthandler.Handler, serverHandler *nextserverhandler.Handler) {
	if env.Cluster() != "mq_consumer" {
		return
	}
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			log.V1.CtxInfo(ctx, "env: %v %s", env.InTCE(), env.Env())
			if !env.InTCE() && !strings.HasPrefix(env.Env(), "boe_") {
				// create on https://cloud.bytedance.net/env/envs/all/list
				// and set as SERVICE_ENV environment variable
				log.V1.CtxError(ctx, "local apiserver is not allowed to run without SERVICE_ENV=boe_*")
				return errors.New("local apiserver is not allowed to run without SERVICE_ENV=boe_*")
			}
			return nil
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := handler.StartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return handler.StopConsumer()
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := serverHandler.StartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return serverHandler.StopConsumer()
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := serverHandler.KnowledgebaseStartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return serverHandler.KnowledgebaseStopConsumer()
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := serverHandler.KnowledgebaseOfflineStartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return serverHandler.KnowledgebaseOfflineStopConsumer()
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := serverHandler.TraceStartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return serverHandler.TraceStopConsumer()
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := serverHandler.DeployReviewStartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return serverHandler.DeployReviewStopConsumer()
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := serverHandler.TestingMonitorStartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return serverHandler.TestingMonitorStopConsumer()
		},
	})
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				if err := serverHandler.CronTaskStartConsumer(); err != nil {
					panic(err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return serverHandler.CronTaskStopConsumer()
		},
	})
}

var MQModule = fx.Options(
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.RuntimeOrchestrator
		conf.ConsumeTags = entity.RuntimeOrchestrationTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("runtime orchestrator mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"runtime_orchestrator_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.AssignmentMonitor
		conf.ConsumeTags = entity.AssignmentMonitoringTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("assignment monitor mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"assignment_monitor_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.NextRuntimeOrchestrator
		conf.ConsumeTags = entity.NextRuntimeOrchestrationTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("next runtime orchestrator mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"next_runtime_orchestrator_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.NextSessionMonitor
		conf.ConsumeTags = entity.NextSessionMonitorTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("next session monitor mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"next_session_monitor_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.Knowledgebase
		conf.ConsumeTags = entity.KnowledgebaseTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("knowledgebase mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"knowledgebase_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.KnowledgebaseOffline
		conf.ConsumeTags = entity.KnowledgebaseOfflineTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("knowledgebase offline mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"knowledgebase_offline_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.NextTrace
		conf.ConsumeTags = entity.NextTraceTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("next trace mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"next_trace_mq"`),
	)),

	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.DeployReview
		conf.ConsumeTags = entity.DeployReviewTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("deploy review mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"deploy_review_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.TestingMonitor
		conf.ConsumeTags = entity.TestingMonitorTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("testing mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"testing_monitor_mq"`),
	)),
	fx.Provide(fx.Annotate(func(config *config.AgentSphereConfig) (rocketmq.Client, error) {
		conf := config.MQConfig.TaskCronJob
		conf.ConsumeTags = entity.TaskCronJobTag
		conf.FeatureEnv = env.Env()
		log.V1.Info("next runtime task cronjob mq config: %+v", conf)
		cli, err := rocketmq.NewClient(&conf, rocketmq.WithMiddleWare(middleware.PanicRecover))
		if err != nil {
			return nil, err
		}
		return cli, err
	},
		fx.ResultTags(`name:"task_cronjob"`),
	)),
	fx.Invoke(RunConsumers),
)
