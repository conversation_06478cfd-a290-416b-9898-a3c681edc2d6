package main

import (
	"context"
	"time"

	"code.byted.org/devgpt/kiwis/lib/cloud_oauth"

	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/testing"
	"code.byted.org/devgpt/kiwis/port/bits"
	"code.byted.org/devgpt/kiwis/port/tqs"
	"code.byted.org/gopkg/env"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/fx"

	bpmservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/bpm"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/code_repo"
	deployreviewservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/deploy_review"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/dev_service"
	nextKnowledgebaseService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgebase"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mention"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/notification_message"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/platform_config"
	templateservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/template"
	runtimeserver "code.byted.org/devgpt/kiwis/agentsphere/runtime_server/client"
	"code.byted.org/devgpt/kiwis/lib/cloud_mcp"
	idgenlib "code.byted.org/devgpt/kiwis/lib/idgenerator"
	"code.byted.org/devgpt/kiwis/lib/snowflakeid"
	"code.byted.org/devgpt/kiwis/port/bytedoc"
	"code.byted.org/devgpt/kiwis/port/es"
	"code.byted.org/devgpt/kiwis/port/nextcode"
	"code.byted.org/devgpt/kiwis/port/viking"

	"code.byted.org/videoarch/imagex-sdk-golang/v2/base"
	"code.byted.org/videoarch/imagex-sdk-golang/v2/service/imagex"

	"code.byted.org/devgpt/kiwis/agentsphere/common/credential"
	"code.byted.org/devgpt/kiwis/agentsphere/common/redissemaphore"
	"code.byted.org/devgpt/kiwis/agentsphere/journal"
	serverdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	serverDalImpl "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal/impl"
	webabconfigservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/ab/web"
	activityservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/activity"
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	artifactService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	crontaskservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/cron_tasks"
	debugservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/debug"
	deploymentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/deployment"
	icmservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/icm"
	knowledgeservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/knowledgeset"
	nextLarkService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lock"
	mcpservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/mcp"
	monitorservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/monitor"
	permissionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/permission"
	promptservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/prompt"
	replayservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/replay"
	resourcemanageservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/resourcemanage"
	scmservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/scm"
	sessionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session"
	sessioncollectionservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/session_collection"
	shareservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/share"
	spaceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/space"
	traceservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/trace"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	runtimeservice "code.byted.org/devgpt/kiwis/agentsphere/runtime/service"
	agspserverdal "code.byted.org/devgpt/kiwis/agentsphere/server/dal"
	"code.byted.org/devgpt/kiwis/copilotstack/common/auth"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/di"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/codebase"
	"code.byted.org/devgpt/kiwis/port/db"
	"code.byted.org/devgpt/kiwis/port/faas"
	"code.byted.org/devgpt/kiwis/port/hulkcloud"
	"code.byted.org/devgpt/kiwis/port/lark"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/stratocube"
	"code.byted.org/devgpt/kiwis/port/tcc"
	"code.byted.org/devgpt/kiwis/port/tos"
)

var ConfigModule = fx.Options(
	// Load config from file.
	fx.Provide(config.LoadConfigFunc[config.AgentSphereConfig]("conf/config.agentsphere.nextserver")),
	// Provide sub config items to di graph.
	fx.Provide(di.StructProvider(new(config.AgentSphereConfig), false)),
	// Create tcc client.
	fx.Provide(tcc.NewClient),
	// Inject tcc config to struct.
	fx.Provide(func(cli tcc.Client) (*config.AgentSphereTCCConfig, error) {
		tccCli := libtcc.GetOverWriteTCCClient(cli)
		c := new(config.AgentSphereTCCConfig)
		if err := libtcc.InjectTCCConfig(c, tccCli); err != nil {
			return nil, errors.WithMessage(err, "failed to inject tcc config")
		}
		return c, nil
	}),
	// Provide sub TCC config items to di graph.
	fx.Provide(di.StructProvider(new(config.AgentSphereTCCConfig), false)),
	fx.Invoke(metrics.InitNextServerMetric),
	fx.Invoke(metrics.InitMetric),
)

func main() {
	app := fx.New(
		HertzModule,
		ConfigModule,
		auth.Module,
		MQModule,
		redis.Module,
		CronJobModule,
		fx.Provide(db.NewRDSClient),
		fx.Provide(redissemaphore.NewRedisSemaphore),
		fx.Provide(cloud_oauth.TryNewCloudOAuthClient),
		fx.Provide(credential.NewCache),
		fx.Provide(func(tosConfig *libtcc.GenericConfig[config.TOSConfig]) (tos.Client, error) {
			conf := tosConfig.GetValue()
			return tos.NewClient(conf.Bucket, conf.AccessKey, conf.SecretKey)
		}),
		fx.Provide(func(stratocubeConfig *libtcc.GenericConfig[config.StratoCubeConfig]) stratocube.Client {
			conf := stratocubeConfig.GetValue()
			return stratocube.NewClient(stratocube.ClientOption{
				BaseURL: conf.BaseURL,
				Tenant: lo.Map(stratocubeConfig.GetValue().TenantConfig, func(item config.StratoCubeTenantConfig, index int) stratocube.Tenant {
					return stratocube.Tenant{
						Key:    item.TenantKey,
						ID:     item.TenantID,
						Name:   item.TenantName,
						Secret: item.Secret,
					}
				}),
			})
		}),
		// tce serverflow.agentshpere.runtime
		fx.Provide(runtimeserver.NewRuntimeServerClientFromConf),
		fx.Provide(cloud_mcp.NewCloudMCPClientFromConf),
		// AgentSphere runtime container providers.
		fx.Provide(runtimeservice.NewRuntimeProviderRegistry),
		// AgentSphere server DAO.
		fx.Provide(di.StructConstructor(new(serverdal.DAO))),
		fx.Provide(di.StructConstructor(new(agspserverdal.DAO))),
		// AgentSphere runtime server DAO.
		fx.Provide(di.StructConstructor(new(runtimedal.DAO))),
		serverDalImpl.Module,
		// AgentSphere runtime server service.
		fx.Provide(runtimeservice.NewService),
		// AgentSphere next lark service
		fx.Provide(nextLarkService.NewService),
		// AgentSphere FaaS client
		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) (faas.Client, error) {
			if tccConf == nil || tccConf.NextAgentDeploymentConfig == nil || tccConf.NextAgentDeploymentConfig.GetPointer() == nil {
				return nil, errors.New("next agent deployment config is nil")
			}
			config := tccConf.NextAgentDeploymentConfig.GetValue()
			if config.FaaSToken == "" {
				return nil, errors.New("faas token is empty")
			}
			return faas.NewClient("", config.FaaSToken, config.FaaSBaseURL), nil
		}),
		// AgentSphere next artifact service
		fx.Provide(artifactService.NewService),
		// AgentSphere next deployment service
		fx.Provide(deploymentservice.NewService),
		// AgentSphere next replay service
		fx.Provide(replayservice.NewService),
		// AgentSphere next user service
		fx.Provide(userservice.NewService),
		// AgentSphere next session_collection service
		fx.Provide(sessioncollectionservice.NewService),
		// AgentSphere next session service
		fx.Provide(sessionservice.NewService),
		// AgentSphere share service
		fx.Provide(shareservice.NewService),
		// AgentSphere next session service
		fx.Provide(traceservice.NewService),
		// AgentSphere next agent manage service
		fx.Provide(agentservice.NewService),
		// AgentSphere next agent prompt manage service
		fx.Provide(promptservice.NewService),
		// AgentSphere next monitor service
		fx.Provide(monitorservice.NewService),
		// AgentSphere next agent activity service
		fx.Provide(activityservice.NewService),
		// AgentSphere next MCP service
		fx.Provide(mcpservice.NewService),
		// AgentSphere template service.
		fx.Provide(templateservice.NewService),
		// AgentSphere permission service
		fx.Provide(permissionservice.NewService),
		// AgentSphere knowledgebase service
		fx.Provide(nextKnowledgebaseService.NewService),
		// AgentSphere mention service
		fx.Provide(mention.NewService),
		fx.Provide(notification_message.NewService),
		fx.Provide(code_repo.NewService),
		fx.Provide(dev_service.NewService),
		fx.Provide(platform_config.NewService),
		// AgentSphere space service.
		fx.Provide(spaceservice.NewService),
		// AgentSphere next cron task service.
		fx.Provide(crontaskservice.NewService),
		// AgentSphere deploy review service
		deployreviewservice.Module,
		bpmservice.Module,
		knowledgeservice.Module,
		// ICM service
		icmservice.Module,
		scmservice.Module,
		// LLM service.
		llm.Module,
		// Resource manage service for llm
		fx.Provide(resourcemanageservice.NewService),
		fx.Provide(di.Bind(new(resourcemanageservice.Service), new(llm.ResourceManageService))),
		fx.Provide(debugservice.NewService),
		fx.Provide(testing.NewService),
		// journal service
		journal.Module,
		// web abtest
		fx.Provide(webabconfigservice.NewService),
		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) lark.Client {
			conf := tccConf.NeumaLarkAppConfig.GetValue()
			return lark.NewClient(conf.AppID, conf.AppSecret, conf.DevGPTServiceToken, conf.RedirectURI)
		}),
		fx.Provide(func() (bits.Client, error) {
			return bits.New()
		}),
		fx.Provide(func(conf *config.AgentSphereConfig) (es.Client, error) {
			return es.NewClient(es.NewClientOpt{
				PSM:     conf.KnowledgebaseConfig.ElasticSearchPSM,
				Cluster: conf.KnowledgebaseConfig.ElasticSearchCluster,
				Secure:  env.IsProduct(),
			})
		}),
		fx.Provide(func(conf *config.AgentSphereConfig, tccConf *config.AgentSphereTCCConfig) (*viking.Client, error) {
			value := tccConf.NextAgentKnowledgeConfig.GetValue()
			if env.GetCurrentVRegion() == env.VREGION_SINGAPORECENTRAL {
				return viking.NewClient(conf.KnowledgebaseConfig.VikingName, value.VikingToken, viking.RegionSG)
			}
			return viking.NewClient(conf.KnowledgebaseConfig.VikingName, value.VikingToken, "CN")
		}),
		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) *imagex.ImageXClient {
			instance := imagex.NewInstance()
			conf := tccConf.ImageXConfig.GetValue()
			instance.SetCredential(base.Credentials{
				AccessKeyID:     conf.AccessKey,
				SecretAccessKey: conf.SecretKey,
			})
			return instance
		}),
		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) hulkcloud.Client {
			conf := tccConf.NextAgentHulkCloudConfig.GetValue()
			return hulkcloud.NewClient(conf.BaseURL, conf.Token)
		}),
		fx.Provide(lock.NewLocker),
		fx.Provide(di.Bind(new(lock.Locker), new(lock.Lock))),
		codebase.Module,
		nextcode.NextServerModule,
		fx.Options(
			fx.Provide(func() idgenlib.IDGenerator {
				return idgenlib.NewSnowflakeIDGenerator(snowflakeid.Settings{
					MachineID:       snowflakeid.BytedMachineID,
					BitLenSequence:  5,
					BitLenMachineID: 16,
				})
			}),
		),
		bytedoc.Module,
		fx.Provide(func(tccConf *config.AgentSphereTCCConfig) (tqs.Client, error) {
			if env.IsBoe() {
				return nil, nil
			}
			tqsConfig := tccConf.TQSConfig.GetValue()
			return tqs.NewClient(context.Background(), &tqs.Option{
				AppID:      tqsConfig.AppID,
				AppKey:     tqsConfig.AppKey,
				Username:   tqsConfig.Username,
				Cluster:    tqsConfig.Cluster,
				RPCTimeout: time.Hour,
			})
		}),
	)
	app.Run()
}
