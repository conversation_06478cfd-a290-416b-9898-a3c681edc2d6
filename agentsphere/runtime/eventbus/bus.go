package eventbus

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/trace"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/pkg/errors"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	remotebus "code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus/remote"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
)

// RuntimeServiceClient send messages to runtime or receive messages
type RuntimeServiceClient struct {
	control *remotebus.RemoteBus
	relay   string
}

func NewRuntimeServiceClient(remotebus *remotebus.RemoteBus, relay string) *RuntimeServiceClient {
	return &RuntimeServiceClient{
		control: remotebus,
		relay:   relay,
	}
}

func (cli *RuntimeServiceClient) Close() {
	cli.control.Close()
}

// CreateAgentEventStream create a new event stream and returns its event bus URI.
// NOTE: the uri need to be resolved by RuntimeProvider before use
func (cli *RuntimeServiceClient) CreateAgentEventStream(runID string, server bool) (uri string, err error) {
	var result entity.GetAgentEventStreamResult
	err = cli.control.Call(entity.RPCGetAgentEventStream.String(), entity.GetAgentEventStreamRequest{RunID: runID, Server: server}, &result, entity.RuntimeClientName)
	if err != nil {
		return "", errors.WithMessage(err, "failed to call create event stream")
	}
	return result.BusURI, nil
}

func (cli *RuntimeServiceClient) CtxCreateAgentEventStream(ctx context.Context, runID string, server bool) (uri string, err error) {
	var result entity.GetAgentEventStreamResult
	err = cli.control.CtxCall(ctx, entity.RPCGetAgentEventStream.String(), entity.GetAgentEventStreamRequest{RunID: runID, Server: server}, &result, entity.RuntimeClientName)
	if err != nil {
		return "", errors.WithMessage(err, "failed to call create event stream")
	}
	return result.BusURI, nil
}

func (cli *RuntimeServiceClient) CtxRunAgent(ctx context.Context, opts entity.RunAgentRequest) (err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCRunAgent.String(), err != nil) }()

	ctx, span := trace.StartClientSpan(ctx, entity.RPCRunAgent.String())
	defer trace.FinishClientSpan(span, err)
	// to avoid the request context being cancelled and message being dropped
	ctx = context.WithoutCancel(ctx)

	conn := cli.control.Conn()
	res, err := conn.CtxCallSession(ctx, conn.Session, entity.RPCRunAgent.String(), opts, entity.RuntimeClientName)
	if err != nil {
		return err
	}
	if res == nil { // compatible with old runtime
		return nil
	}
	var result entity.RunAgentResponse
	err = json.Unmarshal(*res, &result)
	if err != nil {
		return err
	}
	if result.Code != 0 {
		return fmt.Errorf("failed to run agent: %s", result.Message)
	}
	return nil
}

func (cli *RuntimeServiceClient) RunAgentWait(opts entity.RunAgentRequest) (err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCRunAgent.String(), err != nil) }()

	conn := cli.control.Conn()
	_, err = conn.CtxCallSession(context.Background(), conn.Session, entity.RPCRunAgent.String(), opts, entity.RuntimeClientName)
	return err
}

func (cli *RuntimeServiceClient) Ping(ctx context.Context, opts entity.PingRequest) (res *entity.PingResponse, err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCPing.String(), err != nil) }()

	var result entity.PingResponse
	err = cli.control.CtxCall(ctx, entity.RPCPing.String(), opts, &result, entity.RuntimeClientName)
	if err != nil {
		return nil, err
	}
	return &result, err
}

func (cli *RuntimeServiceClient) GenerateExperience(ctx context.Context, opts entity.GenerateExperienceRequest) (res *entity.GenerateExperienceResponse, err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCAgentGenerateExperience.String(), err != nil) }()
	ctx, span := trace.StartClientSpan(ctx, entity.RPCAgentGenerateExperience.String())
	defer trace.FinishClientSpan(span, err)
	var result entity.GenerateExperienceResponse
	err = cli.control.CtxCall(ctx, entity.RPCAgentGenerateExperience.String(), opts, &result, entity.RuntimeClientName)
	return &result, err
}

func (cli *RuntimeServiceClient) AbortAgent(opts entity.AbortRunRequest) error {
	return cli.control.Call(entity.RPCAbortAgent.String(), opts, nil, entity.RuntimeClientName)
}

func (cli *RuntimeServiceClient) CtxAbortAgent(ctx context.Context, opts entity.AbortRunRequest) error {
	// 临时逻辑，尝试修复一下 cancel 时间过长的问题
	ctx, cancel := context.WithTimeout(context.WithoutCancel(ctx), 1*time.Minute)
	defer func() {
		cancel()
		if err := ctx.Err(); err != nil {
			log.V1.CtxError(ctx, "failed abort agent: run_id: %s, err: %+v", opts.RunID, err)
		}
	}()
	return cli.control.CtxCall(ctx, entity.RPCAbortAgent.String(), opts, nil, entity.RuntimeClientName)
}

func (cli *RuntimeServiceClient) PauseAgent(opts entity.PauseRunRequest) error {
	return cli.control.Call(entity.RPCPauseAgent.String(), opts, nil, entity.RuntimeClientName)
}

func (cli *RuntimeServiceClient) CtxPauseAgent(ctx context.Context, opts entity.PauseRunRequest) error {
	ctx = context.WithoutCancel(ctx)
	return cli.control.CtxCall(ctx, entity.RPCPauseAgent.String(), opts, nil, entity.RuntimeClientName)
}

func (cli *RuntimeServiceClient) SyncPatchArtifact(opts entity.SyncPatchArtifactRequest) (response *agentsphere.UpdateArtifactResponse, err error) {
	var result agentsphere.UpdateArtifactResponse
	err = cli.control.Call(entity.RPCSyncPatchArtifact.String(), opts, &result, entity.RuntimeClientName)
	return &result, err
}

func (cli *RuntimeServiceClient) CtxSyncPatchArtifact(ctx context.Context, opts entity.SyncPatchArtifactRequest) (response *agentsphere.UpdateArtifactResponse, err error) {
	ctx = context.WithoutCancel(ctx)
	var result agentsphere.UpdateArtifactResponse
	err = cli.control.CtxCall(ctx, entity.RPCSyncPatchArtifact.String(), opts, &result, entity.RuntimeClientName)
	return &result, err
}

func (cli *RuntimeServiceClient) SubmitToolCallResults(opts entity.SubmitToolCallResultRequest) error {
	return cli.control.Call(entity.RPCSubmitToolCallResults.String(), opts, nil, entity.RuntimeClientName)
}

func (cli *RuntimeServiceClient) CtxSubmitToolCallResults(ctx context.Context, opts entity.SubmitToolCallResultRequest) error {
	ctx = context.WithoutCancel(ctx)
	return cli.control.CtxCall(ctx, entity.RPCSubmitToolCallResults.String(), opts, nil, entity.RuntimeClientName)
}

// RetrieveEvents retrieve initial events from runtime
// and ENABLE stream so server can receive subsequent events
func (cli *RuntimeServiceClient) RetrieveEvents(stream *AgentEventStream) error {
	err := cli.control.Call(entity.RPCRetrieveEvents.String(), entity.RetrieveEventsRequest{
		RunID:    stream.runID,
		Since:    stream.offset.Load(),
		ReportTo: stream.ID(),
	}, nil, entity.RuntimeClientName)
	return errors.WithMessage(err, "failed to retrieve events")
}

func (cli *RuntimeServiceClient) CtxRetrieveEvents(ctx context.Context, stream *AgentEventStream) error {
	err := cli.control.CtxCall(ctx, entity.RPCRetrieveEvents.String(), entity.RetrieveEventsRequest{
		RunID:    stream.runID,
		Since:    stream.offset.Load(),
		ReportTo: stream.ID(),
	}, nil, entity.RuntimeClientName)
	return errors.WithMessage(err, "failed to retrieve events")
}

func (cli *RuntimeServiceClient) SendMessage(opts entity.AddMessageRequest) error {
	return cli.control.Call(entity.RPCAgentSendMessage.String(), opts, nil, entity.RuntimeClientName)
}

func (cli *RuntimeServiceClient) CtxSendMessage(ctx context.Context, opts entity.AddMessageRequest) error {
	ctx = context.WithoutCancel(ctx)
	return cli.control.CtxCall(ctx, entity.RPCAgentSendMessage.String(), opts, nil, entity.RuntimeClientName)
}

func (cli *RuntimeServiceClient) ListTestCases(ctx context.Context, opts entity.ListTestCaseRequest) (res *entity.ListTestCaseResponse, err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCTestListCases.String(), err != nil) }()

	var result entity.ListTestCaseResponse
	err = cli.control.CtxCall(ctx, entity.RPCTestListCases.String(), opts, &result, entity.RuntimeClientName)
	if err != nil {
		return nil, err
	}
	return &result, err
}

func (cli *RuntimeServiceClient) RunTestCases(ctx context.Context, opts entity.RunTestCaseRequest) (res *entity.RunTestCaseResponse, err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCTestRunCases.String(), err != nil) }()

	var result entity.RunTestCaseResponse
	err = cli.control.CtxCall(ctx, entity.RPCTestRunCases.String(), opts, &result, entity.RuntimeClientName)
	if err != nil {
		return nil, err
	}
	return &result, err
}

func (cli *RuntimeServiceClient) GetTestExecutions(ctx context.Context, opts entity.GetTestExecutionsRequest) (res *entity.GetTestExecutionsResponse, err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCTestGetExecutions.String(), err != nil) }()

	var result entity.GetTestExecutionsResponse
	err = cli.control.CtxCall(ctx, entity.RPCTestGetExecutions.String(), opts, &result, entity.RuntimeClientName)
	if err != nil {
		return nil, err
	}
	return &result, err
}

func (cli *RuntimeServiceClient) GetAgentRunContext(ctx context.Context, opts entity.GetAgentRunContextRequest) (res *entity.GetAgentRunContextResponse, err error) {
	reportAgentMethodMetricsFunc := metrics.NSM.ReportAgentMethodMetrics()
	defer func() { reportAgentMethodMetricsFunc(entity.RPCTestGetAgentRunContext.String(), err != nil) }()

	var result entity.GetAgentRunContextResponse
	err = cli.control.CtxCall(ctx, entity.RPCTestGetAgentRunContext.String(), opts, &result, entity.RuntimeClientName)
	if err != nil {
		return nil, err
	}
	return &result, err
}
