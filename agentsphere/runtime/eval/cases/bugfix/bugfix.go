package bugfix

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eval"
)

func init() {
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "bugfix_1",
		Message: `## 任务：修复代码仓库中的 Bug

### 第一步：设置 (Setup):

- 操作: 克隆代码仓库。

- 仓库 URL: https://code.byted.org/devgpt/kiwis

- 分支: zzk/eval/bug_fix

### 第二步：代码修复 (Fix):

- 目标: 定位 Bug 的根本原因及其代码上下文，实施有效的代码修复。

- 输入 (请提供尽可能多的信息):

  - 已知代码片段或问题所在位置:

  - 日志/堆栈跟踪: 

  panic_err=‍‍<‍‍Error: runtime error: invalid memory address or nil pointer dereference>

panic_stack=/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/bytedtrace/bytedtrace.go:108 (0x106465a)

/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/bytedtrace/bytedtrace.go:125 (0x106455e)

/usr/local/go/src/runtime/panic.go:791 (0x483bd1)

/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/apimetrics/metrics_v3.go:60 (0x1063644)

/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/apimetrics/metrics_v3.go:65 (0x1062f65)

/usr/local/go/src/runtime/panic.go:791 (0x483bd1)

/usr/local/go/src/runtime/panic.go:262 (0x486758)

/usr/local/go/src/runtime/signal_unix.go:917 (0x486728)

/opt/tiger/compile_path/src/code.byted.org/devgpt/kiwis/agentsphere/server/handler/http_handler.go:727 (0x2e3cc1a)

/opt/tiger/compile_path/src/code.byted.org/devgpt/kiwis/agentsphere/server/handler/http_handler.go:330 (0x2e39d64)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/src/code.byted.org/devgpt/kiwis/copilotstack/common/auth/handler/hertz_middleware.go:46 (0x2e4035b)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/pkg/mod/code.byted.org/lidar/profiler/hertz@v0.4.5/middleware.go:22 (0xf91328)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/pkg/mod/github.com/hertz-contrib/localsession@v0.1.0/server/localsession.go:32 (0x106ee12)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/apimetrics/metrics_v3.go:132 (0x1062d46)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/bytedtrace/bytedtrace.go:127 (0x10642ee)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/ctx/ctx.go:93 (0x1068904)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/pkg/mod/code.byted.org/middleware/hertz@v1.13.7/byted/middlewares/server/recovery/recovery.go:43 (0x10693ad)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/app/context.go:833 (0xed13ae)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/route/engine.go:772 (0xefbb2e)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/protocol/http1/server.go:320 (0xef55d0)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/route/engine.go:540 (0xef9a9c)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/route/engine.go:431 (0xef8f5c)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/hertz@v0.9.6/pkg/network/netpoll/transport.go:132 (0xcab601)

/opt/tiger/compile_path/pkg/mod/github.com/cloudwego/netpoll@v0.6.5/connection_onevent.go:229 (0xc991bc)

/opt/tiger/compile_path/pkg/mod/github.com/bytedance/gopkg@v0.1.1/util/gopool/worker.go:69 (0xbe5967)

/opt/tiger/compile_path/pkg/mod/github.com/bytedance/gopkg@v0.1.1/util/gopool/worker.go:70 (0xbe5868)

/usr/local/go/src/runtime/asm_amd64.s:1700 (0x48cdc0)

  - 问题描述/复现步骤: 

  我在请求 CreateAssignment 这个接口如果"type"这个参数没有传入的话，就会 panic

  - 相关模块/组件: 

- 要求:

  - 识别包含 Bug 的具体文件和行号。

  - 分析根本原因，理解相关代码的上下文（如函数调用链、变量状态、控制流）。

  - 确保修复的正确性、效率和可维护性。

  - 尽量减少对现有代码的影响，避免引入新风险。

  - 保持代码风格一致，添加必要的注释说明。

- 输出: 根本原因分析摘要。

### 第三步：代码提交 (Submission):

- 目标: 将修复提交到 git，并创建合并请求 (MR)。

- 操作:    

  - 创建新的修复分支，提交代码更改，使用清晰的提交信息（Commit Message）总结修复内容。

  - 创建合并请求 (Merge Request) 从修复分支到 zzk/eval/bug_fix。

    - MR 标题:  "aime fix: Bug 的简要标题"

    - MR 描述: 详细说明问题、原因和解决方案，可关联相关 Issue。

- 输出: 创建好的合并请求链接。

### 第四步，结果汇报 (Reporting):

- 目标: 生成 Bug 修复总结报告。    

- 报告内容:

  - 问题简述。

  - 根本原因分析概要。

  - 解决方案说明（包括关键代码修改）。

  - 合并请求 (MR) 链接。

- 要求: 报告清晰、专业、易于理解。`,
		Assertions: []*runtime.Assertion{eval.GetCheckMergeRequestAssertion()},
	})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "bugfix_2",
		Message: `# Fix or implement Github Issue：
## GitHub Repo
django/django

## issues on base_commit
60a7bd89860e504c0c33b02c78edcac87f6d1b5a

## Issue description: 
UserCreationForm should save data from ManyToMany form fields
Description
        
When using contrib.auth.forms.UserCreationForm with a custom User model which has ManyToManyField fields, the data in all related form fields (e.g. a ModelMultipleChoiceField) is not saved. 
This is because unlike its parent class django.forms.ModelForm, UserCreationForm.save(commit=True) omits to call self.save_m2m(). 

---

Can you help me implement the necessary changes to the repository so that the requirements specified in the <problem_statement> are met?
I've already taken care of all changes to any of the test files described in the <problem_statement>. This means you DON'T have to modify the testing logic or any of the tests in any way!

Your task is to make the minimal changes to non-tests files in the repo to ensure the <problem_statement> is satisfied.

At last， use git diff to generate the diff patch content and save it to a local patch.diff file. `,
		Assertions: []*runtime.Assertion{eval.GetCheckPatchFileAssertion(`修改 django/contrib/auth/forms.py 文件中的save 方法，添加了对 self.save_m2m() 的调用`)},
	})
}
