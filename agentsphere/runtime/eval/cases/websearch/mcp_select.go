package websearch

import (
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/memory"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eval"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
)

func init() {
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name:    "project_space_search_mcp_select",
		Message: `帮我检索内场关于使用aime的实践、经验、总结、思考，能让我更好的了解用户实际使用aime的情况，最后的总结帮我输出文档的链接列表，至少帮我找到30篇`,
		Assertions: []*runtime.Assertion{
			eval.NewCustomAssertion("search mcp 选择", projectSpaceSearchMCPSelect),
		}},
	)
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "project_space_search_mcp_select_only",
		Message: `围绕豆包上的 “语音对话” 为特性主题，总结一篇关于豆包语音对话的产品说明书，并输出成文档。
注意，仅基于空间知识库里的文档信息做总结，需要非常详细的描述，包含所有信息。并在你产出的文档上列出你参考过的所有文档名称及文档链接。
“语音对话” 这个特性包含的主要功能包括： Audio、speech、豆包实时通话、按住说话、语音对话、ASR、TTS、语速、音色、音效、视频通话、V3模型、通话字幕、音量均衡、RTC链路、全双工通话，等和豆包上声音相关的功能`,
		Assertions: []*runtime.Assertion{
			eval.NewCustomAssertion("search mcp 选择", onlyProjectSpaceSearchMCPSelect),
		},
	})
}

func projectSpaceSearchMCPSelect(ctx runtime.Context, session *runtime.Session) {
	mcpToolSets := GetDynamicPlannerSelection(ctx, session)
	assert.Truef(ctx, lo.Contains(mcpToolSets, "search") || lo.Contains(mcpToolSets, "research"), "selection: %+v", mcpToolSets)
	return
}

func onlyProjectSpaceSearchMCPSelect(ctx runtime.Context, session *runtime.Session) {
	mcpToolSets := GetDynamicPlannerSelection(ctx, session)
	assert.Truef(ctx, lo.Contains(mcpToolSets, "project_space_search") && !lo.Contains(mcpToolSets, "search") && !lo.Contains(mcpToolSets, "research"), "selection: %+v", mcpToolSets)
	return
}

func GetDynamicPlannerSelection(ctx runtime.Context, session *runtime.Session) []string {
	time.Sleep(10)
	timeout := time.After(time.Minute * 5)
	for {
		select {
		case <-timeout:
			assert.Fail(ctx, "timeout")
		case <-time.After(time.Second * 5):
			agentRunContext := session.GetAgentRunContext()
			m := agentRunContext.State.Memory.(*memory.AgentMemory)
			for _, am := range m.ActionMemory {
				if strings.HasPrefix(am.ExecutorAgent, "dynamic_planner") {
					tools := strings.Split(cast.ToString(am.Inputs["tools"]), ",")
					if len(tools) > 0 {
						return tools
					}
				}
			}
		}
	}
}
