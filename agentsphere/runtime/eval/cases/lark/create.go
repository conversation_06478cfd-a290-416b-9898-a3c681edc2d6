package lark

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eval"
)

func init() {
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "create_lark_sheet",
		Message: `请帮我创建一个飞书表格，用于管理团队的任务分配情况：

1. 包含任务名称、负责人、优先级、状态、截止日期等字段

2. 添加一些示例数据

3. 设置合适的字段类型（单选、多选、日期等）

4. 如果可能的话，创建一个多维表格版本

数据示例：

- 需求分析，张三，高，进行中，2024-01-15

- UI设计，李四，中，已完成，2024-01-10

- 后端开发，王五，高，未开始，2024-01-20`,
		Assertions: []*runtime.Assertion{eval.GetCheckLarkSheetCitationAssertion()},
	})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "mermaid_in_lark_doc",
		Message: `用飞书文档中生成一个**微服务架构图**，要求：

- 包含API网关、用户服务、订单服务、支付服务四个核心模块；

- 用Mermaid 流程图语法绘制，箭头标明 HTTP 调用关系；

- 标注数据库依赖（MySQL/MongoDB）；

- 标题为《电商系统微服务架构》。`,
		Assertions: []*runtime.Assertion{eval.GetCheckLarkDocMermaidAssertion()},
	})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "create_lark_doc",
		Message: `我正在开发一个ai coding工具，重点侧重对存量代码库做二次开发，因此可能需要实现的功能包括：

 - 上下文感知（考虑现有代码的功能逻辑）

 - 代码生成（基于已有的代码规范、三方库、方法封装，精准生成可维护的代码）

 - apply patch（将AI生成的修改代码，精准应用到代码库中，并保持代码的结构和完整性）

 

 请帮我在上述三个方面调研codex，给出其在这三方面的实现方案，用飞书文档形式呈现你的调研结果

 https://github.com/openai/codex`,
		Assertions: []*runtime.Assertion{eval.GetCheckLarkDocumentAssertion()},
	})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "create_lark_doc2",
		Message: `分析新能源汽车 2025 年 Q2 的市场竞争格局，需包含：

- 头部品牌（特斯拉、比亚迪、蔚来）的市占率对比；

- 技术路线差异（纯电 / 混动 / 氢能）的优劣势表格；

- 政策补贴对销量的影响分析；

- 数据来源需标注工信部官网和乘联会报告。`,
		Assertions: []*runtime.Assertion{eval.GetCheckLarkDocumentCitationAssertion()},
	})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "create_lark_doc3",
		Message: `请对多智能体系统的容错机制进行深度调研，需包含：
文献筛选标准：
仅收录近 3 年（2023-2025）顶会论文（NeurIPS/ICML/AAAI）及 SCI 一区期刊文献；
排除预印本及影响因子 < 5.0 的期刊；
分析维度：
对比三类容错方案（检查点恢复、状态回溯、动态负载迁移）的故障恢复率与资源开销；
附 Anthropic、DeepMind 等头部团队的实验数据对比表；
引用规范：
在每项结论后插入[X](#citation:X)标记，X 对应原始文献编号；
过程验证：
输出中间产物：文献筛选日志、数据提取表格截图。`,
		Assertions: []*runtime.Assertion{eval.GetCheckLarkDocumentCitationAssertion()},
	})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name:       "create_lark_doc4",
		Message:    `分析美国播客市场的听众增长情况。创建一个折线图，展示2018年至2024年美国每月收听播客的人数比例。并用一个词云图，展示2024年最受欢迎的播客类型关键词，结果用飞书文档呈现。`,
		Assertions: []*runtime.Assertion{eval.GetCheckLarkDocumentCitationAssertion()},
	},
	)
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name: "create_lark_doc5",
		Message: `请生成一份飞书文档，总结 2025 年 7 月AI大模型领域的行业动态。

要求：

文档标题为《2025 年 7 月 AI 大模型行业报告》；

按技术突破、商业应用、政策监管三个章节组织内容；

引用来源需包含 36 氪、TechCrunch 的公开报道，时间范围为 2025.07.01-2025.07.31；

在‘技术突破’章节插入流程图说明Transformer架构的改进路径；

文档末尾附参考文献超链接。`,
		Assertions: []*runtime.Assertion{eval.GetCheckLarkDocumentCitationAssertion()},
	},
	)
}
