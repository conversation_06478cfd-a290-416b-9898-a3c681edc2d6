package cases

import (
	_ "code.byted.org/devgpt/kiwis/agentsphere/agents/actors/dynamic"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eval"
	_ "code.byted.org/devgpt/kiwis/agentsphere/runtime/eval/cases/bugfix"
	_ "code.byted.org/devgpt/kiwis/agentsphere/runtime/eval/cases/lark"
	_ "code.byted.org/devgpt/kiwis/agentsphere/runtime/eval/cases/websearch"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func init() {
	runtime.RegistryModuleCase(&runtime.ModuleCase{
		Name: "panic",
		Execute: func(ctx runtime.Context, runContext *iris.AgentRunContext) {
			panic("panic")
		}})
	runtime.RegistryModuleCase(&runtime.ModuleCase{
		Name: "fail",
		Execute: func(ctx runtime.Context, runContext *iris.AgentRunContext) {
			require.Fail(ctx, "fail here")
			ctx.Log("should not reach here")
		}})
	runtime.RegistryE2ECase(&runtime.E2ECase{
		Name:       "simple_websearch_e2e_test",
		Message:    "搜索一下美国在哪个大洲",
		Assertions: []*runtime.Assertion{eval.NewCustomAssertion("搜索", simpleSearch)}})
}

func simpleSearch(ctx runtime.Context, session *runtime.Session) {
	err := session.WaitCompleted()
	require.Nil(ctx, err)
	artifacts, err := session.GetArtifacts()
	assert.Nil(ctx, err)
	ctx.Logf("%+v", artifacts)
	artifact, find := lo.Find(artifacts, func(artifact *nextagent.Artifact) bool {
		_, find := lo.Find(artifact.FileMetas, func(fileMeta *nextagent.FileMeta) bool {
			return fileMeta.Type == "file" && fileMeta.SubType == "md"
		})
		return find
	})
	assert.True(ctx, find)
	files, err := session.API.RetrieveArtifactFiles(ctx, &agentsphere.RetrieveArtifactFilesRequest{
		ID:    artifact.ID,
		Files: []string{artifact.FileMetas[0].Name},
	})
	assert.Nil(ctx, err)
	fileContent := files.Files[0].Content
	ctx.Log(fileContent)
	check := ctx.GroundTruthCheck(fileContent, `1. 美国核心领土位于北美洲`)
	ctx.Logf("%+v", check)
	assert.True(ctx, check.Score >= 8)
	return
}
