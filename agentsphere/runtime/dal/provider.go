package runtimedal

import (
	"context"
	"io"
	"time"

	"github.com/docker/docker/api/types/mount"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
)

type ContainerStatus string

const (
	ContainerStatusCreated  ContainerStatus = "created"
	ContainerStatusRunning  ContainerStatus = "running"
	ContainerStatusStopping ContainerStatus = "stopping"
	ContainerStatusStopped  ContainerStatus = "stopped"
	ContainerStatusUnknown  ContainerStatus = "unknown"
	ContainerStatusDeleted  ContainerStatus = "deleted"
)

type ContainerCredential struct {
	Username    string
	CloudJWT    string // for bytecloud operations
	CodebaseJWT string // for code operations
	GitlabToken string // for git operations
}

type DockerOption struct {
	Mounts []mount.Mount
}

type CreateContainerOption struct {
	// container key to reuse, will not create a new container if specified
	//
	// NOTE: in this case container run_id, image, environ, port... cannot not be changed
	// and behavior depends on provider.
	// local: no effect
	// docker: reuse by container name (not implemented)
	// bytesuite: reuse by bytesuite tag
	// initialization script will be skipped, agent version & runtime will not be updated
	ReuseKey string

	SessionID string
	Image     string
	BashImage string
	// DO NOT pass sensitive data as environment variable, as they may be stored in plain text in third party database
	Environ       map[string]string
	Port          int
	ResourceQuota *config.RuntimeResourceQuota
	// Deprecated: transfer credential through RPC methods on each call
	Account       ContainerCredential
	WorkspacePath string
	APIBaseURL    string
	SessionType   string

	DockerOptions *DockerOption

	// BinarySource indicates which binary is used to start the runtime process.
	// AgentSphere agent runtime is used by default.
	// Format: scm://{repo_name}?version={version} or local://{binary_path}
	BinarySource string

	PSM              string
	CodebaseMentions []*iris.CodebaseMention
}

type RuntimeInstance struct {
	ID string
}

type ExecuteOption struct {
	// for cube sync exec
	SyncExecution bool
	Timeout       time.Duration
}

type ExecuteOptionFunc func(*ExecuteOption)

func WithSyncExecution() ExecuteOptionFunc {
	return func(opt *ExecuteOption) {
		opt.SyncExecution = true
	}
}

func WithTimeout(timeout time.Duration) ExecuteOptionFunc {
	return func(opt *ExecuteOption) {
		opt.Timeout = timeout
	}
}

type RuntimeProvider interface {
	CreateContainer(ctx context.Context, tenantKey string, opt CreateContainerOption) (*RuntimeInstance, error)
	StartContainer(ctx context.Context, tenantKey, id string) error
	StopContainer(ctx context.Context, tenantKey, id string) error
	DeleteContainer(ctx context.Context, tenantKey, id string) error
	ExecuteInContainer(ctx context.Context, tenantKey, cmd, id, dir string, optionsFunc ...ExecuteOptionFunc) (string, error)
	ResolveURI(ctx context.Context, tenantKey, host, id, uri string) (string, string)
	GetContainerStatus(ctx context.Context, tenantKey, id string) (ContainerStatus, error)
	ResolveServerURI(ctx context.Context, tenantKey, id, serverName string) (string, error)
	DownloadContainerFile(ctx context.Context, tenantKey, id, filePath string) (io.ReadCloser, error)
	GetSDPWebShell(ctx context.Context, username, containerName, serviceToken, cubeID string) (string, error)
}

type RuntimeProviderRegistry struct {
	Providers map[entity.RuntimeProviderType]RuntimeProvider
}

func (r *RuntimeProviderRegistry) Register(provider entity.RuntimeProviderType, p RuntimeProvider) {
	r.Providers[provider] = p
}

func (r *RuntimeProviderRegistry) HasProvider(provider entity.RuntimeProviderType) bool {
	_, ok := r.Providers[provider]
	return ok
}

func (r *RuntimeProviderRegistry) GetProvider(provider entity.RuntimeProviderType) RuntimeProvider {
	return r.Providers[provider]
}

func (r *RuntimeProviderRegistry) ResolveURI(ctx context.Context, typ entity.RuntimeProviderType, tenantKey, host, id, originalURI string) (string, string) {
	if !r.HasProvider(typ) {
		return originalURI, ""
	}
	return r.GetProvider(typ).ResolveURI(ctx, tenantKey, host, id, originalURI)
}
