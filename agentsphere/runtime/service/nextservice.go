package runtimeservice

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/runtime/service/runtimestarter"
	"code.byted.org/devgpt/kiwis/port/redis"
	"github.com/cenkalti/backoff/v4"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sourcegraph/jsonrpc2"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/runtime/trace"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/util"
)

func (s *Service) getNextAgentConfig(ctx context.Context, sessionID string) (*nextentity.AgentConfigVersion, error) {
	session, err := s.GetNextSession(ctx, GetNextSession{
		SessionID: sessionID,
		Sync:      true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get next session")
	}
	conf, err := s.nextAgentService.GetAgentConfigVersionByRole(ctx, session.Role, session.RuntimeMetaData.AgentConfigVersionID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent config version")
	}
	return conf, nil
}

func (s *Service) NextRuntimeSchedule(ctx context.Context, opt ScheduleOption) error {
	var available bool
	var agent, version string
	var maxConcurrency int
	if opt.Agent != "" {
		conf := s.GetAgentConfig(opt.Agent, opt.Version)
		if conf == nil {
			return errors.Errorf("unknown agent %s@%s", opt.Agent, opt.Version)
		}
		agent, version = opt.Agent, opt.Version
		maxConcurrency = conf.OrchestrationConfig.MaxConcurrency
	} else {
		conf, err := s.getNextAgentConfig(ctx, opt.SessionID)
		if err != nil {
			return err
		}
		agent, version = conf.AgentConfigID, conf.ID
		if conf.RuntimeConfig.OrchestrationConfig != nil {
			maxConcurrency = conf.RuntimeConfig.OrchestrationConfig.MaxConcurrency
		}
	}

	available = s.checkRuntimeCapacity(ctx, agent, version, maxConcurrency)
	if !available {
		log.V1.CtxInfo(ctx, "unable to acquire semaphore for agent %s@%s", agent, version)
		err := s.scheduleNextRuntimeLater(ctx, ScheduleOption{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version})
		return errors.Wrap(err, "failed to schedule next runtime")
	}

	err := s.nextRuntimeOrchestratorMQClient.SendMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeCreateWorkspaceEvent: lo.ToPtr(entity.NextRuntimeCreateWorkspaceEvent{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version}),
	}), entity.NextRuntimeOrchestrationTag)
	return errors.Wrap(err, "failed to schedule next runtime")
}

func (s *Service) scheduleNextRuntimeLater(ctx context.Context, opt ScheduleOption) error {
	delay := 5*time.Second + time.Duration(rand.Intn(5000))*time.Millisecond
	return s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeScheduleEvent: lo.ToPtr(entity.NextRunTimeScheduleEvent{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version}),
	}), delay, entity.NextRuntimeOrchestrationTag)
}

func (s *Service) NextRuntimeCreateWorkspace(ctx context.Context, opt ScheduleOption) error {
	// 确保并发执行不会重复创建
	lockKey := fmt.Sprintf("next_server:create_workspace:%s", opt.SessionID)
	l, err := s.locker.Acquire(ctx, lockKey)
	if err != nil {
		return errors.WithMessagef(err, "failed to lock create workspace for session %s", opt.SessionID)
	}
	defer l.Release(ctx)
	// 兜底先检查一下是否已经创建
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}
	if run.RuntimeMetadata.ContainerID != "" {
		log.V1.CtxInfo(ctx, "session id %s already create workspace", opt.SessionID)
		return nil
	}

	var oldAgentConfig *config.AgentConfig
	var newAgentConfig *nextentity.AgentConfigVersion
	var stopTimeout, providerType string
	if opt.Agent != "" {
		oldAgentConfig = s.GetAgentConfig(opt.Agent, opt.Version)
		if oldAgentConfig == nil {
			return errors.Errorf("unknown agent %s@%s", opt.Agent, opt.Version)
		}
		stopTimeout = oldAgentConfig.OrchestrationConfig.RuntimeStopTimeout
		providerType = oldAgentConfig.RuntimeConfig.Type
	} else {
		newAgentConfig, err = s.getNextAgentConfig(ctx, opt.SessionID)
		if err != nil {
			return err
		}
		if newAgentConfig.RuntimeConfig.OrchestrationConfig != nil {
			stopTimeout = newAgentConfig.RuntimeConfig.OrchestrationConfig.RuntimeStopTimeout
		}
		providerType = string(newAgentConfig.RuntimeConfig.Type)
	}

	resp, err := s.CreateRuntimeForSession(ctx, CreateRuntimeForSessionOption{
		SessionID: opt.SessionID,
		Config: lo.TernaryF(oldAgentConfig != nil,
			func() config.AgentConfig { return *oldAgentConfig },
			func() config.AgentConfig { return config.AgentConfig{} },
		),
		NextConfig:  newAgentConfig,
		APIBaseURL:  fmt.Sprintf("%s%s", s.runtimeAPIConfig.APIBaseURL, s.runtimeAPIConfig.APIPrefix),
		SessionType: agentsphere.ArtifactSessionTypeGeneral,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to create next runtime")
	}

	// 创建成功后，优先发停止事件, 防止容器不会自动删除
	timeout, err := time.ParseDuration(stopTimeout)
	if err != nil {
		timeout = 2 * time.Hour
		log.V1.CtxInfo(ctx, "no timeout is configured or in bad format: %s, using default timeout: %s", stopTimeout, timeout.String())
	}
	err = s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeStopWorkspaceEvent: lo.ToPtr(entity.NextRuntimeStopWorkspaceEvent{
			SessionID:   opt.SessionID,
			WorkspaceID: resp.Inst.ID,
			Agent:       opt.Agent,
			Version:     opt.Version,
			Provider:    providerType,
		}),
	}), timeout, entity.NextRuntimeOrchestrationTag)
	if err != nil {
		log.V1.CtxError(ctx, "failed to send stop workspace event, err: %v", err)
	}

	_, err = s.UpdateAgentRunBySession(ctx, UpdateAgentRuntimeStateOption{
		SessionID:       opt.SessionID,
		ContainerID:     resp.Inst.ID,
		Status:          entity.AgentRunStatusRunning,
		RuntimeProvider: entity.RuntimeProviderType(providerType),
		Sync:            true,
		TenantKey:       resp.TenantKey,
	})

	if err != nil {
		return errors.WithMessage(err, "failed to update next agent run")
	}

	log.V1.CtxInfo(ctx, "create runtime success for session %s, runtime ID: %s", opt.SessionID, resp.Inst.ID)
	return nil
}

func (s *Service) NextRuntimeStopWorkspace(ctx context.Context, opt ScheduleOption) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	if run.Status.IsStopped() {
		return nil
	}
	var oldAgentConfig *config.AgentConfig
	var newAgentConfig *nextentity.AgentConfigVersion
	var stopTimeoutString, deleteTimeoutString, providerType, agent, version string
	if len(run.AgentMetadata.Agents) != 0 {
		metadata := util.First(run.AgentMetadata.Agents)
		agent = metadata.Agent
		version = metadata.Version
		oldAgentConfig = s.GetAgentConfig(agent, version)
		if oldAgentConfig == nil {
			return errors.Errorf("unknown agent %s@%s", agent, version)
		}
		stopTimeoutString = oldAgentConfig.OrchestrationConfig.RuntimeStopTimeout
		deleteTimeoutString = oldAgentConfig.OrchestrationConfig.RuntimeDeleteTimeout
		providerType = oldAgentConfig.RuntimeConfig.Type
	} else {
		newAgentConfig, err = s.getNextAgentConfig(ctx, opt.SessionID)
		if err != nil {
			return err
		}
		if newAgentConfig.RuntimeConfig.OrchestrationConfig != nil {
			stopTimeoutString = newAgentConfig.RuntimeConfig.OrchestrationConfig.RuntimeStopTimeout
			deleteTimeoutString = newAgentConfig.RuntimeConfig.OrchestrationConfig.RuntimeDeleteTimeout
		}
		providerType = string(newAgentConfig.RuntimeConfig.Type)
		agent, version = newAgentConfig.AgentConfigID, newAgentConfig.ID
	}

	stopTimeout, err := time.ParseDuration(stopTimeoutString)
	if err != nil {
		stopTimeout = 2 * time.Hour
		log.V1.CtxInfo(ctx, "no timeout is configured or in bad format: %s, using default timeout: %s", stopTimeoutString, stopTimeout.String())
	}

	session, err := s.nextserverDao.GetSessionWithDeleted(ctx, nextdal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session %s, err: %v", opt.SessionID, err)
		return err
	}
	// 如果是预热容器，直接发送延迟事件
	if session.Status == nextentity.SessionStatusPrepared {
		err = s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
			NextRuntimeStopWorkspaceEvent: lo.ToPtr(entity.NextRuntimeStopWorkspaceEvent{
				SessionID:   opt.SessionID,
				WorkspaceID: run.RuntimeMetadata.ContainerID,
				Agent:       opt.Agent,
				Version:     opt.Version,
				Provider:    providerType,
			}),
		}), stopTimeout, entity.NextRuntimeOrchestrationTag)
		log.V1.CtxInfo(ctx, "send stop runtime event for session %s, runtime ID: %s", opt.SessionID, run.RuntimeMetadata.ContainerID)
		return errors.WithMessage(err, "failed to send stop workspace msg")
	}

	event, err := s.nextserverDao.GetLatestEventBySessionID(ctx, opt.SessionID, false)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.WithMessage(err, "failed to get latest event by session id")
	}
	// 如果不需要马上停止则跳过该逻辑
	if !opt.Immediate {
		// 离最近的更新时间不超时 2 个小时，
		now := time.Now()
		// 如果 event 为 nil, 说明不存在事件，说明容器启动存在问题，执行停止操作
		if event != nil && now.Sub(event.UpdatedAt) < stopTimeout {
			newTimeout := event.UpdatedAt.Add(stopTimeout).Sub(now)
			err = s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
				NextRuntimeStopWorkspaceEvent: lo.ToPtr(entity.NextRuntimeStopWorkspaceEvent{
					SessionID:   opt.SessionID,
					WorkspaceID: run.RuntimeMetadata.ContainerID,
					Agent:       opt.Agent,
					Version:     opt.Version,
					Provider:    providerType,
				}),
			}), newTimeout, entity.NextRuntimeOrchestrationTag)
			log.V1.CtxInfo(ctx, "send stop runtime event for session %s, runtime ID: %s", opt.SessionID, run.RuntimeMetadata.ContainerID)
			return errors.WithMessage(err, "failed to send stop workspace msg")
		}
	} else {
		// 幂等操作：如果是立即停止并且最近的唤醒时间比 stop event 发送的时间还晚, 或者是正在唤醒中，则忽略改次事件
		if session.LatestAgentResumeAt != nil && opt.EventTime != nil && (session.LatestAgentResumeAt.After(*opt.EventTime) || s.checkSessionRestartMark(ctx, opt.SessionID)) {
			return nil
		}
	}

	log.V1.CtxInfo(ctx, "stop workspace: %s@%s", run.RuntimeMetadata.RuntimeProvider, agent)

	err = s.abortAndWaitStop(ctx, opt.SessionID)
	if err != nil {
		return errors.WithMessage(err, "failed to stop workspace")
	}

	err = s.stopWorkspace(ctx, StopWorkspaceOption{
		RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
		WorksapceID:     run.RuntimeMetadata.ContainerID,
		TenantKey:       run.RuntimeMetadata.TenantKey,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to stop workspace")
	}

	_, err = s.UpdateAgentRunBySession(ctx, UpdateAgentRuntimeStateOption{
		SessionID: opt.SessionID,
		Status:    entity.AgentRunStatusCompleted,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "update agent run failed: %s, err: %v", opt.SessionID, err)
	}

	// release semaphore on agent completed
	if !opt.Restart {
		err = s.ReleaseRuntimeSemaphore(ctx, AcqRelSemaphoreOption{
			Agent:   agent,
			Version: version,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to stop workspace")
		}
	}

	deleteTimeout, err := time.ParseDuration(deleteTimeoutString)
	if err != nil {
		deleteTimeout = 24 * time.Hour
		log.V1.CtxInfo(ctx, "no timeout is configured or in bad format: %s, using default timeout: %s", deleteTimeoutString, deleteTimeout.String())
	}
	err = s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeDeleteWorkspaceEvent: lo.ToPtr(entity.NextRuntimeDeleteWorkspaceEvent{
			SessionID:   opt.SessionID,
			WorkspaceID: run.RuntimeMetadata.ContainerID,
			Agent:       opt.Agent,
			Version:     opt.Version,
			Provider:    providerType,
		}),
	}), deleteTimeout, entity.NextRuntimeOrchestrationTag)
	if err != nil {
		log.V1.CtxError(ctx, "failed to send delete workspace event")
	}

	log.V1.CtxInfo(ctx, "stop runtime success for session %s, runtime ID: %s", opt.SessionID, run.RuntimeMetadata.ContainerID)
	return nil
}

type FromType = runtimestarter.FromType

type StartWorkspaceOption struct {
	SessionID string
	User      *authentity.Account
	// FromType  记录启动来源，message 为基于用户消息启动, server 为 nextserver 主动启动
	FromType FromType
}

func (s *Service) NextRuntimeStartWorkspace(ctx context.Context, opt StartWorkspaceOption) error {
	// User 不能为 nil，防御性代码
	if opt.User != nil {
		err := s.credentialCache.CacheCodebaseUserJWT(ctx, opt.User.Username, opt.User.CodebaseUserJWT)
		if err != nil {
			log.V1.CtxError(ctx, "failed to cache codebase user JWT: %v", err)
		}
		err = s.credentialCache.CacheNextCodeUserJWT(ctx, opt.User.Username, opt.User.NextCodeUserJWT)
		if err != nil {
			log.V1.CtxError(ctx, "failed to cache next code token: %v", err)
		}
		err = s.credentialCache.CacheCloudUserJWT(ctx, opt.User.Username, opt.User.CloudUserJWT)
		if err != nil {
			log.V1.CtxError(ctx, "failed to cache cloud user JWT: %v", err)
		}
	}

	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	provider := s.runtimeProviderRegistry.GetProvider(run.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		return errors.New("runtime provider not found")
	}

	if err := provider.StartContainer(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID); err != nil {
		return errors.WithMessage(err, "failed to start workspace")
	}

	err = s.setSessionRestartMark(ctx, opt.SessionID)
	if err != nil {
		// only log
		log.V1.CtxError(ctx, "failed to set session restart mark: %v", err)
	}
	// 标记 session restart from type
	err = s.setSessionRestartFromType(ctx, opt.SessionID, opt.FromType)
	if err != nil {
		// only log
		log.V1.CtxError(ctx, "failed to set session restart from type: %v", err)
	}

	agentConfig, err := s.getNextAgentConfig(ctx, opt.SessionID)
	if err != nil {
		return err
	}

	// 重启成功后，优先发停止事件, 防止容器不会自动删除
	timeout, err := time.ParseDuration(agentConfig.RuntimeConfig.OrchestrationConfig.RuntimeStopTimeout)
	if err != nil {
		timeout = 2 * time.Hour
		log.V1.CtxInfo(ctx, "no timeout is configured or in bad format: %s, using default timeout: %s", agentConfig.RuntimeConfig.OrchestrationConfig.RuntimeStopTimeout, timeout.String())
	}
	err = s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeStopWorkspaceEvent: lo.ToPtr(entity.NextRuntimeStopWorkspaceEvent{
			SessionID:   opt.SessionID,
			WorkspaceID: run.RuntimeMetadata.ContainerID,
			Agent:       agentConfig.AgentConfigID,
			Version:     agentConfig.ID,
			Provider:    string(agentConfig.RuntimeConfig.Type),
			Restart:     true,
		}),
	}), timeout, entity.NextRuntimeOrchestrationTag)
	if err != nil {
		log.V1.CtxError(ctx, "failed to send stop workspace event, err: %v", err)
	}

	_, err = s.UpdateAgentRunBySession(ctx, UpdateAgentRuntimeStateOption{
		SessionID: opt.SessionID,
		Status:    entity.AgentRunStatusRunning,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "update agent run failed: %s, err: %v", opt.SessionID, err)
	}

	return nil
}

func (s *Service) NextRuntimeDeleteWorkspace(ctx context.Context, opt ScheduleOption) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	if !run.Status.IsStopped() {
		return nil
	}

	provider := s.runtimeProviderRegistry.GetProvider(run.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		return errors.New("runtime provider not found")
	}

	status, err := provider.GetContainerStatus(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID)
	if err != nil && !strings.Contains(err.Error(), "cube not found") {
		return errors.WithMessage(err, "failed to get container status")
	}

	if status != runtimedal.ContainerStatusStopped && status != runtimedal.ContainerStatusStopping {
		log.V1.CtxInfo(ctx, "workspace is not stop, status: %s", status)
		return nil
	}

	agentConfig, err := s.getNextAgentConfig(ctx, opt.SessionID)
	if err != nil {
		return err
	}

	// 重启成功后，优先发停止事件, 防止容器不会自动删除
	timeout, err := time.ParseDuration(agentConfig.RuntimeConfig.OrchestrationConfig.RuntimeDeleteTimeout)
	if err != nil {
		timeout = 24 * time.Hour
		log.V1.CtxInfo(ctx, "no timeout is configured or in bad format: %s, using default timeout: %s", agentConfig.RuntimeConfig.OrchestrationConfig.RuntimeDeleteTimeout, timeout.String())
	}

	event, err := s.nextserverDao.GetLatestEventBySessionID(ctx, opt.SessionID, false)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.WithMessage(err, "failed to get latest event by session id")
	}
	// 如果不需要马上停止则执行该逻辑
	if !opt.Immediate {
		// 离最近的更新时间不超时 x 个小时，
		now := time.Now()
		if event != nil && now.Sub(event.UpdatedAt) < timeout {
			newTimeout := event.UpdatedAt.Add(timeout).Sub(now)
			err = s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
				NextRuntimeDeleteWorkspaceEvent: lo.ToPtr(entity.NextRuntimeDeleteWorkspaceEvent{
					SessionID:   opt.SessionID,
					WorkspaceID: run.RuntimeMetadata.ContainerID,
					Agent:       opt.Agent,
					Version:     opt.Version,
					Provider:    string(agentConfig.RuntimeConfig.Type),
					Immediately: opt.Immediate,
				}),
			}), newTimeout, entity.NextRuntimeOrchestrationTag)
			log.V1.CtxInfo(ctx, "send stop runtime event for session %s, runtime ID: %s", opt.SessionID, run.RuntimeMetadata.ContainerID)
			return errors.WithMessage(err, "failed to send delete workspace msg")
		}
	}

	if err := provider.DeleteContainer(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID); err != nil {
		return errors.WithMessage(err, "failed to stop workspace")
	}
	_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
		ID:                 opt.SessionID,
		CanResume:          lo.ToPtr(false),
		CanNotResumeReason: lo.ToPtr(nextentity.SessionCanNotResumeReasonExpired),
		Status:             lo.ToPtr(nextentity.SessionStatusClosed),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update session %s, err: %v", opt.SessionID, err)
	}

	// Delete FaaS deployments in background to avoid blocking main workspace deletion
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "panic in FaaS deployment deletion goroutine for session %s: %v", opt.SessionID, r)
			}
		}()

		bgCtx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()

		err := s.DeleteSessionFaaSDeploymentsByID(bgCtx, opt.SessionID)
		if err != nil {
			log.V1.CtxError(bgCtx, "failed to delete FaaS deployments for session %s: %v", opt.SessionID, err)
		}
	}()

	return nil
}

func (s *Service) NextRuntimeImmediatelyDeleteWorkspace(ctx context.Context, opt ScheduleOption) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	provider := s.runtimeProviderRegistry.GetProvider(run.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		return errors.New("runtime provider not found")
	}

	if !run.Status.IsStopped() {
		err = s.abortAndWaitStop(ctx, opt.SessionID)
		if err != nil {
			return errors.WithMessage(err, "failed to abort and wait stop")
		}

		status, err := provider.GetContainerStatus(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID)
		if err != nil {
			return errors.WithMessage(err, "failed to get container status")
		}

		if status == runtimedal.ContainerStatusRunning || status == runtimedal.ContainerStatusCreated {
			err = s.stopWorkspace(ctx, StopWorkspaceOption{
				RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
				WorksapceID:     run.RuntimeMetadata.ContainerID,
				TenantKey:       run.RuntimeMetadata.TenantKey,
			})
			if err != nil {
				return errors.WithMessage(err, "failed to immediately delete workspace")
			}

			_, err = s.UpdateAgentRunBySession(ctx, UpdateAgentRuntimeStateOption{
				SessionID: opt.SessionID,
				Status:    entity.AgentRunStatusCompleted,
			})
			if err != nil {
				log.V1.CtxWarn(ctx, "update agent run failed: %s, err: %v", opt.SessionID, err)
			}
			var agent, version string
			if len(run.AgentMetadata.Agents) != 0 {
				metadata := util.First(run.AgentMetadata.Agents)
				agent = metadata.Agent
				version = metadata.Version
			} else {
				newAgentConfig, err := s.getNextAgentConfig(ctx, opt.SessionID)
				if err != nil {
					return err
				}
				agent, version = newAgentConfig.AgentConfigID, newAgentConfig.ID
			}

			log.V1.CtxInfo(ctx, "immediately delete workspace success stop: %s@%s", run.RuntimeMetadata.RuntimeProvider, agent)

			// release semaphore on agent completed
			err = s.ReleaseRuntimeSemaphore(ctx, AcqRelSemaphoreOption{
				Agent:   agent,
				Version: version,
			})
			if err != nil {
				return errors.WithMessage(err, "failed to stop workspace")
			}
		}
	}

	status, err := provider.GetContainerStatus(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID)
	if err != nil && !strings.Contains(err.Error(), "cube not found") {
		return errors.WithMessage(err, "failed to get container status")
	}

	if status == runtimedal.ContainerStatusStopped || status == runtimedal.ContainerStatusStopping {
		if err := provider.DeleteContainer(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID); err != nil {
			return errors.WithMessage(err, "failed to stop workspace")
		}
	}
	log.V1.CtxInfo(ctx, "immediately delete workspace success delete: %s", opt.SessionID)

	_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
		ID:                 opt.SessionID,
		CanResume:          lo.ToPtr(false),
		CanNotResumeReason: lo.ToPtr(nextentity.SessionCanNotResumeReasonDeleted),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update session %s, err: %v", opt.SessionID, err)
	}

	// Clean up FaaS deployments for this session in background to avoid blocking main workspace deletion
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "panic in FaaS deployment deletion goroutine for session %s: %v", opt.SessionID, r)
			}
		}()

		bgCtx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()

		err := s.DeleteSessionFaaSDeploymentsByID(bgCtx, opt.SessionID)
		if err != nil {
			log.V1.CtxError(bgCtx, "failed to delete FaaS deployments for session %s: %v", opt.SessionID, err)
		}
	}()

	return nil
}

// 删除没有大仓缓存的旧pod，稍后创建新的有缓存的

func (s *Service) NextRuntimeDeleteOldContainer(ctx context.Context, opt ScheduleOption) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	provider := s.runtimeProviderRegistry.GetProvider(run.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		return errors.New("runtime provider not found")
	}

	if err := provider.DeleteContainer(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID); err != nil {
		return errors.WithMessage(err, "failed to stop workspace")
	}
	_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
		ID:     opt.SessionID,
		Status: pointer.To(nextentity.SessionStatusCreated),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update session %s, err: %v", opt.SessionID, err)
	}
	return nil
}

type NextAbortAgent struct {
	SessionID string
}

func (s *Service) NextAbortAgent(ctx context.Context, opt NextAbortAgent) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}

	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: entity.RuntimeMetadata{
			ContainerID:     run.RuntimeMetadata.ContainerID,
			URI:             run.RuntimeMetadata.URI,
			RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
		},
		Relay: s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect to runtime process: %v", err)
		return err
	}
	log.V1.CtxInfo(ctx, "abort agent runtime process: %v", run.AgentMetadata.Input)

	defer cli.Close()
	ctx = s.InjectCtxDestInfo(ctx, run.RuntimeMetadata.URI, s.GetDestPSM(ctx, run))
	err = cli.CtxAbortAgent(ctx, entity.AbortRunRequest{
		RunID: run.ID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to abort agent")
	}
	return nil
}

type NextPauseAgent struct {
	SessionID string
	Exit      bool
}

func (s *Service) NextPauseAgent(ctx context.Context, opt NextPauseAgent) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}

	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: entity.RuntimeMetadata{
			ContainerID:     run.RuntimeMetadata.ContainerID,
			URI:             run.RuntimeMetadata.URI,
			RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
		},
		Relay: s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect to runtime process: %v", err)
		return err
	}
	log.V1.CtxInfo(ctx, "pause agent runtime process: %v", run.AgentMetadata.Input)

	defer cli.Close()
	ctx = s.InjectCtxDestInfo(ctx, run.RuntimeMetadata.URI, s.GetDestPSM(ctx, run))
	err = cli.CtxPauseAgent(ctx, entity.PauseRunRequest{
		RunID: run.ID,
		Exit:  opt.Exit,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to pause agent")
	}
	return nil
}

type NextRunAgentOption struct {
	SessionID string
	URI       string
}

func (s *Service) NextRunAgent(ctx context.Context, opt NextRunAgentOption) error {
	log.V1.CtxInfo(ctx, "start run agent with session id: %s, uri: %s, ctx err: %v", opt.SessionID, opt.URI, ctx.Err())
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}

	var agentRunConfig *config.AgentRunConfig
	var newAgentRunConfig *entity.AgentRunConfig
	var agentName, openAIToken, psm string
	if len(run.AgentMetadata.Agents) != 0 {
		metadata := util.First(run.AgentMetadata.Agents)
		oldAgentConfig := s.GetAgentConfig(metadata.Agent, metadata.Version)
		if oldAgentConfig == nil {
			return errors.Errorf("unknown agent %s@%s", metadata.Agent, metadata.Version)
		}
		agentName = lo.Ternary(oldAgentConfig.RuntimeAgentID != "", oldAgentConfig.RuntimeAgentID, metadata.Agent)
		agentRunConfig = lo.ToPtr(oldAgentConfig.RuntimeConfig.AgentRunConfig)
		if agentRunConfig.ExtraOptions == nil {
			agentRunConfig.ExtraOptions = make(map[string]any)
		}
		openAIToken = lo.Ternary(oldAgentConfig.RuntimeConfig.InjectOpenAIToken, s.getOpenAIToken(), "")
		psm = oldAgentConfig.RuntimeConfig.PSM
	} else {
		newAgentConfig, err := s.getNextAgentConfig(ctx, opt.SessionID)
		if err != nil {
			return err
		}

		newAgentRunConfig = &entity.AgentRunConfig{
			CustomConfig: newAgentConfig.CustomConfig,
			PromptConfig: &entity.PromptConfig{
				Prompts: lo.Map(newAgentConfig.PromptConfig.Prompts, func(item *nextentity.PromptConfigMetadata, index int) *entity.PromptConfigMetadata {
					return &entity.PromptConfigMetadata{
						Key:     item.Key,
						ID:      item.ID,
						Version: item.Version,
					}
				}),
			},
			KnowledgesetConfig: &entity.KnowledgesetConfig{
				Knowledgesets: lo.Map(newAgentConfig.KnowledgesetConfig.Knowledgesets, func(item *nextentity.KnowledgesetMetadata, index int) *entity.KnowledgesetMetadata {
					return &entity.KnowledgesetMetadata{
						KnowledgesetID:        item.KnowledgesetID,
						KnowledgesetVersionID: item.KnowledgesetVersionID,
						KnowledgesetType:      item.KnowledgesetType,
					}
				}),
			},
		}
		agentName = newAgentConfig.RuntimeConfig.ID
		openAIToken = lo.Ternary(newAgentConfig.RuntimeConfig.InjectOpenAIToken, s.getOpenAIToken(), "")
		psm = newAgentConfig.RuntimeConfig.PSM
	}

	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: entity.RuntimeMetadata{
			ContainerID:     run.RuntimeMetadata.ContainerID,
			URI:             opt.URI,
			RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
		},
		Relay: s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect to runtime process: %v", err)
		return err
	}
	log.V1.CtxInfo(ctx, "connected to runtime process: %s", run.ID)
	defer cli.Close()

	codebaseUserJWT, err := s.credentialCache.GetCodebaseJWT(ctx, run.CreatorID)
	if err != nil {
		log.V1.CtxError(ctx, "credential has expired: %v", err)
		return errors.WithMessage(err, "user credential has expired")
	}

	nextCodeUserJWT, err := s.credentialCache.GetNextCodeUserJWT(ctx, run.CreatorID)
	if err != nil {
		log.V1.CtxError(ctx, "nextcode credential has expired: %v", err)
	}
	// cloud user jwt is optional
	cloudUserJWT, _ := s.credentialCache.GetCloudUserJWT(ctx, run.CreatorID)

	larkAPPID, err := s.nextLarkService.GetUserAccessToken(ctx, run.CreatorID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark user access token: %v", err)
		// return errors.WithMessage(err, "failed to get lark user access token")
	}
	var larkUserOpenID string
	larkUser, err := s.nextLarkService.GetLarkUser(ctx, run.CreatorID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark user info: %v", err)
	}
	if larkUser != nil {
		larkUserOpenID = larkUser.OpenID
	}

	userInfo, err := s.hulkCloudClient.GetUserInfo(ctx, run.CreatorID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get user info: %v", err)
		return errors.WithMessage(err, "failed to get current user info")
	}

	log.V1.CtxInfo(ctx, "send initial message to runtime process: %v", run.AgentMetadata)
	ctx = s.InjectCtxDestInfo(ctx, opt.URI, psm)
	params := run.AgentMetadata.Params
	// if the detect agent result is not nil, set the detection result to the params
	if result, err := s.GetDetectAgentResult(ctx, opt.SessionID); err == nil && result != nil {
		params[entity.RuntimeParametersDetectionResult] = result
	}
	err = cli.CtxRunAgent(ctx, entity.RunAgentRequest{
		AgentName: agentName,
		SessionID: opt.SessionID,
		RunID:     run.ID,
		User:      entity.NewUser(entity.UserTypeUser, run.CreatorID),
		UserInfo: &entity.UserInfo{
			Name:  lo.Ternary(len(userInfo.Name) == 0, userInfo.EnName, userInfo.Name),
			Email: userInfo.Email,
		},
		Environ: map[string]string{
			entity.RuntimeEnvironLLMToken:            openAIToken,
			entity.RuntimeEnvironUserCodebaseJWT:     codebaseUserJWT,
			entity.RuntimeEnvironUserCloudJWT:        cloudUserJWT,
			entity.RunTimeLarkUserAccessToken:        larkAPPID,
			entity.RunTimeLarkUserOpenID:             larkUserOpenID,
			entity.RuntimeNextCodeAppID:              s.nextCodeConfig.GetValue().AppID,
			entity.RuntimeNextCodeSecret:             s.nextCodeConfig.GetValue().AppSecret,
			entity.RuntimeNextCodeUserJWT:            nextCodeUserJWT,
			entity.RuntimeDeepWikiNextCodeAppID:      s.deepWikiConfig.GetValue().NextCodeAppID,
			entity.RuntimeDeepWikiNextCodeSecret:     s.deepWikiConfig.GetValue().NextCodeAppSecret,
			entity.RuntimeDeepWikiCodebaseServiceJWT: s.deepWikiConfig.GetValue().CodebaseServiceJWT,
		},
		Parameters:     params,
		Input:          run.AgentMetadata.Input,
		Config:         agentRunConfig,
		AgentRunConfig: newAgentRunConfig,
		ExitOnFinish:   false,
	})
	if err != nil {
		// connection is lost, the runtime will try to find a new server to connect
		if errors.Is(err, jsonrpc2.ErrClosed) {
			log.V1.CtxInfo(ctx, "failed to send run agent because json rpc closed: %v, session id: %s", err, opt.SessionID)
			return nil
		}
		log.V1.CtxError(ctx, "failed to send run agent process: %+v", err)
		return err
	}
	// FIXME(cyx): 在本地 docker 环境下，偶发收不到 runtime 请求，经测试这里 sleep 2s 可以解决，推测是有某些地方有竞争问题。
	if !env.InTCE() {
		time.Sleep(time.Second * 2)
	}
	log.V1.CtxInfo(ctx, "sent initial message to runtime process, session id: %s", opt.SessionID)
	return nil
}

// NextGenerateRunAgentRequest generate run agent request to make mock AgentRuntimeContext.
func (s *Service) NextGenerateRunAgentRequest(ctx context.Context, role nextentity.SessionRole, user string) (request entity.RunAgentRequest, err error) {
	var agentRunConfig *config.AgentRunConfig
	var newAgentRunConfig *entity.AgentRunConfig
	var agentName, openAIToken string
	newAgentConfig, err := s.nextAgentService.GetAgentConfigVersionByRole(ctx, &role, "")

	if err != nil {
		return request, err
	}

	newAgentRunConfig = &entity.AgentRunConfig{
		CustomConfig: newAgentConfig.CustomConfig,
		PromptConfig: &entity.PromptConfig{
			Prompts: lo.Map(newAgentConfig.PromptConfig.Prompts, func(item *nextentity.PromptConfigMetadata, index int) *entity.PromptConfigMetadata {
				return &entity.PromptConfigMetadata{
					Key:     item.Key,
					ID:      item.ID,
					Version: item.Version,
				}
			}),
		},
		KnowledgesetConfig: &entity.KnowledgesetConfig{
			Knowledgesets: lo.Map(newAgentConfig.KnowledgesetConfig.Knowledgesets, func(item *nextentity.KnowledgesetMetadata, index int) *entity.KnowledgesetMetadata {
				return &entity.KnowledgesetMetadata{
					KnowledgesetID:        item.KnowledgesetID,
					KnowledgesetVersionID: item.KnowledgesetVersionID,
					KnowledgesetType:      item.KnowledgesetType,
				}
			}),
		},
	}
	agentName = newAgentConfig.RuntimeConfig.ID
	openAIToken = lo.Ternary(newAgentConfig.RuntimeConfig.InjectOpenAIToken, s.getOpenAIToken(), "")

	codebaseUserJWT, err := s.credentialCache.GetCodebaseJWT(ctx, user)
	if err != nil {
		log.V1.CtxError(ctx, "credential has expired: %v", err)
		return request, errors.WithMessage(err, "user credential has expired")
	}

	nextCodeUserJWT, err := s.credentialCache.GetNextCodeUserJWT(ctx, user)
	if err != nil {
		log.V1.CtxError(ctx, "nextcode credential has expired: %v", err)
	}
	// cloud user jwt is optional
	cloudUserJWT, _ := s.credentialCache.GetCloudUserJWT(ctx, user)

	larkAPPID, err := s.nextLarkService.GetUserAccessToken(ctx, user)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark user access token: %v", err)
		// return errors.WithMessage(err, "failed to get lark user access token")
	}
	var larkUserOpenID string
	larkUser, err := s.nextLarkService.GetLarkUser(ctx, user)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get lark user info: %v", err)
	}
	if larkUser != nil {
		larkUserOpenID = larkUser.OpenID
	}

	userInfo, err := s.hulkCloudClient.GetUserInfo(ctx, user)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get user info: %v", err)
		return request, errors.WithMessage(err, "failed to get current user info")
	}

	log.V1.CtxInfo(ctx, "send initial message to runtime process: %v", user)
	return entity.RunAgentRequest{
		AgentName:    agentName,
		AssignmentID: "",
		RunID:        "",
		SessionID:    "00000000-0000-0000-0000-000000000000",
		User:         entity.NewUser(entity.UserTypeUser, user),
		UserInfo: &entity.UserInfo{
			Name:  lo.Ternary(len(userInfo.Name) == 0, userInfo.EnName, userInfo.Name),
			Email: userInfo.Email,
		},
		Input:          nil,
		Config:         agentRunConfig,
		AgentRunConfig: newAgentRunConfig,
		Parameters:     map[entity.RuntimeParameterKey]any{},
		Environ: map[string]string{
			entity.RuntimeEnvironLLMToken:            openAIToken,
			entity.RuntimeEnvironUserCodebaseJWT:     codebaseUserJWT,
			entity.RuntimeEnvironUserCloudJWT:        cloudUserJWT,
			entity.RunTimeLarkUserAccessToken:        larkAPPID,
			entity.RunTimeLarkUserOpenID:             larkUserOpenID,
			entity.RuntimeNextCodeAppID:              s.nextCodeConfig.GetValue().AppID,
			entity.RuntimeNextCodeSecret:             s.nextCodeConfig.GetValue().AppSecret,
			entity.RuntimeNextCodeUserJWT:            nextCodeUserJWT,
			entity.RuntimeDeepWikiNextCodeAppID:      s.deepWikiConfig.GetValue().NextCodeAppID,
			entity.RuntimeDeepWikiNextCodeSecret:     s.deepWikiConfig.GetValue().NextCodeAppSecret,
			entity.RuntimeDeepWikiCodebaseServiceJWT: s.deepWikiConfig.GetValue().CodebaseServiceJWT,
		},
		ExitOnFinish: false,
		Restart:      false,
	}, nil
}

type NextSubmitToolResultsOption struct {
	SessionID string
	URI       string
	ID        string
	Name      string
	Results   map[string]any
	Err       *string
	User      *authentity.Account
	LarkToken *string
	Message   *entity.Message
}

func (s *Service) NextSubmitToolResults(ctx context.Context, opt NextSubmitToolResultsOption) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}

	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: entity.RuntimeMetadata{
			ContainerID:     run.RuntimeMetadata.ContainerID,
			URI:             run.RuntimeMetadata.URI,
			RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
		},
		Relay: s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect to runtime process: %v", err)
		return err
	}
	log.V1.CtxInfo(ctx, "send submit tool results to runtime process: %v, runID: %s, toolCallID: %S", run.AgentMetadata.Input, run.ID, opt.ID)

	environ := map[string]string{
		entity.RuntimeEnvironUserCodebaseJWT: opt.User.CodebaseUserJWT,
		entity.RuntimeEnvironUserCloudJWT:    opt.User.CloudUserJWT,
	}
	if opt.LarkToken != nil {
		environ[entity.RunTimeLarkUserAccessToken] = *opt.LarkToken
	}
	defer cli.Close()
	ctx = s.InjectCtxDestInfo(ctx, opt.URI, s.GetDestPSM(ctx, run))
	err = cli.CtxSubmitToolCallResults(ctx, entity.SubmitToolCallResultRequest{
		RunID:   run.ID,
		CallID:  opt.ID,
		Name:    opt.Name,
		Results: opt.Results,
		Error:   opt.Err,
		Environ: environ,
		Message: opt.Message, // 如果有 message 则从 message 中获取参数
	})
	if err != nil {
		return errors.WithMessage(err, "failed to submit tool results")
	}
	return nil
}

type NextSendMessageOption struct {
	URI         string
	SessionID   string
	MessageID   string
	Agent       string
	Version     string
	InitialMsg  string
	Attachments []entity.AttachmentMeta
	Options     entity.MessageOptions
	User        *authentity.Account
	LarkToken   *string
	Sync        bool
	Mentions    []*entity.Mention
}

func (s *Service) NextSendNewMessage(ctx context.Context, opt NextSendMessageOption) error {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      opt.Sync,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}

	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: entity.RuntimeMetadata{
			ContainerID:     run.RuntimeMetadata.ContainerID,
			URI:             run.RuntimeMetadata.URI,
			RuntimeProvider: run.RuntimeMetadata.RuntimeProvider,
		},
		Relay: s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect to runtime process: %v", err)
		return err
	}
	log.V1.CtxInfo(ctx, "send message runtime process: %v, runID: %s", run.AgentMetadata.Input, run.ID)

	defer cli.Close()
	ctx = s.InjectCtxDestInfo(ctx, opt.URI, s.GetDestPSM(ctx, run))
	environ := map[string]string{
		entity.RuntimeEnvironUserCodebaseJWT: opt.User.CodebaseUserJWT,
		entity.RuntimeEnvironUserCloudJWT:    opt.User.CloudUserJWT,
	}
	if opt.LarkToken != nil {
		environ[entity.RunTimeLarkUserAccessToken] = *opt.LarkToken
	}
	err = cli.CtxSendMessage(ctx, entity.AddMessageRequest{
		RunID: run.ID,
		Input: &entity.Message{
			ID:             opt.MessageID,
			ConversationID: opt.SessionID,
			Type:           entity.MessageTypeNormal,
			Creator: entity.User{
				Type:     entity.UserTypeUser,
				Username: opt.User.Username,
			},
			Content: entity.MessageContent{
				Content: opt.InitialMsg,
			},
			Attachments: opt.Attachments,
			Mentions:    opt.Mentions,
			Options:     opt.Options,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		Environ: environ,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to send message")
	}
	return nil
}

type NextCreateAgentRunForPrepare struct {
	Agent       string
	Version     string
	SessionID   string
	Provider    entity.RuntimeProviderType
	URI         string
	ContainerID string
}

func (s *Service) NextCreateAgentRunForPrepare(ctx context.Context, opt NextCreateAgentRunForPrepare) (*entity.AgentRun, error) {
	runID := s.idGen.NewID()

	agents := []entity.AgentMetadata{}
	if opt.Agent != "" {
		agents = append(agents, entity.AgentMetadata{
			Agent:   opt.Agent,
			Version: opt.Version,
		})
	}
	params := map[entity.RuntimeParameterKey]interface{}{}
	session, err := s.nextserverDao.GetSession(ctx, nextdal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}

	params[entity.RuntimeParametersAgentConfigVersionID] = session.RuntimeMetaData.AgentConfigVersionID
	params[entity.RuntimeParametersAgentConfigName] = session.RuntimeMetaData.AgentConfigName
	params[entity.RuntimeParametersWaitAgentRun] = true
	run, err := s.dao.CreateAgentRun(ctx, runtimedal.CreateAgentRunOption{
		ID:        runID,
		SessionID: opt.SessionID,
		CreatorID: "unknown", // 后续更新
		Status:    entity.AgentRunStatusCreated,
		AgentMetadata: entity.AgentRunAgentMetadata{
			Agents: agents,
			Params: params,
		},
		RuntimeMetadata: entity.RuntimeMetadata{
			RuntimeProvider:   opt.Provider,
			ContainerID:       opt.ContainerID,
			PID:               0,
			URI:               opt.URI,
			InitTimeCost:      0,
			LastReportMessage: "",
			WorkspacePath:     "",
			ContainerHost:     "",
			TenantKey:         "",
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create agent run")
	}
	return run, nil
}

type NextUpdateAgentRunForPrepare struct {
	SessionID           string
	MessageID           string
	InitialMsg          string
	Attachments         []entity.AttachmentMeta
	Options             entity.MessageOptions
	User                *authentity.Account
	EnableInternalTools *bool
	MCPs                []*nextentity.MCP
	Agents              []entity.AgentMetadata
	Params              map[entity.RuntimeParameterKey]interface{}
	SpaceID             string
	Mentions            []*entity.Mention
}

func (s *Service) NextUpdateAgentRunForPrepare(ctx context.Context, opt NextUpdateAgentRunForPrepare) (*entity.AgentRun, error) {
	var (
		run *entity.AgentRun
		err error
	)
	err = backoff.Retry(func() error {
		run, err = s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
			SessionID: opt.SessionID,
			Sync:      false,
		})
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}
	var datasetID string
	if len(opt.SpaceID) > 0 {
		dataset, err := s.nextserverDao.GetDatasetBySpaceID(ctx, opt.SpaceID)
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get dataset by spaceID `%s`", opt.SpaceID)
		}
		datasetID = dataset.ID
	}
	run, err = s.dao.UpdateAgentRunFunc(ctx, run.ID, func(item *entity.AgentRun) {
		item.CreatorID = opt.User.Username
		params := item.AgentMetadata.Params
		if params == nil {
			params = map[entity.RuntimeParameterKey]interface{}{}
		}
		if opt.Options.Locale != "" {
			params[entity.RuntimeParametersUserLocale] = opt.Options.Locale
		}
		if opt.EnableInternalTools != nil {
			params[entity.RuntimeParametersEnableInternalTools] = *opt.EnableInternalTools
		}
		if len(opt.MCPs) > 0 {
			params[entity.RuntimeParametersMCPs] = ConvertToMCPProviders(opt.MCPs)
		}
		params[entity.RuntimeParametersDisableToolSummarizer] = true
		if opt.SpaceID != "" {
			params[entity.RuntimeParametersSpaceID] = opt.SpaceID
		}
		if datasetID != "" {
			params[entity.RuntimeParametersDatasetID] = datasetID
		}
		for k, v := range opt.Params {
			params[k] = v
		}
		agents := run.AgentMetadata.Agents
		if len(opt.Agents) > 0 {
			agents = opt.Agents
		}
		item.AgentMetadata = entity.AgentRunAgentMetadata{
			Agents: agents,
			Params: params,
			Input: &entity.Message{
				ID:             opt.MessageID,
				ConversationID: opt.SessionID,
				Type:           entity.MessageTypeNormal,
				Creator: entity.User{
					Type:     entity.UserTypeUser,
					Username: opt.User.Username,
				},
				Content: entity.MessageContent{
					Content: opt.InitialMsg,
				},
				Attachments: opt.Attachments,
				Mentions:    opt.Mentions,
				Options:     opt.Options,
				UpdatedAt:   time.Now(),
			},
		}
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update agent run")
	}

	err = s.credentialCache.CacheCodebaseUserJWT(ctx, opt.User.Username, opt.User.CodebaseUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache codebase user JWT: %v", err)
	}
	err = s.credentialCache.CacheNextCodeUserJWT(ctx, opt.User.Username, opt.User.NextCodeUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache next code token: %v", err)
	}
	err = s.credentialCache.CacheCloudUserJWT(ctx, opt.User.Username, opt.User.CloudUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache cloud user JWT: %v", err)
	}

	return run, nil
}

type NextCreateAgentRunOption struct {
	SessionID           string
	MessageID           string
	Agent               string
	Version             string
	InitialMsg          string
	Attachments         []entity.AttachmentMeta
	Options             entity.MessageOptions
	User                *authentity.Account
	EnableInternalTools *bool
	MCPs                []*nextentity.MCP
	Params              map[entity.RuntimeParameterKey]interface{}
	SpaceID             string
	Mentions            []*entity.Mention
}

func (s *Service) NextCreateAgentRun(ctx context.Context, opt NextCreateAgentRunOption) (*entity.AgentRun, error) {
	runID := s.idGen.NewID()

	if s.agentList == nil {
		return nil, errors.Errorf("default agents is nil")
	}

	var agents []entity.AgentMetadata
	if opt.Agent != "" {
		conf := s.GetAgentConfig(opt.Agent, opt.Version)
		if conf == nil {
			return nil, errors.Errorf("unknown agent %s@%s", opt.Agent, opt.Version)
		}
		agents = append(agents, entity.AgentMetadata{
			Agent:   conf.Agent,
			Version: conf.Version,
		})
	}

	params := map[entity.RuntimeParameterKey]interface{}{}
	for k, v := range opt.Params {
		params[k] = v
	}
	if opt.Options.Locale != "" {
		params[entity.RuntimeParametersUserLocale] = opt.Options.Locale
	}
	if opt.EnableInternalTools != nil {
		params[entity.RuntimeParametersEnableInternalTools] = *opt.EnableInternalTools
	}

	if len(opt.MCPs) > 0 {
		params[entity.RuntimeParametersMCPs] = ConvertToMCPProviders(opt.MCPs)
	}
	if opt.SpaceID != "" {
		params[entity.RuntimeParametersSpaceID] = opt.SpaceID
	}
	params[entity.RuntimeParametersDisableToolSummarizer] = true
	session, err := s.nextserverDao.GetSession(ctx, nextdal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}
	params[entity.RuntimeParametersAgentConfigVersionID] = session.RuntimeMetaData.AgentConfigVersionID
	params[entity.RuntimeParametersAgentConfigName] = session.RuntimeMetaData.AgentConfigName
	params[entity.RuntimeParametersWaitAgentRun] = true
	run, err := s.dao.CreateAgentRun(ctx, runtimedal.CreateAgentRunOption{
		ID:        runID,
		SessionID: opt.SessionID,
		CreatorID: opt.User.Username,
		Status:    entity.AgentRunStatusCreated,
		AgentMetadata: entity.AgentRunAgentMetadata{
			Agents: agents,
			Input: &entity.Message{
				ID:             opt.MessageID,
				ConversationID: opt.SessionID,
				Type:           entity.MessageTypeNormal,
				Creator: entity.User{
					Type:     entity.UserTypeUser,
					Username: opt.User.Username,
				},
				Content: entity.MessageContent{
					Content: opt.InitialMsg,
				},
				Attachments: opt.Attachments,
				Mentions:    opt.Mentions,
				Options:     opt.Options,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			Params: params,
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create agent run")
	}

	err = s.credentialCache.CacheCodebaseUserJWT(ctx, opt.User.Username, opt.User.CodebaseUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache codebase user JWT: %v", err)
	}
	err = s.credentialCache.CacheNextCodeUserJWT(ctx, opt.User.Username, opt.User.NextCodeUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache next code token: %v", err)
	}
	err = s.credentialCache.CacheCloudUserJWT(ctx, opt.User.Username, opt.User.CloudUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache cloud user JWT: %v", err)
	}

	return run, nil
}

// NextSchedule will send a RuntimeScheduleEvent to general agent session queue
func (s *Service) NextSchedule(ctx context.Context, opt ScheduleOption) error {
	return s.nextRuntimeOrchestratorMQClient.SendMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeScheduleEvent: lo.ToPtr(entity.NextRunTimeScheduleEvent{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version}),
	}), entity.NextRuntimeOrchestrationTag)
}

func (s *Service) SendNextRuntimeImmediatelyDeleteWorkspace(ctx context.Context, opt ScheduleOption) error {
	return s.nextRuntimeOrchestratorMQClient.SendMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeImmediatelyDeleteWorkspaceEvent: lo.ToPtr(entity.NextRuntimeImmediatelyDeleteWorkspaceEvent{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version}),
	}), entity.NextRuntimeOrchestrationTag)
}

func (s *Service) SendStopRuntime(ctx context.Context, opt ScheduleOption) error {
	log.V1.CtxInfo(ctx, "sending stop runtime event, session id: %s", opt.SessionID)
	return s.nextRuntimeOrchestratorMQClient.SendMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeStopWorkspaceEvent: lo.ToPtr(entity.NextRuntimeStopWorkspaceEvent{SessionID: opt.SessionID, Agent: opt.Agent, Version: opt.Version, Immediately: opt.Immediate, EventTime: lo.ToPtr(time.Now())}),
	}), entity.NextRuntimeOrchestrationTag)
}

func (s *Service) SendSuspendDebugRuntime(ctx context.Context, runID string) error {
	return s.nextRuntimeOrchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		NextRuntimeSuspendDebugEvent: lo.ToPtr(entity.NextRuntimeSuspendDebugEvent{RunID: runID}),
	}), 30*time.Minute, entity.NextRuntimeOrchestrationTag) // suspend after 30 minutes for debug
}

func (s *Service) InjectCtxDestInfo(ctx context.Context, uri, psm string) context.Context {
	ctx = context.WithValue(ctx, trace.CtxKeyToAddr, uri)
	ctx = context.WithValue(ctx, trace.CtxKeyToService, lo.Ternary(psm != "", psm, "flow.agentsphere.runtime"))
	return ctx
}

func (s *Service) GetDestPSM(ctx context.Context, run *entity.AgentRun) string {
	var psm string
	if len(run.AgentMetadata.Agents) != 0 {
		metadata := util.First(run.AgentMetadata.Agents) // ignore_security_alert SQL_INJECTION
		oldAgentConfig := s.GetAgentConfig(metadata.Agent, metadata.Version)
		if oldAgentConfig != nil {
			psm = oldAgentConfig.RuntimeConfig.PSM
		}
	} else {
		newAgentConfig, err := s.getNextAgentConfig(ctx, run.SessionID)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get next agent config: %v", err)
		} else {
			psm = newAgentConfig.RuntimeConfig.PSM
		}
	}
	return psm
}

func (s *Service) abortAndWaitStop(ctx context.Context, sessionID string) error {
	isRunning := true
	err := s.NextAbortAgent(ctx, NextAbortAgent{SessionID: sessionID})
	if err != nil {
		log.V1.CtxError(ctx, errors.WithMessage(err, "failed to send stop workspace msg").Error())
	} else {
		// 30s 后再检查, 如果检查过程中服务重启了，rmq 层面会重新发送事件重新消费
		startCheckTime := time.Now()
		time.Sleep(30 * time.Second)
		for {
			session, err := s.nextserverDao.GetSessionWithDeleted(ctx, nextdal.GetSessionOption{ID: sessionID, Sync: true})
			if err != nil {
				return errors.WithMessage(err, "failed to get session")
			}
			// 如果 session 的状依然是 running 状态, 并且没有检查超过 5min 则继续检查
			now := time.Now()
			if !session.Status.IsStopped() && now.Sub(startCheckTime).Minutes() <= 5 {
				log.V1.CtxInfo(ctx, "sleep for next abort status check, session_id: %s", sessionID)
				time.Sleep(30 * time.Second)
				continue
			}
			if session.Status.IsStopped() {
				isRunning = false
			}
			log.V1.CtxInfo(ctx, "success abort and wait agent complete, session_id: %s", sessionID)
			break
		}
	}
	// 如果 session 状态没有变化，强制更新为 stopped
	if isRunning {
		_, err := s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
			ID:     sessionID,
			Status: lo.ToPtr(nextentity.SessionStatusStopped),
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to update session in stop progress: %v", err)
		}
	}
	return nil
}

const (
	SessionRestartFormat         = "next_session_restart:%s"
	SessionRestartFromTypeFormat = "next_session_restart_from_type:%s"
)

func (s *Service) checkSessionRestartMark(ctx context.Context, sessionID string) bool {
	key := fmt.Sprintf(SessionRestartFormat, sessionID)
	result, err := s.redis.Exists(ctx, key)
	if err != nil {
		log.V1.CtxError(ctx, "failed to check session restart mark: %v", err)
		return false
	}
	return lo.Ternary(result == 1, true, false)
}

func (s *Service) setSessionRestartMark(ctx context.Context, sessionID string) error {
	// 更新一个临时标记，时间为两分钟，两分钟内出现重启事件，标记为正常重启，否则是异常重启
	key := fmt.Sprintf(SessionRestartFormat, sessionID)
	// 固定设置两分钟
	return s.redis.Set(ctx, key, 1, 2*time.Minute)
}

func (s *Service) setSessionRestartFromType(ctx context.Context, sessionID string, fromType FromType) error {
	// 更新一个临时标记，标记restart fromType
	key := fmt.Sprintf(SessionRestartFromTypeFormat, sessionID)
	// 固定设置两分钟
	return s.redis.Set(ctx, key, string(fromType), 2*time.Minute)
}

func (s *Service) getSessionRestartFromType(ctx context.Context, sessionID string) (FromType, error) {
	// 获取一个临时标记，标记restart fromType
	key := fmt.Sprintf(SessionRestartFromTypeFormat, sessionID)
	result, err := s.redis.Get(ctx, key)
	if err != nil && !redis.IsNil(err) {
		return "", errors.WithMessage(err, "failed to get session restart from type")
	}
	return FromType(result), nil
}

func (s *Service) CheckCubeIsStopped(ctx context.Context, sessionID string) (bool, error) {
	run, err := s.GetAgentRun(ctx, GetAgentRunOption{
		SessionID: sessionID,
	})
	if err != nil {
		return false, errors.WithMessage(err, "failed to get agent run")
	}

	provider := s.runtimeProviderRegistry.GetProvider(run.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		return false, errors.New("runtime provider not found")
	}

	status, err := provider.GetContainerStatus(ctx, run.RuntimeMetadata.TenantKey, run.RuntimeMetadata.ContainerID)
	if err != nil {
		return false, errors.WithMessage(err, "failed to get container status")
	}
	log.V1.CtxInfo(ctx, "[CheckCubeIsStopped] container status is %s, session id %s", status, sessionID)
	return status == runtimedal.ContainerStatusStopped, nil
}

type DeleteSessionFaaSDeploymentsOption struct {
	SessionID string
}

// DeleteSessionFaaSDeployments deletes all FaaS deployments for artifacts under a session.
// This function:
// 1. Lists all deployments under the given session
// 2. Filters for backend deployments with FaaS IDs in metadata
// 3. Deletes the FaaS cluster first, then the service
// 4. Handles errors gracefully and continues with other deployments if one fails
func (s *Service) DeleteSessionFaaSDeployments(ctx context.Context, opt DeleteSessionFaaSDeploymentsOption) error {
	log.V1.CtxInfo(ctx, "start deleting FaaS deployments for session %s", opt.SessionID)

	// 1. List all deployments under this session
	deployments, err := s.nextserverDao.ListDeploymentsBySessionID(ctx, nextdal.ListDeploymentsBySessionIDOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list deployments for session")
	}

	if len(deployments) == 0 {
		log.V1.CtxInfo(ctx, "no deployments found for session %s", opt.SessionID)
		return nil
	}

	log.V1.CtxInfo(ctx, "found %d deployments for session %s", len(deployments), opt.SessionID)

	// 2. For each deployment, find backend deployments with FaaS ID and delete them
	var deleteErrors []error
	for _, deployment := range deployments {
		// Only process backend deployments with FaaS ID
		if deployment.Category != nextentity.DeploymentCategoryBackend {
			continue
		}

		if deployment.Metadata == nil || deployment.Metadata.FaasID == "" {
			continue
		}

		log.V1.CtxInfo(ctx, "deleting FaaS deployment %s with FaaS ID %s", deployment.ID, deployment.Metadata.FaasID)

		// 2.1 Delete cluster first
		clusterErr := s.deleteFaaSCluster(ctx, deployment.Metadata.FaasID)
		if clusterErr != nil {
			log.V1.CtxError(ctx, "failed to delete FaaS cluster for deployment %s: %v", deployment.ID, clusterErr)
		}

		// 2.2 Delete service (even if cluster deletion failed)
		serviceErr := s.deleteFaaSService(ctx, deployment.Metadata.FaasID)
		if serviceErr != nil {
			log.V1.CtxError(ctx, "failed to delete FaaS service for deployment %s: %v", deployment.ID, serviceErr)
		}

		// Record errors if either operation failed
		if clusterErr != nil {
			deleteErrors = append(deleteErrors, errors.WithMessage(clusterErr, "failed to delete cluster for deployment "+deployment.ID))
		}
		if serviceErr != nil {
			deleteErrors = append(deleteErrors, errors.WithMessage(serviceErr, "failed to delete service for deployment "+deployment.ID))
		}

		// Log success if both operations succeeded
		if clusterErr == nil && serviceErr == nil {
			log.V1.CtxInfo(ctx, "successfully deleted FaaS deployment %s with FaaS ID %s", deployment.ID, deployment.Metadata.FaasID)
		} else {
			log.V1.CtxWarn(ctx, "partially deleted FaaS deployment %s with FaaS ID %s (cluster: %v, service: %v)",
				deployment.ID, deployment.Metadata.FaasID, clusterErr, serviceErr)
		}
	}

	if len(deleteErrors) > 0 {
		return errors.Errorf("failed to delete some FaaS deployments: %v", deleteErrors)
	}

	log.V1.CtxInfo(ctx, "successfully deleted all FaaS deployments for session %s", opt.SessionID)
	return nil
}

// DeleteSessionFaaSDeploymentsByID is a convenience function that takes a session ID string
func (s *Service) DeleteSessionFaaSDeploymentsByID(ctx context.Context, sessionID string) error {
	return s.DeleteSessionFaaSDeployments(ctx, DeleteSessionFaaSDeploymentsOption{
		SessionID: sessionID,
	})
}

// deleteFaaSCluster deletes a FaaS cluster
func (s *Service) deleteFaaSCluster(ctx context.Context, serviceID string) error {
	config := s.tccConf.NextAgentDeploymentConfig.GetValue()
	region := config.FaaSRegion
	cluster := config.FaaSCluster

	log.V1.CtxInfo(ctx, "deleting FaaS cluster for service %s", serviceID)

	_, err := s.faasClient.DeleteCluster(ctx, serviceID, region, cluster, false, "session cleanup")
	if err != nil {
		// Check if the error is due to cluster not found (already deleted)
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "404") {
			log.V1.CtxInfo(ctx, "FaaS cluster for service %s already deleted or not found", serviceID)
			return nil
		}
		return errors.WithMessage(err, "failed to delete FaaS cluster")
	}

	log.V1.CtxInfo(ctx, "successfully deleted FaaS cluster for service %s", serviceID)
	return nil
}

// deleteFaaSService deletes a FaaS service
func (s *Service) deleteFaaSService(ctx context.Context, serviceID string) error {
	log.V1.CtxInfo(ctx, "deleting FaaS service %s", serviceID)

	_, err := s.faasClient.DeleteService(ctx, serviceID, false, "session cleanup")
	if err != nil {
		return errors.WithMessage(err, "failed to delete FaaS service")
	}

	log.V1.CtxInfo(ctx, "successfully deleted FaaS service %s", serviceID)
	return nil
}
