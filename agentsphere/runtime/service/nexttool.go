package runtimeservice

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cenkalti/backoff/v4"
	"github.com/samber/lo"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/knowledge_graph"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace/reader"
	agententity "code.byted.org/devgpt/kiwis/agentsphere/agents/entity"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	artifactservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/mapstructure"
)

const (
	// agent 中的 tool 名称
	toolNameExecuteCommand            = "execute_command"
	toolNameBash                      = "bash"
	toolNameWebSearch                 = "web_search"
	toolNameQuickSearch               = "quick_search"
	toolNameQuickResearch             = "quick_research"
	toolNameCreateFile                = "create_file"
	toolNameWriteFile                 = "write_file"
	toolNameReadFile                  = "read_file"
	toolNameRead                      = "read"
	toolNamePatchFile                 = "patch_file"
	toolNameAppendFile                = "append_file"
	toolNameSearchReplaceFile         = "search_replace_file"
	toolNameReadMarkdownFile          = "read_markdown_files"
	toolNameExcelPreview              = "excel_preview"
	toolNameGeneralDataFilePreview    = "general_data_file_preview"
	toolNameDeploy                    = "deploy"
	toolNameDeployBackend             = "deploy_backend"
	toolNameDeployFrontend            = "deploy_frontend"
	toolNameGitClone                  = "git_clone" // 后面可能会用 execute_command 实现
	toolNameHtmlVision                = "html_vision"
	toolNameBrowserGoto               = "browser_goto"
	toolNameBrowserQuickExtraction    = "browser_goto_and_extraction"
	toolNameBrowserMarkdownifyContent = "browser_markdownify_content"
	toolNameGrepSearch                = "grep_search"
	toolNameGlobSearch                = "glob_search"

	toolNameBrowserGetLibraScreenshot   = "browser_get_libra_screenshot_and_data"
	toolNameBrowserGetAeolusScreenshot  = "browser_get_aeolus_screenshot_and_data"
	toolNameBrowserGetMeegoScreenshot   = "browser_get_meego_screenshot_and_data"
	toolNameBrowserExecuteScript        = "browser_execute_script"
	toolNameBrowserClick                = "browser_click"
	toolNameBrowserHover                = "browser_hover"
	toolNameBrowserInput                = "browser_input"
	toolNameBrowserSendKeys             = "browser_send_keys"
	toolNameBrowserSelectDropdownOption = "browser_select_dropdown_option"
	toolNameBrowserScrollDown           = "browser_scroll_down"
	toolNameBrowserScrollUp             = "browser_scroll_up"
	toolNameBrowserGotoPDF              = "browser_goto_pdf"
	toolNameBrowserWait                 = "browser_wait"
	toolNameQrcodeLogin                 = "browser_qrcode_login"
	toolNameBrowserCheckLogin           = "browser_check_login"
	toolNameBrowserAskDoubao            = "browser_ask_doubao_and_fetch_answer"
	toolNameBrowserVlm                  = "browser_handover_to_vlm"
	toolNameMcpCall                     = "mcp_call"
	toolNameSubmitMR                    = "submit_merge_request"
	toolMcpPrefix                       = "mcp:" // 动态 actor 的 mcp 工具都会使用 mcp server 上的原始名称，但会带上这个前缀
	toolMcpCreateLarkDoc                = "mcp:lark_create_lark_doc"
	toolMcpCreateLarkTable              = "mcp:lark_create_lark_table"
	toolDevOpsAgentToolPrefix           = "devops_agent:"
	// DevOps Agent specific tool names
	toolDevOpsAgentGetSCMName         = "devops_agent:get_scm_name"
	toolDevOpsAgentSCMBuild           = "devops_agent:scm_build"
	toolDevOpsAgentGetSCMBuildStatus  = "devops_agent:get_scm_build_status"
	toolDevOpsAgentGetSCMBuildLog     = "devops_agent:get_scm_build_log"
	toolDevOpsAgentTCEDeploy          = "devops_agent:tce_deploy"
	toolDevOpsAgentGetTCEDeployStatus = "devops_agent:get_tce_deploy_status"
	toolDevOpsAgentSCMToolConclusion  = "devops_agent:scm_tool_conclusion"
	toolDevOpsAgentTCEToolConclusion  = "devops_agent:tce_tool_conclusion"
	toolNameLarkDownload              = "lark_download"
	// KnowledgeGraph tool names
	toolNameKnowledgeGraphSearchServiceInfo = "knowledge_graph_search_service_info"
	toolNameKnowledgeGraphSearchRepo        = "knowledge_graph_search_repo"
)

const (
	// tool 类型，对应到前端
	toolTypeCodeEditor = "code_editor"
	toolTypeTextEditor = "text_editor"
	toolTypeTerminal   = "terminal"
	toolTypeSearchList = "search_list"
	toolTypeBrowser    = "browser"
)

type toolCallDescType string

var (
	toolCallDescTypeExecutingCommand             = "ExecutingCommand"
	toolCallDescTypeCreatingDocument             = "CreatingDocument"
	toolCallDescTypeEditingDocument              = "EditingDocument"
	toolCallDescTypeCreatingCodeFile             = "CreatingCodeFile"
	toolCallDescTypeEditingCodeFile              = "EditingCodeFile"
	toolCallDescTypeDeployingWebpage             = "DeployingWebpage"
	toolCallDescTypeDeployingBackendService      = "DeployingBackendService"
	toolCallDescTypeOpeningWebpage               = "OpeningWebpage"
	toolCallDescTypeClickingOnWebpage            = "ClickingOnWebpage"
	toolCallDescTypeEnteringContent              = "EnteringContent"
	toolCallDescTypeHoveringOverElement          = "HoveringOverElement"
	toolCallDescTypeScrollingDownWebpage         = "ScrollingDownWebpage"
	toolCallDescTypeScrollingUpWebpage           = "ScrollingUpWebpage"
	toolCallDescTypeBrowsingWebpageContent       = "BrowsingWebpageContent"
	toolCallDescTypeWaitingForUserLogin          = "WaitingForUserLogin"
	toolCallDescTypeUserTimedOutWithoutLoggingIn = "UserTimedOutWithoutLoggingIn"
	toolCallDescTypeScanCodeSuccessfulDetected   = "ScanCodeSuccessfulDetected"
	toolCallDescTypeBrowsingWebpage              = "BrowsingWebpage"
	toolCallDescTypeSearching                    = "Searching"
)

func genToolCallDescription(i18nConfig map[string]string, terminal *nextidl.Terminal, textEditor *nextidl.TextEditor,
	codeEditor *nextidl.CodeEditor, browser *nextidl.Browser, searchList *nextidl.SearchList, toolName string) string {
	if terminal != nil {
		return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeExecutingCommand], terminal.Command)
	}
	if textEditor != nil {
		if textEditor.CreateFile != nil {
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeCreatingDocument], textEditor.CreateFile.FilePath)
		}
		if textEditor.PatchFile != nil {
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeEditingDocument], textEditor.PatchFile.FilePath)
		}
		if textEditor.AppendFile != nil {
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeEditingDocument], textEditor.AppendFile.FilePath)
		}
	}
	if codeEditor != nil {
		if codeEditor.CreateFile != nil {
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeCreatingCodeFile], codeEditor.CreateFile.FilePath)
		}
		if codeEditor.PatchFile != nil {
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeEditingCodeFile], codeEditor.PatchFile.FilePath)
		}
		if codeEditor.AppendFile != nil {
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeEditingCodeFile], codeEditor.AppendFile.FilePath)
		}
	}
	if browser != nil {
		if browser.ContentType == nextidl.BrowserContentType_BrowserTypeDeploy {
			if toolName == toolNameDeploy || toolName == toolNameDeployFrontend {
				return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeDeployingWebpage], browser.URL)
			}
			if toolName == toolNameDeployBackend {
				return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeDeployingBackendService], browser.URL)
			}
		}
		switch toolName {
		case toolNameBrowserGoto:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeOpeningWebpage], browser.URL)
		case toolNameBrowserClick:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeClickingOnWebpage], browser.URL)
		case toolNameBrowserInput, toolNameBrowserSendKeys, toolNameBrowserSelectDropdownOption:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeEnteringContent], browser.URL)
		case toolNameBrowserHover:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeHoveringOverElement], browser.URL)
		case toolNameBrowserScrollDown:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeScrollingDownWebpage], browser.URL)
		case toolNameBrowserScrollUp:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeScrollingUpWebpage], browser.URL)
		case toolNameBrowserQuickExtraction, toolNameBrowserMarkdownifyContent:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeBrowsingWebpageContent], browser.URL)
		case toolNameQrcodeLogin:
			return i18nConfig[toolCallDescTypeWaitingForUserLogin]
		case toolNameBrowserCheckLogin:
			if browser.LoginInfo != nil && browser.LoginInfo.LoginStatus == nextidl.LoginStatus_LoginStatusFailed {
				return i18nConfig[toolCallDescTypeUserTimedOutWithoutLoggingIn]
			}
			return i18nConfig[toolCallDescTypeScanCodeSuccessfulDetected]
		default:
			return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeBrowsingWebpage], browser.URL)
		}
	}
	if searchList != nil {
		return fmt.Sprintf("%s: %s", i18nConfig[toolCallDescTypeSearching], searchList.Query)
	}

	return ""
}

type genToolCallSummaryOption struct {
	ToolCallEvent *entity.NextToolCall
	SessionID     string
	Locale        string
}

func (s *Service) genToolCallSummary(ctx context.Context, opt genToolCallSummaryOption) string {
	toolCallEvent, sessionID, locale := opt.ToolCallEvent, opt.SessionID, opt.Locale
	if toolCallEvent == nil {
		return ""
	}

	// Prepare tool inputs as JSON string for better readability
	inputsJSON, _ := json.Marshal(toolCallEvent.Inputs)
	inputPart1, inputPart2, squeezed := prompt.SqueezeToken(string(inputsJSON), 2048)

	// Prepare the user message content
	userContent := fmt.Sprintf(`
工具名称: %s
工具输入: %s%s%s
执行状态: %s
工具描述：%s

请描述工具调用操作，不要包含工具名称，不超过20个字。
`, toolCallEvent.Tool, inputPart1, lo.Ternary(squeezed, "...[truncated]...", ""), inputPart2,
		lo.Ternary(toolCallEvent.Error != "", fmt.Sprintf("出错: %s", toolCallEvent.Error), "成功"),
		toolCallEvent.Description)

	res, err := s.llmService.ChatCompletion(ctx, llm.ChatCompletionRequest{
		Model:       "doubao-1.5-lite-32k-250115",
		MaxTokens:   lo.ToPtr(1024),
		Temperature: lo.ToPtr(float32(0.4)),
		Tag:         "tool_call_summary",
		SessionID:   sessionID,
		Messages: []llm.ChatCompletionMessage{
			{
				Role: "system",
				Content: fmt.Sprintf(`用简洁的短语（不超过20个字）总结工具调用操作。
重点关注工具调用的最终目的或主要动作。
不要在输出中包含工具名称。

中文示例:
- 将 markdown 转换为 html 文件
- 检查域名是否有效
- 检查 /home/<USER>
- 克隆仓库 example/repo

英文示例:
- Convert markdown to html file
- Check if the domain name is valid
- Check the files in the /home/<USER>
- Clone repository example/repo

注意:
- 你必须使用回应语言为：%s
- 语言是 en 参考英文示例，zh 参考中文示例
- 只需要使用一种语言回复
- 尽可能提供具体信息（如文件名、URL、命令等）
- 如果执行出错，简要说明错误情况`, locale),
			},
			{
				Role:    "user",
				Content: userContent,
			},
		},
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to generate tool call summary: %v", err)
		return ""
	}

	result, err := res.Aggregation(ctx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to generate tool call summary: %v", err)
		return ""
	}

	if len(result.Choices) == 0 {
		log.V1.CtxError(ctx, "no choices returned from LLM for tool call summary")
		return ""
	}

	return result.Choices[0].Message.Content
}

func (s *Service) packToolEvent(ctx context.Context, event entity.SessionDataStreamEvent, isOffline bool, sessionID, username string) *nextidl.UseToolEvent {
	if event.NextToolCall == nil {
		return nil
	}
	if event.NextToolCall.Status == entity.ToolCallStatusStarted {
		log.V1.CtxInfo(ctx, "skip to send tool call event, offset: %v", event.EventOffset)
		return nil // started 的 tool 事件不发送
	}
	planStepID, err := s.GetPlanStepIDByAgentStepID(ctx, event.NextToolCall.StepID)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get plan step id by agent step id: %v", err)
	}
	terminal, textEditor, codeEditor, browser, searchList, toolType := s.packToolEventContentAndType(ctx, event.NextToolCall, sessionID, username)
	if terminal == nil && textEditor == nil && codeEditor == nil && browser == nil && searchList == nil {
		log.V1.CtxWarn(ctx, "ignore invalid tool event, offset: %v", event.EventOffset)
		return nil
	}
	// 需要用户扫码登录时发送通知
	if isOffline && event.NextToolCall.Tool == toolNameQrcodeLogin {
		err = s.nextLarkService.SendQrcodeLoginNotification(ctx, sessionID, username, s.getSessionTitle(ctx, sessionID))
		if err != nil {
			log.V1.CtxError(ctx, "failed to send lark qrcode login notification: %v", err)
		}
	}
	latestMessage, err := s.nextserverDao.GetLatestMessageByRole(ctx, sessionID, nextentity.MessageRoleUser, false)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get latest message: %v", err)
	}
	var locale = "zh"
	if latestMessage != nil {
		locale = latestMessage.Options.Locale
	}
	var summary string
	// 在线场景从缓存中获取 summary，为了和离线场景保持一致
	if !isOffline {
		err = backoff.Retry(func() error {
			summary, err = s.redis.Get(ctx, getToolSummaryCacheKey(sessionID, event.NextToolCall.Tool, event.EventOffset))
			return err
		}, backoff.WithMaxRetries(backoff.NewConstantBackOff(500*time.Millisecond), 20)) // 等待轮询 10s
		if err != nil {
			log.V1.CtxError(ctx, "failed to get summary from cache: %v", err)
		}
	}
	if summary == "" {
		// 调用大模型实时生成 summary
		summary = s.genToolCallSummary(ctx, genToolCallSummaryOption{
			ToolCallEvent: event.NextToolCall,
			SessionID:     sessionID,
			Locale:        locale,
		})
		if summary == "" {
			summary = event.NextToolCall.Description
		}
		err = s.redis.Set(ctx, getToolSummaryCacheKey(sessionID, event.NextToolCall.Tool, event.EventOffset), summary, time.Hour*24)
		if err != nil {
			log.V1.CtxError(ctx, "failed to set summary to cache: %v", err)
		}
	}

	// default use zh config
	i18nConfig := s.nextToolI18nConfig.GetValue().ZH
	if locale == "en" {
		i18nConfig = s.nextToolI18nConfig.GetValue().EN
	}

	return &nextidl.UseToolEvent{
		EventID:     s.idGen.NewID(),
		StepID:      planStepID,
		PlanID:      "",
		AgentStepID: event.NextToolCall.StepID,
		ToolName:    toolType,
		Summary:     summary,
		Description: genToolCallDescription(i18nConfig, terminal, textEditor, codeEditor, browser, searchList, event.NextToolCall.Tool),
		Status:      string(event.NextToolCall.Status),
		Terminal:    terminal,
		TextEditor:  textEditor,
		CodeEditor:  codeEditor,
		Browser:     browser,
		SearchList:  searchList,
		Timestamp:   event.Timestamp.Unix(),
		EventOffset: event.EventOffset,
		SessionID:   sessionID,
		EventKey:    nextentity.GetUseToolEventKey(sessionID, event.NextToolCall.StepID, event.EventOffset),
	}
}

func getToolSummaryCacheKey(sessionID, toolName string, offset int64) string {
	return fmt.Sprintf("tool_call_summary:%s:%s:%d", sessionID, toolName, offset)
}

func (s *Service) packToolEventContentAndType(ctx context.Context, toolCallEvent *entity.NextToolCall, sessionID, username string) (
	terminal *nextidl.Terminal, textEditor *nextidl.TextEditor, codeEditor *nextidl.CodeEditor, browser *nextidl.Browser,
	searchList *nextidl.SearchList, toolType string) {
	if toolCallEvent == nil {
		return
	}
	if toolCallEvent.Error != "" {
		log.V1.CtxWarn(ctx, "session: %s, tool event error: %v", sessionID, toolCallEvent.Error)
		return
	}

	toolName := toolCallEvent.Tool

	switch toolName {
	case toolNameExecuteCommand, toolNameBash:
		terminal = &nextidl.Terminal{
			Command: lo.Ternary(conv.DefaultAny[string](toolCallEvent.Inputs["command"]) == "", conv.DefaultAny[string](toolCallEvent.Inputs["cmd"]), conv.DefaultAny[string](toolCallEvent.Inputs["command"])),
			Output:  []string{conv.DefaultAny[string](toolCallEvent.Outputs["output"])},
		}
		toolType = toolTypeTerminal
	case toolNameLarkDownload:
		// 解析输入参数
		var inputs lark.LarkToolArgs
		err := mapstructure.Decode(toolCallEvent.Inputs, &inputs)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal lark download inputs: %v", err)
			return
		}

		// 解析输出结果
		var output lark.DownloadLarkContentResult
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal lark download outputs: %v", err)
			return
		}

		// 构建命令和输出
		var outputLines []string
		if output.FilePath != "" {
			outputLines = []string{fmt.Sprintf("Downloaded: %s", output.FilePath)}
		} else if len(output.FilePaths) > 0 {
			outputLines = append(outputLines, fmt.Sprintf("Downloaded %d files:", len(output.FilePaths)))
			for _, filePath := range output.FilePaths {
				outputLines = append(outputLines, fmt.Sprintf("  - %s", filePath))
			}
		} else {
			outputLines = []string{"No files downloaded"}
		}

		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("lark_download %s", inputs.DocumentURL),
			Output:  outputLines,
		}
		toolType = toolTypeTerminal
	case toolNameWebSearch, toolNameQuickSearch, toolNameQuickResearch:
		type searchListItem struct {
			Title       string `mapstructure:"title"`
			URL         string `mapstructure:"url"`
			Description string `mapstructure:"description"`
			Icon        string `mapstructure:"icon"`
		}
		var results []searchListItem
		err := mapstructure.Decode(toolCallEvent.Outputs["results"], &results)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal results: %v", err)
		}
		searchList = &nextidl.SearchList{
			Query: conv.DefaultAny[string](toolCallEvent.Inputs["query"]),
			Results: lo.Map(results, func(item searchListItem, _ int) *nextidl.SearchListItem {
				return &nextidl.SearchListItem{Title: item.Title, URL: item.URL, Description: item.Description, Favicon: item.Icon}
			}),
		}
		toolType = toolTypeSearchList
	case toolNameReadFile:
		var output nextidl.FileLines
		err := mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal outputs: %v", err)
		}
		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("cat %s", conv.DefaultAny[string](toolCallEvent.Inputs["file_path"])),
			Output: lo.Map(output.Lines, func(item *nextidl.Line, _ int) string {
				return item.Content
			}),
		}
		toolType = toolTypeTerminal
	case toolNameRead:
		var output reader.ReadFileActionOutput
		err := mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal outputs: %v", err)
		}

		path := conv.DefaultAny[string](toolCallEvent.Inputs["file_path"])
		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("cat %s", path),
			Output:  strings.Split(output.RawContent, "\n"),
		}
		toolType = toolTypeTerminal

	case toolNameCreateFile, toolNameWriteFile, toolNameAppendFile:
		filePath := conv.DefaultAny[string](toolCallEvent.Inputs["file_path"])
		content := conv.DefaultAny[string](toolCallEvent.Inputs["content"])
		// write file 的文件内容在 outputs 中，agent 底层实现略有不同
		outputContent := conv.DefaultAny[string](toolCallEvent.Outputs["content"])
		if len(content) == 0 {
			content = outputContent
		}
		// 忽略 lark md 文件相关的事件，避免暴露实现细节，但对内部研发放开权限
		if nextentity.IsLarkMDFile(filePath) && !s.userService.IsDeveloper(&authentity.Account{Username: username}) {
			return
		}

		// TODO: get url and link source if it's a link.
		artifactType, subType := artifactservice.GetArtifactTypeAndSubType(filePath, []byte(content), "") // TODO: fill with linkSource.
		// 区分是文档还是代码类型
		if artifactType == nextentity.ArtifactTypeFile {
			textEditor = &nextidl.TextEditor{}
			if toolName == toolNameAppendFile {
				textEditor.AppendFile = &nextidl.AppendFile{
					FilePath: filePath,
					Content:  content,
					SubType:  subType,
					URL:      "",
				}
			} else {
				textEditor.CreateFile = &nextidl.CreateFile{
					FilePath: filePath,
					Content:  content,
					SubType:  subType,
					URL:      "",
				}
			}
			toolType = toolTypeTextEditor
		} else { // 代码类型
			codeEditor = &nextidl.CodeEditor{}
			if toolName == toolNameAppendFile {
				codeEditor.AppendFile = &nextidl.AppendFile{
					FilePath: filePath,
					Content:  content,
					SubType:  subType,
					URL:      "",
				}
			} else {
				codeEditor.CreateFile = &nextidl.CreateFile{
					FilePath: filePath,
					Content:  content,
					SubType:  subType,
					URL:      "",
				}
			}
			toolType = toolTypeCodeEditor
		}
	case toolNamePatchFile, toolNameSearchReplaceFile: // SearchReplaceFile 是 PatchFile 的新版本，输入输出格式基本一致
		type patchListItem struct {
			FilePath string `mapstructure:"file_path"`
			Patch    string `mapstructure:"patch"`
		}
		var results []patchListItem
		err := mapstructure.Decode(toolCallEvent.Outputs["succeed"], &results)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal results: %v", err)
		}
		filePath := conv.DefaultAny[string](toolCallEvent.Inputs["file_path"])
		diff := conv.DefaultAny[string](toolCallEvent.Inputs["diff"])
		// TODO: get url and link source if it's a link.
		artifactType, subType := artifactservice.GetArtifactTypeAndSubType(filePath, []byte(diff), "") // TODO: fill with linkSource.
		// 区分是文档还是代码类型
		if artifactType == nextentity.ArtifactTypeFile { // 文档类型
			textEditor = &nextidl.TextEditor{
				PatchFile: &nextidl.PatchFile{
					FilePath: filePath,
					Diff:     diff,
					PatchFileResult: lo.Map(results, func(item patchListItem, _ int) *nextidl.FilePatch {
						return &nextidl.FilePatch{
							FilePath: item.FilePath,
							Patch:    item.Patch,
						}
					}),
					SubType: subType,
					URL:     "",
				},
			}
			toolType = toolTypeTextEditor
		} else { // 代码类型
			codeEditor = &nextidl.CodeEditor{
				PatchFile: &nextidl.PatchFile{
					FilePath: filePath,
					Diff:     diff,
					PatchFileResult: lo.Map(results, func(item patchListItem, _ int) *nextidl.FilePatch {
						return &nextidl.FilePatch{
							FilePath: item.FilePath,
							Patch:    item.Patch,
						}
					}),
					SubType: subType,
					URL:     "",
				},
			}
			toolType = toolTypeCodeEditor
		}
	case toolNameReadMarkdownFile:
		var input workspace.ReadMarkdownFilesArgs
		var output workspace.MarkDownFiles
		err := mapstructure.Decode(toolCallEvent.Inputs, &input)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal read markdown files inputs: %v", err)
		}
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		files := input.Paths
		var lines []string
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal outputs: %v", err)
		} else {
			for _, file := range output.Files {
				lines = append(lines, "reading file: "+file.FileName)
				lines = append(lines, strings.Split(file.Content, "\n")...)
			}
		}

		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("cat %s", strings.Join(files, " && cat ")),
			Output:  lines,
		}
		toolType = toolTypeTerminal
	case toolNameExcelPreview, toolNameGeneralDataFilePreview:
		// 预览文件
		filePath := conv.DefaultAny[string](toolCallEvent.Inputs["file_path"])
		preview := conv.DefaultAny[map[string]any](toolCallEvent.Outputs["preview"])
		var previewLines []string
		if toolName == toolNameExcelPreview {
			excel := workspace.ExcelAnalysis{}
			if err := mapstructure.Decode(preview, &excel); err != nil {
				log.V1.CtxError(ctx, "failed to unmarshal excel: %v", err)
			} else {
				previewLines = strings.Split(excel.String(), "\n")
			}
		}
		if toolName == toolNameGeneralDataFilePreview {
			file := workspace.FileAnalysis{}
			if err := mapstructure.Decode(preview, &file); err != nil {
				log.V1.CtxError(ctx, "failed to unmarshal file: %v", err)
			} else {
				previewLines = strings.Split(file.String(), "\n")
			}
		}

		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("preview %s", filePath),
			Output:  previewLines,
		}
		toolType = toolTypeTerminal
	case toolNameBrowserGoto, toolNameBrowserClick, toolNameBrowserInput, toolNameBrowserSendKeys,
		toolNameBrowserSelectDropdownOption, toolNameBrowserGotoPDF, toolNameBrowserCheckLogin, toolNameQrcodeLogin,
		toolNameBrowserAskDoubao, toolNameBrowserVlm, toolNameBrowserWait, toolNameBrowserHover, toolNameBrowserScrollDown,
		toolNameBrowserScrollUp, toolNameBrowserGetLibraScreenshot, toolNameBrowserGetAeolusScreenshot, toolNameBrowserExecuteScript, toolNameBrowserQuickExtraction, toolNameBrowserGetMeegoScreenshot:
		var (
			u             = conv.DefaultAny[string](toolCallEvent.Inputs["url"])
			screenshotURL string
			output        browserUseOutput
		)
		err := mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal browser output: %v", err)
		}
		if output.Response.Error != "" || (output.Response.Success != nil && !*output.Response.Success) {
			log.V1.CtxInfo(ctx, "ignore error browser tool event: %s", toolCallEvent.Tool)
			return
		}
		if output.BrowserState.URL != "" {
			u = output.BrowserState.URL
		}

		if output.Screenshot.ArtifactID != "" {
			screenshotURL = s.getScreenshotURL(ctx, output.Screenshot.ArtifactID, output.Screenshot.Path)
		}
		var (
			loginInfo   *nextidl.LoginInfo
			contentType = nextidl.BrowserContentType_BrowserTypeScreenshot
		)
		switch toolCallEvent.Tool {
		case toolNameQrcodeLogin: // 需要扫码登录
			loginInfo = &nextidl.LoginInfo{
				LoginStatus:  nextidl.LoginStatus_LoginStatusWaiting,
				LoginTimeout: nil,
			}
			if output.LoginTimeout > 0 {
				loginInfo.LoginTimeout = lo.ToPtr(int64(output.LoginTimeout))
			}
			u = output.StreamURL
		case toolNameBrowserCheckLogin: // 扫码完成或失败
			loginInfo = &nextidl.LoginInfo{
				LoginStatus:  lo.Ternary(output.Response.LoginSuccess, nextidl.LoginStatus_LoginStatusSuccess, nextidl.LoginStatus_LoginStatusFailed),
				LoginTimeout: nil,
			}
		}
		if u == "" && screenshotURL == "" && loginInfo == nil {
			log.V1.CtxWarn(ctx, "ignore empty browser tool event: %s", toolCallEvent.Tool)
			return
		}
		if loginInfo != nil {
			contentType = nextidl.BrowserContentType_BrowserTypeLogin
		}
		browser = &nextidl.Browser{
			ScreenshotURL: screenshotURL,
			URL:           u,
			ContentType:   contentType,
			LoginInfo:     loginInfo,
		}
		toolType = toolTypeBrowser
	case toolNameHtmlVision:
		task := conv.DefaultAny[string](toolCallEvent.Inputs["task"])
		path := conv.DefaultAny[string](toolCallEvent.Outputs["path"])
		llmResponse := conv.DefaultAny[string](toolCallEvent.Outputs["llm_response"])
		artifactID := conv.DefaultAny[string](toolCallEvent.Outputs["artifact_id"])

		if artifactID == "" {
			// 没有 artifact id 说明没有截图，直接返回
			return
		}
		logs.V1.CtxInfo(ctx, "html vision task: %s, llm response: %s", task, llmResponse)
		var screenshotURL string
		artifact, err := s.nextserverDao.GetArtifact(ctx, nextdal.GetArtifactOption{
			ID:   artifactID,
			Sync: false,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get artifact for screenshot: %v", err)
			return
		}

		if artifact != nil {
			for _, file := range artifact.FileMetas {
				if file.Name == path {
					screenshotURL = file.ImageURL(s.imagexConfig.GetValue().DefaultDomain, s.imagexConfig.GetValue().DefaultTemplateID)
					break
				}
			}
		}

		if screenshotURL == "" {
			screenshotURL = conv.DefaultAny[string](toolCallEvent.Outputs["image_content"])
		}

		browser = &nextidl.Browser{
			URL:           path,
			ScreenshotURL: screenshotURL,
			ContentType:   nextidl.BrowserContentType_BrowserTypeScreenshot,
		}
		toolType = toolTypeBrowser
	case toolNameDeploy, toolNameDeployBackend, toolNameDeployFrontend:
		artifactID := conv.DefaultAny[string](toolCallEvent.Outputs["artifact_id"])
		url := conv.DefaultAny[string](toolCallEvent.Outputs["url"])
		browser = &nextidl.Browser{
			URL:         url,
			ContentType: nextidl.BrowserContentType_BrowserTypeDeploy,
		}
		deployment, err := s.nextDeploymentService.GetDeploymentByArtifactID(ctx, artifactID)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get deployment by artifact_id %s, err: %v", artifactID, err)
		} else if deployment != nil {
			log.V1.CtxInfo(ctx, "found deployment for artifact %s: %s", artifactID, deployment.URL)
			browser.DeployID = deployment.ID
		}
		toolType = toolTypeBrowser
	case toolNameGitClone:
		inputs := struct {
			Directory string `mapstructure:"directory"`
			Platform  string `mapstructure:"platform"`
			RepoName  string `mapstructure:"repo_name"`
		}{}
		err := mapstructure.Decode(toolCallEvent.Inputs, &inputs)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal git clone inputs: %v", err)
			return
		}

		outputs := struct {
			RepoName string `mapstructure:"repo_name"`
			Message  string `mapstructure:"message"`
		}{}
		err = mapstructure.Decode(toolCallEvent.Outputs, &outputs)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal git clone outputs: %v", err)
			return
		}

		repoURL := ""
		repoName := lo.Ternary(outputs.RepoName != "", outputs.RepoName, inputs.RepoName)
		switch inputs.Platform {
		case "github":
			repoURL = fmt.Sprintf("https://github.com/%s.git", repoName)
		case "codebase":
			repoURL = fmt.Sprintf("https://code.byted.org/%s.git", repoName)
		default:
			log.V1.CtxWarn(ctx, "unknown git platform: %s", inputs.Platform)
			repoURL = fmt.Sprintf("%s.git", repoName)
		}
		output := ""
		if toolCallEvent.Error != "" {
			output = fmt.Sprintf("failed due to error: %s", toolCallEvent.Error)
		} else {
			output = fmt.Sprintf("successfully cloned %s to %s", repoURL, inputs.Directory)
		}
		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("git clone %s %s",
				repoURL,
				inputs.Directory),
			Output: []string{output},
		}
		toolType = toolTypeTerminal
	case toolNameMcpCall:
		var inputs agententity.MCPCallArgs
		err := mapstructure.Decode(toolCallEvent.Inputs, &inputs)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal mcp call inputs: %v", err)
			return
		}
		var output agententity.MCPCallOutput
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal mcp call outputs: %v", err)
			return
		}
		var toolResult any
		if len(output.TextContents) > 0 {
			temp := make([]any, 0)
			for _, item := range output.TextContents {
				var unescapedJSONString string
				err = json.Unmarshal([]byte(item), &unescapedJSONString)
				if err != nil {
					unescapedJSONString = item
				}
				var jsonObj json.RawMessage
				if err = json.Unmarshal([]byte(unescapedJSONString), &jsonObj); err == nil {
					temp = append(temp, jsonObj)
				} else {
					temp = append(temp, item)
				}
			}
			toolResult = temp
		} else if toolCallEvent.Error != "" {
			toolResult = toolCallEvent.Error
		}

		content := make(map[string]any)
		toolArgs := make(map[string]any)
		if inputs.Arguments != "" {
			err = json.Unmarshal([]byte(inputs.Arguments), &toolArgs)
			if err != nil {
				log.V1.CtxError(ctx, "failed to unmarshal mcp call arguments: %v", err)
				return
			}
		}
		content["执行参数"] = toolArgs
		content["执行结果"] = toolResult
		if len(output.Attachments) > 0 {
			content["附件"] = output.Attachments
		}
		jsonData, err := json.MarshalIndent(content, "", "  ")
		if err != nil {
			log.V1.CtxError(ctx, "failed to marshal mcp call content: %v", err)
		}

		textEditor = &nextidl.TextEditor{
			CreateFile: &nextidl.CreateFile{
				FilePath: fmt.Sprintf("tool_%s_%s.md", inputs.Name, toolCallEvent.StepID),
				Content:  fmt.Sprintf("```json\n%s\n```", string(jsonData)),
			},
		}
		toolType = toolTypeTextEditor
	case toolNameSubmitMR:
		type submitMRArgs struct {
			Description  string `mapstructure:"description"`
			RepoName     string `mapstructure:"repo_name"`
			SourceBranch string `mapstructure:"source_branch"`
			TargetBranch string `mapstructure:"target_branch"`
			Title        string `mapstructure:"title"`
		}
		type submitMROutput struct {
			MergeRequestURL string `mapstructure:"merge_request_url"`
		}
		var inputs submitMRArgs
		err := mapstructure.Decode(toolCallEvent.Inputs, &inputs)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal mcp call inputs: %v", err)
			return
		}
		var output submitMROutput
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal mcp call outputs: %v", err)
			return
		}
		terminal = &nextidl.Terminal{
			Command: "codebase mr create",
			Output: []string{
				fmt.Sprintf("Created Merge Request(%s -> %s): %s", inputs.SourceBranch, inputs.TargetBranch, output.MergeRequestURL),
				inputs.Title,
				inputs.Description,
			},
		}
		toolType = toolTypeTerminal
	case toolMcpCreateLarkDoc:
		type createLarkDocOutput struct {
			DocumentURL string `mapstructure:"document_url"`
			Title       string `mapstructure:"title"`
		}
		var output createLarkDocOutput
		err := mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal mcp call outputs: %v", err)
			return
		}
		textEditor = &nextidl.TextEditor{
			CreateFile: &nextidl.CreateFile{
				FilePath: output.Title,
				SubType:  nextidl.ArtifactLinkSubTypeLarkDoc,
				URL:      output.DocumentURL,
			},
		}
		toolType = toolTypeTextEditor
	case toolMcpCreateLarkTable:
		type createLarkTableArgs struct {
			Title string `mapstructure:"title"`
		}
		type createLarkTableOutput struct {
			URL string `mapstructure:"url"`
		}
		var inputs createLarkTableArgs
		err := mapstructure.Decode(toolCallEvent.Inputs, &inputs)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal mcp call inputs: %v", err)
			return
		}
		var output createLarkTableOutput
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal mcp call outputs: %v", err)
			return
		}
		textEditor = &nextidl.TextEditor{
			CreateFile: &nextidl.CreateFile{
				FilePath: inputs.Title,
				SubType:  nextidl.ArtifactLinkSubTypeLarkSheet,
				URL:      output.URL,
			},
		}
		toolType = toolTypeTextEditor
	// DevOps Agent tools
	case toolDevOpsAgentGetSCMName, toolDevOpsAgentGetSCMBuildStatus, toolDevOpsAgentGetTCEDeployStatus, toolDevOpsAgentSCMToolConclusion, toolDevOpsAgentTCEToolConclusion, toolDevOpsAgentSCMBuild, toolDevOpsAgentTCEDeploy:
		content := make(map[string]any)
		if toolCallEvent.Outputs != nil {
			content = toolCallEvent.Outputs
		}

		contentBytes, err := marshalWithoutHTMLEscape(content)
		if err != nil {
			contentBytes = []byte("{}")
		}
		textEditor = &nextidl.TextEditor{
			CreateFile: &nextidl.CreateFile{
				FilePath: fmt.Sprintf("tool_%s_%s.md", toolCallEvent.Tool, toolCallEvent.StepID),
				Content:  fmt.Sprintf("```json\n%s\n```", string(contentBytes)),
				SubType:  "md",
			},
		}
		toolType = toolTypeTextEditor
	case toolDevOpsAgentGetSCMBuildLog:
		// Build log tools show raw log content in terminal
		var logContent string
		if toolCallEvent.Outputs != nil {
			if extractedErrors, ok := toolCallEvent.Outputs["extracted_errors"].(string); ok && extractedErrors != "" {
				logContent = extractedErrors
			} else if buildLog, ok := toolCallEvent.Outputs["build_log"].(string); ok && buildLog != "" {
				logContent = buildLog
			} else {
				logContent = "No build log available"
			}
		} else {
			logContent = "No outputs received"
		}
		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("# %s", toolName),
			Output:  []string{logContent},
		}
		toolType = toolTypeTerminal
	case toolNameKnowledgeGraphSearchServiceInfo, toolNameKnowledgeGraphSearchRepo:
		// 解析输入参数
		var inputs knowledge_graph.KnowledgeGraphToolArgs
		err := mapstructure.Decode(toolCallEvent.Inputs, &inputs)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal knowledge_graph_tool_args: %v", err)
			return
		}

		// 解析输出结果
		var output knowledge_graph.KnowledgeGraphToolResp
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal knowledge_graph_tool outputs: %v", err)
			return
		}

		outputLines := []string{
			fmt.Sprintf("\n%s retrieved nothing.", toolName),
		}
		// knowledge_graph_search_service_info 返回的内容只显示召回内容的大小给用户看
		if toolName == toolNameKnowledgeGraphSearchServiceInfo {
			if output.Result != "null" {
				outputLines = []string{
					fmt.Sprintf("\n%s retrieved totally %d(bytes) of content.", toolName, len(output.Result)),
				}
			}
			// knowledge_graph_search_repo 返回的内容只返回":"前的第一句话：Found 0 relevant code snippets
		} else if toolName == toolNameKnowledgeGraphSearchRepo {
			rslts := strings.Split(output.Result, ":")
			if len(rslts) > 0 {
				outputLines = []string{
					fmt.Sprintf("\n%s %s", toolName, rslts[0]),
				}
			}
		}

		// 构建命令和输出
		terminal = &nextidl.Terminal{
			Command: fmt.Sprintf("%s %s", toolName, inputs.ToString()),
			Output:  outputLines,
		}
		toolType = toolTypeTerminal
	case toolNameGrepSearch:
		var args workspace.GrepSearchRangeArgs
		err := mapstructure.Decode(toolCallEvent.Inputs, &args)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal grep search inputs: %v", err)
			return
		}
		var output workspace.GrepSearchOutput
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal grep search outputs: %v", err)
			return
		}
		var command string
		if args.Path != "" {
			command = fmt.Sprintf("search pattern \"%s\" in %s", args.Pattern, args.Path)
		} else {
			command = fmt.Sprintf("search pattern \"%s\"", args.Pattern)
		}

		terminal = &nextidl.Terminal{
			Command: command,
			Output:  []string{output.Matches},
		}
		toolType = toolTypeTerminal
	case toolNameGlobSearch:
		var args workspace.GlobSearchArgs
		err := mapstructure.Decode(toolCallEvent.Inputs, &args)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal glob search inputs: %v", err)
			return
		}
		var output workspace.GlobSearchOutput
		err = mapstructure.Decode(toolCallEvent.Outputs, &output)
		if err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal glob search outputs: %v", err)
			return
		}
		var command string
		if args.Path != "" {
			command = fmt.Sprintf("search pattern \"%s\" in %s", args.Pattern, args.Path)
		} else {
			command = fmt.Sprintf("search pattern \"%s\"", args.Pattern)
		}

		outputs := []string{fmt.Sprintf("Found %d files (hidden: %d)", output.TotalFiles, output.HiddenFiles)}
		for _, file := range output.Files {
			outputs = append(outputs, file.Path)
		}
		terminal = &nextidl.Terminal{
			Command: command,
			Output:  outputs,
		}
		toolType = toolTypeTerminal
	default:
		// 所有剩下的 mcp tool，先与 V1 对齐
		// 搜图工具复用mcp tool逻辑
		if strings.HasPrefix(toolName, toolMcpPrefix) && len(toolType) == 0 {
			content := make(map[string]any)
			var output agententity.MCPCallOutput
			err := mapstructure.Decode(toolCallEvent.Outputs, &output)
			if err != nil {
				log.V1.CtxError(ctx, "failed to unmarshal mcp call outputs: %v", err)
				return
			}
			var toolResult any
			if len(output.TextContents) > 0 {
				temp := make([]any, 0)
				for _, item := range output.TextContents {
					var unescapedJSONString string
					err = json.Unmarshal([]byte(item), &unescapedJSONString)
					if err != nil {
						unescapedJSONString = item
					}
					var jsonObj json.RawMessage
					if err = json.Unmarshal([]byte(unescapedJSONString), &jsonObj); err == nil {
						temp = append(temp, jsonObj)
					} else {
						temp = append(temp, item)
					}
				}
				toolResult = temp
			} else if toolCallEvent.Error != "" {
				toolResult = toolCallEvent.Error
			} else {
				toolResult = toolCallEvent.Outputs
			}

			content["执行参数"] = toolCallEvent.Inputs
			content["执行结果"] = toolResult
			if len(output.Attachments) > 0 {
				content["附件"] = output.Attachments
			}
			jsonData, err := json.MarshalIndent(content, "", "  ")
			if err != nil {
				log.V1.CtxError(ctx, "failed to marshal mcp call content: %v", err)
			}

			// TODO: get url and file type.
			textEditor = &nextidl.TextEditor{
				CreateFile: &nextidl.CreateFile{
					FilePath: fmt.Sprintf("tool_%s_%s.md", toolCallEvent.Tool, toolCallEvent.StepID),
					Content:  fmt.Sprintf("```json\n%s\n```", string(jsonData)),
					SubType:  "md",
					URL:      "",
				},
			}
			toolType = toolTypeTextEditor
		}
	}
	return
}

func (s *Service) getScreenshotURL(ctx context.Context, artifactID, path string) string {
	artifact, err := s.nextserverDao.GetArtifact(ctx, nextdal.GetArtifactOption{
		ID:   artifactID,
		Sync: true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get artifact for screenshot: %v", err)
		return ""
	}
	var screenshotURL string
	if artifact != nil {
		for _, file := range artifact.FileMetas {
			if file.Name == path {
				screenshotURL = file.ImageURL(s.imagexConfig.GetValue().DefaultDomain, s.imagexConfig.GetValue().DefaultTemplateID)
				break
			}
		}
	}
	return screenshotURL
}

type browserUseOutput struct {
	BrowserState struct {
		Title string `mapstructure:"title"`
		URL   string `mapstructure:"url"`
	} `mapstructure:"browser_state"`
	Response struct {
		Error        string `mapstructure:"error"`
		LoginSuccess bool   `mapstructure:"login_success"`
		Message      string `mapstructure:"message"`
		Success      *bool  `mapstructure:"success"`
	} `mapstructure:"response"`
	Screenshot   nextidl.Screenshot `mapstructure:"screenshot_without_highlights"`
	StreamURL    string             `mapstructure:"stream_url"`
	LoginTimeout int                `mapstructure:"login_timeout"`
}

// marshalWithoutHTMLEscape marshals content to JSON without HTML escaping
func marshalWithoutHTMLEscape(content map[string]any) ([]byte, error) {
	var buffer bytes.Buffer
	encoder := json.NewEncoder(&buffer)
	encoder.SetEscapeHTML(false)
	encoder.SetIndent("", "  ")
	err := encoder.Encode(content)
	if err != nil {
		return nil, err
	}

	// Remove trailing newline that Encode adds
	result := buffer.Bytes()
	if len(result) > 0 && result[len(result)-1] == '\n' {
		result = result[:len(result)-1]
	}
	return result, nil
}
