package runtimeservice

import (
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	"github.com/stretchr/testify/assert"
)

func TestArtifactsToJSONWithMultipleAttachments(t *testing.T) {
	serverMessage := struct {
		Attachments []*nextentity.Attachment
	}{
		Attachments: []*nextentity.Attachment{
			{FileName: "file1.txt", URL: "http://example.com/file1"},
			{FileName: "image.png", URL: "http://example.com/image"},
			{FileName: "doc.pdf", URL: "http://example.com/doc.pdf"},
		},
	}

	artifacts := serverMessage.Attachments
	var parts []string
	for _, attachment := range artifacts {
		if attachment.URL != "" {
			parts = append(parts, fmt.Sprintf("- [%s](%s)", attachment.FileName, attachment.URL))
		}
	}
	textContent := strings.Join(parts, "\n")
	// 直接用匿名结构体
	final := struct {
		ZhCN struct {
			Content [][]struct {
				Tag  string `json:"tag"`
				Text string `json:"text"`
			} `json:"content"`
		} `json:"zh_cn"`
	}{}

	// 填充数据
	final.ZhCN.Content = [][]struct {
		Tag  string `json:"tag"`
		Text string `json:"text"`
	}{
		{
			{
				Tag:  "md",
				Text: textContent,
			},
		},
	}

	jsonBytes, err := json.Marshal(final)

	assert.NoError(t, err)
	expectedText := "- [file1.txt](http://example.com/file1)\n- [image.png](http://example.com/image)\n- [doc.pdf](http://example.com/doc.pdf)"
	// 修复：expectedJSON 应该与 final 结构体匹配
	expectedFinal := struct {
		ZhCN struct {
			Content [][]struct {
				Tag  string `json:"tag"`
				Text string `json:"text"`
			} `json:"content"`
		} `json:"zh_cn"`
	}{}
	expectedFinal.ZhCN.Content = [][]struct {
		Tag  string `json:"tag"`
		Text string `json:"text"`
	}{
		{
			{
				Tag:  "md",
				Text: expectedText,
			},
		},
	}
	expectedJSON, _ := json.Marshal(expectedFinal)
	t.Logf("jsonBytes: %s", jsonBytes)
	assert.JSONEq(t, string(expectedJSON), string(jsonBytes))
}

func TestArtifactsToJSONWithNilAttachmentsSlice(t *testing.T) {
	serverMessage := struct {
		Attachments []*nextentity.Attachment
	}{
		Attachments: nil, // Simulating a nil slice
	}

	artifacts := serverMessage.Attachments
	var parts []string
	for _, attachment := range artifacts { // Loop over nil slice should be safe, resulting in no iterations
		if attachment.URL != "" {
			parts = append(parts, fmt.Sprintf("- [%s](%s)", attachment.FileName, attachment.URL))
		}
	}
	textContent := strings.Join(parts, "\n")
	// 直接用匿名结构体
	final := struct {
		ZhCN struct {
			Content [][]struct {
				Tag  string `json:"tag"`
				Text string `json:"text"`
			} `json:"content"`
		} `json:"zh_cn"`
	}{}

	// 填充数据
	final.ZhCN.Content = [][]struct {
		Tag  string `json:"tag"`
		Text string `json:"text"`
	}{
		{
			{
				Tag:  "md",
				Text: textContent,
			},
		},
	}

	jsonBytes, err := json.Marshal(final)

	assert.NoError(t, err)
	expectedText := ""
	// 修复：expectedJSON 应该与 final 结构体匹配
	expectedFinal := struct {
		ZhCN struct {
			Content [][]struct {
				Tag  string `json:"tag"`
				Text string `json:"text"`
			} `json:"content"`
		} `json:"zh_cn"`
	}{}
	expectedFinal.ZhCN.Content = [][]struct {
		Tag  string `json:"tag"`
		Text string `json:"text"`
	}{
		{
			{
				Tag:  "md",
				Text: expectedText,
			},
		},
	}
	expectedJSON, _ := json.Marshal(expectedFinal)
	assert.JSONEq(t, string(expectedJSON), string(jsonBytes))
}
