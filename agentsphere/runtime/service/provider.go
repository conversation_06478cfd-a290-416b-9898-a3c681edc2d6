package runtimeservice

import (
	"bytes"
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"text/template"

	"al.essio.dev/pkg/shellescape"
	bytesuite "code.byted.org/bytesuite/bytesuite-cli/clients"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/workspace"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/port/stratocube"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/mount"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"
	docker "github.com/docker/docker/client"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"k8s.io/apimachinery/pkg/api/resource"
)

func NewRuntimeProviderRegistry(stratocubeCli stratocube.Client, config *config.AgentSphereConfig, stratoCubeConfig *tcc.GenericConfig[config.StratoCubeConfig]) (*runtimedal.RuntimeProviderRegistry, error) {
	if config == nil {
		return nil, errors.New("config is nil")
	}
	registry := &runtimedal.RuntimeProviderRegistry{
		Providers: make(map[entity.RuntimeProviderType]runtimedal.RuntimeProvider),
	}

	registry.Register("docker", NewDockerProvider())
	registry.Register("local", NewLocalProvider("output/agentsphere_runtime"))
	registry.Register("bytesuite", NewBytesuiteProvider())
	registry.Register("stratocube", NewStratoCubeProvider(stratocubeCli, stratoCubeConfig))

	return registry, nil
}

var (
	//go:embed scripts/init_docker.sh
	initDockerScript         string
	initDockerScriptTemplate = template.Must(template.New("init_docker_script").Parse(initDockerScript))
	//go:embed scripts/init_bytesuite_runtime.sh
	initBytesuiteScript         string
	initBytesuiteScriptTemplate = template.Must(template.New("init_bytesuite_script").Parse(initBytesuiteScript))
	//go:embed scripts/init_stratocube.sh
	initStratoCubeScript         string
	initStratoCubeScriptTemplate = template.Must(template.New("init_stratocube_script").Parse(initStratoCubeScript))
)

func getLocalDebugAPIBaseURL(ctx context.Context, apiBaseURL string) string {
	parsed, err := url.Parse(apiBaseURL)
	if err != nil {
		log.V1.CtxError(ctx, "failed to parse and resolve api base url to localhost: %s err: %v", apiBaseURL, err)
		return apiBaseURL
	}
	return fmt.Sprintf("http://localhost:6789%s", parsed.Path)
}

// local debug container provider for debug purpose only
// NO USE FOR PRODUCTION
// only available on macos/linux
type localProvider struct {
	path string // path to the runtime binary
	// Key is the pid of docker cli process.
	cmdsMap     map[string]*exec.Cmd
	cmdsMapLock *sync.Mutex
}

func NewLocalProvider(path string) runtimedal.RuntimeProvider {
	return &localProvider{
		path:        path,
		cmdsMap:     make(map[string]*exec.Cmd),
		cmdsMapLock: &sync.Mutex{},
	}
}

type runtimeRuntimeBinarySource struct {
	Type    string
	Path    string
	Version string
}

func parseRuntimeBinarySource(uri string) (*runtimeRuntimeBinarySource, error) {
	res, err := url.Parse(uri)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse runtime binary source")
	}
	path := res.Path
	if res.Host != "" {
		path = res.Host + path
	}
	return &runtimeRuntimeBinarySource{
		Type:    res.Scheme,
		Path:    path,
		Version: res.Query().Get("version"),
	}, nil
}

func (p localProvider) CreateContainer(ctx context.Context, _ string, opt runtimedal.CreateContainerOption) (*runtimedal.RuntimeInstance, error) {
	runtimeBinPath := p.path
	if len(opt.BinarySource) != 0 && strings.HasPrefix(opt.BinarySource, "file://") {
		runtimeBinPath = strings.TrimPrefix(opt.BinarySource, "file://")
	}
	cmdPath, err := exec.LookPath(runtimeBinPath)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get runtime process binary path")
	}
	log.V1.CtxInfo(ctx, "resolved runtime binary path to %s", cmdPath)
	cmd := exec.CommandContext(ctx, cmdPath) // #nosec G204
	envs := prepareEnviron(opt)
	envs[entity.RuntimeEnvironAPIBaseURL] = getLocalDebugAPIBaseURL(ctx, opt.APIBaseURL)
	// Inherit all envs from current process for local provider, as some toolkits such as Go SDK need them.
	for _, v := range os.Environ() {
		s := strings.SplitN(v, "=", 2)
		if len(s) != 2 {
			continue
		}
		envs[s[0]] = s[1]
	}
	for k, v := range opt.Environ {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
	}
	cmd.Stderr = os.Stderr
	cmd.Stdout = os.Stderr

	err = cmd.Start()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	p.cmdsMapLock.Lock()
	p.cmdsMap[strconv.Itoa(cmd.Process.Pid)] = cmd
	p.cmdsMapLock.Unlock()

	return &runtimedal.RuntimeInstance{ID: strconv.Itoa(cmd.Process.Pid)}, nil
}

func (p localProvider) GetContainerStatus(ctx context.Context, tenantKey, id string) (runtimedal.ContainerStatus, error) {
	p.cmdsMapLock.Lock()
	defer p.cmdsMapLock.Unlock()
	cmd, ok := p.cmdsMap[id]
	if !ok {
		return runtimedal.ContainerStatusUnknown, errors.Errorf("container %s not found", id)
	}
	if cmd.ProcessState == nil {
		return runtimedal.ContainerStatusCreated, nil
	}
	if cmd.ProcessState.Exited() {
		return runtimedal.ContainerStatusStopped, nil
	}
	return runtimedal.ContainerStatusRunning, nil
}

func (p localProvider) StartContainer(ctx context.Context, tenantKey, id string) error {
	return nil
}

func (p localProvider) StopContainer(ctx context.Context, tenantKey, id string) error {
	pid, err := strconv.ParseInt(id, 10, 32)
	if err != nil {
		return errors.WithMessagef(err, "invalid pid %s", id)
	}
	return syscall.Kill(int(pid), syscall.SIGINT)
}

func (p localProvider) DeleteContainer(ctx context.Context, tenantKey, id string) error {
	return p.StopContainer(ctx, tenantKey, id)
}

func (p localProvider) DownloadContainerFile(ctx context.Context, tenantKey, id, filePath string) (io.ReadCloser, error) {
	return io.NopCloser(bytes.NewReader([]byte{})), nil
}

func (p localProvider) GetSDPWebShell(ctx context.Context, username, containerName, serviceToken, cubeID string) (string, error) {
	return "", errors.New("not implemented")
}

func (p localProvider) ExecuteInContainer(ctx context.Context, tenantKey, cmd, id, dir string, optionsFunc ...runtimedal.ExecuteOptionFunc) (string, error) {
	panic("not implemented")
}

func (p localProvider) ResolveURI(ctx context.Context, tenantKey, host, id, uri string) (string, string) {
	return uri, ""
}

func (p localProvider) ResolveServerURI(ctx context.Context, tenantKey, id, serverName string) (string, error) {
	return "", errors.New("not implemented")
}

// for local development only
type dockerProvider struct {
	cli *docker.Client
}

type ContainerIDGroup struct {
	RuntimeContainerID string `json:"runtime_container_id"`
	BashContainerID    string `json:"bash_container_id,omitempty"`
}

func (group *ContainerIDGroup) Marshal() string {
	data, _ := json.Marshal(group)
	return string(data)
}

func ParseContainerIDGroup(id string) (*ContainerIDGroup, error) {
	var group ContainerIDGroup
	if strings.HasPrefix(id, "{") { // 新格式
		err := json.Unmarshal([]byte(id), &group)
		return &group, err
	} else { // 兼容旧格式
		group.RuntimeContainerID = id
		return &group, nil
	}
}

func (p dockerProvider) DownloadContainerFile(ctx context.Context, tenantKey, id, filePath string) (io.ReadCloser, error) {
	return io.NopCloser(bytes.NewReader([]byte{})), nil
}

func (p dockerProvider) GetSDPWebShell(ctx context.Context, username, containerName, serviceToken, cubeID string) (string, error) {
	return "", errors.New("not implemented")
}

func (p dockerProvider) CreateContainer(ctx context.Context, tenantKey string, opt runtimedal.CreateContainerOption) (*runtimedal.RuntimeInstance, error) {
	envs := prepareEnviron(opt)
	envs[entity.RuntimeDockerArch] = runtime.GOARCH
	envs[entity.RuntimeEnvironAPIBaseURL] = getLocalDebugAPIBaseURL(ctx, opt.APIBaseURL)
	envs["CONSUL_HTTP_HOST"] = os.Getenv("CONSUL_HTTP_HOST")
	environ := make([]string, 0, len(envs))
	for k, v := range opt.Environ {
		environ = append(environ, fmt.Sprintf("%s=%s", k, v))
	}
	wd, _ := os.Getwd()
	var (
		binSrc = &runtimeRuntimeBinarySource{
			Type:    "file",
			Path:    filepath.Join(wd, "output/agentsphere_runtime"),
			Version: "",
		}
		err error
	)
	if len(opt.BinarySource) != 0 {
		binSrc, err = parseRuntimeBinarySource(opt.BinarySource)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to parse runtime binary source")
		}
		if binSrc.Type != "file" {
			return nil, errors.Errorf("docker provider only supports file source, got: %s", binSrc.Type)
		}
	}
	err = p.pullImageIfNotExists(ctx, opt.Image)
	if err != nil {
		return nil, err
	}

	script, err := prepareInitScript(initDockerScriptTemplate, envInitScriptArgs{Env: opt.Environ})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to prepare init script")
	}

	mounts := []mount.Mount{
		{
			Type:   mount.TypeBind,
			Source: binSrc.Path,
			Target: "/usr/local/bin/runtime/agentsphere_runtime",
		},
	}
	bashMounts := make([]mount.Mount, 0)

	// bash image
	if opt.BashImage != "" {
		err = p.pullImageIfNotExists(ctx, opt.BashImage)
		if err != nil {
			return nil, err
		}

		workspaceVolume, err := p.cli.VolumeCreate(ctx, volume.CreateOptions{
			Name: fmt.Sprintf("%s_workspace", opt.SessionID),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create workspace volume")
		}
		tmpVolume, err := p.cli.VolumeCreate(ctx, volume.CreateOptions{
			Name: fmt.Sprintf("%s_tmp", opt.SessionID),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create tmp volume")
		}

		bashMounts = append(bashMounts, mount.Mount{
			Type:   mount.TypeVolume,
			Source: workspaceVolume.Name,
			Target: "/workspace",
		}, mount.Mount{
			Type:   mount.TypeVolume,
			Source: tmpVolume.Name,
			Target: "/tmp",
		}, mount.Mount{
			Type:   mount.TypeVolume,
			Source: tmpVolume.Name,
			Target: "/opt/tmp/.cube-share-sock",
		})
		mounts = append(mounts, bashMounts...)
	}

	if opt.DockerOptions != nil {
		mounts = append(mounts, opt.DockerOptions.Mounts...)
		bashMounts = append(bashMounts, opt.DockerOptions.Mounts...)
	}
	resp, err := p.cli.ContainerCreate(ctx, &container.Config{
		Image: opt.Image,
		Env:   environ,
		Cmd:   []string{"/bin/bash", "-c", script},
	}, &container.HostConfig{
		// AutoRemove:  true, // disable this if you want to preserve the container when init fails
		NetworkMode: network.NetworkHost,
		Privileged:  true,
		Mounts:      mounts,
	}, nil, nil, opt.SessionID) // #nosec G112
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create container")
	}
	if err := p.cli.ContainerStart(ctx, resp.ID, container.StartOptions{}); err != nil {
		return nil, errors.WithMessage(err, "failed to start container")
	}

	if opt.BashImage != "" {
		bashResp, err := p.cli.ContainerCreate(ctx, &container.Config{
			Image: opt.BashImage,
			Env:   environ,
			Cmd:   []string{"/home/<USER>/.config/code-server/start.sh"},
		}, &container.HostConfig{
			// AutoRemove:  true, // disable this if you want to preserve the container when init fails
			NetworkMode: network.NetworkHost,
			Privileged:  true,
			Mounts:      bashMounts,
		}, nil, nil, fmt.Sprintf("%s_bash", opt.SessionID)) // #nosec G112
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create bash container")
		}
		if err := p.cli.ContainerStart(ctx, bashResp.ID, container.StartOptions{}); err != nil {
			return nil, errors.WithMessage(err, "failed to start bash container")
		}

		// runtime container id和bash container id，用','分隔
		containerIDGroup := ContainerIDGroup{
			RuntimeContainerID: resp.ID,
			BashContainerID:    bashResp.ID,
		}
		return &runtimedal.RuntimeInstance{
			ID: (&containerIDGroup).Marshal(),
		}, nil
	} else {
		return &runtimedal.RuntimeInstance{
			ID: resp.ID,
		}, nil
	}
}

func (p dockerProvider) pullImageIfNotExists(ctx context.Context, imageName string) error {
	_, _, err := p.cli.ImageInspectWithRaw(ctx, imageName)
	if err != nil {
		log.V1.Info("pulling image %s", imageName)
		reader, err := p.cli.ImagePull(ctx, imageName, image.PullOptions{})
		if err != nil {
			log.V1.Error("failed to pull image %s: %+v", imageName, err)
			return errors.WithMessagef(err, "failed to pull image %s", imageName)
		}
		defer reader.Close()
		_, err = io.ReadAll(reader)
		if err != nil && err != io.EOF {
			return errors.WithMessagef(err, "failed to read image pull result for %s", imageName)
		}
	}
	return nil
}

func (p dockerProvider) StartContainer(ctx context.Context, tenantKey, id string) error {
	containerIDGroup, err := ParseContainerIDGroup(id)
	if err != nil {
		return err
	}
	if containerIDGroup.RuntimeContainerID != "" {
		err := p.cli.ContainerStart(ctx, containerIDGroup.RuntimeContainerID, container.StartOptions{})
		if err != nil {
			return err
		}
	}
	if containerIDGroup.BashContainerID != "" {
		err := p.cli.ContainerStart(ctx, containerIDGroup.BashContainerID, container.StartOptions{})
		if err != nil {
			return err
		}
	}
	return nil
}

func (p dockerProvider) StopContainer(ctx context.Context, tenantKey, id string) error {
	containerIDGroup, err := ParseContainerIDGroup(id)
	if err != nil {
		return err
	}
	if containerIDGroup.RuntimeContainerID != "" {
		err := p.cli.ContainerStop(ctx, containerIDGroup.RuntimeContainerID, container.StopOptions{Timeout: lo.ToPtr(0)})
		if err != nil {
			return err
		}
	}
	if containerIDGroup.BashContainerID != "" {
		err := p.cli.ContainerStop(ctx, containerIDGroup.BashContainerID, container.StopOptions{Timeout: lo.ToPtr(0)})
		if err != nil {
			return err
		}
	}
	return nil
}

func (p dockerProvider) DeleteContainer(ctx context.Context, tenantKey, id string) error {
	containerIDGroup, err := ParseContainerIDGroup(id)
	if err != nil {
		return err
	}
	if containerIDGroup.RuntimeContainerID != "" {
		err := p.cli.ContainerRemove(ctx, containerIDGroup.RuntimeContainerID, container.RemoveOptions{
			RemoveVolumes: true,
		})
		if err != nil {
			return err
		}
	}
	if containerIDGroup.BashContainerID != "" {
		err := p.cli.ContainerRemove(ctx, containerIDGroup.BashContainerID, container.RemoveOptions{
			RemoveVolumes: true,
		})
		if err != nil {
			return err
		}
	}

	res, err := p.cli.ContainerInspect(ctx, containerIDGroup.RuntimeContainerID)
	if err != nil {
		return errors.WithMessage(err, "failed to inspect container")
	}
	sessionID := res.Name

	// 删除volume
	err = p.cli.VolumeRemove(ctx, fmt.Sprintf("%s_workspace", sessionID), true)
	if err != nil && !strings.Contains(err.Error(), "not found") {
		return errors.WithMessage(err, "failed to remove workspace volume")
	}
	err = p.cli.VolumeRemove(ctx, fmt.Sprintf("%s_tmp", sessionID), true)
	if err != nil && !strings.Contains(err.Error(), "not found") {
		return errors.WithMessage(err, "failed to remove tmp volume")
	}
	return nil
}

func (p dockerProvider) GetContainerStatus(ctx context.Context, tenantKey, id string) (runtimedal.ContainerStatus, error) {
	containerIDGroup, err := ParseContainerIDGroup(id)
	if err != nil {
		return runtimedal.ContainerStatusUnknown, err
	}

	res, err := p.cli.ContainerInspect(ctx, containerIDGroup.RuntimeContainerID)
	if err != nil {
		return runtimedal.ContainerStatusUnknown, errors.WithMessage(err, "failed to inspect container")
	}
	// "created", "running", "paused", "restarting", "removing", "exited", or "dead"
	switch res.State.Status {

	case "created", "restarting":
		return runtimedal.ContainerStatusCreated, nil
	case "removing":
		return runtimedal.ContainerStatusStopping, nil
	case "exited", "dead", "paused":
		return runtimedal.ContainerStatusStopped, nil
	case "running":
		return runtimedal.ContainerStatusRunning, nil
	default:
		return runtimedal.ContainerStatusUnknown, nil
	}
}

func (p dockerProvider) ExecuteInContainer(ctx context.Context, tenantKey, cmd, id, dir string, optionsFunc ...runtimedal.ExecuteOptionFunc) (string, error) {
	containerIDGroup, err := ParseContainerIDGroup(id)
	if err != nil {
		return "", err
	}
	containerID := lo.Ternary(containerIDGroup.BashContainerID != "", containerIDGroup.BashContainerID, containerIDGroup.RuntimeContainerID)
	excConfig := container.ExecOptions{
		Cmd:          []string{"/bin/bash", "-c", cmd},
		WorkingDir:   dir,
		AttachStdout: true,
		AttachStderr: true,
	}
	execIDResp, err := p.cli.ContainerExecCreate(ctx, containerID, excConfig)
	if err != nil {
		return "", errors.WithMessagef(err, "failed to create exec: %+v", cmd)
	}

	resp, err := p.cli.ContainerExecAttach(ctx, execIDResp.ID, container.ExecAttachOptions{})
	if err != nil {
		return "", errors.WithMessagef(err, "failed to attach exec: %+v", cmd)
	}
	defer resp.Close()
	output, err := io.ReadAll(resp.Reader)
	if err != nil {
		return "", errors.WithMessagef(err, "failed to read exec output: %+v", cmd)
	}
	return string(output), nil
}

func (p dockerProvider) ResolveURI(ctx context.Context, tenantKey, host, id, uri string) (string, string) {
	return uri, ""
}

func (p dockerProvider) ResolveServerURI(ctx context.Context, tenantKey, id, serverName string) (string, error) {
	return "", errors.New("not implemented")
}

func NewDockerProvider() runtimedal.RuntimeProvider {
	cli, err := docker.NewClientWithOpts(docker.FromEnv, docker.WithAPIVersionNegotiation())
	if err != nil {
		log.V1.Error("failed to create docker client", err)
		return nil
	}
	return &dockerProvider{cli}
}

func NewBytesuiteProvider() runtimedal.RuntimeProvider {
	return &bytesuiteProvider{}
}

type bytesuiteProvider struct{}

func (p bytesuiteProvider) DownloadContainerFile(ctx context.Context, tenantKey, id, filePath string) (io.ReadCloser, error) {
	return io.NopCloser(bytes.NewReader([]byte{})), nil
}

func (p bytesuiteProvider) GetSDPWebShell(ctx context.Context, username, containerName, serviceToken, cubeID string) (string, error) {
	return "", errors.New("not implemented")
}

func (p bytesuiteProvider) ExecuteInContainer(ctx context.Context, tenantKey, cmd, id, dir string, optionsFunc ...runtimedal.ExecuteOptionFunc) (string, error) {
	panic("not implemented")
}

func (p bytesuiteProvider) CreateContainer(ctx context.Context, tenantKey string, opt runtimedal.CreateContainerOption) (*runtimedal.RuntimeInstance, error) {
	instanceTag := lo.Ternary(opt.ReuseKey != "", opt.ReuseKey, fmt.Sprintf("agentsphere_%s", opt.SessionID))
	psm := "flow.agentsphere.runtime"
	boeenv := "boe_agsp_runtime"
	region := "boe"
	cluster := "default"
	runtimeType := "cmd"
	// bytesuite uses string type for context key, other types will fail
	ctx = context.WithValue(ctx, "username", opt.Account.Username)
	ctx = context.WithValue(ctx, "token", opt.Account.CloudJWT)
	environ := prepareEnviron(opt)

	var instance *bytesuite.OInstance
	var err error
	if instance, err = bytesuite.FindInstanceByTagAndRuntime(ctx, instanceTag, runtimeType, psm, boeenv); instance == nil || err != nil {
		instance, err = bytesuite.CreateInstance(ctx, &bytesuite.CreateInstanceReq{
			InstanceMeta: bytesuite.InstanceMeta{
				PSM: psm, RuntimeType: runtimeType, Region: region, Cluster: cluster, ENV: boeenv, Name: instanceTag, Tag: instanceTag,
			},
			InstanceConfig: bytesuite.InstanceConfig{CmdConfig: &bytesuite.CmdConfig{
				RunImage: opt.Image,
				ENVs:     environ,
				Resource: &bytesuite.Resource{CPU: 4, MEM: 8},
				Ports: []bytesuite.Port{
					{Intent: "primary", IsPrimary: true, Port: 6789, Type: "tcp"}, // fixed port number, same as TCE flow.agentsphere.runtime
					{Intent: "debug", IsPrimary: false, Port: opt.Port, Type: "tcp"},
				},
				// bytesuite requires specified folder exist before execute custom script
				// we use an empty git repo to avoid bytesuite init error
				CodeInfo: &bytesuite.OCodeInfo{
					GitURL:     "******************:ide/empty.git",
					BranchName: "master",
				},
			}},
			InitCode: true,
		})
		defer func() {
			if err != nil && instance != nil {
				bytesuite.DeleteInstance(ctx, instance.ID)
			}
		}()
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create runtime container")
	}

	script, err := prepareInitScript(initBytesuiteScriptTemplate, envInitScriptArgs{Env: opt.Environ})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to prepare init script")
	}
	_, err = bytesuite.ExecuteInstance(ctx, instance.ID, script, true)
	log.V1.CtxInfo(ctx, "execute init script in bytesuite instance [%s], err: %v", instance.ID, err)
	return &runtimedal.RuntimeInstance{ID: instance.ID}, errors.WithMessage(err, "failed to init runtime container")
}

func (p bytesuiteProvider) StartContainer(ctx context.Context, tenantKey, id string) error {
	instance, err := bytesuite.StartInstance(ctx, id, nil, false)
	log.V1.CtxInfo(ctx, "start runtime container [%s] status [%s], err: %v", instance.ID, instance.Status, err)
	return errors.WithMessage(err, "failed to start runtime container")
}

func (p bytesuiteProvider) StopContainer(ctx context.Context, tenantKey, id string) error {
	_, err := bytesuite.StopInstance(ctx, id)
	if err != nil {
		return errors.WithMessage(err, "failed to stop runtime container")
	}
	_, err = bytesuite.DeleteInstance(ctx, id)
	return errors.WithMessage(err, "failed to delete bytesuite instance")
}

func (p bytesuiteProvider) DeleteContainer(ctx context.Context, tenantKey, id string) error {
	_, err := bytesuite.DeleteInstance(ctx, id)
	return err
}

func (p bytesuiteProvider) GetContainerStatus(ctx context.Context, tenantKey, id string) (runtimedal.ContainerStatus, error) {
	// TODO(cyx): implement.
	return runtimedal.ContainerStatusRunning, nil
}

func (p bytesuiteProvider) ResolveURI(ctx context.Context, tenantKey, host, id, uri string) (string, string) {
	return uri, ""
}

func (p bytesuiteProvider) ResolveServerURI(ctx context.Context, tenantKey, id, serverName string) (string, error) {
	return "", errors.New("not implemented")
}

type stratocubeProvider struct {
	cli              stratocube.Client
	stratoCubeConfig *tcc.GenericConfig[config.StratoCubeConfig]
}

var _ runtimedal.RuntimeProvider = (*stratocubeProvider)(nil)

const (
	stratocubeRuntimeContainerName = "runtime"
	stratocubeIngressPort          = 17000 // stratocube 固定的 ingress 端口号，所有 cube 都需要访问该端口通过 nginx 转发
	stratocubeDebugPort            = 17200 // 调试用, code-server / ..., 访问需要增加 /debug 前缀, nginx rewrite 会去掉

	stratocubePVCStorageClassPPE    = "strato-csi-ceph-hl" // CN BOE 使用的 pvc storage class
	stratocubePVCStorageClassOnline = "strato-csi-ceph-yg" // CN 线上使用的 pvc storage class

	runtimePSM = "flow.agentsphere.runtime"
)

func NewStratoCubeProvider(cli stratocube.Client, stratoCubeConfig *tcc.GenericConfig[config.StratoCubeConfig]) runtimedal.RuntimeProvider {
	return &stratocubeProvider{
		cli:              cli,
		stratoCubeConfig: stratoCubeConfig,
	}
}

func (s *stratocubeProvider) DownloadContainerFile(ctx context.Context, tenantKey, id, filePath string) (io.ReadCloser, error) {
	downloadFileResponse, err := s.cli.DownloadFile(ctx, tenantKey, stratocube.DownloadFileRequest{
		ID:            id,
		Path:          filePath,
		ContainerName: stratocubeRuntimeContainerName,
	})
	if err != nil {
		return nil, err
	}
	return downloadFileResponse.ReadCloser, nil
}

func (s *stratocubeProvider) GetSDPWebShell(ctx context.Context, username, containerName, serviceToken, cubeID string) (string, error) {
	resp, err := s.cli.GetSDPWebShell(ctx, stratocube.GetSDPWebShellRequest{
		ID:            cubeID,
		ContainerName: containerName,
		User:          username,
		ServiceToken:  serviceToken,
	})
	if err != nil {
		return "", err
	}
	return resp.SdpURL, nil
}

func (s *stratocubeProvider) CreateContainer(ctx context.Context, tenantKey string, opt runtimedal.CreateContainerOption) (*runtimedal.RuntimeInstance, error) {
	var (
		err    error
		binSrc = &runtimeRuntimeBinarySource{
			Type:    "scm",
			Path:    "devgpt/agentsphere/runtime",
			Version: "", // leave empty for latest version
		}
	)
	if len(opt.BinarySource) != 0 {
		binSrc, err = parseRuntimeBinarySource(opt.BinarySource)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to parse runtime binary source")
		}
		if binSrc.Type != "scm" {
			return nil, errors.Errorf("unsupported runtime binary source type: %s", binSrc.Type)
		}
	}

	// stratocube 容器在线上网络，无法连接本地服务，但 BOE apiserver 有逃逸，stratocube 只连接 BOE API Server
	opt.Environ[entity.RuntimeEnvironAPIBaseURL] = opt.APIBaseURL
	environ := prepareEnviron(opt)
	wildcardDomain := lo.Ternary(env.IsBoe(), stratocube.WildcardDomainPPE, stratocube.WildcardDomainOnline)
	storageClass := lo.Ternary(env.IsBoe(), stratocubePVCStorageClassPPE, stratocubePVCStorageClassOnline)
	if opt.ResourceQuota.StorageClass != nil && *opt.ResourceQuota.StorageClass != "" {
		storageClass = *opt.ResourceQuota.StorageClass
	}
	psm := lo.Ternary(opt.PSM == "", runtimePSM, opt.PSM)

	initScript, err := prepareInitScript(initStratoCubeScriptTemplate, envInitScriptArgs{
		UpdateRuntime:  false,
		Env:            environ,
		RuntimeSCM:     binSrc.Path,
		RuntimeVersion: binSrc.Version,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to prepare init script")
	}

	cpus := stratocube.ResourceQuantity{
		Limits:   "4000m",
		Requests: "2000m",
	}
	if opt.ResourceQuota != nil && opt.ResourceQuota.CPU != nil {
		cpus = stratocube.ResourceQuantity{
			Limits:   opt.ResourceQuota.CPU.Limits,
			Requests: opt.ResourceQuota.CPU.Requests,
		}
	}
	memory := stratocube.ResourceQuantity{
		Limits:   "16384Mi",
		Requests: "8192Mi",
	}
	if opt.ResourceQuota != nil && opt.ResourceQuota.Memory != nil {
		memory = stratocube.ResourceQuantity{
			Limits:   opt.ResourceQuota.Memory.Limits,
			Requests: opt.ResourceQuota.Memory.Requests,
		}
	}
	hostPaths := []stratocube.HostPathListItem{
		{
			Name: "consul-deploy",
			Path: "/opt/tiger/consul_deploy",
		},
		{
			Name: "consul-agent",
			Path: "/opt/tmp/consul_agent",
		},
		{
			Name: "bvc",
			Path: "/usr/local/tao/agent/modules/bvc/bin/bvc",
		},
	}
	volumes := []stratocube.VolumeMountsItem{
		{
			Name:      "consul-deploy",
			MountPath: "/opt/tiger/consul_deploy",
			ReadOnly:  true,
		},
		{
			Name:      "consul-agent",
			MountPath: "/opt/tmp/consul_agent",
			ReadOnly:  true,
		},
		{
			Name:      "bvc",
			MountPath: "/usr/bin/bvc",
			ReadOnly:  true,
		},
	}
	pvcs := []stratocube.PVCListItem{}
	if opt.ResourceQuota != nil && opt.ResourceQuota.PersistWorkspace != nil {
		rootVol := stratocube.PVCListItem{
			Name:             "root-vol",
			StorageRequests:  lo.Ternary(opt.ResourceQuota.PersistWorkspace.Requests != "", opt.ResourceQuota.PersistWorkspace.Requests, "8Gi"),
			StorageClassName: storageClass,
		}
		volumes = append(volumes, stratocube.VolumeMountsItem{
			Name:      rootVol.Name,
			MountPath: "/workspace",
			SubPath:   "fs/workspace",
			ReadOnly:  false,
		}, stratocube.VolumeMountsItem{
			Name:      rootVol.Name,
			MountPath: "/tmp",
			SubPath:   "fs/tmp",
			ReadOnly:  false,
		})
		pvcs = append(pvcs, rootVol)

		// 支持多个大仓的预热
		pvcIndex := 0
		for _, codebaseMention := range opt.CodebaseMentions {
			// 上限10个大仓
			if pvcIndex > 10 {
				break
			}
			if workspace.RepoInWarmupList(ctx, opt.SessionID, codebaseMention.RepoName) {
				annotations := make(map[string]string)
				annotations["strato_repo"] = codebaseMention.RepoName
				annotations["strato_branch"] = codebaseMention.Branch
				annotations["strato_commit_id"] = codebaseMention.CommitID

				if pvcIndex == 0 {
					pvcs[len(pvcs)-1].Annotations = lo.Assign(pvcs[len(pvcs)-1].Annotations, annotations)
				} else {
					cacheBaseDir := strings.ReplaceAll(strings.ReplaceAll(codebaseMention.RepoName, "/", "-"), "_", "-")
					pvc := stratocube.PVCListItem{
						Name:             fmt.Sprintf("cache-%s", strings.ToLower(cacheBaseDir)),
						StorageRequests:  rootVol.StorageRequests,
						StorageClassName: rootVol.StorageClassName,
						Annotations:      annotations,
					}
					volumes = append(volumes, stratocube.VolumeMountsItem{
						Name:      pvc.Name,
						MountPath: fmt.Sprintf("/workspace/.bigrepo_warmup/%s", cacheBaseDir),
						SubPath:   fmt.Sprintf("fs/workspace/.bigrepo_warmup/%s", cacheBaseDir),
						ReadOnly:  false,
					})
					pvcs = append(pvcs, pvc)
				}
				pvcIndex++
			}
		}
	}

	// 每多克隆一个仓库，加8G内存，防止git OOM
	if len(opt.CodebaseMentions) > 1 {
		deltaQuantity := (len(opt.CodebaseMentions) - 1) * 8
		if deltaQuantity > 24 {
			deltaQuantity = 24
		}
		deltaQuantityStr := fmt.Sprintf("%dGi", deltaQuantity)
		memory.Limits, err = addQuantity(ctx, memory.Limits, deltaQuantityStr)
		if err != nil {
			log.V1.CtxError(ctx, "create stratocube runtime memory.Limits add quantity failed: %+v", err)
		}
		memory.Requests, err = addQuantity(ctx, memory.Requests, deltaQuantityStr)
		if err != nil {
			log.V1.CtxError(ctx, "create stratocube runtime memory.Requests add quantity failed: %+v", err)
		}
	}

	spec := stratocube.CubeSpec{
		OS:             stratocube.CubeOSLinux,
		WildcardDomain: wildcardDomain,
		Containers: []stratocube.ContainerSpec{
			{
				Image: opt.Image,
				Name:  stratocubeRuntimeContainerName,
				Command: []string{
					"/bin/bash", "-c", initScript,
				},
				Ports: []stratocube.PortItem{
					// runtime listen port. for server-runtime communication
					{
						Name:        "runtime",
						Var:         opt.Port,
						Protocol:    stratocube.PortProtocolHTTP,
						NeedIngress: true,
						ServicePort: stratocubeIngressPort,
					},
					// Uncomment when it is ready.
					// {
					// 	Name:        "debug",
					// 	Var:         stratocubeDebugPort,
					// 	Protocol:    stratocube.PortProtocolHTTP,
					// 	NeedIngress: true,
					// 	ServicePort: stratocubeDebugPort,
					// },
				},
				Environ: lo.MapToSlice(environ, func(k, v string) stratocube.EnvironItem {
					return stratocube.EnvironItem{Name: k, Value: v}
				}),
				CPURequirements: cpus,
				MemRequirements: memory,
				VolumeMounts:    volumes,
			},
		},
		Volumes: stratocube.Volumes{
			HostPathList: hostPaths,
			PVCList:      pvcs,
		},
		CtrlInfo: stratocube.CtrlInfo{
			EnableBytedInfra:    true,
			StreamLogPSM:        psm,
			EnableFileServer:    true,
			EnableBrowserServer: true,
			EnableCodeServer:    true,
			CodeServerImage:     opt.BashImage,
			ACLEntries: []stratocube.ACLEntry{
				{
					Path: "/workspace/log",
				},
			},
			ACLIsolationMode: "mountNS",
		},
	}

	log.V1.CtxInfo(ctx, "create stratocube runtime cube with spec: %s", conv.JSONString(spec))
	res, err := s.cli.CreateCube(ctx, tenantKey, stratocube.CreateCubeRequest{
		Description: fmt.Sprintf("CodeAgent Runtime Container for %s", opt.Image),
		Spec:        spec,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to create runtime container")
	}
	return &runtimedal.RuntimeInstance{ID: res.CubeID}, nil
}

func (s *stratocubeProvider) ExecuteInContainer(ctx context.Context, tenantKey, cmd, id, dir string, optionsFunc ...runtimedal.ExecuteOptionFunc) (string, error) {
	options := runtimedal.ExecuteOption{}
	for _, optionFunc := range optionsFunc {
		optionFunc(&options)
	}
	resp, err := s.cli.CubeExec(ctx, tenantKey, stratocube.CubeExecRequest{
		CubeID:        id,
		ContainerName: stratocubeRuntimeContainerName,
		Command: []string{
			"bash", "-c", fmt.Sprintf("cd %s\n%s", dir, cmd),
		},
		SyncExecution: options.SyncExecution,
		Timeout:       options.Timeout,
	})
	if err != nil {
		return "", err
	}
	return resp.Stdout, nil
}

func (s *stratocubeProvider) StartContainer(ctx context.Context, tenantKey, id string) error {
	return s.cli.ResumeCube(ctx, tenantKey, stratocube.ResumeCubeRequest{CubeID: id})
}

func (s *stratocubeProvider) StopContainer(ctx context.Context, tenantKey, id string) error {
	return s.cli.SuspendCube(ctx, tenantKey, stratocube.SuspendCubeRequest{CubeID: id})
}

func (s *stratocubeProvider) DeleteContainer(ctx context.Context, tenantKey, id string) error {
	return s.cli.DeleteCube(ctx, tenantKey, stratocube.DeleteCubeRequest{CubeID: id})
}

func (s *stratocubeProvider) ResolveURI(ctx context.Context, tenantKey, host, id, originalURI string) (string, string) {
	uri, err := url.Parse(originalURI)
	if err != nil {
		log.V1.CtxError(ctx, "failed to parse uri", err)
	}
	if host == "" {
		resp, err := s.cli.GetCubeWildcardDomain(ctx, tenantKey, stratocube.GetCubeWildcardDomainRequest{
			CubeID: id,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get cube status", err)
		}
		domain := string(lo.Ternary(env.IsBoe(), stratocube.WildcardDomainPPE, stratocube.WildcardDomainOnline))
		if resp != nil && resp.WildcardDomain != "" {
			domain = resp.WildcardDomain
		}
		host = fmt.Sprintf("%s.%s:%d", id, domain, stratocubeIngressPort)
	}
	uri.Host = host
	return uri.String(), uri.Host
}

func (s *stratocubeProvider) ResolveServerURI(ctx context.Context, tenantKey, id, serverName string) (string, error) {
	res, err := s.cli.GetCubeStatus(ctx, tenantKey, stratocube.GetCubeStatusRequest{CubeID: id})
	if err != nil {
		return "", errors.WithMessage(err, "failed to get runtime container status")
	}
	if res.Status != stratocube.CubeStatusRunning {
		return "", nil
	}
	for _, endpoint := range res.Endpoints {
		if endpoint.ContainerName == serverName {
			return endpoint.URL, nil
		}
	}

	return "", errors.Errorf("server %s not found in cube %s", serverName, id)
}

func (s *stratocubeProvider) GetContainerStatus(ctx context.Context, tenantKey, id string) (runtimedal.ContainerStatus, error) {
	res, err := s.cli.GetCubeStatus(ctx, tenantKey, stratocube.GetCubeStatusRequest{CubeID: id})
	if err != nil {
		return runtimedal.ContainerStatusUnknown, errors.WithMessage(err, "failed to get runtime container status")
	}
	switch res.Status {
	case stratocube.CubeStatusRunning:
		return runtimedal.ContainerStatusRunning, nil
	case stratocube.CubeStatusSuspending:
		return runtimedal.ContainerStatusStopping, nil
	case stratocube.CubeStatusSuspend, stratocube.CubeStatusExit:
		return runtimedal.ContainerStatusStopped, nil
	case stratocube.CubeStatusCreating, stratocube.CubeStatusResuming:
		return runtimedal.ContainerStatusCreated, nil
	case stratocube.CubeStatusDeleted:
		return runtimedal.ContainerStatusDeleted, nil
	default:
		return runtimedal.ContainerStatusUnknown, errors.Errorf("unknown runtime container status: %s", res.Status)
	}
}

func prepareEnviron(opt runtimedal.CreateContainerOption) map[string]string {
	environ := opt.Environ
	environ[entity.RuntimeEnvironSessionID] = opt.SessionID
	environ[entity.RuntimeEnvironPort] = strconv.Itoa(opt.Port)
	environ[entity.RuntimeEnvironWorkspacePath] = opt.WorkspacePath
	environ[entity.RuntimeEnvironSessionType] = opt.SessionType
	environ[entity.RuntimeTCEEnv] = os.Getenv(entity.RuntimeTCEEnv)
	environ[entity.RuntimeTCEHostEnv] = os.Getenv(entity.RuntimeTCEHostEnv)

	// remove empty values, as stratocube will throw error
	for k, v := range opt.Environ {
		if v == "" {
			delete(environ, k)
		}
	}

	return environ
}

type envInitScriptArgs struct {
	UpdateRuntime  bool
	Env            map[string]string
	RuntimeSCM     string
	RuntimeVersion string
}

func prepareInitScript(template *template.Template, args envInitScriptArgs) (string, error) {
	var script bytes.Buffer
	args.Env = lo.MapValues(args.Env, func(v string, k string) string {
		return shellescape.Quote(v)
	})
	err := template.Execute(&script, args)
	if err != nil {
		return "", errors.WithMessage(err, "failed to init environment variables for runtime container")
	}

	return script.String(), nil
}

func addQuantity(ctx context.Context, quantityStr string, deltaQuantityStr string) (string, error) {
	quantity, err := resource.ParseQuantity(quantityStr)
	if err != nil {
		log.V1.CtxError(ctx, "ParseQuantity %s failed: %+v", quantityStr, err)
		return quantityStr, err
	} else {
		deltaQuantity, err := resource.ParseQuantity(deltaQuantityStr)
		if err != nil {
			log.V1.CtxError(ctx, "ParseQuantity %s failed: %+v", deltaQuantityStr, err)
			return quantityStr, err
		} else {
			quantity.Add(deltaQuantity)
			return quantity.String(), nil
		}
	}
}
