package runtimeservice

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"runtime/debug"
	"strconv"
	"time"

	"github.com/AlekSi/pointer"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/jsonrpc2"
	"go.uber.org/fx"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/actions/external/codebase"
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"code.byted.org/devgpt/kiwis/agentsphere/common/credential"
	"code.byted.org/devgpt/kiwis/agentsphere/common/redissemaphore"
	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextserverdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	agentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/agent"
	artifactservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	deploymentservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/deployment"
	nextLarkService "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lock"
	replayservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/replay"
	userservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/user"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus"
	remotebus "code.byted.org/devgpt/kiwis/agentsphere/runtime/eventbus/remote"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/service/runtimestarter"
	serverdal "code.byted.org/devgpt/kiwis/agentsphere/server/dal"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/agentsphere"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/copilotstack/llm"
	"code.byted.org/devgpt/kiwis/lib/codegenerator"
	"code.byted.org/devgpt/kiwis/lib/config"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/devgpt/kiwis/lib/stream"
	libtcc "code.byted.org/devgpt/kiwis/lib/tcc"
	"code.byted.org/devgpt/kiwis/lib/uuid"
	"code.byted.org/devgpt/kiwis/port/faas"
	"code.byted.org/devgpt/kiwis/port/hulkcloud"
	"code.byted.org/devgpt/kiwis/port/redis"
	"code.byted.org/devgpt/kiwis/port/rocketmq"
	"code.byted.org/devgpt/kiwis/port/tos"
	"code.byted.org/gopkg/logs/v2/log"
)

var (
	ErrAgentStopped     = errors.New("agent is stopped")
	ErrAgentRunNotFound = errors.New("agent run not found")
)

type Service struct {
	dao                             *runtimedal.DAO
	serverDao                       *serverdal.DAO
	nextserverDao                   *nextserverdal.DAO
	taskExecutionDAO                nextserverdal.TaskExecutionDAO
	orchestratorMQClient            rocketmq.Client
	assignmentMonitorMQClient       rocketmq.Client
	nextRuntimeOrchestratorMQClient rocketmq.Client
	nextSessionMonitorMQClient      rocketmq.Client
	nextTraceMQClient               rocketmq.Client
	redis                           redis.Client
	redisSemaphore                  *redissemaphore.RedisSemaphore
	credentialCache                 *credential.Cache
	idGen                           uuid.Generator
	modelConfig                     *libtcc.GenericConfig[config.ModelDispatchConfig]
	agentList                       *libtcc.GenericConfig[[]config.AgentConfig]
	gitlabToken                     *libtcc.GenericConfig[config.GitlabTokenConfig]
	relayConfig                     *libtcc.GenericConfig[config.AGSPRelayConfig]
	imagexConfig                    *libtcc.GenericConfig[config.ImageXTCCConfig]
	nextCodeConfig                  *libtcc.GenericConfig[config.NextCodeConfig]
	deepWikiConfig                  *libtcc.GenericConfig[config.DeepWikiConfig]
	nextToolI18nConfig              *libtcc.GenericConfig[config.NextToolI18nConfig]
	hulkCloudClient                 hulkcloud.Client
	runtimeProviderRegistry         *runtimedal.RuntimeProviderRegistry
	runtimeAPIConfig                config.AgentSphereRuntimeAPIConfig
	nextLarkService                 *nextLarkService.Service
	codeGenerator                   *codegenerator.Generator
	nextArtifactService             *artifactservice.Service
	nextDeploymentService           *deploymentservice.Service
	nextReplayService               *replayservice.Service
	nextAgentService                *agentservice.Service
	userService                     *userservice.Service
	tosCli                          tos.Client
	tccConf                         *config.AgentSphereTCCConfig
	llmService                      llm.Service
	locker                          lock.Lock
	faasClient                      faas.Client
}

type CreateServiceOption struct {
	fx.In
	DAO                             *runtimedal.DAO
	ServerDAO                       *serverdal.DAO
	NextserverDao                   *nextserverdal.DAO
	TaskExecutionDAO                nextserverdal.TaskExecutionDAO
	RedisSemaphore                  *redissemaphore.RedisSemaphore
	CredentialCache                 *credential.Cache
	Redis                           redis.Client
	OrchestratorMQClient            rocketmq.Client                                   `name:"runtime_orchestrator_mq"`
	AssignmentMonitorMQClient       rocketmq.Client                                   `name:"assignment_monitor_mq"`
	NextRuntimeOrchestratorMQClient rocketmq.Client                                   `name:"next_runtime_orchestrator_mq"`
	NextSessionMonitorMQClient      rocketmq.Client                                   `name:"next_session_monitor_mq" optional:"true"`
	NextTraceMQClient               rocketmq.Client                                   `name:"next_trace_mq" optional:"true"`
	IDGen                           uuid.Generator                                    `optional:"true"`
	ModelConfig                     *libtcc.GenericConfig[config.ModelDispatchConfig] `optional:"true"`
	AgentList                       *libtcc.GenericConfig[[]config.AgentConfig]       `optional:"true"`
	GitlabToken                     *libtcc.GenericConfig[config.GitlabTokenConfig]   `optional:"true"`
	RelayConfig                     *libtcc.GenericConfig[config.AGSPRelayConfig]     `optional:"true"`
	ImagexConfig                    *libtcc.GenericConfig[config.ImageXTCCConfig]     `optional:"true"`
	NextCodeConfig                  *libtcc.GenericConfig[config.NextCodeConfig]      `optional:"true"`
	DeepWikiConfig                  *libtcc.GenericConfig[config.DeepWikiConfig]      `optional:"true"`
	NextToolI18nConfig              *libtcc.GenericConfig[config.NextToolI18nConfig]  `optional:"true"`
	ProviderRegistry                *runtimedal.RuntimeProviderRegistry               `optional:"true"`
	RuntimeAPIConfig                config.AgentSphereRuntimeAPIConfig
	HulkCloudClient                 hulkcloud.Client `optional:"true"`
	NextLarkService                 *nextLarkService.Service
	NextArtifactService             *artifactservice.Service
	NextDeploymentService           *deploymentservice.Service
	NextReplayService               *replayservice.Service
	NextAgentService                *agentservice.Service
	UserService                     *userservice.Service
	TosCli                          tos.Client
	TCCConf                         *config.AgentSphereTCCConfig
	LLMService                      llm.Service
	Locker                          lock.Lock
	FaasClient                      faas.Client `optional:"true"`
}

func NewService(opt CreateServiceOption) (*Service, error) {
	generator, err := codegenerator.NewGenerator(10)
	if err != nil {
		return nil, err
	}

	s := &Service{
		dao:                             opt.DAO,
		serverDao:                       opt.ServerDAO,
		nextserverDao:                   opt.NextserverDao,
		taskExecutionDAO:                opt.TaskExecutionDAO,
		orchestratorMQClient:            opt.OrchestratorMQClient,
		assignmentMonitorMQClient:       opt.AssignmentMonitorMQClient,
		nextRuntimeOrchestratorMQClient: opt.NextRuntimeOrchestratorMQClient,
		nextSessionMonitorMQClient:      opt.NextSessionMonitorMQClient,
		nextTraceMQClient:               opt.NextTraceMQClient,
		redis:                           opt.Redis,
		credentialCache:                 opt.CredentialCache,
		redisSemaphore:                  opt.RedisSemaphore,
		idGen:                           uuid.GetDefaultGenerator(opt.IDGen),
		modelConfig:                     opt.ModelConfig,
		agentList:                       opt.AgentList,
		gitlabToken:                     opt.GitlabToken,
		relayConfig:                     opt.RelayConfig,
		nextToolI18nConfig:              opt.NextToolI18nConfig,
		runtimeProviderRegistry:         lo.Ternary(opt.ProviderRegistry != nil, opt.ProviderRegistry, &runtimedal.RuntimeProviderRegistry{}),
		hulkCloudClient:                 opt.HulkCloudClient,
		runtimeAPIConfig:                opt.RuntimeAPIConfig,
		nextLarkService:                 opt.NextLarkService,
		codeGenerator:                   generator,
		imagexConfig:                    opt.ImagexConfig,
		nextCodeConfig:                  opt.NextCodeConfig,
		deepWikiConfig:                  opt.DeepWikiConfig,
		nextArtifactService:             opt.NextArtifactService,
		nextDeploymentService:           opt.NextDeploymentService,
		nextReplayService:               opt.NextReplayService,
		nextAgentService:                opt.NextAgentService,
		userService:                     opt.UserService,
		tosCli:                          opt.TosCli,
		tccConf:                         opt.TCCConf,
		llmService:                      opt.LLMService,
		locker:                          opt.Locker,
		faasClient:                      opt.FaasClient,
	}
	runtimestarter.Get().RegisterStarter(func(ctx context.Context, evt *runtimestarter.StartEvent) error {
		return s.NextRuntimeStartWorkspace(ctx, StartWorkspaceOption{
			SessionID: evt.SessionID,
			User:      evt.User,
			FromType:  evt.FromType,
		})
	})
	return s, nil
}

type GetAgentRunOption struct {
	// 三选一
	RunID        string
	SessionID    string
	AssignmentID string

	Sync bool
}

func (s *Service) GetAgentRun(ctx context.Context, opt GetAgentRunOption) (*entity.AgentRun, error) {
	if opt.RunID == "" && opt.SessionID == "" && opt.AssignmentID == "" {
		return nil, errors.New("no run id or session id or assignment id specified")
	}
	return s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		RunID:        opt.RunID,
		SessionID:    opt.SessionID,
		AssignmentID: opt.AssignmentID,
		Sync:         opt.Sync,
	})
}

type CreateAgentRunOption struct {
	AssignmentID string
	SessionID    string
	Agent        string
	Version      string
	InitialMsg   string
	Parameters   map[entity.RuntimeParameterKey]any
	User         *authentity.Account
}

// FIXME(cyx): temporarily passing token into runtime process directly.
func (s *Service) getOpenAIToken() string {
	token := os.Getenv("OPENAI_API_KEY")
	if len(token) == 0 && s.modelConfig != nil {
		conf, ok := lo.Find(s.modelConfig.GetPointer().Models, func(m *config.ModelAuthConfig) bool {
			return m.Type == config.ModelTypeAzure
		})
		if ok {
			token = conf.AccessKey
		}
	}
	return token
}

func (s *Service) CreateAgentRun(ctx context.Context, opt CreateAgentRunOption) (*entity.AgentRun, error) {
	runID := s.idGen.NewID()

	if s.agentList == nil {
		return nil, errors.Errorf("default agents is nil")
	}

	conf := s.GetAgentConfig(opt.Agent, opt.Version)
	if conf == nil {
		return nil, errors.Errorf("agent version %s@%s is not found", opt.Agent, opt.Version)
	}
	session, err := s.nextserverDao.GetSession(ctx, nextserverdal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}

	run, err := s.dao.CreateAgentRun(ctx, runtimedal.CreateAgentRunOption{
		ID:           runID,
		AssignmentID: opt.AssignmentID,
		SessionID:    opt.SessionID,
		CreatorID:    opt.User.Username,
		Status:       entity.AgentRunStatusCreated,
		AgentMetadata: entity.AgentRunAgentMetadata{
			Agents: []entity.AgentMetadata{
				{
					Agent:   conf.Agent,
					Version: conf.Version,
				},
			},
			Params: map[entity.RuntimeParameterKey]any{
				"statement":                                  opt.InitialMsg,
				entity.RuntimeParametersABParams:             session.RuntimeMetaData.ABParams,
				entity.RuntimeParametersAgentConfigVersionID: session.RuntimeMetaData.AgentConfigVersionID,
				entity.RuntimeParametersAgentConfigName:      session.RuntimeMetaData.AgentConfigName,
			},
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create agent run")
	}

	err = s.credentialCache.CacheCodebaseUserJWT(ctx, opt.User.Username, opt.User.CodebaseUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache codebase user JWT: %v", err)
	}
	err = s.credentialCache.CacheNextCodeUserJWT(ctx, opt.User.Username, opt.User.NextCodeUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache next code token: %v", err)
	}
	err = s.credentialCache.CacheCloudUserJWT(ctx, opt.User.Username, opt.User.CloudUserJWT)
	if err != nil {
		log.V1.CtxError(ctx, "failed to cache cloud user JWT: %v", err)
	}

	return run, nil
}

type InitAgentRunOption struct {
	// if assignment is newly created, pass the created assignment so we don't need to read from master DB again,
	// and avoid sync latency between DBs causing problem
	Assignment *entity.Assignment
	SessionID  string
	RunID      string
}

// InitAgentRun send initial message to runtime process, and blocks until agent run finished
func (s *Service) InitAgentRun(ctx context.Context, opt InitAgentRunOption) (err error) {
	defer func() {
		if r := recover(); r != nil {
			log.V1.CtxError(ctx, "failed to run agent: %v", r)
			err = errors.Errorf("failed to run agent: %v", r)
		}
		if err != nil {
			s.UpdateAgentRunState(ctx, UpdateAgentRunStateOption{
				RunID:  opt.RunID,
				Status: lo.ToPtr(entity.AgentRunStatusFailed),
				Error:  lo.ToPtr(err.Error()),
			})
		}
	}()

	session, err := s.WaitRuntimeWorkspace(ctx, WaitRuntimeWorkspaceOption{
		SessionID: opt.SessionID,
		Timeout:   5 * time.Minute,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to wait runtime workspace: %v", err)
		return errors.WithMessage(err, "failed to wait runtime workspace to be ready")
	}
	var cli *eventbus.RuntimeServiceClient
	connect := func() error {
		cli, err = s.connect(ctx, connectOption{
			RuntimeMetadata: session.RuntimeMetadata,
			Relay:           s.relayConfig.GetValue().RelayURL,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to connect to runtime process: %v", err)
			return errors.WithMessage(err, "failed to connect to runtime process")
		}
		return nil
	}
	err = backoff.Retry(connect, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect to runtime process: %v", err)
		return errors.WithMessage(err, "failed to connect to runtime process")
	}
	defer cli.Close()
	conf := s.GetAgentConfig(session.Agent, session.Version)
	if conf == nil {
		log.V1.CtxError(ctx, "agent version %s@%s is not found", session.Agent, session.Version)
		return errors.Errorf("agent version %s@%s is not found", session.Agent, session.Version)
	}
	if opt.Assignment == nil {
		run, err := s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
			RunID: opt.RunID,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get agent run: %v", err)
			return errors.WithMessage(err, "failed to get agent run")
		}
		assignment, err := s.serverDao.GetAssignment(ctx, serverdal.GetAssignmentOption{
			AssignmentID: run.AssignmentID,
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to get assignment: %v", err)
			return errors.WithMessage(err, "failed to get assignment")
		}
		if assignment.Type == entity.AssignmentTypeBatch {
			log.V1.CtxInfo(ctx, "init agent run with batch assignment: %+v, stack: %s", assignment, string(debug.Stack()))
			assignment, err = s.serverDao.StartNextAssignment(ctx, serverdal.StartNextAssignmentOption{
				BatchID: assignment.ID,
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to start next assignment: %v", err)
				return errors.WithMessage(err, "failed to start next assignment")
			}
		}
		opt.Assignment = assignment
	}
	codebaseUserJWT, err := s.credentialCache.GetCodebaseJWT(ctx, opt.Assignment.Creator.Username)
	if err != nil {
		log.V1.CtxError(ctx, "credential has expired: %v", err)
		return errors.WithMessage(err, "user credential has expired")
	}
	nextCodeUserJWT, err := s.credentialCache.GetNextCodeUserJWT(ctx, opt.Assignment.Creator.Username)
	if err != nil {
		log.V1.CtxError(ctx, "nextcode credential has expired: %v", err)
	}
	cloudUserJWT, _ := s.credentialCache.GetCloudUserJWT(ctx, opt.Assignment.Creator.Username)

	_, err = s.UpdateAgentRunState(ctx, UpdateAgentRunStateOption{
		RunID:  opt.RunID,
		Status: lo.ToPtr(entity.AgentRunStatusRunning),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update agent run state: %v", err)
	}
	agentRunConfig := conf.RuntimeConfig.AgentRunConfig
	if agentRunConfig.ExtraOptions == nil {
		agentRunConfig.ExtraOptions = make(map[string]any)
	}
	log.V1.CtxInfo(ctx, "send initial message to runtime process: %v", opt.Assignment.Parameters)
	agentName := lo.Ternary(conf.RuntimeAgentID != "", conf.RuntimeAgentID, session.Agent)
	err = cli.RunAgentWait(entity.RunAgentRequest{
		AgentName:    agentName,
		AssignmentID: opt.Assignment.ID,
		SessionID:    opt.SessionID,
		RunID:        opt.RunID,
		User:         session.CreatorID,
		Parameters:   opt.Assignment.Parameters,
		Environ: map[string]string{
			entity.RuntimeEnvironLLMToken:            lo.Ternary(conf.RuntimeConfig.InjectOpenAIToken, s.getOpenAIToken(), ""),
			entity.RuntimeEnvironUserCodebaseJWT:     codebaseUserJWT,
			entity.RuntimeEnvironUserCloudJWT:        cloudUserJWT,
			entity.RuntimeNextCodeAppID:              s.nextCodeConfig.GetValue().AppID,
			entity.RuntimeNextCodeSecret:             s.nextCodeConfig.GetValue().AppSecret,
			entity.RuntimeNextCodeUserJWT:            nextCodeUserJWT,
			entity.RuntimeDeepWikiNextCodeAppID:      s.deepWikiConfig.GetValue().NextCodeAppID,
			entity.RuntimeDeepWikiNextCodeSecret:     s.deepWikiConfig.GetValue().NextCodeAppSecret,
			entity.RuntimeDeepWikiCodebaseServiceJWT: s.deepWikiConfig.GetValue().CodebaseServiceJWT,
		},
		Config:       lo.ToPtr(agentRunConfig),
		ExitOnFinish: false,
	})
	if err != nil {
		// connection is lost, the runtime will try to find a new server to connect
		if errors.Is(err, jsonrpc2.ErrClosed) {
			return nil
		}
		log.V1.CtxError(ctx, "failed to send run agent process: %+v", err)
		return err
	} else {
		log.V1.CtxInfo(ctx, "sent initial message to runtime process")
	}
	return nil
}

type ProcessEventsForAgentRunOption struct {
	SessionID string
	RunID     string
	Reconnect bool
}

func (s *Service) ProcessEventsForAgentRun(ctx context.Context, opt ProcessEventsForAgentRunOption) error {
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID, Sync: true})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session %s: %v", opt.SessionID, err)
		return err
	}

	offset, err := s.redis.Get(ctx, s.agentRunEventOffsetKey(opt.RunID))
	if err != nil && !redis.IsNil(err) {
		log.V1.CtxWarn(ctx, "failed to get run events offset: %v, use 0 as default", err)
	}
	var offsetInt int64
	if offset != "" {
		offsetInt, err = strconv.ParseInt(offset, 10, 64)
		if err != nil {
			log.V1.CtxError(ctx, "failed to parse run events offset: %v", err)
		}
	}

	log.V1.CtxInfo(ctx, "start to process events for agent run: %s, event offset: %d", opt.RunID, offsetInt)
	recv, err := s.getAgentEventStream(ctx, getAgentEventStreamOption{
		RuntimeMetadata: session.RuntimeMetadata,
		RunID:           opt.RunID,
		SessionID:       opt.SessionID,
		AgentID:         session.Agent,
		AgentVersion:    session.Version,
		OperateDB:       true,
		EventOffset:     offsetInt,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get agent event stream: %v", err)
		return err
	}
	recv.Close()
	return nil
}

type getAgentEventStreamOption struct {
	Cli             *eventbus.RuntimeServiceClient
	RuntimeMetadata entity.RuntimeMetadata
	RunID           string
	SessionID       string
	AgentID         string
	AgentVersion    string
	OperateDB       bool
	EventOffset     int64 // retrieve events since this index
}

func (s *Service) getAgentEventStream(ctx context.Context, opt getAgentEventStreamOption) (recv *stream.RecvChannel[entity.SessionDataStreamEvent], err error) {
	cli := opt.Cli
	if cli == nil {
		cli, err = s.connect(ctx, connectOption{RuntimeMetadata: opt.RuntimeMetadata, Relay: s.relayConfig.GetValue().RelayURL})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to connect to runtime process")
		}
	}
	busURI, err := cli.CtxCreateAgentEventStream(ctx, opt.RunID, opt.OperateDB)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create agent event stream")
	}

	uri, host := s.runtimeProviderRegistry.ResolveURI(ctx, opt.RuntimeMetadata.RuntimeProvider, opt.RuntimeMetadata.TenantKey, opt.RuntimeMetadata.ContainerHost, opt.RuntimeMetadata.ContainerID, busURI)
	if host != "" {
		_, err = s.serverDao.UpdateSessionFn(ctx, opt.SessionID, func(session *entity.Session) error {
			session.RuntimeMetadata.ContainerHost = host
			return nil
		})
		if err != nil {
			log.V1.CtxError(ctx, "failed to update session runtime metadata: %v", err)
		}
	}

	bus, err := remotebus.Connect(uri, remotebus.WithRelay(s.relayConfig.GetValue().RelayURL), remotebus.WithName("datastream"))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent event stream")
	}
	bus.HandleDefaultRoutes() // register keepalive handler
	events := eventbus.NewAgentEventStream(bus, opt.RunID, opt.EventOffset)

	currentWorkspaceState, err := initializeWorkspaceState(ctx, s, opt.SessionID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to initialize workspace state")
	}
	messageNumber, err := initializeMessageNumber(ctx, s, opt.SessionID)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to initialize message number")
	}

	processor := NewRuntimeEventProcessor(opt, currentWorkspaceState, messageNumber, s.idGen)

	send, recv := stream.NewChannel[entity.SessionDataStreamEvent](20)

	if opt.OperateDB {
		events.OnLog(func(level logrus.Level, content string) {
			switch level {
			case logrus.DebugLevel:
				log.V1.CtxDebug(ctx, content)
			case logrus.InfoLevel:
				log.V1.CtxInfo(ctx, content)
			case logrus.WarnLevel:
				log.V1.CtxWarn(ctx, content)
			case logrus.ErrorLevel:
				log.V1.CtxError(ctx, content)
			default:
				log.V1.CtxInfo(ctx, content)
			}
		})
	}

	events.OnEvent(ctx, func(event *iris.AgentRunEvent[any]) {
		var err error
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "panic: %v", r)
				// we can still continue to process other events
			}
			if err != nil && opt.OperateDB {
				_, updateErr := s.UpdateAgentRunState(ctx, UpdateAgentRunStateOption{
					RunID:  opt.RunID,
					Status: lo.ToPtr(entity.AgentRunStatusFailed),
					Error:  lo.ToPtr(err.Error()),
				})
				if updateErr != nil {
					log.V1.CtxWarn(ctx, "failed to update session status to failed: %+v", err)
				}
				return
			}
		}()
		events, ops := processor.ProcessEvents(ctx, event.Event.String(), event.Data)
		for _, e := range events {
			send.Publish(e)
		}
		if opt.OperateDB {
			// for db operations
			for _, op := range ops {
				switch t := op.(type) {
				case *serverdal.CreateMessageOption:
					_, err = s.serverDao.CreateMessage(ctx, *t)
				case *serverdal.UpdateMessageOption:
					_, err = s.serverDao.UpdateMessage(ctx, *t)
				case *serverdal.UpsertWorkspaceComponentStateOption:
					_, err = s.serverDao.UpsertWorkspaceComponentState(ctx, *t)
				case *serverdal.UpdateSessionOption:
					_, err = s.serverDao.UpdateSession(ctx, *t)
				case *serverdal.UpdateAssignmentOption:
					_, err = s.serverDao.UpdateAssignment(ctx, *t)
				case *serverdal.UpdateAssignmentBySessionOption:
					_, err = s.serverDao.UpdateAssignmentBySession(ctx, *t)
				case *runtimedal.CreateAgentRunToolCallOption:
					_, err = s.dao.CreateAgentRunToolCall(ctx, *t)
				case *runtimedal.UpdateAgentRunOption:
					_, err = s.dao.UpdateAgentRun(ctx, *t)
				}
				if err != nil {
					send.PublishError(errors.WithMessage(err, "failed to operate db"), true)
					continue
				}
			}
			if event.Offset >= 0 {
				// store the next index to consume
				s.redis.Set(ctx, s.agentRunEventOffsetKey(opt.RunID), event.Offset+1, time.Hour*24*7)
			}
		}

		if t, ok := event.Data.(*iris.EventAgentRunCompleted); ok {
			log.V1.CtxInfo(ctx, "agent run completed, run ID: %s, error: %s", t.RunID, t.Error)
			if len(t.Error) != 0 {
				send.PublishError(errors.Errorf("agent run failed: %s", t.Error), false)
			}
			send.Close()
			if !opt.OperateDB {
				return
			}

			_ = cli.AbortAgent(entity.AbortRunRequest{RunID: opt.RunID})
			cli.Close()
			s.UpdateAgentRunState(ctx, UpdateAgentRunStateOption{
				RunID:  opt.RunID,
				Status: lo.ToPtr(lo.Ternary(len(t.Error) == 0, entity.AgentRunStatusCompleted, entity.AgentRunStatusFailed)),
				Error:  lo.Ternary(t.Error != "", lo.ToPtr(t.Error), nil),
			})
			// We should immediately stop container to release resources, as there may be other queued assignments requesting CPU/Mem quota in K8s
			// the logic is implemented in AssisnmentStatusUpdate event handler to support ReuseWorkspace
			// provider := s.runtimeProviderRegistry.GetProvider(opt.RuntimeMetadata.RuntimeProvider)
			// if provider == nil {
			// 	return
			// }
			// stopErr := provider.StopContainer(ctx, opt.RuntimeMetadata.ContainerID)
			// if stopErr != nil {
			// 	log.V1.CtxError(ctx, "failed to stop container: %s", err)
			// }
			// s.UpdateRuntimeState(ctx, UpdateRuntimeStateOption{
			// 	SessionID: opt.SessionID,
			// 	Status:    lo.ToPtr(entity.SessionStatusStopped),
			// })
		}
	})

	if err := cli.RetrieveEvents(events); err != nil {
		cli.Close()
		send.Close()
		return nil, errors.WithMessage(err, "failed to retrieve events")
	}

	return recv, nil
}

type waitRuntimeOption struct {
	SessionID       string
	Status          entity.SessionStatus
	Callback        func(session *entity.Session) error
	TimeoutCallback func() error
}

func (s *Service) waitUntilRuntimeReady(ctx context.Context, opt waitRuntimeOption) {
	log.V1.CtxInfo(ctx, "start to wait session %s to be %s", opt.SessionID, opt.Status)
	tryFunc := func() (*entity.Session, error) {
		session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
		if err != nil {
			return nil, backoff.Permanent(errors.WithMessage(err, "failed to get session"))
		}

		if session.Status != opt.Status {
			return nil, errors.Errorf("runtime status is still %s, expect %s", session.Status, opt.Status)
		}

		return session, nil
	}

	// TODO: use constant backoff for the fist 30s to provide service ASAP and exponential backoff for the rest
	ticker := backoff.NewTicker(
		backoff.NewConstantBackOff(time.Second * 1),
	)
	defer ticker.Stop()
	timeout := time.After(time.Minute * 5)
	for {
		select {
		case <-ticker.C:
			session, err := tryFunc()
			if err != nil {
				if errors.Is(err, backoff.Permanent(errors.New(""))) {
					log.V1.CtxWarn(ctx, "failed to wait session: %s, err: %+v", opt.SessionID, err)
					return
				}
				log.V1.CtxInfo(ctx, "still waiting session: %s, err: %v", opt.SessionID, err)
				continue
			}
			if err := opt.Callback(session); err != nil {
				log.V1.CtxWarn(ctx, "failed to callback session: %s, err: %+v", opt.SessionID, err)
				return
			}
			return
		case <-timeout:
			log.V1.CtxError(ctx, "failed to wait session: %s, timeout", opt.SessionID)
			if err := opt.TimeoutCallback(); err != nil {
				log.V1.CtxWarn(ctx, "failed to timeout callback session: %s, err: %+v", opt.SessionID, err)
				return
			}
			return
		}
	}
}

type waitAgentOption struct {
	RunID  string
	Status []entity.AgentRunStatus
}

func (s *Service) waitUtilAgentRunning(ctx context.Context, opt waitAgentOption) (run *entity.AgentRun, err error) {
	ticker := backoff.NewTicker(
		backoff.NewConstantBackOff(time.Second * 1),
	)
	defer ticker.Stop()
	timeout := time.After(time.Minute * 5)
	for {
		select {
		case <-ticker.C:
			run, err = s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
				RunID: opt.RunID,
			})
			if err != nil {
				if errors.Is(err, backoff.Permanent(errors.New(""))) {
					log.V1.CtxWarn(ctx, "failed to wait session: %s, err: %+v", opt.RunID, err)
					return
				}
				log.V1.CtxInfo(ctx, "still waiting session: %s, err: %v", opt.RunID, err)
				continue
			}
			if lo.Contains(opt.Status, run.Status) {
				return run, nil
			}

		case <-timeout:
			log.V1.CtxError(ctx, "failed to wait session: %s, timeout", opt.RunID)
			return nil, errors.Errorf("failed to wait agent run: %s, timeout", opt.RunID)
		}
	}
}

type UpdateRuntimeStateOption struct {
	SessionID string

	// set and upted by runtime
	Status                 *entity.SessionStatus
	RuntimeProcessURI      *string
	RuntimeProcessPID      *int
	RuntimeProcessInitCost time.Duration
	RuntimeProcessMessage  *string
	RuntimeWorkspacePath   *string

	// set and updated by server
	RuntimeProviderType *entity.RuntimeProviderType
	RuntimeContainerID  *string

	TenantKey *string
}

func (s *Service) UpdateRuntimeState(ctx context.Context, opt UpdateRuntimeStateOption) (*entity.Session, error) {
	return s.serverDao.UpdateSessionFn(ctx, opt.SessionID, func(o *entity.Session) error {
		// If the agent run is already failed, do not update the status.
		if opt.Status != nil {
			o.Status = lo.FromPtr(opt.Status)
		}
		if opt.RuntimeProcessURI != nil {
			o.RuntimeMetadata.URI = *opt.RuntimeProcessURI
		}
		if opt.RuntimeProcessPID != nil {
			o.RuntimeMetadata.PID = *opt.RuntimeProcessPID
		}
		if opt.RuntimeProcessInitCost != 0 {
			o.RuntimeMetadata.InitTimeCost = opt.RuntimeProcessInitCost.Milliseconds()
		}
		if opt.RuntimeProcessMessage != nil {
			o.RuntimeMetadata.LastReportMessage = *opt.RuntimeProcessMessage
		}
		if opt.RuntimeProviderType != nil {
			o.RuntimeMetadata.RuntimeProvider = *opt.RuntimeProviderType
		}
		if opt.RuntimeContainerID != nil {
			o.RuntimeMetadata.ContainerID = *opt.RuntimeContainerID
		}
		if opt.RuntimeWorkspacePath != nil {
			o.RuntimeMetadata.WorkspacePath = *opt.RuntimeWorkspacePath
		}
		if opt.TenantKey != nil {
			o.RuntimeMetadata.TenantKey = *opt.TenantKey
		}
		return nil
	})
}

type UpdateAgentRunStateOption struct {
	RunID  string
	Status *entity.AgentRunStatus
	Error  *string
}

func (s *Service) UpdateAgentRunState(ctx context.Context, opt UpdateAgentRunStateOption) (*entity.AgentRun, error) {
	var originalRun *entity.AgentRun
	run, err := s.dao.UpdateAgentRunFunc(ctx, opt.RunID, func(run *entity.AgentRun) {
		// create a copy before modifying its status
		originalRun = lo.ToPtr(lo.FromPtr(run))
		if run.Status.IsStopped() {
			log.V1.CtxInfo(ctx, "agent run status has already finished with `%s`, cannot be updated", run.Status)
			return
		}
		if opt.Status != nil {
			run.Status = lo.FromPtr(opt.Status)
		}
		if opt.Error != nil {
			run.Detail.FailureReason = opt.Error
		}
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update agent run")
	}

	// update corresponding assignment and parent assignment status
	_, err = s.serverDao.UpdateAssignment(ctx, serverdal.UpdateAssignmentOption{
		ID:     run.AssignmentID,
		Status: lo.ToPtr(opt.Status.AssignmentStatus()),
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to update assignment status, err: %v", err)
	}
	// will notify user assignment status via lark bot, avoid duplicate calls
	err = s.assignmentMonitorMQClient.SendMessage(ctx, conv.JSONBytes(entity.AssignmentMonitoringEvent{
		AssignmentStatusUpdate: &entity.AssignmentStatusUpdate{
			AssignmentID: run.AssignmentID,
			Status:       opt.Status.AssignmentStatus(),
		},
	}), entity.AssignmentMonitoringTag)
	log.V1.CtxInfo(ctx, "updated assignment(%s) status to `%s` for agent run(%s): %v", run.AssignmentID, run.Status, run.ID, err)

	if !originalRun.Status.IsStopped() && opt.Status.IsStopped() {
		assignment, err := s.serverDao.GetAssignment(ctx, serverdal.GetAssignmentOption{
			AssignmentID: run.AssignmentID,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get assignment")
		}
		session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: run.SessionID})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get session")
		}
		// release semaphore on agent completed
		s.ReleaseRuntimeSemaphore(ctx, AcqRelSemaphoreOption{
			Agent:             session.Agent,
			Version:           session.Version,
			BatchAssignmentID: assignment.ParentID,
		})
		recycleTimeout := 14 * 24 * time.Hour
		config := s.GetAgentConfig(session.Agent, session.Version)
		if config != nil {
			timeout, err := time.ParseDuration(config.OrchestrationConfig.RecycleTimeout)
			if err == nil {
				recycleTimeout = timeout
			}
		}
		s.orchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
			RecycleContainerEvent: &entity.RecycleContainerEvent{AssignmentID: run.AssignmentID, SessionID: run.SessionID},
		}), recycleTimeout, entity.RuntimeOrchestrationTag)
	}

	return run, nil
}

type AttachAgentRunDataStreamOption struct {
	SessionID    string
	AgentID      string
	AgentVersion string
}

type AgentRunEvent struct {
	Thinking         *iris.EventAgentThink
	StepUpdate       *iris.EventAgentStep
	ToolCall         *iris.EventAgentToolCall
	ToolCallRequired *iris.EventAgentToolCallRequired
	Completed        *iris.EventAgentRunCompleted
	ShellUpdate      *iris.EventShellStdio
	ThoughtDelta     *iris.EventAgentThinkDelta
}

func (s *Service) AttachAgentRunDataStream(ctx context.Context, opt AttachAgentRunDataStreamOption) (*stream.RecvChannel[entity.SessionDataStreamEvent], error) {
	run, err := s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get agent run")
	}
	log.V1.CtxInfo(ctx, "attach agent run data stream, run ID: %s, status: %s", run.ID, run.Status)
	if run.Status.IsStopped() {
		if run.Detail.FailureReason != nil {
			return nil, errors.WithMessagef(ErrAgentStopped, "agent exited with error: %s", *run.Detail.FailureReason)
		}
		return nil, ErrAgentStopped
	}
	if run.Status != entity.AgentRunStatusRunning {
		run, err = s.waitUtilAgentRunning(ctx, waitAgentOption{
			RunID:  run.ID,
			Status: []entity.AgentRunStatus{entity.AgentRunStatusRunning, entity.AgentRunStatusCompleted, entity.AgentRunStatusFailed, entity.AgentRunStatusCanceled},
		})
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to wait agent to start")
	}

	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}
	recv, err := s.getAgentEventStream(ctx, getAgentEventStreamOption{
		RuntimeMetadata: session.RuntimeMetadata,
		RunID:           run.ID,
		SessionID:       opt.SessionID,
		AgentID:         opt.AgentID,
		AgentVersion:    opt.AgentVersion,
		OperateDB:       false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to connect to runtime process")
	}

	return recv, nil
}

func (s *Service) CancelAgentRun(ctx context.Context, sessionID string) error {
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: sessionID})
	if err != nil {
		return errors.WithMessage(err, "failed to get session")
	}
	if !session.Status.IsReady() {
		log.V1.CtxInfo(ctx, "session status is `%s`, cannot be canceled", session.Status)
		return nil
	}

	run, err := s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: sessionID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}
	if run.Status.IsStopped() {
		log.V1.CtxInfo(ctx, "agent run status is `%s`, cannot be canceled", run.Status)
		return nil
	}

	// Try to abort agent run and process gracefully, but if it fails, we will still stop the container and update the status.
	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: session.RuntimeMetadata,
		Relay:           s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to connect to runtime process, err: %+v", err)
	} else {
		defer cli.Close()

		abortErr := cli.AbortAgent(entity.AbortRunRequest{
			RunID: run.ID,
		})
		if abortErr != nil {
			log.V1.CtxWarn(ctx, "failed to abort agent run, err: %+v", abortErr)
		} else {
			log.V1.CtxInfo(ctx, "actively aborted agent run %s", run.ID)
		}
	}

	stopContainerErr := s.runtimeProviderRegistry.GetProvider(session.RuntimeMetadata.RuntimeProvider).StopContainer(ctx, run.RuntimeMetadata.TenantKey, session.RuntimeMetadata.ContainerID)
	if stopContainerErr != nil {
		log.V1.CtxWarn(ctx, "failed to stop runtime container, err: %+v", stopContainerErr)
	} else {
		log.V1.CtxInfo(ctx, "actively stopped runtime container %s", session.RuntimeMetadata.ContainerID)
	}

	_, err = s.UpdateAgentRunState(ctx, UpdateAgentRunStateOption{
		RunID:  run.ID,
		Status: lo.ToPtr(entity.AgentRunStatusCanceled),
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to update agent run status to canceled, err: %+v", err)
	}

	return nil
}

type RecycleRuntimeOption struct {
	SessionID string
}

func (s *Service) RecycleRuntime(ctx context.Context, opt RecycleRuntimeOption) error {
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session %s: %v", opt.SessionID, err)
		return err
	}
	provider := s.runtimeProviderRegistry.GetProvider(session.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		log.V1.CtxError(ctx, "failed to get runtime provider %s", session.RuntimeMetadata.RuntimeProvider)
		return errors.Errorf("unknown runtime provider type: %s", session.RuntimeMetadata.RuntimeProvider)
	}
	err = provider.DeleteContainer(ctx, session.RuntimeMetadata.TenantKey, session.RuntimeMetadata.ContainerID)
	// ignore errors for non-strato-cube providers
	// local & docker containers are not shared across machines, and returning error causes the MQ to retry
	if err != nil && lo.Contains([]entity.RuntimeProviderType{entity.RuntimeProviderTypeStratoCube}, session.RuntimeMetadata.RuntimeProvider) {
		log.V1.CtxError(ctx, "failed to delete runtime container %s: %v", session.RuntimeMetadata.ContainerID, err)
		return err
	}
	log.V1.CtxInfo(ctx, "runtime container %s@%s deleted", session.RuntimeMetadata.ContainerID, session.RuntimeMetadata.RuntimeProvider)
	_, err = s.UpdateRuntimeState(ctx, UpdateRuntimeStateOption{
		SessionID: opt.SessionID,
		Status:    lo.ToPtr(entity.SessionStatusStopped),
	})
	return err
}

type WaitRuntimeWorkspaceOption struct {
	SessionID string
	Timeout   time.Duration
}

func (s *Service) WaitRuntimeWorkspace(ctx context.Context, opt WaitRuntimeWorkspaceOption) (session *entity.Session, err error) {
	ctx, cancel := context.WithTimeout(ctx, opt.Timeout)
	defer cancel()

	// This may be immediately called after CreateSession, when the session record is not yet synced
	session, err = s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID, Sync: true})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}
	if session.Status.IsReady() {
		return session, nil
	}

	s.waitUntilRuntimeReady(ctx, waitRuntimeOption{
		SessionID: opt.SessionID,
		Status:    entity.SessionStatusRunning,
		Callback: func(result *entity.Session) error {
			session = result
			if result.Status == entity.SessionStatusStopped {
				err = errors.New("runtime process has stopped: " + result.RuntimeMetadata.LastReportMessage)
				return err
			}
			return nil
		},
		TimeoutCallback: func() error {
			return errors.New("timeout")
		},
	})

	return session, err
}

type StartRuntimeWorkspaceOption struct {
	SessionID string
	TenantKey string
	Timeout   time.Duration
}

func (s *Service) StartRuntimeWorkspace(ctx context.Context, opt StartRuntimeWorkspaceOption) (*entity.Session, error) {
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
	if err != nil {
		return nil, err
	}

	if session.Status.IsReady() {
		return session, nil
	}

	if session.Status.IsStopped() {
		provider := s.runtimeProviderRegistry.GetProvider(session.RuntimeMetadata.RuntimeProvider)
		if provider == nil {
			return nil, errors.New("runtime provider not found")
		}
		err := provider.StartContainer(ctx, session.RuntimeMetadata.TenantKey, session.RuntimeMetadata.ContainerID)
		if err != nil {
			return nil, err
		}
	}

	return s.WaitRuntimeWorkspace(ctx, WaitRuntimeWorkspaceOption{
		SessionID: opt.SessionID,
		Timeout:   lo.Ternary(opt.Timeout > 0, opt.Timeout, time.Minute*5),
	})
}

type StopRuntimeWorkspaceOption struct {
	SessionID string
}

func (s *Service) StopRuntimeWorkspace(ctx context.Context, opt StopRuntimeWorkspaceOption) error {
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
	if err != nil {
		return err
	}
	if session.Status.IsStopped() {
		return nil
	}

	provider := s.runtimeProviderRegistry.GetProvider(session.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		return errors.New("runtime provider not found")
	}

	err = provider.StopContainer(ctx, session.RuntimeMetadata.TenantKey, session.RuntimeMetadata.ContainerID)
	if err != nil {
		return errors.WithMessage(err, "failed to stop workspace")
	}
	log.V1.CtxInfo(ctx, "runtime container %s@%s stopped", session.RuntimeMetadata.ContainerID, session.RuntimeMetadata.RuntimeProvider)
	_, err = s.UpdateRuntimeState(ctx, UpdateRuntimeStateOption{
		SessionID: opt.SessionID,
		Status:    lo.ToPtr(entity.SessionStatusStopped),
	})
	return errors.WithMessage(err, "failed to stop workspace")
}

type SyncPatchArtifactOption struct {
	User       *authentity.Account
	SessionID  string
	ArtifactID string
}

func (s *Service) SyncPatchArtifact(ctx context.Context, opt SyncPatchArtifactOption) (*agentsphere.UpdateArtifactResponse, error) {
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}
	if session.Status.IsStopped() {
		log.V1.CtxInfo(ctx, "runtime %s is stopped, restarting to sync patch artifact", session.ID)
		_, err := s.StartRuntimeWorkspace(ctx, StartRuntimeWorkspaceOption{
			SessionID: opt.SessionID,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to start workspace")
		}
	}
	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: session.RuntimeMetadata,
		Relay:           s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to connect to runtime process")
	}
	defer cli.Close()
	resp, err := cli.SyncPatchArtifact(entity.SyncPatchArtifactRequest{
		ArtifactID:      opt.ArtifactID,
		CodebaseUserJWT: opt.User.CodebaseUserJWT,
	})
	return resp, errors.WithMessage(err, "failed to sync patch artifact")
}

type SubmitToolCallResultsOption struct {
	SessionID string
	ID        string
	Results   map[string]any
	Err       *string
}

func (s *Service) SubmitToolCallResults(ctx context.Context, opt SubmitToolCallResultsOption) error {
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
	if err != nil {
		return errors.WithMessage(err, "failed to get session")
	}
	run, err := s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: opt.SessionID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run")
	}

	toolCall, err := s.dao.GetAgentRunToolCallByID(ctx, opt.ID)
	if err != nil {
		return errors.WithMessage(err, "failed to get agent run tool call")
	}

	status := entity.AgentRunToolCallStatusCompleted
	if opt.Err != nil {
		status = entity.AgentRunToolCallStatusFailed
		log.V1.CtxInfo(ctx, "tool call %s is failed, err: %s", opt.ID, *opt.Err)
	}

	toolCall, err = s.dao.UpdateAgentRunToolCall(ctx, runtimedal.UpdateAgentRunToolCallOption{
		ID:      toolCall.ID,
		Status:  lo.ToPtr(status),
		Results: opt.Results,
		Error:   opt.Err,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update agent run tool call")
	}

	cli, err := s.connect(ctx, connectOption{
		RuntimeMetadata: session.RuntimeMetadata,
		Relay:           s.relayConfig.GetValue().RelayURL,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to connect to runtime process")
	}
	defer cli.Close()
	err = cli.SubmitToolCallResults(entity.SubmitToolCallResultRequest{
		RunID:   run.ID,
		CallID:  toolCall.ID,
		Name:    toolCall.Name,
		Results: opt.Results,
		Error:   opt.Err,
	})

	log.V1.CtxInfo(ctx, "submitted tool call results, runID: %s, toolCallID: %s, err: %v", run.ID, toolCall.ID, err)
	return err
}

type CreateRuntimeForSessionOption struct {
	SessionID  string
	Config     config.AgentConfig
	NextConfig *nextentity.AgentConfigVersion
	// for created containers, this is empty
	Account     *authentity.Account
	APIBaseURL  string
	SessionType string
}

type CreateRuntimeForSessionResult struct {
	Inst      *runtimedal.RuntimeInstance
	TenantKey string
}

func (s *Service) CreateRuntimeForSession(ctx context.Context, opt CreateRuntimeForSessionOption) (*CreateRuntimeForSessionResult, error) {
	var runtimeType, image, bashImage, binarySource, psm, tenantKey string
	var resourceQuota config.RuntimeResourceQuota
	var envCfgs map[string]string
	var port int
	if opt.NextConfig != nil {
		tenantKey = opt.NextConfig.RuntimeConfig.ID
		runtimeType = string(opt.NextConfig.RuntimeConfig.Type)
		envCfgs = opt.NextConfig.RuntimeConfig.Envs
		port = pointer.Get(opt.NextConfig.RuntimeConfig.Port)
		binarySource = pointer.Get(opt.NextConfig.RuntimeConfig.BinarySource)
		image = pointer.Get(opt.NextConfig.RuntimeConfig.Image)
		bashImage = pointer.Get(opt.NextConfig.RuntimeConfig.BashImage)
		psm = opt.NextConfig.RuntimeConfig.PSM
		if opt.NextConfig.RuntimeConfig.RuntimeResourceQuota != nil {
			quota := opt.NextConfig.RuntimeConfig.RuntimeResourceQuota
			resourceQuota = config.RuntimeResourceQuota{
				CPU: lo.TernaryF(quota.CPU != nil,
					func() *config.ResourceQuantity {
						return &config.ResourceQuantity{
							Requests: quota.CPU.Requests,
							Limits:   pointer.Get(quota.CPU.Limits),
						}
					},
					func() *config.ResourceQuantity { return nil },
				),
				Memory: lo.TernaryF(quota.MEM != nil,
					func() *config.ResourceQuantity {
						return &config.ResourceQuantity{
							Requests: quota.MEM.Requests,
							Limits:   pointer.Get(quota.MEM.Limits),
						}
					},
					func() *config.ResourceQuantity { return nil },
				),
				PersistWorkspace: lo.TernaryF(quota.PersistWorkspace != nil,
					func() *config.ResourceQuantity {
						return &config.ResourceQuantity{
							Requests: quota.PersistWorkspace.Requests,
							Limits:   pointer.Get(quota.PersistWorkspace.Limits),
						}
					},
					func() *config.ResourceQuantity { return nil },
				),
				StorageClass: quota.StorageClass,
			}
		}
	} else {
		tenantKey = lo.Ternary(opt.Config.RuntimeAgentID != "", opt.Config.RuntimeAgentID, opt.Config.Agent)
		runtimeType = opt.Config.RuntimeConfig.Type
		envCfgs = opt.Config.RuntimeConfig.Envs
		port = opt.Config.RuntimeConfig.Port
		binarySource = opt.Config.RuntimeConfig.BinarySource
		image = opt.Config.RuntimeConfig.Image
		psm = opt.Config.RuntimeConfig.PSM
		resourceQuota = opt.Config.RuntimeConfig.Quota
	}

	runtimeProvider := s.runtimeProviderRegistry.GetProvider(entity.RuntimeProviderType(runtimeType))
	if runtimeProvider == nil {
		return nil, errors.Errorf("unknown runtime provider type: %s", runtimeType)
	}

	// copy env variables since config.envs points to original map
	envs := make(map[string]string)
	for k, v := range envCfgs {
		envs[k] = v
	}

	workspacePath := "/workspace/iris_" + opt.SessionID
	if port == 0 {
		// limit port 10000 to 15000
		port = rand.Int()%5000 + 10000
	}

	// 创建容器时传递codebase mention，用于云盘复用方案加速大仓克隆
	codebaseMentions := s.GetCodebaseMentions(ctx, opt.SessionID, opt.Account)

	o := runtimedal.CreateContainerOption{
		SessionID:        opt.SessionID,
		Image:            image,
		BashImage:        bashImage,
		Environ:          envs,
		Port:             port,
		ResourceQuota:    &resourceQuota,
		WorkspacePath:    workspacePath,
		BinarySource:     binarySource,
		APIBaseURL:       opt.APIBaseURL,
		SessionType:      opt.SessionType,
		PSM:              psm,
		CodebaseMentions: codebaseMentions,
	}
	log.V1.CtxInfo(ctx, "creating runtime for session %s, type: %s", opt.SessionID, runtimeType)
	inst, err := runtimeProvider.CreateContainer(ctx, tenantKey, o)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create runtime")
	}
	return &CreateRuntimeForSessionResult{
		Inst:      inst,
		TenantKey: tenantKey,
	}, nil
}

func (s *Service) GetCodebaseMentions(ctx context.Context, sessionID string, account *authentity.Account) []*iris.CodebaseMention {
	log.V1.CtxInfo(ctx, "bigrepo_warmup: try to get codebase mentions for session %s", sessionID)

	var codebaseMentions []*iris.CodebaseMention
	messages, err := s.nextserverDao.ListMessages(ctx, nextserverdal.ListMessageOption{
		SessionID: sessionID,
		Sync:      true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "bigrepo_warmup: list next_message of session %s failed: %+v", sessionID, err)
	} else if len(messages) == 0 {
		log.V1.CtxError(ctx, "bigrepo_warmup: list next_message of session %s empty", sessionID)
	} else {
		log.V1.CtxInfo(ctx, "bigrepo_warmup: list next_message of session %s: %v", sessionID, messages)

		userName := lo.TernaryF(account != nil && account.Username != "", func() string {
			return account.Username
		}, func() string {
			return messages[0].Creator
		})

		codebaseUserJWT := lo.TernaryF(account != nil, func() string {
			return account.CodebaseUserJWT
		}, func() string {
			jwt, _ := s.credentialCache.GetCodebaseJWT(ctx, userName)
			return jwt
		})

		if account == nil {
			account = &authentity.Account{
				Username:        userName,
				CodebaseUserJWT: codebaseUserJWT,
			}
		}

		extractMentionsOption := ExtractMentionsOption{
			SessionID: sessionID,
			Message: &iris.Message{
				ID:      messages[0].ID,
				Content: messages[0].Content.Content,
				Mentions: lo.FilterMap(messages[0].Mentions, func(mention *entity.Mention, _ int) (iris.Mention, bool) {
					if mention == nil {
						return nil, false
					}
					m := iris.GetMention(*mention)
					if m == nil {
						return nil, false
					}
					return m, true
				}),
			},
			Account: account,
		}
		log.V1.CtxInfo(ctx, "bigrepo_warmup: extract mentions option of session %s: %s", sessionID, extractMentionsOption)
		start := time.Now()
		mentions, err := s.ExtractMentions(ctx, extractMentionsOption)
		if err != nil {
			log.V1.CtxError(ctx, "bigrepo_warmup: extract mentions of session %s failed: %+v", sessionID, err)
		} else if len(mentions) == 0 {
			log.V1.CtxInfo(ctx, "bigrepo_warmup: extract mentions of session %s empty", sessionID)
		} else {
			elapsed := time.Since(start).Seconds()
			log.V1.CtxInfo(ctx, "bigrepo_warmup: extract mentions of session %s cost %fs, mentions=%v", sessionID, elapsed, mentions)

			run, err := s.NewRunContext(ctx, NewRunContextOption{
				SessionID: sessionID,
				Config:    &config.AgentRunConfig{},
				Environ: func() *iris.RunEnviron {
					environ := iris.NewRunEnviron()
					environ.Set(entity.RuntimeEnvironUserCodebaseJWT, codebaseUserJWT)
					return environ
				}(),
			})
			if err != nil {
				log.V1.CtxError(ctx, "bigrepo_warmup: fail to create run context for session %s: %+v", sessionID, err)
				return nil
			}

			cli, err := codebase.NewCodebaseClient(run)
			if err != nil {
				log.V1.CtxError(ctx, "bigrepo_warmup: session %s unable to create codebase client: %+v", sessionID, err)
				return nil
			}

			existsRepoName := make(map[string]bool)
			for _, mention := range mentions {
				if mention.GetType() == iris.MentionTypeCodebase {
					codebaseMention, _ := mention.(*iris.CodebaseMention)

					// check user permission
					role, err := cli.GetRepoUserRole(run, userName, codebaseMention.RepoName)
					if err != nil {
						log.V1.CtxError(ctx, "bigrepo_warmup: fail to get repo user role for user %s repo %s session %s: %+v", userName, codebaseMention.RepoName, sessionID, err)
						continue
					}
					clonePermissionRoles := []string{"master", "owner", "developer", "reporter"}
					if !lo.Contains(clonePermissionRoles, role) {
						log.V1.CtxError(ctx, "bigrepo_warmup: user %s do not have permission to clone %s for session %s", run.User.Username, codebaseMention.RepoName, sessionID)
						continue
					}

					// check ok
					if _, ok := existsRepoName[codebaseMention.RepoName]; !ok {
						codebaseMentions = append(codebaseMentions, codebaseMention)
						existsRepoName[codebaseMention.RepoName] = true
					}
				}
			}
		}
	}
	return codebaseMentions
}

type PrepareRuntimeContainerOption struct {
	Agent   string
	Version string
}

// PrepareRuntimeContainer will try to create at most config.OrchestrationConfig.PoolSize containers
func (s *Service) PrepareRuntimeContainer(ctx context.Context, opt PrepareRuntimeContainerOption) error {
	config := s.GetAgentConfig(opt.Agent, opt.Version)
	if config == nil {
		log.V1.CtxWarn(ctx, "agent %s@%s is not found, skip prepare containers", opt.Agent, opt.Version)
		return nil // this agent is removed
	}

	sessions, err := s.serverDao.ListSessions(ctx, serverdal.ListSessionsOption{
		User:  &entity.AgentSpherePlatform,
		Limit: int64(config.OrchestrationConfig.PoolSize),
		StatusIn: []entity.SessionStatus{
			entity.SessionStatusCreated,
			entity.SessionStatusRunning,
		},
		Agent:   opt.Agent,
		Version: opt.Version,
		Offset:  0,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list sessions")
	}
	log.V1.CtxInfo(ctx, "agent %s@%s has %d prepared containers, expected %d", opt.Agent, opt.Version, len(sessions), config.OrchestrationConfig.PoolSize)
	if len(sessions) >= config.OrchestrationConfig.PoolSize {
		log.V1.CtxDebug(ctx, "agent %s@%s has enough containers (%d/%d)", opt.Agent, opt.Version, len(sessions), config.OrchestrationConfig.PoolSize)
		return nil
	}

	wg := conc.NewWaitGroup()
	for i := 0; i < config.OrchestrationConfig.PoolSize-len(sessions); i++ {
		wg.Go(func() {
			session, err := s.serverDao.CreateSession(ctx, serverdal.CreateSessionOption{
				ID:              s.idGen.NewID(),
				Status:          entity.SessionStatus(entity.SessionStatusCreated),
				Creator:         entity.AgentSpherePlatform,
				Agent:           opt.Agent,
				Version:         opt.Version,
				RuntimeMetadata: entity.RuntimeMetadata{},
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to create session: %v", err)
				return
			}
			resp, err := s.CreateRuntimeForSession(ctx, CreateRuntimeForSessionOption{
				SessionID:  session.ID,
				Config:     *config,
				APIBaseURL: fmt.Sprintf("%s%s", s.runtimeAPIConfig.APIBaseURL, s.runtimeAPIConfig.APIPrefix),
			})
			if err != nil {
				log.V1.CtxError(ctx, "failed to allocate runtime: %v", err)
			}
			s.serverDao.UpdateSessionFn(ctx, session.ID, func(o *entity.Session) error {
				o.RuntimeMetadata.ContainerID = resp.Inst.ID
				o.RuntimeMetadata.RuntimeProvider = entity.RuntimeProviderType(config.RuntimeConfig.Type)
				o.RuntimeMetadata.TenantKey = resp.TenantKey
				return nil
			})
		})
	}

	return nil
}

type AllocateRuntimeOption struct {
	BatchID      string // parent assignment id if batch mode is enabled
	AssignmentID string
	SessionID    string
	Agent        string
	Version      string
}

// AllocateRuntime will try to allocate runtime for session
// if max capacity is reached, it sends a deferred message to the runtime queue which will try later
func (s *Service) AllocateRuntime(ctx context.Context, opt AllocateRuntimeOption) error {
	config := s.GetAgentConfig(opt.Agent, opt.Version)
	if config == nil {
		return errors.Errorf("unknown agent %s@%s", opt.Agent, opt.Version)
	}

	// in most cases assignment is new born and we need to ensure it is available
	assignment, err := s.serverDao.GetAssignment(ctx, serverdal.GetAssignmentOption{
		AssignmentID: opt.AssignmentID,
		Sync:         true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get assignment")
	}
	// already running or completed
	if !assignment.Status.IsBorn() {
		return nil
	}

	if config.OrchestrationConfig.MaxConcurrency > 0 {
		ok := s.AcquireRuntimeSemaphore(ctx, AcqRelSemaphoreOption{
			Agent:             opt.Agent,
			Version:           opt.Version,
			BatchAssignmentID: opt.BatchID,
		})
		if !ok {
			log.V1.CtxInfo(ctx, "unable to acquire semaphore for agent %s@%s", config.Agent, config.Version)
			return s.QueueRuntime(ctx, QueueRuntimeOption{BatchID: opt.BatchID, AssignmentID: opt.AssignmentID, SessionID: opt.SessionID})
		}
	}

	s.serverDao.UpdateAssignment(ctx, serverdal.UpdateAssignmentOption{
		ID:     opt.AssignmentID,
		Status: lo.ToPtr(entity.AssignmentStatusCreated),
	})

	timeout, err := time.ParseDuration(config.OrchestrationConfig.Timeout)
	if err != nil {
		timeout = 2 * time.Hour
		log.V1.CtxInfo(ctx, "no timeout is configured or in bad format: %s, using default timeout: %s", config.OrchestrationConfig.Timeout, timeout.String())
	}
	// agent run timeout starts from successful allocation to prevent server-runtime communication error that may hold the resource forever
	s.orchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		AgentRunTimeoutEvent: lo.ToPtr(entity.AgentRunTimeoutEvent{AssignmentID: opt.AssignmentID, Timeout: timeout.String()}),
	}), timeout, entity.RuntimeOrchestrationTag)

	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID, Sync: true})
	if err != nil {
		return errors.WithMessage(err, "failed to get session")
	}
	if len(session.RuntimeMetadata.ContainerID) != 0 {
		log.V1.Info("session %s has a pre-created container, skip runtime creation", session.ID)
		return nil
	}
	resp, err := s.CreateRuntimeForSession(ctx, CreateRuntimeForSessionOption{
		SessionID:  opt.SessionID,
		Config:     *config,
		APIBaseURL: fmt.Sprintf("%s%s", s.runtimeAPIConfig.APIBaseURL, s.runtimeAPIConfig.APIPrefix),
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to create runtime instance for session %s, provider: %s, err: %v", opt.SessionID, config.RuntimeConfig.Type, err)
		return err
	}
	_, err = s.UpdateRuntimeState(ctx, UpdateRuntimeStateOption{
		SessionID:           opt.SessionID,
		RuntimeContainerID:  lo.ToPtr(resp.Inst.ID),
		RuntimeProviderType: lo.ToPtr(entity.RuntimeProviderType(config.RuntimeConfig.Type)),
		TenantKey:           lo.ToPtr(resp.TenantKey),
	})
	log.V1.CtxInfo(ctx, "allocated runtime %s for session %s, instance(%s): %s", resp.Inst.ID, opt.SessionID, entity.RuntimeProviderType(config.RuntimeConfig.Type), resp.Inst.ID)
	// the runtime is now starting, and once it reports ready in runtime api, we'll send run agent request
	return errors.WithMessage(err, "failed to update session")
}

type QueueRuntimeOption struct {
	BatchID      string
	AssignmentID string
	SessionID    string
}

// QueueAssignment will send a message to assignment queue and check later
func (s *Service) QueueRuntime(ctx context.Context, opt QueueRuntimeOption) error {
	// Use a random extra delay between 0 and 5 seconds to prevent messages from the same batch assignment from being processed at the same time
	delay := 5*time.Second + time.Duration(rand.Intn(5000))*time.Millisecond
	return s.orchestratorMQClient.SendDelayedMessage(ctx, conv.JSONBytes(entity.RuntimeOrchestrationEvent{
		RuntimeAllocationEvent: lo.ToPtr(entity.RuntimeAllocationEvent{BatchID: opt.BatchID, AssignmentID: opt.AssignmentID, SessionID: opt.SessionID}),
	}), delay, entity.RuntimeOrchestrationTag)
}

type AcqRelSemaphoreOption struct {
	BatchAssignmentID string
	Agent             string
	Version           string
}

func (s *Service) AcquireRuntimeSemaphore(ctx context.Context, opt AcqRelSemaphoreOption) bool {
	// 两步 acquire：
	// 先从 batch assignment 中获取剩余容量，然后再从 agent semaphore 中获取剩余容量
	// 如果都满足，才可以分配容器；如果 agent semaphore 获取失败，则需要回滚
	if opt.BatchAssignmentID != "" {
		batchOptions, err := s.serverDao.GetBatchOptions(ctx, opt.BatchAssignmentID, false)
		if err != nil {
			return false
		}
		log.V1.CtxInfo(ctx, "batch assignment %s: options %+v", opt.BatchAssignmentID, batchOptions)

		remain, err := s.redisSemaphore.AcquireCounter(ctx, s.assignmentSemaphoreKey(opt.BatchAssignmentID), int64(batchOptions.Concurrency))
		log.V1.CtxInfo(ctx, "acquired semaphore for batch assignment %s: remain %v, err: %v", opt.BatchAssignmentID, remain, err)
		if err != nil {
			return false
		}
	}
	config := s.GetAgentConfig(opt.Agent, opt.Version)
	if config.OrchestrationConfig.MaxConcurrency <= 0 {
		return true
	}

	remain, err := s.redisSemaphore.AcquireCounter(ctx, s.agentSemaphoreKey(opt.Agent, opt.Version), int64(config.OrchestrationConfig.MaxConcurrency))
	log.V1.CtxInfo(ctx, "acquired semaphore for agent %s@%s: remain %v", opt.Agent, opt.Version, remain)

	if err != nil {
		// revert
		releaseErr := s.redisSemaphore.ReleaseCounter(ctx, s.assignmentSemaphoreKey(opt.BatchAssignmentID))
		log.V1.CtxInfo(ctx, "failed to acquire semaphore for agent %s@%s: %v, releasing: %v", opt.Agent, opt.Version, err, releaseErr)
	}

	return err == nil
}

func (s *Service) ReleaseRuntimeSemaphore(ctx context.Context, opt AcqRelSemaphoreOption) error {
	// there's no need to check MaxConcurrency in config, since version may be deleted.
	// we still need to release semaphore anyway, XDecrBy ensures it won't go below 0
	err := s.redisSemaphore.ReleaseCounter(ctx, s.agentSemaphoreKey(opt.Agent, opt.Version))
	if err != nil {
		return err
	}

	if opt.BatchAssignmentID != "" {
		return s.redisSemaphore.ReleaseCounter(ctx, s.assignmentSemaphoreKey(opt.BatchAssignmentID))
	}
	return nil
}

type EnsureRuntimeOption struct {
	SessionID   string
	AgentConfig config.AgentConfig
	User        *authentity.Account
	Run         *entity.AgentRun
}

// EnsureRuntime will create a runtime container if not exist
func (s *Service) EnsureRuntime(ctx context.Context, opt EnsureRuntimeOption) (run *entity.AgentRun, err error) {
	// create agent run container if not exist
	session, err := s.serverDao.GetSession(ctx, serverdal.GetSessionOption{ID: opt.SessionID})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get session")
	}
	if session.RuntimeMetadata.ContainerID != "" {
		return opt.Run, nil
	}

	resp, err := s.CreateRuntimeForSession(ctx, CreateRuntimeForSessionOption{
		SessionID:  opt.SessionID,
		Config:     opt.AgentConfig,
		Account:    opt.User,
		APIBaseURL: fmt.Sprintf("%s%s", s.runtimeAPIConfig.APIBaseURL, s.runtimeAPIConfig.APIPrefix),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create agent run container")
	}
	log.V1.CtxInfo(ctx, "create agent run container success, run ID: %s, container ID: %s", opt.Run.ID, resp.Inst.ID)

	_, err = s.UpdateRuntimeState(ctx, UpdateRuntimeStateOption{
		SessionID:          opt.SessionID,
		RuntimeContainerID: &resp.Inst.ID,
		TenantKey:          &resp.TenantKey,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update agent run")
	}
	return run, nil
}

type connectOption struct {
	RuntimeMetadata entity.RuntimeMetadata
	Relay           string
	ReceiveEvents   bool // if true, caller is responsible for calling eb.Conn().Ready() after register handlers
	TenantKey       string
}

func (s *Service) connect(ctx context.Context, opt connectOption) (result *eventbus.RuntimeServiceClient, err error) {
	tags := &metrics.NextServerConnectAgentTag{
		ContainerID: opt.RuntimeMetadata.ContainerID,
	}
	_ = metrics.NSM.ConnectAgentRate.WithTags(tags).Add(1)
	defer metrics.TraceTimer(metrics.NSM.ConnectAgentTimer.WithTags(tags))()
	defer func() {
		if err != nil {
			_ = metrics.NSM.ConnectAgentError.WithTags(tags).Add(1)
		}
	}()

	provider := s.runtimeProviderRegistry.GetProvider(opt.RuntimeMetadata.RuntimeProvider)
	if provider == nil {
		return nil, errors.Errorf("unknown runtime provider type: %s", opt.RuntimeMetadata.RuntimeProvider)
	}
	uri, _ := provider.ResolveURI(ctx, opt.RuntimeMetadata.TenantKey, opt.RuntimeMetadata.ContainerHost, opt.RuntimeMetadata.ContainerID, opt.RuntimeMetadata.URI)

	eb, err := remotebus.Connect(uri, remotebus.WithRelay(opt.Relay), remotebus.WithName("apiserver"))
	if err != nil {
		log.V1.CtxError(ctx, "failed to connect to runtime process, URI: %s, err: %v", uri, err)
		return nil, errors.WithMessage(err, "runtime is not running")
	}
	if !opt.ReceiveEvents {
		eb.Conn().Ready()
	}
	log.V1.CtxInfo(ctx, "connected to runtime process, URI: %s", uri)
	cli := eventbus.NewRuntimeServiceClient(eb, opt.Relay)
	return cli, nil
}

func (s *Service) GetAgentConfig(agent, version string) *config.AgentConfig {
	agentList := s.agentList.GetValue()
	conf, ok := lo.Find(agentList, func(item config.AgentConfig) bool {
		return item.Agent == agent && item.Version == version
	})
	if !ok {
		return nil
	}
	return &conf
}

// resource key to restrict max concurrent tasks for agent, shared globally by all users
func (s *Service) agentSemaphoreKey(agent, version string) string {
	return fmt.Sprintf("agent:%s@%s", agent, version)
}

// resource key to restrict max concurrent tasks for batch assignment
func (s *Service) assignmentSemaphoreKey(assignmentID string) string {
	return fmt.Sprintf("assignment:%s", assignmentID)
}

func (s *Service) agentRunEventOffsetKey(runID string) string {
	return fmt.Sprintf("run:events:%s", runID)
}

func (s *Service) ListModels(ctx context.Context) ([]*nextagent.Model, error) {
	models := s.modelConfig.GetPointer().Models
	return lo.Map(models, func(m *config.ModelAuthConfig, _ int) *nextagent.Model {
		return &nextagent.Model{
			Type:   string(m.Type),
			Models: m.Models,
		}
	}), nil
}

func (s *Service) IngestTraces(ctx context.Context, req *agentsphere.IngestTracesRequest) error {
	if len(req.Traces) == 0 {
		return nil
	}
	log.V1.CtxInfo(ctx, "ingest traces: %d, session id: %s", len(req.Traces), req.Traces[0].SessionID)
	msgs := lo.Map(req.Traces, func(t *agentsphere.Trace, _ int) []byte {
		// Parse raw trace data as map
		var rawTraceData map[string]interface{}
		if err := json.Unmarshal([]byte(t.TraceData), &rawTraceData); err != nil {
			log.V1.CtxError(ctx, "failed to unmarshal trace data: %v, raw data: %s", err, t.TraceData)
			return nil
		}
		// Extract tags
		tags := make(map[string]string)
		if rawTags, ok := rawTraceData["tags"].(map[string]interface{}); ok {
			for k, v := range rawTags {
				if str, ok := v.(string); ok {
					tags[k] = str
				} else {
					tags[k] = conv.JSONString(v)
				}
			}
		}

		return conv.JSONBytes(nextentity.NextTraceEvent{
			SessionID: t.SessionID,
			TraceID:   t.TraceID,
			TraceData: &nextentity.TraceData{
				SpanID:         conv.DefaultAny[string](rawTraceData["span_id"]),
				ParentSpanID:   conv.DefaultAny[string](rawTraceData["parent_id"]),
				SpanType:       conv.DefaultAny[string](rawTraceData["type"]),
				SpanName:       conv.DefaultAny[string](rawTraceData["name"]),
				SpanStartTime:  int64(conv.DefaultAny[float64](rawTraceData["start_time"])),
				SpanEndTime:    int64(conv.DefaultAny[float64](rawTraceData["end_time"])),
				SpanDurationMs: int64(conv.DefaultAny[float64](rawTraceData["duration_ms"])),
				SpanAttributes: tags,
			},
		})
	})
	msgs = lo.Filter(msgs, func(msg []byte, _ int) bool {
		return msg != nil
	})

	if len(msgs) == 0 {
		return errors.New("all trace messages failed to process")
	}

	// 前提：所有 trace 的 session id 相同
	partitionKey := req.Traces[0].SessionID

	return s.nextTraceMQClient.SendBatchOrderlyMessage(ctx, msgs, partitionKey, entity.NextTraceTag)
}

type GetSDPWebShellURLOption struct {
	Username     string
	ServiceToken string
	CubeID       string
}

func (s *Service) GetSDPWebShellURL(ctx context.Context, opt GetSDPWebShellURLOption) string {
	p := s.runtimeProviderRegistry.GetProvider(entity.RuntimeProviderTypeStratoCube)
	webShellURL, err := p.GetSDPWebShell(ctx, opt.Username, "runtime", opt.ServiceToken, opt.CubeID)
	if err != nil {
		log.V1.CtxError(ctx, "failed get sdp webshell url: %v", err)
		return ""
	}

	return webShellURL
}
