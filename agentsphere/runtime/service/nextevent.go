package runtimeservice

import (
	"code.byted.org/devgpt/kiwis/agentsphere/next_server/service/artifact"
	replayservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/replay"
	"github.com/AlekSi/pointer"

	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	serverservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service"
	larkservice "code.byted.org/devgpt/kiwis/agentsphere/next_server/service/lark"
	"code.byted.org/devgpt/kiwis/agentsphere/runtime/service/runtimestarter"
	authentity "code.byted.org/devgpt/kiwis/copilotstack/common/auth/entity"
	"code.byted.org/devgpt/kiwis/lib/conv"
	"code.byted.org/devgpt/kiwis/lib/metrics"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cenkalti/backoff/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"code.byted.org/devgpt/kiwis/agentsphere/entity"
	nextdal "code.byted.org/devgpt/kiwis/agentsphere/next_server/dal"
	nextentity "code.byted.org/devgpt/kiwis/agentsphere/next_server/entity"
	runtimedal "code.byted.org/devgpt/kiwis/agentsphere/runtime/dal"
	"code.byted.org/devgpt/kiwis/api/idl/hertz_gen/common"
	nextidl "code.byted.org/devgpt/kiwis/api/idl/hertz_gen/nextagent"
	"code.byted.org/devgpt/kiwis/lib/stream"
	"code.byted.org/devgpt/kiwis/port/db"
)

// ProcessNextAgentRunEvents fetch events from runtime service and save to db.
func (s *Service) ProcessNextAgentRunEvents(ctx context.Context, opt ProcessEventsForAgentRunOption) (err error) {
	reportMetricsFunc := metrics.NSM.ReportEventMetrics()
	defer func() {
		reportMetricsFunc("ProcessNextAgentRunEvents", err != nil)
	}()

	event, err := s.nextserverDao.GetLatestEventBySessionID(ctx, opt.SessionID, true)
	if err != nil && !db.IsRecordNotFoundError(err) {
		return errors.WithMessage(err, "failed to get latest event by session id")
	}
	var offset int64
	if event != nil {
		offset = lo.Ternary(event.EventOffset == 0, 0, event.EventOffset+1)
	}
	log.V1.CtxInfo(ctx, "start to process events for agent run: %s, event offset: %d", opt.RunID, offset)

	session, err := s.nextserverDao.GetSession(ctx, nextdal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: false,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get session")
	}

	// 判断是不是唤醒需不需要发送消息
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "check restart message panic: %v", r)
			}
		}()
		if err := s.sendNewMessageWithRestart(ctx, session); err != nil {
			log.V1.CtxError(ctx, "send restart message error: %v", err)
			return
		}
		log.V1.CtxInfo(ctx, "send restart message success")
	}()

	// username 参数留空，离线存储event时不需要用户名
	ch, err := s.FetchSessionEvents(ctx, FetchSessionEventsOption{
		SessionID: opt.SessionID,
		Username:  session.Creator,
		Title:     session.Title,
		Offset:    offset,
		Reconnect: opt.Reconnect,
		IsOffline: true,
		StartTime: session.StartedAt,
	})
	if err != nil {
		if ch != nil {
			for e := range ch.DataChannel { // 处理剩余事件
				s.saveEvents(ctx, []nextentity.EventData{e})
			}
		}
		return err
	}
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "agent run stream panic: %v", r)
			}
		}()
		// TODO: 这里暂时不做批量插入，因为需要保证用户刷新时的事件实时性，后面考虑优化
		const batchSize = 1
		var batchEvents []nextentity.EventData
		for e := range ch.DataChannel {
			// ignore empty event
			if e.Event == "" {
				continue
			}
			log.V1.CtxInfo(ctx, "[ProcessNextAgentRunEvents] received event from runtime process: %v", e.Event)
			batchEvents = append(batchEvents, e)
			if len(batchEvents) >= batchSize {
				err = s.saveEvents(ctx, batchEvents)
				if err != nil {
					log.V1.CtxError(ctx, "failed to save events: %v", err)
				}
				batchEvents = nil
			}
		}
		// 批量插入
		err = s.saveEvents(ctx, batchEvents)
		if err != nil {
			log.V1.CtxError(ctx, "failed to save events: %v", err)
		}
	}()

	return nil
}

func (s *Service) sendNewMessageWithRestart(ctx context.Context, session *nextentity.Session) error {
	latestMessage, err := s.nextserverDao.GetLatestMessageByRole(ctx, session.ID, nextentity.MessageRoleUser, true)
	if err != nil {
		return errors.WithMessage(err, "failed to get latest message")
	}
	if latestMessage.Status == nextentity.MessageStatusWait {
		agentConfig := serverservice.GetGeneralAgentByRole(session.Role, "")
		logID, _ := ctxvalues.LogID(ctx)
		var newCtx context.Context
		if session.RuntimeMetaData.LogID != "" && logID != session.RuntimeMetaData.LogID {
			newCtx = ctxvalues.SetLogID(ctx, session.RuntimeMetaData.LogID)
		} else {
			newCtx = ctx
		}

		codebaseUserJWT, err := s.credentialCache.GetCodebaseJWT(ctx, session.Creator)
		if err != nil {
			log.V1.CtxError(ctx, "codebase credential has expired: %v", err)
		}
		nextCodeUserJWT, err := s.credentialCache.GetNextCodeUserJWT(ctx, session.Creator)
		if err != nil {
			log.V1.CtxError(ctx, "codebase credential has expired: %v", err)
		}
		// cloud user jwt is optional
		cloudUserJWT, err := s.credentialCache.GetCloudUserJWT(ctx, session.Creator)
		if err != nil {
			log.V1.CtxError(ctx, "cloud credential has expired: %v", err)
		}
		larkAPPToken, err := s.nextLarkService.GetUserAccessToken(ctx, session.Creator)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get lark user access token: %v", err)
		}

		operation := func() error {
			attachments := lo.Map(latestMessage.Attachments, func(a *nextentity.Attachment, _ int) entity.AttachmentMeta {
				return entity.AttachmentMeta{
					ArtifactID: a.ID,
					Filename:   a.FileName,
				}
			})
			for _, m := range latestMessage.Mentions { // 将 attachments mention 也作为 attachment 传给 agent
				if m.AttachmentMention != nil {
					attachments = append(attachments, entity.AttachmentMeta{
						ArtifactID: m.AttachmentMention.ArtifactID,
						Filename:   m.AttachmentMention.Path,
					})
				}
			}
			return s.NextSendNewMessage(newCtx, NextSendMessageOption{
				SessionID:   session.ID,
				MessageID:   latestMessage.ID,
				Agent:       agentConfig.Name,
				Version:     agentConfig.Version,
				InitialMsg:  latestMessage.Content.Content,
				Attachments: attachments,
				User: &authentity.Account{
					Username:        session.Creator,
					CodebaseUserJWT: codebaseUserJWT,
					NextCodeUserJWT: nextCodeUserJWT,
					CloudUserJWT:    cloudUserJWT,
				},
				Options: entity.MessageOptions{
					Locale: latestMessage.Options.Locale,
				},
				LarkToken: &larkAPPToken,
				Sync:      true,
			})
		}
		err = backoff.Retry(operation, backoff.WithContext(backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3), ctx))
		if err != nil {
			return errors.WithMessage(err, "failed to send restart new message")
		}

		_, err = s.nextserverDao.UpdateMessage(ctx, nextdal.UpdateMessageOption{
			ID:     latestMessage.ID,
			Status: lo.ToPtr(nextentity.MessageStatusSent),
		})
		if err != nil {
			return errors.WithMessage(err, "failed update message status")
		}

		_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
			ID:     session.ID,
			Status: lo.ToPtr(nextentity.SessionStatusRunning),
		})
		if err != nil {
			return errors.WithMessage(err, "failed update session status")
		}
	}
	return nil
}

func (s *Service) saveEvents(ctx context.Context, events []nextentity.EventData) error {
	if len(events) == 0 {
		return nil
	}
	parsedEvents := lo.FilterMap(events, func(item nextentity.EventData, _ int) (*nextentity.Event, bool) {
		if item.Event == "" || item.Data == nil {
			return nil, false
		}
		e := item.ToEvent()
		if e == nil {
			return nil, false
		}
		return e, true
	})
	err := s.nextserverDao.BatchCreateEvent(ctx, parsedEvents)
	if err != nil {
		return errors.WithMessage(err, "failed to batch create event")
	}
	log.V1.CtxInfo(ctx, "saved events with count %d", len(parsedEvents))
	return nil
}

type FetchSessionEventsOption struct {
	SessionID string
	Username  string
	Title     string
	Offset    int64
	Reconnect bool
	IsOffline bool
	StartTime time.Time
}

func (s *Service) isRestartFromServer(ctx context.Context, sessionID string) bool {
	// 非异常重启
	fromType, tmpErr := s.getSessionRestartFromType(ctx, sessionID)
	if tmpErr != nil {
		log.V1.CtxError(ctx, "failed to get session restart from type: %v", tmpErr)
		return false
	}
	log.V1.CtxInfo(ctx, "session restart from %s, session id: %v", fromType, sessionID)
	return fromType == runtimestarter.FromTypeServer
}

// FetchSessionEvents get sse events from runtime service and do something when received event.
func (s *Service) FetchSessionEvents(ctx context.Context, opt FetchSessionEventsOption) (*stream.RecvChannel[nextentity.EventData], error) {
	send, recv := stream.NewChannel[nextentity.EventData](20)
	// restart 场景下，server 端重启不需要发送 Preparing 进度通知
	if opt.Offset == 0 || (s.checkSessionRestartMark(ctx, opt.SessionID) && !s.isRestartFromServer(ctx, opt.SessionID)) {
		offset := opt.Offset
		e, err := s.nextserverDao.GetLatestEventBySessionID(ctx, opt.SessionID, true)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.V1.CtxError(ctx, "failed to get latest event: %v", err)
			}
		} else {
			offset = e.EventOffset
		}
		send.DataChannel <- nextentity.EventData{
			Event: nextidl.EventNameProgressNotice,
			Data: &nextidl.ProgressNoticeEvent{
				EventID:     s.idGen.NewID(),
				SessionID:   opt.SessionID,
				Status:      nextidl.Preparing,
				Timestamp:   time.Now().Unix(),
				EventOffset: offset,
				EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.Preparing, offset),
			},
		}
	}

	var (
		ch  *stream.RecvChannel[entity.SessionDataStreamEvent]
		err error
	)
	if opt.Reconnect {
		ch, err = s.MustGetAgentRunEvents(ctx, GetAgentRunStreamOption{
			SessionID:   opt.SessionID,
			EventOffset: opt.Offset,
			Reconnect:   opt.Reconnect,
			IsOffline:   opt.IsOffline,
		})
	} else {
		ch, err = s.GetAgentRunEvents(ctx, GetAgentRunStreamOption{
			SessionID:   opt.SessionID,
			EventOffset: opt.Offset,
			Reconnect:   opt.Reconnect,
			IsOffline:   opt.IsOffline,
		})
	}
	if err != nil {
		log.V1.CtxError(ctx, "failed to get agent run events: %v, offline: %v, offset %d, session id: %s", err, opt.IsOffline, opt.Offset, opt.SessionID)
		s.sendAndSaveErrorEvent(ctx, err, send, opt.SessionID)
		send.Close()
		if ch != nil {
			ch.Close()
		}
		return recv, err
	}
	var (
		latestPlanID string
		runID        string
	)
	run, err := s.dao.GetAgentRun(ctx, runtimedal.GetAgentRunOption{
		SessionID: opt.SessionID,
		Sync:      true,
	})
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to get agent run: %v", err)
	}
	if run != nil {
		runID = run.ID
	}
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.V1.CtxError(ctx, "agent run stream panic: %+v, stack: %+v, offline: %v, offset %d, session id: %s", r, string(debug.Stack()), opt.IsOffline, opt.Offset, opt.SessionID)
			}
		}()

		stream.Forward(ctx, ch, send, func(event entity.SessionDataStreamEvent) nextentity.EventData {
			PrintJSON(ctx, fmt.Sprintf("--->>>[FetchSessionEvents] fetch event [%s] offline [%v]", opt.SessionID, opt.IsOffline), event)

			if event.EventOffset == 0 && !event.Timestamp.IsZero() && !opt.StartTime.IsZero() {
				firstEventCost := time.Since(opt.StartTime).Milliseconds()
				log.V1.CtxInfo(ctx, "session %s first event cost %vms, offline: %v", opt.SessionID, firstEventCost, opt.IsOffline)

				_ = metrics.NSM.AgentFirstEventLatency.WithTags(&metrics.NextServerAgentEventTag{
					IsOffline: opt.IsOffline,
				}).Observe(float64(firstEventCost))
			}
			switch {
			case event.NextMessageCreated != nil:
				// 每个 query 的第一条回复 message 的 ReplyMessageID 非空，此时发送一次 Thinking 事件
				if event.NextMessageCreated.ReplyMessageID != "" {
					send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
						EventID:     s.idGen.NewID(),
						SessionID:   opt.SessionID,
						Status:      nextidl.Thinking,
						Timestamp:   event.Timestamp.Unix(),
						EventOffset: event.EventOffset,
						EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.Thinking, event.EventOffset),
					}}
				}

				var (
					messageAttachments []*nextentity.Attachment
					generateVisualPage *nextidl.GenerateVisualPage
					messageID          = event.NextMessageCreated.ID
				)
				if len(event.NextMessageCreated.Attachments) > 0 { // 判断产物是否需要预览
					generateVisualPage = &nextidl.GenerateVisualPage{
						DisplayGenerate:   true,
						GeneratePageQuery: "将上述任务结果转为可视化页面并部署",
					}
					messageAttachments = lo.FilterMap(event.NextMessageCreated.Attachments, func(item entity.Attachment, _ int) (*nextentity.Attachment, bool) {
						attachment, err := s.nextArtifactService.GetAndUpdateAttachmentArtifact(ctx, artifact.GetAndUpdateAttachmentArtifactOption{
							SessionID:         opt.SessionID,
							Attachment:        item,
							UpdateDisplay:     true,
							IsAgentAttachment: true,
							MessageID:         messageID,
						})
						if err != nil {
							log.V1.CtxError(ctx, "failed to operate attachment artifact: %v", err)
							return nil, false
						}
						// 过滤掉不需要的文件
						if attachment != nil && nextentity.ShouldFilterFile(attachment.Path) {
							return nil, false
						}
						// 产物中如果出现了deployment的链接，就不需要展示可视化按钮
						if attachment != nil && attachment.SubType == nextentity.LinkArtifactKeySourceDeployment.String() {
							generateVisualPage = nil
						}
						return attachment, true
					})
				}
				if opt.IsOffline {
					if messageID == "" {
						messageID = s.idGen.NewID()
					}
					_, err := s.nextserverDao.CreateMessage(ctx, nextdal.CreateMessageOption{
						ID:          messageID,
						SessionID:   opt.SessionID,
						TaskID:      "",
						Role:        nextentity.MessageRoleAssistant,
						Content:     nextentity.MessageContent{Content: event.NextMessageCreated.Content},
						Creator:     "",
						Attachments: messageAttachments,
					})
					if err != nil {
						log.V1.CtxError(ctx, "failed to create message: %v", err)
					}
				}
				previewID := nextentity.GetPreviewAttachmentID(messageAttachments)
				return nextentity.EventData{Event: nextidl.EventNameMessageCreate, Data: &nextidl.MessageCreateEvent{
					EventID: s.idGen.NewID(),
					Message: &nextidl.Message{
						MessageID: messageID,
						SessionID: opt.SessionID,
						TaskID:    "",
						Role:      string(nextentity.MessageRoleAssistant),
						Content:   event.NextMessageCreated.Content,
						Attachments: lo.FilterMap(messageAttachments, func(item *nextentity.Attachment, _ int) (*nextidl.Attachment, bool) {
							if item == nil {
								return nil, false
							}
							attachment := &nextidl.Attachment{
								ID:            item.ID,
								FileName:      item.FileName,
								Path:          item.Path,
								Type:          item.Type,
								URL:           item.URL,
								ContentType:   item.ContentType,
								ContentLength: item.ContentLength,
								ParentStepIDs: nil,
								LarkToken:     item.LarkToken,
								SubType:       &item.SubType,
								Version:       int32(item.Version),
							}
							if item.SubType == nextentity.LinkArtifactKeySourceURL.String() { // 标记为可直接跳转的附件
								attachment.NeedJump = true
							} else if attachment.ID == previewID { // 标记默认需要预览的附件
								attachment.NeedPreview = true
							}

							return attachment, true
						}),
						Creator:   "",
						CreatedAt: "",
						UpdatedAt: "",
					},
					Timestamp:          event.Timestamp.Unix(),
					EventOffset:        event.EventOffset,
					ReplyMessageID:     event.NextMessageCreated.ReplyMessageID,
					GenerateVisualPage: generateVisualPage,
					EventKey:           nextentity.GetMessageCreateEventKey(opt.SessionID, event.NextMessageCreated.ID, event.EventOffset),
				}}
			case event.NextPlanUpdated != nil:
				if latestPlanID != "" && event.NextPlanUpdated.ID != latestPlanID {
					send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
						EventID:     s.idGen.NewID(),
						SessionID:   opt.SessionID,
						Status:      nextidl.ReThinking,
						Timestamp:   event.Timestamp.Unix(),
						EventOffset: event.EventOffset,
						EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.ReThinking, event.EventOffset),
					}}
					latestPlanID = event.NextPlanUpdated.ID
				}
				message, err := s.nextserverDao.GetLatestMessageByRole(ctx, opt.SessionID, nextentity.MessageRoleUser, false)
				if err != nil {
					log.V1.CtxError(ctx, "failed to get latest message: %v", err)
				}
				var messageID string
				if message != nil {
					messageID = message.ID
				}
				return nextentity.EventData{Event: nextidl.EventNamePlanUpdate, Data: &nextidl.PlanUpdateEvent{
					EventID: s.idGen.NewID(),
					TaskID:  "",
					PlanID:  event.NextPlanUpdated.ID,
					Steps: lo.Map(event.NextPlanUpdated.PlanSteps, func(s entity.PlanStep, _ int) *nextidl.Step {
						return &nextidl.Step{
							StepID:           s.ID,
							TaskID:           "",
							Title:            s.Description,
							Status:           s.Status,
							StartTime:        0,
							EndTime:          0,
							ParentMessageIDs: []string{messageID},
						}
					}),
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					Status:      event.NextPlanUpdated.Status,
					SessionID:   opt.SessionID,
					EventKey:    nextentity.GetPlanUpdateEventKey(opt.SessionID, event.NextPlanUpdated.ID, event.EventOffset),
				}}
			case event.NextPlanStepUpdated != nil:
				if event.NextPlanStepUpdated.Status == nextentity.StepStatusRunning.String() {
					send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
						EventID:     s.idGen.NewID(),
						SessionID:   opt.SessionID,
						Status:      nextidl.Executing,
						Timestamp:   event.Timestamp.Unix(),
						EventOffset: event.EventOffset,
						EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.Executing, event.EventOffset),
					}}
					_, err = s.nextserverDao.CreateStep(ctx, nextdal.CreateStepOption{
						ID:          event.NextPlanStepUpdated.ID,
						SessionID:   opt.SessionID,
						TaskID:      "",
						PlanID:      event.NextPlanStepUpdated.PlanID,
						AgentStepID: event.NextPlanStepUpdated.StepID,
						Description: event.NextPlanStepUpdated.Description,
						Status:      nextentity.StepStatus(event.NextPlanStepUpdated.Status),
					})
					if err != nil {
						log.V1.CtxError(ctx, "failed to create plan step: %v", err)
					}
				} else if event.NextPlanStepUpdated.Status == nextentity.StepStatusSuccess.String() {
					err = s.nextserverDao.UpdateStep(ctx, nextdal.UpdateStepOption{
						ID:     event.NextPlanStepUpdated.ID,
						Status: lo.ToPtr(nextentity.StepStatus(event.NextPlanStepUpdated.Status)),
					})
					if err != nil {
						log.V1.CtxWarn(ctx, "failed to update plan step: %v", err)
					}
				}
				return nextentity.EventData{Event: nextidl.EventNameStepUpdate, Data: &nextidl.StepUpdateEvent{
					EventID:     s.idGen.NewID(),
					StepID:      event.NextPlanStepUpdated.ID,
					PlanID:      event.NextPlanStepUpdated.PlanID,
					Status:      event.NextPlanStepUpdated.Status,
					Summary:     event.NextPlanStepUpdated.Description,
					Description: "",
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					SessionID:   opt.SessionID,
					EventKey:    nextentity.GetStepUpdateEventKey(opt.SessionID, event.NextPlanStepUpdated.ID, event.EventOffset),
				}}
			case event.NextToolCall != nil:
				e := s.packToolEvent(ctx, event, opt.IsOffline, opt.SessionID, opt.Username)
				if e == nil {
					return nextentity.EventData{}
				}
				return nextentity.EventData{Event: nextidl.EventNameUseTool, Data: e}
			case event.NextToolCallRequired != nil:
				send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
					EventID:     s.idGen.NewID(),
					SessionID:   opt.SessionID,
					Status:      nextidl.WaitForReplay,
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.WaitForReplay, event.EventOffset),
				}}
				e := s.packToolCallRequired(ctx, event, opt.IsOffline, opt.SessionID, opt.Username)
				return nextentity.EventData{Event: nextidl.EventNameToolCallRequired, Data: e}
			case event.NextToolCallConfirmed != nil:
				send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
					EventID:     s.idGen.NewID(),
					SessionID:   opt.SessionID,
					Status:      nextidl.Executing,
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.Executing, event.EventOffset),
				}}
				e := s.packToolCallConfirmed(ctx, event, opt.SessionID)
				send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameToolCallConfirmed, Data: e}
			case event.NextAgentStepUpdated != nil: // 只做状态存储，不需要存事件
				// 每一个新 query 都会最先收到一次这个 step 事件，TODO：需要确认 step executor 名称是否会改
				if lo.Contains([]string{"neuma", "aime"}, event.NextAgentStepUpdated.Executor) && event.NextAgentStepUpdated.Status == entity.AgentRunStepStatusCreated {
					send.DataChannel <- nextentity.EventData{
						Event: nextidl.EventNameProgressNotice,
						Data: &nextidl.ProgressNoticeEvent{
							EventID:     s.idGen.NewID(),
							SessionID:   opt.SessionID,
							Status:      nextidl.Recognizing,
							Timestamp:   event.Timestamp.Unix(),
							EventOffset: event.EventOffset,
							EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.Recognizing, event.EventOffset),
						},
					}
				}
				// 存储或更新 agent step，保证幂等
				s.createOrUpdateAgentStep(ctx, event.NextAgentStepUpdated, runID)
				return nextentity.EventData{}
			case event.NextEventAgentIdle != nil: // task 结束，进入等待状态
				if opt.IsOffline {
					go func() {
						defer func() {
							if r := recover(); r != nil {
								log.V1.CtxError(ctx, string(debug.Stack()))
							}
						}()

						session, err := s.nextserverDao.GetSession(ctx, nextdal.GetSessionOption{
							ID:   opt.SessionID,
							Sync: true,
						})
						if err != nil {
							log.V1.CtxError(ctx, "failed to get session %s: %v", opt.SessionID, err)
						}
						var sessionOldStatus nextentity.SessionStatus
						title := "你的"
						if session != nil {
							sessionOldStatus = session.Status
							title = session.Title
						}

						_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
							ID:     opt.SessionID,
							Status: lo.ToPtr(nextentity.SessionStatusIdle),
						})
						log.V1.CtxInfo(ctx, "update session to idle, sessionOldStatus: %s, sessionId: %s", sessionOldStatus, opt.SessionID)
						if err != nil {
							log.V1.CtxError(ctx, "failed to update session %s: %v", opt.SessionID, err)
						}
						// 如果是停止对话，session状态是从canceled变成idle，不发送通知
						if sessionOldStatus != nextentity.SessionStatusCanceled {
							err = s.nextSessionMonitorMQClient.SendMessage(ctx,
								conv.JSONBytes(nextentity.ServerMonitorEvent{
									EventName: nextentity.MonitorEventTypeSessionStatus,
									SessionStatusEvent: &nextentity.SessionStatusEvent{
										SessionID:          opt.SessionID,
										SessionStatus:      string(nextentity.SessionStatusIdle),
										SessionEventOffset: event.EventOffset,
									}}), entity.NextSessionMonitorTag)
							if err != nil {
								log.V1.CtxError(ctx, "failed to send task finish mq %s: %v", opt.SessionID, err)
							}

							// 发送消息卡片
							s.handleAgentIdleNotification(ctx, title, agentIdleNotificationOption{
								SessionID: opt.SessionID,
								Username:  opt.Username,
								Title:     title,
							})

						}
					}()
				}
				send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
					EventID:     s.idGen.NewID(),
					SessionID:   opt.SessionID,
					Status:      nextidl.WaitForNext,
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.WaitForNext, event.EventOffset),
				}}
			case event.NextAgentRestart != nil:
				var eve nextentity.EventData
				// 检查是不是异常重启，如果是则发送一个 error event
				mark := s.checkSessionRestartMark(ctx, opt.SessionID)
				if mark {
					// 非异常重启
					fromType, tmpErr := s.getSessionRestartFromType(ctx, opt.SessionID)
					log.V1.CtxInfo(ctx, "session restart from %s, session id: %v", fromType, opt.SessionID)
					if tmpErr != nil {
						log.V1.CtxError(ctx, "failed to get session restart from type: %v", tmpErr)
					}
					// 如果是基于用户 message 重启，则什么都不需要做，兼容 “” 字符串原始语意（等价于 fromType == FromTypeMessage）
					if fromType == runtimestarter.FromTypeMessage || fromType == "" {
						return nextentity.EventData{}
					}
					// from server, need send ‘wait_for_next’ event
					eve = nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
						EventID:     s.idGen.NewID(),
						SessionID:   opt.SessionID,
						Status:      nextidl.WaitForNext,
						Timestamp:   time.Now().Unix(),
						EventOffset: event.EventOffset,
						EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.WaitForNext, event.EventOffset),
					}}
				} else {
					// unexpectedly restart, need send error event
					log.V1.CtxError(ctx, "session restart unexpectedly, session id: %v", opt.SessionID)
					eve = nextentity.EventData{Event: nextidl.EventNameError, Data: &nextidl.ErrorEvent{
						EventID:     s.idGen.NewID(),
						SessionID:   opt.SessionID,
						ErrorCode:   common.ErrorCode_ErrInternalFatal,
						Message:     "session restart unexpectedly",
						Timestamp:   time.Now().Unix(),
						EventOffset: event.EventOffset,
						EventKey:    nextentity.GetErrorEventKey(opt.SessionID, int64(common.ErrorCode_ErrInternalFatal), event.EventOffset),
					}}
				}
				send.DataChannel <- eve
				if opt.IsOffline {
					_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
						ID:     opt.SessionID,
						Status: lo.ToPtr(nextentity.SessionStatusIdle),
					})
					if err != nil {
						log.V1.CtxError(ctx, "failed to update session %s: %v", opt.SessionID, err)
					}
				}
			case event.NextAgentRunCompleted != nil: // session 结束，进入休眠
				send.DataChannel <- nextentity.EventData{Event: nextidl.EventNameProgressNotice, Data: &nextidl.ProgressNoticeEvent{
					EventID:     s.idGen.NewID(),
					SessionID:   opt.SessionID,
					Status:      nextidl.Sleeping,
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					EventKey:    nextentity.GetProgressNoticeEventKey(opt.SessionID, nextidl.Sleeping, event.EventOffset),
				}}
				session, err := s.nextserverDao.GetSessionWithDeleted(ctx, nextdal.GetSessionOption{ID: opt.SessionID, Sync: true})
				if err != nil {
					log.V1.CtxError(ctx, "failed to get session %s: %v", opt.SessionID, err)
				}
				if opt.IsOffline {
					err = s.nextserverDao.DeleteDraftTemplateVersionsBySessionID(ctx, opt.SessionID) // 休眠时清理模板草稿
					if err != nil {
						log.V1.CtxError(ctx, "failed to delete draft template versions by session id %s: %v", opt.SessionID, err)
					}
				}
				// 任务未完成就进入休眠，此时发送一个 error 事件，因为可能是 agent 卡住超时
				// 防止 Agent 发送事件丢失，新增相关兜底逻辑
				// 如果是非休眠唤醒期间收到的 Complete 事件才认为是异常任务
				// 具体 Case: 休眠时发送的完成事件没有收到，用户唤醒后才重新收到该事件
				if session != nil && session.Status.IsTaskRunning() && !s.checkSessionRestartMark(ctx, opt.SessionID) {
					log.V1.CtxError(ctx, "session completed unexpectedly. session id: %v, error:%v, set session to error", opt.SessionID, event.NextAgentRunCompleted.Error)

					errEvent := nextentity.EventData{Event: nextidl.EventNameError, Data: &nextidl.ErrorEvent{
						EventID:     s.idGen.NewID(),
						SessionID:   opt.SessionID,
						ErrorCode:   common.ErrorCode_ErrInternalFatal, // 不可恢复的错误
						Message:     "session completed unexpectedly",
						Timestamp:   time.Now().Unix(),
						EventOffset: event.EventOffset,
						EventKey:    nextentity.GetErrorEventKey(opt.SessionID, int64(common.ErrorCode_ErrInternalFatal), event.EventOffset),
					}}
					send.DataChannel <- errEvent
					if opt.IsOffline {
						_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
							ID:     opt.SessionID,
							Status: lo.ToPtr(nextentity.SessionStatusAbnormal),
						})
						if err != nil {
							log.V1.CtxError(ctx, "failed to update session %s: %v", opt.SessionID, err)
						}
					}
					return nextentity.EventData{}
				}
				if opt.IsOffline && !session.Status.IsFinal() {
					_, err = s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
						ID:     opt.SessionID,
						Status: lo.ToPtr(nextentity.SessionStatusStopped),
					})
					if err != nil {
						log.V1.CtxError(ctx, "failed to update session %s: %v", opt.SessionID, err)
					}
				}

				return nextentity.EventData{Event: nextidl.EventNameSessionCompleted, Data: &nextidl.SessionCompletedEvent{
					EventID:     s.idGen.NewID(),
					SessionID:   opt.SessionID,
					Status:      "",
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					EventKey:    nextentity.GetSessionCompletedEventKey(opt.SessionID, event.EventOffset),
				}}
			case event.NextEventReference != nil:
				return nextentity.EventData{Event: nextidl.EventNameReference, Data: &nextidl.ReferenceEvent{
					EventID:   s.idGen.NewID(),
					SessionID: opt.SessionID,
					References: lo.Map(event.NextEventReference.References, func(item entity.ReferenceItem, _ int) *nextidl.ReferenceItem {
						return &nextidl.ReferenceItem{
							ID:    int32(item.ID),
							Title: item.Title,
							URI:   item.URI,
						}
					}),
					Timestamp:   event.Timestamp.Unix(),
					EventOffset: event.EventOffset,
					EventKey:    nextentity.GetReferenceEventKey(opt.SessionID, event.EventOffset),
				}}
			default:
				//log.V1.CtxInfo(ctx, "unknown session event: %+v", event)
			}
			return nextentity.EventData{}
		})
	}()
	return recv, nil
}

type agentIdleNotificationOption struct {
	SessionID string
	Username  string
	Title     string
}

// handleAgentIdleNotification 处理Agent空闲状态的通知逻辑
func (s *Service) handleAgentIdleNotification(ctx context.Context, title string, opt agentIdleNotificationOption) {
	// 确保最后更新TaskExecution状态
	defer func() {
		if err := s.updateTaskExecutionStatus(ctx, opt.SessionID); err != nil {
			log.V1.CtxError(ctx, "failed to update task execution status %s: %v", opt.SessionID, err)
		}
	}()

	// 获取定时任务历史记录
	cronTaskHistory, err := s.getCronTaskHistory(ctx, opt.SessionID)
	if err != nil || cronTaskHistory == nil {
		log.V1.CtxInfo(ctx, "failed to get task execution %s, maybe not cron task: %v", opt.SessionID, err)
		s.sendFallbackNotification(ctx, title, opt)
		return
	}

	// 获取最后一条Agent回复
	serverMessage, err := s.nextserverDao.GetLastAgentReply(ctx, opt.SessionID)
	if err != nil || serverMessage == nil {
		log.V1.CtxError(ctx, "failed to get last agent reply %s: %v", opt.SessionID, err)
		s.sendFallbackNotification(ctx, title, opt)
		return
	}

	// 创建replay链接
	replayID := s.createReplayLink(ctx, opt.SessionID)

	// 发送定时任务完成通知
	if err := s.sendCronTaskNotification(ctx, cronTaskHistory, opt, replayID); err != nil {
		log.V1.CtxError(ctx, "failed to send cron task finish notification %s: %v", opt.SessionID, err)
		s.sendFallbackNotification(ctx, title, opt)
		return
	}

	// 如果有附件，添加评论
	if err := s.addArtifactsComment(ctx, cronTaskHistory, serverMessage); err != nil {
		log.V1.CtxError(ctx, "failed to add artifacts comment %s: %v", opt.SessionID, err)
		// 注意：这里不发送兜底卡片，因为主要通知已经发送成功
	}
}

// getCronTaskHistory 获取定时任务历史记录
func (s *Service) getCronTaskHistory(ctx context.Context, sessionID string) (*nextentity.TaskExecution, error) {
	return s.taskExecutionDAO.GetTaskExecution(ctx, nextdal.GetTaskExecutionOption{
		SessionID:         sessionID,
		Sync:              true,
		LarkMessageStatus: lo.ToPtr(nextentity.TaskExecutionLarkMessageStatusIdle),
	})
}

// sendFallbackNotification 发送兜底通知
func (s *Service) sendFallbackNotification(ctx context.Context, title string, opt agentIdleNotificationOption) {
	if err := s.nextLarkService.SendTaskFinishNotification(ctx, opt.SessionID, opt.Username, title); err != nil {
		log.V1.CtxError(ctx, "failed to send task finish notification %s: %v", opt.SessionID, err)
	}
}

// createReplayLink 创建replay链接
func (s *Service) createReplayLink(ctx context.Context, sessionID string) string {
	lastEvent, err := s.nextserverDao.GetLatestEventBySessionID(ctx, sessionID, true)
	if err != nil || lastEvent == nil {
		log.V1.CtxError(ctx, "failed to get latest event %s: %v", sessionID, err)
		return ""
	}

	replay, err := s.nextReplayService.CreateReplay(ctx, replayservice.CreateReplayOption{
		SessionID:        sessionID,
		EventOffsetStart: lo.ToPtr(int64(0)),
		EventOffsetEnd:   lo.ToPtr(lastEvent.EventOffset),
		EventReadSync:    false,
		CreateFrom:       string(nextentity.ReplayFromUser),
	})
	if err != nil || replay == nil {
		log.V1.CtxError(ctx, "failed to create replay %s: %v", sessionID, err)
		return ""
	}

	return replay.ID
}

// sendCronTaskNotification 发送定时任务通知
func (s *Service) sendCronTaskNotification(ctx context.Context, cronTaskHistory *nextentity.TaskExecution, opt agentIdleNotificationOption, replayID string) error {
	if pointer.Get(cronTaskHistory.Config.SendUserNotification) {
		return s.nextLarkService.SendPersonalCronTaskFinishNotification(ctx, larkservice.SendPersonalCronTaskFinishNotificationOption{
			TaskName:      opt.Title,
			TaskUID:       cronTaskHistory.TaskUID,
			SessionID:     opt.SessionID,
			LarkMessageID: lo.FromPtr(cronTaskHistory.LarkMessageID),
			ReplayID:      replayID,
		})
	} else {
		return s.nextLarkService.SendGroupCronTaskFinishNotification(ctx, larkservice.SendGroupCronTaskFinishNotificationOption{
			TaskName:      opt.Title,
			Username:      cronTaskHistory.Username,
			TaskUID:       cronTaskHistory.TaskUID,
			SessionID:     opt.SessionID,
			LarkMessageID: lo.FromPtr(cronTaskHistory.LarkMessageID),
			ReplayID:      replayID,
		})
	}
}

// addArtifactsComment 添加artifacts评论
func (s *Service) addArtifactsComment(ctx context.Context, cronTaskHistory *nextentity.TaskExecution, serverMessage *nextentity.Message) error {
	artifacts := serverMessage.Attachments
	if len(artifacts) == 0 {
		log.V1.CtxInfo(ctx, "no attachments found, session id is %v", cronTaskHistory.SessionID)
		return nil
	}

	// 构建有效的URL列表
	var parts []string
	for _, attachment := range artifacts {
		if attachment.URL != "" {
			parts = append(parts, fmt.Sprintf("- [%s](%s)", attachment.FileName, attachment.URL))
		}
	}

	// 如果没有有效的URL，则不发送评论
	if len(parts) == 0 {
		log.V1.CtxInfo(ctx, "no valid url found, session id is %v", cronTaskHistory.SessionID)
		return nil
	}

	// 构建评论内容
	commentContent := s.buildArtifactsComment(strings.Join(parts, "\n"))

	return s.nextLarkService.AddCommentToCronTaskFinishNotification(ctx, lo.FromPtr(cronTaskHistory.LarkMessageID), commentContent)
}

// buildArtifactsComment 构建artifacts评论的JSON结构
func (s *Service) buildArtifactsComment(textContent string) string {
	// 定义评论结构
	type CommentContent struct {
		Tag  string `json:"tag"`
		Text string `json:"text"`
	}

	type Comment struct {
		ZhCN struct {
			Content [][]CommentContent `json:"content"`
		} `json:"zh_cn"`
	}

	comment := Comment{}
	comment.ZhCN.Content = [][]CommentContent{
		{
			{
				Tag:  "md",
				Text: textContent,
			},
		},
	}

	jsonBytes, err := json.Marshal(comment)
	if err != nil {
		log.V1.CtxError(context.Background(), "Failed to marshal artifacts to JSON: %v", err)
		return ""
	}

	return string(jsonBytes)
}

// updateTaskExecutionStatus 更新TaskExecution状态
func (s *Service) updateTaskExecutionStatus(ctx context.Context, sessionID string) error {
	_, err := s.taskExecutionDAO.UpdateTaskExecution(ctx, nextdal.UpdateTaskExecutionOption{
		SessionID:         sessionID,
		LarkMessageStatus: lo.ToPtr(nextentity.TaskExecutionLarkMessageStatusDone),
	})
	return err
}

func PrintJSON(ctx context.Context, key string, v any) {
	d, _ := json.MarshalIndent(v, "", "  ")
	log.V1.CtxInfo(ctx, "%s: %s", key, string(d))
}

type GetNextSession struct {
	SessionID string
	Sync      bool
}

func (s *Service) GetNextSession(ctx context.Context, opt GetNextSession) (*nextentity.Session, error) {
	session, err := s.nextserverDao.GetSessionWithDeleted(ctx, nextdal.GetSessionOption{
		ID:   opt.SessionID,
		Sync: opt.Sync,
	})
	if err != nil {
		return nil, err
	}
	return session, nil
}

type UpdateNextSession struct {
	SessionID string
	Status    *nextentity.SessionStatus
}

func (s *Service) UpdateSession(ctx context.Context, opt UpdateNextSession) (*nextentity.Session, error) {
	return s.nextserverDao.UpdateSession(ctx, nextdal.UpdateSessionOption{
		ID:     opt.SessionID,
		Status: opt.Status,
	})
}

func (s *Service) getSessionTitle(ctx context.Context, sessionID string) string {
	session, err := s.nextserverDao.GetSession(ctx, nextdal.GetSessionOption{
		ID:   sessionID,
		Sync: true,
	})
	if err != nil {
		log.V1.CtxError(ctx, "failed to get session %s: %v", sessionID, err)
	}
	var title string
	if session != nil {
		title = session.Title
	}
	return title
}
