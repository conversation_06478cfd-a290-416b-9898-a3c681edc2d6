package copilot

import (
	_ "embed"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/prompt"
	"code.byted.org/devgpt/kiwis/copilotstack/agent/framework"
)

var (
	//go:embed diagnose_copilot_system.go.tml
	DiagnoseCopilotSystemPrompt         string
	DiagnoseCopilotSystemPromptTemplate = prompt.MustGetTemplate("DiagnoseCopilotSystemPrompt", DiagnoseCopilotSystemPrompt)
	//go:embed diagnose_copilot_user.go.tml
	DiagnoseCopilotUserPrompt         string
	DiagnoseCopilotUserPromptTemplate = prompt.MustGetTemplate("DiagnoseCopilotUserPrompt", DiagnoseCopilotUserPrompt)
	//go:embed gpt_copilot_system.go.tml
	GptCopilotSystemPrompt         string
	GptCopilotSystemPromptTemplate = prompt.MustGetTemplate("GptCopilotSystemPrompt", GptCopilotSystemPrompt)
)

func GetSystemPrompt(trajectory string, query string) prompt.ComposeVaryMessageOption {
	return func() (msgs []*framework.ChatMessage, err error) {
		composeMessage, _ := prompt.WithSystemMessage(DiagnoseCopilotSystemPromptTemplate, map[string]any{"Trajectory": trajectory})()
		userMessage, _ := prompt.WithUserMessage(DiagnoseCopilotUserPromptTemplate, map[string]any{"Query": query})()
		return append(composeMessage, userMessage...), nil
	}
}

func GetGptSystemPrompt(trajectory string, query string) prompt.ComposeVaryMessageOption {
	return func() (msgs []*framework.ChatMessage, err error) {
		composeMessage, _ := prompt.WithSystemMessage(GptCopilotSystemPromptTemplate, map[string]any{"Trajectory": trajectory})()
		userMessage, _ := prompt.WithUserMessage(DiagnoseCopilotUserPromptTemplate, map[string]any{"Query": query})()
		return append(composeMessage, userMessage...), nil
	}
}
