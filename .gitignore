# kitex logs.
/log
# kitex/hertz remote config.
conf/dumped_hertz_remote_config.json
conf/kitex_remote_config.json
# agentbench config
idecopilot/dev/agentbench/conf/config.json
agentsphere/eval/conf/config.json
# local test cmds.
/dev/local
/dev/apitest/http-client.private.env.json
/dev/apitest/*private*
/dev/apitest/requests_history
/dev/apitest/devgpt-test-play.http
/dev/apitest/devgpt-test-play-boe.http
# IDE config.
/.idea
.idea/
# VSCode config.
/.vscode
# Compile dir.
/output
# Coverage output file.
c.out
.run/
**__debug_bin**

.DS_Store

# dependence on external databases
port/bytedoc/bytedoc_dependent_test.go
.env_profile
/node_modules

# RocketMQ log
**/log/rocketmq.log**
**private**

# 忽略所有 .env 文件
.env
.env.*

__pycache__
agentsphere/agents/.claude/
