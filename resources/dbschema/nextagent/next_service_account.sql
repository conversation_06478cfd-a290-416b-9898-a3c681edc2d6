CREATE TABLE `next_service_account`
(
    `id`                 BIGINT UNSIGNED  NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`                VARCHAR(40)      NOT NULL COMMENT 'unique string ID, usually UUID',
    `name`               VA<PERSON>HA<PERSON>(128)     NOT NULL COMMENT 'service name',
    `description`        VARCHAR(512)     DEFAULT '' COMMENT 'description',
    `webhook_config`     JSON             DEFAULT NULL COMMENT 'webhook config',
    `allow_psms`         JSON             DEFAULT NULL COMMENT 'allow psms',
    `allow_sudo`         TINYINT          NOT NULL COMMENT 'allow sudo',
    `owner`              VARCHAR(40)      NOT NULL COMMENT 'owner',
    `enabled`            TINYINT          NOT NULL COMMENT '是否开启',
    `created_at`         TIMESTAMP   DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create timestamp',
    `updated_at`         TIMESTAMP   DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`         BIGINT UNSIGNED DEFAULT 0  NOT NULL COMMENT 'deleted at',
    UNIQUE KEY           `uk_uid`  (`uid`)  COMMENT 'query resource ID',
    UNIQUE KEY           `uk_name` (`name`) COMMENT 'unique name'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='next service account';