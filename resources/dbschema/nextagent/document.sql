CREATE TABLE `document` -- space document
(
    `id`              BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`             VARCHAR(40)                            NOT NULL COMMENT 'unique string',
    `dataset_id`      VARCHAR(40)                            NOT NULL COMMENT 'dataset ID',
    `creator`         VARCHAR(64)                            NOT NULL COMMENT 'creator',
    `source_uid`      VARCHAR(128) DEFAULT ''                NOT NULL COMMENT 'source uid',
    `source_type`     VARCHAR(40)  DEFAULT ''                NOT NULL COMMENT 'source type:lark_wiki/lark_doc/lark_docx',
    `title`           VARCHAR(256) DEFAULT ''                NOT NULL COMMENT 'title',
    `content`         VARCHAR(64) NULL COMMENT 'content tos key',
    `owner`           VARCHAR(64)  DEFAULT ''                NOT NULL COMMENT 'owner',
    `created_at`      TIMESTAMP    DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create timestamp',
    `updated_at`      TIMESTAMP    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `last_updated_at` TIMESTAMP    DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'doc last update timestamp',
    `deleted_at`      BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    `process_status`  VARCHAR(64)  DEFAULT ''                NOT NULL COMMENT 'doc last process status',
    `failed_reason`   JSON NULL COMMENT 'doc process failed reason',
    `content_type`     VARCHAR(40)  DEFAULT ''                NOT NULL COMMENT 'content type:doc/docx/sheet',
    `document_created_at`  TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'doc created at',
    `import_type`      VARCHAR(40)  DEFAULT 'single'          NOT NULL  COMMENT '导入类型:single/wiki_tree',
    `wiki_space_name`  VARCHAR(40)  DEFAULT ''                NOT NULL  COMMENT '知识库空间名称',
    UNIQUE KEY `uk_uid` (`uid`) COMMENT 'query document by id',
    KEY               `idx_dataset_id_updated_at` (`dataset_id`,`updated_at`) COMMENT 'dataset_id',
    KEY               `idx_source_uid` (`source_uid`) COMMENT 'source_uid',
    KEY               `idx_owner` (`owner`) COMMENT 'owner'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='space document';
