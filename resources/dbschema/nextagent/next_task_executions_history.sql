CREATE TABLE `next_task_executions_history`
(
    `id`                    BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `created_at`            TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`            TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '上一次更新时间',
    `deleted_at`            BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删除时间戳',
--     `trigger_type`          INT         NOT NULL COMMENT '触发类型：1-定时触发，2-手动重试，3-事件触发',
    `external_type`         INT             NOT NULL COMMENT '触发类型：1-定时触发，2-手动重试，3-事件触发, 4-Cdoebase, 5-Slardar',
--     `task_uid`              VARCHAR(100)    NOT NULL COMMENT '定时任务unique ID',
    `external_uid`          VARCHAR(100)    NOT NULL COMMENT '关联类型 id，比如可能是定时任务 id，也可以为空',
    `execute_time`          TIMESTAMP       NOT NULL COMMENT '实际执行时间',
    `config`                JSON DEFAULT NULL COMMENT '任务统一的配置，包括通知群的配置，通知人的配置等等',
    `status`                INT         NOT NULL DEFAULT 0 COMMENT '运行任务的结果：1 成功 2 失败',
    `error_message`         TEXT            NULL COMMENT '错误信息',
    `session_id`            VARCHAR(100)    NOT NULL COMMENT '对话ID',
--     `execution_duration`    INT UNSIGNED    NULL COMMENT '执行耗时(秒)',
    `username`              VARCHAR(50) NOT NULL COMMENT '用户名称',
    `mq_event_id`           VARCHAR(64)     NULL COMMENT 'MQ的消息ID',
    `lark_message_id`       VARCHAR(64)     NULL COMMENT 'lark的消息ID',
    `lark_message_status`   TINYINT NOT NULL COMMENT '1 发送失败 2 发送成功',

    KEY `idx_external_uid` (`external_uid`, `deleted_at`) COMMENT '场景：查询任务的执行历史',
    KEY `idx_username` (`username`, `deleted_at`) COMMENT '场景：查询用户的任务执行记录',
    KEY `idx_execute_time` (`execute_time`) COMMENT '场景：按执行时间查询',
    KEY `idx_status` (`status`, `deleted_at`) COMMENT '场景：按状态查询执行记录',
    KEY `idx_mq_event_id` (`mq_event_id`) COMMENT '场景：通过MQ消息ID查询执行记录',
    KEY `idx_session_id` (`session_id`, `deleted_at`) COMMENT '场景：通过session_id查询和更新任务执行记录'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='任务执行记录表';