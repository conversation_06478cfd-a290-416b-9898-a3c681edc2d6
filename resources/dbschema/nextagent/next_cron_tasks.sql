CREATE TABLE `next_cron_tasks`
(
    `id`                   BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `created_at`           TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '上一次更新时间',
    `deleted_at`           BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删除时间戳',
    `username`             VARCHAR(50)     NOT NULL COMMENT '用户名称',
    `uid`                  VARCHAR(100)    NOT NULL COMMENT '定时任务unique ID',
    `name`                 VARCHAR(255)    NOT NULL COMMENT '任务名称',
    `description`          TEXT            NULL COMMENT '任务描述',
    `task_type`            INT             NOT NULL COMMENT '任务类型：1-AimeBot，2-自动拉群，3-绑定现有群',
    `template_id`          VARCHAR(100)    NOT NULL COMMENT 'AI模板ID',
    `schedule`             VARCHAR(100)    NOT NULL COMMENT 'Cron表达式',
    `timezone`             VARCHAR(50)     NOT NULL DEFAULT 'Asia/Shanghai' COMMENT '时区',
    `next_execute_time`    TIMESTAMP       NOT NULL COMMENT '下次执行时间',
    `next_mq_event_id`     VARCHAR(64)     NULL COMMENT '下次执行的MQ的消息ID，用于区分是否为废弃消息',
    `last_execute_time`    TIMESTAMP       NULL COMMENT '最后执行时间',
    `last_run_status`      INT             NOT NULL DEFAULT 0 COMMENT '最后执行状态：0-未执行，1-成功，2-失败，3-运行中',
    `task_status`          INT             NOT NULL DEFAULT 1 COMMENT '任务状态：0-停用，1-启用，2-暂停',
    `group_info`           JSON    NOT NULL COMMENT '群组信息',
    `role`                 INT             DEFAULT NULL COMMENT 'session role',
    `space_id`             VARCHAR(100)    NOT NULL COMMENT '空间ID',
    `content`              TEXT    NULL COMMENT '模板中的Content',
    `option`               TEXT    NULL COMMENT '模板中的option',

    KEY `idx_username` (`username`) COMMENT '场景：查询用户的任务列表',
    KEY `idx_next_execute_time` (`next_execute_time`, `task_status`, `deleted_at`) COMMENT '场景：任务调度器获取待执行任务',
    KEY `idx_task_status` (`task_status`, `deleted_at`) COMMENT '场景：按状态查询任务',
    KEY `idx_next_mq_event_id` (`next_mq_event_id`) COMMENT '场景：通过MQ消息ID查询执行记录',
    KEY `idx_space_id` (`space_id`, `deleted_at`) COMMENT '场景：按空间查询任务列表',
    UNIQUE KEY `uk_uid` (`uid`) COMMENT '保证定时任务UID唯一性'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='定时任务配置表';