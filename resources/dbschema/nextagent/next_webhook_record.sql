CREATE TABLE `next_webhook_record`
(
    `id`                     BIGINT UNSIGNED  NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`                    VARCHAR(40)      NOT NULL COMMENT 'unique string ID, usually UUID',
    `task_id`                VARCHAR(40)      NOT NULL COMMENT 'task id',
    `session_id`             VARCHAR(40)      NOT NULL COMMENT 'session id',
    `url`                    varchar(255)     NOT NULL COMMENT 'url',
    `request_headers`        text             DEFAULT NULL COMMENT 'request headers',
    `request_data`           mediumtext       DEFAULT NULL COMMENT 'request data',
    `response_headers`       text             DEFAULT NULL COMMENT 'response headers',
    `response_body`          text             DEFAULT NULL COMMENT 'response body',
    `response_status`        varchar(255)     DEFAULT NULL COMMENT 'response status',
    `execution_duration`     float            DEFAULT NULL COMMENT 'execution duration',
    `internal_error_message` varchar(255)     DEFAULT NULL COMMENT 'internal error message',
    `created_at`             TIMESTAMP   DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create timestamp',
    `updated_at`             TIMESTAMP   DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`             BIGINT UNSIGNED DEFAULT 0  NOT NULL COMMENT 'deleted at',
    UNIQUE KEY    `uk_uid`  (`uid`)  COMMENT 'query resource ID',
    KEY           `idx_task_id` (`task_id`) COMMENT 'task id',
    KEY           `idx_session_id` (`session_id`) COMMENT 'session id'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='next webhook record';