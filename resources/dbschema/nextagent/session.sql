CREATE TABLE `next_session` -- next agent session
(
    `id`               BIGINT UNSIGNED                                                       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    `uid`              VARCHAR(40)                           NOT NULL COMMENT 'unique string ID, usually UUID',
    `title`            VA<PERSON>HAR(2048)                         NOT NULL COMMENT 'session title',
    `creator`          VARCHAR(64)                           NOT NULL COMMENT 'session creator',
    `status`           VARCHAR(64)                           NOT NULL COMMENT 'session status`',
    `context`          JSON                                  NOT NULL COMMENT 'input request context, a json map',
    `runtime_metadata` JSON                                  NOT NULL COMMENT 'runtime metadata',
    `role`             int                                   DEFAULT NULL COMMENT 'session role',
    `template_id`      VARCHAR(40)                           DEFAULT ''                NOT NULL COMMENT 'template ID, usually UUID',
    `started_at`       BIGINT UNSIGNED                       DEFAULT 0                                             NOT NULL COMMENT 'started at, the unit is ms',
    `source_space_id`  VARCHAR(40)                           DEFAULT ''                NOT NULL COMMENT 'source space ID, usually UUID',
    `last_message_at`  TIMESTAMP                             DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'last message timestamp',
    `can_resume`       int                                   DEFAULT 0 NOT NULL COMMENT 'can resume',
    `can_not_resume_reason` int                              NOT NULL DEFAULT 0 COMMENT 'can not resume reason 0 not_allowed 1 expired 2 deleted',
    `latest_agent_resume_at` TIMESTAMP                       DEFAULT NULL COMMENT 'latest agent resume at',
    `source`           int                                   NOT NULL DEFAULT 1 COMMENT '0: unknown 1: personal 2: cron job 3: codebase 4: slardar',
    `created_at`       TIMESTAMP   DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create timestamp',
    `updated_at`       TIMESTAMP   DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT 'last update timestamp',
    `deleted_at`       BIGINT UNSIGNED DEFAULT 0                                             NOT NULL COMMENT 'deleted at',
    KEY                `idx_uid` (`uid`) COMMENT 'query session by ID',
    KEY                `idx_creator_created_at_role` (`creator`, `created_at`, `role`) COMMENT 'query session by creator and created at and role',
    KEY                `idx_status` (`status`) COMMENT 'query status',
    KEY                `idx_last_message_at` (`last_message_at`) COMMENT 'query session by last message at',
    KEY                `idx_latest_agent_resume_at` (`latest_agent_resume_at`) COMMENT 'query session by latest_agent_resume_at'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='session';
