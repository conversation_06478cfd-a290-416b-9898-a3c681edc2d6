BaseConfig:
  Domain: "https://aime.bytedance.net"

TCCConfig:
  PSM: flow.agentsphere.config

DBConfig:
  PSM: toutiao.mysql.codeagent
  DBName: codeagent

AuthConfig:
  EnabledAuthOrigins: [ "codebase", "byte_cloud" ]
  EnableSignCodebaseJWT: true
  SignCodebaseJWTScopes: [ "repos.contents:fetch", "repos.contents:push" ]
  SignNextCodeJWTScopes: [ "repo:read", "repo:list", "repo:write" ]
  IncludeCodebaseUserDetails: true

CodebaseConfig:
  APIBaseURL: "https://codebase-api.byted.org"
  V1APIBaseURL: "https://code.byted.org/_"

MQConfig:
  RuntimeOrchestrator:
    Cluster: "web_common4"
    Topic: "codeagent"
    ConsumerGroup: "runtime_orchestrator"
    SwimlaneV2: true
  AssignmentMonitor:
    Cluster: "web_common4"
    Topic: "codeagent"
    ConsumerGroup: "assignment_monitor"
    SwimlaneV2: true
  NextRuntimeOrchestrator:
    Cluster: "web_common4"
    Topic: "codeagent"
    ConsumerGroup: "next_runtime_orchestrator"
    SwimlaneV2: true
  NextSessionMonitor:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "next_session_monitor"
    SwimlaneV2: true
  TestingMonitor:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "next_testing_monitor"
    SwimlaneV2: true
  Knowledgebase:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "knowledgebase"
    SwimlaneV2: true
    WorkerCount: 1
    EnableRateLimit: true
    RateLimitQPS: 5
  KnowledgebaseOffline:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "knowledgebase_offline"
    SwimlaneV2: true
    WorkerCount: 1
    EnableRateLimit: true
    RateLimitQPS: 5
  NextTrace:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "next_trace"
    SwimlaneV2: true
    Orderly: true
    RetryTimes: 3
  DeployReview:
    Cluster: "web_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "deploy_review"
    SwimlaneV2: true
  TaskCronJob:
    Cluster: "web_common4"
    Topic: "codeagent"
    ConsumerGroup: "task_cronjob"
    SwimlaneV2: true

RedisConfig:
  PSM: toutiao.redis.codeagent
  DialTimeout: 500
  ReadTimeout: 2000
  WriteTimeout: 1000
  AutoLoadConf: true

RuntimeAPIConfig:
  APIBaseURL: "https://aime.bytedance.net"
  APIPrefix: "/api/agents/v2"
KnowledgebaseConfig:
  ElasticSearchPSM: "byte.es.agent_knowledgebase.service.lf"
  ElasticSearchCluster: "data"
  VikingName: "devgpt_1750318656__agentsphere_knowledgebase"
  ElasticSearchRecallIndex: "agentsphere_knowledgebase"
  PruneSegmentTable: "aime.dwd_segment_prune"
  PatrolSegmentTable: "aime.dwd_segment_patrol"

ByteDocConfig:
  URI: "mongodb+consul+token://bytedance.bytedoc.aime_trace/aime_trace?connectTimeoutMS=2000"
  Database: "aime_trace"
  ReadURI: "mongodb+consul+token://bytedance.bytedoc.aime_trace/aime_trace?connectTimeoutMS=2000&readPreference=secondary"
RuntimeServerConfig:
  APIBaseURL: "https://agentsphere-runtime.bytedance.net"

CloudMCPAPIConfig:
  APIBaseURL: "https://cloud.bytedance.net"

BPMConfig:
  Host: "https://bpm.bytedance.net"
  Timeout: 30
  WorkflowConfigID: "24475"

SCMConfig:
  Host: "https://scm.byted.org"
  Timeout: 30
  RepoName: "devgpt/agentsphere/runtime"
  RepoID: "401256"

ICMConfig:
  Host: "http://image-manager.byted.org"
  Timeout: 30
  Namespace: "base"
  OnlineImageName: "iris_runtime_general"
  OnlineBashImageName: "iris_bash_general"
  Region: "China-North-LF"
  Registry: "hub.byted.org"

AgentDeploymentConfig:
  FrontendURL: "https://%s.aime-app.bytedance.net"
  BackendURL: "https://%s.fn.bytedance.net/docs"
