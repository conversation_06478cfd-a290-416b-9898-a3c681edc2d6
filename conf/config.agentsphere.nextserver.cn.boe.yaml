BaseConfig:
  Domain: "https://aime-boe.bytedance.net"

TCCConfig:
  PSM: flow.agentsphere.config

DBConfig:
  PSM: toutiao.mysql.copilot
  DBName: copilot
  LogLevel: info

AuthConfig:
  EnabledAuthOrigins: ["codebase", "byte_cloud"]
  EnableSignCodebaseJWT: true
  SignCodebaseJWTScopes: ["repos.contents:fetch", "repos.contents:push"]
  SignNextCodeJWTScopes: ["repo:read", "repo:list", "repo:write"]
  IncludeCodebaseUserDetails: true

CodebaseConfig:
  APIBaseURL: "https://codebase-api.byted.org"
  V1APIBaseURL: "https://code.byted.org/_"

MQConfig:
  RuntimeOrchestrator:
    Cluster: "test_common4"
    Topic: "codeagent"
    ConsumerGroup: "runtime_orchestrator"
    SwimlaneV2: true
  AssignmentMonitor:
    Cluster: "test_common4"
    Topic: "codeagent"
    ConsumerGroup: "assignment_monitor"
    SwimlaneV2: true
  NextRuntimeOrchestrator:
    Cluster: "test_common4"
    Topic: "codeagent"
    ConsumerGroup: "next_runtime_orchestrator"
    SwimlaneV2: true
  NextSessionMonitor:
    Cluster: "test_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "next_session_monitor"
    SwimlaneV2: true
  TestingMonitor:
    Cluster: "test_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "next_testing_monitor"
    SwimlaneV2: true
  Knowledgebase:
    Cluster: "test_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "knowledgebase"
    SwimlaneV2: true
    WorkerCount: 1
    EnableRateLimit: true
    RateLimitQPS: 5
  KnowledgebaseOffline:
    Cluster: "test_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "knowledgebase_offline"
    SwimlaneV2: true
    WorkerCount: 1
    EnableRateLimit: true
    RateLimitQPS: 5
  NextTrace:
    Cluster: "test_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "next_trace"
    SwimlaneV2: true
    Orderly: true
    RetryTimes: 3
  DeployReview:
    Cluster: "test_common4"
    Topic: "aime_monitor"
    ConsumerGroup: "deploy_review"
    SwimlaneV2: true
  TaskCronJob:
    Cluster: "test_common4"
    Topic: "codeagent"
    ConsumerGroup: "task_cronjob"
    SwimlaneV2: true

RedisConfig:
  PSM: toutiao.redis.codeagent
  DialTimeout: 500
  ReadTimeout: 2000
  WriteTimeout: 1000
  AutoLoadConf: true

RuntimeAPIConfig:
  APIBaseURL: "https://aime-boe.bytedance.net"
  APIPrefix: "/api/agents/v2"
KnowledgebaseConfig:
  # This cluster is for testing, no stability guarantee.
  # TODO: change to biz cluster.
  ElasticSearchPSM: "byte.es.mix5"
  ElasticSearchCluster: "data"
  ElasticSearchRecallIndex: "agentsphere_knowledgebase"
  VikingName: "devgpt_1750143120__agentsphere_knowledgebase_boe"

ByteDocConfig:
  URI: "mongodb+consul+token://bytedance.bytedoc.aime_trace/aime_trace?connectTimeoutMS=2000"
  Database: "aime_trace"
RuntimeServerConfig: # boe环境需要请求线上cn，因为boe容器的环境是线上
  APIBaseURL: "https://agentsphere-runtime.bytedance.net"

CloudMCPAPIConfig: # 同RuntimeServerConfig
  APIBaseURL: "https://cloud.bytedance.net"

BPMConfig:
  Host: "https://bpm-boe.bytedance.net"
  Timeout: 30
  WorkflowConfigID: "13670"

SCMConfig:
  Host: "https://scm.byted.org"
  Timeout: 30
  RepoName: "devgpt/agentsphere/runtime"
  RepoID: "401256"

ICMConfig:
  Host: "http://image-manager.byted.org"
  Timeout: 30
  Namespace: "base"
  OnlineImageName: "iris_runtime_general"
  OnlineBashImageName: "iris_bash_general"
  Region: "China-North-LF"
  Registry: "hub.byted.org"

AgentDeploymentConfig:
  FrontendURL: "https://%s.aime-boe-app.bytedance.net"
  BackendURL: "https://%s.fn-boe.bytedance.net/docs"
