BaseConfig:
  Domain: "https://aime.tiktok-row.net"

TCCConfig:
  PSM: flow.agentsphere.config

DBConfig:
  PSM: toutiao.mysql.codeagent
  DBName: codeagent

AuthConfig:
  EnabledAuthOrigins: ["codebase", "byte_cloud"]
  EnableSignCodebaseJWT: true
  SignCodebaseJWTScopes: ["repos.contents:fetch", "repos.contents:push"]
  SignNextCodeJWTScopes: ["repo:read", "repo:list", "repo:write"]
  IncludeCodebaseUserDetails: true

CodebaseConfig:
  APIBaseURL: "https://codebase-api.byted.org"
  V1APIBaseURL: "https://code.byted.org/_"

MQConfig:
  RuntimeOrchestrator:
    Cluster: "web_normal"
    Topic: "codeagent"
    ConsumerGroup: "runtime_orchestrator"
    SwimlaneV2: true
  AssignmentMonitor:
    Cluster: "web_normal"
    Topic: "codeagent"
    ConsumerGroup: "assignment_monitor"
    SwimlaneV2: true
  NextRuntimeOrchestrator:
    Cluster: "web_normal"
    Topic: "codeagent"
    ConsumerGroup: "next_runtime_orchestrator"
    SwimlaneV2: true
  NextSessionMonitor:
    Cluster: "web_normal"
    Topic: "aime_monitor"
    ConsumerGroup: "next_session_monitor"
    SwimlaneV2: true
  TestingMonitor:
    Cluster: "web_normal"
    Topic: "aime_monitor"
    ConsumerGroup: "next_testing_monitor"
    SwimlaneV2: true
  Knowledgebase:
    Cluster: "web_normal"
    Topic: "aime_monitor"
    ConsumerGroup: "knowledgebase"
    SwimlaneV2: true
    WorkerCount: 1
    EnableRateLimit: true
    RateLimitQPS: 5
  KnowledgebaseOffline:
    Cluster: "web_normal"
    Topic: "aime_monitor"
    ConsumerGroup: "knowledgebase_offline"
    SwimlaneV2: true
    WorkerCount: 1
    EnableRateLimit: true
    RateLimitQPS: 5
  NextTrace:
    Cluster: "web_normal"
    Topic: "aime_monitor"
    ConsumerGroup: "next_trace"
    SwimlaneV2: true
    Orderly: true
    RetryTimes: 3
  DeployReview:
    Cluster: "web_normal"
    Topic: "aime_monitor"
    ConsumerGroup: "deploy_review"
    SwimlaneV2: true
  TaskCronJob:
    Cluster: "web_normal"
    Topic: "codeagent"
    ConsumerGroup: "task_cronjob"
    SwimlaneV2: true

RedisConfig:
  PSM: toutiao.redis.codeagent
  DialTimeout: 500
  ReadTimeout: 2000
  WriteTimeout: 1000
  AutoLoadConf: true

RuntimeAPIConfig:
  APIBaseURL: "https://aime.byted.org"
  APIPrefix: "/api/agents/v2"
KnowledgebaseConfig:
  ElasticSearchPSM: "byte.es.agent_knowledgebase.service.my"
  ElasticSearchCluster: "data"
  VikingName: "sg_devgpt_1752667863__agentsphere_knowledgebase"
  ElasticSearchRecallIndex: "agentsphere_knowledgebase"

ByteDocConfig:
  URI: "mongodb+consul+token://bytedance.bytedoc.aime_trace/aime_trace?connectTimeoutMS=2000"
  Database: "aime_trace"
  ReadURI: "mongodb+consul+token://bytedance.bytedoc.aime_trace/aime_trace?connectTimeoutMS=2000&readPreference=secondary"
RuntimeServerConfig:
  APIBaseURL: "https://agentsphere-runtime.byted.org"

CloudMCPAPIConfig:
  APIBaseURL: "https://cloud-i18n.bytedance.net"
BPMConfig:
  Host: "https://bpm-i18n.bytedance.net/"
  Timeout: 30
  WorkflowConfigID: "9509"

SCMConfig:
  Host: "https://scm.byted.org"
  Timeout: 30
  RepoName: "devgpt/agentsphere/runtime"
  RepoID: "401256"

ICMConfig:
  Host: "http://image-manager.byted.org"
  Timeout: 30
  Namespace: "base"
  OnlineImageName: "iris_runtime_general"
  OnlineBashImageName: "iris_bash_general"
  Region: "Aliyun_SG"
  Registry: "aliyun-sin-hub.byted.org"

AgentDeploymentConfig:
  FrontendURL: "https://%s.aime-app.tiktok-row.net"
  BackendURL: "https://%s.sg-fn.tiktok-row.net/docs"
