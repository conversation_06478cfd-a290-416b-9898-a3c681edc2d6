// This main.go file is intended only for local debugging purposes.
// The actual online entry point `main` is automatically generated by the `build.sh` script.
//
// Note: Any code added here will not be included in the final binary.
package main

import (
	// if you need to bootstrap a different atom, change this path

	plugin "code.byted.org/devgpt/kiwis/bits_deploy_faas/atomic/aime_deploy"
	"code.byted.org/iesarch/atomic"
)

func main() {
	atomic.Run(&plugin.Plugin{})
}
