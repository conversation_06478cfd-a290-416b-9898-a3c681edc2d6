export BYTED_HOST_IPV6=::1                              # 配置 ipv6 环境变量
export HERTZ_CONF_DIR=conf                              # hertz conf 路径
export HERTZ_LOG_DIR=log                                # hertz log 路径
export CONSUL_HTTP_HOST=common-consul-boe.bytedance.net # 配置成你的开发机 ip
export MY_HOST_IPV6=::1                                 # 配置成开发机的ipv6
export METRICS_LOG_LEVEL=5                              # disable metrics 日志
export RUNTIME_IDC_NAME=boe                             # 配置机房
export ENV_TYPE=bytedance                               # 配置内外场，内场为 bytedance，外场为 toc
export PSM=flow.codeassist.assistant
export _BYTEFAAS_RUNTIME_PORT=8000 

doas -p toutiao.devops.aime_agent_deploy go run main.go
