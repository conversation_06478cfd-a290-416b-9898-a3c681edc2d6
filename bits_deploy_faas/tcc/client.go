package tcc

import (
	"sync"
	"time"

	"code.byted.org/gopkg/tccclient"
)

var tccClient *tccclient.ClientV2
var tcconce sync.Once

func InitTccClient() {
	tcconce.Do(func() {
		config := tccclient.NewConfigV2()
		config.Confspace = "default"
		config.SetFirstGetTimeout(500 * time.Millisecond).SetFirstGetRetry(3)
		var err error
		tccClient, err = tccclient.NewClientV2("flow.agentsphere.config", config)
		if err != nil {
			panic(err)
		}
	})
}

func GetClient() *tccclient.ClientV2 {
	if tccClient == nil {
		InitTccClient()
	}
	return tccClient
}
