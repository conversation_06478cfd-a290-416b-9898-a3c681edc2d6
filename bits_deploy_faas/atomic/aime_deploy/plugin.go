package aimedeploy

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/iesarch/atomic"
	"code.byted.org/devgpt/kiwis/bits_deploy_faas/tcc"
	"code.byted.org/middleware/hertz/pkg/protocol"
	"github.com/pkg/errors"
	poolsdk "github.com/sourcegraph/conc/pool"
	"gopkg.in/yaml.v3"
)

const (
	AimeBOEHost    = "https://aime-boe.bytedance.net"
	AimeCNHost     = "https://aime.bytedance.net"
	AimeI18nHost   = "https://aime-my.byted.org"
	AimeI18nOgHost = "https://aime.tiktok-row.net"

	CloudI18n    = "https://cloud-i18n.bytedance.net"
	CloudCN      = "https://cloud.bytedance.net"
	CNRegistry   = "hub.byted.org/base/iris_runtime_general"
	I18nRegistry = "aliyun-sin-hub.byted.org/base/iris_runtime_general"
)

// Plugin is main object of atomic on ByteCycle/Bits2.0.
type Plugin struct {
	IsOnline          atomic.FromStr[string] `json:"is_online"`
	DavidSourceID     atomic.FromStr[string] `json:"david_source_id"`
	XiaomeiSourceID   atomic.FromStr[string] `json:"xiao_mei_source_id"`
	XiaoshuaiSourceID atomic.FromStr[string] `json:"xiao_shuai_source_id"`

	Region     atomic.FromStr[string] `json:"region"`
	Env        atomic.FromStr[string] `json:"env"`
	ScmVersion atomic.FromStr[string] `json:"scm_version"`
	ScmBranch  atomic.FromStr[string] `json:"scm_branch"`
	IcmVersion atomic.FromStr[string] `json:"icm_version"`
	TrainID    atomic.FromStr[string] `json:"train_id"`
	TrainName  atomic.FromStr[string] `json:"train_name"`
	URL        atomic.FromStr[string] `json:"url"`

	DavidAgentConfigVersionID     string `json:"david_agent_config_version_id"`
	XiaoMeiAgentConfigVersionID   string `json:"xiao_mei_agent_config_version_id"`
	XiaoShuaiAgentConfigVersionID string `json:"xiao_shuai_agent_config_version_id"`

	davidAgentConfigID     string
	xiaoMeiAgentConfigID   string
	xiaoShuaiAgentConfigID string
	user                   string
}

// Init server init logic, only called once when server start
func (p *Plugin) Init() []atomic.ServOption {
	tcc.InitTccClient()
	InitHttpClient()
	return nil
}

// Bind is pre-handler for each method being called.
func (p *Plugin) Bind(ctx context.Context, cycleCtx *atomic.CycleCtx) (context.Context, error) {
	var err error
	p.user, err = atomic.GetTriggerUser(ctx)
	if err != nil {
		return ctx, err
	}
	err = p.getAgentConfigID(ctx)
	if err != nil {
		return ctx, nil
	}
	if p.IcmVersion.V != "" {
		if !strings.HasPrefix(p.IcmVersion.V, CNRegistry) && !strings.HasPrefix(p.IcmVersion.V, I18nRegistry) {
			return ctx, errors.New("icm_version is not valid")
		}
	}
	return ctx, nil
}

// Prepare stage of atomic execution.
func (p *Plugin) Prepare(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called prepare").Emit()
	if p.ScmVersion.V == "" {
		return errors.New("scm_version is empty")
	}

	if p.Region.V == "" {
		p.Region.V = "boe"
	}

	return nil
}

// processAgentConfig 处理单个 agent 配置，根据是否有源 ID 决定使用哪种方式创建版本
func (p *Plugin) processAgentConfig(ctx context.Context, agentConfigID string, sourceID string) (string, error) {
	if agentConfigID == "" {
		return "", nil
	}

	var versionID string
	var err error

	if sourceID != "" {
		versionID, err = p.copyVersionForBase(ctx, agentConfigID, sourceID)
	} else {
		versionID, err = p.copyVersionForFeature(ctx, agentConfigID)
	}

	return versionID, err
}

// Execute stage of atomic execution.
func (p *Plugin) Execute(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called execute").Emit()
	pool := poolsdk.New().WithErrors().WithContext(ctx)
	pool.Go(func(ctx context.Context) error {
		versionID, err := p.processAgentConfig(ctx, p.davidAgentConfigID, p.DavidSourceID.V)
		if err != nil {
			return err
		}
		p.DavidAgentConfigVersionID = versionID
		return nil
	})
	pool.Go(func(ctx context.Context) error {
		versionID, err := p.processAgentConfig(ctx, p.xiaoMeiAgentConfigID, p.XiaomeiSourceID.V)
		if err != nil {
			return err
		}
		p.XiaoMeiAgentConfigVersionID = versionID
		return nil
	})
	pool.Go(func(ctx context.Context) error {
		versionID, err := p.processAgentConfig(ctx, p.xiaoShuaiAgentConfigID, p.XiaoshuaiSourceID.V)
		if err != nil {
			return err
		}
		p.XiaoShuaiAgentConfigVersionID = versionID
		return nil
	})

	if err := pool.Wait(); err != nil {
		return errors.Wrap(err, "create agent config version failed")
	}

	return nil
}

type Column struct {
	Name      string `title:"名字"`
	VersionID string `title:"版本"`
}

// Finish stage of atomic execution. You can link to other User Define Function here.
func (p *Plugin) Finish(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called finish").Emit()
	atomic.AddTableV2(ctx, []Column{
		{"大卫集成", p.DavidAgentConfigVersionID},
		{"小美集成", p.XiaoMeiAgentConfigVersionID},
		{"小帅集成", p.XiaoShuaiAgentConfigVersionID},
	})
	if p.davidAgentConfigID != "" {
		atomic.AddIframe(ctx, "大卫集成", fmt.Sprintf("%s/lab/agents/version/detail?id=%s&config_id=%s", p.getAimeLinkHost(), p.DavidAgentConfigVersionID, p.davidAgentConfigID))
	}
	if p.xiaoMeiAgentConfigID != "" {
		atomic.AddIframe(ctx, "小美集成", fmt.Sprintf("%s/lab/agents/version/detail?id=%s&config_id=%s", p.getAimeLinkHost(), p.XiaoMeiAgentConfigVersionID, p.xiaoMeiAgentConfigID))
	}
	if p.xiaoShuaiAgentConfigID != "" {
		atomic.AddIframe(ctx, "小帅集成", fmt.Sprintf("%s/lab/agents/version/detail?id=%s&config_id=%s", p.getAimeLinkHost(), p.XiaoShuaiAgentConfigVersionID, p.xiaoShuaiAgentConfigID))
	}

	// button show "Pass" and after trigger, we will go to Plugin.Paas function
	atomic.AddExtraAction(
		ctx,
		"完成",
		"Pass",
		atomic.WithEABtnStatus(atomic.EABSuccess),
	)
	// button show "Reject" and after trigger, we will go to Plugin.Reject function
	atomic.AddExtraAction(
		ctx,
		"未完成",
		"Reject",
		atomic.WithEABtnStatus(atomic.EABWarning),
	)
	return nil
}

// Pass is a User Define Function, you can add other function like this.
func (p *Plugin) Pass(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called Pass").Emit()
	return nil
}

// Reject is a User Define Function, you can add other function like this.
func (p *Plugin) Reject(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called Reject").Emit()
	return errors.New("user reject")
}

// Stop stage of atomic execution, when user click Cancel on ByteCycle/Bits2.0.
func (p *Plugin) Stop(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called stop").Emit()
	return nil
}

// Stop stage of atomic execution, when user click Skip on ByteCycle/Bits2.0.
func (p *Plugin) Skip(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called skip").Emit()
	return nil
}

// Rollback stage of atomic execution, when user initiate Rollback ticket on ByteCycle/Bits2.0.
func (p *Plugin) Rollback(ctx context.Context) error {
	log.Logger.Info().With(ctx).Str("called rollback").Emit()
	return nil
}

func (p *Plugin) getAimeHost() string {
	switch p.Region.V {
	case "boe":
		return AimeBOEHost
	case "cn":
		return AimeCNHost
	case "i18n":
		return AimeI18nHost
	default:
		return AimeBOEHost
	}
}

func (p *Plugin) getAimeLinkHost() string {
	switch p.Region.V {
	case "boe":
		return AimeBOEHost
	case "cn":
		return AimeCNHost
	case "i18n":
		return AimeI18nOgHost
	default:
		return AimeBOEHost
	}
}

func (p *Plugin) getCloudHost() string {
	switch p.Region.V {
	case "i18n", "sg":
		return CloudI18n
	default:
		return CloudCN
	}
}

func (p *Plugin) copyVersionForFeature(ctx context.Context, agentConfigID string) (string, error) {
	url := "/api/agents/v2/deploy/bits/upsert/agent/version"
	data := map[string]any{
		"agent_config_id": agentConfigID,
		"bits_build_id":   p.TrainID.V,
		"scm_version":     fmt.Sprintf("scm://devgpt/agentsphere/runtime?version=%s", p.ScmVersion.V),
		"icm_version":     p.IcmVersion.V,
		"user":            p.user,
		"description":     fmt.Sprintf("%s(%s), release integration test version created by bits workflow: %s", p.TrainName.V, p.ScmBranch.V, p.URL.V),
	}
	return p.request(ctx, url, data)
}

func (p *Plugin) copyVersionForBase(ctx context.Context, agentConfigID string, sourceAgentConfigVersionID string) (string, error) {
	url := "/api/agents/v2/agent/config/version/copy"
	data := map[string]any{
		"agent_config_id": agentConfigID,
		"source_id":       sourceAgentConfigVersionID,
		"description":     fmt.Sprintf("%s(%s), release version created by bits workflow: %s", p.TrainName.V, p.ScmBranch.V, p.URL.V),
	}
	return p.request(ctx, url, data)
}

func (p *Plugin) request(ctx context.Context, url string, data map[string]any) (string, error) {
	req := &protocol.Request{}
	res := &protocol.Response{}
	requestURI := fmt.Sprintf("%s%s", p.getAimeHost(), url)
	req.SetRequestURI(requestURI)
	req.SetMethod("POST")
	req.Header.Set("Content-Type", "application/json")
	if p.Env.V != "" {
		req.Header.Set("x-tt-env", p.Env.V)
		req.Header.Set("x-use-ppe", "1")
	}

	jwt, err := p.genJWT(ctx)
	if err != nil {
		return "", errors.Wrap(err, "gen jwt failed")
	}
	req.Header.Set("X-JWT-Token", jwt)
	body, err := json.Marshal(data)
	if err != nil {
		return "", errors.Wrap(err, "marshal request body failed")
	}
	req.SetBody(body)
	log.Logger.Info().With(ctx).Str("request_uri", requestURI).Str("method", "POST").Emit()

	err = GetHttpClient().Do(ctx, req, res)
	if err != nil {
		return "", errors.Wrap(err, "do request failed")
	}

	var s struct {
		AgentConfigVersionID string `json:"agent_config_version_id"`
	}
	statusCode := res.StatusCode()

	if statusCode != http.StatusOK {
		log.Logger.Error().With(ctx).Int(statusCode).Str("body", string(res.Body())).Str("message", "request_failed").Emit()
		return "", errors.Errorf("request failed, status code: %d, body: %s", statusCode, res.Body())
	}
	err = json.Unmarshal(res.Body(), &s)
	if err != nil {
		log.Logger.Error().With(ctx).Int(statusCode).Str("body", string(res.Body())).Str("message", "unmarshal_failed").Emit()
		return "", errors.Wrap(err, "unmarshal response body failed")
	}
	return s.AgentConfigVersionID, nil
}

func (p *Plugin) genJWT(ctx context.Context) (jwt string, err error) {
	req := &protocol.Request{}
	res := &protocol.Response{}
	req.SetRequestURI(fmt.Sprintf("%s/auth/api/v1/jwt", p.getCloudHost()))
	req.SetMethod("GET")
	secret, err := p.getJWTSecret(ctx)
	if err != nil {
		err = errors.WithMessage(err, "failed to get jwt secret")
		return
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", secret))
	err = GetHttpClient().Do(ctx, req, res)
	if err != nil {
		err = errors.WithMessage(err, "failed to do request")
		return
	}
	if res.StatusCode() != http.StatusOK {
		err = errors.Errorf("request failed, status code: %d, body: %s", res.StatusCode(), res.Body())
		return
	}
	jwt = res.Header.Get("X-Jwt-Token")
	if len(jwt) == 0 {
		err = errors.New("got empty jwt token")
		return
	}
	return
}

func (p *Plugin) getJWTSecret(ctx context.Context) (string, error) {
	value, err := tcc.GetClient().Get(ctx, "next_aime_account")
	if err != nil {
		return "", errors.Wrap(err, "get tcc secret failed")
	}
	var account NextAimeAccountConfig
	err = yaml.Unmarshal([]byte(value), &account)
	if err != nil {
		return "", errors.Wrap(err, "unmarshal tcc secret failed")
	}
	if p.Region.V == "i18n" {
		return account.AimeAccountI18nSecretKey, nil
	}
	return account.AimeAccountSecretKey, nil
}

func (p *Plugin) getAgentConfigID(ctx context.Context) error {
	value, err := tcc.GetClient().Get(ctx, "next_deploy_review")
	if err != nil {
		return errors.Wrap(err, "get tcc secret failed")
	}
	var deployReviewConfig NextDeployReview
	err = yaml.Unmarshal([]byte(value), &deployReviewConfig)
	if err != nil {
		return errors.Wrap(err, "unmarshal tcc secret failed")
	}

	if p.IsOnline.V == "" {
		p.davidAgentConfigID = deployReviewConfig.IntegrationTestAgentConfigID.InvitedExpert
		p.xiaoMeiAgentConfigID = deployReviewConfig.IntegrationTestAgentConfigID.Newbie
		p.xiaoShuaiAgentConfigID = deployReviewConfig.IntegrationTestAgentConfigID.Intern
	} else {
		p.davidAgentConfigID = deployReviewConfig.BaseAgentConfigID.InvitedExpert
		p.xiaoMeiAgentConfigID = deployReviewConfig.BaseAgentConfigID.Newbie
		p.xiaoShuaiAgentConfigID = deployReviewConfig.BaseAgentConfigID.Intern
	}

	return nil
}
