package aimedeploy

import (
	"sync"
	"time"

	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/pkg/app/client"
	"github.com/cloudwego/hertz/pkg/network/standard"
)

var httpClient *byted.Client
var httpOnce sync.Once

func InitHttpClient() {
	httpOnce.Do(func() {
		client, err := byted.NewClient(byted.WithAppClientOptions(
			client.WithDialTimeout(30*time.Second),
			client.WithDialer(standard.NewDialer())),
		)
		if err != nil {
			panic(err)
		}
		httpClient = client
	})
}

func GetHttpClient() *byted.Client {
	if httpClient == nil {
		InitHttpClient()
	}
	return httpClient
}
