package aimedeploy

type NextAimeAccountConfig struct {
	BPMToken                 string `yaml:"BPMToken"`
	AimeAccountSecretKey     string `yaml:"AimeAccountSecretKey"`
	AimeAccountI18nSecretKey string `yaml:"AimeAccountI18nSecretKey"`
	AimeICMAccount           string `yaml:"AimeICMAccount"`
	AimeICMToken             string `yaml:"AimeICMToken"`
}

type AgentConfigIDMap struct {
	InvitedExpert string `yaml:"InvitedExpert"` // 大卫
	Newbie        string `yaml:"Newbie"`        // 小美
	Intern        string `yaml:"Intern"`        // 小帅
}

type NextDeployReview struct {
	IntegrationTestAgentConfigID AgentConfigIDMap `yaml:"IntegrationTestAgentConfigID"`
	BaseAgentConfigID            AgentConfigIDMap `yaml:"BaseAgentConfigID"`
}
