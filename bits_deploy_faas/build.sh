#!/bin/bash

fileContent="""
package main

import (
    plugin \"%s\"
    \"code.byted.org/iesarch/atomic\"
)

func main() {
	atomic.Run(&plugin.Plugin{})
}
"""
sub_package=${1}
if [ -z "${sub_package}" ]; then
  sub_package="atomic/aime_deploy"
fi

module=$(head -n1 go.mod | cut -d" " -f2)

mkdir ___build_temp output 2>/dev/null
printf "${fileContent}" "${module}/${sub_package}" >___build_temp/main.go
go build -gcflags "all=-N -l" -v -o output/main ___build_temp/main.go
rm -rf ___build_temp
