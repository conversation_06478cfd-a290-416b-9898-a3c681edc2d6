# Bits Atomic Framework 

Atomic 框架是新一代编写原子服务的框架，支持OOP语法编写原子服务，与Bits交互使用静态类型，
添加了MonoRepo支持和开放中间件的能力。

如果想要知道默认有哪些可以实现的方法，可以使用IDE的`实现interface`功能，实现`atomic.Full`接口即可看到  
> 如果方法没有内容请删除，框架对于未实现的方法有快速路径  
> 会提升原子执行速度和让代码更简洁

对于新接触这个框架，我们推荐先阅读 [Golang原子开发新手入门](https://bytedance.feishu.cn/docx/XpDEdcV6AoPup2xcBlYcMy1ungb)；  
有基本的了解之后，想进阶的，可以阅读 [Atomic框架Readme和源码](https://code.byted.org/iesarch/atomic)； 

如果更改了`atomic/example`路径导致编译失败的，或者在一个仓库内编写多个原子代码的(Monorepo):
- `___build_temp/main.go:5:5 ...` 编译报错
- 在SCM仓库的`配置` -> `编译脚本相对路径` 添加对应原子module路径
- 比如：`build.sh atomic/i18n/janus_start_publish`

![build script add sub module to compile](image.png)

# 使用本地调试

1. 启动 main.go 文件
2. 使用 `devops_agent debug-faas --agent_port 5000` 转发线上流量

如果需要启动不同的原子服务, 注意修改main.go中的import文件

命令行工具下载, 参考
[这篇文档](https://bytedance.larkoffice.com/docs/doccnLfBSgM6WjEdNh86zLzg7hg)